"use client";

import { SidebarButton } from "@/components/ui/sidebar-button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { ArrowRight } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

interface UseAutoResizeTextareaProps {
  minHeight: number;
  maxHeight?: number;
}

function useAutoResizeTextarea({
  minHeight,
  maxHeight,
}: UseAutoResizeTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const adjustHeight = useCallback(
    (reset?: boolean) => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      if (reset) {
        textarea.style.height = `${minHeight}px`;
        return;
      }

      textarea.style.height = `${minHeight}px`;

      const newHeight = Math.max(
        minHeight,
        Math.min(textarea.scrollHeight, maxHeight ?? Number.POSITIVE_INFINITY)
      );

      textarea.style.height = `${newHeight}px`;
    },
    [minHeight, maxHeight]
  );

  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = `${minHeight}px`;
    }
  }, [minHeight]);

  useEffect(() => {
    const handleResize = () => adjustHeight();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [adjustHeight]);

  return { textareaRef, adjustHeight };
}

export function AI_Prompt() {
  const [value, setValue] = useState("");
  const { textareaRef, adjustHeight } = useAutoResizeTextarea({
    minHeight: 60,
    maxHeight: 200,
  });
  const [selectedModel, setSelectedModel] = useState("GPT-4-1 Mini");

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey && value.trim()) {
      e.preventDefault();
      setValue("");
      adjustHeight(true);
      // Here you can add message sending
    }
  };

  const handleSend = () => {
    if (!value.trim()) return;
    setValue("");
    adjustHeight(true);
    // Here you can add message sending
  };

  return (
    <div className="w-full">
      <div className="bg-black/5 dark:bg-white/5 rounded-2xl p-1.5">
        <div className="relative">
          <div className="relative flex flex-col">
            <div
              className="overflow-y-auto relative"
              style={{ maxHeight: "400px" }}
            >
              <Textarea
                id="ai-input-15"
                value={value}
                placeholder={"What can I do for you?"}
                className={cn(
                  "w-full bg-white-900 dark:bg-gray-700/60 rounded-xl px-3 py-2 pr-12 border border-gray-300/50 dark:border-gray-600/50 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 resize-none focus-visible:ring-0 focus-visible:ring-offset-0 transition-all duration-200",
                  "min-h-[120px]",
                  "hover:bg-gray-200/80 dark:hover:bg-gray-700/80 hover:border-gray-400 dark:hover:border-gray-500",
                  value &&
                    "bg-gray-200/90 dark:bg-gray-700/90 border-gray-400 dark:border-gray-500"
                )}
                ref={textareaRef}
                onKeyDown={handleKeyDown}
                onChange={(e) => {
                  setValue(e.target.value);
                  adjustHeight();
                }}
              />

              {/* Send button positioned at right bottom of input */}
              <div className="absolute bottom-2 right-2">
                <SidebarButton
                  type="button"
                  icon={ArrowRight}
                  layout="icon-only"
                  size="md"
                  variant={value.trim() ? "secondary" : "ghost"}
                  iconClassName="text-white"
                  disabled={!value.trim()}
                  aria-label="Send message"
                  onClick={handleSend}
                  className={cn(
                    "transition-all duration-200",
                    value.trim()
                      ? "bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg hover:shadow-xl"
                      : "opacity-40 bg-gray-300/50 dark:bg-gray-600/50 hover:opacity-60"
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
