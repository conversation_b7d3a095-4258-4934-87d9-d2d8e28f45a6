"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { TooltipProvider } from "@/components/ui/tooltip";
import { checkItemDependencies } from "@/lib/dependencyManager";
import type {
  BusinessItem,
  BusinessSection,
} from "@/types/BusinessSection.types";
import { Check, ChevronUp, Lightbulb, Zap } from "lucide-react";
import { useState } from "react";

// Enhanced Item Row Component with dependency checking
const EnhancedItemRow = ({
  item,
  onItemClick,
}: {
  item: BusinessItem;
  onItemClick: (item: BusinessItem) => void;
}) => {
  // Check dependencies if item has them
  const dependencyCheck = item.dependencies
    ? checkItemDependencies(item.id)
    : null;
  const isBlocked = dependencyCheck && !dependencyCheck.isValid;

  const getStatusStyles = (status: string, blocked: boolean = false) => {
    if (blocked) {
      return "bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60";
    }

    switch (status) {
      case "validated":
        return "bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-700";
      case "action":
        return "bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-700";
      case "idea":
        return "bg-yellow-50 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-700";
      case "invalidated":
        return "bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-600";
      default:
        return "bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300";
    }
  };

  return (
    <TooltipProvider>
      <div
        className={`flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] ${getStatusStyles(
          item.status,
          isBlocked ?? false
        )} hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 ${
          isBlocked ? "cursor-not-allowed" : ""
        }`}
        onClick={() => {
          if (!isBlocked) {
            onItemClick(item);
          }
        }}
      >
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {/* Item Title */}
          <div className="flex-1 min-w-0">
            <span className="font-medium text-sm truncate">{item.title}</span>
          </div>
        </div>

        {/* Status Badges */}
        <div className="flex items-center gap-1 flex-shrink-0">
          {item.status !== "invalidated" && (
            <>
              {item.actions > 0 && (
                <Badge
                  variant="secondary"
                  className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6"
                >
                  <Zap className="h-3 w-3" />
                  {item.actions > 1 ? item.actions : ""}
                </Badge>
              )}
              {item.ideas > 0 && (
                <Badge
                  variant="secondary"
                  className="bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6"
                >
                  <Lightbulb className="h-3 w-3" />
                  {item.ideas > 1 ? item.ideas : ""}
                </Badge>
              )}
              {item.results > 0 && (
                <Badge
                  variant="secondary"
                  className="bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6"
                >
                  <Check className="h-4 w-4" />
                  {item.results > 1 ? item.results : ""}
                </Badge>
              )}
            </>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
};

// Enhanced Expandable Card Component
const EnhancedExpandableCard = ({
  section,
  onItemClick,
}: {
  section: BusinessSection;
  onItemClick: (item: BusinessItem) => void;
}) => {
  const [isExpanded, setIsExpanded] = useState(true);

  // Calculate section progress
  const totalItems = section.items.length;
  const validatedItems = section.items.filter(
    (item) => item.status === "validated"
  ).length;
  const progressPercentage =
    totalItems > 0 ? Math.round((validatedItems / totalItems) * 100) : 0;

  return (
    <Card className="bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40">
      <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 my-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <CardTitle className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {section.title}
                </CardTitle>
                <Badge variant="outline" className="text-xs">
                  {validatedItems}/{totalItems}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">
                  {progressPercentage}%
                </span>
                <ChevronUp
                  className={`h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform ${
                    isExpanded ? "rotate-0" : "rotate-180"
                  }`}
                />
              </div>
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        <CollapsibleContent>
          <CardContent className="py-0 px-3">
            {section.items
              .sort((a, b) => (a.order || 0) - (b.order || 0))
              .map((item) => (
                <EnhancedItemRow
                  key={item.id}
                  item={item}
                  onItemClick={onItemClick}
                />
              ))}
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  );
};

interface BusinessSectionsGridEnhancedProps {
  sections: BusinessSection[];
  onItemClick: (item: BusinessItem) => void;
}

export function BusinessSectionsGridEnhanced({
  sections,
  onItemClick,
}: BusinessSectionsGridEnhancedProps) {
  return (
    <div className="w-full relative z-40">
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min">
          {sections.map((section) => (
            <EnhancedExpandableCard
              key={section.id}
              section={section}
              onItemClick={onItemClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
