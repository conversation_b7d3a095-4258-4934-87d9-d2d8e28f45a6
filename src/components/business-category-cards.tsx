"use client";

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  getAllCategoryOpacities,
  getCategoryProgress,
} from "@/lib/cardOpacityUtils";
import { ArrowRight } from "lucide-react";

interface BusinessCategoryCardsProps {
  onCategoryClick?: (category: string) => void;
}

export function BusinessCategoryCards({
  onCategoryClick,
}: BusinessCategoryCardsProps) {
  const categoryOpacities = getAllCategoryOpacities();

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {categoryOpacities.map((categoryInfo) => {
        const progress = getCategoryProgress(categoryInfo.category);

        return (
          <Card
            key={categoryInfo.category}
            className={`bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group border`}
            style={{ opacity: categoryInfo.opacity }}
            onClick={() => onCategoryClick?.(categoryInfo.category)}
          >
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold">
                {categoryInfo.category}
              </CardTitle>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Next Items */}
              {progress.nextItems.length > 0 && (
                <div className="border-t pt-3">
                  <div className="text-xs text-muted-foreground mb-2">
                    Next items:
                  </div>
                  <div className="space-y-1">
                    {progress.nextItems.slice(0, 2).map((item, index) => (
                      <div
                        key={index}
                        className="text-xs text-gray-700 dark:text-gray-300 flex items-center gap-1"
                      >
                        <ArrowRight className="h-3 w-3 text-muted-foreground" />
                        <span className="truncate">{item}</span>
                      </div>
                    ))}
                    {progress.nextItems.length > 2 && (
                      <div className="text-xs text-muted-foreground">
                        +{progress.nextItems.length - 2} more
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Opacity Debug Info (remove in production) */}
              <div className="mt-3 pt-3 border-t">
                <div className="text-xs text-muted-foreground">
                  Opacity: {Math.round(categoryInfo.opacity * 100)}%
                </div>
                <div className="text-xs text-muted-foreground">
                  {categoryInfo.message}
                </div>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
