// Status counting system for business sections
import { businessItemsData } from "@/data/businessItemsData";
import type {
  BusinessItem,
  BusinessSection,
} from "@/types/BusinessSection.types";

export interface StatusCounts {
  ideas: number;
  actions: number;
  validated: number;
  invalidated: number;
  total: number;
}

export interface CategoryStatusCounts extends StatusCounts {
  category: string;
  items: BusinessItem[];
}

export interface DetailedStatusInfo {
  overall: StatusCounts;
  byCategory: CategoryStatusCounts[];
  byStatus: {
    idea: BusinessItem[];
    action: BusinessItem[];
    validated: BusinessItem[];
    invalidated: BusinessItem[];
  };
  readyToProgress: BusinessItem[];
  blocked: BusinessItem[];
}

/**
 * Count status distribution for a list of business items
 */
export function countItemStatuses(items: BusinessItem[]): StatusCounts {
  const counts = {
    ideas: 0,
    actions: 0,
    validated: 0,
    invalidated: 0,
    total: items.length,
  };

  items.forEach((item) => {
    switch (item.status) {
      case "idea":
        counts.ideas++;
        break;
      case "action":
        counts.actions++;
        break;
      case "validated":
        counts.validated++;
        break;
      case "invalidated":
        counts.invalidated++;
        break;
    }
  });

  return counts;
}

/**
 * Count badge numbers (ideas, actions, results) for items
 */
export function countBadgeNumbers(items: BusinessItem[]): {
  totalIdeas: number;
  totalActions: number;
  totalResults: number;
} {
  return items.reduce(
    (acc, item) => ({
      totalIdeas: acc.totalIdeas + (item.ideas || 0),
      totalActions: acc.totalActions + (item.actions || 0),
      totalResults: acc.totalResults + (item.results || 0),
    }),
    { totalIdeas: 0, totalActions: 0, totalResults: 0 }
  );
}

/**
 * Get status counts for a specific category
 */
export function getCategoryStatusCounts(
  sections: BusinessSection[],
  categoryName: string
): CategoryStatusCounts | null {
  const section = sections.find((s) => s.title === categoryName);
  if (!section) return null;

  const statusCounts = countItemStatuses(section.items);

  return {
    category: categoryName,
    items: section.items,
    ...statusCounts,
  };
}

/**
 * Get comprehensive status information for all business sections
 */
export function getDetailedStatusInfo(
  sections: BusinessSection[]
): DetailedStatusInfo {
  const allItems = sections.flatMap((section) => section.items);

  // Overall counts
  const overall = countItemStatuses(allItems);

  // By category
  const byCategory: CategoryStatusCounts[] = sections.map((section) => ({
    category: section.title,
    items: section.items,
    ...countItemStatuses(section.items),
  }));

  // By status
  const byStatus = {
    idea: allItems.filter((item) => item.status === "idea"),
    action: allItems.filter((item) => item.status === "action"),
    validated: allItems.filter((item) => item.status === "validated"),
    invalidated: allItems.filter((item) => item.status === "invalidated"),
  };

  // Items ready to progress (dependencies satisfied)
  const readyToProgress = allItems.filter((item) => {
    if (item.status === "validated") return false;
    if (!item.dependencies || item.dependencies.length === 0) return true;

    // Check if all dependencies are confirmed
    return item.dependencies.every((depId) => {
      const depItem = businessItemsData.find((dep) => dep.id === depId);
      return depItem?.status === "validated";
    });
  });

  // Blocked items (dependencies not satisfied)
  const blocked = allItems.filter((item) => {
    if (item.status === "validated") return false;
    if (!item.dependencies || item.dependencies.length === 0) return false;

    // Check if any dependencies are not confirmed
    return item.dependencies.some((depId) => {
      const depItem = businessItemsData.find((dep) => dep.id === depId);
      return depItem?.status !== "validated";
    });
  });

  return {
    overall,
    byCategory,
    byStatus,
    readyToProgress,
    blocked,
  };
}

/**
 * Calculate completion percentage for a category
 */
export function getCategoryCompletionPercentage(
  sections: BusinessSection[],
  categoryName: string
): number {
  const categoryData = getCategoryStatusCounts(sections, categoryName);
  if (!categoryData || categoryData.total === 0) return 0;

  return Math.round((categoryData.validated / categoryData.total) * 100);
}

/**
 * Get next recommended items to work on
 */
export function getRecommendedNextItems(
  sections: BusinessSection[],
  limit: number = 5
): BusinessItem[] {
  const statusInfo = getDetailedStatusInfo(sections);

  // Prioritize items that:
  // 1. Are ready to progress (dependencies satisfied)
  // 2. Have more dependents (blocking other items)
  // 3. Are earlier in the order

  return statusInfo.readyToProgress
    .map((item) => {
      // Count how many items depend on this one
      const dependentCount = businessItemsData.filter((dep) =>
        dep.dependencies?.includes(item.id)
      ).length;

      return {
        item,
        dependentCount,
        order: item.order || 999,
      };
    })
    .sort((a, b) => {
      // Sort by dependent count (desc), then by order (asc)
      if (a.dependentCount !== b.dependentCount) {
        return b.dependentCount - a.dependentCount;
      }
      return a.order - b.order;
    })
    .slice(0, limit)
    .map(({ item }) => item);
}

/**
 * Get progress summary for dashboard
 */
export function getProgressSummary(sections: BusinessSection[]): {
  totalProgress: number;
  categoryProgress: Array<{
    category: string;
    progress: number;
    status: "not-started" | "in-progress" | "completed";
  }>;
  nextMilestones: string[];
  blockedCount: number;
} {
  const statusInfo = getDetailedStatusInfo(sections);

  const totalProgress =
    statusInfo.overall.total > 0
      ? Math.round(
          (statusInfo.overall.validated / statusInfo.overall.total) * 100
        )
      : 0;

  const categoryProgress = statusInfo.byCategory.map((category) => {
    const progress =
      category.total > 0
        ? Math.round((category.validated / category.total) * 100)
        : 0;

    let status: "not-started" | "in-progress" | "completed";
    if (progress === 0) status = "not-started";
    else if (progress === 100) status = "completed";
    else status = "in-progress";

    return {
      category: category.category,
      progress,
      status,
    };
  });

  const nextMilestones = getRecommendedNextItems(sections, 3).map(
    (item) => item.title
  );

  return {
    totalProgress,
    categoryProgress,
    nextMilestones,
    blockedCount: statusInfo.blocked.length,
  };
}

/**
 * Validate status counts for consistency
 */
export function validateStatusCounts(sections: BusinessSection[]): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  sections.forEach((section) => {
    section.items.forEach((item) => {
      // Check if badge counts are non-negative
      if (item.ideas < 0) {
        errors.push(`${item.title}: Ideas count cannot be negative`);
      }
      if (item.actions < 0) {
        errors.push(`${item.title}: Actions count cannot be negative`);
      }
      if (item.results < 0) {
        errors.push(`${item.title}: Results count cannot be negative`);
      }

      // Check if confirmed items have results
      if (item.status === "validated" && item.results === 0) {
        errors.push(
          `${item.title}: Validated items should have at least 1 result`
        );
      }

      // Check if invalidated items don't have badges
      if (
        item.status === "invalidated" &&
        (item.ideas > 0 || item.actions > 0 || item.results > 0)
      ) {
        errors.push(
          `${item.title}: Invalidated items should not have idea/action/result badges`
        );
      }
    });
  });

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Export utility functions for easy access
 */
export const statusCountingUtils = {
  countItemStatuses,
  countBadgeNumbers,
  getCategoryStatusCounts,
  getDetailedStatusInfo,
  getCategoryCompletionPercentage,
  getRecommendedNextItems,
  getProgressSummary,
  validateStatusCounts,
};
