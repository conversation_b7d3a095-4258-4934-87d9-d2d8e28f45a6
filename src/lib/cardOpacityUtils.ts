// Card opacity utilities based on business item status
import { businessItemsData } from "@/data/businessItemsData";

export interface CategoryOpacityInfo {
  category: string;
  opacity: number;
  statusDistribution: {
    idea: number;
    action: number;
    validated: number;
    invalidated: number;
    confirmed: number;
    unproven: number;
  };
  dominantStatus:
    | "idea"
    | "action"
    | "validated"
    | "invalidated"
    | "confirmed"
    | "unproven";
  message: string;
}

/**
 * Calculate opacity for a category based on status distribution
 * Rules:
 * - Any confirmed: 50% opacity
 * - Any action: 20% opacity
 * - Any idea: 10% opacity
 * - All unproven: 100% opacity
 */
export const calculateCategoryOpacity = (
  category: string
): CategoryOpacityInfo => {
  const categoryItems = businessItemsData.filter(
    (item) => item.category === category
  );

  if (categoryItems.length === 0) {
    return {
      category,
      opacity: 1,
      statusDistribution: {
        idea: 0,
        action: 0,
        validated: 0,
        invalidated: 0,
        confirmed: 0,
        unproven: 0,
      },
      dominantStatus: "invalidated",
      message: "No items in category",
    };
  }

  // Count status distribution
  const statusDistribution = {
    idea: categoryItems.filter((item) => item.status === "idea").length,
    action: categoryItems.filter((item) => item.status === "action").length,
    validated: categoryItems.filter((item) => item.status === "validated")
      .length,
    invalidated: categoryItems.filter((item) => item.status === "invalidated")
      .length,
    confirmed: categoryItems.filter((item) => item.status === "confirmed")
      .length,
    unproven: categoryItems.filter((item) => item.status === "unproven").length,
  };

  // Determine opacity based on priority rules
  let opacity = 1; // Default 100%
  let dominantStatus:
    | "idea"
    | "action"
    | "validated"
    | "invalidated"
    | "confirmed"
    | "unproven" = "invalidated";
  let message = "All items are invalidated";

  if (statusDistribution.validated > 0) {
    opacity = 0.5; // 50%
    dominantStatus = "validated";
    message = `${statusDistribution.validated} validated items`;
  } else if (statusDistribution.action > 0) {
    opacity = 0.2; // 20%
    dominantStatus = "action";
    message = `${statusDistribution.action} items in progress`;
  } else if (statusDistribution.idea > 0) {
    opacity = 0.1; // 10%
    dominantStatus = "idea";
    message = `${statusDistribution.idea} items with ideas`;
  }

  return {
    category,
    opacity,
    statusDistribution,
    dominantStatus,
    message,
  };
};

/**
 * Get opacity information for all categories
 */
export const getAllCategoryOpacities = (): CategoryOpacityInfo[] => {
  const categories = ["Market", "Solution", "Sales & Marketing", "Company"];
  return categories.map(calculateCategoryOpacity);
};

/**
 * Get CSS opacity value as string
 */
export const getOpacityStyle = (category: string): string => {
  const opacityInfo = calculateCategoryOpacity(category);
  return opacityInfo.opacity.toString();
};

/**
 * Get CSS classes for opacity styling
 */
export const getOpacityClasses = (category: string): string => {
  const opacityInfo = calculateCategoryOpacity(category);

  switch (opacityInfo.dominantStatus) {
    case "validated":
      return "opacity-50"; // 50%
    case "action":
      return "opacity-20"; // 20%
    case "idea":
      return "opacity-10"; // 10%
    default:
      return "opacity-100"; // 100%
  }
};

/**
 * Get status indicator for category cards
 */
export const getCategoryStatusIndicator = (
  category: string
): {
  color: string;
  text: string;
  icon: string;
} => {
  const opacityInfo = calculateCategoryOpacity(category);

  switch (opacityInfo.dominantStatus) {
    case "validated":
      return {
        color: "text-green-600 dark:text-green-400",
        text: "Validated",
        icon: "✓",
      };
    case "action":
      return {
        color: "text-blue-600 dark:text-blue-400",
        text: "In Progress",
        icon: "⚡",
      };
    case "idea":
      return {
        color: "text-yellow-600 dark:text-yellow-400",
        text: "Ideas",
        icon: "💡",
      };
    default:
      return {
        color: "text-gray-600 dark:text-gray-400",
        text: "Not Started",
        icon: "○",
      };
  }
};

/**
 * Get completion percentage for a category
 */
export const getCategoryCompletion = (category: string): number => {
  const categoryItems = businessItemsData.filter(
    (item) => item.category === category
  );
  if (categoryItems.length === 0) return 0;

  const validatedItems = categoryItems.filter(
    (item) => item.status === "validated"
  );
  return Math.round((validatedItems.length / categoryItems.length) * 100);
};

/**
 * Get progress information for category
 */
export const getCategoryProgress = (
  category: string
): {
  completed: number;
  total: number;
  percentage: number;
  nextItems: string[];
} => {
  const categoryItems = businessItemsData.filter(
    (item) => item.category === category
  );
  const completed = categoryItems.filter(
    (item) => item.status === "validated"
  ).length;
  const total = categoryItems.length;
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

  // Get next items that can be worked on (dependencies satisfied)
  const nextItems = categoryItems
    .filter((item) => {
      if (item.status === "validated") return false;

      // Check if dependencies are satisfied
      if (!item.dependencies || item.dependencies.length === 0) return true;

      return item.dependencies.every((depId) => {
        const depItem = businessItemsData.find((dep) => dep.id === depId);
        return depItem?.status === "validated";
      });
    })
    .slice(0, 3) // Limit to 3 next items
    .map((item) => item.title);

  return {
    completed,
    total,
    percentage,
    nextItems,
  };
};
