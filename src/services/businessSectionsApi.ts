// API service layer for business sections
import { fetchBusinessSectionsNew } from "@/lib/businessSectionsDataNew";
import type {
  BusinessItem,
  BusinessSection,
} from "@/types/BusinessSection.types";

// API Configuration

// Error types
export class ApiError extends Error {
  constructor(message: string, public status: number, public code?: string) {
    super(message);
    this.name = "ApiError";
  }
}

// Request wrapper with error handling

// Business Sections API
export class BusinessSectionsApi {
  // Fetch all business sections for a project
  static async fetchSections(_projectId: string): Promise<BusinessSection[]> {
    try {
      // Use the new comprehensive data with proper delay simulation
      return await fetchBusinessSectionsNew();

      // Uncomment when API is ready:
      // return await apiRequest<BusinessSection[]>(`/projects/${projectId}/business-sections`);
    } catch (error) {
      console.error("Failed to fetch business sections:", error);
      throw error;
    }
  }

  // Fetch a specific business item
  static async fetchItem(
    projectId: string,
    itemId: string
  ): Promise<BusinessItem> {
    try {
      // Mock implementation
      const sections = await this.fetchSections(projectId);
      const item = sections
        .flatMap((section) => section.items)
        .find((item) => item.id === itemId);

      if (!item) {
        throw new ApiError("Business item not found", 404, "ITEM_NOT_FOUND");
      }

      return item;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem>(`/projects/${projectId}/business-sections/items/${itemId}`);
    } catch (error) {
      console.error("Failed to fetch business item:", error);
      throw error;
    }
  }

  // Update a business item
  static async updateItem(
    projectId: string,
    itemId: string,
    updates: Partial<BusinessItem>
  ): Promise<BusinessItem> {
    try {
      // Mock implementation - in real app, this would update the backend
      const item = await this.fetchItem(projectId, itemId);
      const updatedItem = { ...item, ...updates };

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      return updatedItem;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem>(
      //   `/projects/${projectId}/business-sections/items/${itemId}`,
      //   {
      //     method: "PATCH",
      //     body: JSON.stringify(updates),
      //   }
      // );
    } catch (error) {
      console.error("Failed to update business item:", error);
      throw error;
    }
  }

  // Create a new business item
  static async createItem(
    _projectId: string,
    _sectionId: string,
    item: Omit<BusinessItem, "id">
  ): Promise<BusinessItem> {
    try {
      // Mock implementation
      const newItem: BusinessItem = {
        ...item,
        id: `item-${Date.now()}`, // Generate temporary ID
      };

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      return newItem;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem>(
      //   `/projects/${projectId}/business-sections/${sectionId}/items`,
      //   {
      //     method: "POST",
      //     body: JSON.stringify(item),
      //   }
      // );
    } catch (error) {
      console.error("Failed to create business item:", error);
      throw error;
    }
  }

  // Delete a business item
  static async deleteItem(_projectId: string, _itemId: string): Promise<void> {
    try {
      // Mock implementation
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Uncomment when API is ready:
      // await apiRequest<void>(
      //   `/projects/${projectId}/business-sections/items/${itemId}`,
      //   {
      //     method: "DELETE",
      //   }
      // );
    } catch (error) {
      console.error("Failed to delete business item:", error);
      throw error;
    }
  }

  // Bulk update multiple items
  static async bulkUpdateItems(
    projectId: string,
    updates: Array<{ itemId: string; updates: Partial<BusinessItem> }>
  ): Promise<BusinessItem[]> {
    try {
      // Mock implementation
      const updatedItems: BusinessItem[] = [];

      for (const { itemId, updates: itemUpdates } of updates) {
        const updatedItem = await this.updateItem(
          projectId,
          itemId,
          itemUpdates
        );
        updatedItems.push(updatedItem);
      }

      return updatedItems;

      // Uncomment when API is ready:
      // return await apiRequest<BusinessItem[]>(
      //   `/projects/${projectId}/business-sections/items/bulk-update`,
      //   {
      //     method: "PATCH",
      //     body: JSON.stringify({ updates }),
      //   }
      // );
    } catch (error) {
      console.error("Failed to bulk update business items:", error);
      throw error;
    }
  }

  // Get business sections analytics
  static async getAnalytics(projectId: string): Promise<{
    totalItems: number;
    completedItems: number;
    completionPercentage: number;
    categoryBreakdown: Record<string, { total: number; completed: number }>;
  }> {
    try {
      const sections = await this.fetchSections(projectId);
      const allItems = sections.flatMap((section) => section.items);

      const totalItems = allItems.length;
      const completedItems = allItems.filter(
        (item) => item.status === "validated"
      ).length;
      const completionPercentage =
        totalItems > 0 ? (completedItems / totalItems) * 100 : 0;

      const categoryBreakdown: Record<
        string,
        { total: number; completed: number }
      > = {};

      sections.forEach((section) => {
        const total = section.items.length;
        const completed = section.items.filter(
          (item) => item.status === "validated"
        ).length;
        categoryBreakdown[section.title] = { total, completed };
      });

      return {
        totalItems,
        completedItems,
        completionPercentage,
        categoryBreakdown,
      };

      // Uncomment when API is ready:
      // return await apiRequest<any>(`/projects/${projectId}/business-sections/analytics`);
    } catch (error) {
      console.error("Failed to fetch business sections analytics:", error);
      throw error;
    }
  }
}

// Export convenience functions
export const {
  fetchSections,
  fetchItem,
  updateItem,
  createItem,
  deleteItem,
  bulkUpdateItems,
  getAnalytics,
} = BusinessSectionsApi;
