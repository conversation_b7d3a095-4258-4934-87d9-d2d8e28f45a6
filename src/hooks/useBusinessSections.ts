// React hook for managing business sections state
import { useToast } from "@/hooks/useToast";
import { ApiError, BusinessSectionsApi } from "@/services/businessSectionsApi";
import type {
  BusinessItem,
  BusinessSection,
} from "@/types/BusinessSection.types";
import { useCallback, useEffect, useState } from "react";

export interface UseBusinessSectionsState {
  sections: BusinessSection[];
  isLoading: boolean;
  error: string | null;
  analytics: {
    totalItems: number;
    completedItems: number;
    completionPercentage: number;
    categoryBreakdown: Record<string, { total: number; completed: number }>;
  } | null;
}

export interface UseBusinessSectionsActions {
  fetchSections: () => Promise<void>;
  updateItem: (itemId: string, updates: Partial<BusinessItem>) => Promise<void>;
  createItem: (
    sectionId: string,
    item: Omit<BusinessItem, "id">
  ) => Promise<void>;
  deleteItem: (itemId: string) => Promise<void>;
  bulkUpdateItems: (
    updates: Array<{ itemId: string; updates: Partial<BusinessItem> }>
  ) => Promise<void>;
  refreshAnalytics: () => Promise<void>;
  clearError: () => void;
}

export interface UseBusinessSectionsReturn
  extends UseBusinessSectionsState,
    UseBusinessSectionsActions {}

export function useBusinessSections(
  projectId: string
): UseBusinessSectionsReturn {
  const [state, setState] = useState<UseBusinessSectionsState>({
    sections: [],
    isLoading: false,
    error: null,
    analytics: null,
  });

  const toast = useToast();

  // Helper to update state
  const updateState = useCallback(
    (updates: Partial<UseBusinessSectionsState>) => {
      setState((prev) => ({ ...prev, ...updates }));
    },
    []
  );

  // Helper to handle errors
  const handleError = useCallback(
    (error: unknown, action: string) => {
      console.error(`Error in ${action}:`, error);

      let errorMessage = "An unexpected error occurred";

      if (error instanceof ApiError) {
        errorMessage = error.message;
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      updateState({ error: errorMessage, isLoading: false });

      toast.error("Error", {
        description: errorMessage,
      });
    },
    [updateState, toast]
  );

  // Fetch business sections
  const fetchSections = useCallback(async () => {
    try {
      updateState({ isLoading: true, error: null });

      const sections = await BusinessSectionsApi.fetchSections(projectId);

      updateState({
        sections,
        isLoading: false,
        error: null,
      });
    } catch (error) {
      handleError(error, "fetchSections");
    }
  }, [projectId, updateState, handleError]);

  // Update a business item
  const updateItem = useCallback(
    async (itemId: string, updates: Partial<BusinessItem>) => {
      try {
        updateState({ isLoading: true, error: null });

        const updatedItem = await BusinessSectionsApi.updateItem(
          projectId,
          itemId,
          updates
        );

        // Update the item in the local state
        setState((prev) => ({
          ...prev,
          sections: prev.sections.map((section) => ({
            ...section,
            items: section.items.map((item) =>
              item.id === itemId ? { ...item, ...updatedItem } : item
            ),
          })),
          isLoading: false,
          error: null,
        }));

        toast.success("Success", {
          description: "Business item updated successfully",
        });
      } catch (error) {
        handleError(error, "updateItem");
      }
    },
    [projectId, handleError, toast]
  );

  // Create a new business item
  const createItem = useCallback(
    async (sectionId: string, item: Omit<BusinessItem, "id">) => {
      try {
        updateState({ isLoading: true, error: null });

        const newItem = await BusinessSectionsApi.createItem(
          projectId,
          sectionId,
          item
        );

        // Add the item to the local state
        setState((prev) => ({
          ...prev,
          sections: prev.sections.map((section) =>
            section.id === sectionId
              ? { ...section, items: [...section.items, newItem] }
              : section
          ),
          isLoading: false,
          error: null,
        }));

        toast.success("Success", {
          description: "Business item created successfully",
        });
      } catch (error) {
        handleError(error, "createItem");
      }
    },
    [projectId, handleError, toast]
  );

  // Delete a business item
  const deleteItem = useCallback(
    async (itemId: string) => {
      try {
        updateState({ isLoading: true, error: null });

        await BusinessSectionsApi.deleteItem(projectId, itemId);

        // Remove the item from local state
        setState((prev) => ({
          ...prev,
          sections: prev.sections.map((section) => ({
            ...section,
            items: section.items.filter((item) => item.id !== itemId),
          })),
          isLoading: false,
          error: null,
        }));

        toast.success("Success", {
          description: "Business item deleted successfully",
        });
      } catch (error) {
        handleError(error, "deleteItem");
      }
    },
    [projectId, handleError, toast]
  );

  // Bulk update multiple items
  const bulkUpdateItems = useCallback(
    async (
      updates: Array<{ itemId: string; updates: Partial<BusinessItem> }>
    ) => {
      try {
        updateState({ isLoading: true, error: null });

        const updatedItems = await BusinessSectionsApi.bulkUpdateItems(
          projectId,
          updates
        );

        // Update multiple items in local state
        setState((prev) => {
          const updatedSections = prev.sections.map((section) => ({
            ...section,
            items: section.items.map((item) => {
              const updatedItem = updatedItems.find(
                (updated) => updated.id === item.id
              );
              return updatedItem ? { ...item, ...updatedItem } : item;
            }),
          }));

          return {
            ...prev,
            sections: updatedSections,
            isLoading: false,
            error: null,
          };
        });

        toast.success("Success", {
          description: `${updates.length} business items updated successfully`,
        });
      } catch (error) {
        handleError(error, "bulkUpdateItems");
      }
    },
    [projectId, handleError, toast]
  );

  // Refresh analytics
  const refreshAnalytics = useCallback(async () => {
    try {
      const analytics = await BusinessSectionsApi.getAnalytics(projectId);
      updateState({ analytics });
    } catch (error) {
      console.error("Failed to refresh analytics:", error);
      // Don't show toast for analytics errors as they're not critical
    }
  }, [projectId, updateState]);

  // Clear error
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // Load initial data
  useEffect(() => {
    if (projectId) {
      fetchSections();
      refreshAnalytics();
    }
  }, [projectId, fetchSections, refreshAnalytics]);

  return {
    ...state,
    fetchSections,
    updateItem,
    createItem,
    deleteItem,
    bulkUpdateItems,
    refreshAnalytics,
    clearError,
  };
}
