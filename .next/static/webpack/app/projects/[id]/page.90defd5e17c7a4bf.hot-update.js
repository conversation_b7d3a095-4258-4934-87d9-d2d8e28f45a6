"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx":
/*!***************************************************************************!*\
  !*** ./src/components/business-sections/BusinessSectionsGridEnhanced.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessSectionsGridEnhanced: () => (/* binding */ BusinessSectionsGridEnhanced)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ BusinessSectionsGridEnhanced auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Enhanced Item Row Component with dependency checking\nconst EnhancedItemRow = (param)=>{\n    let { item, onItemClick } = param;\n    // Check dependencies if item has them\n    const dependencyCheck = item.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_4__.checkItemDependencies)(item.id) : null;\n    const isBlocked = dependencyCheck && !dependencyCheck.isValid;\n    const getStatusStyles = function(status) {\n        let blocked = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (blocked) {\n            return \"bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60\";\n        }\n        switch(status){\n            case \"validated\":\n                return \"bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-700\";\n            case \"action\":\n                return \"bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-700\";\n            case \"idea\":\n                return \"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-700\";\n            case \"invalidated\":\n                return \"bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-600\";\n            default:\n                return \"bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300\";\n        }\n    };\n    const itemContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] \".concat(getStatusStyles(item.status, isBlocked !== null && isBlocked !== void 0 ? isBlocked : false), \" hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 \").concat(isBlocked ? \"cursor-not-allowed opacity-50\" : \"\"),\n        onClick: ()=>{\n            if (!isBlocked) {\n                onItemClick(item);\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 flex-1 min-w-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium text-sm truncate\",\n                        children: item.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 flex-shrink-0\",\n                children: item.status !== \"invalidated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        item.actions > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.actions > 1 ? item.actions : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, undefined),\n                        item.ideas > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.ideas > 1 ? item.ideas : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 15\n                        }, undefined),\n                        item.results > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.results > 1 ? item.results : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n    return itemContent;\n};\n_c = EnhancedItemRow;\n// Enhanced Expandable Card Component\nconst EnhancedExpandableCard = (param)=>{\n    let { section, onItemClick } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Calculate section progress\n    const totalItems = section.items.length;\n    const validatedItems = section.items.filter((item)=>item.status === \"validated\").length;\n    const progressPercentage = totalItems > 0 ? Math.round(validatedItems / totalItems * 100) : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n            open: isExpanded,\n            onOpenChange: setIsExpanded,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 my-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                validatedItems,\n                                                \"/\",\n                                                totalItems\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                progressPercentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform \".concat(isExpanded ? \"rotate-0\" : \"rotate-180\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-0 px-3\",\n                        children: section.items.sort((a, b)=>(a.order || 0) - (b.order || 0)).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedItemRow, {\n                                item: item,\n                                onItemClick: onItemClick\n                            }, item.id, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedExpandableCard, \"MzqrZ0LJxgqPa6EOF1Vxw0pgYA4=\");\n_c1 = EnhancedExpandableCard;\nfunction BusinessSectionsGridEnhanced(param) {\n    let { sections, onItemClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min\",\n                children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedExpandableCard, {\n                        section: section,\n                        onItemClick: onItemClick\n                    }, section.id, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 185,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BusinessSectionsGridEnhanced;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EnhancedItemRow\");\n$RefreshReg$(_c1, \"EnhancedExpandableCard\");\n$RefreshReg$(_c2, \"BusinessSectionsGridEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\n"));

/***/ })

});