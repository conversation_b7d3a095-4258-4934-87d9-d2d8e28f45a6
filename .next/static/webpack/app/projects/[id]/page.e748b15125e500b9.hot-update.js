"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ui/animated-ai-input.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/animated-ai-input.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AI_Prompt: () => (/* binding */ AI_Prompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/sidebar-button */ \"(app-pages-browser)/./src/components/ui/sidebar-button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ AI_Prompt auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction useAutoResizeTextarea(param) {\n    let { minHeight, maxHeight } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const adjustHeight = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"useAutoResizeTextarea.useCallback[adjustHeight]\": (reset)=>{\n            const textarea = textareaRef.current;\n            if (!textarea) return;\n            if (reset) {\n                textarea.style.height = \"\".concat(minHeight, \"px\");\n                return;\n            }\n            textarea.style.height = \"\".concat(minHeight, \"px\");\n            const newHeight = Math.max(minHeight, Math.min(textarea.scrollHeight, maxHeight !== null && maxHeight !== void 0 ? maxHeight : Number.POSITIVE_INFINITY));\n            textarea.style.height = \"\".concat(newHeight, \"px\");\n        }\n    }[\"useAutoResizeTextarea.useCallback[adjustHeight]\"], [\n        minHeight,\n        maxHeight\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"useAutoResizeTextarea.useEffect\": ()=>{\n            const textarea = textareaRef.current;\n            if (textarea) {\n                textarea.style.height = \"\".concat(minHeight, \"px\");\n            }\n        }\n    }[\"useAutoResizeTextarea.useEffect\"], [\n        minHeight\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"useAutoResizeTextarea.useEffect\": ()=>{\n            const handleResize = {\n                \"useAutoResizeTextarea.useEffect.handleResize\": ()=>adjustHeight()\n            }[\"useAutoResizeTextarea.useEffect.handleResize\"];\n            window.addEventListener(\"resize\", handleResize);\n            return ({\n                \"useAutoResizeTextarea.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n            })[\"useAutoResizeTextarea.useEffect\"];\n        }\n    }[\"useAutoResizeTextarea.useEffect\"], [\n        adjustHeight\n    ]);\n    return {\n        textareaRef,\n        adjustHeight\n    };\n}\n_s(useAutoResizeTextarea, \"dY5gUJDyLTt7nR3p8fNJ7y+Lo90=\");\nconst OPENAI_ICON = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n    children: [\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 256 260\",\n            \"aria-label\": \"OpenAI Icon\",\n            className: \"w-4 h-4 dark:hidden block\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"OpenAI Icon Light\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M239.184 106.203a64.716 64.716 0 0 0-5.576-53.103C219.452 28.459 191 15.784 163.213 21.74A65.586 65.586 0 0 0 52.096 45.22a64.716 64.716 0 0 0-43.23 31.36c-14.31 24.602-11.061 55.634 8.033 76.74a64.665 64.665 0 0 0 5.525 53.102c14.174 24.65 42.644 37.324 70.446 31.36a64.72 64.72 0 0 0 48.754 21.744c28.481.025 53.714-18.361 62.414-45.481a64.767 64.767 0 0 0 43.229-31.36c14.137-24.558 10.875-55.423-8.083-76.483Zm-97.56 136.338a48.397 48.397 0 0 1-31.105-11.255l1.535-.87 51.67-29.825a8.595 8.595 0 0 0 4.247-7.367v-72.85l21.845 12.636c.218.111.37.32.409.563v60.367c-.056 26.818-21.783 48.545-48.601 48.601Zm-104.466-44.61a48.345 48.345 0 0 1-5.781-32.589l1.534.921 51.722 29.826a8.339 8.339 0 0 0 8.441 0l63.181-36.425v25.221a.87.87 0 0 1-.358.665l-52.335 30.184c-23.257 13.398-52.97 5.431-66.404-17.803ZM23.549 85.38a48.499 48.499 0 0 1 25.58-21.333v61.39a8.288 8.288 0 0 0 4.195 7.316l62.874 36.272-21.845 12.636a.819.819 0 0 1-.767 0L41.353 151.53c-23.211-13.454-31.171-43.144-17.804-66.405v.256Zm179.466 41.695-63.08-36.63L161.73 77.86a.819.819 0 0 1 .768 0l52.233 30.184a48.6 48.6 0 0 1-7.316 87.635v-61.391a8.544 8.544 0 0 0-4.4-7.213Zm21.742-32.69-1.535-.922-51.619-30.081a8.39 8.39 0 0 0-8.492 0L99.98 99.808V74.587a.716.716 0 0 1 .307-.665l52.233-30.133a48.652 48.652 0 0 1 72.236 50.391v.205ZM88.061 139.097l-21.845-12.585a.87.87 0 0 1-.41-.614V65.685a48.652 48.652 0 0 1 79.757-37.346l-1.535.87-51.67 29.825a8.595 8.595 0 0 0-4.246 7.367l-.051 72.697Zm11.868-25.58 28.138-16.217 28.188 16.218v32.434l-28.086 16.218-28.188-16.218-.052-32.434Z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 60,\n            columnNumber: 5\n        }, undefined),\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 256 260\",\n            \"aria-label\": \"OpenAI Icon\",\n            className: \"w-4 h-4 hidden dark:block\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"OpenAI Icon Dark\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fill: \"#fff\",\n                    d: \"M239.184 106.203a64.716 64.716 0 0 0-5.576-53.103C219.452 28.459 191 15.784 163.213 21.74A65.586 65.586 0 0 0 52.096 45.22a64.716 64.716 0 0 0-43.23 31.36c-14.31 24.602-11.061 55.634 8.033 76.74a64.665 64.665 0 0 0 5.525 53.102c14.174 24.65 42.644 37.324 70.446 31.36a64.72 64.72 0 0 0 48.754 21.744c28.481.025 53.714-18.361 62.414-45.481a64.767 64.767 0 0 0 43.229-31.36c14.137-24.558 10.875-55.423-8.083-76.483Zm-97.56 136.338a48.397 48.397 0 0 1-31.105-11.255l1.535-.87 51.67-29.825a8.595 8.595 0 0 0 4.247-7.367v-72.85l21.845 12.636c.218.111.37.32.409.563v60.367c-.056 26.818-21.783 48.545-48.601 48.601Zm-104.466-44.61a48.345 48.345 0 0 1-5.781-32.589l1.534.921 51.722 29.826a8.339 8.339 0 0 0 8.441 0l63.181-36.425v25.221a.87.87 0 0 1-.358.665l-52.335 30.184c-23.257 13.398-52.97 5.431-66.404-17.803ZM23.549 85.38a48.499 48.499 0 0 1 25.58-21.333v61.39a8.288 8.288 0 0 0 4.195 7.316l62.874 36.272-21.845 12.636a.819.819 0 0 1-.767 0L41.353 151.53c-23.211-13.454-31.171-43.144-17.804-66.405v.256Zm179.466 41.695-63.08-36.63L161.73 77.86a.819.819 0 0 1 .768 0l52.233 30.184a48.6 48.6 0 0 1-7.316 87.635v-61.391a8.544 8.544 0 0 0-4.4-7.213Zm21.742-32.69-1.535-.922-51.619-30.081a8.39 8.39 0 0 0-8.492 0L99.98 99.808V74.587a.716.716 0 0 1 .307-.665l52.233-30.133a48.652 48.652 0 0 1 72.236 50.391v.205ZM88.061 139.097l-21.845-12.585a.87.87 0 0 1-.41-.614V65.685a48.652 48.652 0 0 1 79.757-37.346l-1.535.87-51.67 29.825a8.595 8.595 0 0 0-4.246 7.367l-.051 72.697Zm11.868-25.58 28.138-16.217 28.188 16.218v32.434l-28.086 16.218-28.188-16.218-.052-32.434Z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 71,\n            columnNumber: 5\n        }, undefined)\n    ]\n}, void 0, true);\nfunction AI_Prompt() {\n    _s1();\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const { textareaRef, adjustHeight } = useAutoResizeTextarea({\n        minHeight: 60,\n        maxHeight: 200\n    });\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"GPT-4-1 Mini\");\n    const AI_MODELS = [\n        \"o3-mini\",\n        \"Gemini 2.5 Flash\",\n        \"Claude 3.5 Sonnet\",\n        \"GPT-4-1 Mini\",\n        \"GPT-4-1\"\n    ];\n    const MODEL_ICONS = {\n        \"o3-mini\": OPENAI_ICON,\n        \"Gemini 2.5 Flash\": /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            height: \"1em\",\n            className: \"w-4 h-4\",\n            viewBox: \"0 0 24 24\",\n            xmlns: \"http://www.w3.org/2000/svg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"Gemini\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"defs\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"linearGradient\", {\n                        id: \"lobe-icons-gemini-fill\",\n                        x1: \"0%\",\n                        x2: \"68.73%\",\n                        y1: \"100%\",\n                        y2: \"30.395%\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"0%\",\n                                stopColor: \"#1C7DFF\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"52.021%\",\n                                stopColor: \"#1C69FF\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"stop\", {\n                                offset: \"100%\",\n                                stopColor: \"#F0DCD6\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M12 24A14.304 14.304 0 000 12 14.304 14.304 0 0012 0a14.305 14.305 0 0012 12 14.305 14.305 0 00-12 12\",\n                    fill: \"url(#lobe-icons-gemini-fill)\",\n                    fillRule: \"nonzero\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, this),\n        \"Claude 3.5 Sonnet\": /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"#000\",\n                    fillRule: \"evenodd\",\n                    className: \"w-4 h-4 dark:hidden block\",\n                    viewBox: \"0 0 24 24\",\n                    width: \"1em\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Anthropic Icon Light\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M13.827 3.52h3.603L24 20h-3.603l-6.57-16.48zm-7.258 0h3.767L16.906 20h-3.674l-1.343-3.461H5.017l-1.344 3.46H0L6.57 3.522zm4.132 9.959L8.453 7.687 6.205 13.48H10.7z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    fill: \"#fff\",\n                    fillRule: \"evenodd\",\n                    className: \"w-4 h-4 hidden dark:block\",\n                    viewBox: \"0 0 24 24\",\n                    width: \"1em\",\n                    xmlns: \"http://www.w3.org/2000/svg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: \"Anthropic Icon Dark\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            d: \"M13.827 3.52h3.603L24 20h-3.603l-6.57-16.48zm-7.258 0h3.767L16.906 20h-3.674l-1.343-3.461H5.017l-1.344 3.46H0L6.57 3.522zm4.132 9.959L8.453 7.687 6.205 13.48H10.7z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true),\n        \"GPT-4-1 Mini\": OPENAI_ICON,\n        \"GPT-4-1\": OPENAI_ICON\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey && value.trim()) {\n            e.preventDefault();\n            setValue(\"\");\n            adjustHeight(true);\n        // Here you can add message sending\n        }\n    };\n    const handleSend = ()=>{\n        if (!value.trim()) return;\n        setValue(\"\");\n        adjustHeight(true);\n    // Here you can add message sending\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black/5 dark:bg-white/5 rounded-2xl p-1.5\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex flex-col\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto relative\",\n                        style: {\n                            maxHeight: \"400px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__.Textarea, {\n                                id: \"ai-input-15\",\n                                value: value,\n                                placeholder: \"What can I do for you?\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full bg-white-100/60 dark:bg-gray-700/60 rounded-xl px-3 py-2 pr-12 border border-gray-300/50 dark:border-gray-600/50 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 resize-none focus-visible:ring-0 focus-visible:ring-offset-0 transition-all duration-200\", \"min-h-[120px]\", \"hover:bg-gray-200/80 dark:hover:bg-gray-700/80 hover:border-gray-400 dark:hover:border-gray-500\", value && \"bg-gray-200/90 dark:bg-gray-700/90 border-gray-400 dark:border-gray-500\"),\n                                ref: textareaRef,\n                                onKeyDown: handleKeyDown,\n                                onChange: (e)=>{\n                                    setValue(e.target.value);\n                                    adjustHeight();\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-2 right-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_1__.SidebarButton, {\n                                    type: \"button\",\n                                    icon: _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    layout: \"icon-only\",\n                                    size: \"md\",\n                                    variant: value.trim() ? \"secondary\" : \"ghost\",\n                                    iconClassName: \"text-white\",\n                                    disabled: !value.trim(),\n                                    \"aria-label\": \"Send message\",\n                                    onClick: handleSend,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"transition-all duration-200\", value.trim() ? \"bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg hover:shadow-xl\" : \"opacity-40 bg-gray-300/50 dark:bg-gray-600/50 hover:opacity-60\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                lineNumber: 183,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 182,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n        lineNumber: 181,\n        columnNumber: 5\n    }, this);\n}\n_s1(AI_Prompt, \"7miKjfTyNby4oG5huPpm3748zAw=\", false, function() {\n    return [\n        useAutoResizeTextarea\n    ];\n});\n_c = AI_Prompt;\nvar _c;\n$RefreshReg$(_c, \"AI_Prompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL3VpL2FuaW1hdGVkLWFpLWlucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBRStEO0FBQ1g7QUFDbkI7QUFDUztBQUN1QjtBQU9qRSxTQUFTUSxzQkFBc0IsS0FHRjtRQUhFLEVBQzdCQyxTQUFTLEVBQ1RDLFNBQVMsRUFDa0IsR0FIRTs7SUFJN0IsTUFBTUMsY0FBY0wsNkNBQU1BLENBQXNCO0lBRWhELE1BQU1NLGVBQWVSLGtEQUFXQTsyREFDOUIsQ0FBQ1M7WUFDQyxNQUFNQyxXQUFXSCxZQUFZSSxPQUFPO1lBQ3BDLElBQUksQ0FBQ0QsVUFBVTtZQUVmLElBQUlELE9BQU87Z0JBQ1RDLFNBQVNFLEtBQUssQ0FBQ0MsTUFBTSxHQUFHLEdBQWEsT0FBVlIsV0FBVTtnQkFDckM7WUFDRjtZQUVBSyxTQUFTRSxLQUFLLENBQUNDLE1BQU0sR0FBRyxHQUFhLE9BQVZSLFdBQVU7WUFFckMsTUFBTVMsWUFBWUMsS0FBS0MsR0FBRyxDQUN4QlgsV0FDQVUsS0FBS0UsR0FBRyxDQUFDUCxTQUFTUSxZQUFZLEVBQUVaLHNCQUFBQSx1QkFBQUEsWUFBYWEsT0FBT0MsaUJBQWlCO1lBR3ZFVixTQUFTRSxLQUFLLENBQUNDLE1BQU0sR0FBRyxHQUFhLE9BQVZDLFdBQVU7UUFDdkM7MERBQ0E7UUFBQ1Q7UUFBV0M7S0FBVTtJQUd4QkwsZ0RBQVNBOzJDQUFDO1lBQ1IsTUFBTVMsV0FBV0gsWUFBWUksT0FBTztZQUNwQyxJQUFJRCxVQUFVO2dCQUNaQSxTQUFTRSxLQUFLLENBQUNDLE1BQU0sR0FBRyxHQUFhLE9BQVZSLFdBQVU7WUFDdkM7UUFDRjswQ0FBRztRQUFDQTtLQUFVO0lBRWRKLGdEQUFTQTsyQ0FBQztZQUNSLE1BQU1vQjtnRUFBZSxJQUFNYjs7WUFDM0JjLE9BQU9DLGdCQUFnQixDQUFDLFVBQVVGO1lBQ2xDO21EQUFPLElBQU1DLE9BQU9FLG1CQUFtQixDQUFDLFVBQVVIOztRQUNwRDswQ0FBRztRQUFDYjtLQUFhO0lBRWpCLE9BQU87UUFBRUQ7UUFBYUM7SUFBYTtBQUNyQztHQTFDU0o7QUE0Q1QsTUFBTXFCLDRCQUNKOztzQkFDRSw4REFBQ0M7WUFDQ0MsT0FBTTtZQUNOQyxPQUFNO1lBQ05mLFFBQU87WUFDUGdCLFNBQVE7WUFDUkMsY0FBVztZQUNYQyxXQUFVOzs4QkFFViw4REFBQ0M7OEJBQU07Ozs7Ozs4QkFDUCw4REFBQ0M7b0JBQUtDLEdBQUU7Ozs7Ozs7Ozs7OztzQkFFViw4REFBQ1I7WUFDQ0MsT0FBTTtZQUNOQyxPQUFNO1lBQ05mLFFBQU87WUFDUGdCLFNBQVE7WUFDUkMsY0FBVztZQUNYQyxXQUFVOzs4QkFFViw4REFBQ0M7OEJBQU07Ozs7Ozs4QkFDUCw4REFBQ0M7b0JBQ0NFLE1BQUs7b0JBQ0xELEdBQUU7Ozs7Ozs7Ozs7Ozs7O0FBTUgsU0FBU0U7O0lBQ2QsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUduQywrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLEVBQUVJLFdBQVcsRUFBRUMsWUFBWSxFQUFFLEdBQUdKLHNCQUFzQjtRQUMxREMsV0FBVztRQUNYQyxXQUFXO0lBQ2I7SUFDQSxNQUFNLENBQUNpQyxlQUFlQyxpQkFBaUIsR0FBR3JDLCtDQUFRQSxDQUFDO0lBRW5ELE1BQU1zQyxZQUFZO1FBQ2hCO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVELE1BQU1DLGNBQStDO1FBQ25ELFdBQVdqQjtRQUNYLGtDQUNFLDhEQUFDQztZQUNDYixRQUFPO1lBQ1BrQixXQUFVO1lBQ1ZGLFNBQVE7WUFDUkYsT0FBTTs7OEJBRU4sOERBQUNLOzhCQUFNOzs7Ozs7OEJBQ1AsOERBQUNXOzhCQUNDLDRFQUFDQzt3QkFDQ0MsSUFBRzt3QkFDSEMsSUFBRzt3QkFDSEMsSUFBRzt3QkFDSEMsSUFBRzt3QkFDSEMsSUFBRzs7MENBRUgsOERBQUNDO2dDQUFLQyxRQUFPO2dDQUFLQyxXQUFVOzs7Ozs7MENBQzVCLDhEQUFDRjtnQ0FBS0MsUUFBTztnQ0FBVUMsV0FBVTs7Ozs7OzBDQUNqQyw4REFBQ0Y7Z0NBQUtDLFFBQU87Z0NBQU9DLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUdsQyw4REFBQ25CO29CQUNDQyxHQUFFO29CQUNGQyxNQUFLO29CQUNMa0IsVUFBUzs7Ozs7Ozs7Ozs7O1FBSWYsbUNBQ0U7OzhCQUNFLDhEQUFDM0I7b0JBQ0NTLE1BQUs7b0JBQ0xrQixVQUFTO29CQUNUdEIsV0FBVTtvQkFDVkYsU0FBUTtvQkFDUkQsT0FBTTtvQkFDTkQsT0FBTTs7c0NBRU4sOERBQUNLO3NDQUFNOzs7Ozs7c0NBQ1AsOERBQUNDOzRCQUFLQyxHQUFFOzs7Ozs7Ozs7Ozs7OEJBRVYsOERBQUNSO29CQUNDUyxNQUFLO29CQUNMa0IsVUFBUztvQkFDVHRCLFdBQVU7b0JBQ1ZGLFNBQVE7b0JBQ1JELE9BQU07b0JBQ05ELE9BQU07O3NDQUVOLDhEQUFDSztzQ0FBTTs7Ozs7O3NDQUNQLDhEQUFDQzs0QkFBS0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7UUFJZCxnQkFBZ0JUO1FBQ2hCLFdBQVdBO0lBQ2I7SUFFQSxNQUFNNkIsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsSUFBSXBCLE1BQU1xQixJQUFJLElBQUk7WUFDcERILEVBQUVJLGNBQWM7WUFDaEJyQixTQUFTO1lBQ1Q5QixhQUFhO1FBQ2IsbUNBQW1DO1FBQ3JDO0lBQ0Y7SUFFQSxNQUFNb0QsYUFBYTtRQUNqQixJQUFJLENBQUN2QixNQUFNcUIsSUFBSSxJQUFJO1FBQ25CcEIsU0FBUztRQUNUOUIsYUFBYTtJQUNiLG1DQUFtQztJQUNyQztJQUVBLHFCQUNFLDhEQUFDcUQ7UUFBSTlCLFdBQVU7a0JBQ2IsNEVBQUM4QjtZQUFJOUIsV0FBVTtzQkFDYiw0RUFBQzhCO2dCQUFJOUIsV0FBVTswQkFDYiw0RUFBQzhCO29CQUFJOUIsV0FBVTs4QkFDYiw0RUFBQzhCO3dCQUNDOUIsV0FBVTt3QkFDVm5CLE9BQU87NEJBQUVOLFdBQVc7d0JBQVE7OzBDQUU1Qiw4REFBQ1QsNkRBQVFBO2dDQUNQZ0QsSUFBRztnQ0FDSFIsT0FBT0E7Z0NBQ1B5QixhQUFhO2dDQUNiL0IsV0FBV2pDLDhDQUFFQSxDQUNYLDZSQUNBLGlCQUNBLG1HQUNBdUMsU0FDRTtnQ0FFSjBCLEtBQUt4RDtnQ0FDTHlELFdBQVdWO2dDQUNYVyxVQUFVLENBQUNWO29DQUNUakIsU0FBU2lCLEVBQUVXLE1BQU0sQ0FBQzdCLEtBQUs7b0NBQ3ZCN0I7Z0NBQ0Y7Ozs7OzswQ0FJRiw4REFBQ3FEO2dDQUFJOUIsV0FBVTswQ0FDYiw0RUFBQ25DLHdFQUFhQTtvQ0FDWnVFLE1BQUs7b0NBQ0xDLE1BQU1yRSxzRkFBVUE7b0NBQ2hCc0UsUUFBTztvQ0FDUEMsTUFBSztvQ0FDTEMsU0FBU2xDLE1BQU1xQixJQUFJLEtBQUssY0FBYztvQ0FDdENjLGVBQWM7b0NBQ2RDLFVBQVUsQ0FBQ3BDLE1BQU1xQixJQUFJO29DQUNyQjVCLGNBQVc7b0NBQ1g0QyxTQUFTZDtvQ0FDVDdCLFdBQVdqQyw4Q0FBRUEsQ0FDWCwrQkFDQXVDLE1BQU1xQixJQUFJLEtBQ04sa0ZBQ0E7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVeEI7SUFsSmdCdEI7O1FBRXdCaEM7OztLQUZ4QmdDIiwic291cmNlcyI6WyIvVXNlcnMvYXJlZi9EYXRhL25ldyBlcmEvc2lpZnQtbmV4dC9zcmMvY29tcG9uZW50cy91aS9hbmltYXRlZC1haS1pbnB1dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IFNpZGViYXJCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NpZGViYXItYnV0dG9uXCI7XG5pbXBvcnQgeyBUZXh0YXJlYSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvdGV4dGFyZWFcIjtcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5pbXBvcnQgeyBBcnJvd1JpZ2h0IH0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2ssIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuXG5pbnRlcmZhY2UgVXNlQXV0b1Jlc2l6ZVRleHRhcmVhUHJvcHMge1xuICBtaW5IZWlnaHQ6IG51bWJlcjtcbiAgbWF4SGVpZ2h0PzogbnVtYmVyO1xufVxuXG5mdW5jdGlvbiB1c2VBdXRvUmVzaXplVGV4dGFyZWEoe1xuICBtaW5IZWlnaHQsXG4gIG1heEhlaWdodCxcbn06IFVzZUF1dG9SZXNpemVUZXh0YXJlYVByb3BzKSB7XG4gIGNvbnN0IHRleHRhcmVhUmVmID0gdXNlUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQ+KG51bGwpO1xuXG4gIGNvbnN0IGFkanVzdEhlaWdodCA9IHVzZUNhbGxiYWNrKFxuICAgIChyZXNldD86IGJvb2xlYW4pID0+IHtcbiAgICAgIGNvbnN0IHRleHRhcmVhID0gdGV4dGFyZWFSZWYuY3VycmVudDtcbiAgICAgIGlmICghdGV4dGFyZWEpIHJldHVybjtcblxuICAgICAgaWYgKHJlc2V0KSB7XG4gICAgICAgIHRleHRhcmVhLnN0eWxlLmhlaWdodCA9IGAke21pbkhlaWdodH1weGA7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgdGV4dGFyZWEuc3R5bGUuaGVpZ2h0ID0gYCR7bWluSGVpZ2h0fXB4YDtcblxuICAgICAgY29uc3QgbmV3SGVpZ2h0ID0gTWF0aC5tYXgoXG4gICAgICAgIG1pbkhlaWdodCxcbiAgICAgICAgTWF0aC5taW4odGV4dGFyZWEuc2Nyb2xsSGVpZ2h0LCBtYXhIZWlnaHQgPz8gTnVtYmVyLlBPU0lUSVZFX0lORklOSVRZKVxuICAgICAgKTtcblxuICAgICAgdGV4dGFyZWEuc3R5bGUuaGVpZ2h0ID0gYCR7bmV3SGVpZ2h0fXB4YDtcbiAgICB9LFxuICAgIFttaW5IZWlnaHQsIG1heEhlaWdodF1cbiAgKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHRleHRhcmVhID0gdGV4dGFyZWFSZWYuY3VycmVudDtcbiAgICBpZiAodGV4dGFyZWEpIHtcbiAgICAgIHRleHRhcmVhLnN0eWxlLmhlaWdodCA9IGAke21pbkhlaWdodH1weGA7XG4gICAgfVxuICB9LCBbbWluSGVpZ2h0XSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBoYW5kbGVSZXNpemUgPSAoKSA9PiBhZGp1c3RIZWlnaHQoKTtcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBoYW5kbGVSZXNpemUpO1xuICAgIHJldHVybiAoKSA9PiB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcInJlc2l6ZVwiLCBoYW5kbGVSZXNpemUpO1xuICB9LCBbYWRqdXN0SGVpZ2h0XSk7XG5cbiAgcmV0dXJuIHsgdGV4dGFyZWFSZWYsIGFkanVzdEhlaWdodCB9O1xufVxuXG5jb25zdCBPUEVOQUlfSUNPTiA9IChcbiAgPD5cbiAgICA8c3ZnXG4gICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgIHdpZHRoPVwiMjBcIlxuICAgICAgaGVpZ2h0PVwiMjBcIlxuICAgICAgdmlld0JveD1cIjAgMCAyNTYgMjYwXCJcbiAgICAgIGFyaWEtbGFiZWw9XCJPcGVuQUkgSWNvblwiXG4gICAgICBjbGFzc05hbWU9XCJ3LTQgaC00IGRhcms6aGlkZGVuIGJsb2NrXCJcbiAgICA+XG4gICAgICA8dGl0bGU+T3BlbkFJIEljb24gTGlnaHQ8L3RpdGxlPlxuICAgICAgPHBhdGggZD1cIk0yMzkuMTg0IDEwNi4yMDNhNjQuNzE2IDY0LjcxNiAwIDAgMC01LjU3Ni01My4xMDNDMjE5LjQ1MiAyOC40NTkgMTkxIDE1Ljc4NCAxNjMuMjEzIDIxLjc0QTY1LjU4NiA2NS41ODYgMCAwIDAgNTIuMDk2IDQ1LjIyYTY0LjcxNiA2NC43MTYgMCAwIDAtNDMuMjMgMzEuMzZjLTE0LjMxIDI0LjYwMi0xMS4wNjEgNTUuNjM0IDguMDMzIDc2Ljc0YTY0LjY2NSA2NC42NjUgMCAwIDAgNS41MjUgNTMuMTAyYzE0LjE3NCAyNC42NSA0Mi42NDQgMzcuMzI0IDcwLjQ0NiAzMS4zNmE2NC43MiA2NC43MiAwIDAgMCA0OC43NTQgMjEuNzQ0YzI4LjQ4MS4wMjUgNTMuNzE0LTE4LjM2MSA2Mi40MTQtNDUuNDgxYTY0Ljc2NyA2NC43NjcgMCAwIDAgNDMuMjI5LTMxLjM2YzE0LjEzNy0yNC41NTggMTAuODc1LTU1LjQyMy04LjA4My03Ni40ODNabS05Ny41NiAxMzYuMzM4YTQ4LjM5NyA0OC4zOTcgMCAwIDEtMzEuMTA1LTExLjI1NWwxLjUzNS0uODcgNTEuNjctMjkuODI1YTguNTk1IDguNTk1IDAgMCAwIDQuMjQ3LTcuMzY3di03Mi44NWwyMS44NDUgMTIuNjM2Yy4yMTguMTExLjM3LjMyLjQwOS41NjN2NjAuMzY3Yy0uMDU2IDI2LjgxOC0yMS43ODMgNDguNTQ1LTQ4LjYwMSA0OC42MDFabS0xMDQuNDY2LTQ0LjYxYTQ4LjM0NSA0OC4zNDUgMCAwIDEtNS43ODEtMzIuNTg5bDEuNTM0LjkyMSA1MS43MjIgMjkuODI2YTguMzM5IDguMzM5IDAgMCAwIDguNDQxIDBsNjMuMTgxLTM2LjQyNXYyNS4yMjFhLjg3Ljg3IDAgMCAxLS4zNTguNjY1bC01Mi4zMzUgMzAuMTg0Yy0yMy4yNTcgMTMuMzk4LTUyLjk3IDUuNDMxLTY2LjQwNC0xNy44MDNaTTIzLjU0OSA4NS4zOGE0OC40OTkgNDguNDk5IDAgMCAxIDI1LjU4LTIxLjMzM3Y2MS4zOWE4LjI4OCA4LjI4OCAwIDAgMCA0LjE5NSA3LjMxNmw2Mi44NzQgMzYuMjcyLTIxLjg0NSAxMi42MzZhLjgxOS44MTkgMCAwIDEtLjc2NyAwTDQxLjM1MyAxNTEuNTNjLTIzLjIxMS0xMy40NTQtMzEuMTcxLTQzLjE0NC0xNy44MDQtNjYuNDA1di4yNTZabTE3OS40NjYgNDEuNjk1LTYzLjA4LTM2LjYzTDE2MS43MyA3Ny44NmEuODE5LjgxOSAwIDAgMSAuNzY4IDBsNTIuMjMzIDMwLjE4NGE0OC42IDQ4LjYgMCAwIDEtNy4zMTYgODcuNjM1di02MS4zOTFhOC41NDQgOC41NDQgMCAwIDAtNC40LTcuMjEzWm0yMS43NDItMzIuNjktMS41MzUtLjkyMi01MS42MTktMzAuMDgxYTguMzkgOC4zOSAwIDAgMC04LjQ5MiAwTDk5Ljk4IDk5LjgwOFY3NC41ODdhLjcxNi43MTYgMCAwIDEgLjMwNy0uNjY1bDUyLjIzMy0zMC4xMzNhNDguNjUyIDQ4LjY1MiAwIDAgMSA3Mi4yMzYgNTAuMzkxdi4yMDVaTTg4LjA2MSAxMzkuMDk3bC0yMS44NDUtMTIuNTg1YS44Ny44NyAwIDAgMS0uNDEtLjYxNFY2NS42ODVhNDguNjUyIDQ4LjY1MiAwIDAgMSA3OS43NTctMzcuMzQ2bC0xLjUzNS44Ny01MS42NyAyOS44MjVhOC41OTUgOC41OTUgMCAwIDAtNC4yNDYgNy4zNjdsLS4wNTEgNzIuNjk3Wm0xMS44NjgtMjUuNTggMjguMTM4LTE2LjIxNyAyOC4xODggMTYuMjE4djMyLjQzNGwtMjguMDg2IDE2LjIxOC0yOC4xODgtMTYuMjE4LS4wNTItMzIuNDM0WlwiIC8+XG4gICAgPC9zdmc+XG4gICAgPHN2Z1xuICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICB3aWR0aD1cIjIwXCJcbiAgICAgIGhlaWdodD1cIjIwXCJcbiAgICAgIHZpZXdCb3g9XCIwIDAgMjU2IDI2MFwiXG4gICAgICBhcmlhLWxhYmVsPVwiT3BlbkFJIEljb25cIlxuICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCBoaWRkZW4gZGFyazpibG9ja1wiXG4gICAgPlxuICAgICAgPHRpdGxlPk9wZW5BSSBJY29uIERhcms8L3RpdGxlPlxuICAgICAgPHBhdGhcbiAgICAgICAgZmlsbD1cIiNmZmZcIlxuICAgICAgICBkPVwiTTIzOS4xODQgMTA2LjIwM2E2NC43MTYgNjQuNzE2IDAgMCAwLTUuNTc2LTUzLjEwM0MyMTkuNDUyIDI4LjQ1OSAxOTEgMTUuNzg0IDE2My4yMTMgMjEuNzRBNjUuNTg2IDY1LjU4NiAwIDAgMCA1Mi4wOTYgNDUuMjJhNjQuNzE2IDY0LjcxNiAwIDAgMC00My4yMyAzMS4zNmMtMTQuMzEgMjQuNjAyLTExLjA2MSA1NS42MzQgOC4wMzMgNzYuNzRhNjQuNjY1IDY0LjY2NSAwIDAgMCA1LjUyNSA1My4xMDJjMTQuMTc0IDI0LjY1IDQyLjY0NCAzNy4zMjQgNzAuNDQ2IDMxLjM2YTY0LjcyIDY0LjcyIDAgMCAwIDQ4Ljc1NCAyMS43NDRjMjguNDgxLjAyNSA1My43MTQtMTguMzYxIDYyLjQxNC00NS40ODFhNjQuNzY3IDY0Ljc2NyAwIDAgMCA0My4yMjktMzEuMzZjMTQuMTM3LTI0LjU1OCAxMC44NzUtNTUuNDIzLTguMDgzLTc2LjQ4M1ptLTk3LjU2IDEzNi4zMzhhNDguMzk3IDQ4LjM5NyAwIDAgMS0zMS4xMDUtMTEuMjU1bDEuNTM1LS44NyA1MS42Ny0yOS44MjVhOC41OTUgOC41OTUgMCAwIDAgNC4yNDctNy4zNjd2LTcyLjg1bDIxLjg0NSAxMi42MzZjLjIxOC4xMTEuMzcuMzIuNDA5LjU2M3Y2MC4zNjdjLS4wNTYgMjYuODE4LTIxLjc4MyA0OC41NDUtNDguNjAxIDQ4LjYwMVptLTEwNC40NjYtNDQuNjFhNDguMzQ1IDQ4LjM0NSAwIDAgMS01Ljc4MS0zMi41ODlsMS41MzQuOTIxIDUxLjcyMiAyOS44MjZhOC4zMzkgOC4zMzkgMCAwIDAgOC40NDEgMGw2My4xODEtMzYuNDI1djI1LjIyMWEuODcuODcgMCAwIDEtLjM1OC42NjVsLTUyLjMzNSAzMC4xODRjLTIzLjI1NyAxMy4zOTgtNTIuOTcgNS40MzEtNjYuNDA0LTE3LjgwM1pNMjMuNTQ5IDg1LjM4YTQ4LjQ5OSA0OC40OTkgMCAwIDEgMjUuNTgtMjEuMzMzdjYxLjM5YTguMjg4IDguMjg4IDAgMCAwIDQuMTk1IDcuMzE2bDYyLjg3NCAzNi4yNzItMjEuODQ1IDEyLjYzNmEuODE5LjgxOSAwIDAgMS0uNzY3IDBMNDEuMzUzIDE1MS41M2MtMjMuMjExLTEzLjQ1NC0zMS4xNzEtNDMuMTQ0LTE3LjgwNC02Ni40MDV2LjI1NlptMTc5LjQ2NiA0MS42OTUtNjMuMDgtMzYuNjNMMTYxLjczIDc3Ljg2YS44MTkuODE5IDAgMCAxIC43NjggMGw1Mi4yMzMgMzAuMTg0YTQ4LjYgNDguNiAwIDAgMS03LjMxNiA4Ny42MzV2LTYxLjM5MWE4LjU0NCA4LjU0NCAwIDAgMC00LjQtNy4yMTNabTIxLjc0Mi0zMi42OS0xLjUzNS0uOTIyLTUxLjYxOS0zMC4wODFhOC4zOSA4LjM5IDAgMCAwLTguNDkyIDBMOTkuOTggOTkuODA4Vjc0LjU4N2EuNzE2LjcxNiAwIDAgMSAuMzA3LS42NjVsNTIuMjMzLTMwLjEzM2E0OC42NTIgNDguNjUyIDAgMCAxIDcyLjIzNiA1MC4zOTF2LjIwNVpNODguMDYxIDEzOS4wOTdsLTIxLjg0NS0xMi41ODVhLjg3Ljg3IDAgMCAxLS40MS0uNjE0VjY1LjY4NWE0OC42NTIgNDguNjUyIDAgMCAxIDc5Ljc1Ny0zNy4zNDZsLTEuNTM1Ljg3LTUxLjY3IDI5LjgyNWE4LjU5NSA4LjU5NSAwIDAgMC00LjI0NiA3LjM2N2wtLjA1MSA3Mi42OTdabTExLjg2OC0yNS41OCAyOC4xMzgtMTYuMjE3IDI4LjE4OCAxNi4yMTh2MzIuNDM0bC0yOC4wODYgMTYuMjE4LTI4LjE4OC0xNi4yMTgtLjA1Mi0zMi40MzRaXCJcbiAgICAgIC8+XG4gICAgPC9zdmc+XG4gIDwvPlxuKTtcblxuZXhwb3J0IGZ1bmN0aW9uIEFJX1Byb21wdCgpIHtcbiAgY29uc3QgW3ZhbHVlLCBzZXRWYWx1ZV0gPSB1c2VTdGF0ZShcIlwiKTtcbiAgY29uc3QgeyB0ZXh0YXJlYVJlZiwgYWRqdXN0SGVpZ2h0IH0gPSB1c2VBdXRvUmVzaXplVGV4dGFyZWEoe1xuICAgIG1pbkhlaWdodDogNjAsXG4gICAgbWF4SGVpZ2h0OiAyMDAsXG4gIH0pO1xuICBjb25zdCBbc2VsZWN0ZWRNb2RlbCwgc2V0U2VsZWN0ZWRNb2RlbF0gPSB1c2VTdGF0ZShcIkdQVC00LTEgTWluaVwiKTtcblxuICBjb25zdCBBSV9NT0RFTFMgPSBbXG4gICAgXCJvMy1taW5pXCIsXG4gICAgXCJHZW1pbmkgMi41IEZsYXNoXCIsXG4gICAgXCJDbGF1ZGUgMy41IFNvbm5ldFwiLFxuICAgIFwiR1BULTQtMSBNaW5pXCIsXG4gICAgXCJHUFQtNC0xXCIsXG4gIF07XG5cbiAgY29uc3QgTU9ERUxfSUNPTlM6IFJlY29yZDxzdHJpbmcsIFJlYWN0LlJlYWN0Tm9kZT4gPSB7XG4gICAgXCJvMy1taW5pXCI6IE9QRU5BSV9JQ09OLFxuICAgIFwiR2VtaW5pIDIuNSBGbGFzaFwiOiAoXG4gICAgICA8c3ZnXG4gICAgICAgIGhlaWdodD1cIjFlbVwiXG4gICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTRcIlxuICAgICAgICB2aWV3Qm94PVwiMCAwIDI0IDI0XCJcbiAgICAgICAgeG1sbnM9XCJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Z1wiXG4gICAgICA+XG4gICAgICAgIDx0aXRsZT5HZW1pbmk8L3RpdGxlPlxuICAgICAgICA8ZGVmcz5cbiAgICAgICAgICA8bGluZWFyR3JhZGllbnRcbiAgICAgICAgICAgIGlkPVwibG9iZS1pY29ucy1nZW1pbmktZmlsbFwiXG4gICAgICAgICAgICB4MT1cIjAlXCJcbiAgICAgICAgICAgIHgyPVwiNjguNzMlXCJcbiAgICAgICAgICAgIHkxPVwiMTAwJVwiXG4gICAgICAgICAgICB5Mj1cIjMwLjM5NSVcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxzdG9wIG9mZnNldD1cIjAlXCIgc3RvcENvbG9yPVwiIzFDN0RGRlwiIC8+XG4gICAgICAgICAgICA8c3RvcCBvZmZzZXQ9XCI1Mi4wMjElXCIgc3RvcENvbG9yPVwiIzFDNjlGRlwiIC8+XG4gICAgICAgICAgICA8c3RvcCBvZmZzZXQ9XCIxMDAlXCIgc3RvcENvbG9yPVwiI0YwRENENlwiIC8+XG4gICAgICAgICAgPC9saW5lYXJHcmFkaWVudD5cbiAgICAgICAgPC9kZWZzPlxuICAgICAgICA8cGF0aFxuICAgICAgICAgIGQ9XCJNMTIgMjRBMTQuMzA0IDE0LjMwNCAwIDAwMCAxMiAxNC4zMDQgMTQuMzA0IDAgMDAxMiAwYTE0LjMwNSAxNC4zMDUgMCAwMDEyIDEyIDE0LjMwNSAxNC4zMDUgMCAwMC0xMiAxMlwiXG4gICAgICAgICAgZmlsbD1cInVybCgjbG9iZS1pY29ucy1nZW1pbmktZmlsbClcIlxuICAgICAgICAgIGZpbGxSdWxlPVwibm9uemVyb1wiXG4gICAgICAgIC8+XG4gICAgICA8L3N2Zz5cbiAgICApLFxuICAgIFwiQ2xhdWRlIDMuNSBTb25uZXRcIjogKFxuICAgICAgPD5cbiAgICAgICAgPHN2Z1xuICAgICAgICAgIGZpbGw9XCIjMDAwXCJcbiAgICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgZGFyazpoaWRkZW4gYmxvY2tcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgIHdpZHRoPVwiMWVtXCJcbiAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgPlxuICAgICAgICAgIDx0aXRsZT5BbnRocm9waWMgSWNvbiBMaWdodDwvdGl0bGU+XG4gICAgICAgICAgPHBhdGggZD1cIk0xMy44MjcgMy41MmgzLjYwM0wyNCAyMGgtMy42MDNsLTYuNTctMTYuNDh6bS03LjI1OCAwaDMuNzY3TDE2LjkwNiAyMGgtMy42NzRsLTEuMzQzLTMuNDYxSDUuMDE3bC0xLjM0NCAzLjQ2SDBMNi41NyAzLjUyMnptNC4xMzIgOS45NTlMOC40NTMgNy42ODcgNi4yMDUgMTMuNDhIMTAuN3pcIiAvPlxuICAgICAgICA8L3N2Zz5cbiAgICAgICAgPHN2Z1xuICAgICAgICAgIGZpbGw9XCIjZmZmXCJcbiAgICAgICAgICBmaWxsUnVsZT1cImV2ZW5vZGRcIlxuICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgaGlkZGVuIGRhcms6YmxvY2tcIlxuICAgICAgICAgIHZpZXdCb3g9XCIwIDAgMjQgMjRcIlxuICAgICAgICAgIHdpZHRoPVwiMWVtXCJcbiAgICAgICAgICB4bWxucz1cImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCJcbiAgICAgICAgPlxuICAgICAgICAgIDx0aXRsZT5BbnRocm9waWMgSWNvbiBEYXJrPC90aXRsZT5cbiAgICAgICAgICA8cGF0aCBkPVwiTTEzLjgyNyAzLjUyaDMuNjAzTDI0IDIwaC0zLjYwM2wtNi41Ny0xNi40OHptLTcuMjU4IDBoMy43NjdMMTYuOTA2IDIwaC0zLjY3NGwtMS4zNDMtMy40NjFINS4wMTdsLTEuMzQ0IDMuNDZIMEw2LjU3IDMuNTIyem00LjEzMiA5Ljk1OUw4LjQ1MyA3LjY4NyA2LjIwNSAxMy40OEgxMC43elwiIC8+XG4gICAgICAgIDwvc3ZnPlxuICAgICAgPC8+XG4gICAgKSxcbiAgICBcIkdQVC00LTEgTWluaVwiOiBPUEVOQUlfSUNPTixcbiAgICBcIkdQVC00LTFcIjogT1BFTkFJX0lDT04sXG4gIH07XG5cbiAgY29uc3QgaGFuZGxlS2V5RG93biA9IChlOiBSZWFjdC5LZXlib2FyZEV2ZW50PEhUTUxUZXh0QXJlYUVsZW1lbnQ+KSA9PiB7XG4gICAgaWYgKGUua2V5ID09PSBcIkVudGVyXCIgJiYgIWUuc2hpZnRLZXkgJiYgdmFsdWUudHJpbSgpKSB7XG4gICAgICBlLnByZXZlbnREZWZhdWx0KCk7XG4gICAgICBzZXRWYWx1ZShcIlwiKTtcbiAgICAgIGFkanVzdEhlaWdodCh0cnVlKTtcbiAgICAgIC8vIEhlcmUgeW91IGNhbiBhZGQgbWVzc2FnZSBzZW5kaW5nXG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVNlbmQgPSAoKSA9PiB7XG4gICAgaWYgKCF2YWx1ZS50cmltKCkpIHJldHVybjtcbiAgICBzZXRWYWx1ZShcIlwiKTtcbiAgICBhZGp1c3RIZWlnaHQodHJ1ZSk7XG4gICAgLy8gSGVyZSB5b3UgY2FuIGFkZCBtZXNzYWdlIHNlbmRpbmdcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWJsYWNrLzUgZGFyazpiZy13aGl0ZS81IHJvdW5kZWQtMnhsIHAtMS41XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIGZsZXggZmxleC1jb2xcIj5cbiAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwib3ZlcmZsb3cteS1hdXRvIHJlbGF0aXZlXCJcbiAgICAgICAgICAgICAgc3R5bGU9e3sgbWF4SGVpZ2h0OiBcIjQwMHB4XCIgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgICAgaWQ9XCJhaS1pbnB1dC0xNVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e3ZhbHVlfVxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcIldoYXQgY2FuIEkgZG8gZm9yIHlvdT9cIn1cbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgICAgICAgICAgXCJ3LWZ1bGwgYmctd2hpdGUtMTAwLzYwIGRhcms6YmctZ3JheS03MDAvNjAgcm91bmRlZC14bCBweC0zIHB5LTIgcHItMTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMC81MCBkYXJrOmJvcmRlci1ncmF5LTYwMC81MCBkYXJrOnRleHQtd2hpdGUgcGxhY2Vob2xkZXI6dGV4dC1ncmF5LTUwMCBkYXJrOnBsYWNlaG9sZGVyOnRleHQtZ3JheS00MDAgcmVzaXplLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTAgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0wIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMFwiLFxuICAgICAgICAgICAgICAgICAgXCJtaW4taC1bMTIwcHhdXCIsXG4gICAgICAgICAgICAgICAgICBcImhvdmVyOmJnLWdyYXktMjAwLzgwIGRhcms6aG92ZXI6YmctZ3JheS03MDAvODAgaG92ZXI6Ym9yZGVyLWdyYXktNDAwIGRhcms6aG92ZXI6Ym9yZGVyLWdyYXktNTAwXCIsXG4gICAgICAgICAgICAgICAgICB2YWx1ZSAmJlxuICAgICAgICAgICAgICAgICAgICBcImJnLWdyYXktMjAwLzkwIGRhcms6YmctZ3JheS03MDAvOTAgYm9yZGVyLWdyYXktNDAwIGRhcms6Ym9yZGVyLWdyYXktNTAwXCJcbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgIHJlZj17dGV4dGFyZWFSZWZ9XG4gICAgICAgICAgICAgICAgb25LZXlEb3duPXtoYW5kbGVLZXlEb3dufVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgc2V0VmFsdWUoZS50YXJnZXQudmFsdWUpO1xuICAgICAgICAgICAgICAgICAgYWRqdXN0SGVpZ2h0KCk7XG4gICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgLz5cblxuICAgICAgICAgICAgICB7LyogU2VuZCBidXR0b24gcG9zaXRpb25lZCBhdCByaWdodCBib3R0b20gb2YgaW5wdXQgKi99XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTIgcmlnaHQtMlwiPlxuICAgICAgICAgICAgICAgIDxTaWRlYmFyQnV0dG9uXG4gICAgICAgICAgICAgICAgICB0eXBlPVwiYnV0dG9uXCJcbiAgICAgICAgICAgICAgICAgIGljb249e0Fycm93UmlnaHR9XG4gICAgICAgICAgICAgICAgICBsYXlvdXQ9XCJpY29uLW9ubHlcIlxuICAgICAgICAgICAgICAgICAgc2l6ZT1cIm1kXCJcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e3ZhbHVlLnRyaW0oKSA/IFwic2Vjb25kYXJ5XCIgOiBcImdob3N0XCJ9XG4gICAgICAgICAgICAgICAgICBpY29uQ2xhc3NOYW1lPVwidGV4dC13aGl0ZVwiXG4gICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXZhbHVlLnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJTZW5kIG1lc3NhZ2VcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlU2VuZH1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgICAgICAgICAgICAgIFwidHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwXCIsXG4gICAgICAgICAgICAgICAgICAgIHZhbHVlLnRyaW0oKVxuICAgICAgICAgICAgICAgICAgICAgID8gXCJiZy1hY2NlbnQgaG92ZXI6YmctYWNjZW50LzkwIHRleHQtYWNjZW50LWZvcmVncm91bmQgc2hhZG93LWxnIGhvdmVyOnNoYWRvdy14bFwiXG4gICAgICAgICAgICAgICAgICAgICAgOiBcIm9wYWNpdHktNDAgYmctZ3JheS0zMDAvNTAgZGFyazpiZy1ncmF5LTYwMC81MCBob3ZlcjpvcGFjaXR5LTYwXCJcbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJTaWRlYmFyQnV0dG9uIiwiVGV4dGFyZWEiLCJjbiIsIkFycm93UmlnaHQiLCJ1c2VDYWxsYmFjayIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwidXNlQXV0b1Jlc2l6ZVRleHRhcmVhIiwibWluSGVpZ2h0IiwibWF4SGVpZ2h0IiwidGV4dGFyZWFSZWYiLCJhZGp1c3RIZWlnaHQiLCJyZXNldCIsInRleHRhcmVhIiwiY3VycmVudCIsInN0eWxlIiwiaGVpZ2h0IiwibmV3SGVpZ2h0IiwiTWF0aCIsIm1heCIsIm1pbiIsInNjcm9sbEhlaWdodCIsIk51bWJlciIsIlBPU0lUSVZFX0lORklOSVRZIiwiaGFuZGxlUmVzaXplIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJPUEVOQUlfSUNPTiIsInN2ZyIsInhtbG5zIiwid2lkdGgiLCJ2aWV3Qm94IiwiYXJpYS1sYWJlbCIsImNsYXNzTmFtZSIsInRpdGxlIiwicGF0aCIsImQiLCJmaWxsIiwiQUlfUHJvbXB0IiwidmFsdWUiLCJzZXRWYWx1ZSIsInNlbGVjdGVkTW9kZWwiLCJzZXRTZWxlY3RlZE1vZGVsIiwiQUlfTU9ERUxTIiwiTU9ERUxfSUNPTlMiLCJkZWZzIiwibGluZWFyR3JhZGllbnQiLCJpZCIsIngxIiwieDIiLCJ5MSIsInkyIiwic3RvcCIsIm9mZnNldCIsInN0b3BDb2xvciIsImZpbGxSdWxlIiwiaGFuZGxlS2V5RG93biIsImUiLCJrZXkiLCJzaGlmdEtleSIsInRyaW0iLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZVNlbmQiLCJkaXYiLCJwbGFjZWhvbGRlciIsInJlZiIsIm9uS2V5RG93biIsIm9uQ2hhbmdlIiwidGFyZ2V0IiwidHlwZSIsImljb24iLCJsYXlvdXQiLCJzaXplIiwidmFyaWFudCIsImljb25DbGFzc05hbWUiLCJkaXNhYmxlZCIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/animated-ai-input.tsx\n"));

/***/ })

});