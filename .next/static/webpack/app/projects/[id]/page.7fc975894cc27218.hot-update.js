"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ui/animated-ai-input.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/animated-ai-input.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AI_Prompt: () => (/* binding */ AI_Prompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/sidebar-button */ \"(app-pages-browser)/./src/components/ui/sidebar-button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ AI_Prompt auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction useAutoResizeTextarea(param) {\n    let { minHeight, maxHeight } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const adjustHeight = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"useAutoResizeTextarea.useCallback[adjustHeight]\": (reset)=>{\n            const textarea = textareaRef.current;\n            if (!textarea) return;\n            if (reset) {\n                textarea.style.height = \"\".concat(minHeight, \"px\");\n                return;\n            }\n            textarea.style.height = \"\".concat(minHeight, \"px\");\n            const newHeight = Math.max(minHeight, Math.min(textarea.scrollHeight, maxHeight !== null && maxHeight !== void 0 ? maxHeight : Number.POSITIVE_INFINITY));\n            textarea.style.height = \"\".concat(newHeight, \"px\");\n        }\n    }[\"useAutoResizeTextarea.useCallback[adjustHeight]\"], [\n        minHeight,\n        maxHeight\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"useAutoResizeTextarea.useEffect\": ()=>{\n            const textarea = textareaRef.current;\n            if (textarea) {\n                textarea.style.height = \"\".concat(minHeight, \"px\");\n            }\n        }\n    }[\"useAutoResizeTextarea.useEffect\"], [\n        minHeight\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"useAutoResizeTextarea.useEffect\": ()=>{\n            const handleResize = {\n                \"useAutoResizeTextarea.useEffect.handleResize\": ()=>adjustHeight()\n            }[\"useAutoResizeTextarea.useEffect.handleResize\"];\n            window.addEventListener(\"resize\", handleResize);\n            return ({\n                \"useAutoResizeTextarea.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n            })[\"useAutoResizeTextarea.useEffect\"];\n        }\n    }[\"useAutoResizeTextarea.useEffect\"], [\n        adjustHeight\n    ]);\n    return {\n        textareaRef,\n        adjustHeight\n    };\n}\n_s(useAutoResizeTextarea, \"dY5gUJDyLTt7nR3p8fNJ7y+Lo90=\");\nconst OPENAI_ICON = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n    children: [\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 256 260\",\n            \"aria-label\": \"OpenAI Icon\",\n            className: \"w-4 h-4 dark:hidden block\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"OpenAI Icon Light\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M239.184 106.203a64.716 64.716 0 0 0-5.576-53.103C219.452 28.459 191 15.784 163.213 21.74A65.586 65.586 0 0 0 52.096 45.22a64.716 64.716 0 0 0-43.23 31.36c-14.31 24.602-11.061 55.634 8.033 76.74a64.665 64.665 0 0 0 5.525 53.102c14.174 24.65 42.644 37.324 70.446 31.36a64.72 64.72 0 0 0 48.754 21.744c28.481.025 53.714-18.361 62.414-45.481a64.767 64.767 0 0 0 43.229-31.36c14.137-24.558 10.875-55.423-8.083-76.483Zm-97.56 136.338a48.397 48.397 0 0 1-31.105-11.255l1.535-.87 51.67-29.825a8.595 8.595 0 0 0 4.247-7.367v-72.85l21.845 12.636c.218.111.37.32.409.563v60.367c-.056 26.818-21.783 48.545-48.601 48.601Zm-104.466-44.61a48.345 48.345 0 0 1-5.781-32.589l1.534.921 51.722 29.826a8.339 8.339 0 0 0 8.441 0l63.181-36.425v25.221a.87.87 0 0 1-.358.665l-52.335 30.184c-23.257 13.398-52.97 5.431-66.404-17.803ZM23.549 85.38a48.499 48.499 0 0 1 25.58-21.333v61.39a8.288 8.288 0 0 0 4.195 7.316l62.874 36.272-21.845 12.636a.819.819 0 0 1-.767 0L41.353 151.53c-23.211-13.454-31.171-43.144-17.804-66.405v.256Zm179.466 41.695-63.08-36.63L161.73 77.86a.819.819 0 0 1 .768 0l52.233 30.184a48.6 48.6 0 0 1-7.316 87.635v-61.391a8.544 8.544 0 0 0-4.4-7.213Zm21.742-32.69-1.535-.922-51.619-30.081a8.39 8.39 0 0 0-8.492 0L99.98 99.808V74.587a.716.716 0 0 1 .307-.665l52.233-30.133a48.652 48.652 0 0 1 72.236 50.391v.205ZM88.061 139.097l-21.845-12.585a.87.87 0 0 1-.41-.614V65.685a48.652 48.652 0 0 1 79.757-37.346l-1.535.87-51.67 29.825a8.595 8.595 0 0 0-4.246 7.367l-.051 72.697Zm11.868-25.58 28.138-16.217 28.188 16.218v32.434l-28.086 16.218-28.188-16.218-.052-32.434Z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 60,\n            columnNumber: 5\n        }, undefined),\n        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"20\",\n            height: \"20\",\n            viewBox: \"0 0 256 260\",\n            \"aria-label\": \"OpenAI Icon\",\n            className: \"w-4 h-4 hidden dark:block\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                    children: \"OpenAI Icon Dark\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fill: \"#fff\",\n                    d: \"M239.184 106.203a64.716 64.716 0 0 0-5.576-53.103C219.452 28.459 191 15.784 163.213 21.74A65.586 65.586 0 0 0 52.096 45.22a64.716 64.716 0 0 0-43.23 31.36c-14.31 24.602-11.061 55.634 8.033 76.74a64.665 64.665 0 0 0 5.525 53.102c14.174 24.65 42.644 37.324 70.446 31.36a64.72 64.72 0 0 0 48.754 21.744c28.481.025 53.714-18.361 62.414-45.481a64.767 64.767 0 0 0 43.229-31.36c14.137-24.558 10.875-55.423-8.083-76.483Zm-97.56 136.338a48.397 48.397 0 0 1-31.105-11.255l1.535-.87 51.67-29.825a8.595 8.595 0 0 0 4.247-7.367v-72.85l21.845 12.636c.218.111.37.32.409.563v60.367c-.056 26.818-21.783 48.545-48.601 48.601Zm-104.466-44.61a48.345 48.345 0 0 1-5.781-32.589l1.534.921 51.722 29.826a8.339 8.339 0 0 0 8.441 0l63.181-36.425v25.221a.87.87 0 0 1-.358.665l-52.335 30.184c-23.257 13.398-52.97 5.431-66.404-17.803ZM23.549 85.38a48.499 48.499 0 0 1 25.58-21.333v61.39a8.288 8.288 0 0 0 4.195 7.316l62.874 36.272-21.845 12.636a.819.819 0 0 1-.767 0L41.353 151.53c-23.211-13.454-31.171-43.144-17.804-66.405v.256Zm179.466 41.695-63.08-36.63L161.73 77.86a.819.819 0 0 1 .768 0l52.233 30.184a48.6 48.6 0 0 1-7.316 87.635v-61.391a8.544 8.544 0 0 0-4.4-7.213Zm21.742-32.69-1.535-.922-51.619-30.081a8.39 8.39 0 0 0-8.492 0L99.98 99.808V74.587a.716.716 0 0 1 .307-.665l52.233-30.133a48.652 48.652 0 0 1 72.236 50.391v.205ZM88.061 139.097l-21.845-12.585a.87.87 0 0 1-.41-.614V65.685a48.652 48.652 0 0 1 79.757-37.346l-1.535.87-51.67 29.825a8.595 8.595 0 0 0-4.246 7.367l-.051 72.697Zm11.868-25.58 28.138-16.217 28.188 16.218v32.434l-28.086 16.218-28.188-16.218-.052-32.434Z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 71,\n            columnNumber: 5\n        }, undefined)\n    ]\n}, void 0, true);\nfunction AI_Prompt() {\n    _s1();\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const { textareaRef, adjustHeight } = useAutoResizeTextarea({\n        minHeight: 60,\n        maxHeight: 200\n    });\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"GPT-4-1 Mini\");\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey && value.trim()) {\n            e.preventDefault();\n            setValue(\"\");\n            adjustHeight(true);\n        // Here you can add message sending\n        }\n    };\n    const handleSend = ()=>{\n        if (!value.trim()) return;\n        setValue(\"\");\n        adjustHeight(true);\n    // Here you can add message sending\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black/5 dark:bg-white/5 rounded-2xl p-1.5\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex flex-col\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto relative\",\n                        style: {\n                            maxHeight: \"400px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__.Textarea, {\n                                id: \"ai-input-15\",\n                                value: value,\n                                placeholder: \"What can I do for you?\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full bg-white-100 dark:bg-gray-700/60 rounded-xl px-3 py-2 pr-12 border border-gray-300/50 dark:border-gray-600/50 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 resize-none focus-visible:ring-0 focus-visible:ring-offset-0 transition-all duration-200\", \"min-h-[120px]\", \"hover:bg-gray-200/80 dark:hover:bg-gray-700/80 hover:border-gray-400 dark:hover:border-gray-500\", value && \"bg-gray-200/90 dark:bg-gray-700/90 border-gray-400 dark:border-gray-500\"),\n                                ref: textareaRef,\n                                onKeyDown: handleKeyDown,\n                                onChange: (e)=>{\n                                    setValue(e.target.value);\n                                    adjustHeight();\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-2 right-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_1__.SidebarButton, {\n                                    type: \"button\",\n                                    icon: _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    layout: \"icon-only\",\n                                    size: \"md\",\n                                    variant: value.trim() ? \"secondary\" : \"ghost\",\n                                    iconClassName: \"text-white\",\n                                    disabled: !value.trim(),\n                                    \"aria-label\": \"Send message\",\n                                    onClick: handleSend,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"transition-all duration-200\", value.trim() ? \"bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg hover:shadow-xl\" : \"opacity-40 bg-gray-300/50 dark:bg-gray-600/50 hover:opacity-60\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 116,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 114,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n        lineNumber: 113,\n        columnNumber: 5\n    }, this);\n}\n_s1(AI_Prompt, \"7miKjfTyNby4oG5huPpm3748zAw=\", false, function() {\n    return [\n        useAutoResizeTextarea\n    ];\n});\n_c = AI_Prompt;\nvar _c;\n$RefreshReg$(_c, \"AI_Prompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/animated-ai-input.tsx\n"));

/***/ })

});