"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx":
/*!***************************************************************************!*\
  !*** ./src/components/business-sections/BusinessSectionsGridEnhanced.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessSectionsGridEnhanced: () => (/* binding */ BusinessSectionsGridEnhanced)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ BusinessSectionsGridEnhanced auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Enhanced Item Row Component with dependency checking\nconst EnhancedItemRow = (param)=>{\n    let { item, onItemClick } = param;\n    // Check dependencies if item has them\n    const dependencyCheck = item.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_4__.checkItemDependencies)(item.id) : null;\n    const isBlocked = dependencyCheck && !dependencyCheck.isValid;\n    const getStatusStyles = function(status) {\n        let blocked = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (blocked) {\n            return \"bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60\";\n        }\n        switch(status){\n            case \"validated\":\n                return \"bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-700\";\n            case \"action\":\n                return \"bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-700\";\n            case \"idea\":\n                return \"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-700\";\n            case \"invalidated\":\n                return \"bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-600\";\n            default:\n                return \"bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300\";\n        }\n    };\n    const itemContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] \".concat(getStatusStyles(item.status, isBlocked !== null && isBlocked !== void 0 ? isBlocked : false), \" hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 \").concat(isBlocked ? \"cursor-not-allowed opacity-50 group\" : \"\"),\n        onClick: ()=>{\n            if (!isBlocked) {\n                onItemClick(item);\n            }\n        },\n        title: isBlocked ? \"It's too early to work on this\" : \"\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 flex-1 min-w-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium text-sm truncate\",\n                        children: item.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 flex-shrink-0\",\n                children: item.status !== \"invalidated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        item.actions > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.actions > 1 ? item.actions : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 15\n                        }, undefined),\n                        item.ideas > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.ideas > 1 ? item.ideas : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 15\n                        }, undefined),\n                        item.results > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.results > 1 ? item.results : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n    return itemContent;\n};\n_c = EnhancedItemRow;\n// Enhanced Expandable Card Component\nconst EnhancedExpandableCard = (param)=>{\n    let { section, onItemClick } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Calculate section progress\n    const totalItems = section.items.length;\n    const validatedItems = section.items.filter((item)=>item.status === \"validated\").length;\n    const progressPercentage = totalItems > 0 ? Math.round(validatedItems / totalItems * 100) : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n            open: isExpanded,\n            onOpenChange: setIsExpanded,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 my-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                progressPercentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform \".concat(isExpanded ? \"rotate-0\" : \"rotate-180\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-0 px-3\",\n                        children: section.items.sort((a, b)=>(a.order || 0) - (b.order || 0)).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedItemRow, {\n                                item: item,\n                                onItemClick: onItemClick\n                            }, item.id, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 133,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedExpandableCard, \"MzqrZ0LJxgqPa6EOF1Vxw0pgYA4=\");\n_c1 = EnhancedExpandableCard;\nfunction BusinessSectionsGridEnhanced(param) {\n    let { sections, onItemClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min\",\n                children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedExpandableCard, {\n                        section: section,\n                        onItemClick: onItemClick\n                    }, section.id, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BusinessSectionsGridEnhanced;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EnhancedItemRow\");\n$RefreshReg$(_c1, \"EnhancedExpandableCard\");\n$RefreshReg$(_c2, \"BusinessSectionsGridEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\n"));

/***/ })

});