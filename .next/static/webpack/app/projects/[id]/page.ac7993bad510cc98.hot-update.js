"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx":
/*!***************************************************************************!*\
  !*** ./src/components/business-sections/BusinessSectionsGridEnhanced.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessSectionsGridEnhanced: () => (/* binding */ BusinessSectionsGridEnhanced)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_6__);\n/* __next_internal_client_entry_do_not_use__ BusinessSectionsGridEnhanced auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Enhanced Item Row Component with dependency checking\nconst EnhancedItemRow = (param)=>{\n    let { item, onItemClick } = param;\n    // Check dependencies if item has them\n    const dependencyCheck = item.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_5__.checkItemDependencies)(item.id) : null;\n    const isBlocked = dependencyCheck && !dependencyCheck.isValid;\n    const getStatusStyles = function(status) {\n        let blocked = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (blocked) {\n            return \"bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60\";\n        }\n        switch(status){\n            case \"validated\":\n                return \"bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-700\";\n            case \"action\":\n                return \"bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-700\";\n            case \"idea\":\n                return \"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-700\";\n            case \"invalidated\":\n                return \"bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-600\";\n            default:\n                return \"bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300\";\n        }\n    };\n    const itemContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] \".concat(getStatusStyles(item.status, isBlocked !== null && isBlocked !== void 0 ? isBlocked : false), \" hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 \").concat(isBlocked ? \"cursor-not-allowed opacity-50\" : \"\"),\n        onClick: ()=>{\n            if (!isBlocked) {\n                onItemClick(item);\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 flex-1 min-w-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-medium text-sm truncate\",\n                            children: item.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined),\n                        isBlocked && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                            children: \"It's too early to work on this\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 flex-shrink-0\",\n                children: item.status !== \"invalidated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        item.actions > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.actions > 1 ? item.actions : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, undefined),\n                        item.ideas > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.ideas > 1 ? item.ideas : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 15\n                        }, undefined),\n                        item.results > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.results > 1 ? item.results : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 58,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipProvider, {\n        children: isBlocked ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.Tooltip, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipTrigger, {\n                    asChild: true,\n                    children: itemContent\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_4__.TooltipContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"It's too early to work on this\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 123,\n            columnNumber: 9\n        }, undefined) : itemContent\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, undefined);\n};\n_c = EnhancedItemRow;\n// Enhanced Expandable Card Component\nconst EnhancedExpandableCard = (param)=>{\n    let { section, onItemClick } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_6__.useState)(true);\n    // Calculate section progress\n    const totalItems = section.items.length;\n    const validatedItems = section.items.filter((item)=>item.status === \"validated\").length;\n    const progressPercentage = totalItems > 0 ? Math.round(validatedItems / totalItems * 100) : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n            open: isExpanded,\n            onOpenChange: setIsExpanded,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 my-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                            children: section.title\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"text-xs\",\n                                            children: [\n                                                validatedItems,\n                                                \"/\",\n                                                totalItems\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: [\n                                                progressPercentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform \".concat(isExpanded ? \"rotate-0\" : \"rotate-180\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-0 px-3\",\n                        children: section.items.sort((a, b)=>(a.order || 0) - (b.order || 0)).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedItemRow, {\n                                item: item,\n                                onItemClick: onItemClick\n                            }, item.id, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 156,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 155,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedExpandableCard, \"MzqrZ0LJxgqPa6EOF1Vxw0pgYA4=\");\n_c1 = EnhancedExpandableCard;\nfunction BusinessSectionsGridEnhanced(param) {\n    let { sections, onItemClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min\",\n                children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedExpandableCard, {\n                        section: section,\n                        onItemClick: onItemClick\n                    }, section.id, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 210,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BusinessSectionsGridEnhanced;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EnhancedItemRow\");\n$RefreshReg$(_c1, \"EnhancedExpandableCard\");\n$RefreshReg$(_c2, \"BusinessSectionsGridEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\n"));

/***/ })

});