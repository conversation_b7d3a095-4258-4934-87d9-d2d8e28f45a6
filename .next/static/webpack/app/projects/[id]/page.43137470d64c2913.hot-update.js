"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx":
/*!***************************************************************************!*\
  !*** ./src/components/business-sections/BusinessSectionsGridEnhanced.tsx ***!
  \***************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessSectionsGridEnhanced: () => (/* binding */ BusinessSectionsGridEnhanced)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/collapsible */ \"(app-pages-browser)/./src/components/ui/collapsible.tsx\");\n/* harmony import */ var _lib_dependencyManager__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/dependencyManager */ \"(app-pages-browser)/./src/lib/dependencyManager.ts\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronUp,Lightbulb,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ BusinessSectionsGridEnhanced auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Enhanced Item Row Component with dependency checking\nconst EnhancedItemRow = (param)=>{\n    let { item, onItemClick } = param;\n    // Check dependencies if item has them\n    const dependencyCheck = item.dependencies ? (0,_lib_dependencyManager__WEBPACK_IMPORTED_MODULE_4__.checkItemDependencies)(item.id) : null;\n    const isBlocked = dependencyCheck && !dependencyCheck.isValid;\n    const getStatusStyles = function(status) {\n        let blocked = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (blocked) {\n            return \"bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60\";\n        }\n        switch(status){\n            case \"validated\":\n                return \"bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 border-green-200 dark:border-green-700\";\n            case \"action\":\n                return \"bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 border-blue-200 dark:border-blue-700\";\n            case \"idea\":\n                return \"bg-yellow-50 dark:bg-yellow-900/20 text-yellow-900 dark:text-yellow-100 border-yellow-200 dark:border-yellow-700\";\n            case \"invalidated\":\n                return \"bg-gray-50 dark:bg-gray-800 text-gray-600 dark:text-gray-300 border-gray-200 dark:border-gray-600\";\n            default:\n                return \"bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300\";\n        }\n    };\n    const itemContent = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] \".concat(getStatusStyles(item.status, isBlocked !== null && isBlocked !== void 0 ? isBlocked : false), \" hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 \").concat(isBlocked ? \"cursor-not-allowed opacity-50 group\" : \"\"),\n        onClick: ()=>{\n            if (!isBlocked) {\n                onItemClick(item);\n            }\n        },\n        title: isBlocked ? \"It's too early to work on this\" : \"\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3 flex-1 min-w-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"font-medium text-sm truncate\",\n                        children: item.title\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-1 flex-shrink-0\",\n                children: item.status !== \"invalidated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        item.actions > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.actions > 1 ? item.actions : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 15\n                        }, undefined),\n                        item.ideas > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-3 w-3\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.ideas > 1 ? item.ideas : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 15\n                        }, undefined),\n                        item.results > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            variant: \"secondary\",\n                            className: \"bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, undefined),\n                                item.results > 1 ? item.results : \"\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 52,\n        columnNumber: 5\n    }, undefined);\n    return itemContent;\n};\n_c = EnhancedItemRow;\n// Enhanced Expandable Card Component\nconst EnhancedExpandableCard = (param)=>{\n    let { section, onItemClick } = param;\n    _s();\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    // Calculate section progress\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.Collapsible, {\n            open: isExpanded,\n            onOpenChange: setIsExpanded,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleTrigger, {\n                    asChild: true,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 mb-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                        children: section.title\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronUp_Lightbulb_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform \".concat(isExpanded ? \"rotate-0\" : \"rotate-180\")\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_collapsible__WEBPACK_IMPORTED_MODULE_3__.CollapsibleContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"py-0 px-3\",\n                        children: section.items.sort((a, b)=>(a.order || 0) - (b.order || 0)).map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedItemRow, {\n                                item: item,\n                                onItemClick: onItemClick\n                            }, item.id, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 127,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedExpandableCard, \"MzqrZ0LJxgqPa6EOF1Vxw0pgYA4=\");\n_c1 = EnhancedExpandableCard;\nfunction BusinessSectionsGridEnhanced(param) {\n    let { sections, onItemClick } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full relative z-40\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min\",\n                children: sections.map((section)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedExpandableCard, {\n                        section: section,\n                        onItemClick: onItemClick\n                    }, section.id, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n                lineNumber: 176,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n            lineNumber: 175,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BusinessSectionsGridEnhanced;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EnhancedItemRow\");\n$RefreshReg$(_c1, \"EnhancedExpandableCard\");\n$RefreshReg$(_c2, \"BusinessSectionsGridEnhanced\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-sections/BusinessSectionsGridEnhanced.tsx\n"));

/***/ })

});