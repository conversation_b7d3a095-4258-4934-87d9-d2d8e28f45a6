"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/business-item-table.tsx":
/*!************************************************!*\
  !*** ./src/components/business-item-table.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BusinessItemTable: () => (/* binding */ BusinessItemTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* harmony import */ var _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/stores/businessItemStore */ \"(app-pages-browser)/./src/stores/businessItemStore.ts\");\n/* harmony import */ var _hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @hello-pangea/dnd */ \"(app-pages-browser)/./node_modules/@hello-pangea/dnd/dist/dnd.esm.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grip-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,GripVertical,MoreHorizontal,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _EditableCell__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./EditableCell */ \"(app-pages-browser)/./src/components/EditableCell.tsx\");\n/* __next_internal_client_entry_do_not_use__ BusinessItemTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n// Get status color helper\nconst getStatusColor = (status)=>{\n    switch(status){\n        case \"idea\":\n            return \"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200\";\n        case \"action\":\n            return \"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200\";\n        case \"validated\":\n            return \"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\";\n        case \"invalidated\":\n            return \"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200\";\n        default:\n            return \"bg-muted text-muted-foreground\";\n    }\n};\n// Helper function to determine status based on content\nconst getAutoStatus = (title, actions, result)=>{\n    const hasTitle = title.trim() !== \"\";\n    const hasActions = actions.trim() !== \"\";\n    const hasResult = result.trim() !== \"\";\n    if (hasTitle && !hasActions && !hasResult) {\n        return \"idea\";\n    } else if (hasTitle && hasActions && !hasResult) {\n        return \"action\";\n    } else if (hasTitle && hasActions && hasResult) {\n        return \"validated\"; // Default when all fields are filled\n    }\n    return \"idea\"; // Default fallback\n};\n// Helper function to check if status should be editable\nconst isStatusEditable = (title, actions, result)=>{\n    const hasTitle = title.trim() !== \"\";\n    const hasActions = actions.trim() !== \"\";\n    const hasResult = result.trim() !== \"\";\n    // Only editable when all three fields are filled (can choose between validated/invalidated)\n    return hasTitle && hasActions && hasResult;\n};\n// StatusSelect component\nfunction StatusSelect(param) {\n    let { value, onChange, placeholder, disabled = false, detail } = param;\n    const editable = isStatusEditable(detail.title, detail.actions, detail.result);\n    if (!editable) {\n        // Show as non-editable badge\n        const autoStatus = getAutoStatus(detail.title, detail.actions, detail.result);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n            className: \"\".concat(getStatusColor(autoStatus), \" capitalize\"),\n            children: autoStatus\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.Select, {\n        value: value,\n        onValueChange: onChange,\n        disabled: disabled,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectTrigger, {\n                className: \"w-[120px] h-7 px-2\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectValue, {\n                    placeholder: placeholder\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                        value: \"validated\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            className: getStatusColor(\"validated\"),\n                            children: \"Validated\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_5__.SelectItem, {\n                        value: \"invalidated\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            className: getStatusColor(\"invalidated\"),\n                            children: \"Invalidated\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 129,\n        columnNumber: 5\n    }, this);\n}\n_c = StatusSelect;\n// DragHandle component\nfunction DragHandle(param) {\n    let { provided } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ...provided.dragHandleProps,\n        className: \"cursor-grab py-3 px-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-muted/50\",\n        title: \"Drag to reorder\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-4 w-4 text-muted-foreground hover:text-foreground transition-colors\"\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n            lineNumber: 153,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_c1 = DragHandle;\n// BusinessItemRow component\nfunction BusinessItemRow(param) {\n    let { detail, index, editingCell, setEditingCell, onSave, onStatusChange } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__.Draggable, {\n        draggableId: detail.id,\n        index: index,\n        children: (provided, snapshot)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                ref: provided.innerRef,\n                ...provided.draggableProps,\n                className: \"group hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 \".concat(snapshot.isDragging ? \"bg-gray-200 dark:bg-gray-700 shadow-lg scale-[1.02] rotate-1\" : \"\", \" \").concat(index % 2 === 1 ? \"bg-gray-50 dark:bg-gray-900\" : \"bg-background\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-0 px-0 border-r\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DragHandle, {\n                            provided: provided\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"font-medium py-3 px-3 border-r w-1/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                            id: detail.id,\n                            field: \"title\",\n                            value: detail.title,\n                            multiline: true,\n                            className: \"font-semibold\",\n                            editingCell: editingCell,\n                            setEditingCell: setEditingCell,\n                            onSave: onSave\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3 border-r w-1/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                            id: detail.id,\n                            field: \"actions\",\n                            value: detail.actions,\n                            multiline: true,\n                            editingCell: editingCell,\n                            setEditingCell: setEditingCell,\n                            onSave: onSave\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3 border-r w-1/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                            id: detail.id,\n                            field: \"result\",\n                            value: detail.result,\n                            multiline: true,\n                            editingCell: editingCell,\n                            setEditingCell: setEditingCell,\n                            onSave: onSave\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3 border-r\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusSelect, {\n                            value: detail.status,\n                            onChange: (value)=>onStatusChange(detail.id, value),\n                            detail: detail\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                        className: \"py-3 px-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"h-8 w-8 p-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Open menu\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                    align: \"end\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"gap-2 text-red-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_GripVertical_MoreHorizontal_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Delete\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                    lineNumber: 240,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 233,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 177,\n                columnNumber: 9\n            }, this)\n    }, detail.id, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 175,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BusinessItemRow;\n// NewRow component\nfunction NewRow(param) {\n    let { newRowData, editingCell, setEditingCell, onSave, onStatusChange } = param;\n    // Create a mock detail object for status determination\n    const mockDetail = {\n        id: \"new-row\",\n        title: newRowData.title,\n        actions: newRowData.actions,\n        result: newRowData.result,\n        status: newRowData.status\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n        className: \"bg-gray-50 dark:bg-gray-900 border-t-2 border-dashed border-gray-300 dark:border-gray-600\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"font-medium py-3 px-3 border-r w-1/3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                    id: \"new-row\",\n                    field: \"title\",\n                    value: \"\",\n                    multiline: true,\n                    className: \"font-semibold\",\n                    editingCell: editingCell,\n                    setEditingCell: setEditingCell,\n                    onSave: onSave,\n                    newRowData: newRowData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r w-1/3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                    id: \"new-row\",\n                    field: \"actions\",\n                    value: \"\",\n                    multiline: false,\n                    editingCell: editingCell,\n                    setEditingCell: setEditingCell,\n                    onSave: onSave,\n                    newRowData: newRowData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r w-1/3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_EditableCell__WEBPACK_IMPORTED_MODULE_10__.EditableCell, {\n                    id: \"new-row\",\n                    field: \"result\",\n                    value: \"\",\n                    multiline: false,\n                    editingCell: editingCell,\n                    setEditingCell: setEditingCell,\n                    onSave: onSave,\n                    newRowData: newRowData\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 321,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3 border-r\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusSelect, {\n                    value: newRowData.status,\n                    onChange: (value)=>onStatusChange(\"new-row\", value),\n                    placeholder: \"Status\",\n                    detail: mockDetail\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 334,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 333,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableCell, {\n                className: \"py-3 px-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 w-8\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 341,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 292,\n        columnNumber: 5\n    }, this);\n}\n_c3 = NewRow;\nfunction BusinessItemTable(param) {\n    let { itemDetails } = param;\n    _s();\n    const { updateItemDetail, addItemDetail } = (0,_stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStore)();\n    // Local state for reordering\n    const [localItemDetails, setLocalItemDetails] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(itemDetails);\n    const [editingCell, setEditingCell] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(null);\n    const [isHowItWorksOpen, setIsHowItWorksOpen] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const [newRowData, setNewRowData] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)({\n        id: \"new-row\",\n        title: \"\",\n        actions: \"\",\n        result: \"\",\n        status: \"idea\"\n    });\n    // Update local state when props change\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)({\n        \"BusinessItemTable.useEffect\": ()=>{\n            setLocalItemDetails(itemDetails);\n        }\n    }[\"BusinessItemTable.useEffect\"], [\n        itemDetails\n    ]);\n    const handleSave = (id, field, value)=>{\n        if (id === \"new-row\") {\n            // Handle new row creation\n            const updatedNewRowData = {\n                ...newRowData,\n                [field]: value\n            };\n            // Calculate the appropriate status based on filled fields\n            const autoStatus = getAutoStatus(field === \"title\" ? value : newRowData.title, field === \"actions\" ? value : newRowData.actions, field === \"result\" ? value : newRowData.result);\n            const newId = \"item-\".concat(Date.now());\n            const newItem = {\n                id: newId,\n                title: field === \"title\" ? value : newRowData.title,\n                actions: field === \"actions\" ? value : newRowData.actions,\n                result: field === \"result\" ? value : newRowData.result,\n                status: autoStatus,\n                description: \"\",\n                updatedAt: new Date().toISOString()\n            };\n            // Update the specific field that was edited\n            newItem[field] = value;\n            // Only create if there's actual content\n            if (value.trim() !== \"\") {\n                const updatedItems = [\n                    ...localItemDetails,\n                    newItem\n                ];\n                setLocalItemDetails(updatedItems);\n                addItemDetail(newItem);\n                // Reset new row data\n                setNewRowData({\n                    id: \"new-row\",\n                    title: \"\",\n                    actions: \"\",\n                    result: \"\",\n                    status: \"idea\"\n                });\n            } else {\n                // Just update the new row data for display\n                setNewRowData(updatedNewRowData);\n            }\n        } else {\n            // Handle existing item update\n            const item = localItemDetails.find((item)=>item.id === id);\n            if (item) {\n                const updatedItem = {\n                    ...item,\n                    [field]: value\n                };\n                // Auto-update status based on content\n                const autoStatus = getAutoStatus(field === \"title\" ? value : item.title, field === \"actions\" ? value : item.actions, field === \"result\" ? value : item.result);\n                // Only auto-update status if it's not manually set to validated/invalidated\n                if (!isStatusEditable(updatedItem.title, updatedItem.actions, updatedItem.result) || item.status !== \"validated\" && item.status !== \"invalidated\") {\n                    updatedItem.status = autoStatus;\n                }\n                const updatedItems = localItemDetails.map((existingItem)=>existingItem.id === id ? updatedItem : existingItem);\n                setLocalItemDetails(updatedItems);\n                updateItemDetail(id, {\n                    [field]: value,\n                    status: updatedItem.status\n                });\n            }\n        }\n        setEditingCell(null);\n    };\n    const handleDragEnd = (result)=>{\n        // Check if the drop was outside the droppable area\n        if (!result.destination) {\n            return;\n        }\n        const { source, destination } = result;\n        // If dropped in the same position, do nothing\n        if (source.index === destination.index) {\n            return;\n        }\n        // Reorder items\n        const newItems = Array.from(localItemDetails);\n        const [reorderedItem] = newItems.splice(source.index, 1);\n        newItems.splice(destination.index, 0, reorderedItem);\n        setLocalItemDetails(newItems);\n    };\n    // Handle status change (only for validated/invalidated when all fields are filled)\n    const handleStatusChange = (id, newStatus)=>{\n        if (id === \"new-row\") {\n            setNewRowData((prev)=>({\n                    ...prev,\n                    status: newStatus\n                }));\n            return;\n        }\n        const updatedItems = localItemDetails.map((item)=>item.id === id ? {\n                ...item,\n                status: newStatus\n            } : item);\n        setLocalItemDetails(updatedItems);\n        updateItemDetail(id, {\n            status: newStatus\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__.TooltipProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-full overflow-hidden -m-0 -p-0\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"w-full p-0 m-0 overflow-hidden border-0 shadow-none rounded-none\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"p-0 m-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__.DragDropContext, {\n                            onDragEnd: handleDragEnd,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full overflow-auto max-h-[calc(100vh-200px)] m-0 p-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.Table, {\n                                    className: \"m-0 rounded-none\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHeader, {\n                                            className: \"sticky top-0 bg-gray-200 dark:bg-gray-800 backdrop-blur-sm z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableRow, {\n                                                className: \"border-b-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"bg-gray-200 dark:bg-gray-800 border-r\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3\",\n                                                        children: \"Idea\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                        lineNumber: 506,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3\",\n                                                        children: \"Action\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                        lineNumber: 509,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3\",\n                                                        children: \"Result\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-32\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableHead, {\n                                                        className: \"font-semibold bg-gray-200 dark:bg-gray-800 w-20\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 503,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hello_pangea_dnd__WEBPACK_IMPORTED_MODULE_12__.Droppable, {\n                                            droppableId: \"business-items\",\n                                            children: (provided)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_6__.TableBody, {\n                                                    ...provided.droppableProps,\n                                                    ref: provided.innerRef,\n                                                    children: [\n                                                        localItemDetails.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BusinessItemRow, {\n                                                                detail: detail,\n                                                                index: index,\n                                                                editingCell: editingCell,\n                                                                setEditingCell: setEditingCell,\n                                                                onSave: handleSave,\n                                                                onStatusChange: handleStatusChange\n                                                            }, detail.id, false, {\n                                                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 27\n                                                            }, this)),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NewRow, {\n                                                            newRowData: newRowData,\n                                                            editingCell: editingCell,\n                                                            setEditingCell: setEditingCell,\n                                                            onSave: handleSave,\n                                                            onStatusChange: handleStatusChange\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        provided.placeholder\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                                    lineNumber: 524,\n                                                    columnNumber: 23\n                                                }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 500,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-b border-gray-200 dark:border-gray-700\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                    lineNumber: 499,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n                lineNumber: 498,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n            lineNumber: 497,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx\",\n        lineNumber: 496,\n        columnNumber: 5\n    }, this);\n}\n_s(BusinessItemTable, \"hsghXYR8cMt5O7DWFS+NX589ziY=\", false, function() {\n    return [\n        _stores_businessItemStore__WEBPACK_IMPORTED_MODULE_8__.useBusinessItemStore\n    ];\n});\n_c4 = BusinessItemTable;\nvar _c, _c1, _c2, _c3, _c4;\n$RefreshReg$(_c, \"StatusSelect\");\n$RefreshReg$(_c1, \"DragHandle\");\n$RefreshReg$(_c2, \"BusinessItemRow\");\n$RefreshReg$(_c3, \"NewRow\");\n$RefreshReg$(_c4, \"BusinessItemTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/business-item-table.tsx\n"));

/***/ })

});