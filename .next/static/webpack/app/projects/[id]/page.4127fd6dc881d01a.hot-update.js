"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/components/ui/animated-ai-input.tsx":
/*!*************************************************!*\
  !*** ./src/components/ui/animated-ai-input.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AI_Prompt: () => (/* binding */ AI_Prompt)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/sidebar-button */ \"(app-pages-browser)/./src/components/ui/sidebar-button.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ AI_Prompt auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\nfunction useAutoResizeTextarea(param) {\n    let { minHeight, maxHeight } = param;\n    _s();\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const adjustHeight = (0,react__WEBPACK_IMPORTED_MODULE_4__.useCallback)({\n        \"useAutoResizeTextarea.useCallback[adjustHeight]\": (reset)=>{\n            const textarea = textareaRef.current;\n            if (!textarea) return;\n            if (reset) {\n                textarea.style.height = \"\".concat(minHeight, \"px\");\n                return;\n            }\n            textarea.style.height = \"\".concat(minHeight, \"px\");\n            const newHeight = Math.max(minHeight, Math.min(textarea.scrollHeight, maxHeight !== null && maxHeight !== void 0 ? maxHeight : Number.POSITIVE_INFINITY));\n            textarea.style.height = \"\".concat(newHeight, \"px\");\n        }\n    }[\"useAutoResizeTextarea.useCallback[adjustHeight]\"], [\n        minHeight,\n        maxHeight\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"useAutoResizeTextarea.useEffect\": ()=>{\n            const textarea = textareaRef.current;\n            if (textarea) {\n                textarea.style.height = \"\".concat(minHeight, \"px\");\n            }\n        }\n    }[\"useAutoResizeTextarea.useEffect\"], [\n        minHeight\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"useAutoResizeTextarea.useEffect\": ()=>{\n            const handleResize = {\n                \"useAutoResizeTextarea.useEffect.handleResize\": ()=>adjustHeight()\n            }[\"useAutoResizeTextarea.useEffect.handleResize\"];\n            window.addEventListener(\"resize\", handleResize);\n            return ({\n                \"useAutoResizeTextarea.useEffect\": ()=>window.removeEventListener(\"resize\", handleResize)\n            })[\"useAutoResizeTextarea.useEffect\"];\n        }\n    }[\"useAutoResizeTextarea.useEffect\"], [\n        adjustHeight\n    ]);\n    return {\n        textareaRef,\n        adjustHeight\n    };\n}\n_s(useAutoResizeTextarea, \"dY5gUJDyLTt7nR3p8fNJ7y+Lo90=\");\nfunction AI_Prompt() {\n    _s1();\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"\");\n    const { textareaRef, adjustHeight } = useAutoResizeTextarea({\n        minHeight: 60,\n        maxHeight: 200\n    });\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(\"GPT-4-1 Mini\");\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey && value.trim()) {\n            e.preventDefault();\n            setValue(\"\");\n            adjustHeight(true);\n        // Here you can add message sending\n        }\n    };\n    const handleSend = ()=>{\n        if (!value.trim()) return;\n        setValue(\"\");\n        adjustHeight(true);\n    // Here you can add message sending\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-black/5 dark:bg-white/5 rounded-2xl p-1.5\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex flex-col\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-y-auto relative\",\n                        style: {\n                            maxHeight: \"400px\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_2__.Textarea, {\n                                id: \"ai-input-15\",\n                                value: value,\n                                placeholder: \"What can I do for you?\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-full bg-red-600 dark:bg-gray-700/60 rounded-xl px-3 py-2 pr-12 border border-gray-300/50 dark:border-gray-600/50 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 resize-none focus-visible:ring-0 focus-visible:ring-offset-0 transition-all duration-200\", \"min-h-[120px]\", \"hover:bg-gray-200/80 dark:hover:bg-gray-700/80 hover:border-gray-400 dark:hover:border-gray-500\", value && \"bg-gray-200/90 dark:bg-gray-700/90 border-gray-400 dark:border-gray-500\"),\n                                ref: textareaRef,\n                                onKeyDown: handleKeyDown,\n                                onChange: (e)=>{\n                                    setValue(e.target.value);\n                                    adjustHeight();\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-2 right-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar_button__WEBPACK_IMPORTED_MODULE_1__.SidebarButton, {\n                                    type: \"button\",\n                                    icon: _barrel_optimize_names_ArrowRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    layout: \"icon-only\",\n                                    size: \"md\",\n                                    variant: value.trim() ? \"secondary\" : \"ghost\",\n                                    iconClassName: \"text-white\",\n                                    disabled: !value.trim(),\n                                    \"aria-label\": \"Send message\",\n                                    onClick: handleSend,\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"transition-all duration-200\", value.trim() ? \"bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg hover:shadow-xl\" : \"opacity-40 bg-gray-300/50 dark:bg-gray-600/50 hover:opacity-60\")\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n            lineNumber: 84,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, this);\n}\n_s1(AI_Prompt, \"7miKjfTyNby4oG5huPpm3748zAw=\", false, function() {\n    return [\n        useAutoResizeTextarea\n    ];\n});\n_c = AI_Prompt;\nvar _c;\n$RefreshReg$(_c, \"AI_Prompt\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/animated-ai-input.tsx\n"));

/***/ })

});