"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/projects/[id]/page",{

/***/ "(app-pages-browser)/./src/services/businessSectionsApi.ts":
/*!*********************************************!*\
  !*** ./src/services/businessSectionsApi.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   BusinessSectionsApi: () => (/* binding */ BusinessSectionsApi),\n/* harmony export */   bulkUpdateItems: () => (/* binding */ bulkUpdateItems),\n/* harmony export */   createItem: () => (/* binding */ createItem),\n/* harmony export */   deleteItem: () => (/* binding */ deleteItem),\n/* harmony export */   fetchItem: () => (/* binding */ fetchItem),\n/* harmony export */   fetchSections: () => (/* binding */ fetchSections),\n/* harmony export */   getAnalytics: () => (/* binding */ getAnalytics),\n/* harmony export */   updateItem: () => (/* binding */ updateItem)\n/* harmony export */ });\n/* harmony import */ var _lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/businessSectionsDataNew */ \"(app-pages-browser)/./src/lib/businessSectionsDataNew.ts\");\n// API service layer for business sections\n\n// API Configuration\n// Error types\nclass ApiError extends Error {\n    constructor(message, status, code){\n        super(message), this.status = status, this.code = code;\n        this.name = \"ApiError\";\n    }\n}\n// Request wrapper with error handling\n// Business Sections API\nclass BusinessSectionsApi {\n    // Fetch all business sections for a project\n    static async fetchSections(_projectId) {\n        try {\n            // Use the new comprehensive data with proper delay simulation\n            return await (0,_lib_businessSectionsDataNew__WEBPACK_IMPORTED_MODULE_0__.fetchBusinessSectionsNew)();\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessSection[]>(`/projects/${projectId}/business-sections`);\n        } catch (error) {\n            console.error(\"Failed to fetch business sections:\", error);\n            throw error;\n        }\n    }\n    // Fetch a specific business item\n    static async fetchItem(projectId, itemId) {\n        try {\n            // Mock implementation\n            const sections = await this.fetchSections(projectId);\n            const item = sections.flatMap((section)=>section.items).find((item)=>item.id === itemId);\n            if (!item) {\n                throw new ApiError(\"Business item not found\", 404, \"ITEM_NOT_FOUND\");\n            }\n            return item;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem>(`/projects/${projectId}/business-sections/items/${itemId}`);\n        } catch (error) {\n            console.error(\"Failed to fetch business item:\", error);\n            throw error;\n        }\n    }\n    // Update a business item\n    static async updateItem(projectId, itemId, updates) {\n        try {\n            // Mock implementation - in real app, this would update the backend\n            const item = await this.fetchItem(projectId, itemId);\n            const updatedItem = {\n                ...item,\n                ...updates\n            };\n            // Simulate API delay\n            await new Promise((resolve)=>setTimeout(resolve, 300));\n            return updatedItem;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem>(\n        //   `/projects/${projectId}/business-sections/items/${itemId}`,\n        //   {\n        //     method: \"PATCH\",\n        //     body: JSON.stringify(updates),\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to update business item:\", error);\n            throw error;\n        }\n    }\n    // Create a new business item\n    static async createItem(_projectId, _sectionId, item) {\n        try {\n            // Mock implementation\n            const newItem = {\n                ...item,\n                id: \"item-\".concat(Date.now())\n            };\n            // Simulate API delay\n            await new Promise((resolve)=>setTimeout(resolve, 300));\n            return newItem;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem>(\n        //   `/projects/${projectId}/business-sections/${sectionId}/items`,\n        //   {\n        //     method: \"POST\",\n        //     body: JSON.stringify(item),\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to create business item:\", error);\n            throw error;\n        }\n    }\n    // Delete a business item\n    static async deleteItem(_projectId, _itemId) {\n        try {\n            // Mock implementation\n            await new Promise((resolve)=>setTimeout(resolve, 300));\n        // Uncomment when API is ready:\n        // await apiRequest<void>(\n        //   `/projects/${projectId}/business-sections/items/${itemId}`,\n        //   {\n        //     method: \"DELETE\",\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to delete business item:\", error);\n            throw error;\n        }\n    }\n    // Bulk update multiple items\n    static async bulkUpdateItems(projectId, updates) {\n        try {\n            // Mock implementation\n            const updatedItems = [];\n            for (const { itemId, updates: itemUpdates } of updates){\n                const updatedItem = await this.updateItem(projectId, itemId, itemUpdates);\n                updatedItems.push(updatedItem);\n            }\n            return updatedItems;\n        // Uncomment when API is ready:\n        // return await apiRequest<BusinessItem[]>(\n        //   `/projects/${projectId}/business-sections/items/bulk-update`,\n        //   {\n        //     method: \"PATCH\",\n        //     body: JSON.stringify({ updates }),\n        //   }\n        // );\n        } catch (error) {\n            console.error(\"Failed to bulk update business items:\", error);\n            throw error;\n        }\n    }\n    // Get business sections analytics\n    static async getAnalytics(projectId) {\n        try {\n            const sections = await this.fetchSections(projectId);\n            const allItems = sections.flatMap((section)=>section.items);\n            const totalItems = allItems.length;\n            const completedItems = allItems.filter((item)=>item.status === \"validated\").length;\n            const completionPercentage = totalItems > 0 ? completedItems / totalItems * 100 : 0;\n            const categoryBreakdown = {};\n            sections.forEach((section)=>{\n                const total = section.items.length;\n                const completed = section.items.filter((item)=>item.status === \"validated\").length;\n                categoryBreakdown[section.title] = {\n                    total,\n                    completed\n                };\n            });\n            return {\n                totalItems,\n                completedItems,\n                completionPercentage,\n                categoryBreakdown\n            };\n        // Uncomment when API is ready:\n        // return await apiRequest<any>(`/projects/${projectId}/business-sections/analytics`);\n        } catch (error) {\n            console.error(\"Failed to fetch business sections analytics:\", error);\n            throw error;\n        }\n    }\n}\n// Export convenience functions\nconst { fetchSections, fetchItem, updateItem, createItem, deleteItem, bulkUpdateItems, getAnalytics } = BusinessSectionsApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/businessSectionsApi.ts\n"));

/***/ })

});