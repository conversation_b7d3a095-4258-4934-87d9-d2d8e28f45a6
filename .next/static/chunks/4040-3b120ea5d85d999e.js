(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4040],{15452:(e,t,n)=>{"use strict";n.d(t,{UC:()=>en,VY:()=>eo,ZL:()=>ee,bL:()=>X,bm:()=>el,hE:()=>er,hJ:()=>et,l9:()=>$});var r=n(12115),o=n(85185),l=n(6101),a=n(46081),i=n(61285),s=n(5845),u=n(19178),d=n(25519),c=n(34378),f=n(28905),p=n(63655),g=n(92293),h=n(93795),v=n(38168),m=n(99708),y=n(95155),b="Dialog",[x,j]=(0,a.A)(b),[C,D]=x(b),R=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:l,onOpenChange:a,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:b});return(0,y.jsx)(C,{scope:t,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};R.displayName=b;var _="DialogTrigger",w=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=D(_,n),i=(0,l.s)(t,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":U(a.open),...r,ref:i,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});w.displayName=_;var O="DialogPortal",[P,k]=x(O,{forceMount:void 0}),A=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:l}=e,a=D(O,t);return(0,y.jsx)(P,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:n||a.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};A.displayName=O;var I="DialogOverlay",E=r.forwardRef((e,t)=>{let n=k(I,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=D(I,e.__scopeDialog);return l.modal?(0,y.jsx)(f.C,{present:r||l.open,children:(0,y.jsx)(F,{...o,ref:t})}):null});E.displayName=I;var N=(0,m.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=D(I,n);return(0,y.jsx)(h.A,{as:N,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":U(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),M="DialogContent",T=r.forwardRef((e,t)=>{let n=k(M,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,l=D(M,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||l.open,children:l.modal?(0,y.jsx)(B,{...o,ref:t}):(0,y.jsx)(G,{...o,ref:t})})});T.displayName=M;var B=r.forwardRef((e,t)=>{let n=D(M,e.__scopeDialog),a=r.useRef(null),i=(0,l.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(q,{...e,ref:i,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=r.forwardRef((e,t)=>{let n=D(M,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,y.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(l.current=!0));let i=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&l.current&&t.preventDefault()}})}),q=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,c=D(M,n),f=r.useRef(null),p=(0,l.s)(t,f);return(0,g.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,y.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":U(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(z,{titleId:c.titleId}),(0,y.jsx)(Q,{contentRef:f,descriptionId:c.descriptionId})]})]})}),H="DialogTitle",W=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=D(H,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})});W.displayName=H;var L="DialogDescription",S=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=D(L,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})});S.displayName=L;var V="DialogClose",Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,l=D(V,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function U(e){return e?"open":"closed"}Z.displayName=V;var J="DialogTitleWarning",[K,Y]=(0,a.q)(J,{contentName:M,titleName:H,docsSlug:"dialog"}),z=e=>{let{titleId:t}=e,n=Y(J),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Q=e=>{let{contentRef:t,descriptionId:n}=e,o=Y("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(l))},[l,t,n]),null},X=R,$=w,ee=A,et=E,en=T,er=W,eo=S,el=Z},27016:(e,t,n)=>{"use strict";n.d(t,{PromisifiedAuthProvider:()=>s,d:()=>u});var r=n(48879),o=n(38572),l=n(35583),a=n(12115);let i=a.createContext(null);function s(e){let{authPromise:t,children:n}=e;return a.createElement(i.Provider,{value:t},n)}function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,l.useRouter)(),n=a.useContext(i),s=n;return(n&&"then"in n&&(s=a.use(n)),"undefined"!=typeof window)?(0,r.As)({...s,...e}):t?(0,r.As)(e):(0,o.hP)({...s,...e})}},34835:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},35583:(e,t,n)=>{e.exports=n(63950)},38572:(e,t,n)=>{"use strict";n.d(t,{T5:()=>o.T5,hP:()=>r.hP,nO:()=>o.nO,yC:()=>l}),n(79419);var r=n(9693),o=n(62451);function l(e,t,n){let o=t.path||(null==n?void 0:n.path);return"path"===(t.routing||(null==n?void 0:n.routing)||"path")?o?{...n,...t,routing:"path"}:r.sb.throw((0,r.kd)(e)):t.path?r.sb.throw((0,r.s7)(e)):{...n,...t,path:void 0}}},54416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},63950:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useRouter",{enumerable:!0,get:function(){return l}});let r=n(12115),o=n(70901);function l(){return(0,r.useContext)(o.RouterContext)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70901:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return r}});let r=n(88229)._(n(12115)).default.createContext(null)},71007:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])}}]);