"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8879],{9693:(e,t,r)=>{r.d(t,{cy:()=>d,SW:()=>p,D:()=>P,wm:()=>O,sR:()=>S,n:()=>_,sb:()=>a,s7:()=>j,Wq:()=>g,yN:()=>f,kd:()=>w,kf:()=>b,vb:()=>y,wV:()=>u,Vo:()=>m,Kz:()=>L,As:()=>W,ho:()=>l.ho,hP:()=>x,ui:()=>D,FE:()=>h,Z5:()=>l.Z5,D_:()=>l.D_,Wp:()=>l.Wp,dy:()=>l.wV,g7:()=>l.g7,go:()=>N,yC:()=>F,Jd:()=>l.Jd,P6:()=>C,aU:()=>U,ld:()=>z,Wv:()=>I,UX:()=>E,Uw:()=>M,_I:()=>k,$n:()=>v,Q:()=>$});var n=r(23789),i=r(15018);r(85649);var o=r(36898),s=r(12115),l=r(63018),a=(0,n._r)({packageName:"@clerk/clerk-react"});function u(e){a.setMessages(e).setPackageName(e)}var[d,c]=(0,l.e3)("AuthContext"),p=l.ED,h=l.hQ,f="You've added multiple <ClerkProvider> components in your React component tree. Wrap your components in a single <ClerkProvider>.",g=e=>`You've passed multiple children components to <${e}/>. You can only pass a single child component or text.`,m="Unsupported usage of isSatellite, domain or proxyUrl. The usage of isSatellite, domain or proxyUrl as function is not supported in non-browser environments.",v="<UserProfile.Page /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",k="<UserProfile.Link /> component needs to be a direct child of `<UserProfile />` or `<UserButton />`.",y="<OrganizationProfile.Page /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",b="<OrganizationProfile.Link /> component needs to be a direct child of `<OrganizationProfile />` or `<OrganizationSwitcher />`.",_=e=>`<${e} /> can only accept <${e}.Page /> and <${e}.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.`,S=e=>`Missing props. <${e}.Page /> component requires the following props: url, label, labelIcon, alongside with children to be rendered inside the page.`,P=e=>`Missing props. <${e}.Link /> component requires the following props: url, label and labelIcon.`,w=e=>`The <${e}/> component uses path-based routing by default unless a different routing strategy is provided using the \`routing\` prop. When path-based routing is used, you need to provide the path where the component is mounted on by using the \`path\` prop. Example: <${e} path={'/my-path'} />`,j=e=>`The \`path\` prop will only be respected when the Clerk component uses path-based routing. To resolve this error, pass \`routing='path'\` to the <${e}/> component, or drop the \`path\` prop to switch to hash-based routing. For more details please refer to our docs: https://clerk.com/docs`,C="<UserButton /> can only accept <UserButton.UserProfilePage />, <UserButton.UserProfileLink /> and <UserButton.MenuItems /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",O="<UserButton.MenuItems /> component can only accept <UserButton.Action /> and <UserButton.Link /> as its children. Any other provided component will be ignored. Additionally, please ensure that the component is rendered in a client component.",E="<UserButton.MenuItems /> component needs to be a direct child of `<UserButton />`.",U="<UserButton.Action /> component needs to be a direct child of `<UserButton.MenuItems />`.",M="<UserButton.Link /> component needs to be a direct child of `<UserButton.MenuItems />`.",z="Missing props. <UserButton.Link /> component requires the following props: href, label and labelIcon.",I="Missing props. <UserButton.Action /> component requires the following props: label.",L=e=>{(0,l.Kz)(()=>{a.throwMissingClerkProviderError({source:e})})},A=e=>new Promise(t=>{let r=n=>{["ready","degraded"].includes(n)&&(t(),e.off("status",r))};e.on("status",r,{notify:!0})}),T=e=>async t=>(await A(e),e.session)?e.session.getToken(t):null,R=e=>async(...t)=>(await A(e),e.signOut(...t)),W=(e={})=>{var t,r;L("useAuth");let{treatPendingAsSignedOut:n,...i}=null!=e?e:{},l=c();void 0===l.sessionId&&void 0===l.userId&&(l=null!=i?i:{});let a=h(),u=(0,s.useCallback)(T(a),[a]),d=(0,s.useCallback)(R(a),[a]);return null==(t=a.telemetry)||t.record((0,o.FJ)("useAuth",{treatPendingAsSignedOut:n})),x({...l,getToken:u,signOut:d},{treatPendingAsSignedOut:null!=n?n:null==(r=a.__internal_getOption)?void 0:r.call(a,"treatPendingAsSignedOut")})};function x(e,{treatPendingAsSignedOut:t=!0}={}){let{userId:r,orgId:n,orgRole:o,has:l,signOut:u,getToken:d,orgPermissions:c,factorVerificationAge:p,sessionClaims:h}=null!=e?e:{},f=(0,s.useCallback)(e=>l?l(e):(0,i.MR)({userId:r,orgId:n,orgRole:o,orgPermissions:c,factorVerificationAge:p,features:(null==h?void 0:h.fea)||"",plans:(null==h?void 0:h.pla)||""})(e),[l,r,n,o,c,p]),g=(0,i.M2)({authObject:{...e,getToken:d,signOut:u,has:f},options:{treatPendingAsSignedOut:t}});return g||a.throw("Invalid state. Feel free to submit a bug or reach out to support here: https://clerk.com/support")}function D(e){let{startEmailLinkFlow:t,cancelEmailLinkFlow:r}=s.useMemo(()=>e.createEmailLinkFlow(),[e]);return s.useEffect(()=>r,[]),{startEmailLinkFlow:t,cancelEmailLinkFlow:r}}var N=()=>{var e;L("useSignIn");let t=h(),r=(0,l.WD)();return(null==(e=t.telemetry)||e.record((0,o.FJ)("useSignIn")),r)?{isLoaded:!0,signIn:r.signIn,setActive:t.setActive}:{isLoaded:!1,signIn:void 0,setActive:void 0}},F=()=>{var e;L("useSignUp");let t=h(),r=(0,l.WD)();return(null==(e=t.telemetry)||e.record((0,o.FJ)("useSignUp")),r)?{isLoaded:!0,signUp:r.signUp,setActive:t.setActive}:{isLoaded:!1,signUp:void 0,setActive:void 0}},$=(e,t)=>{let r=("string"==typeof t?t:null==t?void 0:t.component)||e.displayName||e.name||"Component";e.displayName=r;let n="string"==typeof t?void 0:t,i=t=>{L(r||"withClerk");let i=h();return i.loaded||(null==n?void 0:n.renderWhileLoading)?s.createElement(e,{...t,component:r,clerk:i}):null};return i.displayName=`withClerk(${r})`,i}},14129:(e,t,r)=>{r.d(t,{Fj:()=>o,MC:()=>i,b_:()=>n});var n=()=>!1,i=()=>!1,o=()=>{try{return!0}catch{}return!1}},15018:(e,t,r)=>{r.d(t,{D:()=>f,M2:()=>v,MR:()=>m});var n={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},i=new Set(["first_factor","second_factor","multi_factor"]),o=new Set(["strict_mfa","strict","moderate","lax"]),s=e=>"number"==typeof e&&e>0,l=e=>i.has(e),a=e=>o.has(e),u=e=>e.replace(/^(org:)*/,"org:"),d=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(u(e.permission)):e.role?u(n)===u(e.role):null:null},c=(e,t)=>{let{org:r,user:n}=h(e),[i,o]=t.split(":"),s=o||i;return"org"===i?r.includes(s):"user"===i?n.includes(s):[...r,...n].includes(s)},p=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?c(r,e.feature):e.plan&&n?c(n,e.plan):null},h=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},f=e=>{if(!e)return!1;let t="string"==typeof e&&a(e),r="object"==typeof e&&l(e.level)&&s(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?n[e]:e).bind(null,e)},g=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=f(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[o,s]=t,l=-1!==o?i>o:null,a=-1!==s?i>s:null;switch(n){case"first_factor":return l;case"second_factor":return -1!==s?a:l;case"multi_factor":return -1===s?l:l&&a}},m=e=>t=>{if(!e.userId)return!1;let r=p(t,e),n=d(t,e),i=g(t,e);return[r||n,i].some(e=>null===e)?[r||n,i].some(e=>!0===e):[r||n,i].every(e=>!0===e)},v=({authObject:{sessionId:e,sessionStatus:t,userId:r,actor:n,orgId:i,orgRole:o,orgSlug:s,signOut:l,getToken:a,has:u,sessionClaims:d},options:{treatPendingAsSignedOut:c=!0}})=>void 0===e&&void 0===r?{isLoaded:!1,isSignedIn:void 0,sessionId:e,sessionClaims:void 0,userId:r,actor:void 0,orgId:void 0,orgRole:void 0,orgSlug:void 0,has:void 0,signOut:l,getToken:a}:null===e&&null===r?{isLoaded:!0,isSignedIn:!1,sessionId:e,userId:r,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:c&&"pending"===t?{isLoaded:!0,isSignedIn:!1,sessionId:null,userId:null,sessionClaims:null,actor:null,orgId:null,orgRole:null,orgSlug:null,has:()=>!1,signOut:l,getToken:a}:e&&d&&r&&i&&o?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:i,orgRole:o,orgSlug:s||null,has:u,signOut:l,getToken:a}:e&&d&&r&&!i?{isLoaded:!0,isSignedIn:!0,sessionId:e,sessionClaims:d,userId:r,actor:n||null,orgId:null,orgRole:null,orgSlug:null,has:u,signOut:l,getToken:a}:void 0},22436:(e,t,r)=>{var n=r(12115),i="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=n.useState,s=n.useEffect,l=n.useLayoutEffect,a=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!i(e,r)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=o({inst:{value:r,getSnapshot:t}}),i=n[0].inst,d=n[1];return l(function(){i.value=r,i.getSnapshot=t,u(i)&&d({inst:i})},[e,r,t]),s(function(){return u(i)&&d({inst:i}),e(function(){u(i)&&d({inst:i})})},[e]),a(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:d},23789:(e,t,r)=>{r.d(t,{_r:()=>n._r});var n=r(92872);r(85649)},30852:(e,t,r)=>{r.d(t,{zz:()=>i});var n=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function i(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}n(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),n(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""})},36898:(e,t,r)=>{r.d(t,{FJ:()=>n.FJ,YF:()=>n.YF});var n=r(67937);r(30852),r(85649)},43877:(e,t,r)=>{function n(){return"undefined"!=typeof window}r.d(t,{M:()=>n}),RegExp("bot|spider|crawl|APIs-Google|AdsBot|Googlebot|mediapartners|Google Favicon|FeedFetcher|Google-Read-Aloud|DuplexWeb-Google|googleweblight|bing|yandex|baidu|duckduck|yahoo|ecosia|ia_archiver|facebook|instagram|pinterest|reddit|slack|twitter|whatsapp|youtube|semrush","i"),r(85649)},48416:(e,t,r)=>{r.d(t,{VK:()=>o,b_:()=>n.b_,Fj:()=>n.Fj,s2:()=>i});var n=r(14129),i=e=>{(0,n.b_)()&&console.error(`Clerk: ${e}`)};function o(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}r(85649)},48879:(e,t,r)=>{r.d(t,{Lq:()=>ea,B$:()=>c.B$,wF:()=>c.wF,lT:()=>c.lT,z0:()=>c.z0,A0:()=>c.A0,lJ:()=>eR,ul:()=>et,PQ:()=>eo,oE:()=>ei,nC:()=>ee,NC:()=>en,nm:()=>el,rm:()=>c.rm,m2:()=>c.m2,W5:()=>c.W5,mO:()=>c.mO,Xn:()=>c.Xn,eG:()=>c.eG,Ls:()=>$,hZ:()=>eE,M_:()=>eU,ct:()=>eM,Hx:()=>V,Ny:()=>ez,Rv:()=>eu,uF:()=>Y,Fv:()=>J,cP:()=>es,As:()=>p.As,ho:()=>p.ho,ui:()=>p.ui,Z5:()=>p.Z5,D_:()=>p.D_,Wp:()=>p.Wp,wV:()=>p.dy,g7:()=>p.g7,go:()=>p.go,yC:()=>p.yC,Jd:()=>p.Jd});var n,i,o,s,l,a,u,d,c=r(79419),p=r(9693),h=r(48416),f=r(12115),g=r(47650),m=(e,...t)=>{let r={...e};for(let e of t)delete r[e];return r};r(85649);var v=r(63018),k=e=>t=>{try{return f.Children.only(e)}catch{return p.sb.throw((0,p.Wq)(t))}},y=(e,t)=>(e||(e=t),"string"==typeof e&&(e=f.createElement("button",null,e)),e),b=e=>(...t)=>{if(e&&"function"==typeof e)return e(...t)},_=new Map,S=e=>{let[t,r]=(0,f.useState)(new Map);return e.map(e=>({id:e.id,mount:t=>r(r=>new Map(r).set(String(e.id),t)),unmount:()=>r(t=>{let r=new Map(t);return r.set(String(e.id),null),r}),portal:()=>{let r=t.get(String(e.id));return r?(0,g.createPortal)(e.component,r):null}}))},P=(e,t)=>!!e&&f.isValidElement(e)&&(null==e?void 0:e.type)===t,w=(e,t)=>O({children:e,reorderItemsLabels:["account","security"],LinkComponent:K,PageComponent:B,MenuItemsComponent:q,componentName:"UserProfile"},t),j=(e,t)=>O({children:e,reorderItemsLabels:["general","members"],LinkComponent:H,PageComponent:X,componentName:"OrganizationProfile"},t),C=e=>{let t=[],r=[H,X,q,B,K];return f.Children.forEach(e,e=>{r.some(t=>P(e,t))||t.push(e)}),t},O=(e,t)=>{let{children:r,LinkComponent:n,PageComponent:i,MenuItemsComponent:o,reorderItemsLabels:s,componentName:l}=e,{allowForAnyChildren:a=!1}=t||{},u=[];f.Children.forEach(r,e=>{if(!P(e,i)&&!P(e,n)&&!P(e,o)){e&&!a&&(0,h.s2)((0,p.n)(l));return}let{props:t}=e,{children:r,label:d,url:c,labelIcon:f}=t;if(P(e,i))if(E(t,s))u.push({label:d});else{if(!U(t))return void(0,h.s2)((0,p.sR)(l));u.push({label:d,labelIcon:f,children:r,url:c})}if(P(e,n))if(!M(t))return void(0,h.s2)((0,p.D)(l));else u.push({label:d,labelIcon:f,url:c})});let d=[],c=[],g=[];u.forEach((e,t)=>{if(U(e)){d.push({component:e.children,id:t}),c.push({component:e.labelIcon,id:t});return}M(e)&&g.push({component:e.labelIcon,id:t})});let m=S(d),v=S(c),k=S(g),y=[],b=[];return u.forEach((e,t)=>{if(E(e,s))return void y.push({label:e.label});if(U(e)){let{portal:r,mount:n,unmount:i}=m.find(e=>e.id===t),{portal:o,mount:s,unmount:l}=v.find(e=>e.id===t);y.push({label:e.label,url:e.url,mount:n,unmount:i,mountIcon:s,unmountIcon:l}),b.push(r),b.push(o);return}if(M(e)){let{portal:r,mount:n,unmount:i}=k.find(e=>e.id===t);y.push({label:e.label,url:e.url,mountIcon:n,unmountIcon:i}),b.push(r);return}}),{customPages:y,customPagesPortals:b}},E=(e,t)=>{let{children:r,label:n,url:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},U=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!!t&&!!n&&!!i&&!!r},M=e=>{let{children:t,label:r,url:n,labelIcon:i}=e;return!t&&!!n&&!!i&&!!r},z=e=>I({children:e,reorderItemsLabels:["manageAccount","signOut"],MenuItemsComponent:q,MenuActionComponent:G,MenuLinkComponent:Z,UserProfileLinkComponent:K,UserProfilePageComponent:B}),I=({children:e,MenuItemsComponent:t,MenuActionComponent:r,MenuLinkComponent:n,UserProfileLinkComponent:i,UserProfilePageComponent:o,reorderItemsLabels:s})=>{let l=[],a=[],u=[];f.Children.forEach(e,e=>{if(!P(e,t)&&!P(e,i)&&!P(e,o)){e&&(0,h.s2)(p.P6);return}if(P(e,i)||P(e,o))return;let{props:a}=e;f.Children.forEach(a.children,e=>{if(!P(e,r)&&!P(e,n)){e&&(0,h.s2)(p.wm);return}let{props:t}=e,{label:i,labelIcon:o,href:a,onClick:u,open:d}=t;if(P(e,r))if(L(t,s))l.push({label:i});else{if(!A(t))return void(0,h.s2)(p.Wv);let e={label:i,labelIcon:o};if(void 0!==u)l.push({...e,onClick:u});else{if(void 0===d)return void(0,h.s2)("Custom menu item must have either onClick or open property");l.push({...e,open:d.startsWith("/")?d:`/${d}`})}}if(P(e,n))if(!T(t))return void(0,h.s2)(p.ld);else l.push({label:i,labelIcon:o,href:a})})});let d=[],c=[];l.forEach((e,t)=>{A(e)&&d.push({component:e.labelIcon,id:t}),T(e)&&c.push({component:e.labelIcon,id:t})});let g=S(d),m=S(c);return l.forEach((e,t)=>{if(L(e,s)&&a.push({label:e.label}),A(e)){let{portal:r,mount:n,unmount:i}=g.find(e=>e.id===t),o={label:e.label,mountIcon:n,unmountIcon:i};"onClick"in e?o.onClick=e.onClick:"open"in e&&(o.open=e.open),a.push(o),u.push(r)}if(T(e)){let{portal:r,mount:n,unmount:i}=m.find(e=>e.id===t);a.push({label:e.label,href:e.href,mountIcon:n,unmountIcon:i}),u.push(r)}}),{customMenuItems:a,customMenuItemsPortals:u}},L=(e,t)=>{let{children:r,label:n,onClick:i,labelIcon:o}=e;return!r&&!i&&!o&&t.some(e=>e===n)},A=e=>{let{label:t,labelIcon:r,onClick:n,open:i}=e;return!!r&&!!t&&("function"==typeof n||"string"==typeof i)},T=e=>{let{label:t,href:r,labelIcon:n}=e;return!!r&&!!n&&!!t};function R(e){let t=(0,f.useRef)(),[r,n]=(0,f.useState)("rendering");return(0,f.useEffect)(()=>{if(!e)throw Error("Clerk: no component name provided, unable to detect mount.");"undefined"==typeof window||t.current||(t.current=(function(e){let{root:t=null==document?void 0:document.body,selector:r,timeout:n=0}=e;return new Promise((e,i)=>{if(!t)return void i(Error("No root element provided"));let o=t;if(r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)return void e();let s=new MutationObserver(n=>{for(let i of n)if("childList"===i.type&&(!o&&r&&(o=null==t?void 0:t.querySelector(r)),(null==o?void 0:o.childElementCount)&&o.childElementCount>0)){s.disconnect(),e();return}});s.observe(t,{childList:!0,subtree:!0}),n>0&&setTimeout(()=>{s.disconnect(),i(Error("Timeout waiting for element children"))},n)})})({selector:`[data-clerk-component="${e}"]`}).then(()=>{n("rendered")}).catch(()=>{n("error")}))},[e]),r}var W=e=>"mount"in e,x=e=>"open"in e,D=e=>null==e?void 0:e.map(({mountIcon:e,unmountIcon:t,...r})=>r),N=class extends f.PureComponent{constructor(){super(...arguments),this.rootRef=f.createRef()}componentDidUpdate(e){var t,r,n,i;if(!W(e)||!W(this.props))return;let o=m(e.props,"customPages","customMenuItems","children"),s=m(this.props.props,"customPages","customMenuItems","children"),l=(null==(t=o.customPages)?void 0:t.length)!==(null==(r=s.customPages)?void 0:r.length),a=(null==(n=o.customMenuItems)?void 0:n.length)!==(null==(i=s.customMenuItems)?void 0:i.length),u=D(e.props.customMenuItems),d=D(this.props.props.customMenuItems);(!(0,v.MZ)(o,s)||!(0,v.MZ)(u,d)||l||a)&&this.rootRef.current&&this.props.updateProps({node:this.rootRef.current,props:this.props.props})}componentDidMount(){this.rootRef.current&&(W(this.props)&&this.props.mount(this.rootRef.current,this.props.props),x(this.props)&&this.props.open(this.props.props))}componentWillUnmount(){this.rootRef.current&&(W(this.props)&&this.props.unmount(this.rootRef.current),x(this.props)&&this.props.close())}render(){let{hideRootHtmlElement:e=!1}=this.props,t={ref:this.rootRef,...this.props.rootProps,...this.props.component&&{"data-clerk-component":this.props.component}};return f.createElement(f.Fragment,null,!e&&f.createElement("div",{...t}),this.props.children)}},F=e=>{var t,r;return f.createElement(f.Fragment,null,null==(t=null==e?void 0:e.customPagesPortals)?void 0:t.map((e,t)=>(0,f.createElement)(e,{key:t})),null==(r=null==e?void 0:e.customMenuItemsPortals)?void 0:r.map((e,t)=>(0,f.createElement)(e,{key:t})))},$=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountSignIn,unmount:e.unmountSignIn,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"SignIn",renderWhileLoading:!0}),V=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountSignUp,unmount:e.unmountSignUp,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"SignUp",renderWhileLoading:!0});function B({children:e}){return(0,h.s2)(p.$n),f.createElement(f.Fragment,null,e)}function K({children:e}){return(0,h.s2)(p._I),f.createElement(f.Fragment,null,e)}var J=Object.assign((0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=w(n.children);return f.createElement(f.Fragment,null,i&&r,f.createElement(N,{component:t,mount:e.mountUserProfile,unmount:e.unmountUserProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:s},rootProps:o},f.createElement(F,{customPagesPortals:l})))},{component:"UserProfile",renderWhileLoading:!0}),{Page:B,Link:K}),Q=(0,f.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}});function q({children:e}){return(0,h.s2)(p.UX),f.createElement(f.Fragment,null,e)}function G({children:e}){return(0,h.s2)(p.aU),f.createElement(f.Fragment,null,e)}function Z({children:e}){return(0,h.s2)(p.Uw),f.createElement(f.Fragment,null,e)}var Y=Object.assign((0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=w(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),a=Object.assign(n.userProfileProps||{},{customPages:s}),{customMenuItems:u,customMenuItemsPortals:d}=z(n.children),c=C(n.children),p={mount:e.mountUserButton,unmount:e.unmountUserButton,updateProps:e.__unstable__updateProps,props:{...n,userProfileProps:a,customMenuItems:u}};return f.createElement(Q.Provider,{value:p},i&&r,e.loaded&&f.createElement(N,{component:t,...p,hideRootHtmlElement:!!n.__experimental_asProvider,rootProps:o},n.__experimental_asProvider?c:null,f.createElement(F,{customPagesPortals:l,customMenuItemsPortals:d})))},{component:"UserButton",renderWhileLoading:!0}),{UserProfilePage:B,UserProfileLink:K,MenuItems:q,Action:G,Link:Z,__experimental_Outlet:function(e){let t=(0,f.useContext)(Q),r={...t,props:{...t.props,...e}};return f.createElement(N,{...r})}});function X({children:e}){return(0,h.s2)(p.vb),f.createElement(f.Fragment,null,e)}function H({children:e}){return(0,h.s2)(p.kf),f.createElement(f.Fragment,null,e)}var ee=Object.assign((0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=j(n.children);return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountOrganizationProfile,unmount:e.unmountOrganizationProfile,updateProps:e.__unstable__updateProps,props:{...n,customPages:s},rootProps:o},f.createElement(F,{customPagesPortals:l})))},{component:"OrganizationProfile",renderWhileLoading:!0}),{Page:X,Link:H}),et=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountCreateOrganization,unmount:e.unmountCreateOrganization,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"CreateOrganization",renderWhileLoading:!0}),er=(0,f.createContext)({mount:()=>{},unmount:()=>{},updateProps:()=>{}}),en=Object.assign((0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}},{customPages:s,customPagesPortals:l}=j(n.children,{allowForAnyChildren:!!n.__experimental_asProvider}),a=Object.assign(n.organizationProfileProps||{},{customPages:s}),u=C(n.children),d={mount:e.mountOrganizationSwitcher,unmount:e.unmountOrganizationSwitcher,updateProps:e.__unstable__updateProps,props:{...n,organizationProfileProps:a},rootProps:o,component:t};return e.__experimental_prefetchOrganizationSwitcher(),f.createElement(er.Provider,{value:d},f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{...d,hideRootHtmlElement:!!n.__experimental_asProvider},n.__experimental_asProvider?u:null,f.createElement(F,{customPagesPortals:l}))))},{component:"OrganizationSwitcher",renderWhileLoading:!0}),{OrganizationProfilePage:X,OrganizationProfileLink:H,__experimental_Outlet:function(e){let t=(0,f.useContext)(er),r={...t,props:{...t.props,...e}};return f.createElement(N,{...r})}}),ei=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountOrganizationList,unmount:e.unmountOrganizationList,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"OrganizationList",renderWhileLoading:!0}),eo=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,open:e.openGoogleOneTap,close:e.closeGoogleOneTap,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"GoogleOneTap",renderWhileLoading:!0}),es=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountWaitlist,unmount:e.unmountWaitlist,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"Waitlist",renderWhileLoading:!0}),el=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountPricingTable,unmount:e.unmountPricingTable,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"PricingTable",renderWhileLoading:!0}),ea=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountApiKeys,unmount:e.unmountApiKeys,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"ApiKeys",renderWhileLoading:!0}),eu=(0,p.Q)(({clerk:e,component:t,fallback:r,...n})=>{let i="rendering"===R(t)||!e.loaded,o={...i&&r&&{style:{display:"none"}}};return f.createElement(f.Fragment,null,i&&r,e.loaded&&f.createElement(N,{component:t,mount:e.mountTaskSelectOrganization,unmount:e.unmountTaskSelectOrganization,updateProps:e.__unstable__updateProps,props:n,rootProps:o}))},{component:"TaskSelectOrganization",renderWhileLoading:!0}),ed=e=>{throw TypeError(e)},ec=(e,t,r)=>t.has(e)||ed("Cannot "+r),ep=(e,t,r)=>(ec(e,t,"read from private field"),r?r.call(e):t.get(e)),eh=(e,t,r)=>t.has(e)?ed("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ef=(e,t,r,n)=>(ec(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),eg=(e,t,r)=>(ec(e,t,"access private method"),r),em=r(62451),ev=r(76829),ek=(e,t,r)=>!e&&r?ey(r):eb(t),ey=e=>{let t=e.userId,r=e.user,n=e.sessionId,i=e.sessionStatus,o=e.sessionClaims,s=e.session,l=e.organization,a=e.orgId,u=e.orgRole,d=e.orgPermissions,c=e.orgSlug;return{userId:t,user:r,sessionId:n,session:s,sessionStatus:i,sessionClaims:o,organization:l,orgId:a,orgRole:u,orgPermissions:d,orgSlug:c,actor:e.actor,factorVerificationAge:e.factorVerificationAge}},eb=e=>{let t=e.user?e.user.id:e.user,r=e.user,n=e.session?e.session.id:e.session,i=e.session,o=e.session?.status,s=e.session?e.session.lastActiveToken?.jwt?.claims:null,l=e.session?e.session.factorVerificationAge:null,a=i?.actor,u=e.organization,d=e.organization?e.organization.id:e.organization,c=u?.slug,p=u?r?.organizationMemberships?.find(e=>e.organization.id===d):u,h=p?p.permissions:p;return{userId:t,user:r,sessionId:n,session:i,sessionStatus:o,sessionClaims:s,organization:u,orgId:d,orgRole:p?p.role:p,orgSlug:c,orgPermissions:h,actor:a,factorVerificationAge:l}},e_=r(43877),eS=(e,t,r,n,i)=>{let{notify:o}=i||{},s=e.get(r);s||(s=[],e.set(r,s)),s.push(n),o&&t.has(r)&&n(t.get(r))},eP=(e,t,r)=>(e.get(t)||[]).map(e=>e(r)),ew=(e,t,r)=>{let n=e.get(t);n&&(r?n.splice(n.indexOf(r)>>>0,1):e.set(t,[]))},ej=()=>{let e=new Map,t=new Map,r=new Map;return{on:(...r)=>eS(e,t,...r),prioritizedOn:(...e)=>eS(r,t,...e),emit:(n,i)=>{t.set(n,i),eP(r,n,i),eP(e,n,i)},off:(...t)=>ew(e,...t),prioritizedOff:(...e)=>ew(r,...e),internal:{retrieveListeners:t=>e.get(t)||[]}}},eC={Status:"status"},eO=()=>ej();"undefined"==typeof window||window.global||(window.global="undefined"==typeof global?window:global);var eE=(0,p.Q)(({clerk:e,children:t,...r})=>{let{signUpFallbackRedirectUrl:n,forceRedirectUrl:i,fallbackRedirectUrl:o,signUpForceRedirectUrl:s,mode:l,initialValues:a,withSignUp:u,oauthFlow:d,...c}=r,p=k(t=y(t,"Sign in"))("SignInButton"),h=()=>{let t={forceRedirectUrl:i,fallbackRedirectUrl:o,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:s,initialValues:a,withSignUp:u,oauthFlow:d};return"modal"===l?e.openSignIn({...t,appearance:r.appearance}):e.redirectToSignIn({...t,signInFallbackRedirectUrl:o,signInForceRedirectUrl:i})},g=async e=>(p&&"object"==typeof p&&"props"in p&&await b(p.props.onClick)(e),h()),m={...c,onClick:g};return f.cloneElement(p,m)},{component:"SignInButton",renderWhileLoading:!0}),eU=(0,p.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:n,...i}=r,o=k(t=y(t,"Sign in with Metamask"))("SignInWithMetamaskButton"),s=async()=>{!async function(){await e.authenticateWithMetamask({redirectUrl:n||void 0})}()},l=async e=>(await b(o.props.onClick)(e),s()),a={...i,onClick:l};return f.cloneElement(o,a)},{component:"SignInWithMetamask",renderWhileLoading:!0}),eM=(0,p.Q)(({clerk:e,children:t,...r})=>{let{redirectUrl:n="/",signOutOptions:i,...o}=r,s=k(t=y(t,"Sign out"))("SignOutButton"),l=()=>e.signOut({redirectUrl:n,...i}),a=async e=>(await b(s.props.onClick)(e),l()),u={...o,onClick:a};return f.cloneElement(s,u)},{component:"SignOutButton",renderWhileLoading:!0}),ez=(0,p.Q)(({clerk:e,children:t,...r})=>{let{fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,mode:l,initialValues:a,oauthFlow:u,...d}=r,c=k(t=y(t,"Sign up"))("SignUpButton"),p=()=>{let t={fallbackRedirectUrl:n,forceRedirectUrl:i,signInFallbackRedirectUrl:o,signInForceRedirectUrl:s,initialValues:a,oauthFlow:u};return"modal"===l?e.openSignUp({...t,appearance:r.appearance,unsafeMetadata:r.unsafeMetadata}):e.redirectToSignUp({...t,signUpFallbackRedirectUrl:n,signUpForceRedirectUrl:i})},h=async e=>(c&&"object"==typeof c&&"props"in c&&await b(c.props.onClick)(e),p()),g={...d,onClick:h};return f.cloneElement(c,g)},{component:"SignUpButton",renderWhileLoading:!0});void 0===globalThis.__BUILD_DISABLE_RHC__&&(globalThis.__BUILD_DISABLE_RHC__=!1);var eI={name:"@clerk/clerk-react",version:"5.39.0",environment:"production"},eL=class e{constructor(e){eh(this,u),this.clerkjs=null,this.preopenOneTap=null,this.preopenUserVerification=null,this.preopenSignIn=null,this.preopenCheckout=null,this.preopenPlanDetails=null,this.preopenSubscriptionDetails=null,this.preopenSignUp=null,this.preopenUserProfile=null,this.preopenOrganizationProfile=null,this.preopenCreateOrganization=null,this.preOpenWaitlist=null,this.premountSignInNodes=new Map,this.premountSignUpNodes=new Map,this.premountUserProfileNodes=new Map,this.premountUserButtonNodes=new Map,this.premountOrganizationProfileNodes=new Map,this.premountCreateOrganizationNodes=new Map,this.premountOrganizationSwitcherNodes=new Map,this.premountOrganizationListNodes=new Map,this.premountMethodCalls=new Map,this.premountWaitlistNodes=new Map,this.premountPricingTableNodes=new Map,this.premountApiKeysNodes=new Map,this.premountOAuthConsentNodes=new Map,this.premountTaskSelectOrganizationNodes=new Map,this.premountAddListenerCalls=new Map,this.loadedListeners=[],eh(this,n,"loading"),eh(this,i),eh(this,o),eh(this,s),eh(this,l,eO()),this.buildSignInUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignInUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignInUrl",t)},this.buildSignUpUrl=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildSignUpUrl(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildSignUpUrl",t)},this.buildAfterSignInUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignInUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignInUrl",t)},this.buildAfterSignUpUrl=(...e)=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildAfterSignUpUrl(...e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildAfterSignUpUrl",t)},this.buildAfterSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterSignOutUrl",e)},this.buildNewSubscriptionRedirectUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildNewSubscriptionRedirectUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildNewSubscriptionRedirectUrl",e)},this.buildAfterMultiSessionSingleSignOutUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildAfterMultiSessionSingleSignOutUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildAfterMultiSessionSingleSignOutUrl",e)},this.buildUserProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildUserProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildUserProfileUrl",e)},this.buildCreateOrganizationUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildCreateOrganizationUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildCreateOrganizationUrl",e)},this.buildOrganizationProfileUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildOrganizationProfileUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildOrganizationProfileUrl",e)},this.buildWaitlistUrl=()=>{let e=()=>{var e;return(null==(e=this.clerkjs)?void 0:e.buildWaitlistUrl())||""};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("buildWaitlistUrl",e)},this.buildUrlWithAuth=e=>{let t=()=>{var t;return(null==(t=this.clerkjs)?void 0:t.buildUrlWithAuth(e))||""};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("buildUrlWithAuth",t)},this.handleUnauthenticated=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.handleUnauthenticated()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("handleUnauthenticated",e)},this.on=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.on)return this.clerkjs.on(...e);ep(this,l).on(...e)},this.off=(...e)=>{var t;if(null==(t=this.clerkjs)?void 0:t.off)return this.clerkjs.off(...e);ep(this,l).off(...e)},this.addOnLoaded=e=>{this.loadedListeners.push(e),this.loaded&&this.emitLoaded()},this.emitLoaded=()=>{this.loadedListeners.forEach(e=>e()),this.loadedListeners=[]},this.beforeLoad=e=>{if(!e)throw Error("Failed to hydrate latest Clerk JS")},this.hydrateClerkJS=e=>{var t;if(!e)throw Error("Failed to hydrate latest Clerk JS");return this.clerkjs=e,this.premountMethodCalls.forEach(e=>e()),this.premountAddListenerCalls.forEach((t,r)=>{t.nativeUnsubscribe=e.addListener(r)}),null==(t=ep(this,l).internal.retrieveListeners("status"))||t.forEach(e=>{this.on("status",e,{notify:!0})}),null!==this.preopenSignIn&&e.openSignIn(this.preopenSignIn),null!==this.preopenCheckout&&e.__internal_openCheckout(this.preopenCheckout),null!==this.preopenPlanDetails&&e.__internal_openPlanDetails(this.preopenPlanDetails),null!==this.preopenSubscriptionDetails&&e.__internal_openSubscriptionDetails(this.preopenSubscriptionDetails),null!==this.preopenSignUp&&e.openSignUp(this.preopenSignUp),null!==this.preopenUserProfile&&e.openUserProfile(this.preopenUserProfile),null!==this.preopenUserVerification&&e.__internal_openReverification(this.preopenUserVerification),null!==this.preopenOneTap&&e.openGoogleOneTap(this.preopenOneTap),null!==this.preopenOrganizationProfile&&e.openOrganizationProfile(this.preopenOrganizationProfile),null!==this.preopenCreateOrganization&&e.openCreateOrganization(this.preopenCreateOrganization),null!==this.preOpenWaitlist&&e.openWaitlist(this.preOpenWaitlist),this.premountSignInNodes.forEach((t,r)=>{e.mountSignIn(r,t)}),this.premountSignUpNodes.forEach((t,r)=>{e.mountSignUp(r,t)}),this.premountUserProfileNodes.forEach((t,r)=>{e.mountUserProfile(r,t)}),this.premountUserButtonNodes.forEach((t,r)=>{e.mountUserButton(r,t)}),this.premountOrganizationListNodes.forEach((t,r)=>{e.mountOrganizationList(r,t)}),this.premountWaitlistNodes.forEach((t,r)=>{e.mountWaitlist(r,t)}),this.premountPricingTableNodes.forEach((t,r)=>{e.mountPricingTable(r,t)}),this.premountApiKeysNodes.forEach((t,r)=>{e.mountApiKeys(r,t)}),this.premountOAuthConsentNodes.forEach((t,r)=>{e.__internal_mountOAuthConsent(r,t)}),this.premountTaskSelectOrganizationNodes.forEach((t,r)=>{e.mountTaskSelectOrganization(r,t)}),void 0===this.clerkjs.status&&ep(this,l).emit(eC.Status,"ready"),this.emitLoaded(),this.clerkjs},this.__experimental_checkout=(...e)=>{var t;return null==(t=this.clerkjs)?void 0:t.__experimental_checkout(...e)},this.__unstable__updateProps=async e=>{let t=await eg(this,u,d).call(this);if(t&&"__unstable__updateProps"in t)return t.__unstable__updateProps(e)},this.__internal_navigateToTaskIfAvailable=async e=>this.clerkjs?this.clerkjs.__internal_navigateToTaskIfAvailable(e):Promise.reject(),this.setActive=e=>this.clerkjs?this.clerkjs.setActive(e):Promise.reject(),this.openSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignIn(e):this.preopenSignIn=e},this.closeSignIn=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignIn():this.preopenSignIn=null},this.__internal_openCheckout=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openCheckout(e):this.preopenCheckout=e},this.__internal_closeCheckout=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeCheckout():this.preopenCheckout=null},this.__internal_openPlanDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openPlanDetails(e):this.preopenPlanDetails=e},this.__internal_closePlanDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closePlanDetails():this.preopenPlanDetails=null},this.__internal_openSubscriptionDetails=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openSubscriptionDetails(e):this.preopenSubscriptionDetails=null!=e?e:null},this.__internal_closeSubscriptionDetails=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeSubscriptionDetails():this.preopenSubscriptionDetails=null},this.__internal_openReverification=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_openReverification(e):this.preopenUserVerification=e},this.__internal_closeReverification=()=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_closeReverification():this.preopenUserVerification=null},this.openGoogleOneTap=e=>{this.clerkjs&&this.loaded?this.clerkjs.openGoogleOneTap(e):this.preopenOneTap=e},this.closeGoogleOneTap=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeGoogleOneTap():this.preopenOneTap=null},this.openUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openUserProfile(e):this.preopenUserProfile=e},this.closeUserProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeUserProfile():this.preopenUserProfile=null},this.openOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.openOrganizationProfile(e):this.preopenOrganizationProfile=e},this.closeOrganizationProfile=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeOrganizationProfile():this.preopenOrganizationProfile=null},this.openCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.openCreateOrganization(e):this.preopenCreateOrganization=e},this.closeCreateOrganization=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeCreateOrganization():this.preopenCreateOrganization=null},this.openWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.openWaitlist(e):this.preOpenWaitlist=e},this.closeWaitlist=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeWaitlist():this.preOpenWaitlist=null},this.openSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.openSignUp(e):this.preopenSignUp=e},this.closeSignUp=()=>{this.clerkjs&&this.loaded?this.clerkjs.closeSignUp():this.preopenSignUp=null},this.mountSignIn=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignIn(e,t):this.premountSignInNodes.set(e,t)},this.unmountSignIn=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignIn(e):this.premountSignInNodes.delete(e)},this.mountSignUp=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountSignUp(e,t):this.premountSignUpNodes.set(e,t)},this.unmountSignUp=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountSignUp(e):this.premountSignUpNodes.delete(e)},this.mountUserProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserProfile(e,t):this.premountUserProfileNodes.set(e,t)},this.unmountUserProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserProfile(e):this.premountUserProfileNodes.delete(e)},this.mountOrganizationProfile=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationProfile(e,t):this.premountOrganizationProfileNodes.set(e,t)},this.unmountOrganizationProfile=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationProfile(e):this.premountOrganizationProfileNodes.delete(e)},this.mountCreateOrganization=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountCreateOrganization(e,t):this.premountCreateOrganizationNodes.set(e,t)},this.unmountCreateOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountCreateOrganization(e):this.premountCreateOrganizationNodes.delete(e)},this.mountOrganizationSwitcher=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationSwitcher(e,t):this.premountOrganizationSwitcherNodes.set(e,t)},this.unmountOrganizationSwitcher=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationSwitcher(e):this.premountOrganizationSwitcherNodes.delete(e)},this.__experimental_prefetchOrganizationSwitcher=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.__experimental_prefetchOrganizationSwitcher()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("__experimental_prefetchOrganizationSwitcher",e)},this.mountOrganizationList=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountOrganizationList(e,t):this.premountOrganizationListNodes.set(e,t)},this.unmountOrganizationList=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountOrganizationList(e):this.premountOrganizationListNodes.delete(e)},this.mountUserButton=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountUserButton(e,t):this.premountUserButtonNodes.set(e,t)},this.unmountUserButton=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountUserButton(e):this.premountUserButtonNodes.delete(e)},this.mountWaitlist=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountWaitlist(e,t):this.premountWaitlistNodes.set(e,t)},this.unmountWaitlist=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountWaitlist(e):this.premountWaitlistNodes.delete(e)},this.mountPricingTable=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountPricingTable(e,t):this.premountPricingTableNodes.set(e,t)},this.unmountPricingTable=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountPricingTable(e):this.premountPricingTableNodes.delete(e)},this.mountApiKeys=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountApiKeys(e,t):this.premountApiKeysNodes.set(e,t)},this.unmountApiKeys=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountApiKeys(e):this.premountApiKeysNodes.delete(e)},this.__internal_mountOAuthConsent=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_mountOAuthConsent(e,t):this.premountOAuthConsentNodes.set(e,t)},this.__internal_unmountOAuthConsent=e=>{this.clerkjs&&this.loaded?this.clerkjs.__internal_unmountOAuthConsent(e):this.premountOAuthConsentNodes.delete(e)},this.mountTaskSelectOrganization=(e,t)=>{this.clerkjs&&this.loaded?this.clerkjs.mountTaskSelectOrganization(e,t):this.premountTaskSelectOrganizationNodes.set(e,t)},this.unmountTaskSelectOrganization=e=>{this.clerkjs&&this.loaded?this.clerkjs.unmountTaskSelectOrganization(e):this.premountTaskSelectOrganizationNodes.delete(e)},this.addListener=e=>{if(this.clerkjs)return this.clerkjs.addListener(e);{let t=()=>{var t;let r=this.premountAddListenerCalls.get(e);r&&(null==(t=r.nativeUnsubscribe)||t.call(r),this.premountAddListenerCalls.delete(e))};return this.premountAddListenerCalls.set(e,{unsubscribe:t,nativeUnsubscribe:void 0}),t}},this.navigate=e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.navigate(e)};this.clerkjs&&this.loaded?t():this.premountMethodCalls.set("navigate",t)},this.redirectWithAuth=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectWithAuth(...e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectWithAuth",t)},this.redirectToSignIn=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignIn(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignIn",t)},this.redirectToSignUp=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.redirectToSignUp(e)};return this.clerkjs&&this.loaded?t():void this.premountMethodCalls.set("redirectToSignUp",t)},this.redirectToUserProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToUserProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToUserProfile",e)},this.redirectToAfterSignUp=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignUp()};if(this.clerkjs&&this.loaded)return e();this.premountMethodCalls.set("redirectToAfterSignUp",e)},this.redirectToAfterSignIn=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignIn()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignIn",e)},this.redirectToAfterSignOut=()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToAfterSignOut()};this.clerkjs&&this.loaded?e():this.premountMethodCalls.set("redirectToAfterSignOut",e)},this.redirectToOrganizationProfile=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToOrganizationProfile()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToOrganizationProfile",e)},this.redirectToCreateOrganization=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToCreateOrganization()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToCreateOrganization",e)},this.redirectToWaitlist=async()=>{let e=()=>{var e;return null==(e=this.clerkjs)?void 0:e.redirectToWaitlist()};return this.clerkjs&&this.loaded?e():void this.premountMethodCalls.set("redirectToWaitlist",e)},this.handleRedirectCallback=async e=>{var t;let r=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleRedirectCallback(e)};this.clerkjs&&this.loaded?null==(t=r())||t.catch(()=>{}):this.premountMethodCalls.set("handleRedirectCallback",r)},this.handleGoogleOneTapCallback=async(e,t)=>{var r;let n=()=>{var r;return null==(r=this.clerkjs)?void 0:r.handleGoogleOneTapCallback(e,t)};this.clerkjs&&this.loaded?null==(r=n())||r.catch(()=>{}):this.premountMethodCalls.set("handleGoogleOneTapCallback",n)},this.handleEmailLinkVerification=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.handleEmailLinkVerification(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("handleEmailLinkVerification",t)},this.authenticateWithMetamask=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithMetamask(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithMetamask",t)},this.authenticateWithCoinbaseWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithCoinbaseWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithCoinbaseWallet",t)},this.authenticateWithOKXWallet=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithOKXWallet(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithOKXWallet",t)},this.authenticateWithWeb3=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.authenticateWithWeb3(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("authenticateWithWeb3",t)},this.authenticateWithGoogleOneTap=async e=>(await eg(this,u,d).call(this)).authenticateWithGoogleOneTap(e),this.__internal_loadStripeJs=async()=>(await eg(this,u,d).call(this)).__internal_loadStripeJs(),this.createOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.createOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("createOrganization",t)},this.getOrganization=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.getOrganization(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("getOrganization",t)},this.joinWaitlist=async e=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.joinWaitlist(e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("joinWaitlist",t)},this.signOut=async(...e)=>{let t=()=>{var t;return null==(t=this.clerkjs)?void 0:t.signOut(...e)};if(this.clerkjs&&this.loaded)return t();this.premountMethodCalls.set("signOut",t)};let{Clerk:t=null,publishableKey:r}=e||{};ef(this,s,r),ef(this,o,null==e?void 0:e.proxyUrl),ef(this,i,null==e?void 0:e.domain),this.options=e,this.Clerk=t,this.mode=(0,e_.M)()?"browser":"server",this.options.sdkMetadata||(this.options.sdkMetadata=eI),ep(this,l).emit(eC.Status,"loading"),ep(this,l).prioritizedOn(eC.Status,e=>ef(this,n,e)),ep(this,s)&&this.loadClerkJS()}get publishableKey(){return ep(this,s)}get loaded(){var e;return(null==(e=this.clerkjs)?void 0:e.loaded)||!1}get status(){var e;return this.clerkjs?(null==(e=this.clerkjs)?void 0:e.status)||(this.clerkjs.loaded?"ready":"loading"):ep(this,n)}static getOrCreateInstance(t){return(0,e_.M)()&&ep(this,a)&&(!t.Clerk||ep(this,a).Clerk===t.Clerk)&&ep(this,a).publishableKey===t.publishableKey||ef(this,a,new e(t)),ep(this,a)}static clearInstance(){ef(this,a,null)}get domain(){return"undefined"!=typeof window&&window.location?(0,h.VK)(ep(this,i),new URL(window.location.href),""):"function"==typeof ep(this,i)?p.sb.throw(p.Vo):ep(this,i)||""}get proxyUrl(){return"undefined"!=typeof window&&window.location?(0,h.VK)(ep(this,o),new URL(window.location.href),""):"function"==typeof ep(this,o)?p.sb.throw(p.Vo):ep(this,o)||""}__internal_getOption(e){var t,r;return(null==(t=this.clerkjs)?void 0:t.__internal_getOption)?null==(r=this.clerkjs)?void 0:r.__internal_getOption(e):this.options[e]}get sdkMetadata(){var e;return(null==(e=this.clerkjs)?void 0:e.sdkMetadata)||this.options.sdkMetadata||void 0}get instanceType(){var e;return null==(e=this.clerkjs)?void 0:e.instanceType}get frontendApi(){var e;return(null==(e=this.clerkjs)?void 0:e.frontendApi)||""}get isStandardBrowser(){var e;return(null==(e=this.clerkjs)?void 0:e.isStandardBrowser)||this.options.standardBrowser||!1}get isSatellite(){return"undefined"!=typeof window&&window.location?(0,h.VK)(this.options.isSatellite,new URL(window.location.href),!1):"function"==typeof this.options.isSatellite&&p.sb.throw(p.Vo)}async loadClerkJS(){var e,t;if("browser"===this.mode&&!this.loaded){"undefined"!=typeof window&&(window.__clerk_publishable_key=ep(this,s),window.__clerk_proxy_url=this.proxyUrl,window.__clerk_domain=this.domain);try{if(this.Clerk){let e;(t=this.Clerk,"function"==typeof t)?(e=new this.Clerk(ep(this,s),{proxyUrl:this.proxyUrl,domain:this.domain}),this.beforeLoad(e),await e.load(this.options)):(e=this.Clerk).loaded||(this.beforeLoad(e),await e.load(this.options)),global.Clerk=e}else if(!__BUILD_DISABLE_RHC__){if(global.Clerk||await (0,em._R)({...this.options,publishableKey:ep(this,s),proxyUrl:this.proxyUrl,domain:this.domain,nonce:this.options.nonce}),!global.Clerk)throw Error("Failed to download latest ClerkJS. Contact <EMAIL>.");this.beforeLoad(global.Clerk),await global.Clerk.load(this.options)}if(null==(e=global.Clerk)?void 0:e.loaded)return this.hydrateClerkJS(global.Clerk);return}catch(e){ep(this,l).emit(eC.Status,"error"),console.error(e.stack||e.message||e);return}}}get version(){var e;return null==(e=this.clerkjs)?void 0:e.version}get client(){return this.clerkjs?this.clerkjs.client:void 0}get session(){return this.clerkjs?this.clerkjs.session:void 0}get user(){return this.clerkjs?this.clerkjs.user:void 0}get organization(){return this.clerkjs?this.clerkjs.organization:void 0}get telemetry(){return this.clerkjs?this.clerkjs.telemetry:void 0}get __unstable__environment(){return this.clerkjs?this.clerkjs.__unstable__environment:void 0}get isSignedIn(){return!!this.clerkjs&&this.clerkjs.isSignedIn}get billing(){var e;return null==(e=this.clerkjs)?void 0:e.billing}get apiKeys(){var e;return null==(e=this.clerkjs)?void 0:e.apiKeys}__unstable__setEnvironment(...e){this.clerkjs&&"__unstable__setEnvironment"in this.clerkjs&&this.clerkjs.__unstable__setEnvironment(e)}};function eA(e){let{isomorphicClerkOptions:t,initialState:r,children:n}=e,{isomorphicClerk:i,clerkStatus:o}=eT(t),[s,l]=f.useState({client:i.client,session:i.session,user:i.user,organization:i.organization});f.useEffect(()=>i.addListener(e=>l({...e})),[]);let a=ek(i.loaded,s,r),u=f.useMemo(()=>({value:i}),[o]),d=f.useMemo(()=>({value:s.client}),[s.client]),{sessionId:c,sessionStatus:h,sessionClaims:g,session:m,userId:k,user:y,orgId:b,actor:_,organization:S,orgRole:P,orgSlug:w,orgPermissions:j,factorVerificationAge:C}=a,O=f.useMemo(()=>({value:{sessionId:c,sessionStatus:h,sessionClaims:g,userId:k,actor:_,orgId:b,orgRole:P,orgSlug:w,orgPermissions:j,factorVerificationAge:C}}),[c,h,k,_,b,P,w,C,null==g?void 0:g.__raw]),E=f.useMemo(()=>({value:m}),[c,m]),U=f.useMemo(()=>({value:y}),[k,y]),M=f.useMemo(()=>({value:{organization:S}}),[b,S]);return f.createElement(p.SW.Provider,{value:u},f.createElement(v.pc.Provider,{value:d},f.createElement(v.IC.Provider,{value:E},f.createElement(v.TS,{...M.value},f.createElement(p.cy.Provider,{value:O},f.createElement(v.Rs.Provider,{value:U},f.createElement(v.Rx,{value:void 0},n)))))))}n=new WeakMap,i=new WeakMap,o=new WeakMap,s=new WeakMap,l=new WeakMap,a=new WeakMap,u=new WeakSet,d=function(){return new Promise(e=>{this.addOnLoaded(()=>e(this.clerkjs))})},eh(eL,a);var eT=e=>{let t=f.useRef(eL.getOrCreateInstance(e)),[r,n]=f.useState(t.current.status);return f.useEffect(()=>{t.current.__unstable__updateProps({appearance:e.appearance})},[e.appearance]),f.useEffect(()=>{t.current.__unstable__updateProps({options:e})},[e.localization]),f.useEffect(()=>(t.current.on("status",n),()=>{t.current&&t.current.off("status",n),eL.clearInstance()}),[]),{isomorphicClerk:t.current,clerkStatus:r}},eR=function(e,t,r){let n=e.displayName||e.name||t||"Component",i=n=>(!function(e,t,r=1){f.useEffect(()=>{let n=_.get(e)||0;return n==r?p.sb.throw(t):(_.set(e,n+1),()=>{_.set(e,(_.get(e)||1)-1)})},[])}(t,r),f.createElement(e,{...n}));return i.displayName=`withMaxAllowedInstancesGuard(${n})`,i}(function(e){let{initialState:t,children:r,__internal_bypassMissingPublishableKey:n,...i}=e,{publishableKey:o="",Clerk:s}=i;return s||n||(o?o&&!(0,ev.rA)(o)&&p.sb.throwInvalidPublishableKeyError({key:o}):p.sb.throwMissingPublishableKeyError()),f.createElement(eA,{initialState:t,isomorphicClerkOptions:i},r)},"ClerkProvider",p.yN);eR.displayName="ClerkProvider",(0,p.wV)({packageName:"@clerk/clerk-react"}),(0,em.kX)("@clerk/clerk-react")},49033:(e,t,r)=>{e.exports=r(22436)},62451:(e,t,r)=>{r.d(t,{T5:()=>w,nO:()=>P,_R:()=>S,kX:()=>y});var n=(e,t="5.80.0")=>{if(e)return e;let r=i(t);return r?"snapshot"===r?"5.80.0":r:o(t)},i=e=>e.trim().replace(/^v/,"").match(/-(.+?)(\.|$)/)?.[1],o=e=>e.trim().replace(/^v/,"").split(".")[0];function s(e){return e.startsWith("/")}var l=/\/$|\/\?|\/#/,a={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},u=async e=>new Promise(t=>setTimeout(t,e)),d=(e,t)=>t?e*(1+Math.random()):e,c=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=d(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await u(r()),t++}},p=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:o,factor:s,retryImmediately:l,jitter:p}={...a,...t},h=c({initialDelay:i,maxDelayBetweenRetries:o,factor:s,jitter:p});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;l&&1===r?await u(d(100,p)):await h()}};async function h(e="",t){let{async:r,defer:n,beforeLoad:i,crossOrigin:o,nonce:s}=t||{};return p(()=>new Promise((t,l)=>{e||l(Error("loadScript cannot be called without a src")),document&&document.body||l("loadScript cannot be called when document does not exist");let a=document.createElement("script");o&&a.setAttribute("crossorigin",o),a.async=r||!1,a.defer=n||!1,a.addEventListener("load",()=>{a.remove(),t(a)}),a.addEventListener("error",()=>{a.remove(),l()}),a.src=e,a.nonce=s,i?.(a),document.body.appendChild(a)}),{shouldRetry:(e,t)=>t<=5})}var f=r(92872),g=r(76829),m="Clerk: Failed to load Clerk",{isDevOrStagingUrl:v}=(0,g.RZ)(),k=(0,f._r)({packageName:"@clerk/shared"});function y(e){k.setPackageName({packageName:e})}function b(){if("undefined"==typeof window||!window.Clerk)return!1;let e=window.Clerk;return"object"==typeof e&&"function"==typeof e.load}function _(e){return new Promise((t,r)=>{let n=!1,i=(e,t)=>{clearTimeout(e),clearInterval(t)},o=()=>{!n&&b()&&(n=!0,i(s,l),t(null))},s=setTimeout(()=>{n||(n=!0,i(s,l),b()?t(null):r(Error(m)))},e);o();let l=setInterval(()=>{if(n)return void clearInterval(l);o()},100)})}var S=async e=>{let t=e?.scriptLoadTimeout??15e3;if(b())return null;if(document.querySelector("script[data-clerk-js-script]"))return _(t);if(!e?.publishableKey)return k.throwMissingPublishableKeyError(),null;let r=_(t);return h(P(e),{async:!0,crossOrigin:"anonymous",nonce:e.nonce,beforeLoad:j(e)}).catch(()=>{throw Error(m)}),r},P=e=>{let{clerkJSUrl:t,clerkJSVariant:r,clerkJSVersion:i,proxyUrl:o,domain:l,publishableKey:a}=e;if(t)return t;let u="";u=o&&function(e){var t;return!e||(t=e,/^http(s)?:\/\//.test(t||""))||s(e)}(o)?(function(e){return e?s(e)?new URL(e,window.location.origin).toString():e:""})(o).replace(/http(s)?:\/\//,""):l&&!v((0,g.q5)(a)?.frontendApi||"")?function(e){let t;if(!e)return"";if(e.match(/^(clerk\.)+\w*$/))t=/(clerk\.)*(?=clerk\.)/;else{if(e.match(/\.clerk.accounts/))return e;t=/^(clerk\.)*/gi}let r=e.replace(t,"");return`clerk.${r}`}(l):(0,g.q5)(a)?.frontendApi||"";let d=r?`${r.replace(/\.+$/,"")}.`:"",c=n(i);return`https://${u}/npm/@clerk/clerk-js@${c}/dist/clerk.${d}browser.js`},w=e=>{let t={};return e.publishableKey&&(t["data-clerk-publishable-key"]=e.publishableKey),e.proxyUrl&&(t["data-clerk-proxy-url"]=e.proxyUrl),e.domain&&(t["data-clerk-domain"]=e.domain),e.nonce&&(t.nonce=e.nonce),t},j=e=>t=>{let r=w(e);for(let e in r)t.setAttribute(e,r[e])};r(85649)},63018:(e,t,r)=>{let n,i;r.d(t,{ED:()=>eZ,pc:()=>e0,TS:()=>e7,IC:()=>e2,Rs:()=>eX,Rx:()=>e9,e3:()=>eq,MZ:()=>tv,Kz:()=>te,ho:()=>tm,hQ:()=>eY,WD:()=>e1,Z5:()=>ts,D_:()=>ta,Wp:()=>ty,wV:()=>tc,g7:()=>th,Jd:()=>tg});var o={};r.r(o),r.d(o,{SWRConfig:()=>eE,default:()=>eU,mutate:()=>er,preload:()=>ec,unstable_serialize:()=>ej,useSWRConfig:()=>ed});var s=r(67937),l=(...e)=>{},a=()=>{let e=l,t=l;return{promise:new Promise((r,n)=>{e=r,t=n}),resolve:e,reject:t}};r(30852);var u=r(92872),d="reverification-error",c=e=>({clerk_error:{type:"forbidden",reason:d,metadata:{reverification:e}}}),p=e=>e&&"object"==typeof e&&"clerk_error"in e&&e.clerk_error?.type==="forbidden"&&e.clerk_error?.reason===d,h=r(15018),f=r(85649),g=r(12115),m=r(49033),v=Object.prototype.hasOwnProperty;let k=new WeakMap,y=()=>{},b=y(),_=Object,S=e=>e===b,P=e=>"function"==typeof e,w=(e,t)=>({...e,...t}),j=e=>P(e.then),C={},O={},E="undefined",U=typeof window!=E,M=typeof document!=E,z=U&&"Deno"in window,I=()=>U&&typeof window.requestAnimationFrame!=E,L=(e,t)=>{let r=k.get(e);return[()=>!S(t)&&e.get(t)||C,n=>{if(!S(t)){let i=e.get(t);t in O||(O[t]=i),r[5](t,w(i,n),i||C)}},r[6],()=>!S(t)&&t in O?O[t]:!S(t)&&e.get(t)||C]},A=!0,[T,R]=U&&window.addEventListener?[window.addEventListener.bind(window),window.removeEventListener.bind(window)]:[y,y],W={initFocus:e=>(M&&document.addEventListener("visibilitychange",e),T("focus",e),()=>{M&&document.removeEventListener("visibilitychange",e),R("focus",e)}),initReconnect:e=>{let t=()=>{A=!0,e()},r=()=>{A=!1};return T("online",t),T("offline",r),()=>{R("online",t),R("offline",r)}}},x=!g.useId,D=!U||z,N=e=>I()?window.requestAnimationFrame(e):setTimeout(e,1),F=D?g.useEffect:g.useLayoutEffect,$="undefined"!=typeof navigator&&navigator.connection,V=!D&&$&&(["slow-2g","2g"].includes($.effectiveType)||$.saveData),B=new WeakMap,K=e=>_.prototype.toString.call(e),J=(e,t)=>e==="[object ".concat(t,"]"),Q=0,q=e=>{let t,r,n=typeof e,i=K(e),o=J(i,"Date"),s=J(i,"RegExp"),l=J(i,"Object");if(_(e)!==e||o||s)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=B.get(e))return t;if(t=++Q+"~",B.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=q(e[r])+",";B.set(e,t)}if(l){t="#";let n=_.keys(e).sort();for(;!S(r=n.pop());)S(e[r])||(t+=r+":"+q(e[r])+",");B.set(e,t)}}return t},G=e=>{if(P(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?q(e):"",t]},Z=0,Y=()=>++Z;async function X(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[n,i,o,s]=t,l=w({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),a=l.populateCache,u=l.rollbackOnError,d=l.optimisticData,c=e=>"function"==typeof u?u(e):!1!==u,p=l.throwOnError;if(P(i)){let e=[];for(let t of n.keys())!/^\$(inf|sub)\$/.test(t)&&i(n.get(t)._k)&&e.push(t);return Promise.all(e.map(h))}return h(i);async function h(e){let r,[i]=G(e);if(!i)return;let[s,u]=L(n,i),[h,f,g,m]=k.get(n),v=()=>{let t=h[i];return(P(l.revalidate)?l.revalidate(s().data,e):!1!==l.revalidate)&&(delete g[i],delete m[i],t&&t[0])?t[0](2).then(()=>s().data):s().data};if(t.length<3)return v();let y=o,_=!1,w=Y();f[i]=[w,0];let C=!S(d),O=s(),E=O.data,U=O._c,M=S(U)?E:U;if(C&&u({data:d=P(d)?d(M,E):d,_c:M}),P(y))try{y=y(M)}catch(e){r=e,_=!0}if(y&&j(y)){if(y=await y.catch(e=>{r=e,_=!0}),w!==f[i][0]){if(_)throw r;return y}_&&C&&c(r)&&(a=!0,u({data:M,_c:b}))}if(a&&!_&&(P(a)?u({data:a(y,M),error:b,_c:b}):u({data:y,error:b,_c:b})),f[i][1]=Y(),Promise.resolve(v()).then(()=>{u({_c:b})}),_){if(p)throw r;return}return y}}let H=(e,t)=>{for(let r in e)e[r][0]&&e[r][0](t)},ee=(e,t)=>{if(!k.has(e)){let r=w(W,t),n=Object.create(null),i=X.bind(b,e),o=y,s=Object.create(null),l=(e,t)=>{let r=s[e]||[];return s[e]=r,r.push(t),()=>r.splice(r.indexOf(t),1)},a=(t,r,n)=>{e.set(t,r);let i=s[t];if(i)for(let e of i)e(r,n)},u=()=>{if(!k.has(e)&&(k.set(e,[n,Object.create(null),Object.create(null),Object.create(null),i,a,l]),!D)){let t=r.initFocus(setTimeout.bind(b,H.bind(b,n,0))),i=r.initReconnect(setTimeout.bind(b,H.bind(b,n,1)));o=()=>{t&&t(),i&&i(),k.delete(e)}}};return u(),[e,i,u,o]}return[e,k.get(e)[4]]},[et,er]=ee(new Map),en=w({onLoadingSlow:y,onSuccess:y,onError:y,onErrorRetry:(e,t,r,n,i)=>{let o=r.errorRetryCount,s=i.retryCount,l=~~((Math.random()+.5)*(1<<(s<8?s:8)))*r.errorRetryInterval;(S(o)||!(s>o))&&setTimeout(n,l,i)},onDiscarded:y,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:V?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:V?5e3:3e3,compare:function e(t,r){var n,i;if(t===r)return!0;if(t&&r&&(n=t.constructor)===r.constructor){if(n===Date)return t.getTime()===r.getTime();if(n===RegExp)return t.toString()===r.toString();if(n===Array){if((i=t.length)===r.length)for(;i--&&e(t[i],r[i]););return -1===i}if(!n||"object"==typeof t){for(n in i=0,t)if(v.call(t,n)&&++i&&!v.call(r,n)||!(n in r)||!e(t[n],r[n]))return!1;return Object.keys(r).length===i}}return t!=t&&r!=r},isPaused:()=>!1,cache:et,mutate:er,fallback:{}},{isOnline:()=>A,isVisible:()=>{let e=M&&document.visibilityState;return S(e)||"hidden"!==e}}),ei=(e,t)=>{let r=w(e,t);if(t){let{use:n,fallback:i}=e,{use:o,fallback:s}=t;n&&o&&(r.use=n.concat(o)),i&&s&&(r.fallback=w(i,s))}return r},eo=(0,g.createContext)({}),es="$inf$",el=U&&window.__SWR_DEVTOOLS_USE__,ea=el?window.__SWR_DEVTOOLS_USE__:[],eu=e=>P(e[1])?[e[0],e[1],e[2]||{}]:[e[0],null,(null===e[1]?e[2]:e[1])||{}],ed=()=>w(en,(0,g.useContext)(eo)),ec=(e,t)=>{let[r,n]=G(e),[,,,i]=k.get(et);if(i[r])return i[r];let o=t(n);return i[r]=o,o},ep=ea.concat(e=>(t,r,n)=>{let i=r&&((...e)=>{let[n]=G(t),[,,,i]=k.get(et);if(n.startsWith(es))return r(...e);let o=i[n];return S(o)?r(...e):(delete i[n],o)});return e(t,i,n)}),eh=(e,t,r)=>{let n=t[e]||(t[e]=[]);return n.push(r),()=>{let e=n.indexOf(r);e>=0&&(n[e]=n[n.length-1],n.pop())}};el&&(window.__SWR_DEVTOOLS_REACT__=g);let ef=()=>{},eg=ef(),em=Object,ev=e=>e===eg,ek=e=>"function"==typeof e,ey=new WeakMap,eb=e=>em.prototype.toString.call(e),e_=(e,t)=>e===`[object ${t}]`,eS=0,eP=e=>{let t,r,n=typeof e,i=eb(e),o=e_(i,"Date"),s=e_(i,"RegExp"),l=e_(i,"Object");if(em(e)!==e||o||s)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=ey.get(e))return t;if(t=++eS+"~",ey.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=eP(e[r])+",";ey.set(e,t)}if(l){t="#";let n=em.keys(e).sort();for(;!ev(r=n.pop());)ev(e[r])||(t+=r+":"+eP(e[r])+",");ey.set(e,t)}}return t},ew=e=>{if(ek(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eP(e):"",t]},ej=e=>ew(e)[0],eC=g.use||(e=>{switch(e.status){case"pending":throw e;case"fulfilled":return e.value;case"rejected":throw e.reason;default:throw e.status="pending",e.then(t=>{e.status="fulfilled",e.value=t},t=>{e.status="rejected",e.reason=t}),e}}),eO={dedupe:!0},eE=_.defineProperty(e=>{let{value:t}=e,r=(0,g.useContext)(eo),n=P(t),i=(0,g.useMemo)(()=>n?t(r):t,[n,r,t]),o=(0,g.useMemo)(()=>n?i:ei(r,i),[n,r,i]),s=i&&i.provider,l=(0,g.useRef)(b);s&&!l.current&&(l.current=ee(s(o.cache||et),i));let a=l.current;return a&&(o.cache=a[0],o.mutate=a[1]),F(()=>{if(a)return a[2]&&a[2](),a[3]},[]),(0,g.createElement)(eo.Provider,w(e,{value:o}))},"defaultValue",{value:en}),eU=(n=(e,t,r)=>{let{cache:n,compare:i,suspense:o,fallbackData:s,revalidateOnMount:l,revalidateIfStale:a,refreshInterval:u,refreshWhenHidden:d,refreshWhenOffline:c,keepPreviousData:p}=r,[h,f,v,y]=k.get(n),[_,C]=G(e),O=(0,g.useRef)(!1),E=(0,g.useRef)(!1),U=(0,g.useRef)(_),M=(0,g.useRef)(t),z=(0,g.useRef)(r),I=()=>z.current,A=()=>I().isVisible()&&I().isOnline(),[T,R,W,$]=L(n,_),V=(0,g.useRef)({}).current,B=S(s)?S(r.fallback)?b:r.fallback[_]:s,K=(e,t)=>{for(let r in V)if("data"===r){if(!i(e[r],t[r])&&(!S(e[r])||!i(en,t[r])))return!1}else if(t[r]!==e[r])return!1;return!0},J=(0,g.useMemo)(()=>{let e=!!_&&!!t&&(S(l)?!I().isPaused()&&!o&&!1!==a:l),r=t=>{let r=w(t);return(delete r._k,e)?{isValidating:!0,isLoading:!0,...r}:r},n=T(),i=$(),s=r(n),u=n===i?s:r(i),d=s;return[()=>{let e=r(T());return K(e,d)?(d.data=e.data,d.isLoading=e.isLoading,d.isValidating=e.isValidating,d.error=e.error,d):(d=e,e)},()=>u]},[n,_]),Q=(0,m.useSyncExternalStore)((0,g.useCallback)(e=>W(_,(t,r)=>{K(r,t)||e()}),[n,_]),J[0],J[1]),q=!O.current,Z=h[_]&&h[_].length>0,H=Q.data,ee=S(H)?B&&j(B)?eC(B):B:H,et=Q.error,er=(0,g.useRef)(ee),en=p?S(H)?S(er.current)?ee:er.current:H:ee,ei=(!Z||!!S(et))&&(q&&!S(l)?l:!I().isPaused()&&(o?!S(ee)&&a:S(ee)||a)),eo=!!(_&&t&&q&&ei),es=S(Q.isValidating)?eo:Q.isValidating,el=S(Q.isLoading)?eo:Q.isLoading,ea=(0,g.useCallback)(async e=>{let t,n,o=M.current;if(!_||!o||E.current||I().isPaused())return!1;let s=!0,l=e||{},a=!v[_]||!l.dedupe,u=()=>x?!E.current&&_===U.current&&O.current:_===U.current,d={isValidating:!1,isLoading:!1},c=()=>{R(d)},p=()=>{let e=v[_];e&&e[1]===n&&delete v[_]},g={isValidating:!0};S(T().data)&&(g.isLoading=!0);try{if(a&&(R(g),r.loadingTimeout&&S(T().data)&&setTimeout(()=>{s&&u()&&I().onLoadingSlow(_,r)},r.loadingTimeout),v[_]=[o(C),Y()]),[t,n]=v[_],t=await t,a&&setTimeout(p,r.dedupingInterval),!v[_]||v[_][1]!==n)return a&&u()&&I().onDiscarded(_),!1;d.error=b;let e=f[_];if(!S(e)&&(n<=e[0]||n<=e[1]||0===e[1]))return c(),a&&u()&&I().onDiscarded(_),!1;let l=T().data;d.data=i(l,t)?l:t,a&&u()&&I().onSuccess(t,_,r)}catch(r){p();let e=I(),{shouldRetryOnError:t}=e;!e.isPaused()&&(d.error=r,a&&u()&&(e.onError(r,_,e),(!0===t||P(t)&&t(r))&&(!I().revalidateOnFocus||!I().revalidateOnReconnect||A())&&e.onErrorRetry(r,_,e,e=>{let t=h[_];t&&t[0]&&t[0](3,e)},{retryCount:(l.retryCount||0)+1,dedupe:!0})))}return s=!1,c(),!0},[_,n]),eu=(0,g.useCallback)((...e)=>X(n,U.current,...e),[]);if(F(()=>{M.current=t,z.current=r,S(H)||(er.current=H)}),F(()=>{if(!_)return;let e=ea.bind(b,eO),t=0;I().revalidateOnFocus&&(t=Date.now()+I().focusThrottleInterval);let r=eh(_,h,(r,n={})=>{if(0==r){let r=Date.now();I().revalidateOnFocus&&r>t&&A()&&(t=r+I().focusThrottleInterval,e())}else if(1==r)I().revalidateOnReconnect&&A()&&e();else if(2==r)return ea();else if(3==r)return ea(n)});return E.current=!1,U.current=_,O.current=!0,R({_k:C}),ei&&!v[_]&&(S(ee)||D?e():N(e)),()=>{E.current=!0,r()}},[_]),F(()=>{let e;function t(){let t=P(u)?u(T().data):u;t&&-1!==e&&(e=setTimeout(r,t))}function r(){!T().error&&(d||I().isVisible())&&(c||I().isOnline())?ea(eO).then(t):t()}return t(),()=>{e&&(clearTimeout(e),e=-1)}},[u,d,c,_]),(0,g.useDebugValue)(en),o&&S(ee)&&_){if(!x&&D)throw Error("Fallback data is required when using Suspense in SSR.");M.current=t,z.current=r,E.current=!1;let e=y[_];if(S(e)||eC(eu(e)),S(et)){let e=ea(eO);S(en)||(e.status="fulfilled",e.value=!0),eC(e)}else throw et}return{mutate:eu,get data(){return V.data=!0,en},get error(){return V.error=!0,et},get isValidating(){return V.isValidating=!0,es},get isLoading(){return V.isLoading=!0,el}}},function(...e){let t=ed(),[r,i,o]=eu(e),s=ei(t,o),l=n,{use:a}=s,u=(a||[]).concat(ep);for(let e=u.length;e--;)l=u[e](l);return l(r,i||s.fetcher||null,s)}),eM=()=>{},ez=eM(),eI=Object,eL=e=>e===ez,eA=e=>"function"==typeof e,eT=new WeakMap,eR=e=>eI.prototype.toString.call(e),eW=(e,t)=>e===`[object ${t}]`,ex=0,eD=e=>{let t,r,n=typeof e,i=eR(e),o=eW(i,"Date"),s=eW(i,"RegExp"),l=eW(i,"Object");if(eI(e)!==e||o||s)t=o?e.toJSON():"symbol"==n?e.toString():"string"==n?JSON.stringify(e):""+e;else{if(t=eT.get(e))return t;if(t=++ex+"~",eT.set(e,t),Array.isArray(e)){for(r=0,t="@";r<e.length;r++)t+=eD(e[r])+",";eT.set(e,t)}if(l){t="#";let n=eI.keys(e).sort();for(;!eL(r=n.pop());)eL(e[r])||(t+=r+":"+eD(e[r])+",");eT.set(e,t)}}return t},eN=e=>{if(eA(e))try{e=e()}catch(t){e=""}let t=e;return[e="string"==typeof e?e:(Array.isArray(e)?e.length:e)?eD(e):"",t]},eF=e=>eN(e?e(0,null):null)[0],e$=Promise.resolve(),eV=(i=e=>(t,r,n)=>{let i,o=(0,g.useRef)(!1),{cache:s,initialSize:l=1,revalidateAll:a=!1,persistSize:u=!1,revalidateFirstPage:d=!0,revalidateOnMount:c=!1,parallel:p=!1}=n,[,,,h]=k.get(et);try{(i=eF(t))&&(i=es+i)}catch(e){}let[f,v,y]=L(s,i),_=(0,g.useCallback)(()=>S(f()._l)?l:f()._l,[s,i,l]);(0,m.useSyncExternalStore)((0,g.useCallback)(e=>i?y(i,()=>{e()}):()=>{},[s,i]),_,_);let w=(0,g.useCallback)(()=>{let e=f()._l;return S(e)?l:e},[i,l]),j=(0,g.useRef)(w());F(()=>{if(!o.current){o.current=!0;return}i&&v({_l:u?j.current:w()})},[i,s]);let C=c&&!o.current,O=e(i,async e=>{let i=f()._i,o=f()._r;v({_r:b});let l=[],u=w(),[c]=L(s,e),g=c().data,m=[],k=null;for(let e=0;e<u;++e){let[u,c]=G(t(e,p?null:k));if(!u)break;let[f,v]=L(s,u),y=f().data,b=a||i||S(y)||d&&!e&&!S(g)||C||g&&!S(g[e])&&!n.compare(g[e],y);if(r&&("function"==typeof o?o(y,c):b)){let t=async()=>{if(u in h){let e=h[u];delete h[u],y=await e}else y=await r(c);v({data:y,_k:c}),l[e]=y};p?m.push(t):await t()}else l[e]=y;p||(k=y)}return p&&await Promise.all(m.map(e=>e())),v({_i:b}),l},n),E=(0,g.useCallback)(function(e,t){let r="boolean"==typeof t?{revalidate:t}:t||{},n=!1!==r.revalidate;return i?(n&&(S(e)?v({_i:!0,_r:r.revalidate}):v({_i:!1,_r:r.revalidate})),arguments.length?O.mutate(e,{...r,revalidate:n}):O.mutate()):e$},[i,s]),U=(0,g.useCallback)(e=>{let r;if(!i)return e$;let[,n]=L(s,i);if(P(e)?r=e(w()):"number"==typeof e&&(r=e),"number"!=typeof r)return e$;n({_l:r}),j.current=r;let o=[],[l]=L(s,i),a=null;for(let e=0;e<r;++e){let[r]=G(t(e,a)),[n]=L(s,r),i=r?n().data:b;if(S(i))return E(l().data);o.push(i),a=i}return E(o)},[i,s,E,w]);return{size:w(),setSize:U,mutate:E,get data(){return O.data},get error(){return O.error},get isValidating(){return O.isValidating},get isLoading(){return O.isLoading}}},(...e)=>{let[t,r,n]=eu(e),o=(n.use||[]).concat(i);return eU(t,r,{...n,use:o})});var eB=Object.prototype.hasOwnProperty;function eK(e,t,r){for(r of e.keys())if(eJ(r,t))return r}function eJ(e,t){var r,n,i;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((n=e.length)===t.length)for(;n--&&eJ(e[n],t[n]););return -1===n}if(r===Set){if(e.size!==t.size)return!1;for(n of e)if((i=n)&&"object"==typeof i&&!(i=eK(t,i))||!t.has(i))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(n of e)if((i=n[0])&&"object"==typeof i&&!(i=eK(t,i))||!eJ(n[1],t.get(i)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((n=e.byteLength)===t.byteLength)for(;n--&&e.getInt8(n)===t.getInt8(n););return -1===n}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===t.byteLength)for(;n--&&e[n]===t[n];);return -1===n}if(!r||"object"==typeof e){for(r in n=0,e)if(eB.call(e,r)&&++n&&!eB.call(t,r)||!(r in t)||!eJ(e[r],t[r]))return!1;return Object.keys(t).length===n}}return e!=e&&t!=t}function eQ(e,t){if(!e)throw"string"==typeof t?Error(t):Error(`${t.displayName} not found`)}var eq=(e,t)=>{let{assertCtxFn:r=eQ}=t||{},n=g.createContext(void 0);return n.displayName=e,[n,()=>{let t=g.useContext(n);return r(t,`${e} not found`),t.value},()=>{let e=g.useContext(n);return e?e.value:{}}]},eG={};(0,f.VA)(eG,{useSWR:()=>eU,useSWRInfinite:()=>eV}),(0,f.ie)(eG,o);var[eZ,eY]=eq("ClerkInstanceContext"),[eX,eH]=eq("UserContext"),[e0,e1]=eq("ClientContext"),[e2,e5]=eq("SessionContext"),[e8,e3]=(g.createContext({}),eq("CheckoutContext")),e9=({children:e,...t})=>g.createElement(e8.Provider,{value:{value:t}},e),[e6,e4]=eq("OrganizationContext"),e7=({children:e,organization:t,swrConfig:r})=>g.createElement(eG.SWRConfig,{value:r},g.createElement(e6.Provider,{value:{value:{organization:t}}},e));function te(e){if(!g.useContext(eZ)){if("function"==typeof e)return void e();throw Error(`${e} can only be used within the <ClerkProvider /> component.

Possible fixes:
1. Ensure that the <ClerkProvider /> is correctly wrapping your application where this component is used.
2. Check for multiple versions of the \`@clerk/shared\` package in your project. Use a tool like \`npm ls @clerk/shared\` to identify multiple versions, and update your dependencies to only rely on one.

Learn more: https://clerk.com/docs/components/clerk-provider`.trim())}}function tt(e,t){let r=new Set(Object.keys(t)),n={};for(let t of Object.keys(e))r.has(t)||(n[t]=e[t]);return n}var tr=(e,t)=>{let r="boolean"==typeof e&&e,n=(0,g.useRef)(r?t.initialPage:e?.initialPage??t.initialPage),i=(0,g.useRef)(r?t.pageSize:e?.pageSize??t.pageSize),o={};for(let n of Object.keys(t))o[n]=r?t[n]:e?.[n]??t[n];return{...o,initialPage:n.current,pageSize:i.current}},tn={dedupingInterval:6e4,focusThrottleInterval:12e4},ti=(e,t,r,n)=>{let[i,o]=(0,g.useState)(e.initialPage??1),s=(0,g.useRef)(e.initialPage??1),l=(0,g.useRef)(e.pageSize??10),a=r.enabled??!0,u="cache"===r.__experimental_mode,d=r.infinite??!1,c=r.keepPreviousData??!1,p={...n,...e,initialPage:i,pageSize:l.current},{data:h,isValidating:f,isLoading:m,error:v,mutate:k}=eU(!d&&a&&(u||t)?p:null,!u&&t?r=>{let i=tt(r,n);return t({...e,...i})}:null,{keepPreviousData:c,...tn}),{data:y,isLoading:b,isValidating:_,error:S,size:P,setSize:w,mutate:j}=eV(t=>d&&a?{...e,...n,initialPage:s.current+t,pageSize:l.current}:null,e=>{let r=tt(e,n);return t?.(r)},tn),C=(0,g.useMemo)(()=>d?P:i,[d,P,i]),O=(0,g.useCallback)(e=>d?void w(e):o(e),[w]),E=(0,g.useMemo)(()=>d?y?.map(e=>e?.data).flat()??[]:h?.data??[],[d,h,y]),U=(0,g.useMemo)(()=>d?y?.[y?.length-1]?.total_count||0:h?.total_count??0,[d,h,y]),M=d?b:m,z=d?_:f,I=(d?S:v)??null,L=(0,g.useCallback)(()=>{O(e=>Math.max(0,e+1))},[O]),A=(0,g.useCallback)(()=>{O(e=>Math.max(0,e-1))},[O]),T=(s.current-1)*l.current,R=Math.ceil((U-T)/l.current),W=U-T*l.current>C*l.current,x=(C-1)*l.current>T*l.current,D=d?e=>j(e,{revalidate:!1}):e=>k(e,{revalidate:!1});return{data:E,count:U,error:I,isLoading:M,isFetching:z,isError:!!I,page:C,pageCount:R,fetchPage:O,fetchNext:L,fetchPrevious:A,hasNextPage:W,hasPreviousPage:x,revalidate:d?()=>j():()=>k(),setData:D}},to={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function ts(e){var t,r;let{domains:n,membershipRequests:i,memberships:o,invitations:l,subscriptions:a}=e||{};te("useOrganization");let{organization:u}=e4(),d=e5(),c=tr(n,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,enrollmentMode:void 0}),p=tr(i,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),h=tr(o,{initialPage:1,pageSize:10,role:void 0,keepPreviousData:!1,infinite:!1,query:void 0}),f=tr(l,{initialPage:1,pageSize:10,status:["pending"],keepPreviousData:!1,infinite:!1}),g=tr(a,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),m=eY();m.telemetry?.record((0,s.FJ)("useOrganization"));let v=void 0===n?void 0:{initialPage:c.initialPage,pageSize:c.pageSize,enrollmentMode:c.enrollmentMode},k=void 0===i?void 0:{initialPage:p.initialPage,pageSize:p.pageSize,status:p.status},y=void 0===o?void 0:{initialPage:h.initialPage,pageSize:h.pageSize,role:h.role,query:h.query},b=void 0===l?void 0:{initialPage:f.initialPage,pageSize:f.pageSize,status:f.status},_=void 0===a?void 0:{initialPage:g.initialPage,pageSize:g.pageSize,orgId:u?.id},S=ti({...v},u?.getDomains,{keepPreviousData:c.keepPreviousData,infinite:c.infinite,enabled:!!v},{type:"domains",organizationId:u?.id}),P=ti({...k},u?.getMembershipRequests,{keepPreviousData:p.keepPreviousData,infinite:p.infinite,enabled:!!k},{type:"membershipRequests",organizationId:u?.id}),w=ti(y||{},u?.getMemberships,{keepPreviousData:h.keepPreviousData,infinite:h.infinite,enabled:!!y},{type:"members",organizationId:u?.id}),j=ti({...b},u?.getInvitations,{keepPreviousData:f.keepPreviousData,infinite:f.infinite,enabled:!!b},{type:"invitations",organizationId:u?.id}),C=ti({..._},u?.getSubscriptions,{keepPreviousData:g.keepPreviousData,infinite:g.infinite,enabled:!!_},{type:"subscriptions",organizationId:u?.id});return void 0===u?{isLoaded:!1,organization:void 0,membership:void 0,domains:to,membershipRequests:to,memberships:to,invitations:to,subscriptions:to}:null===u?{isLoaded:!0,organization:null,membership:null,domains:null,membershipRequests:null,memberships:null,invitations:null,subscriptions:null}:!m.loaded&&u?{isLoaded:!0,organization:u,membership:void 0,domains:to,membershipRequests:to,memberships:to,invitations:to,subscriptions:to}:{isLoaded:m.loaded,organization:u,membership:(t=d.user.organizationMemberships,r=u.id,t.find(e=>e.organization.id===r)),domains:S,membershipRequests:P,memberships:w,invitations:j,subscriptions:C}}var tl={data:void 0,count:void 0,error:void 0,isLoading:!1,isFetching:!1,isError:!1,page:void 0,pageCount:void 0,fetchPage:void 0,fetchNext:void 0,fetchPrevious:void 0,hasNextPage:!1,hasPreviousPage:!1,revalidate:void 0,setData:void 0};function ta(e){let{userMemberships:t,userInvitations:r,userSuggestions:n}=e||{};te("useOrganizationList");let i=tr(t,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1}),o=tr(r,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),l=tr(n,{initialPage:1,pageSize:10,status:"pending",keepPreviousData:!1,infinite:!1}),a=eY(),u=eH();a.telemetry?.record((0,s.FJ)("useOrganizationList"));let d=void 0===t?void 0:{initialPage:i.initialPage,pageSize:i.pageSize},c=void 0===r?void 0:{initialPage:o.initialPage,pageSize:o.pageSize,status:o.status},p=void 0===n?void 0:{initialPage:l.initialPage,pageSize:l.pageSize,status:l.status},h=!!(a.loaded&&u),f=ti(d||{},u?.getOrganizationMemberships,{keepPreviousData:i.keepPreviousData,infinite:i.infinite,enabled:!!d},{type:"userMemberships",userId:u?.id}),g=ti({...c},u?.getOrganizationInvitations,{keepPreviousData:o.keepPreviousData,infinite:o.infinite,enabled:!!c},{type:"userInvitations",userId:u?.id}),m=ti({...p},u?.getOrganizationSuggestions,{keepPreviousData:l.keepPreviousData,infinite:l.infinite,enabled:!!p},{type:"userSuggestions",userId:u?.id});return h?{isLoaded:h,setActive:a.setActive,createOrganization:a.createOrganization,userMemberships:f,userInvitations:g,userSuggestions:m}:{isLoaded:!1,createOrganization:void 0,setActive:void 0,userMemberships:tl,userInvitations:tl,userSuggestions:tl}}var tu="undefined"!=typeof window?g.useLayoutEffect:g.useEffect,td="useSession",tc=()=>{te(td);let e=e5(),t=eY();return(t.telemetry?.record((0,s.FJ)(td)),void 0===e)?{isLoaded:!1,isSignedIn:void 0,session:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,session:null}:{isLoaded:!0,isSignedIn:t.isSignedIn,session:e}},tp="useSessionList",th=()=>{te(tp);let e=eY(),t=e1(),r=eY();return(r.telemetry?.record((0,s.FJ)(tp)),t)?{isLoaded:!0,sessions:t.sessions,setActive:e.setActive}:{isLoaded:!1,sessions:void 0,setActive:void 0}},tf="useUser";function tg(){te(tf);let e=eH(),t=eY();return(t.telemetry?.record((0,s.FJ)(tf)),void 0===e)?{isLoaded:!1,isSignedIn:void 0,user:void 0}:null===e?{isLoaded:!0,isSignedIn:!1,user:null}:{isLoaded:!0,isSignedIn:!0,user:e}}var tm=()=>(te("useClerk"),eY()),tv=eJ;async function tk(e){try{let t=await e;if(t instanceof Response)return t.json();return t}catch(e){if((0,u.$R)(e)&&e.errors.find(({code:e})=>"session_reverification_required"===e))return c();throw e}}var ty=(e,t)=>{let{__internal_openReverification:r,telemetry:n}=tm(),i=(0,g.useRef)(e),o=(0,g.useRef)(t);return n?.record((0,s.FJ)("useReverification",{onNeedsReverification:!!t?.onNeedsReverification})),tu(()=>{i.current=e,o.current=t}),(0,g.useCallback)((...e)=>(function(e){return function(t){return async(...r)=>{let n=await tk(t(...r));if(p(n)){let i=a(),o=(0,h.D)(n.clerk_error.metadata?.reverification),s=o?o().level:void 0,l=()=>{i.reject(new u.cR("User cancelled attempted verification",{code:"reverification_cancelled"}))},d=()=>{i.resolve(!0)};void 0===e.onNeedsReverification?e.openUIComponent?.({level:s,afterVerification:d,afterVerificationCancelled:l}):e.onNeedsReverification({cancel:l,complete:d,level:s}),await i.promise,n=await tk(t(...r))}return n}}})({openUIComponent:r,telemetry:n,...o.current})(i.current)(...e),[r,n])};function tb({hookName:e,resourceType:t,useFetcher:r,options:n}){return function(i){let{for:o,...l}=i||{for:"user"};te(e);let a=r(o),u=tr(l,{initialPage:1,pageSize:10,keepPreviousData:!1,infinite:!1,__experimental_mode:void 0}),d=eY(),c=eH(),{organization:p}=e4();d.telemetry?.record((0,s.FJ)(e));let h=void 0===l?void 0:{initialPage:u.initialPage,pageSize:u.pageSize,..."organization"===o?{orgId:p?.id}:{}},f=!!(d.loaded&&(n?.unauthenticated||c));return ti(h||{},a,{keepPreviousData:u.keepPreviousData,infinite:u.infinite,enabled:!!h&&f,__experimental_mode:u.__experimental_mode},{type:t,userId:c?.id,..."organization"===o?{orgId:p?.id}:{}})}}tb({hookName:"useStatements",resourceType:"commerce-statements",useFetcher:()=>eY().billing.getStatements}),tb({hookName:"usePaymentAttempts",resourceType:"commerce-payment-attempts",useFetcher:()=>eY().billing.getPaymentAttempts}),tb({hookName:"usePaymentMethods",resourceType:"commerce-payment-methods",useFetcher:e=>{let{organization:t}=e4(),r=eH();return"organization"===e?t?.getPaymentSources:r?.getPaymentSources}}),tb({hookName:"usePlans",resourceType:"commerce-plans",useFetcher:e=>{let t=eY();return({orgId:r,...n})=>t.billing.getPlans({...n,for:e})},options:{unauthenticated:!0}});var t_=e=>{let t=(0,g.useRef)(e);return(0,g.useEffect)(()=>{t.current=e},[e]),t.current},tS=(e,t,r)=>{let n=!!r,i=(0,g.useRef)(r);(0,g.useEffect)(()=>{i.current=r},[r]),(0,g.useEffect)(()=>{if(!n||!e)return()=>{};let r=(...e)=>{i.current&&i.current(...e)};return e.on(t,r),()=>{e.off(t,r)}},[n,t,e,i])},tP=g.createContext(null);tP.displayName="ElementsContext";var tw=(e,t)=>{if(!e)throw Error(`Could not find Elements context; You need to wrap the part of your app that ${t} in an <Elements> provider.`);return e},tj=({stripe:e,options:t,children:r})=>{let n=React5.useMemo(()=>tM(e),[e]),[i,o]=React5.useState(()=>({stripe:"sync"===n.tag?n.stripe:null,elements:"sync"===n.tag?n.stripe.elements(t):null}));React5.useEffect(()=>{let e=!0,r=e=>{o(r=>r.stripe?r:{stripe:e,elements:e.elements(t)})};return"async"!==n.tag||i.stripe?"sync"!==n.tag||i.stripe||r(n.stripe):n.stripePromise.then(t=>{t&&e&&r(t)}),()=>{e=!1}},[n,i,t]);let s=t_(e);React5.useEffect(()=>{null!==s&&s!==e&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[s,e]);let l=t_(t);return React5.useEffect(()=>{if(!i.elements)return;let e=tA(t,l,["clientSecret","fonts"]);e&&i.elements.update(e)},[t,l,i.elements]),React5.createElement(tP.Provider,{value:i},r)},tC=e=>tw(React5.useContext(tP),e),tO=()=>{let{elements:e}=tC("calls useElements()");return e},tE="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",tU=(e,t=tE)=>{if(null===e||tL(e))return e;throw Error(t)},tM=(e,t=tE)=>{if(tI(e))return{tag:"async",stripePromise:Promise.resolve(e).then(e=>tU(e,t))};let r=tU(e,t);return null===r?{tag:"empty"}:{tag:"sync",stripe:r}},tz=e=>null!==e&&"object"==typeof e,tI=e=>tz(e)&&"function"==typeof e.then,tL=e=>tz(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment,tA=(e,t,r)=>tz(e)?Object.keys(e).reduce((n,i)=>{let o=!tz(t)||!tR(e[i],t[i]);return r.includes(i)?(o&&console.warn(`Unsupported prop change: options.${i} is not a mutable property.`),n):o?{...n||{},[i]:e[i]}:n},null):null,tT="[object Object]",tR=(e,t)=>{if(!tz(e)||!tz(t))return e===t;let r=Array.isArray(e);if(r!==Array.isArray(t))return!1;let n=Object.prototype.toString.call(e)===tT;if(n!==(Object.prototype.toString.call(t)===tT))return!1;if(!n&&!r)return e===t;let i=Object.keys(e),o=Object.keys(t);if(i.length!==o.length)return!1;let s={};for(let e=0;e<i.length;e+=1)s[i[e]]=!0;for(let e=0;e<o.length;e+=1)s[o[e]]=!0;let l=Object.keys(s);return l.length===i.length&&l.every(r=>tR(e[r],t[r]))},tW=()=>{let{stripe:e}=tx("calls useStripe()");return e},tx=e=>tw(g.useContext(tP),e),tD=e=>e.charAt(0).toUpperCase()+e.slice(1),[tN,tF]=(((e,t)=>{let r=`${tD(e)}Element`,n=t?e=>{tx(`mounts <${r}>`);let{id:t,className:n}=e;return g.createElement("div",{id:t,className:n})}:({id:t,className:n,fallback:i,options:o={},onBlur:s,onFocus:l,onReady:a,onChange:u,onEscape:d,onClick:c,onLoadError:p,onLoaderStart:h,onNetworksChange:f,onConfirm:m,onCancel:v,onShippingAddressChange:k,onShippingRateChange:y})=>{let b,_=tx(`mounts <${r}>`),S="elements"in _?_.elements:null,[P,w]=g.useState(null),j=g.useRef(null),C=g.useRef(null),[O,E]=(0,g.useState)(!1);tS(P,"blur",s),tS(P,"focus",l),tS(P,"escape",d),tS(P,"click",c),tS(P,"loaderror",p),tS(P,"loaderstart",h),tS(P,"networkschange",f),tS(P,"confirm",m),tS(P,"cancel",v),tS(P,"shippingaddresschange",k),tS(P,"shippingratechange",y),tS(P,"change",u),a&&(b=()=>{E(!0),a(P)}),tS(P,"ready",b),g.useLayoutEffect(()=>{if(null===j.current&&null!==C.current&&S){let t=null;S&&(t=S.create(e,o)),j.current=t,w(t),t&&t.mount(C.current)}},[S,o]);let U=t_(o);return g.useEffect(()=>{if(!j.current)return;let e=tA(o,U,["paymentRequest"]);e&&"update"in j.current&&j.current.update(e)},[o,U]),g.useLayoutEffect(()=>()=>{if(j.current&&"function"==typeof j.current.destroy)try{j.current.destroy(),j.current=null}catch{}},[]),g.createElement(g.Fragment,null,!O&&i,g.createElement("div",{id:t,style:{height:O?"unset":"0px",visibility:O?"visible":"hidden"},className:n,ref:C}))};return n.displayName=r,n.__elementType=e})("payment","undefined"==typeof window),eq("StripeLibsContext")),t$=()=>tm().__unstable__environment,tV=(e="user")=>{let{organization:t}=ts(),{user:r}=tg(),n="organization"===e?t:r,i=tF(),{data:o,trigger:s}=useSWRMutation({key:"commerce-payment-source-initialize",resourceId:n?.id},()=>n?.initializePaymentSource({gateway:"stripe"})),l=t$();useEffect2(()=>{n?.id&&s().catch(()=>{})},[n?.id]);let a=o?.externalGatewayId,u=o?.externalClientSecret,d=o?.paymentMethodOrder,c=l?.commerceSettings.billing.stripePublishableKey,{data:p}=useSWR(i&&a&&c?{key:"stripe-sdk",externalGatewayId:a,stripePublishableKey:c}:null,({stripePublishableKey:e,externalGatewayId:t})=>i?.loadStripe(e,{stripeAccount:t}),{keepPreviousData:!0,revalidateOnFocus:!1,dedupingInterval:6e4});return{stripe:p,initializePaymentSource:s,externalClientSecret:u,paymentMethodOrder:d}},[tB,tK]=eq("PaymentElementContext"),[tJ,tQ]=eq("StripeUtilsContext"),tq=({children:e})=>{let t=tW(),r=tO();return React6.createElement(tJ.Provider,{value:{value:{stripe:t,elements:r}}},e)},tG=({children:e})=>React6.createElement(tJ.Provider,{value:{value:{}}},e)},67937:(e,t,r)=>{r.d(t,{FJ:()=>y,YF:()=>b}),r(30852);var n,i,o,s,l,a,u,d,c,p,h,f,g,m,v,k=r(85649);r(49509),n=new WeakMap,i=new WeakMap,o=new WeakSet,s=function(e){let{sk:t,pk:r,payload:n,...i}=e,o={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>o[e]))},l=function(){let e=localStorage.getItem((0,k.S7)(this,n));return e?JSON.parse(e):{}},a=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem((0,k.S7)(this,n)),!1}};u=new WeakMap,d=new WeakMap,c=new WeakMap,p=new WeakMap,h=new WeakMap,f=new WeakSet,g=function(e,t){let r=Math.random();return!!(r<=(0,k.S7)(this,u).samplingRate&&(void 0===t||r<=t))&&!(0,k.S7)(this,d).isEventThrottled(e)},m=function(){fetch(new URL("/v1/event",(0,k.S7)(this,u).endpoint),{method:"POST",body:JSON.stringify({events:(0,k.S7)(this,p)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{(0,k.OV)(this,p,[])}).catch(()=>void 0)},v=function(){let e={name:(0,k.S7)(this,c).sdk,version:(0,k.S7)(this,c).sdkVersion};if("undefined"!=typeof window){let t=window;if(t.Clerk){let r=t.Clerk;if("object"==typeof r&&null!==r&&"constructor"in r&&"function"==typeof r.constructor&&r.constructor.sdkMetadata){let{name:t,version:n}=r.constructor.sdkMetadata;void 0!==t&&(e.name=t),void 0!==n&&(e.version=n)}}}return e};function y(e,t){return{event:"METHOD_CALLED",payload:{method:e,...t}}}function b(e){return{event:"FRAMEWORK_METADATA",eventSamplingRate:.1,payload:e}}},76829:(e,t,r)=>{r.d(t,{RZ:()=>u,rA:()=>a,q5:()=>l});var n=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,i=r(82163),o="pk_live_";function s(e){if(!e.endsWith("$"))return!1;let t=e.slice(0,-1);return!t.includes("$")&&t.includes(".")}function l(e,t={}){let r;if(!(e=e||"")||!a(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!a(e))throw Error("Publishable key not valid.");return null}let i=e.startsWith(o)?"production":"development";try{r=n(e.split("_")[2])}catch{if(t.fatal)throw Error("Publishable key not valid: Failed to decode key.");return null}if(!s(r)){if(t.fatal)throw Error("Publishable key not valid: Decoded key has invalid format.");return null}let u=r.slice(0,-1);return t.proxyUrl?u=t.proxyUrl:"development"!==i&&t.domain&&t.isSatellite&&(u=`clerk.${t.domain}`),{instanceType:i,frontendApi:u}}function a(e=""){try{if(!(e.startsWith(o)||e.startsWith("pk_test_")))return!1;let t=e.split("_");if(3!==t.length)return!1;let r=t[2];if(!r)return!1;let i=n(r);return s(i)}catch{return!1}}function u(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=i.gE.some(e=>r.endsWith(e)),e.set(r,n)),n}}}},79419:(e,t,r)=>{r.d(t,{B$:()=>k,wF:()=>c,lT:()=>d,z0:()=>a,A0:()=>u,rm:()=>v,m2:()=>m,W5:()=>p,mO:()=>h,Xn:()=>f,eG:()=>g});var n=r(9693),i=r(14129),o=new Set,s=(e,t,r)=>{let n=(0,i.MC)()||(0,i.Fj)(),s=r??e;o.has(s)||n||(o.add(s),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};r(85649);var l=r(12115);r(63018);var a=({children:e})=>((0,n.Kz)("ClerkLoaded"),(0,n.FE)().loaded)?e:null,u=({children:e})=>((0,n.Kz)("ClerkLoading"),"loading"!==(0,n.FE)().status)?null:e,d=({children:e})=>((0,n.Kz)("ClerkFailed"),"error"!==(0,n.FE)().status)?null:e,c=({children:e})=>((0,n.Kz)("ClerkDegraded"),"degraded"!==(0,n.FE)().status)?null:e,p=(0,n.Q)(({clerk:e,...t})=>{let{client:r,session:n}=e,i=r.signedInSessions?r.signedInSessions.length>0:r.activeSessions&&r.activeSessions.length>0;return l.useEffect(()=>{null===n&&i?e.redirectToAfterSignOut():e.redirectToSignIn(t)},[]),null},"RedirectToSignIn"),h=(0,n.Q)(({clerk:e,...t})=>(l.useEffect(()=>{e.redirectToSignUp(t)},[]),null),"RedirectToSignUp"),f=(0,n.Q)(({clerk:e})=>{let{session:t}=e;return l.useEffect(()=>{if(!t)return void e.redirectToSignIn();e.__internal_navigateToTaskIfAvailable()},[]),null},"RedirectToTask"),g=(0,n.Q)(({clerk:e})=>(l.useEffect(()=>{s("RedirectToUserProfile","Use the `redirectToUserProfile()` method instead."),e.redirectToUserProfile()},[]),null),"RedirectToUserProfile"),m=(0,n.Q)(({clerk:e})=>(l.useEffect(()=>{s("RedirectToOrganizationProfile","Use the `redirectToOrganizationProfile()` method instead."),e.redirectToOrganizationProfile()},[]),null),"RedirectToOrganizationProfile"),v=(0,n.Q)(({clerk:e})=>(l.useEffect(()=>{s("RedirectToCreateOrganization","Use the `redirectToCreateOrganization()` method instead."),e.redirectToCreateOrganization()},[]),null),"RedirectToCreateOrganization"),k=(0,n.Q)(({clerk:e,...t})=>(l.useEffect(()=>{e.handleRedirectCallback(t)},[]),null),"AuthenticateWithRedirectCallback")},82163:(e,t,r)=>{r.d(t,{FW:()=>u,HG:()=>a,Vc:()=>l,gE:()=>i,iM:()=>n,mG:()=>o,ub:()=>s});var n=[".lcl.dev",".lclstage.dev",".lclclerk.com"],i=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],o=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],s=[".accountsstage.dev"],l="https://api.lclclerk.com",a="https://api.clerkstage.dev",u="https://api.clerk.com"},85649:(e,t,r)=>{r.d(t,{OV:()=>h,S7:()=>p,VA:()=>a,ie:()=>d,jq:()=>f});var n=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,s=Object.prototype.hasOwnProperty,l=e=>{throw TypeError(e)},a=(e,t)=>{for(var r in t)n(e,r,{get:t[r],enumerable:!0})},u=(e,t,r,l)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let a of o(t))s.call(e,a)||a===r||n(e,a,{get:()=>t[a],enumerable:!(l=i(t,a))||l.enumerable});return e},d=(e,t,r)=>(u(e,t,"default"),r&&u(r,t,"default")),c=(e,t,r)=>t.has(e)||l("Cannot "+r),p=(e,t,r)=>(c(e,t,"read from private field"),r?r.call(e):t.get(e)),h=(e,t,r,n)=>(c(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),f=(e,t,r)=>(c(e,t,"access private method"),r)},92872:(e,t,r)=>{function n(e){return"clerkError"in e}r.d(t,{$R:()=>n,_r:()=>s,cR:()=>i});var i=class e extends Error{constructor(t,{code:r}){let n="\uD83D\uDD12 Clerk:",i=RegExp(n.replace(" ","\\s*"),"i"),o=t.replace(i,""),s=`${n} ${o.trim()}

(code="${r}")
`;super(s),this.toString=()=>`[${this.name}]
Message:${this.message}`,Object.setPrototypeOf(this,e.prototype),this.code=r,this.message=s,this.clerkRuntimeError=!0,this.name="ClerkRuntimeError"}},o=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function s({packageName:e,customMessages:t}){let r=e;function n(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}let i={...o,...t};return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(n(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(n(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(n(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(n(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(n(i.MissingClerkProvider,e))},throw(e){throw Error(n(e))}}}}}]);