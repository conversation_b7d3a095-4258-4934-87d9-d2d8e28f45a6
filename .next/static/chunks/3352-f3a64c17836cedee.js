"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3352],{47262:(e,t,a)=>{a.d(t,{S:()=>o});var r=a(95155);a(12115);var n=a(76981),s=a(5196),i=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)(n.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[2px] border-2 transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(s.A,{className:"size-3.5 stroke-[3]"})})})}},62523:(e,t,a)=>{a.d(t,{p:()=>s});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},63352:(e,t,a)=>{a.d(t,{C:()=>h});var r=a(95155),n=a(30285),s=a(66695),i=a(47262),o=a(62523),l=a(92236),c=a(88539),d=a(60760),u=a(38274),m=a(5196),f=a(42355),g=a(13052),x=a(12115),p=a(96578);function h(e){var t,a;let{config:h,className:b=""}=e,[v,k]=(0,x.useState)(0),[y,j]=(0,x.useState)({}),[w,N]=(0,x.useState)(!1),C=h.questions[v],z=v===h.questions.length-1,E=0===v;(0,x.useEffect)(()=>{if(!C)return;let e=y[C.id];"text"===C.type||"textarea"===C.type?N(!!e&&e.trim().length>0):"multiple"===C.type?N(Array.isArray(e)&&e.length>0):"single"===C.type?N(null!=e):N(!1)},[C,y]),(0,x.useEffect)(()=>{if((null==C?void 0:C.type)==="single"&&w&&!z){let e=setTimeout(()=>{S()},800);return()=>clearTimeout(e)}},[w,null==C?void 0:C.type,z]);let S=()=>{if(z)h.onComplete(y);else{var e;let t=v+1;k(t),null==(e=h.onStepChange)||e.call(h,t,y)}},A=(e,t)=>{j(a=>({...a,[e]:t}))},R=e=>{A(C.id,e)},B=(e,t)=>{let a=y[C.id]||[];t?A(C.id,[...a,e]):A(C.id,a.filter(t=>t!==e))},M=e=>{A(C.id,e)};return C?(0,r.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden ".concat(b),children:[(0,r.jsx)(p.T,{fadeEdge:90,dotSizes:[1,1.5,2],spacing:25,dotsPerRow:6,opacity:.2,darkOpacity:.3,lightColors:["CCCCCC","BBBBBB","DDDDDD"],darkColors:["666666","777777","555555"]}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,r.jsx)("div",{className:"relative z-10 flex flex-col min-h-screen p-4",children:(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"w-full max-w-md",children:[(0,r.jsx)(d.N,{mode:"wait",children:(0,r.jsx)(u.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:(0,r.jsxs)(s.Zp,{className:"border",children:[(0,r.jsxs)(s.aR,{className:"text-center pb-3",children:[(0,r.jsx)(s.ZB,{className:"text-lg font-semibold",children:C.title}),C.subtitle&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:C.subtitle})]}),(0,r.jsxs)(s.Wu,{className:"space-y-4",children:["single"===C.type&&(0,r.jsx)("div",{className:"space-y-2",children:null==(t=C.options)?void 0:t.map(e=>(0,r.jsx)(n.$,{variant:y[C.id]===e.value?"default":"outline",className:"w-full justify-start h-auto py-3 px-4 text-left",onClick:()=>R(e.value),children:(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsx)("span",{children:e.label}),y[C.id]===e.value&&(0,r.jsx)(m.A,{className:"w-4 h-4"})]})},e.value.toString()))}),"multiple"===C.type&&(0,r.jsx)("div",{className:"space-y-2",children:null==(a=C.options)?void 0:a.map(e=>{let t=(y[C.id]||[]).includes(e.value);return(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer",onClick:()=>B(e.value,!t),children:[(0,r.jsx)(i.S,{checked:t,onCheckedChange:t=>B(e.value,t)}),(0,r.jsx)("span",{className:"flex-1",children:e.label})]},e.value.toString())})}),"text"===C.type&&(0,r.jsx)(o.p,{placeholder:C.placeholder||"Enter your answer...",value:y[C.id]||"",onChange:e=>M(e.target.value),className:"w-full",autoFocus:!0}),"textarea"===C.type&&(0,r.jsx)(c.T,{placeholder:C.placeholder||"Enter your answer...",value:y[C.id]||"",onChange:e=>M(e.target.value),className:"w-full min-h-[100px]",autoFocus:!0}),("single"!==C.type||z)&&(0,r.jsxs)("div",{className:"flex justify-between pt-4",children:[!z&&(0,r.jsxs)(n.$,{variant:"outline",onClick:()=>{if(!E){var e;let t=v-1;k(t),null==(e=h.onStepChange)||e.call(h,t,y)}},disabled:E,className:"flex items-center gap-2",size:"sm",children:[(0,r.jsx)(f.A,{className:"w-4 h-4"}),"Previous"]}),(0,r.jsxs)(n.$,{onClick:S,disabled:!w,className:"flex items-center gap-2 bg-[#166534] hover:bg-[#166534]/90 ".concat(z?"w-full":"ml-auto"),size:"sm",children:[z?"Complete":"Next",!z&&(0,r.jsx)(g.A,{className:"w-4 h-4"})]})]})]})]})},v)}),(0,r.jsxs)("div",{className:"mt-8 space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Step ",v+1," of ",h.questions.length]}),(0,r.jsxs)("span",{className:"text-xs text-muted-foreground",children:[Math.round((v+1)/h.questions.length*100),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-muted rounded-full h-1",children:(0,r.jsx)(u.P.div,{className:"bg-[#166534] h-1 rounded-full",initial:{width:0},animate:{width:"".concat((v+1)/h.questions.length*100,"%")},transition:{duration:.3}})})]}),(0,r.jsx)("div",{className:"flex justify-center mt-6",children:(0,r.jsx)(l.g,{size:24,textSize:14,animated:!0,showText:!0,className:"opacity-60"})})]})})})]}):null}},66695:(e,t,a)=>{a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>s,aR:()=>i,wL:()=>d});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex px-6 [.border-t]:pt-6",t),...a})}},88539:(e,t,a)=>{a.d(t,{T:()=>i});var r=a(95155),n=a(12115),s=a(59434);let i=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...n})});i.displayName="Textarea"},92236:(e,t,a)=>{a.d(t,{g:()=>l});var r=a(95155),n=a(11518),s=a.n(n);a(12115);var i=a(6874),o=a.n(i);let l=e=>{let{size:t=48,textSize:a=24,className:n="",animated:i=!1,showText:l=!1,href:c,animationSpeed:d=5}=e,u=t/48,m=32*u,f=20*u,g=+u,x=1.25*u,p=(0,r.jsxs)("div",{style:{position:"relative",width:t,height:t,display:"inline-block",animation:i?"logoFloat ".concat(3/d,"s ease-in-out infinite"):"none"},children:[(0,r.jsx)("div",{style:{position:"absolute",width:t,height:t,borderRadius:"50%",background:"#b4fd98",border:"".concat(g,"px solid #73ed47"),left:0,top:0,zIndex:1,transform:"rotate(70deg)",animation:i?"logoRotate ".concat(8/d,"s linear infinite"):"none"},children:(0,r.jsx)("div",{style:{position:"absolute",width:m,height:m,borderRadius:"50%",background:"#0A4000",border:"".concat(g,"px solid #73ed47"),left:t-m-g-x,top:g+x,zIndex:2,transform:"rotate(280deg)",animation:i?"logoPulse ".concat(2/d,"s ease-in-out infinite"):"none"},children:(0,r.jsx)("div",{style:{position:"absolute",width:f,height:f,borderRadius:"50%",background:"#fff",border:"".concat(g,"px solid #73ed47"),left:m-f-g-x,top:g+x,zIndex:3,animation:i?"logoGlow ".concat(4/d,"s ease-in-out infinite"):"none"}})})}),i&&(0,r.jsx)(s(),{id:"5428b1dfccf92ce4",children:"@-webkit-keyframes logoRotate{0%{-webkit-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);transform:rotate(430deg)}}@-moz-keyframes logoRotate{0%{-moz-transform:rotate(70deg);transform:rotate(70deg)}100%{-moz-transform:rotate(430deg);transform:rotate(430deg)}}@-o-keyframes logoRotate{0%{-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-o-transform:rotate(430deg);transform:rotate(430deg)}}@keyframes logoRotate{0%{-webkit-transform:rotate(70deg);-moz-transform:rotate(70deg);-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);-moz-transform:rotate(430deg);-o-transform:rotate(430deg);transform:rotate(430deg)}}"})]}),h=(0,r.jsxs)("div",{className:"flex items-center gap-3 ".concat(n),children:[p,(0,r.jsx)("span",{className:"font-bold text-foreground",style:{fontSize:"".concat(a,"px"),position:"relative",animation:i?"textGlow ".concat(3/d,"s ease-in-out infinite"):"none"},children:"siift.ai"})]}),b=l?h:p;return c?(0,r.jsx)(o(),{href:c,className:"hover:opacity-80 transition-opacity",children:b}):b}},96578:(e,t,a)=>{a.d(t,{T:()=>s});var r=a(95155),n=a(12115);function s(e){let{fadeEdge:t=80,dotSizes:a=[2],spacing:s=20,dotsPerRow:i=8,opacity:o=.3,darkOpacity:l=.4,lightColors:c=["888888"],darkColors:d=["AAAAAA"],className:u=""}=e,m=e=>{let t=1e4*Math.sin(e);return t-Math.floor(t)},{dots:f,patternSize:g}=(0,n.useMemo)(()=>{let e=[],t=i*s;for(let t=0;t<i;t++)for(let r=0;r<i;r++){let n=r*s+s/2,i=t*s+s/2,o=1e3*t+r,l=Math.floor(m(o)*a.length),u=Math.floor(m(o+1)*c.length),f=Math.floor(m(o+2)*d.length),g=a[l],x=c[u],p=d[f];e.push({x:n,y:i,size:g,lightColor:x,darkColor:p})}return{dots:e,patternSize:t}},[i,s,a,c,d]),x=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=f.map((t,a)=>{let r=e?t.darkColor:t.lightColor;return"<circle cx='".concat(t.x,"' cy='").concat(t.y,"' r='").concat(t.size,"' fill='%23").concat(r,"'/>")}).join("");return"url(\"data:image/svg+xml,%3Csvg width='".concat(g,"' height='").concat(g,"' viewBox='0 0 ").concat(g," ").concat(g,"' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E").concat(t,'%3C/g%3E%3C/svg%3E")')};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-[".concat(o,"] dark:opacity-0 ").concat(u),style:{backgroundImage:x(!1),maskImage:"\n            linear-gradient(to right, transparent 0%, black ".concat(t,"%, black ").concat(100-t,"%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black ").concat(t,"%, black ").concat(100-t,"%, transparent 100%)\n          "),maskComposite:"intersect",WebkitMaskImage:"\n            linear-gradient(to right, transparent 0%, black ".concat(t,"%, black ").concat(100-t,"%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black ").concat(t,"%, black ").concat(100-t,"%, transparent 100%)\n          "),WebkitMaskComposite:"source-in"}}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-0 dark:opacity-[".concat(l,"] ").concat(u),style:{backgroundImage:x(!0),maskImage:"\n            linear-gradient(to right, transparent 0%, black ".concat(t,"%, black ").concat(100-t,"%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black ").concat(t,"%, black ").concat(100-t,"%, transparent 100%)\n          "),maskComposite:"intersect",WebkitMaskImage:"\n            linear-gradient(to right, transparent 0%, black ".concat(t,"%, black ").concat(100-t,"%, transparent 100%),\n            linear-gradient(to bottom, transparent 0%, black ").concat(t,"%, black ").concat(100-t,"%, transparent 100%)\n          "),WebkitMaskComposite:"source-in"}})]})}}}]);