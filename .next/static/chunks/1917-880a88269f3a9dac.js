"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1917],{31917:(e,t,i)=>{i.d(t,{N:()=>l});class s{static savecode(e,t){this.codes.set(e,t)}static getCode(e){return this.codes.get(e)}static deleteCode(e){this.codes.delete(e)}static saveMessage(e){this.messages.push(e),this.messages.length>100&&this.messages.shift()}static getMessages(e){return e?this.messages.filter(t=>t.to===e):[...this.messages]}static setRateLimit(e,t){this.rateLimits.set(e,t)}static getRateLimit(e){return this.rateLimits.get(e)}static clearExpiredCodes(){let e=Date.now();for(let[t,i]of this.codes.entries())i.expiresAt<e&&this.codes.delete(t)}static clearExpiredRateLimits(){let e=Date.now();for(let[t,i]of this.rateLimits.entries())i.resetAt<e&&this.rateLimits.delete(t)}static getAllCodes(){return this.codes}static clearMessages(){this.messages.splice(0)}static clearRateLimits(){this.rateLimits.clear()}}s.codes=new Map,s.messages=[],s.rateLimits=new Map;let a={"email-verification":{subject:"Verify your email address",body:'\n      <h2>Welcome to Siift!</h2>\n      <p>Please verify your email address by entering this code:</p>\n      <div style="font-size: 24px; font-weight: bold; color: #2563eb; padding: 20px; background: #f3f4f6; border-radius: 8px; text-align: center; margin: 20px 0;">\n        {{CODE}}\n      </div>\n      <p>This code will expire in 15 minutes.</p>\n      <p>If you didn\'t create an account, please ignore this email.</p>\n    ',type:"verification"},"password-reset":{subject:"Reset your password",body:'\n      <h2>Password Reset Request</h2>\n      <p>You requested to reset your password. Use this code to continue:</p>\n      <div style="font-size: 24px; font-weight: bold; color: #dc2626; padding: 20px; background: #fef2f2; border-radius: 8px; text-align: center; margin: 20px 0;">\n        {{CODE}}\n      </div>\n      <p>This code will expire in 15 minutes.</p>\n      <p>If you didn\'t request this, please ignore this email.</p>\n    ',type:"password-reset"}};class r{static generateCode(){let e=this.config.codeLength,t="";for(let i=0;i<e;i++)t+=Math.floor(10*Math.random()).toString();return t}static createCodeKey(e,t){return"".concat(e,":").concat(t)}static checkRateLimit(e){s.clearExpiredRateLimits();let t=s.getRateLimit(e),i=Date.now();if(t&&t.resetAt>i){if(t.count>=this.config.maxEmailsPerHour){let e=Error("Rate limit exceeded. Please try again later.");throw e.code="RATE_LIMITED",e.details={resetAt:t.resetAt},e}s.setRateLimit(e,{...t,count:t.count+1})}else s.setRateLimit(e,{email:e,count:1,resetAt:i+36e5})}static async sendEmailVerification(e){await new Promise(e=>setTimeout(e,800));try{this.checkRateLimit(e.email);let t=this.generateCode(),i=Date.now(),r=i+60*this.config.codeExpirationMinutes*1e3,o={code:t,email:e.email,type:"email-verification",expiresAt:r,attempts:0,maxAttempts:this.config.maxAttempts,createdAt:i},n=this.createCodeKey(e.email,"email-verification");s.savecode(n,o);let c=a["email-verification"],d={id:"msg_".concat(i,"_").concat(Math.random().toString(36).substr(2,9)),to:e.email,subject:c.subject,body:c.body.replace("{{CODE}}",t),type:c.type,sentAt:i,verificationCode:t};return s.saveMessage(d),s.clearExpiredCodes(),{success:!0,messageId:d.id,message:"Verification email sent successfully"}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send verification email")}}static async sendPasswordReset(e){await new Promise(e=>setTimeout(e,800));try{this.checkRateLimit(e.email);let t=this.generateCode(),i=Date.now(),r=i+60*this.config.codeExpirationMinutes*1e3,o={code:t,email:e.email,type:"password-reset",expiresAt:r,attempts:0,maxAttempts:this.config.maxAttempts,createdAt:i},n=this.createCodeKey(e.email,"password-reset");s.savecode(n,o);let c=a["password-reset"],d={id:"msg_".concat(i,"_").concat(Math.random().toString(36).substr(2,9)),to:e.email,subject:c.subject,body:c.body.replace("{{CODE}}",t),type:c.type,sentAt:i,verificationCode:t};return s.saveMessage(d),s.clearExpiredCodes(),{success:!0,messageId:d.id,message:"Password reset email sent successfully"}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send password reset email")}}static async verifyCode(e){await new Promise(e=>setTimeout(e,500)),s.clearExpiredCodes();let t=this.createCodeKey(e.email,e.type),i=s.getCode(t);if(!i){let e=Error("Verification code not found or expired");throw e.code="CODE_EXPIRED",e}if(i.expiresAt<Date.now()){s.deleteCode(t);let e=Error("Verification code has expired");throw e.code="CODE_EXPIRED",e}if(i.attempts>=i.maxAttempts){s.deleteCode(t);let e=Error("Maximum verification attempts exceeded");throw e.code="MAX_ATTEMPTS_EXCEEDED",e}if(i.attempts++,s.savecode(t,i),i.code!==e.code){let e=Error("Invalid verification code");throw e.code="CODE_INVALID",e.details={attemptsRemaining:i.maxAttempts-i.attempts},e}return s.deleteCode(t),!0}static getMessages(e){return s.getMessages(e)}static getLatestCode(e,t){let i=this.createCodeKey(e,t),a=s.getCode(i);return(null==a?void 0:a.code)||null}static getAllActiveCodes(){s.clearExpiredCodes();let e=[];for(let[t,i]of s.getAllCodes().entries())i.expiresAt>Date.now()&&e.push(i);return e}static clearAll(){s.getAllCodes().clear(),s.clearMessages(),s.clearRateLimits()}}r.config={codeLength:6,codeExpirationMinutes:15,maxAttempts:3,rateLimitMinutes:1,maxEmailsPerHour:10};let o=[{id:"admin-1",email:"<EMAIL>",name:"Admin User",role:"admin",isEmailVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"user-1",email:"<EMAIL>",name:"Test User",role:"user",isEmailVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],n={},c=e=>new Promise(t=>setTimeout(t,e)),d=e=>{let t={userId:e.id,email:e.email,role:e.role,exp:Math.floor(Date.now()/1e3)+604800};return"mock.jwt.".concat(btoa(JSON.stringify(t)))};class l{static async login(e,t){await c(1e3);let i=o.find(t=>t.email===e);if(!i)throw Error("User not found");if(({"<EMAIL>":"123456789@","<EMAIL>":"password123"})[e]!==t)throw Error("Invalid password");let s=d(i),a=d(i);return{user:i,tokens:{accessToken:s,refreshToken:a,expiresAt:Date.now()+6048e5}}}static async register(e,t,i,s,a){if(await c(1200),o.find(t=>t.email===e))throw Error("User already exists with this email");if(a){if("000000"===a)throw Error("Invalid referral code. Please check and try again.");console.log("Mock: Referral code ".concat(a," accepted"))}let n={id:"user-".concat(Date.now()),email:e,name:"".concat(i," ").concat(s),role:"user",isEmailVerified:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(n);try{await r.sendEmailVerification({email:n.email,name:n.name,type:"registration"})}catch(e){console.warn("Failed to send verification email during registration:",e)}let l=d(n),m=d(n);return{user:n,tokens:{accessToken:l,refreshToken:m,expiresAt:Date.now()+6048e5}}}static async forgotPassword(e){await c(800);let t=o.find(t=>t.email===e);if(!t)return console.log("Mock: Email ".concat(e," not found, but returning success for security")),{message:"If an account with this email exists, a reset code has been sent."};try{let e=await r.sendPasswordReset({email:t.email,name:t.name});return{message:e.message,code:e.code}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send password reset email")}}static async resetPassword(e,t,i){if(await c(1e3),!o.find(t=>t.email===e))throw Error("User not found");try{return await r.verifyCode({email:e,code:t,type:"password-reset"}),console.log("Mock: Password reset successful for ".concat(e)),console.log("Mock: New password would be: ".concat(i)),{message:"Password reset successfully"}}catch(e){if(e instanceof Error)throw e;throw Error("Password reset failed")}}static async refreshToken(e){await c(500);try{let t=JSON.parse(atob(e.split(".")[2])),i=o.find(e=>e.id===t.userId);if(!i)throw Error("Invalid refresh token");let s=d(i),a=d(i),r=Date.now()+6048e5;return{tokens:{accessToken:s,refreshToken:a,expiresAt:r}}}catch(e){throw Error("Invalid refresh token")}}static async verifyToken(e){await c(300);try{return JSON.parse(atob(e.split(".")[2])).exp>Math.floor(Date.now()/1e3)}catch(e){return!1}}static getVerificationCodes(){return{...n}}static clearExpiredCodes(){let e=Date.now();Object.keys(n).forEach(t=>{n[t].expires<e&&delete n[t]})}static async sendEmailVerification(e,t){await c(800);try{if(!o.find(t=>t.email===e))throw Error("User not found");let i=await r.sendEmailVerification({email:e,name:t,type:"registration"});return{success:i.success,message:i.message,code:i.code}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send verification email")}}static async verifyEmail(e,t){await c(600);try{await r.verifyCode({email:e,code:t,type:"email-verification"});let i=o.find(t=>t.email===e);return i&&(i.isEmailVerified=!0,i.updatedAt=new Date().toISOString()),{success:!0,message:"Email verified successfully"}}catch(e){if(e instanceof Error)throw e;throw Error("Email verification failed")}}static async resendEmailVerification(e){await c(800);try{let t=o.find(t=>t.email===e);if(!t)throw Error("User not found");if(t.isEmailVerified)throw Error("Email is already verified");let i=await r.sendEmailVerification({email:e,name:t.name,type:"registration"});return{success:i.success,message:i.message,code:i.code}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to resend verification email")}}}}}]);