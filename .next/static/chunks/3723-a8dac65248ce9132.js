"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3723],{18994:(e,t,r)=>{r.d(t,{N:()=>l});var s=r(95155),a=r(90786),n=r(59434),o=r(6874),i=r.n(o),c=r(35695);function l(){let e=(0,c.usePathname)(),{user:t}=(0,a.P)();return(0,s.jsx)("nav",{className:"flex items-center space-x-6",children:[{name:"Dashboard",href:"/user-dashboard"}].filter(t=>e!==t.href).map(t=>(0,s.jsxs)(i(),{href:t.href,className:(0,n.cn)("text-sm font-medium transition-colors relative",e===t.href?"text-[#166534] hover:text-[#166534]":"text-muted-foreground hover:text-[#166534]"),children:[t.name,e===t.href&&(0,s.jsx)("div",{className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full"})]},t.href))})}},25731:(e,t,r)=>{r.d(t,{Z:()=>n});var s=r(73747);class a{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r="".concat("/api").concat(e),a={headers:{"Content-Type":"application/json",...s.C.getAuthHeaders(),...t.headers},...t};try{let e=await fetch(r,a);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||"HTTP error! status: ".concat(e.status))}let t=await e.json();if(t.success&&void 0!==t.data)return t.data;return t}catch(t){throw console.error("API request failed: ".concat(e),t),t}}async login(e){return this.request("/auth/signin",{method:"POST",body:JSON.stringify(e)})}async register(e){return this.request("/auth/signup",{method:"POST",body:JSON.stringify(e)})}async refreshToken(e){return this.request("/auth/refresh",{method:"POST",body:JSON.stringify({refreshToken:e})})}async verifyToken(e){try{return await this.request("/auth/verify",{method:"POST",headers:{Authorization:"Bearer ".concat(e)}}),!0}catch(e){return!1}}async logout(){return this.request("/auth/logout",{method:"POST"})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/profile",{method:"PUT",body:JSON.stringify(e)})}async getProjects(){return this.request("/projects")}async getProject(e){return this.request("/projects/".concat(e))}async createProject(e){return this.request("/projects",{method:"POST",body:JSON.stringify(e)})}async updateProject(e,t){return this.request("/projects/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteProject(e){return this.request("/projects/".concat(e),{method:"DELETE"})}}let n=new a},41752:(e,t,r)=>{r.d(t,{B:()=>h});var s=r(95155),a=r(71007),n=r(381),o=r(34835),i=r(35695),c=r(99912),l=r(91950),d=r(44838),u=r(90786);function h(){let{user:e,signOut:t,firstName:r,email:h,fullName:m}=(0,u.P)(),f=(0,i.useRouter)();return e?(0,s.jsxs)(d.rI,{children:[(0,s.jsx)(d.ty,{asChild:!0,children:(0,s.jsx)("div",{children:(0,s.jsx)(c.V,{icon:a.A,text:r||"User",variant:"ghost",size:"md",layout:"horizontal",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:l.hS.md})})}),(0,s.jsxs)(d.SQ,{className:"w-56 bg-background border-border",align:"end",forceMount:!0,children:[(0,s.jsx)(d.lp,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none text-foreground",children:m||"User"}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:h||""})]})}),(0,s.jsx)(d.mB,{className:"bg-border"}),(0,s.jsxs)(d._2,{onClick:()=>{f.push("/profile")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]}),(0,s.jsxs)(d._2,{onClick:()=>{f.push("/settings")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Settings"})]}),(0,s.jsx)(d.mB,{className:"bg-border"}),(0,s.jsxs)(d._2,{onClick:()=>{t()},className:"hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer",children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]}):null}},56611:(e,t,r)=>{r.d(t,{c:()=>f});var s=r(95155),a=r(75525),n=r(74783),o=r(6874),i=r.n(o),c=r(35695),l=r(12115),d=r(30285),u=r(38382),h=r(66681),m=r(59434);function f(){let[e,t]=(0,l.useState)(!1),r=(0,c.usePathname)(),{user:o}=(0,h.A)(),f=(null==o?void 0:o.role)==="admin"?[{name:"Admin",href:"/admin",icon:a.A}]:[{name:"Dashboard",href:"/user-dashboard",icon:void 0}];return(0,s.jsxs)(u.cj,{open:e,onOpenChange:t,children:[(0,s.jsx)(u.CG,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors",children:[(0,s.jsx)(n.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,s.jsx)(u.h,{side:"left",className:"pr-0 bg-background border-r border-border",children:(0,s.jsx)("nav",{className:"flex flex-col space-y-3 mt-6",children:f.filter(e=>r!==e.href).map(e=>{let a="/admin"===e.href?r.startsWith("/admin"):r===e.href;return(0,s.jsxs)(i(),{href:e.href,onClick:()=>t(!1),className:(0,m.cn)("text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",a?"text-[#166534] bg-[#166534]/10":"text-muted-foreground hover:text-[#166534]"),children:[e.icon&&(0,s.jsx)(e.icon,{className:"h-4 w-4"}),e.name,a&&(0,s.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full"})]},e.href)})})})]})}},62523:(e,t,r)=>{r.d(t,{p:()=>n});var s=r(95155);r(12115);var a=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>d});var s=r(95155);r(12115);var a=r(59434);function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",t),...r})}},73747:(e,t,r)=>{r.d(t,{C:()=>o});let s="siift_access_token",a="siift_refresh_token",n="siift_user";class o{static setTokens(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=t?localStorage:sessionStorage;r.setItem(s,e.accessToken),e.refreshToken&&r.setItem(a,e.refreshToken)}static getAccessToken(){return localStorage.getItem(s)||sessionStorage.getItem(s)}static getRefreshToken(){return localStorage.getItem(a)||sessionStorage.getItem(a)}static setUser(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(t?localStorage:sessionStorage).setItem(n,JSON.stringify(e))}static getUser(){try{let e=localStorage.getItem(n)||sessionStorage.getItem(n);if(!e)return null;let t=JSON.parse(e);return t.createdAt&&"string"==typeof t.createdAt&&(t.createdAt=new Date(t.createdAt)),t.updatedAt&&"string"==typeof t.updatedAt&&(t.updatedAt=new Date(t.updatedAt)),t}catch(e){return console.error("Error parsing user data:",e),null}}static clearSession(){[localStorage,sessionStorage].forEach(e=>{e.removeItem(s),e.removeItem(a),e.removeItem(n)})}static clearInvalidSession(){let e=this.getAccessToken();e&&(!e.includes(".")||3!==e.split(".").length)&&(console.log("Clearing invalid token format"),this.clearSession())}static isAuthenticated(){return!!this.getAccessToken()}static getAuthHeaders(){let e=this.getAccessToken();return e?{Authorization:"Bearer ".concat(e)}:{}}}},86473:(e,t,r)=>{r.d(t,{N:()=>i});var s=r(95155),a=r(36996),n=r(92262);function o(e){let{children:t,showHeader:r=!0,showFooter:o=!0,constrainHeight:i=!1}=e;return(0,s.jsxs)("div",{className:"".concat(i?"h-screen":"min-h-screen"," flex flex-col bg-background"),children:[r&&(0,s.jsx)(n.Header,{}),(0,s.jsx)("main",{className:"flex-1 ".concat(i?"min-h-0":""),children:t}),o&&(0,s.jsx)(a.Footer,{})]})}function i(e){let{children:t}=e;return(0,s.jsx)(o,{showFooter:!1,constrainHeight:!0,children:(0,s.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:t})})}},92262:(e,t,r)=>{r.d(t,{Header:()=>l});var s=r(95155);r(12115);var a=r(18994),n=r(56611),o=r(41752),i=r(66688),c=r(92236);function l(){return(0,s.jsx)("header",{className:"w-full sticky top-0 z-30 bg-background/80 backdrop-blur border-b border-border",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,s.jsx)(n.c,{})}),(0,s.jsx)(c.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,s.jsx)("div",{className:"hidden md:flex ml-6",children:(0,s.jsx)(a.N,{})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(i.U,{}),(0,s.jsx)(o.B,{})]})]})})}}}]);