"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7724],{70794:(e,t,s)=>{s.d(t,{h:()=>c});var r=s(95155),a=s(66681),i=s(75525),n=s(35695),l=s(12115);function c(e){var t;let{children:s}=e,{user:c,isAuthenticated:d,isLoading:o}=(0,a.A)(),m=(0,n.useRouter)();return((0,l.useEffect)(()=>{if(!o){if(!d)return void m.push("/");if((null==c?void 0:c.role)!=="admin")return void m.push("/user-dashboard")}},[d,o,c,m]),o)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):d?(null==c||null==(t=c.email)?void 0:t.includes("siift.ai"))?(0,r.jsx)(r.Fragment,{children:s}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-4 text-destructive"}),(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"You need a siift.ai email address to access the admin panel."}),(0,r.jsx)("button",{onClick:()=>m.push("/user-dashboard"),className:"mt-4 text-primary hover:underline",children:"Go to Dashboard"})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Please log in to access this page."})]})})}},97724:(e,t,s)=>{s.d(t,{U:()=>$});var r=s(95155),a=s(75525),i=s(72713),n=s(71007),l=s(381),c=s(23861),d=s(14395),o=s(14186),m=s(17580),u=s(49376),x=s(57434),h=s(14738),g=s(25487),j=s(97200),f=s(32919),p=s(69803),b=s(34869),v=s(35695),y=s(12115),N=s(13052),A=s(6874),k=s.n(A),w=s(8643),C=s(18888);function P(e){let{items:t}=e,s=(0,v.usePathname)();return(0,r.jsxs)(C.Cn,{children:[(0,r.jsx)(C.jj,{children:"Platform"}),(0,r.jsx)(C.wZ,{children:t.map(e=>{var t;let a=s===e.url||s.startsWith(e.url+"/");return e.items?(0,r.jsx)(w.Nt,{asChild:!0,defaultOpen:a,className:"group/collapsible",children:(0,r.jsxs)(C.FX,{children:[(0,r.jsx)(w.R6,{asChild:!0,children:(0,r.jsxs)(C.Uj,{tooltip:e.title,className:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors duration-200",children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title}),(0,r.jsx)(N.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,r.jsx)(w.Ke,{children:(0,r.jsx)(C.q9,{className:"ml-4 border-l border-sidebar-border pl-2",children:null==(t=e.items)?void 0:t.map(e=>{let t=s===e.url;return(0,r.jsx)(C.Fg,{children:(0,r.jsx)(C.Cp,{asChild:!0,isActive:t,className:"hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground transition-colors duration-200 text-sidebar-foreground/80",children:(0,r.jsx)(k(),{href:e.url,children:e.title})})},e.title)})})})]})},e.title):(0,r.jsx)(C.FX,{children:(0,r.jsx)(C.Uj,{asChild:!0,tooltip:e.title,isActive:a,className:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors duration-200",children:(0,r.jsxs)(k(),{href:e.url,children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title)})})]})}var U=s(10081),R=s(53311),S=s(61106),_=s(81586),z=s(34835),B=s(91394),F=s(44838),I=s(90786);function M(e){let{user:t}=e,{isMobile:s}=(0,C.cL)(),{signOut:a}=(0,I.P)(),i=(0,v.useRouter)(),n=e=>e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2);return(0,r.jsx)(C.wZ,{children:(0,r.jsx)(C.FX,{children:(0,r.jsxs)(F.rI,{children:[(0,r.jsx)(F.ty,{asChild:!0,children:(0,r.jsxs)(C.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsxs)(B.eu,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(B.BK,{src:t.avatar,alt:t.name}),(0,r.jsx)(B.q5,{className:"rounded-lg",children:n(t.name)})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:t.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:t.email})]}),(0,r.jsx)(U.A,{className:"ml-auto size-4"})]})}),(0,r.jsxs)(F.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:s?"bottom":"right",align:"end",sideOffset:4,children:[(0,r.jsx)(F.lp,{className:"p-0 font-normal",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsxs)(B.eu,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(B.BK,{src:t.avatar,alt:t.name}),(0,r.jsx)(B.q5,{className:"rounded-lg",children:n(t.name)})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:t.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:t.email})]})]})}),(0,r.jsx)(F.mB,{}),(0,r.jsx)(F.I,{children:(0,r.jsxs)(F._2,{className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(R.A,{}),"Upgrade to Pro"]})}),(0,r.jsx)(F.mB,{}),(0,r.jsxs)(F.I,{children:[(0,r.jsxs)(F._2,{onClick:()=>{i.push("/user-dashboard?tab=profile")},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(S.A,{}),"Account"]}),(0,r.jsxs)(F._2,{onClick:()=>{i.push("/user-dashboard?tab=billing")},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(_.A,{}),"Billing"]}),(0,r.jsxs)(F._2,{onClick:()=>{i.push("/user-dashboard?tab=notifications")},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(c.A,{}),"Notifications"]})]}),(0,r.jsx)(F.mB,{}),(0,r.jsxs)(F._2,{onClick:()=>{a()},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(z.A,{}),"Log out"]})]})]})})})}var q=s(84616);function D(e){let{teams:t}=e,{isMobile:s}=(0,C.cL)(),[a,i]=y.useState(t[0]);return a?(0,r.jsx)(C.wZ,{children:(0,r.jsx)(C.FX,{children:(0,r.jsxs)(F.rI,{children:[(0,r.jsx)(F.ty,{asChild:!0,children:(0,r.jsxs)(C.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,r.jsx)(a.logo,{className:"size-4"})}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:a.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:a.plan})]}),(0,r.jsx)(U.A,{className:"ml-auto"})]})}),(0,r.jsxs)(F.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"start",side:s?"bottom":"right",sideOffset:4,children:[(0,r.jsx)(F.lp,{className:"text-muted-foreground text-xs",children:"Teams"}),t.map((e,t)=>(0,r.jsxs)(F._2,{onClick:()=>i(e),className:"gap-2 p-2 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border",children:(0,r.jsx)(e.logo,{className:"size-3.5 shrink-0"})}),e.name,(0,r.jsxs)(F.V0,{children:["⌘",t+1]})]},e.name)),(0,r.jsx)(F.mB,{}),(0,r.jsxs)(F._2,{className:"gap-2 p-2 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border bg-transparent",children:(0,r.jsx)(q.A,{className:"size-4"})}),(0,r.jsx)("div",{className:"text-muted-foreground font-medium",children:"Add team"})]})]})]})})}):null}var G=s(66681);let L=e=>({user:{name:(null==e?void 0:e.name)||"Admin",email:(null==e?void 0:e.email)||"",avatar:(null==e?void 0:e.avatar)||""},teams:[{name:"Siift Admin",logo:a.A,plan:"Admin Panel"}],navMain:[{title:"Dashboard",url:"/admin/analytics",icon:i.A,isActive:!0,items:[{title:"Overview",url:"/admin/analytics/overview"},{title:"User Analytics",url:"/admin/analytics/users"},{title:"Activity Metrics",url:"/admin/analytics/activity-metrics"},{title:"Revenue Reports",url:"/admin/analytics/revenue"},{title:"Performance",url:"/admin/analytics/performance"},{title:"Feedbacks",url:"/admin/analytics/feedbacks"}]},{title:"Profile",url:"/admin/profile",icon:n.A},{title:"Settings",url:"/admin/settings-tab",icon:l.A},{title:"Notifications",url:"/admin/notifications",icon:c.A},{title:"Projects",url:"/admin/projects",icon:d.A,items:[{title:"All Projects",url:"/admin/projects/all"},{title:"Create New",url:"/admin/projects/create"}]},{title:"Recent",url:"/admin/recent",icon:o.A},{title:"User Management",url:"/admin/users",icon:m.A,items:[{title:"All Users",url:"/admin/users/all"},{title:"Active Users",url:"/admin/users/active"},{title:"Pending Approval",url:"/admin/users/pending"},{title:"User Roles",url:"/admin/users/roles"},{title:"Permissions",url:"/admin/users/permissions"}]},{title:"Agent Analytics",url:"/admin/agent",icon:u.A,items:[{title:"Agent Calls",url:"/admin/agent/calls"},{title:"Usage Statistics",url:"/admin/agent/usage-stats"},{title:"Token Trends",url:"/admin/agent/token-trends"},{title:"Performance",url:"/admin/agent/performance"}]},{title:"Reports",url:"/admin/reports",icon:x.A,items:[{title:"System Reports",url:"/admin/reports/system"},{title:"User Reports",url:"/admin/reports/users"},{title:"Activity Reports",url:"/admin/reports/activity"},{title:"Export Data",url:"/admin/reports/export"}]},{title:"System",url:"/admin/system",icon:h.A,items:[{title:"Health Check",url:"/admin/system/health",icon:a.A},{title:"Server Status",url:"/admin/system/status",icon:g.A},{title:"Logs",url:"/admin/system/logs",icon:j.A},{title:"Maintenance",url:"/admin/system/maintenance",icon:l.A}]},{title:"Settings",url:"/admin/settings",icon:l.A,items:[{title:"General",url:"/admin/settings/general",icon:l.A},{title:"Security",url:"/admin/settings/security",icon:f.A},{title:"Notifications",url:"/admin/settings/notifications",icon:c.A},{title:"API Keys",url:"/admin/settings/api-keys",icon:p.A},{title:"Integrations",url:"/admin/settings/integrations",icon:b.A}]}]});function X(e){let{...t}=e,{user:s,logout:a}=(0,G.A)();(0,v.useRouter)();let i=L(s);return(0,r.jsxs)(C.Bx,{collapsible:"icon",...t,children:[(0,r.jsx)(C.Gh,{children:(0,r.jsx)(D,{teams:i.teams})}),(0,r.jsx)(C.Yv,{children:(0,r.jsx)(P,{items:i.navMain})}),(0,r.jsx)(C.CG,{children:(0,r.jsx)(M,{user:i.user})}),(0,r.jsx)(C.jM,{})]})}var E=s(70794),K=s(99708),O=s(59434);function T(e){let{...t}=e;return(0,r.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...t})}function Z(e){let{className:t,...s}=e;return(0,r.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,O.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",t),...s})}function Q(e){let{className:t,...s}=e;return(0,r.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,O.cn)("inline-flex items-center gap-1.5",t),...s})}function Y(e){let{asChild:t,className:s,...a}=e,i=t?K.DX:"a";return(0,r.jsx)(i,{"data-slot":"breadcrumb-link",className:(0,O.cn)("hover:text-foreground transition-colors",s),...a})}function H(e){let{className:t,...s}=e;return(0,r.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,O.cn)("text-foreground font-normal",t),...s})}function V(e){let{children:t,className:s,...a}=e;return(0,r.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,O.cn)("[&>svg]:size-3.5",s),...a,children:null!=t?t:(0,r.jsx)(N.A,{})})}var W=s(22346);let J=e=>{let t=e.split("/").filter(Boolean),s=[];if(t.length>1){if(s.push({title:"Admin",href:"/admin"}),"analytics"===t[1]){if(s.push({title:"Analytics",href:"/admin/analytics"}),t[2]){let r=t[2].split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");s.push({title:r,href:e,isCurrentPage:!0})}}else if("agent"===t[1]&&(s.push({title:"Agent Analytics",href:"/admin/agent"}),t[2])){let r=t[2].split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");s.push({title:r,href:e,isCurrentPage:!0})}}return s};function $(e){let{children:t}=e,s=J((0,v.usePathname)());return(0,r.jsx)(E.h,{children:(0,r.jsxs)(C.GB,{children:[(0,r.jsx)(X,{}),(0,r.jsxs)(C.sF,{children:[(0,r.jsx)("header",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,r.jsx)(C.x2,{className:"-ml-1"}),(0,r.jsx)(W.w,{orientation:"vertical",className:"mr-2 data-[orientation=vertical]:h-4"}),(0,r.jsx)(T,{children:(0,r.jsx)(Z,{children:s.map((e,t)=>(0,r.jsxs)(y.Fragment,{children:[(0,r.jsx)(Q,{className:0===t?"hidden md:block":"",children:e.isCurrentPage?(0,r.jsx)(H,{children:e.title}):(0,r.jsx)(Y,{href:e.href,children:e.title})}),t<s.length-1&&(0,r.jsx)(V,{className:"hidden md:block"})]},e.href))})})]})}),(0,r.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:t})]})]})})}}}]);