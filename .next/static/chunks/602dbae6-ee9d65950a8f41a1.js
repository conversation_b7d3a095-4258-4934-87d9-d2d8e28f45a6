"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1309],{35294:(e,t,r)=>{r.d(t,{JY:()=>nV,gL:()=>lO,sx:()=>n9});var n=r(12115),l=r(47650),i=r(52),a=r(34540),o=r(29418),d=r(27380),s=r(79630);let u=/[ \t]{2,}/g,c=/^[ \t]*/gm,p=e=>e.replace(u," ").replace(c,"").trim(),f=e=>p(`
  %c@hello-pangea/dnd

  %c${p(e)}

  %c👷‍ This is a development only message. It will be removed in production builds.
`);function g(e,t){}g.bind(null,"warn");let m=g.bind(null,"error");function b(){}function h(e,t,r){let n=t.map(t=>{var n;let l=(n=t.options,{...r,...n});return e.addEventListener(t.eventName,t.fn,l),function(){e.removeEventListener(t.eventName,t.fn,l)}});return function(){n.forEach(e=>{e()})}}class y extends Error{}function v(e,t){throw new y("Invariant failed")}y.prototype.toString=function(){return this.message};class I extends n.Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=b,this.onWindowError=e=>{let t=this.getCallbacks();t.isDragging()&&t.tryAbort(),e.error instanceof y&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=h(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(e instanceof y)return void this.setState({});throw e}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}let x=e=>e+1,D=(e,t)=>{let r=e.droppableId===t.droppableId,n=x(e.index),l=x(t.index);return r?`
      You have moved the item from position ${n}
      to position ${l}
    `:`
    You have moved the item from position ${n}
    in list ${e.droppableId}
    to list ${t.droppableId}
    in position ${l}
  `},E=(e,t,r)=>t.droppableId===r.droppableId?`
      The item ${e}
      has been combined with ${r.draggableId}`:`
      The item ${e}
      in list ${t.droppableId}
      has been combined with ${r.draggableId}
      in list ${r.droppableId}
    `,A=e=>`
  The item has returned to its starting position
  of ${x(e.index)}
`,N={dragHandleUsageInstructions:`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,onDragStart:e=>`
  You have lifted an item in position ${x(e.source.index)}
`,onDragUpdate:e=>{let t=e.destination;if(t)return D(e.source,t);let r=e.combine;return r?E(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`
      Movement cancelled.
      ${A(e.source)}
    `;let t=e.destination,r=e.combine;return t?`
      You have dropped the item.
      ${D(e.source,t)}
    `:r?`
      You have dropped the item.
      ${E(e.draggableId,e.source,r)}
    `:`
    The item has been dropped while not over a drop area.
    ${A(e.source)}
  `}};function R(e,t){if(e.length!==t.length)return!1;for(let l=0;l<e.length;l++){var r,n;if(!((r=e[l])===(n=t[l])||Number.isNaN(r)&&Number.isNaN(n))&&1)return!1}return!0}function C(e,t){let r=(0,n.useState)(()=>({inputs:t,result:e()}))[0],l=(0,n.useRef)(!0),i=(0,n.useRef)(r),a=l.current||t&&i.current.inputs&&R(t,i.current.inputs)?i.current:{inputs:t,result:e()};return(0,n.useEffect)(()=>{l.current=!1,i.current=a},[a]),a.result}function P(e,t){return C(()=>e,t)}let w={x:0,y:0},O=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),S=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),B=(e,t)=>e.x===t.x&&e.y===t.y,G=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),L=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},T=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),_=(e,t)=>Math.min(...t.map(t=>T(e,t))),M=e=>t=>({x:e(t.x),y:e(t.y)});var F=(e,t)=>{let r=(0,o.l)({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r};let k=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),$=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],W=(e,t)=>t?k(e,t.scroll.diff.displacement):e,U=(e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e,H=(e,t)=>t&&t.shouldClipSubject?F(t.pageMarginBox,e):(0,o.l)(e);var V=({page:e,withPlaceholder:t,axis:r,frame:n})=>{let l=H(U(W(e.marginBox,n),r,t),n);return{page:e,withPlaceholder:t,active:l}},j=(e,t)=>{e.frame||v();let r=e.frame,n=S(t,r.scroll.initial),l=G(n),i={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:n,displacement:l},max:r.scroll.max}},a=V({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:i});return{...e,frame:i,subject:a}};function z(e,t=R){let r=null;function n(...l){if(r&&r.lastThis===this&&t(l,r.lastArgs))return r.lastResult;let i=e.apply(this,l);return r={lastResult:i,lastArgs:l,lastThis:this},i}return n.clear=function(){r=null},n}let q=z(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),Y=z(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),J=z(e=>Object.values(e)),X=z(e=>Object.values(e));var K=z((e,t)=>X(t).filter(t=>e===t.descriptor.droppableId).sort((e,t)=>e.descriptor.index-t.descriptor.index));function Q(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function Z(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var ee=z((e,t)=>t.filter(t=>t.descriptor.id!==e.descriptor.id)),et=({isMovingForward:e,draggable:t,destination:r,insideDestination:n,previousImpact:l})=>{if(!r.isCombineEnabled||!Q(l))return null;function i(e){let t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...l,at:t}}let a=l.displaced.all,o=a.length?a[0]:null;if(e)return o?i(o):null;let d=ee(t,n);if(!o)return d.length?i(d[d.length-1].descriptor.id):null;let s=d.findIndex(e=>e.descriptor.id===o);-1===s&&v();let u=s-1;return u<0?null:i(d[u].descriptor.id)},er=(e,t)=>e.descriptor.droppableId===t.descriptor.id;let en={point:w,value:0},el={invisible:{},visible:{},all:[]},ei={displaced:el,displacedBy:en,at:null};var ea=(e,t)=>r=>e<=r&&r<=t,eo=e=>{let t=ea(e.top,e.bottom),r=ea(e.left,e.right);return n=>{if(t(n.top)&&t(n.bottom)&&r(n.left)&&r(n.right))return!0;let l=t(n.top)||t(n.bottom),i=r(n.left)||r(n.right);if(l&&i)return!0;let a=n.top<e.top&&n.bottom>e.bottom,o=n.left<e.left&&n.right>e.right;return!!a&&!!o||a&&i||o&&l}},ed=e=>{let t=ea(e.top,e.bottom),r=ea(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};let es={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},eu={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};var ec=e=>t=>{let r=ea(t.top,t.bottom),n=ea(t.left,t.right);return t=>e===es?r(t.top)&&r(t.bottom):n(t.left)&&n(t.right)};let ep=(e,t)=>k(e,t.frame?t.frame.scroll.diff.displacement:w),ef=(e,t,r)=>!!t.subject.active&&r(t.subject.active)(e),eg=(e,t,r)=>r(t)(e),em=({target:e,destination:t,viewport:r,withDroppableDisplacement:n,isVisibleThroughFrameFn:l})=>{let i=n?ep(e,t):e;return ef(i,t,l)&&eg(i,r,l)},eb=e=>em({...e,isVisibleThroughFrameFn:eo}),eh=e=>em({...e,isVisibleThroughFrameFn:ed}),ey=e=>em({...e,isVisibleThroughFrameFn:ec(e.destination.axis)}),ev=(e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;let{invisible:n,visible:l}=t;if(n[e])return!1;let i=l[e];return!i||i.shouldAnimate};function eI({afterDragging:e,destination:t,displacedBy:r,viewport:n,forceShouldAnimate:l,last:i}){return e.reduce(function(e,a){let d=function(e,t){let r=e.page.marginBox,n={top:t.point.y,right:0,bottom:0,left:t.point.x};return(0,o.l)((0,o.fT)(r,n))}(a,r),s=a.descriptor.id;if(e.all.push(s),!eb({target:d,destination:t,viewport:n,withDroppableDisplacement:!0}))return e.invisible[a.descriptor.id]=!0,e;let u=ev(s,i,l);return e.visible[s]={draggableId:s,shouldAnimate:u},e},{all:[],visible:{},invisible:{}})}function ex({insideDestination:e,inHomeList:t,displacedBy:r,destination:n}){let l=function(e,t){if(!e.length)return 0;let r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:el,displacedBy:r,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:l}}}}function eD({draggable:e,insideDestination:t,destination:r,viewport:n,displacedBy:l,last:i,index:a,forceShouldAnimate:o}){let d=er(e,r);if(null==a)return ex({insideDestination:t,inHomeList:d,displacedBy:l,destination:r});let s=t.find(e=>e.descriptor.index===a);if(!s)return ex({insideDestination:t,inHomeList:d,displacedBy:l,destination:r});let u=ee(e,t),c=t.indexOf(s);return{displaced:eI({afterDragging:u.slice(c),destination:r,displacedBy:l,last:i,viewport:n.frame,forceShouldAnimate:o}),displacedBy:l,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:a}}}}function eE(e,t){return!!t.effected[e]}var eA=({isMovingForward:e,destination:t,draggables:r,combine:n,afterCritical:l})=>{if(!t.isCombineEnabled)return null;let i=n.draggableId,a=r[i].descriptor.index;return eE(i,l)?e?a:a-1:e?a+1:a},eN=({isMovingForward:e,isInHomeList:t,insideDestination:r,location:n})=>{if(!r.length)return null;let l=n.index,i=e?l+1:l-1,a=r[0].descriptor.index,o=r[r.length-1].descriptor.index;return i<a||i>(t?o:o+1)?null:i},eR=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:n,destination:l,insideDestination:i,previousImpact:a,viewport:o,afterCritical:d})=>{let s=a.at;if(s||v(),"REORDER"===s.type){let n=eN({isMovingForward:e,isInHomeList:t,location:s.destination,insideDestination:i});return null==n?null:eD({draggable:r,insideDestination:i,destination:l,viewport:o,last:a.displaced,displacedBy:a.displacedBy,index:n})}let u=eA({isMovingForward:e,destination:l,displaced:a.displaced,draggables:n,combine:s.combine,afterCritical:d});return null==u?null:eD({draggable:r,insideDestination:i,destination:l,viewport:o,last:a.displaced,displacedBy:a.displacedBy,index:u})},eC=({displaced:e,afterCritical:t,combineWith:r,displacedBy:n})=>{let l=!!(e.visible[r]||e.invisible[r]);return eE(r,t)?l?w:G(n.point):l?n.point:w},eP=({afterCritical:e,impact:t,draggables:r})=>{let n=Z(t);n||v();let l=n.draggableId;return O(r[l].page.borderBox.center,eC({displaced:t.displaced,afterCritical:e,combineWith:l,displacedBy:t.displacedBy}))};let ew=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,eO=(e,t)=>t.margin[e.end]+t.borderBox[e.size]/2,eS=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,eB=({axis:e,moveRelativeTo:t,isMoving:r})=>L(e.line,t.marginBox[e.end]+ew(e,r),eS(e,t.marginBox,r)),eG=({axis:e,moveRelativeTo:t,isMoving:r})=>L(e.line,t.marginBox[e.start]-eO(e,r),eS(e,t.marginBox,r)),eL=({axis:e,moveInto:t,isMoving:r})=>L(e.line,t.contentBox[e.start]+ew(e,r),eS(e,t.contentBox,r));var eT=({impact:e,draggable:t,draggables:r,droppable:n,afterCritical:l})=>{let i=K(n.descriptor.id,r),a=t.page,d=n.axis;if(!i.length)return eL({axis:d,moveInto:n.page,isMoving:a});let{displaced:s,displacedBy:u}=e,c=s.all[0];if(c){let e=r[c];return eE(c,l)?eG({axis:d,moveRelativeTo:e.page,isMoving:a}):eG({axis:d,moveRelativeTo:(0,o.cY)(e.page,u.point),isMoving:a})}let p=i[i.length-1];return p.descriptor.id===t.descriptor.id?a.borderBox.center:eE(p.descriptor.id,l)?eB({axis:d,moveRelativeTo:(0,o.cY)(p.page,G(l.displacedBy.point)),isMoving:a}):eB({axis:d,moveRelativeTo:p.page,isMoving:a})},e_=(e,t)=>{let r=e.frame;return r?O(t,r.scroll.diff.displacement):t};let eM=({impact:e,draggable:t,droppable:r,draggables:n,afterCritical:l})=>{let i=t.page.borderBox.center,a=e.at;return r&&a?"REORDER"===a.type?eT({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:l}):eP({impact:e,draggables:n,afterCritical:l}):i};var eF=e=>{let t=eM(e),r=e.droppable;return r?e_(r,t):t},ek=(e,t)=>{let r=S(t,e.scroll.initial),n=G(r);return{frame:(0,o.l)({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:n}}}};function e$(e,t){return e.map(e=>t[e])}var eW=({impact:e,viewport:t,destination:r,draggables:n,maxScrollChange:l})=>{let i=ek(t,O(t.scroll.current,l)),a=r.frame?j(r,O(r.frame.scroll.current,l)):r,o=e.displaced,d=eI({afterDragging:e$(o.all,n),destination:r,displacedBy:e.displacedBy,viewport:i.frame,last:o,forceShouldAnimate:!1}),s=eI({afterDragging:e$(o.all,n),destination:a,displacedBy:e.displacedBy,viewport:t.frame,last:o,forceShouldAnimate:!1}),u={},c={},p=[o,d,s];return o.all.forEach(e=>{let t=function(e,t){for(let r=0;r<t.length;r++){let n=t[r].visible[e];if(n)return n}return null}(e,p);if(t){c[e]=t;return}u[e]=!0}),{...e,displaced:{all:o.all,invisible:u,visible:c}}},eU=(e,t)=>O(e.scroll.diff.displacement,t),eH=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{let n=S(eU(r,e),t.page.borderBox.center);return O(t.client.borderBox.center,n)},eV=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:n,withDroppableDisplacement:l,onlyOnMainAxis:i=!1})=>{let a=S(r,e.page.borderBox.center),o={target:k(e.page.borderBox,a),destination:t,withDroppableDisplacement:l,viewport:n};return i?ey(o):eh(o)},ej=({isMovingForward:e,draggable:t,destination:r,draggables:n,previousImpact:l,viewport:i,previousPageBorderBoxCenter:a,previousClientSelection:o,afterCritical:d})=>{if(!r.isEnabled)return null;let s=K(r.descriptor.id,n),u=er(t,r),c=et({isMovingForward:e,draggable:t,destination:r,insideDestination:s,previousImpact:l})||eR({isMovingForward:e,isInHomeList:u,draggable:t,draggables:n,destination:r,insideDestination:s,previousImpact:l,viewport:i,afterCritical:d});if(!c)return null;let p=eF({impact:c,draggable:t,droppable:r,draggables:n,afterCritical:d});if(eV({draggable:t,destination:r,newPageBorderBoxCenter:p,viewport:i.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:eH({pageBorderBoxCenter:p,draggable:t,viewport:i}),impact:c,scrollJumpRequest:null};let f=S(p,a);return{clientSelection:o,impact:eW({impact:c,viewport:i,destination:r,draggables:n,maxScrollChange:f}),scrollJumpRequest:f}};let ez=e=>{let t=e.subject.active;return t||v(),t};var eq=({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:n,viewport:l})=>{let i=r.subject.active;if(!i)return null;let a=r.axis,o=ea(i[a.start],i[a.end]),d=J(n).filter(e=>e!==r).filter(e=>e.isEnabled).filter(e=>!!e.subject.active).filter(e=>eo(l.frame)(ez(e))).filter(t=>{let r=ez(t);return e?i[a.crossAxisEnd]<r[a.crossAxisEnd]:r[a.crossAxisStart]<i[a.crossAxisStart]}).filter(e=>{let t=ez(e),r=ea(t[a.start],t[a.end]);return o(t[a.start])||o(t[a.end])||r(i[a.start])||r(i[a.end])}).sort((t,r)=>{let n=ez(t)[a.crossAxisStart],l=ez(r)[a.crossAxisStart];return e?n-l:l-n}).filter((e,t,r)=>ez(e)[a.crossAxisStart]===ez(r[0])[a.crossAxisStart]);if(!d.length)return null;if(1===d.length)return d[0];let s=d.filter(e=>ea(ez(e)[a.start],ez(e)[a.end])(t[a.line]));return 1===s.length?s[0]:s.length>1?s.sort((e,t)=>ez(e)[a.start]-ez(t)[a.start])[0]:d.sort((e,r)=>{let n=_(t,$(ez(e))),l=_(t,$(ez(r)));return n!==l?n-l:ez(e)[a.start]-ez(r)[a.start]})[0]};let eY=(e,t)=>{let r=e.page.borderBox.center;return eE(e.descriptor.id,t)?S(r,t.displacedBy.point):r},eJ=(e,t)=>{let r=e.page.borderBox;return eE(e.descriptor.id,t)?k(r,G(t.displacedBy.point)):r};var eX=({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:n,afterCritical:l})=>n.filter(e=>eh({target:eJ(e,l),destination:r,viewport:t.frame,withDroppableDisplacement:!0})).sort((t,n)=>{let i=T(e,e_(r,eY(t,l))),a=T(e,e_(r,eY(n,l)));return i<a?-1:a<i?1:t.descriptor.index-n.descriptor.index})[0]||null,eK=z(function(e,t){let r=t[e.line];return{value:r,point:L(e.line,r)}});let eQ=(e,t,r)=>{let n=e.axis;if("virtual"===e.descriptor.mode)return L(n.line,t[n.line]);let l=e.subject.page.contentBox[n.size],i=K(e.descriptor.id,r).reduce((e,t)=>e+t.client.marginBox[n.size],0)+t[n.line]-l;return i<=0?null:L(n.line,i)},eZ=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),e0=(e,t,r)=>{let n=e.frame;er(t,e)&&v(),e.subject.withPlaceholder&&v();let l=eK(e.axis,t.displaceBy).point,i=eQ(e,l,r),a={placeholderSize:l,increasedBy:i,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!n){let t=V({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:e.frame});return{...e,subject:t}}let o=i?O(n.scroll.max,i):n.scroll.max,d=eZ(n,o),s=V({page:e.subject.page,withPlaceholder:a,axis:e.axis,frame:d});return{...e,subject:s,frame:d}},e1=e=>{let t=e.subject.withPlaceholder;t||v();let r=e.frame;if(!r){let t=V({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}let n=t.oldFrameMaxScroll;n||v();let l=eZ(r,n),i=V({page:e.subject.page,axis:e.axis,frame:l,withPlaceholder:null});return{...e,subject:i,frame:l}};var e2=({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:n,draggables:l,destination:i,viewport:a,afterCritical:o})=>{if(!t){if(r.length)return null;let e={displaced:el,displacedBy:en,at:{type:"REORDER",destination:{droppableId:i.descriptor.id,index:0}}},t=eF({impact:e,draggable:n,droppable:i,draggables:l,afterCritical:o}),d=er(n,i)?i:e0(i,n,l);return eV({draggable:n,destination:d,newPageBorderBoxCenter:t,viewport:a.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}let d=e[i.axis.line]<=t.page.borderBox.center[i.axis.line],s=(()=>{let e=t.descriptor.index;return t.descriptor.id===n.descriptor.id||d?e:e+1})(),u=eK(i.axis,n.displaceBy);return eD({draggable:n,insideDestination:r,destination:i,viewport:a,displacedBy:u,last:el,index:s})},e3=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:n,draggables:l,droppables:i,viewport:a,afterCritical:o})=>{let d=eq({isMovingForward:e,pageBorderBoxCenter:t,source:n,droppables:i,viewport:a});if(!d)return null;let s=K(d.descriptor.id,l),u=eX({pageBorderBoxCenter:t,viewport:a,destination:d,insideDestination:s,afterCritical:o}),c=e2({previousPageBorderBoxCenter:t,destination:d,draggable:r,draggables:l,moveRelativeTo:u,insideDestination:s,viewport:a,afterCritical:o});return c?{clientSelection:eH({pageBorderBoxCenter:eF({impact:c,draggable:r,droppable:d,draggables:l,afterCritical:o}),draggable:r,viewport:a}),impact:c,scrollJumpRequest:null}:null},e5=e=>{let t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};let e4=(e,t)=>{let r=e5(e);return r?t[r]:null};var e7=({state:e,type:t})=>{let r=e4(e.impact,e.dimensions.droppables),n=!!r,l=e.dimensions.droppables[e.critical.droppable.id],i=r||l,a=i.axis.direction,o="vertical"===a&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===a&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(o&&!n)return null;let d="MOVE_DOWN"===t||"MOVE_RIGHT"===t,s=e.dimensions.draggables[e.critical.draggable.id],u=e.current.page.borderBoxCenter,{draggables:c,droppables:p}=e.dimensions;return o?ej({isMovingForward:d,previousPageBorderBoxCenter:u,draggable:s,destination:i,draggables:c,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):e3({isMovingForward:d,previousPageBorderBoxCenter:u,draggable:s,isOver:i,draggables:c,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function e9(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function e6(e){let t=ea(e.top,e.bottom),r=ea(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}let e8=(e,t)=>(0,o.l)(k(e,t));var te=(e,t)=>{let r=e.frame;return r?e8(t,r.scroll.diff.value):t};function tt({displaced:e,id:t}){return!!(e.visible[t]||e.invisible[t])}var tr=({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:n,last:l,viewport:i,afterCritical:a})=>{let o=r.axis,d=eK(r.axis,t.displaceBy),s=d.value,u=e[o.start],c=e[o.end],p=ee(t,n).find(e=>{let t=e.descriptor.id,r=e.page.borderBox.center[o.line],n=eE(t,a),i=tt({displaced:l,id:t});return n?i?c<=r:u<r-s:i?c<=r+s:u<r})||null,f=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:p,inHomeList:er(t,r)});return eD({draggable:t,insideDestination:n,destination:r,viewport:i,last:l,displacedBy:d,index:f})},tn=({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:n,insideDestination:l,afterCritical:i})=>{if(!n.isCombineEnabled)return null;let a=n.axis,o=eK(n.axis,e.displaceBy),d=o.value,s=t[a.start],u=t[a.end],c=ee(e,l).find(e=>{let t=e.descriptor.id,n=e.page.borderBox,l=n[a.size]/4,o=eE(t,i),c=tt({displaced:r.displaced,id:t});return o?c?u>n[a.start]+l&&u<n[a.end]-l:s>n[a.start]-d+l&&s<n[a.end]-d-l:c?u>n[a.start]+d+l&&u<n[a.end]+d-l:s>n[a.start]+l&&s<n[a.end]-l});return c?{displacedBy:o,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:c.descriptor.id,droppableId:n.descriptor.id}}}:null},tl=({pageOffset:e,draggable:t,draggables:r,droppables:n,previousImpact:l,viewport:i,afterCritical:a})=>{let o=e8(t.page.borderBox,e),d=function({pageBorderBox:e,draggable:t,droppables:r}){let n=J(r).filter(t=>{if(!t.isEnabled)return!1;let r=t.subject.active;if(!r||!(e.left<r.right)||!(e.right>r.left)||!(e.top<r.bottom)||!(e.bottom>r.top))return!1;if(e6(r)(e.center))return!0;let n=t.axis,l=r.center[n.crossAxisLine],i=e[n.crossAxisStart],a=e[n.crossAxisEnd],o=ea(r[n.crossAxisStart],r[n.crossAxisEnd]),d=o(i),s=o(a);return!d&&!s||(d?i<l:a>l)});return n.length?1===n.length?n[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){let n=t.page.borderBox.center,l=r.map(t=>{let r=t.axis,l=L(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:T(n,l)}}).sort((e,t)=>t.distance-e.distance);return l[0]?l[0].id:null}({pageBorderBox:e,draggable:t,candidates:n}):null}({pageBorderBox:o,draggable:t,droppables:n});if(!d)return ei;let s=n[d],u=K(s.descriptor.id,r),c=te(s,o);return tn({pageBorderBoxWithDroppableScroll:c,draggable:t,previousImpact:l,destination:s,insideDestination:u,afterCritical:a})||tr({pageBorderBoxWithDroppableScroll:c,draggable:t,destination:s,insideDestination:u,last:l.displaced,viewport:i,afterCritical:a})},ti=(e,t)=>({...e,[t.descriptor.id]:t});let ta=({previousImpact:e,impact:t,droppables:r})=>{let n=e5(e),l=e5(t);if(!n||n===l)return r;let i=r[n];return i.subject.withPlaceholder?ti(r,e1(i)):r};var to=({draggable:e,draggables:t,droppables:r,previousImpact:n,impact:l})=>{let i=ta({previousImpact:n,impact:l,droppables:r}),a=e5(l);if(!a)return i;let o=r[a];return er(e,o)||o.subject.withPlaceholder?i:ti(i,e0(o,e,t))},td=({state:e,clientSelection:t,dimensions:r,viewport:n,impact:l,scrollJumpRequest:i})=>{let a=n||e.viewport,o=r||e.dimensions,d=t||e.current.client.selection,s=S(d,e.initial.client.selection),u={offset:s,selection:d,borderBoxCenter:O(e.initial.client.borderBoxCenter,s)},c={selection:O(u.selection,a.scroll.current),borderBoxCenter:O(u.borderBoxCenter,a.scroll.current),offset:O(u.offset,a.scroll.diff.value)},p={client:u,page:c};if("COLLECTING"===e.phase)return{...e,dimensions:o,viewport:a,current:p};let f=o.draggables[e.critical.draggable.id],g=l||tl({pageOffset:c.offset,draggable:f,draggables:o.draggables,droppables:o.droppables,previousImpact:e.impact,viewport:a,afterCritical:e.afterCritical}),m=to({draggable:f,impact:g,previousImpact:e.impact,draggables:o.draggables,droppables:o.droppables});return{...e,current:p,dimensions:{draggables:o.draggables,droppables:m},impact:g,viewport:a,scrollJumpRequest:i||null,forceShouldAnimate:!i&&null}},ts=({impact:e,viewport:t,draggables:r,destination:n,forceShouldAnimate:l})=>{var i;let a=e.displaced,o=eI({afterDragging:(i=a.all,i.map(e=>r[e])),destination:n,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:l,last:a});return{...e,displaced:o}},tu=({impact:e,draggable:t,droppable:r,draggables:n,viewport:l,afterCritical:i})=>eH({pageBorderBoxCenter:eF({impact:e,draggable:t,draggables:n,droppable:r,afterCritical:i}),draggable:t,viewport:l}),tc=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&v();let n=e.impact,l=r||e.viewport,i=t||e.dimensions,{draggables:a,droppables:o}=i,d=a[e.critical.draggable.id],s=e5(n);s||v();let u=o[s],c=ts({impact:n,viewport:l,destination:u,draggables:a}),p=tu({impact:c,draggable:d,droppable:u,draggables:a,viewport:l,afterCritical:e.afterCritical});return td({impact:c,clientSelection:p,state:e,dimensions:i,viewport:l})},tp=e=>({index:e.index,droppableId:e.droppableId}),tf=({draggable:e,home:t,draggables:r,viewport:n})=>{let l=eK(t.axis,e.displaceBy),i=K(t.descriptor.id,r),a=i.indexOf(e);-1===a&&v();let o=i.slice(a+1),d=o.reduce((e,t)=>(e[t.descriptor.id]=!0,e),{}),s={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:l,effected:d};return{impact:{displaced:eI({afterDragging:o,destination:t,displacedBy:l,last:null,viewport:n.frame,forceShouldAnimate:!1}),displacedBy:l,at:{type:"REORDER",destination:tp(e.descriptor)}},afterCritical:s}},tg=(e,t)=>({draggables:e.draggables,droppables:ti(e.droppables,t)});let tm=e=>{},tb=e=>{};var th=({draggable:e,offset:t,initialWindowScroll:r})=>{let n=(0,o.cY)(e.client,t),l=(0,o.SQ)(n,r);return{...e,placeholder:{...e.placeholder,client:n},client:n,page:l}},ty=e=>{let t=e.frame;return t||v(),t},tv=({additions:e,updatedDroppables:t,viewport:r})=>{let n=r.scroll.diff.value;return e.map(e=>{let l=O(n,ty(t[e.descriptor.droppableId]).scroll.diff.value);return th({draggable:e,offset:l,initialWindowScroll:r.scroll.initial})})},tI=({state:e,published:t})=>{tm();let r=t.modified.map(t=>j(e.dimensions.droppables[t.droppableId],t.scroll)),n={...e.dimensions.droppables,...q(r)},l=Y(tv({additions:t.additions,updatedDroppables:n,viewport:e.viewport})),i={...e.dimensions.draggables,...l};t.removals.forEach(e=>{delete i[e]});let a={droppables:n,draggables:i},o=e5(e.impact),d=o?a.droppables[o]:null,{impact:s,afterCritical:u}=tf({draggable:a.draggables[e.critical.draggable.id],home:a.droppables[e.critical.droppable.id],draggables:i,viewport:e.viewport}),c=d&&d.isCombineEnabled?e.impact:s,p=tl({pageOffset:e.current.page.offset,draggable:a.draggables[e.critical.draggable.id],draggables:a.draggables,droppables:a.droppables,previousImpact:c,viewport:e.viewport,afterCritical:u});tb();let f={...e,phase:"DRAGGING",impact:p,onLiftImpact:s,dimensions:a,afterCritical:u,forceShouldAnimate:!1};return"COLLECTING"===e.phase?f:{...f,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}};let tx=e=>"SNAP"===e.movementMode,tD=(e,t,r)=>{let n=tg(e.dimensions,t);return!tx(e)||r?td({state:e,dimensions:n}):tc({state:e,dimensions:n})};function tE(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}let tA={phase:"IDLE",completed:null,shouldFlush:!1};var tN=(e=tA,t)=>{if("FLUSH"===t.type)return{...tA,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&v();let{critical:r,clientSelection:n,viewport:l,dimensions:i,movementMode:a}=t.payload,o=i.draggables[r.draggable.id],d=i.droppables[r.droppable.id],s={selection:n,borderBoxCenter:o.client.borderBox.center,offset:w},u={client:s,page:{selection:O(s.selection,l.scroll.initial),borderBoxCenter:O(s.selection,l.scroll.initial),offset:O(s.selection,l.scroll.diff.value)}},c=J(i.droppables).every(e=>!e.isFixedOnPage),{impact:p,afterCritical:f}=tf({draggable:o,home:d,draggables:i.draggables,viewport:l});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:a,dimensions:i,initial:u,current:u,isWindowScrollAllowed:c,impact:p,afterCritical:f,onLiftImpact:p,viewport:l,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&v(),{...e,phase:"COLLECTING"});if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&v(),tI({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;e9(e)||v();let{client:r}=t.payload;return B(r,e.current.client.selection)?e:td({state:e,clientSelection:r,impact:tx(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"COLLECTING"===e.phase)return tE(e);e9(e)||v();let{id:r,newScroll:n}=t.payload,l=e.dimensions.droppables[r];return l?tD(e,j(l,n),!1):e}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;e9(e)||v();let{id:r,isEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||v(),l.isEnabled===n&&v(),tD(e,{...l,isEnabled:n},!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;e9(e)||v();let{id:r,isCombineEnabled:n}=t.payload,l=e.dimensions.droppables[r];return l||v(),l.isCombineEnabled===n&&v(),tD(e,{...l,isCombineEnabled:n},!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;e9(e)||v(),e.isWindowScrollAllowed||v();let r=t.payload.newScroll;if(B(e.viewport.scroll.current,r))return tE(e);let n=ek(e.viewport,r);return tx(e)?tc({state:e,viewport:n}):td({state:e,viewport:n})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!e9(e))return e;let r=t.payload.maxScroll;if(B(r,e.viewport.scroll.max))return e;let n={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:n}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&v();let r=e7({state:e,type:t.type});return r?td({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){let r=t.payload.reason;return"COLLECTING"!==e.phase&&v(),{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){let{completed:r,dropDuration:n,newHomeClientOffset:l}=t.payload;return"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&v(),{phase:"DROP_ANIMATING",completed:r,dropDuration:n,newHomeClientOffset:l,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){let{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function tR(e,t){return e instanceof Object&&"type"in e&&e.type===t}let tC=e=>({type:"BEFORE_INITIAL_CAPTURE",payload:e}),tP=e=>({type:"LIFT",payload:e}),tw=e=>({type:"INITIAL_PUBLISH",payload:e}),tO=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),tS=()=>({type:"COLLECTION_STARTING",payload:null}),tB=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),tG=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),tL=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),tT=e=>({type:"MOVE",payload:e}),t_=e=>({type:"MOVE_BY_WINDOW_SCROLL",payload:e}),tM=()=>({type:"MOVE_UP",payload:null}),tF=()=>({type:"MOVE_DOWN",payload:null}),tk=()=>({type:"MOVE_RIGHT",payload:null}),t$=()=>({type:"MOVE_LEFT",payload:null}),tW=()=>({type:"FLUSH",payload:null}),tU=e=>({type:"DROP_ANIMATE",payload:e}),tH=e=>({type:"DROP_COMPLETE",payload:e}),tV=e=>({type:"DROP",payload:e}),tj=e=>({type:"DROP_PENDING",payload:e}),tz=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});var tq=e=>({getState:t,dispatch:r})=>n=>l=>{if(!tR(l,"LIFT"))return void n(l);let{id:i,clientSelection:a,movementMode:o}=l.payload,d=t();"DROP_ANIMATING"===d.phase&&r(tH({completed:d.completed})),"IDLE"!==t().phase&&v(),r(tW()),r(tC({draggableId:i,movementMode:o}));let{critical:s,dimensions:u,viewport:c}=e.startPublishing({draggableId:i,scrollOptions:{shouldPublishImmediately:"SNAP"===o}});r(tw({critical:s,dimensions:u,clientSelection:a,movementMode:o,viewport:c}))},tY=e=>()=>t=>r=>{tR(r,"INITIAL_PUBLISH")&&e.dragging(),tR(r,"DROP_ANIMATE")&&e.dropping(r.payload.completed.result.reason),(tR(r,"FLUSH")||tR(r,"DROP_COMPLETE"))&&e.resting(),t(r)};let tJ={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},tX={opacity:{drop:0,combining:.7},scale:{drop:.75}},tK={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},tQ=`${tK.outOfTheWay}s ${tJ.outOfTheWay}`,tZ={fluid:`opacity ${tQ}`,snap:`transform ${tQ}, opacity ${tQ}`,drop:e=>{let t=`${e}s ${tJ.drop}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${tQ}`,placeholder:`height ${tQ}, width ${tQ}, margin ${tQ}`},t0=e=>B(e,w)?void 0:`translate(${e.x}px, ${e.y}px)`,t1={moveTo:t0,drop:(e,t)=>{let r=t0(e);if(r)return t?`${r} scale(${tX.scale.drop})`:r}},{minDropTime:t2,maxDropTime:t3}=tK,t5=t3-t2;var t4=({current:e,destination:t,reason:r})=>{let n=T(e,t);if(n<=0)return t2;if(n>=1500)return t3;let l=t2+n/1500*t5;return Number(("CANCEL"===r?.6*l:l).toFixed(2))},t7=({impact:e,draggable:t,dimensions:r,viewport:n,afterCritical:l})=>{let{draggables:i,droppables:a}=r,o=e5(e),d=o?a[o]:null,s=a[t.descriptor.droppableId];return S(tu({impact:e,draggable:t,draggables:i,afterCritical:l,droppable:d||s,viewport:n}),t.client.borderBox.center)},t9=({draggables:e,reason:t,lastImpact:r,home:n,viewport:l,onLiftImpact:i})=>r.at&&"DROP"===t?"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:el},didDropInsideDroppable:!0}:{impact:ts({draggables:e,impact:i,destination:n,viewport:l,forceShouldAnimate:!0}),didDropInsideDroppable:!1};let t6=({getState:e,dispatch:t})=>r=>n=>{if(!tR(n,"DROP"))return void r(n);let l=e(),i=n.payload.reason;if("COLLECTING"===l.phase)return void t(tj({reason:i}));if("IDLE"===l.phase)return;"DROP_PENDING"===l.phase&&l.isWaiting&&v(),"DRAGGING"!==l.phase&&"DROP_PENDING"!==l.phase&&v();let a=l.critical,o=l.dimensions,d=o.draggables[l.critical.draggable.id],{impact:s,didDropInsideDroppable:u}=t9({reason:i,lastImpact:l.impact,afterCritical:l.afterCritical,onLiftImpact:l.onLiftImpact,home:l.dimensions.droppables[l.critical.droppable.id],viewport:l.viewport,draggables:l.dimensions.draggables}),c=u?Q(s):null,p=u?Z(s):null,f={index:a.draggable.index,droppableId:a.droppable.id},g={draggableId:d.descriptor.id,type:d.descriptor.type,source:f,reason:i,mode:l.movementMode,destination:c,combine:p},m=t7({impact:s,draggable:d,dimensions:o,viewport:l.viewport,afterCritical:l.afterCritical}),b={critical:l.critical,afterCritical:l.afterCritical,result:g,impact:s};if(!(!B(l.current.client.offset,m)||g.combine))return void t(tH({completed:b}));let h=t4({current:l.current.client.offset,destination:m,reason:i});t(tU({newHomeClientOffset:m,dropDuration:h,completed:b}))};var t8=()=>({x:window.pageXOffset,y:window.pageYOffset});let re=e=>tR(e,"DROP_COMPLETE")||tR(e,"DROP_ANIMATE")||tR(e,"FLUSH"),rt=e=>{let t=function({onWindowScroll:e}){let t=(0,d.A)(function(){e(t8())}),r={eventName:"scroll",options:{passive:!0,capture:!1},fn:e=>{(e.target===window||e.target===window.document)&&t()}},n=b;function l(){return n!==b}return{start:function(){l()&&v(),n=h(window,[r])},stop:function(){l()||v(),t.cancel(),n(),n=b},isActive:l}}({onWindowScroll:t=>{e.dispatch(t_({newScroll:t}))}});return e=>r=>{!t.isActive()&&tR(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&re(r)&&t.stop(),e(r)}};var rr=e=>{let t=!1,r=!1,n=setTimeout(()=>{r=!0}),l=l=>{!t&&(r||(t=!0,e(l),clearTimeout(n)))};return l.wasCalled=()=>t,l},rn=()=>{let e=[],t=t=>{let r=e.findIndex(e=>e.timerId===t);-1===r&&v();let[n]=e.splice(r,1);n.callback()};return{add:r=>{let n=setTimeout(()=>t(n));e.push({timerId:n,callback:r})},flush:()=>{if(!e.length)return;let t=[...e];e.length=0,t.forEach(e=>{clearTimeout(e.timerId),e.callback()})}}};let rl=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.droppableId===t.droppableId&&e.index===t.index,ri=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId,ra=(e,t)=>{if(e===t)return!0;let r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,n=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&n},ro=(e,t)=>{tm(),t(),tb()},rd=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function rs(e,t,r,n){if(!e)return void r(n(t));let l=rr(r);e(t,{announce:l}),l.wasCalled()||r(n(t))}var ru=(e,t)=>{let r=rn(),n=null,l=r=>{n||v(),n=null,ro("onDragEnd",()=>rs(e().onDragEnd,r,t,N.onDragEnd))};return{beforeCapture:(t,r)=>{n&&v(),ro("onBeforeCapture",()=>{let n=e().onBeforeCapture;n&&n({draggableId:t,mode:r})})},beforeStart:(t,r)=>{n&&v(),ro("onBeforeDragStart",()=>{let n=e().onBeforeDragStart;n&&n(rd(t,r))})},start:(l,i)=>{n&&v();let a=rd(l,i);n={mode:i,lastCritical:l,lastLocation:a.source,lastCombine:null},r.add(()=>{ro("onDragStart",()=>rs(e().onDragStart,a,t,N.onDragStart))})},update:(l,i)=>{let a=Q(i),o=Z(i);n||v();let d=!ra(l,n.lastCritical);d&&(n.lastCritical=l);let s=!rl(n.lastLocation,a);s&&(n.lastLocation=a);let u=!ri(n.lastCombine,o);if(u&&(n.lastCombine=o),!d&&!s&&!u)return;let c={...rd(l,n.mode),combine:o,destination:a};r.add(()=>{ro("onDragUpdate",()=>rs(e().onDragUpdate,c,t,N.onDragUpdate))})},flush:()=>{n||v(),r.flush()},drop:l,abort:()=>{n&&l({...rd(n.lastCritical,n.mode),combine:null,destination:null,reason:"CANCEL"})}}},rc=(e,t)=>{let r=ru(e,t);return e=>t=>n=>{if(tR(n,"BEFORE_INITIAL_CAPTURE"))return void r.beforeCapture(n.payload.draggableId,n.payload.movementMode);if(tR(n,"INITIAL_PUBLISH")){let e=n.payload.critical;r.beforeStart(e,n.payload.movementMode),t(n),r.start(e,n.payload.movementMode);return}if(tR(n,"DROP_COMPLETE")){let e=n.payload.completed.result;r.flush(),t(n),r.drop(e);return}if(t(n),tR(n,"FLUSH"))return void r.abort();let l=e.getState();"DRAGGING"===l.phase&&r.update(l.critical,l.impact)}};let rp=e=>t=>r=>{if(!tR(r,"DROP_ANIMATION_FINISHED"))return void t(r);let n=e.getState();"DROP_ANIMATING"!==n.phase&&v(),e.dispatch(tH({completed:n.completed}))},rf=e=>{let t=null,r=null;return n=>l=>{if((tR(l,"FLUSH")||tR(l,"DROP_COMPLETE")||tR(l,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),n(l),!tR(l,"DROP_ANIMATE"))return;let i={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch(tz())}};r=requestAnimationFrame(()=>{r=null,t=h(window,[i])})}};var rg=e=>()=>t=>r=>{(tR(r,"DROP_COMPLETE")||tR(r,"FLUSH")||tR(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)},rm=e=>{let t=!1;return()=>r=>n=>{if(tR(n,"INITIAL_PUBLISH")){t=!0,e.tryRecordFocus(n.payload.critical.draggable.id),r(n),e.tryRestoreFocusRecorded();return}if(r(n),t){if(tR(n,"FLUSH")){t=!1,e.tryRestoreFocusRecorded();return}if(tR(n,"DROP_COMPLETE")){t=!1;let r=n.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}};let rb=e=>tR(e,"DROP_COMPLETE")||tR(e,"DROP_ANIMATE")||tR(e,"FLUSH");var rh=e=>t=>r=>n=>{if(rb(n)){e.stop(),r(n);return}if(tR(n,"INITIAL_PUBLISH")){r(n);let l=t.getState();"DRAGGING"!==l.phase&&v(),e.start(l);return}r(n),e.scroll(t.getState())};let ry=e=>t=>r=>{if(t(r),!tR(r,"PUBLISH_WHILE_DRAGGING"))return;let n=e.getState();"DROP_PENDING"===n.phase&&(n.isWaiting||e.dispatch(tV({reason:n.reason})))},rv=i.Zz;var rI=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:n,announce:l,autoScroller:a})=>(0,i.y$)(tN,rv((0,i.Tw)(tY(r),rg(e),tq(e),t6,rp,rf,ry,rh(a),rt,rm(t),rc(n,l))));let rx=()=>({additions:{},removals:{},modified:{}});var rD=({scrollHeight:e,scrollWidth:t,height:r,width:n})=>{let l=S({x:t,y:e},{x:n,y:r});return{x:Math.max(0,l.x),y:Math.max(0,l.y)}},rE=()=>{let e=document.documentElement;return e||v(),e},rA=()=>{let e=rE();return rD({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},rN=()=>{let e=t8(),t=rA(),r=e.y,n=e.x,l=rE(),i=l.clientWidth,a=l.clientHeight;return{frame:(0,o.l)({top:r,left:n,right:n+i,bottom:r+a}),scroll:{initial:e,current:e,max:t,diff:{value:w,displacement:w}}}},rR=({critical:e,scrollOptions:t,registry:r})=>{tm();let n=rN(),l=n.scroll.current,i=e.droppable,a=r.droppable.getAllByType(i.type).map(e=>e.callbacks.getDimensionAndWatchScroll(l,t)),o={draggables:Y(r.draggable.getAllByType(e.draggable.type).map(e=>e.getDimension(l))),droppables:q(a)};return tb(),{dimensions:o,critical:e,viewport:n}};function rC(e,t,r){return r.descriptor.id!==t.id&&r.descriptor.type===t.type&&"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var rP=(e,t)=>{let r=null,n=function({registry:e,callbacks:t}){let r=rx(),n=null,l=()=>{n||(t.collectionStarting(),n=requestAnimationFrame(()=>{n=null,tm();let{additions:l,removals:i,modified:a}=r,o=Object.keys(l).map(t=>e.draggable.getById(t).getDimension(w)).sort((e,t)=>e.descriptor.index-t.descriptor.index),d=Object.keys(a).map(t=>{let r=e.droppable.getById(t).callbacks.getScrollWhileDragging();return{droppableId:t,scroll:r}}),s={additions:o,removals:Object.keys(i),modified:d};r=rx(),tb(),t.publish(s)}))};return{add:e=>{let t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],l()},remove:e=>{let t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],l()},stop:()=>{n&&(cancelAnimationFrame(n),n=null,r=rx())}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),l=t=>{r||v();let l=r.critical.draggable;"ADDITION"===t.type&&rC(e,l,t.value)&&n.add(t.value),"REMOVAL"===t.type&&rC(e,l,t.value)&&n.remove(t.value)};return{updateDroppableIsEnabled:(n,l)=>{e.droppable.exists(n)||v(),r&&t.updateDroppableIsEnabled({id:n,isEnabled:l})},updateDroppableIsCombineEnabled:(n,l)=>{r&&(e.droppable.exists(n)||v(),t.updateDroppableIsCombineEnabled({id:n,isCombineEnabled:l}))},scrollDroppable:(t,n)=>{r&&e.droppable.getById(t).callbacks.scroll(n)},updateDroppableScroll:(n,l)=>{r&&(e.droppable.exists(n)||v(),t.updateDroppableScroll({id:n,newScroll:l}))},startPublishing:t=>{r&&v();let n=e.draggable.getById(t.draggableId),i=e.droppable.getById(n.descriptor.droppableId),a={draggable:n.descriptor,droppable:i.descriptor};return r={critical:a,unsubscribe:e.subscribe(l)},rR({critical:a,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;n.stop();let t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach(e=>e.callbacks.dragStopped()),r.unsubscribe(),r=null}}},rw=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason,rO=e=>{window.scrollBy(e.x,e.y)};let rS=z(e=>J(e).filter(e=>!!e.isEnabled&&!!e.frame)),rB=(e,t)=>rS(t).find(t=>(t.frame||v(),e6(t.frame.pageMarginBox)(e)))||null;var rG=({center:e,destination:t,droppables:r})=>{if(t){let e=r[t];return e.frame?e:null}return rB(e,r)};let rL={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var rT=(e,t,r=()=>rL)=>{let n=r(),l=e[t.size]*n.startFromPercentage;return{startScrollingFrom:l,maxScrollValueAt:e[t.size]*n.maxScrollAtPercentage}},r_=({startOfRange:e,endOfRange:t,current:r})=>{let n=t-e;return 0===n?0:(r-e)/n},rM=(e,t,r=()=>rL)=>{let n=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return n.maxPixelScroll;if(e===t.startScrollingFrom)return 1;let l=r_({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e});return Math.ceil(n.maxPixelScroll*n.ease(1-l))},rF=(e,t,r)=>{let n=r(),l=n.durationDampening.accelerateAt,i=n.durationDampening.stopDampeningAt,a=Date.now()-t;if(a>=i)return e;if(a<l)return 1;let o=r_({startOfRange:l,endOfRange:i,current:a});return Math.ceil(e*n.ease(o))},rk=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:n,getAutoScrollerOptions:l})=>{let i=rM(e,t,l);return 0===i?0:n?Math.max(rF(i,r,l),1):i},r$=({container:e,distanceToEdges:t,dragStartTime:r,axis:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=rT(e,n,i);return t[n.end]<t[n.start]?rk({distanceToEdge:t[n.end],thresholds:a,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i}):-1*rk({distanceToEdge:t[n.start],thresholds:a,dragStartTime:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i})},rW=({container:e,subject:t,proposedScroll:r})=>{let n=t.height>e.height,l=t.width>e.width;return l||n?l&&n?null:{x:l?0:r.x,y:n?0:r.y}:r};let rU=M(e=>0===e?0:e);var rH=({dragStartTime:e,container:t,subject:r,center:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a={top:n.y-t.top,right:t.right-n.x,bottom:t.bottom-n.y,left:n.x-t.left},o=r$({container:t,distanceToEdges:a,dragStartTime:e,axis:es,shouldUseTimeDampening:l,getAutoScrollerOptions:i}),d=rU({x:r$({container:t,distanceToEdges:a,dragStartTime:e,axis:eu,shouldUseTimeDampening:l,getAutoScrollerOptions:i}),y:o});if(B(d,w))return null;let s=rW({container:t,subject:r,proposedScroll:d});return s?B(s,w)?null:s:null};let rV=M(e=>0===e?0:e>0?1:-1),rj=(()=>{let e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:n})=>{let l=O(t,n),i={x:e(l.x,r.x),y:e(l.y,r.y)};return B(i,w)?null:i}})(),rz=({max:e,current:t,change:r})=>{let n={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},l=rV(r),i=rj({max:n,current:t,change:l});return!i||0!==l.x&&0===i.x||0!==l.y&&0===i.y},rq=(e,t)=>rz({current:e.scroll.current,max:e.scroll.max,change:t}),rY=(e,t)=>{if(!rq(e,t))return null;let r=e.scroll.max;return rj({current:e.scroll.current,max:r,change:t})},rJ=(e,t)=>{let r=e.frame;return!!r&&rz({current:r.scroll.current,max:r.scroll.max,change:t})},rX=(e,t)=>{let r=e.frame;return r&&rJ(e,t)?rj({current:r.scroll.current,max:r.scroll.max,change:t}):null};var rK=({viewport:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=rH({dragStartTime:n,container:e.frame,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i});return a&&rq(e,a)?a:null},rQ=({droppable:e,subject:t,center:r,dragStartTime:n,shouldUseTimeDampening:l,getAutoScrollerOptions:i})=>{let a=e.frame;if(!a)return null;let o=rH({dragStartTime:n,container:a.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:l,getAutoScrollerOptions:i});return o&&rJ(e,o)?o:null},rZ=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:n,scrollDroppable:l,getAutoScrollerOptions:i})=>{let a=e.current.page.borderBoxCenter,o=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){let l=rK({dragStartTime:t,viewport:e.viewport,subject:o,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});if(l)return void n(l)}let d=rG({center:a,destination:e5(e.impact),droppables:e.dimensions.droppables});if(!d)return;let s=rQ({dragStartTime:t,droppable:d,subject:o,center:a,shouldUseTimeDampening:r,getAutoScrollerOptions:i});s&&l(d.descriptor.id,s)},r0=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=()=>rL})=>{let n=(0,d.A)(e),l=(0,d.A)(t),i=null,a=e=>{i||v();let{shouldUseTimeDampening:t,dragStartTime:a}=i;rZ({state:e,scrollWindow:n,scrollDroppable:l,dragStartTime:a,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{tm(),i&&v();let t=Date.now(),n=!1,l=()=>{n=!0};rZ({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:l,scrollDroppable:l,getAutoScrollerOptions:r}),i={dragStartTime:t,shouldUseTimeDampening:n},tb(),n&&a(e)},stop:()=>{i&&(n.cancel(),l.cancel(),i=null)},scroll:a}},r1=({move:e,scrollDroppable:t,scrollWindow:r})=>{let n=(t,r)=>{e({client:O(t.current.client.selection,r)})},l=(e,r)=>{if(!rJ(e,r))return r;let n=rX(e,r);if(!n)return t(e.descriptor.id,r),null;let l=S(r,n);return t(e.descriptor.id,l),S(r,l)},i=(e,t,n)=>{if(!e||!rq(t,n))return n;let l=rY(t,n);if(!l)return r(n),null;let i=S(n,l);return r(i),S(n,i)};return e=>{let t=e.scrollJumpRequest;if(!t)return;let r=e5(e.impact);r||v();let a=l(e.dimensions.droppables[r],t);if(!a)return;let o=e.viewport,d=i(e.isWindowScrollAllowed,o,a);d&&n(e,d)}},r2=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:n})=>{let l=r0({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:n}),i=r1({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{if(!n().disabled&&"DRAGGING"===e.phase){if("FLUID"===e.movementMode)return void l.scroll(e);e.scrollJumpRequest&&i(e)}},start:l.start,stop:l.stop}};let r3="data-rfd",r5=(()=>{let e=`${r3}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),r4=(()=>{let e=`${r3}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),r7=(()=>{let e=`${r3}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),r9={contextId:`${r3}-scroll-container-context-id`},r6=e=>t=>`[${t}="${e}"]`,r8=(e,t)=>e.map(e=>{let r=e.styles[t];return r?`${e.selector} { ${r} }`:""}).join(" ");var ne=e=>{let t=r6(e),r=(()=>{let e=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(r5.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),n=(()=>{let e=`
      transition: ${tZ.outOfTheWay};
    `;return{selector:t(r4.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),l=[n,r,{selector:t(r7.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:r8(l,"always"),resting:r8(l,"resting"),dragging:r8(l,"dragging"),dropAnimating:r8(l,"dropAnimating"),userCancel:r8(l,"userCancel")}};let nt="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?n.useLayoutEffect:n.useEffect,nr=()=>{let e=document.querySelector("head");return e||v(),e},nn=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function nl(e,t){return Array.from(e.querySelectorAll(t))}var ni=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function na(e){return e instanceof ni(e).HTMLElement}function no(e,t){let r=nl(document,`[${r5.contextId}="${e}"]`);if(!r.length)return null;let n=r.find(e=>e.getAttribute(r5.draggableId)===t);return n&&na(n)?n:null}function nd(){let e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach(t=>t(e))}function n(t){return e.draggables[t]||null}function l(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{let n=e.draggables[r.descriptor.id];n&&n.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{let l=t.descriptor.id,i=n(l);i&&t.uniqueId===i.uniqueId&&(delete e.draggables[l],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){let t=n(e);return t||v(),t},findById:n,exists:e=>!!n(e),getAllByType:t=>Object.values(e.draggables).filter(e=>e.descriptor.type===t)},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{let r=l(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){let t=l(e);return t||v(),t},findById:l,exists:e=>!!l(e),getAllByType:t=>Object.values(e.droppables).filter(e=>e.descriptor.type===t)},subscribe:function(e){return t.push(e),function(){let r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var ns=n.createContext(null),nu=()=>{let e=document.body;return e||v(),e};let nc={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},np=e=>`rfd-announcement-${e}`,nf={separator:"::"};function ng(e,t=nf){let r=n.useId();return C(()=>`${e}${t.separator}${r}`,[t.separator,e,r])}var nm=n.createContext(null),nb={react:"^18.0.0 || ^19.0.0"};let nh=/(\d+)\.(\d+)\.(\d+)/,ny=e=>{let t=nh.exec(e);null==t&&v();let r=Number(t[1]);return{major:r,minor:Number(t[2]),patch:Number(t[3]),raw:e}},nv=(e,t)=>t.major>e.major||!(t.major<e.major)&&(t.minor>e.minor||!(t.minor<e.minor)&&t.patch>=e.patch);var nI=(e,t)=>{if(nv(ny(e),ny(t)))return},nx=e=>{let t=e.doctype;t&&(t.name.toLowerCase(),t.publicId)};function nD(e){}function nE(e,t){}function nA(e){let t=(0,n.useRef)(e);return(0,n.useEffect)(()=>{t.current=e}),t}function nN(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}let nR={13:!0,9:!0};var nC=e=>{nR[e.keyCode]&&e.preventDefault()};let nP=(()=>{let e="visibilitychange";return"undefined"==typeof document?e:[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(e=>`on${e}`in document)||e})(),nw={type:"IDLE"};function nO(){}let nS={34:!0,33:!0,36:!0,35:!0},nB={type:"IDLE"},nG=["input","button","textarea","select","option","optgroup","video","audio"];var nL=e=>(0,o.l)(e.getBoundingClientRect()).center;let nT=(()=>{let e="matches";return"undefined"==typeof document?e:[e,"msMatchesSelector","webkitMatchesSelector"].find(e=>e in Element.prototype)||e})();function n_(e){e.preventDefault()}function nM({expected:e,phase:t,isLockActive:r,shouldWarn:n}){return!!r()&&e===t}function nF({lockAPI:e,store:t,registry:r,draggableId:n}){if(e.isClaimed())return!1;let l=r.draggable.findById(n);return!!l&&!!l.options.isEnabled&&!!rw(t.getState(),n)}let nk=[function(e){let t=(0,n.useRef)(nw),r=(0,n.useRef)(b),l=C(()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented||0!==t.button||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,o,{sourceEvent:t});if(!l)return;t.preventDefault();let i={x:t.clientX,y:t.clientY};r.current(),u(l,i)}}),[e]),i=C(()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;let r=e.findClosestDraggableId(t);if(!r)return;let n=e.findOptionsForDraggable(r);n&&!n.shouldRespectForcePress&&e.canGetLock(r)&&t.preventDefault()}}),[e]),a=P(function(){r.current=h(window,[i,l],{passive:!1,capture:!0})},[i,l]),o=P(()=>{"IDLE"!==t.current.type&&(t.current=nw,r.current(),a())},[a]),d=P(()=>{let e=t.current;o(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[o]),s=P(function(){r.current=h(window,function({cancel:e,completed:t,getPhase:r,setPhase:n}){return[{eventName:"mousemove",fn:e=>{var t;let{button:l,clientX:i,clientY:a}=e;if(0!==l)return;let o={x:i,y:a},d=r();if("DRAGGING"===d.type){e.preventDefault(),d.actions.move(o);return}"PENDING"!==d.type&&v(),t=d.point,(Math.abs(o.x-t.x)>=5||Math.abs(o.y-t.y)>=5)&&(e.preventDefault(),n({type:"DRAGGING",actions:d.actions.fluidLift(o)}))}},{eventName:"mouseup",fn:n=>{let l=r();if("DRAGGING"!==l.type)return void e();n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"===r().type)return void e();if(27===t.keyCode){t.preventDefault(),e();return}nC(t)}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{let n=r();if("IDLE"===n.type&&v(),n.actions.shouldRespectForcePress())return void e();t.preventDefault()}},{eventName:nP,fn:e}]}({cancel:d,completed:o,getPhase:()=>t.current,setPhase:e=>{t.current=e}}),{capture:!0,passive:!1})},[d,o]),u=P(function(e,r){"IDLE"!==t.current.type&&v(),t.current={type:"PENDING",point:r,actions:e},s()},[s]);nt(function(){return a(),function(){r.current()}},[a])},function(e){let t=(0,n.useRef)(nO),r=C(()=>({eventName:"keydown",fn:function(r){if(r.defaultPrevented||32!==r.keyCode)return;let n=e.findClosestDraggableId(r);if(!n)return;let i=e.tryGetLock(n,d,{sourceEvent:r});if(!i)return;r.preventDefault();let a=!0,o=i.snapLift();function d(){a||v(),a=!1,t.current(),l()}t.current(),t.current=h(window,function(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:n=>{if(27===n.keyCode){n.preventDefault(),r();return}if(32===n.keyCode){n.preventDefault(),t(),e.drop();return}if(40===n.keyCode){n.preventDefault(),e.moveDown();return}if(38===n.keyCode){n.preventDefault(),e.moveUp();return}if(39===n.keyCode){n.preventDefault(),e.moveRight();return}if(37===n.keyCode){n.preventDefault(),e.moveLeft();return}if(nS[n.keyCode])return void n.preventDefault();nC(n)}},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:nP,fn:r}]}(o,d),{capture:!0,passive:!1})}}),[e]),l=P(function(){t.current=h(window,[r],{passive:!1,capture:!0})},[r]);nt(function(){return l(),function(){t.current()}},[l])},function(e){let t=(0,n.useRef)(nB),r=(0,n.useRef)(b),l=P(function(){return t.current},[]),i=P(function(e){t.current=e},[]),a=C(()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;let n=e.findClosestDraggableId(t);if(!n)return;let l=e.tryGetLock(n,d,{sourceEvent:t});if(!l)return;let{clientX:i,clientY:a}=t.touches[0];r.current(),p(l,{x:i,y:a})}}),[e]),o=P(function(){r.current=h(window,[a],{capture:!0,passive:!1})},[a]),d=P(()=>{let e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),i(nB),r.current(),o())},[o,i]),s=P(()=>{let e=t.current;d(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[d]),u=P(function(){let e={capture:!0,passive:!1},t={cancel:s,completed:d,getPhase:l},n=h(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{let n=r();if("DRAGGING"!==n.type)return void e();n.hasMoved=!0;let{clientX:l,clientY:i}=t.touches[0];t.preventDefault(),n.actions.move({x:l,y:i})}},{eventName:"touchend",fn:n=>{let l=r();if("DRAGGING"!==l.type)return void e();n.preventDefault(),l.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:t=>{if("DRAGGING"!==r().type)return void e();t.preventDefault(),e()}},{eventName:"touchforcechange",fn:t=>{let n=r();"IDLE"===n.type&&v();let l=t.touches[0];if(!l||!(l.force>=.15))return;let i=n.actions.shouldRespectForcePress();if("PENDING"===n.type){i&&e();return}if(i)return n.hasMoved?void t.preventDefault():void e();t.preventDefault()}},{eventName:nP,fn:e}]}(t),e),i=h(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{if("DRAGGING"!==t().type)return void e();27===r.keyCode&&r.preventDefault(),e()}},{eventName:nP,fn:e}]}(t),e);r.current=function(){n(),i()}},[s,l,d]),c=P(function(){let e=l();"PENDING"!==e.type&&v(),i({type:"DRAGGING",actions:e.actions.fluidLift(e.point),hasMoved:!1})},[l,i]),p=P(function(e,t){"IDLE"!==l().type&&v(),i({type:"PENDING",point:t,actions:e,longPressTimerId:setTimeout(c,120)}),u()},[u,l,i,c]);nt(function(){return o(),function(){r.current();let e=l();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),i(nB))}},[l,o,i]),nt(function(){return h(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}],n$=e=>({onBeforeCapture:t=>{(0,l.flushSync)(()=>{e.onBeforeCapture&&e.onBeforeCapture(t)})},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),nW=e=>({...rL,...e.autoScrollerOptions,durationDampening:{...rL.durationDampening,...e.autoScrollerOptions}});function nU(e){return e.current||v(),e.current}function nH(e){let{contextId:t,setCallbacks:r,sensors:l,nonce:o,dragHandleUsageInstructions:u}=e,c=(0,n.useRef)(null);nE(()=>{nI(nb.react,n.version),nx(document)},[]);let p=nA(e),f=P(()=>n$(p.current),[p]),g=P(()=>nW(p.current),[p]),m=function(e){let t=C(()=>np(e),[e]),r=(0,n.useRef)(null);return(0,n.useEffect)(function(){let e=document.createElement("div");return r.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),(0,s.A)(e.style,nc),nu().appendChild(e),function(){setTimeout(function(){let t=nu();t.contains(e)&&t.removeChild(e),e===r.current&&(r.current=null)})}},[t]),P(e=>{let t=r.current;if(t){t.textContent=e;return}},[])}(t),y=function({contextId:e,text:t}){let r=ng("hidden-text",{separator:"-"}),l=C(()=>(function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`})({contextId:e,uniqueId:r}),[r,e]);return(0,n.useEffect)(function(){let e=document.createElement("div");return e.id=l,e.textContent=t,e.style.display="none",nu().appendChild(e),function(){let t=nu();t.contains(e)&&t.removeChild(e)}},[l,t]),l}({contextId:t,text:u}),I=function(e,t){let r=C(()=>ne(e),[e]),l=(0,n.useRef)(null),i=(0,n.useRef)(null),a=P(z(e=>{let t=i.current;t||v(),t.textContent=e}),[]),o=P(e=>{let t=l.current;t||v(),t.textContent=e},[]);nt(()=>{(l.current||i.current)&&v();let n=nn(t),d=nn(t);return l.current=n,i.current=d,n.setAttribute(`${r3}-always`,e),d.setAttribute(`${r3}-dynamic`,e),nr().appendChild(n),nr().appendChild(d),o(r.always),a(r.resting),()=>{let e=e=>{let t=e.current;t||v(),nr().removeChild(t),e.current=null};e(l),e(i)}},[t,o,a,r.always,r.resting,e]);let d=P(()=>a(r.dragging),[a,r.dragging]),s=P(e=>{if("DROP"===e)return void a(r.dropAnimating);a(r.userCancel)},[a,r.dropAnimating,r.userCancel]),u=P(()=>{i.current&&a(r.resting)},[a,r.resting]);return C(()=>({dragging:d,dropping:s,resting:u}),[d,s,u])}(t,o),x=P(e=>{nU(c).dispatch(e)},[]),D=C(()=>(0,i.zH)({publishWhileDragging:tO,updateDroppableScroll:tB,updateDroppableIsEnabled:tG,updateDroppableIsCombineEnabled:tL,collectionStarting:tS},x),[x]),E=function(){let e=C(nd,[]);return(0,n.useEffect)(()=>function(){e.clean()},[e]),e}(),A=C(()=>rP(E,D),[E,D]),N=C(()=>r2({scrollWindow:rO,scrollDroppable:A.scrollDroppable,getAutoScrollerOptions:g,...(0,i.zH)({move:tT},x)}),[A.scrollDroppable,x,g]),R=function(e){let t=(0,n.useRef)({}),r=(0,n.useRef)(null),l=(0,n.useRef)(null),i=(0,n.useRef)(!1),a=P(function(e,r){let n={id:e,focus:r};return t.current[e]=n,function(){let r=t.current;r[e]!==n&&delete r[e]}},[]),o=P(function(t){let r=no(e,t);r&&r!==document.activeElement&&r.focus()},[e]),d=P(function(e,t){r.current===e&&(r.current=t)},[]),s=P(function(){!l.current&&i.current&&(l.current=requestAnimationFrame(()=>{l.current=null;let e=r.current;e&&o(e)}))},[o]),u=P(function(e){r.current=null;let t=document.activeElement;t&&t.getAttribute(r5.draggableId)===e&&(r.current=e)},[]);return nt(()=>(i.current=!0,function(){i.current=!1;let e=l.current;e&&cancelAnimationFrame(e)}),[]),C(()=>({register:a,tryRecordFocus:u,tryRestoreFocusRecorded:s,tryShiftRecord:d}),[a,u,s,d])}(t),w=C(()=>rI({announce:m,autoScroller:N,dimensionMarshal:A,focusMarshal:R,getResponders:f,styleMarshal:I}),[m,N,A,R,f,I]);c.current=w;let O=P(()=>{let e=nU(c);"IDLE"!==e.getState().phase&&e.dispatch(tW())},[]),S=P(()=>{let e=nU(c).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging},[]);r(C(()=>({isDragging:S,tryAbort:O}),[S,O]));let B=P(e=>rw(nU(c).getState(),e),[]),G=P(()=>e9(nU(c).getState()),[]),L=C(()=>({marshal:A,focus:R,contextId:t,canLift:B,isMovementAllowed:G,dragHandleUsageInstructionsId:y,registry:E}),[t,A,y,R,B,G,E]);return!function({contextId:e,store:t,registry:r,customSensors:l,enableDefaultSensors:i}){let a=[...i?nk:[],...l||[]],o=(0,n.useState)(()=>(function(){let e=null;function t(){e||v(),e=null}return{isClaimed:function(){return!!e},isActive:function(t){return t===e},claim:function(t){e&&v();let r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}})())[0],s=P(function(e,t){nN(e)&&!nN(t)&&o.tryAbandon()},[o]);nt(function(){let e=t.getState();return t.subscribe(()=>{let r=t.getState();s(e,r),e=r})},[o,t,s]),nt(()=>o.tryAbandon,[o.tryAbandon]);let u=P(e=>nF({lockAPI:o,registry:r,store:t,draggableId:e}),[o,r,t]),c=P((n,l,i)=>(function({lockAPI:e,contextId:t,store:r,registry:n,draggableId:l,forceSensorStop:i,sourceEvent:a}){if(!nF({lockAPI:e,store:r,registry:n,draggableId:l}))return null;let o=n.draggable.getById(l),s=function(e,t){let r=nl(document,`[${r4.contextId}="${e}"]`).find(e=>e.getAttribute(r4.id)===t);return r&&na(r)?r:null}(t,o.descriptor.id);if(!s||a&&!o.options.canDragInteractiveElements&&function(e,t){let r=t.target;return!!na(r)&&function e(t,r){if(null==r)return!1;if(nG.includes(r.tagName.toLowerCase()))return!0;let n=r.getAttribute("contenteditable");return"true"===n||""===n||r!==t&&e(t,r.parentElement)}(e,r)}(s,a))return null;let u=e.claim(i||b),c="PRE_DRAG";function p(){return o.options.shouldRespectForcePress}function f(){return e.isActive(u)}let g=(function(e,t){nM({expected:e,phase:c,isLockActive:f,shouldWarn:!0})&&r.dispatch(t())}).bind(null,"DRAGGING");function m(t){function n(){e.release(),c="COMPLETED"}function l(e,i={shouldBlockNextClick:!1}){t.cleanup(),i.shouldBlockNextClick&&setTimeout(h(window,[{eventName:"click",fn:n_,options:{once:!0,passive:!1,capture:!0}}])),n(),r.dispatch(tV({reason:e}))}return"PRE_DRAG"!==c&&(n(),v()),r.dispatch(tP(t.liftActionArgs)),c="DRAGGING",{isActive:()=>nM({expected:"DRAGGING",phase:c,isLockActive:f,shouldWarn:!1}),shouldRespectForcePress:p,drop:e=>l("DROP",e),cancel:e=>l("CANCEL",e),...t.actions}}return{isActive:()=>nM({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!1}),shouldRespectForcePress:p,fluidLift:function(e){let t=(0,d.A)(e=>{g(()=>tT({client:e}))});return{...m({liftActionArgs:{id:l,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){return m({liftActionArgs:{id:l,clientSelection:nL(s),movementMode:"SNAP"},cleanup:b,actions:{moveUp:()=>g(tM),moveRight:()=>g(tk),moveDown:()=>g(tF),moveLeft:()=>g(t$)}})},abort:function(){nM({expected:"PRE_DRAG",phase:c,isLockActive:f,shouldWarn:!0})&&e.release()}}})({lockAPI:o,registry:r,contextId:e,store:t,draggableId:n,forceSensorStop:l||null,sourceEvent:i&&i.sourceEvent?i.sourceEvent:null}),[e,o,r,t]),p=P(t=>(function(e,t){let r=function(e,t){let r=t.target;if(!(r instanceof ni(r).Element))return null;let n=`[${r5.contextId}="${e}"]`,l=r.closest?r.closest(n):function e(t,r){return null==t?null:t[nT](r)?t:e(t.parentElement,r)}(r,n);return l&&na(l)?l:null}(e,t);return r?r.getAttribute(r5.draggableId):null})(e,t),[e]),f=P(e=>{let t=r.draggable.findById(e);return t?t.options:null},[r.draggable]),g=P(function(){o.isClaimed()&&(o.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(tW()))},[o,t]),m=P(()=>o.isClaimed(),[o]),y=C(()=>({canGetLock:u,tryGetLock:c,findClosestDraggableId:p,findOptionsForDraggable:f,tryReleaseLock:g,isLockClaimed:m}),[u,c,p,f,g,m]);for(let e=0;e<a.length;e++)a[e](y)}({contextId:t,store:w,registry:E,customSensors:l||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,n.useEffect)(()=>O,[O]),n.createElement(nm.Provider,{value:L},n.createElement(a.Kq,{context:ns,store:w},e.children))}function nV(e){let t=n.useId(),r=e.dragHandleUsageInstructions||N.dragHandleUsageInstructions;return n.createElement(I,null,l=>n.createElement(nH,{nonce:e.nonce,contextId:t,setCallbacks:l,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}let nj={dragging:5e3,dropAnimating:4500},nz=(e,t)=>t?tZ.drop(t.duration):e?tZ.snap:tZ.fluid,nq=(e,t)=>{if(e)return t?tX.opacity.drop:tX.opacity.combining},nY=e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode;var nJ=n.createContext(null);function nX(e){e&&na(e)||v()}function nK(e){let t=(0,n.useContext)(e);return t||v(),t}function nQ(e){e.preventDefault()}var nZ=(e,t)=>e===t,n0=e=>{let{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};let n1=e=>e.combine?e.combine.draggableId:null,n2=e=>e.at&&"COMBINE"===e.at.type?e.at.combine.draggableId:null;function n3(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}let n5={mapped:{type:"SECONDARY",offset:w,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:n3(null)}},n4=(0,a.Ng)(()=>{let e=function(){let e=z((e,t)=>({x:e,y:t})),t=z((e,t,r=null,n=null,l=null)=>({isDragging:!0,isClone:t,isDropAnimating:!!l,dropAnimation:l,mode:e,draggingOver:r,combineWith:n,combineTargetFor:null})),r=z((e,r,n,l,i=null,a=null,o=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:i,combineWith:a,mode:r,offset:e,dimension:n,forceShouldAnimate:o,snapshot:t(r,l,i,a,null)}}));return(n,l)=>{if(nN(n)){if(n.critical.draggable.id!==l.draggableId)return null;let t=n.current.client.offset,i=n.dimensions.draggables[l.draggableId],a=e5(n.impact),o=n2(n.impact),d=n.forceShouldAnimate;return r(e(t.x,t.y),n.movementMode,i,l.isClone,a,o,d)}if("DROP_ANIMATING"===n.phase){let e=n.completed;if(e.result.draggableId!==l.draggableId)return null;let r=l.isClone,i=n.dimensions.draggables[l.draggableId],a=e.result,o=a.mode,d=n0(a),s=n1(a),u={duration:n.dropDuration,curve:tJ.drop,moveTo:n.newHomeClientOffset,opacity:s?tX.opacity.drop:null,scale:s?tX.scale.drop:null};return{mapped:{type:"DRAGGING",offset:n.newHomeClientOffset,dimension:i,dropping:u,draggingOver:d,combineWith:s,mode:o,forceShouldAnimate:null,snapshot:t(o,r,d,s,u)}}}return null}}(),t=function(){let e=z((e,t)=>({x:e,y:t})),t=z(n3),r=z((e,r=null,n)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:n,snapshot:t(r)}})),n=e=>e?r(w,e,!0):null,l=(t,l,i,a)=>{let o=i.displaced.visible[t],d=!!(a.inVirtualList&&a.effected[t]),s=Z(i),u=s&&s.draggableId===t?l:null;if(!o){if(!d)return n(u);if(i.displaced.invisible[t])return null;let l=G(a.displacedBy.point);return r(e(l.x,l.y),u,!0)}if(d)return n(u);let c=i.displacedBy.point;return r(e(c.x,c.y),u,o.shouldAnimate)};return(e,t)=>{if(nN(e))return e.critical.draggable.id===t.draggableId?null:l(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){let r=e.completed;return r.result.draggableId===t.draggableId?null:l(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,n)=>e(r,n)||t(r,n)||n5},{dropAnimationFinished:tz},null,{context:ns,areStatePropsEqual:nZ})(e=>{let t=(0,n.useRef)(null),r=P((e=null)=>{t.current=e},[]),i=P(()=>t.current,[]),{contextId:a,dragHandleUsageInstructionsId:d,registry:s}=nK(nm),{type:u,droppableId:c}=nK(nJ),p=C(()=>({id:e.draggableId,index:e.index,type:u,droppableId:c}),[e.draggableId,e.index,u,c]),{children:f,draggableId:g,isEnabled:m,shouldRespectForcePress:b,canDragInteractiveElements:h,isClone:y,mapped:I,dropAnimationFinished:x}=e;!function(e,t,r){nE(()=>{let n=e.draggableId;n||v(!1),"string"!=typeof n&&v(!1),Number.isInteger(e.index)||v(!1),"DRAGGING"!==e.mapped.type&&(nX(r()),e.isEnabled&&(no(t,n)||v(!1)))})}(e,a,i),y||function(e){let t=ng("draggable"),{descriptor:r,registry:l,getDraggableRef:i,canDragInteractiveElements:a,shouldRespectForcePress:d,isEnabled:s}=e,u=C(()=>({canDragInteractiveElements:a,shouldRespectForcePress:d,isEnabled:s}),[a,s,d]),c=P(e=>{let t=i();return t||v(),function(e,t,r=w){let n=window.getComputedStyle(t),l=t.getBoundingClientRect(),i=(0,o.a)(l,n),a=(0,o.SQ)(i,r),d={client:i,tagName:t.tagName.toLowerCase(),display:n.display};return{descriptor:e,placeholder:d,displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:a}}(r,t,e)},[r,i]),p=C(()=>({uniqueId:t,descriptor:r,options:u,getDimension:c}),[r,c,u,t]),f=(0,n.useRef)(p),g=(0,n.useRef)(!0);nt(()=>(l.draggable.register(f.current),()=>l.draggable.unregister(f.current)),[l.draggable]),nt(()=>{if(g.current){g.current=!1;return}let e=f.current;f.current=p,l.draggable.update(p,e)},[p,l.draggable])}(C(()=>({descriptor:p,registry:s,getDraggableRef:i,canDragInteractiveElements:h,shouldRespectForcePress:b,isEnabled:m}),[p,s,i,h,b,m]));let D=C(()=>m?{tabIndex:0,role:"button","aria-describedby":d,"data-rfd-drag-handle-draggable-id":g,"data-rfd-drag-handle-context-id":a,draggable:!1,onDragStart:nQ}:null,[a,d,g,m]),E=P(e=>{"DRAGGING"===I.type&&I.dropping&&"transform"===e.propertyName&&(0,l.flushSync)(x)},[x,I]),A=C(()=>{let e=function(e){return"DRAGGING"===e.type?function(e){let t=e.dimension.client,{offset:r,combineWith:n,dropping:l}=e,i=!!n,a=nY(e),o=!!l,d=o?t1.drop(r,i):t1.moveTo(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:nz(a,l),transform:d,opacity:nq(i,o),zIndex:o?nj.dropAnimating:nj.dragging,pointerEvents:"none"}}(e):{transform:t1.moveTo(e.offset),transition:e.shouldAnimateDisplacement?void 0:"none"}}(I);return{innerRef:r,draggableProps:{"data-rfd-draggable-context-id":a,"data-rfd-draggable-id":g,style:e,onTransitionEnd:"DRAGGING"===I.type&&I.dropping?E:void 0},dragHandleProps:D}},[a,D,g,I,E,r]),N=C(()=>({draggableId:p.id,type:p.type,source:{index:p.index,droppableId:p.droppableId}}),[p.droppableId,p.id,p.index,p.type]);return n.createElement(n.Fragment,null,f(A,I.snapshot,N))});function n7(e){return nK(nJ).isUsingCloneFor!==e.draggableId||e.isClone?n.createElement(n4,e):null}function n9(e){let t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=!!e.disableInteractiveElementBlocking,l=!!e.shouldRespectForcePress;return n.createElement(n7,(0,s.A)({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:l}))}let n6=e=>t=>e===t,n8=n6("scroll"),le=n6("auto");n6("visible");let lt=(e,t)=>t(e.overflowX)||t(e.overflowY),lr=e=>{let t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return lt(r,n8)||lt(r,le)},ln=()=>!1,ll=e=>null==e?null:e===document.body?ln()?e:null:e===document.documentElement?null:lr(e)?e:ll(e.parentElement);var li=e=>({x:e.scrollLeft,y:e.scrollTop});let la=e=>!!e&&("fixed"===window.getComputedStyle(e).position||la(e.parentElement));var lo=e=>({closestScrollable:ll(e),isFixedOnPage:la(e)}),ld=({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:n,direction:l,client:i,page:a,closest:o})=>{let d=(()=>{if(!o)return null;let{scrollSize:e,client:t}=o,r=rD({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:o.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:o.shouldClipSubject,scroll:{initial:o.scroll,current:o.scroll,max:r,diff:{value:w,displacement:w}}}})(),s="vertical"===l?es:eu,u=V({page:a,withPlaceholder:null,axis:s,frame:d});return{descriptor:e,isCombineEnabled:r,isFixedOnPage:n,axis:s,isEnabled:t,client:i,page:a,frame:d,subject:u}};let ls=(e,t)=>{let r=(0,o.YH)(e);if(!t||e!==t)return r;let n=r.paddingBox.top-t.scrollTop,l=r.paddingBox.left-t.scrollLeft,i=n+t.scrollHeight,a=l+t.scrollWidth,d=(0,o.fT)({top:n,right:a,bottom:i,left:l},r.border);return(0,o.ge)({borderBox:d,margin:r.margin,border:r.border,padding:r.padding})};var lu=({ref:e,descriptor:t,env:r,windowScroll:n,direction:l,isDropDisabled:i,isCombineEnabled:a,shouldClipSubject:d})=>{let s=r.closestScrollable,u=ls(e,s),c=(0,o.SQ)(u,n),p=(()=>{if(!s)return null;let e=(0,o.YH)(s),t={scrollHeight:s.scrollHeight,scrollWidth:s.scrollWidth};return{client:e,page:(0,o.SQ)(e,n),scroll:li(s),scrollSize:t,shouldClipSubject:d}})();return ld({descriptor:t,isEnabled:!i,isCombineEnabled:a,isFixedOnPage:r.isFixedOnPage,direction:l,client:u,page:c,closest:p})};let lc={passive:!1},lp={passive:!0};var lf=e=>e.shouldPublishImmediately?lc:lp;let lg=e=>e&&e.env.closestScrollable||null;function lm(){}let lb={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},lh=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?lb:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin},ly=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{let n=lh({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:n.width,height:n.height,marginTop:n.margin.top,marginRight:n.margin.right,marginBottom:n.margin.bottom,marginLeft:n.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?tZ.placeholder:null}};var lv=n.memo(e=>{let t=(0,n.useRef)(null),r=P(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:l,onTransitionEnd:i,onClose:a,contextId:o}=e,[d,s]=(0,n.useState)("open"===e.animate);(0,n.useEffect)(()=>d?"open"!==l?(r(),s(!1),lm):t.current?lm:(t.current=setTimeout(()=>{t.current=null,s(!1)}),r):lm,[l,d,r]);let u=P(e=>{"height"===e.propertyName&&(i(),"close"===l&&a())},[l,a,i]),c=ly({isAnimatingOpenOnMount:d,animate:e.animate,placeholder:e.placeholder});return n.createElement(e.placeholder.tagName,{style:c,"data-rfd-placeholder-context-id":o,onTransitionEnd:u,ref:e.innerRef})});function lI(e){return"boolean"==typeof e}function lx(e,t){t.forEach(t=>t(e))}let lD=[function({props:e}){e.droppableId||v(),"string"!=typeof e.droppableId&&v()},function({props:e}){lI(e.isDropDisabled)||v(),lI(e.isCombineEnabled)||v(),lI(e.ignoreContainerClipping)||v()},function({getDroppableRef:e}){nX(e())}],lE=[function({props:e,getPlaceholderRef:t}){if(!e.placeholder||t())return}],lA=[function({props:e}){e.renderClone||v()},function({getPlaceholderRef:e}){e()&&v()}];class lN extends n.PureComponent{constructor(...e){super(...e),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!e.on,data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;let e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}let lR={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||v(),document.body}},lC=e=>{let t,r={...e};for(t in lR)void 0===e[t]&&(r={...r,[t]:lR[t]});return r},lP=(e,t)=>e===t.droppable.type,lw=(e,t)=>t.draggables[e.draggable.id],lO=(0,a.Ng)(()=>{let e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=z(e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}})),n=z((n,l,i,a,o,d)=>{let s=o.descriptor.id;if(o.descriptor.droppableId===n){let e=d?{render:d,dragging:r(o.descriptor)}:null;return{placeholder:o.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:i,draggingOverWith:i?s:null,draggingFromThisWith:s,isUsingPlaceholder:!0},useClone:e}}return l?a?{placeholder:o.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:i,draggingOverWith:s,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:e:t});return(r,l)=>{let i=lC(l),a=i.droppableId,o=i.type,d=!i.isDropDisabled,s=i.renderClone;if(nN(r)){let e=r.critical;if(!lP(o,e))return t;let l=lw(e,r.dimensions),i=e5(r.impact)===a;return n(a,d,i,i,l,s)}if("DROP_ANIMATING"===r.phase){let e=r.completed;if(!lP(o,e.critical))return t;let l=lw(e.critical,r.dimensions);return n(a,d,n0(e.result)===a,e5(e.impact)===a,l,s)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){let n=r.completed;if(!lP(o,n.critical))return t;let l=e5(n.impact)===a,i=!!(n.impact.at&&"COMBINE"===n.impact.at.type),d=n.critical.droppable.id===a;if(l)return i?e:t;if(d)return e}return t}},{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},(e,t,r)=>({...lC(r),...e,...t}),{context:ns,areStatePropsEqual:nZ})(e=>{let t=(0,n.useContext)(nm);t||v();let{contextId:r,isMovementAllowed:i}=t,a=(0,n.useRef)(null),o=(0,n.useRef)(null),{children:s,droppableId:u,type:c,mode:p,direction:f,ignoreContainerClipping:g,isDropDisabled:m,isCombineEnabled:b,snapshot:h,useClone:y,updateViewportMaxScroll:I,getContainerForClone:x}=e,D=P(()=>a.current,[]),E=P((e=null)=>{a.current=e},[]),A=P(()=>o.current,[]),N=P((e=null)=>{o.current=e},[]);!function(e){nE(()=>{lx(e,lD),"standard"===e.props.mode&&lx(e,lE),"virtual"===e.props.mode&&lx(e,lA)})}({props:e,getDroppableRef:D,getPlaceholderRef:A});let R=P(()=>{i()&&I({maxScroll:rA()})},[i,I]);!function(e){let t=(0,n.useRef)(null),r=nK(nm),l=ng("droppable"),{registry:i,marshal:a}=r,o=nA(e),s=C(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),u=(0,n.useRef)(s),c=C(()=>z((e,r)=>{t.current||v(),a.updateDroppableScroll(s.id,{x:e,y:r})}),[s.id,a]),p=P(()=>{let e=t.current;return e&&e.env.closestScrollable?li(e.env.closestScrollable):w},[]),f=P(()=>{let e=p();c(e.x,e.y)},[p,c]),g=C(()=>(0,d.A)(f),[f]),m=P(()=>{let e=t.current,r=lg(e);if(e&&r||v(),e.scrollOptions.shouldPublishImmediately)return void f();g()},[g,f]),b=P((e,n)=>{t.current&&v();let l=o.current,i=l.getDroppableRef();i||v();let a=lo(i),d={ref:i,descriptor:s,env:a,scrollOptions:n};t.current=d;let u=lu({ref:i,descriptor:s,env:a,windowScroll:e,direction:l.direction,isDropDisabled:l.isDropDisabled,isCombineEnabled:l.isCombineEnabled,shouldClipSubject:!l.ignoreContainerClipping}),c=a.closestScrollable;return c&&(c.setAttribute(r9.contextId,r.contextId),c.addEventListener("scroll",m,lf(d.scrollOptions))),u},[r.contextId,s,m,o]),h=P(()=>{let e=t.current,r=lg(e);return e&&r||v(),li(r)},[]),y=P(()=>{let e=t.current;e||v();let r=lg(e);t.current=null,r&&(g.cancel(),r.removeAttribute(r9.contextId),r.removeEventListener("scroll",m,lf(e.scrollOptions)))},[m,g]),I=P(e=>{let r=t.current;r||v();let n=lg(r);n||v(),n.scrollTop+=e.y,n.scrollLeft+=e.x},[]),x=C(()=>({getDimensionAndWatchScroll:b,getScrollWhileDragging:h,dragStopped:y,scroll:I}),[y,b,h,I]),D=C(()=>({uniqueId:l,descriptor:s,callbacks:x}),[x,s,l]);nt(()=>(u.current=D.descriptor,i.droppable.register(D),()=>{t.current&&y(),i.droppable.unregister(D)}),[x,s,y,D,a,i.droppable]),nt(()=>{t.current&&a.updateDroppableIsEnabled(u.current.id,!e.isDropDisabled)},[e.isDropDisabled,a]),nt(()=>{t.current&&a.updateDroppableIsCombineEnabled(u.current.id,e.isCombineEnabled)},[e.isCombineEnabled,a])}({droppableId:u,type:c,mode:p,direction:f,isDropDisabled:m,isCombineEnabled:b,ignoreContainerClipping:g,getDroppableRef:D});let O=C(()=>n.createElement(lN,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:e,data:t,animate:l})=>n.createElement(lv,{placeholder:t,onClose:e,innerRef:N,animate:l,contextId:r,onTransitionEnd:R})),[r,R,e.placeholder,e.shouldAnimatePlaceholder,N]),S=C(()=>({innerRef:E,placeholder:O,droppableProps:{"data-rfd-droppable-id":u,"data-rfd-droppable-context-id":r}}),[r,u,O,E]),B=y?y.dragging.draggableId:null,G=C(()=>({droppableId:u,type:c,isUsingCloneFor:B}),[u,B,c]);return n.createElement(nJ.Provider,{value:G},s(S,h),function(){if(!y)return null;let{dragging:e,render:t}=y,r=n.createElement(n7,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(r,n)=>t(r,n,e));return l.createPortal(r,x())}())})}}]);