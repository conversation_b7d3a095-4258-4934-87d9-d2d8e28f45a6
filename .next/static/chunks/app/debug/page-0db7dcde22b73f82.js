(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9302],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>o,t:()=>s});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function o(...e){return n.useCallback(s(...e),e)}},24357:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},25609:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b});var n=r(95155),a=r(12115),s=r(27016),o=r(48879),l=r(30285),i=r(66695),c=r(26126),d=r(62523),u=r(85057),g=r(88539),h=r(78749),f=r(92657),p=r(24357),v=r(56671);function x(){var e;let{isSignedIn:t,getToken:r}=(0,s.d)(),{user:x}=(0,o.Jd)(),{get:b,post:m,isSignedIn:y}=function(){let{getToken:e,isSignedIn:t}=(0,s.d)(),r=(0,a.useCallback)(async function(r){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t)throw Error("User is not signed in");let a=await e();if(!a)throw Error("Failed to get authentication token");let s=await fetch("".concat("http://localhost:3000").concat(r),{...n,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a),...n.headers}});if(!s.ok){let e=await s.text();throw Error("API request failed: ".concat(s.status," ").concat(s.statusText," - ").concat(e))}return s},[e,t]),n=(0,a.useCallback)(async e=>(await r(e,{method:"GET"})).json(),[r]),o=(0,a.useCallback)(async(e,t)=>(await r(e,{method:"POST",body:t?JSON.stringify(t):void 0})).json(),[r]),l=(0,a.useCallback)(async(e,t)=>(await r(e,{method:"PUT",body:t?JSON.stringify(t):void 0})).json(),[r]),i=(0,a.useCallback)(async e=>(await r(e,{method:"DELETE"})).json(),[r]),c=(0,a.useCallback)(async(e,t)=>(await r(e,{method:"PATCH",body:t?JSON.stringify(t):void 0})).json(),[r]);return{makeAuthenticatedRequest:r,get:n,post:o,put:l,delete:i,patch:c,isSignedIn:t,getToken:e}}(),{token:j,fetchAndLogToken:k,copyTokenToClipboard:w}=function(){let{getToken:e,isSignedIn:t}=(0,s.d)(),[r,n]=(0,a.useState)(null),[o,l]=(0,a.useState)(!1),i=(0,a.useCallback)(async function(){let r=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(!t)return r&&console.log("❌ User not signed in"),null;l(!0);try{let t=await e();if(n(t),r&&t){console.group("\uD83D\uDD10 Clerk Token Retrieved"),console.log("\uD83C\uDFAB Token:",t),console.log("\uD83D\uDCCF Length:",t.length),console.log("⏰ Retrieved at:",new Date().toISOString());try{let e=JSON.parse(atob(t.split(".")[1]));console.log("\uD83C\uDD94 Subject (User ID):",e.sub),console.log("\uD83D\uDCC5 Issued At:",new Date(1e3*e.iat)),console.log("⏰ Expires At:",new Date(1e3*e.exp)),console.log("\uD83C\uDFE2 Issuer:",e.iss),console.log("\uD83D\uDCCB Full Payload:",e)}catch(e){console.log("Could not decode JWT payload")}console.groupEnd()}return t}catch(e){return console.error("❌ Error fetching token:",e),null}finally{l(!1)}},[e,t]);(0,a.useEffect)(()=>{t&&i(!0)},[t,i]);let c=(0,a.useCallback)(async()=>await i(!1),[i]),d=(0,a.useCallback)(()=>{r?(console.group("\uD83D\uDD10 Current Stored Token"),console.log("\uD83C\uDFAB Token:",r),console.log("\uD83D\uDCCF Length:",r.length),console.groupEnd()):console.log("❌ No token currently stored")},[r]);return{token:r,loading:o,isSignedIn:t,fetchAndLogToken:i,getTokenSilently:c,logCurrentToken:d,copyTokenToClipboard:()=>{r&&(navigator.clipboard.writeText(r),console.log("\uD83D\uDCCB Token copied to clipboard"))},tokenLength:(null==r?void 0:r.length)||0,hasToken:!!r}}(),[N,C]=(0,a.useState)(""),[T,A]=(0,a.useState)(!1),[E,P]=(0,a.useState)("/api/auth/me"),[S,R]=(0,a.useState)(""),[O,I]=(0,a.useState)(!1),_=async()=>{try{I(!0);let e=await r();e?(C(e),v.oR.success("Token retrieved successfully!"),console.log("\uD83C\uDFAB Fresh Clerk Token:",e)):v.oR.error("Failed to get token")}catch(e){console.error("Error getting token:",e),v.oR.error("Error getting token")}finally{I(!1)}},D=async()=>{try{I(!0),R("");let e=await b(E);R(JSON.stringify(e,null,2)),v.oR.success("API call successful!")}catch(t){let e=t instanceof Error?t.message:String(t);R("Error: ".concat(e)),v.oR.error("API call failed"),console.error("API call error:",t)}finally{I(!1)}},M=async()=>{try{I(!0),R("");let e=await r();if(!e)throw Error("No token available");let t=await fetch("".concat("http://localhost:3000").concat(E),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("HTTP ".concat(t.status,": ").concat(t.statusText));let n=await t.json();R(JSON.stringify(n,null,2)),v.oR.success("Manual API call successful!")}catch(t){let e=t instanceof Error?t.message:String(t);R("Error: ".concat(e)),v.oR.error("Manual API call failed"),console.error("Manual API call error:",t)}finally{I(!1)}},$=N?(e=>{try{let t=JSON.parse(atob(e.split(".")[1]));return{userId:t.sub,email:t.email,issuedAt:new Date(1e3*t.iat),expiresAt:new Date(1e3*t.exp),issuer:t.iss,fullPayload:t}}catch(e){return null}})(N):null;return t?(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(i.Zp,{children:[(0,n.jsx)(i.aR,{children:(0,n.jsxs)(i.ZB,{className:"flex items-center gap-2",children:["Clerk Token Management",(0,n.jsx)(c.E,{variant:t?"default":"destructive",children:t?"Signed In":"Not Signed In"})]})}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"font-semibold",children:"Current User"}),(0,n.jsxs)("div",{className:"text-sm space-y-1",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Email:"})," ",null==x||null==(e=x.emailAddresses[0])?void 0:e.emailAddress]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"User ID:"})," ",null==x?void 0:x.id]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Name:"})," ",null==x?void 0:x.firstName," ",null==x?void 0:x.lastName]})]})]}),(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsx)(l.$,{onClick:_,disabled:O,className:"w-full",children:O?"Getting Token...":"Get Fresh Clerk Token"})}),N&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("h4",{className:"font-semibold",children:"Current Token"}),(0,n.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>A(!T),children:T?(0,n.jsx)(h.A,{className:"h-4 w-4"}):(0,n.jsx)(f.A,{className:"h-4 w-4"})}),(0,n.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{N&&(navigator.clipboard.writeText(N),v.oR.success("Token copied to clipboard!"))},children:(0,n.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,n.jsx)("div",{className:"bg-muted p-3 rounded-lg",children:(0,n.jsx)("code",{className:"text-xs break-all",children:T?N:"".concat(N.substring(0,50),"...")})}),$&&(0,n.jsxs)("div",{className:"text-sm space-y-1",children:[(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"User ID:"})," ",$.userId]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Issued:"})," ",$.issuedAt.toLocaleString()]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Expires:"})," ",$.expiresAt.toLocaleString()]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("strong",{children:"Issuer:"})," ",$.issuer]})]})]})]})]}),(0,n.jsxs)(i.Zp,{children:[(0,n.jsx)(i.aR,{children:(0,n.jsx)(i.ZB,{children:"Test Backend API Calls"})}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(u.J,{htmlFor:"endpoint",children:"API Endpoint"}),(0,n.jsx)(d.p,{id:"endpoint",value:E,onChange:e=>P(e.target.value),placeholder:"/api/auth/me"})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[(0,n.jsx)(l.$,{onClick:D,disabled:O,variant:"default",children:O?"Calling...":"Test with useClerkApi Hook"}),(0,n.jsx)(l.$,{onClick:M,disabled:O,variant:"outline",children:O?"Calling...":"Test Manual API Call"})]}),S&&(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"font-semibold",children:"API Response"}),(0,n.jsx)(g.T,{value:S,readOnly:!0,className:"min-h-[200px] font-mono text-xs"})]})]})]}),(0,n.jsxs)(i.Zp,{children:[(0,n.jsx)(i.aR,{children:(0,n.jsx)(i.ZB,{children:"Code Examples"})}),(0,n.jsxs)(i.Wu,{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"font-semibold",children:"1. Using useClerkApi Hook (Recommended)"}),(0,n.jsx)("pre",{className:"bg-muted p-3 rounded-lg text-xs overflow-x-auto",children:"import { useClerkApi } from '@/hooks/useClerkApi';\n\nconst { get, post, put, delete: del } = useClerkApi();\n\n// GET request\nconst data = await get('/api/users/me');\n\n// POST request\nconst result = await post('/api/projects', { name: 'My Project' });"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"font-semibold",children:"2. Manual Token Usage"}),(0,n.jsx)("pre",{className:"bg-muted p-3 rounded-lg text-xs overflow-x-auto",children:"import { useAuth } from '@clerk/nextjs';\n\nconst { getToken } = useAuth();\n\nconst token = await getToken();\nconst response = await fetch('/api/endpoint', {\n  headers: {\n    'Authorization': `Bearer ${token}`,\n    'Content-Type': 'application/json'\n  }\n});"})]})]})]})]}):(0,n.jsxs)(i.Zp,{children:[(0,n.jsx)(i.aR,{children:(0,n.jsx)(i.ZB,{children:"Clerk Token Example"})}),(0,n.jsx)(i.Wu,{children:(0,n.jsx)("p",{className:"text-muted-foreground",children:"Please sign in to test Clerk token functionality."})})]})}function b(){return(0,n.jsx)("div",{className:"min-h-screen bg-background p-6",children:(0,n.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,n.jsxs)("div",{className:"text-center space-y-4",children:[(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Debug Dashboard"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Debug tools for testing various integrations and features"})]}),(0,n.jsx)("div",{className:"space-y-8",children:(0,n.jsx)(x,{})})]})})}},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(95155);r(12115);var a=r(99708),s=r(74466),o=r(59434);let l=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,asChild:s=!1,...i}=e,c=s?a.DX:"span";return(0,n.jsx)(c,{"data-slot":"badge",className:(0,o.cn)(l({variant:r}),t),...i})}},27016:(e,t,r)=>{"use strict";r.d(t,{PromisifiedAuthProvider:()=>i,d:()=>c});var n=r(48879),a=r(38572),s=r(35583),o=r(12115);let l=o.createContext(null);function i(e){let{authPromise:t,children:r}=e;return o.createElement(l.Provider,{value:t},r)}function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,s.useRouter)(),r=o.useContext(l),i=r;return(r&&"then"in r&&(i=o.use(r)),"undefined"!=typeof window)?(0,n.As)({...i,...e}):t?(0,n.As)(e):(0,a.hP)({...i,...e})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(95155),a=r(99708),s=r(74466);r(12115);var o=r(59434);let l=(0,s.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:r,size:s,asChild:i=!1,...c}=e,d=i?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,o.cn)(l({variant:r,size:s,className:t})),...c})}},35583:(e,t,r)=>{e.exports=r(63950)},38572:(e,t,r)=>{"use strict";r.d(t,{T5:()=>a.T5,hP:()=>n.hP,nO:()=>a.nO,yC:()=>s}),r(79419);var n=r(9693),a=r(62451);function s(e,t,r){let a=t.path||(null==r?void 0:r.path);return"path"===(t.routing||(null==r?void 0:r.routing)||"path")?a?{...r,...t,routing:"path"}:n.sb.throw((0,n.kd)(e)):t.path?n.sb.throw((0,n.s7)(e)):{...r,...t,path:void 0}}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var n=r(12115),a=r(63655),s=r(95155),o=n.forwardRef((e,t)=>(0,s.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var l=o},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(52596),a=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,n.$)(t))}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,n.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>i,sG:()=>l});var n=r(12115),a=r(47650),s=r(99708),o=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(a?r:t,{...s,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function i(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},63950:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useRouter",{enumerable:!0,get:function(){return s}});let n=r(12115),a=r(70901);function s(){return(0,n.useContext)(a.RouterContext)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>s,aR:()=>o,wL:()=>d});var n=r(95155);r(12115);var a=r(59434);function s(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",t),...r})}},70901:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(88229)._(r(12115)).default.createContext(null)},72377:(e,t,r)=>{Promise.resolve().then(r.bind(r,25609))},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>o});var n=r(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,o=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:o,defaultVariants:l}=t,i=Object.keys(o).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let s=a(t)||a(n);return o[e][s]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,i,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var n=r(95155);r(12115);var a=r(40968),s=r(59434);function o(e){let{className:t,...r}=e;return(0,n.jsx)(a.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},88539:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var n=r(95155),a=r(12115),s=r(59434);let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...a})});o.displayName="Textarea"},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>c,TL:()=>o});var n=r(12115),a=r(6101),s=r(95155);function o(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var o;let e,l,i=(o=r,(l=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(l=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),c=function(e,t){let r={...t};for(let n in t){let a=e[n],s=t[n];/^on[A-Z]/.test(n)?a&&s?r[n]=(...e)=>{let t=s(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...s}:"className"===n&&(r[n]=[a,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,a.t)(t,i):i),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...o}=e,l=n.Children.toArray(a),i=l.find(d);if(i){let e=i.props.children,a=l.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...o,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,s.jsx)(t,{...o,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=o("Slot"),i=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=i,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6671,8879,8441,8229,1684,7358],()=>t(72377)),_N_E=e.O()}]);