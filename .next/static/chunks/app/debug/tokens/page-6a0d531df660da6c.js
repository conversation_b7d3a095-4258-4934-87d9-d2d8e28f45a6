(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6513],{5206:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p,dynamic:()=>g});var r=t(95155),a=t(12115),n=t(27016),l=t(48879),i=t(30285),o=t(66695),c=t(26126),d=t(53904),m=t(78749),h=t(92657),u=t(24357),x=t(56671);function f(){var e,s;let{isSignedIn:t,getToken:f,userId:v}=(0,n.d)(),{user:g}=(0,l.Jd)(),{session:p}=(0,l.wV)(),[j,b]=(0,a.useState)({}),[N,k]=(0,a.useState)(!1),[y,w]=(0,a.useState)(!1),A=async()=>{if(t){w(!0);try{var e;let s=await f(),t=await f({template:"integration_firebase"}),r={jwt:s||void 0,template:t||void 0,custom:await f({template:"custom"})||void 0};if(b(r),console.group("\uD83D\uDD10 Clerk Token Information"),console.log("\uD83D\uDCCB User ID:",v),console.log("\uD83D\uDC64 User Email:",null==g||null==(e=g.emailAddresses[0])?void 0:e.emailAddress),console.log("\uD83C\uDFAB Session ID:",null==p?void 0:p.id),console.log("⏰ Session Last Active:",null==p?void 0:p.lastActiveAt),console.log("\uD83D\uDCC5 Session Expires:",null==p?void 0:p.expireAt),console.group("\uD83D\uDD11 Tokens"),console.log("JWT Token:",s),console.log("Template Token:",t),console.log("Token Length:",null==s?void 0:s.length),s)try{let e=JSON.parse(atob(s.split(".")[1]));console.log("JWT Payload:",e),console.log("Token Issued At:",new Date(1e3*e.iat)),console.log("Token Expires At:",new Date(1e3*e.exp))}catch(e){console.log("Could not decode JWT payload")}console.groupEnd(),console.groupEnd()}catch(e){console.error("❌ Error fetching tokens:",e),x.oR.error("Failed to fetch tokens")}finally{w(!1)}}};(0,a.useEffect)(()=>{t&&A()},[t]);let T=(e,s)=>{navigator.clipboard.writeText(e),x.oR.success("".concat(s," copied to clipboard"))},C=e=>e?N?e:"".concat(e.substring(0,20),"...").concat(e.substring(e.length-20)):"No token";return t?(0,r.jsxs)(o.Zp,{className:"w-full max-w-4xl",children:[(0,r.jsxs)(o.aR,{children:[(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:["\uD83D\uDD10 Clerk Token Debug",(0,r.jsx)(c.E,{variant:"default",children:"Signed In"})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(i.$,{onClick:A,disabled:y,size:"sm",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mr-2 ".concat(y?"animate-spin":"")}),"Refresh Tokens"]}),(0,r.jsxs)(i.$,{onClick:()=>k(!N),variant:"outline",size:"sm",children:[N?(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}):(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),N?"Hide":"Show"," Tokens"]})]})]}),(0,r.jsxs)(o.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"User ID"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:v})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Email"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:null==g||null==(e=g.emailAddresses[0])?void 0:e.emailAddress})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Session ID"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:null==p?void 0:p.id})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium",children:"Last Active"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:null==p||null==(s=p.lastActiveAt)?void 0:s.toLocaleString()})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"JWT Token"}),j.jwt&&(0,r.jsxs)(i.$,{onClick:()=>T(j.jwt,"JWT Token"),size:"sm",variant:"outline",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Copy"]})]}),(0,r.jsx)("div",{className:"p-3 bg-muted rounded-lg",children:(0,r.jsx)("code",{className:"text-sm break-all",children:j.jwt?C(j.jwt):"Loading..."})})]}),j.template&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold",children:"Template Token"}),(0,r.jsxs)(i.$,{onClick:()=>T(j.template,"Template Token"),size:"sm",variant:"outline",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Copy"]})]}),(0,r.jsx)("div",{className:"p-3 bg-muted rounded-lg",children:(0,r.jsx)("code",{className:"text-sm break-all",children:C(j.template)})})]}),(0,r.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-900 dark:text-blue-100",children:"How to use these tokens:"}),(0,r.jsxs)("ul",{className:"mt-2 text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"JWT Token:"})," Use for API authentication with your backend"]}),(0,r.jsxs)("li",{children:["• ",(0,r.jsx)("strong",{children:"Template Token:"})," Custom tokens with specific claims"]}),(0,r.jsx)("li",{children:"• Check the browser console for detailed token information"}),(0,r.jsx)("li",{children:"• Tokens are automatically refreshed by Clerk"})]})]})]})]}):(0,r.jsxs)(o.Zp,{className:"w-full max-w-2xl",children:[(0,r.jsx)(o.aR,{children:(0,r.jsxs)(o.ZB,{className:"flex items-center gap-2",children:["\uD83D\uDD10 Clerk Token Debug",(0,r.jsx)(c.E,{variant:"secondary",children:"Not Signed In"})]})}),(0,r.jsx)(o.Wu,{children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"Please sign in to view Clerk tokens."})})]})}var v=t(86473);let g="force-dynamic";function p(){return(0,r.jsx)(v.N,{children:(0,r.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Clerk Token Debug"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"View and debug Clerk authentication tokens. Check the browser console for detailed information."})]}),(0,r.jsx)(f,{})]})})}},18175:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},18994:(e,s,t)=>{"use strict";t.d(s,{N:()=>c});var r=t(95155),a=t(90786),n=t(59434),l=t(6874),i=t.n(l),o=t(35695);function c(){let e=(0,o.usePathname)(),{user:s}=(0,a.P)();return(0,r.jsx)("nav",{className:"flex items-center space-x-6",children:[{name:"Dashboard",href:"/user-dashboard"}].filter(s=>e!==s.href).map(s=>(0,r.jsxs)(i(),{href:s.href,className:(0,n.cn)("text-sm font-medium transition-colors relative",e===s.href?"text-[#166534] hover:text-[#166534]":"text-muted-foreground hover:text-[#166534]"),children:[s.name,e===s.href&&(0,r.jsx)("div",{className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full"})]},s.href))})}},24357:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>o});var r=t(95155);t(12115);var a=t(99708),n=t(74466),l=t(59434);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:s,variant:t,asChild:n=!1,...o}=e,c=n?a.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(i({variant:t}),s),...o})}},41752:(e,s,t)=>{"use strict";t.d(s,{B:()=>h});var r=t(95155),a=t(71007),n=t(381),l=t(34835),i=t(35695),o=t(99912),c=t(91950),d=t(44838),m=t(90786);function h(){let{user:e,signOut:s,firstName:t,email:h,fullName:u}=(0,m.P)(),x=(0,i.useRouter)();return e?(0,r.jsxs)(d.rI,{children:[(0,r.jsx)(d.ty,{asChild:!0,children:(0,r.jsx)("div",{children:(0,r.jsx)(o.V,{icon:a.A,text:t||"User",variant:"ghost",size:"md",layout:"horizontal",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:c.hS.md})})}),(0,r.jsxs)(d.SQ,{className:"w-56 bg-background border-border",align:"end",forceMount:!0,children:[(0,r.jsx)(d.lp,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium leading-none text-foreground",children:u||"User"}),(0,r.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:h||""})]})}),(0,r.jsx)(d.mB,{className:"bg-border"}),(0,r.jsxs)(d._2,{onClick:()=>{x.push("/profile")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,r.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Profile"})]}),(0,r.jsxs)(d._2,{onClick:()=>{x.push("/settings")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Settings"})]}),(0,r.jsx)(d.mB,{className:"bg-border"}),(0,r.jsxs)(d._2,{onClick:()=>{s()},className:"hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer",children:[(0,r.jsx)(l.A,{className:"mr-2 h-4 w-4"}),(0,r.jsx)("span",{children:"Log out"})]})]})]}):null}},51200:(e,s,t)=>{Promise.resolve().then(t.bind(t,5206))},53904:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},56611:(e,s,t)=>{"use strict";t.d(s,{c:()=>x});var r=t(95155),a=t(75525),n=t(74783),l=t(6874),i=t.n(l),o=t(35695),c=t(12115),d=t(30285),m=t(38382),h=t(66681),u=t(59434);function x(){let[e,s]=(0,c.useState)(!1),t=(0,o.usePathname)(),{user:l}=(0,h.A)(),x=(null==l?void 0:l.role)==="admin"?[{name:"Admin",href:"/admin",icon:a.A}]:[{name:"Dashboard",href:"/user-dashboard",icon:void 0}];return(0,r.jsxs)(m.cj,{open:e,onOpenChange:s,children:[(0,r.jsx)(m.CG,{asChild:!0,children:(0,r.jsxs)(d.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors",children:[(0,r.jsx)(n.A,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,r.jsx)(m.h,{side:"left",className:"pr-0 bg-background border-r border-border",children:(0,r.jsx)("nav",{className:"flex flex-col space-y-3 mt-6",children:x.filter(e=>t!==e.href).map(e=>{let a="/admin"===e.href?t.startsWith("/admin"):t===e.href;return(0,r.jsxs)(i(),{href:e.href,onClick:()=>s(!1),className:(0,u.cn)("text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",a?"text-[#166534] bg-[#166534]/10":"text-muted-foreground hover:text-[#166534]"),children:[e.icon&&(0,r.jsx)(e.icon,{className:"h-4 w-4"}),e.name,a&&(0,r.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full"})]},e.href)})})})]})}},59099:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>d});var r=t(95155);t(12115);var a=t(59434);function n(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",s),...t})}function l(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...t})}function i(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...t})}function o(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...t})}function c(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...t})}function d(e){let{className:s,...t}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",s),...t})}},72894:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},74783:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},78749:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},86473:(e,s,t)=>{"use strict";t.d(s,{N:()=>i});var r=t(95155),a=t(36996),n=t(92262);function l(e){let{children:s,showHeader:t=!0,showFooter:l=!0,constrainHeight:i=!1}=e;return(0,r.jsxs)("div",{className:"".concat(i?"h-screen":"min-h-screen"," flex flex-col bg-background"),children:[t&&(0,r.jsx)(n.Header,{}),(0,r.jsx)("main",{className:"flex-1 ".concat(i?"min-h-0":""),children:s}),l&&(0,r.jsx)(a.Footer,{})]})}function i(e){let{children:s}=e;return(0,r.jsx)(l,{showFooter:!1,constrainHeight:!0,children:(0,r.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:s})})}},92262:(e,s,t)=>{"use strict";t.d(s,{Header:()=>c});var r=t(95155);t(12115);var a=t(18994),n=t(56611),l=t(41752),i=t(66688),o=t(92236);function c(){return(0,r.jsx)("header",{className:"w-full sticky top-0 z-30 bg-background/80 backdrop-blur border-b border-border",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,r.jsx)(n.c,{})}),(0,r.jsx)(o.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,r.jsx)("div",{className:"hidden md:flex ml-6",children:(0,r.jsx)(a.N,{})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(i.U,{}),(0,r.jsx)(l.B,{})]})]})})}},92657:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,1917,8128,5748,387,8441,8229,1684,7358],()=>s(51200)),_N_E=e.O()}]);