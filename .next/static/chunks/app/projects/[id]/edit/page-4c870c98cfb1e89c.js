(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2326],{2564:(e,t,s)=>{"use strict";s.d(t,{Qg:()=>n,bL:()=>c});var a=s(12115),r=s(63655),i=s(95155),n=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=a.forwardRef((e,t)=>(0,i.jsx)(r.sG.span,{...e,ref:t,style:{...n,...e.style}}));l.displayName="VisuallyHidden";var c=l},18175:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},23016:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w,dynamic:()=>y});var a=s(95155),r=s(63560),i=s(51154),n=s(35169),l=s(6874),c=s.n(l),d=s(35695),o=s(12115),u=s(62177),m=s(70927),h=s(86473),p=s(30285),x=s(66695),j=s(62523),f=s(85057),v=s(59409),g=s(66681),b=s(25731);let y="force-dynamic",N=m.Ik({name:m.Yj().min(1,"Project name is required").max(100,"Project name must be less than 100 characters"),description:m.Yj().min(1,"Project description is required").max(500,"Description must be less than 500 characters"),status:m.k5(["active","completed","archived"])});function w(e){let{params:t}=e,[s,l]=(0,o.useState)(null),[m,y]=(0,o.useState)(!0),[w,k]=(0,o.useState)(!1),[z,A]=(0,o.useState)(null),[C,P]=(0,o.useState)(null),_=(0,d.useRouter)(),{user:M}=(0,g.A)(),{register:S,handleSubmit:D,formState:{errors:E},setValue:F,watch:q}=(0,u.mN)({resolver:(0,r.u)(N)}),L=q("status");(0,o.useEffect)(()=>{(async()=>{P((await t).id)})()},[t]),(0,o.useEffect)(()=>{C&&(async()=>{try{y(!0);let e=await b.Z.getProject(C);l(e),F("name",e.name),F("description",e.description),F("status",e.status)}catch(e){A(e.message||"Failed to fetch project")}finally{y(!1)}})()},[C,F]);let B=async e=>{if(s){k(!0),A(null);try{await b.Z.updateProject(s.id,e),_.push("/projects/".concat(s.id))}catch(e){A(e.message||"Failed to update project")}finally{k(!1)}}};return m?(0,a.jsx)(h.N,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(i.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading project..."})]})})}):z||!s?(0,a.jsx)(h.N,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:z||"Project not found"}),(0,a.jsx)(p.$,{asChild:!0,children:(0,a.jsx)(c(),{href:"/projects",children:"Back to Projects"})})]})})}):(null==M?void 0:M.id)!==s.userId&&(null==M?void 0:M.role)!=="admin"?(0,a.jsx)(h.N,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:"You don't have permission to edit this project"}),(0,a.jsx)(p.$,{asChild:!0,children:(0,a.jsx)(c(),{href:"/projects/".concat(s.id),children:"Back to Project"})})]})})}):(0,a.jsx)(h.N,{children:(0,a.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(p.$,{asChild:!0,variant:"outline",size:"sm",children:(0,a.jsxs)(c(),{href:"/projects/".concat(s.id),children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Back to Project"]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Project"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Update your project details"})]})]}),(0,a.jsxs)(x.Zp,{children:[(0,a.jsxs)(x.aR,{children:[(0,a.jsx)(x.ZB,{children:"Project Details"}),(0,a.jsx)(x.BT,{children:"Modify the information about your project"})]}),(0,a.jsx)(x.Wu,{children:(0,a.jsxs)("form",{onSubmit:D(B),className:"space-y-6",children:[z&&(0,a.jsx)("div",{className:"p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md",children:z}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"name",children:"Project Name *"}),(0,a.jsx)(j.p,{id:"name",placeholder:"Enter project name",...S("name"),className:E.name?"border-destructive":""}),E.name&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.name.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"description",children:"Description *"}),(0,a.jsx)("textarea",{id:"description",placeholder:"Describe your project...",rows:4,...S("description"),className:"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ".concat(E.description?"border-destructive":"")}),E.description&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.description.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.J,{htmlFor:"status",children:"Status"}),(0,a.jsxs)(v.l6,{value:L,onValueChange:e=>F("status",e),children:[(0,a.jsx)(v.bq,{children:(0,a.jsx)(v.yv,{placeholder:"Select status"})}),(0,a.jsxs)(v.gC,{children:[(0,a.jsx)(v.eb,{value:"active",children:"Active"}),(0,a.jsx)(v.eb,{value:"completed",children:"Completed"}),(0,a.jsx)(v.eb,{value:"archived",children:"Archived"})]})]}),E.status&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.status.message})]}),(0,a.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,a.jsx)(p.$,{type:"submit",disabled:w,children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Changes"}),(0,a.jsx)(p.$,{asChild:!0,type:"button",variant:"outline",children:(0,a.jsx)(c(),{href:"/projects/".concat(s.id),children:"Cancel"})})]})]})})]})]})})}},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,s)=>{"use strict";s.d(t,{b:()=>l});var a=s(12115),r=s(63655),i=s(95155),n=a.forwardRef((e,t)=>(0,i.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=n},58081:(e,t,s)=>{Promise.resolve().then(s.bind(s,23016))},59099:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>d,yv:()=>o});var a=s(95155);s(12115);var r=s(14582),i=s(66474),n=s(5196),l=s(47863),c=s(59434);function d(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:s="default",children:n,...l}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[n,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:s,position:i="popper",...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(p,{}),(0,a.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(x,{})]})})}function h(e){let{className:t,children:s,...i}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(l.A,{className:"size-4"})})}function x(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}},72894:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},74783:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(95155);s(12115);var r=s(40968),i=s(59434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,4043,8062,1917,8128,5748,387,3723,8441,8229,1684,7358],()=>t(58081)),_N_E=e.O()}]);