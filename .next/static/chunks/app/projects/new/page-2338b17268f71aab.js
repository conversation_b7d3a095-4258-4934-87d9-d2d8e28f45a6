(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8580],{18175:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},35169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,s)=>{"use strict";s.d(t,{b:()=>n});var r=s(12115),a=s(63655),i=s(95155),c=r.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var s;t.target.closest("button, input, select, textarea")||(null==(s=e.onMouseDown)||s.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var n=c},59099:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},69830:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>g,dynamic:()=>v});var r=s(95155),a=s(12115),i=s(35695),c=s(6874),n=s.n(c),d=s(62177),l=s(63560),o=s(70927),u=s(35169),m=s(51154),p=s(86473),h=s(30285),x=s(62523),j=s(85057),b=s(66695),f=s(25731);let v="force-dynamic",y=o.Ik({name:o.Yj().min(1,"Project name is required").max(100,"Project name must be less than 100 characters"),description:o.Yj().min(1,"Project description is required").max(500,"Description must be less than 500 characters")});function g(){let[e,t]=(0,a.useState)(!1),[s,c]=(0,a.useState)(null),o=(0,i.useRouter)(),{register:v,handleSubmit:g,formState:{errors:N}}=(0,d.mN)({resolver:(0,l.u)(y),defaultValues:{name:"",description:""}}),k=async e=>{t(!0),c(null);try{let t=await f.Z.createProject(e);o.push("/projects/".concat(t.id))}catch(e){c(e.message||"Failed to create project")}finally{t(!1)}};return(0,r.jsx)(p.N,{children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(h.$,{asChild:!0,variant:"outline",size:"sm",children:(0,r.jsxs)(n(),{href:"/projects",children:[(0,r.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Back to Projects"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Create New Project"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Start a new project and bring your ideas to life"})]})]}),(0,r.jsxs)(b.Zp,{children:[(0,r.jsxs)(b.aR,{children:[(0,r.jsx)(b.ZB,{children:"Project Details"}),(0,r.jsx)(b.BT,{children:"Provide basic information about your project"})]}),(0,r.jsx)(b.Wu,{children:(0,r.jsxs)("form",{onSubmit:g(k),className:"space-y-6",children:[s&&(0,r.jsx)("div",{className:"p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md",children:s}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j.J,{htmlFor:"name",children:"Project Name *"}),(0,r.jsx)(x.p,{id:"name",placeholder:"Enter project name",...v("name"),className:N.name?"border-destructive":""}),N.name&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:N.name.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(j.J,{htmlFor:"description",children:"Description *"}),(0,r.jsx)("textarea",{id:"description",placeholder:"Describe your project...",rows:4,...v("description"),className:"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ".concat(N.description?"border-destructive":"")}),N.description&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:N.description.message})]}),(0,r.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,r.jsx)(h.$,{type:"submit",disabled:e,children:e?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create Project"}),(0,r.jsx)(h.$,{asChild:!0,type:"button",variant:"outline",children:(0,r.jsx)(n(),{href:"/projects",children:"Cancel"})})]})]})})]})]})})}},72894:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},74783:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var r=s(95155);s(12115);var a=s(40968),i=s(59434);function c(e){let{className:t,...s}=e;return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},88011:(e,t,s)=>{Promise.resolve().then(s.bind(s,69830))}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,4043,1917,8128,5748,387,3723,8441,8229,1684,7358],()=>t(88011)),_N_E=e.O()}]);