(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[893],{26126:(e,s,r)=>{"use strict";r.d(s,{E:()=>n});var t=r(95155);r(12115);var a=r(99708),i=r(74466),l=r(59434);let c=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n(e){let{className:s,variant:r,asChild:i=!1,...n}=e,d=i?a.DX:"span";return(0,t.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(c({variant:r}),s),...n})}},42243:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(95155),a=r(12115),i=r(6874),l=r.n(i),c=r(86473),n=r(30285),d=r(62523),o=r(26126),x=r(66695),h=r(25731),u=r(51154),m=r(84616),j=r(47924),g=r(66932),f=r(5623),v=r(50757),p=r(44838);let b={active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",archived:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"};function N(){let[e,s]=(0,a.useState)([]),[r,i]=(0,a.useState)([]),[N,y]=(0,a.useState)(!0),[w,k]=(0,a.useState)(null),[C,A]=(0,a.useState)(""),[_,S]=(0,a.useState)("all");(0,a.useEffect)(()=>{(async()=>{try{y(!0);let e=await h.Z.getProjects(),r=Array.isArray(e)?e:[];s(r),i(r)}catch(e){k(e.message||"Failed to fetch projects"),s([]),i([])}finally{y(!1)}})()},[]),(0,a.useEffect)(()=>{let s=Array.isArray(e)?e:[];C&&(s=s.filter(e=>e.name.toLowerCase().includes(C.toLowerCase())||e.description.toLowerCase().includes(C.toLowerCase()))),"all"!==_&&(s=s.filter(e=>e.status===_)),i(s)},[e,C,_]);let E=async e=>{if(confirm("Are you sure you want to delete this project?"))try{await h.Z.deleteProject(e),s(s=>s.filter(s=>s.id!==e))}catch(e){alert(e.message||"Failed to delete project")}};return N?(0,t.jsx)(c.N,{children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Loading projects..."})]})})}):w?(0,t.jsx)(c.N,{children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-destructive mb-4",children:w}),(0,t.jsx)("button",{onClick:()=>window.location.reload(),className:"text-primary hover:underline",children:"Try again"})]})})}):(0,t.jsx)(c.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Projects"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage and organize your projects"})]}),(0,t.jsx)(n.$,{asChild:!0,className:"border",children:(0,t.jsxs)(l(),{href:"/user-dashboard",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"New Project"]})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(d.p,{placeholder:"Search projects...",value:C,onChange:e=>A(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(p.rI,{children:[(0,t.jsx)(p.ty,{asChild:!0,children:(0,t.jsxs)(n.$,{variant:"outline",children:[(0,t.jsx)(g.A,{className:"mr-2 h-4 w-4"}),"Status: ","all"===_?"All":_]})}),(0,t.jsxs)(p.SQ,{children:[(0,t.jsx)(p._2,{onClick:()=>S("all"),children:"All"}),(0,t.jsx)(p._2,{onClick:()=>S("active"),children:"Active"}),(0,t.jsx)(p._2,{onClick:()=>S("completed"),children:"Completed"}),(0,t.jsx)(p._2,{onClick:()=>S("archived"),children:"Archived"})]})]})]}),0===r.length?(0,t.jsx)(x.Zp,{children:(0,t.jsx)(x.Wu,{className:"flex flex-col items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:0===e.length?"No projects yet":"No projects found"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-4",children:0===e.length?"Create your first project to get started":"Try adjusting your search or filter criteria"}),0===e.length&&(0,t.jsx)(n.$,{asChild:!0,className:"border",children:(0,t.jsxs)(l(),{href:"/user-dashboard",children:[(0,t.jsx)(m.A,{className:"mr-2 h-4 w-4"}),"Create Project"]})})]})})}):(0,t.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:r.map(e=>(0,t.jsxs)(x.Zp,{className:"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer border",children:[(0,t.jsx)(x.aR,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)(x.ZB,{className:"truncate",children:e.name}),(0,t.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,t.jsx)(o.E,{variant:"secondary",className:b[e.status],children:e.status})})]}),(0,t.jsxs)(p.rI,{children:[(0,t.jsx)(p.ty,{asChild:!0,children:(0,t.jsx)(n.$,{variant:"ghost",size:"sm",children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(p.SQ,{align:"end",children:[(0,t.jsx)(p._2,{asChild:!0,children:(0,t.jsx)(l(),{href:"/projects/".concat(e.id),children:"View Details"})}),(0,t.jsx)(p._2,{asChild:!0,children:(0,t.jsx)(l(),{href:"/projects/".concat(e.id,"/edit"),children:"Edit"})}),(0,t.jsx)(p._2,{onClick:()=>E(e.id),className:"text-destructive",children:"Delete"})]})]})]})}),(0,t.jsxs)(x.Wu,{children:[(0,t.jsx)(x.BT,{className:"line-clamp-3 mb-4",children:e.description}),(0,t.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated"," ",(0,v.m)(new Date(e.updatedAt),{addSuffix:!0})]})]})]},e.id))})]})})}},82158:(e,s,r)=>{Promise.resolve().then(r.bind(r,42243))}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,898,1917,8128,5748,387,3723,8441,8229,1684,7358],()=>s(82158)),_N_E=e.O()}]);