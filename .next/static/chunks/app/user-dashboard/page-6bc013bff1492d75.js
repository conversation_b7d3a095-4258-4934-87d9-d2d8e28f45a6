(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9977],{5623:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},11496:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>M,dynamic:()=>F});var s=t(95155),a=t(12115),i=t(18994),n=t(56611),c=t(41752),l=t(66688),o=t(92236),d=t(38274),m=t(60760);function g(e){let{showStickyHeader:r=!0}=e,[t,g]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{if(!r)return;let e=()=>{g(window.scrollY>200)},t=!1,s=()=>{t||(requestAnimationFrame(()=>{e(),t=!1}),t=!0)};return window.addEventListener("scroll",s,{passive:!0}),()=>window.removeEventListener("scroll",s)},[r]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.P.header,{className:"w-full relative z-20 bg-transparent",initial:{opacity:1},animate:{opacity:t?.8:1,scale:t?.98:1},transition:{duration:.2},children:(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,s.jsx)(n.c,{})}),(0,s.jsx)(o.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,s.jsx)("div",{className:"hidden md:flex ml-6",children:(0,s.jsx)(i.N,{})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.U,{}),(0,s.jsx)(c.B,{})]})]})}),r&&(0,s.jsx)(m.N,{children:t&&(0,s.jsxs)(d.P.header,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border shadow-lg",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04] pointer-events-none",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between relative z-10",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,s.jsx)(n.c,{})}),(0,s.jsx)(o.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,s.jsx)("div",{className:"hidden md:flex ml-6",children:(0,s.jsx)(i.N,{})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.U,{}),(0,s.jsx)(c.B,{})]})]})]})})]})}var x=t(22716);function h(){return(0,s.jsx)("div",{className:"relative z-10 container mx-auto px-4 text-center py-20 md:py-20 flex-1 flex items-center",children:(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(x.j,{variant:"dashboard",title:"Let's build something amazing together!",showWelcomeBadge:!0,showFeatureBadges:!0})})})}var u=t(30285),p=t(66695),b=t(26126),v=t(92138),f=t(5623),y=t(35695);function j(){let e=(0,y.useRouter)(),r=e=>{switch(e){case"active":return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";case"in-progress":case"completed":return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";case"planning":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"}},t=e=>{switch(e){case"blue":return"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400";case"purple":return"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400";case"green":return"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400";case"indigo":return"bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400";case"red":return"bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400";default:return"bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400"}};return(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)(p.Zp,{className:"bg-gray-50 dark:bg-card",children:[(0,s.jsx)(p.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(p.ZB,{className:"flex items-center gap-2",children:"Recent Projects"}),(0,s.jsx)(p.BT,{children:"Your active projects and quick access to create new ones."})]}),(0,s.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>{e.push("/projects")},className:"border-2 hover:bg-primary/10 hover:text-primary transition-all duration-200",children:[(0,s.jsx)(v.A,{className:"mr-2 h-4 w-4"}),"View All"]})]})}),(0,s.jsx)(p.Wu,{children:(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[{id:"1",name:"Website Redesign",description:"Complete redesign of company website with modern UI",status:"active",progress:75,color:"blue",initials:"WR"},{id:"2",name:"Mobile App",description:"iOS and Android app development for the platform",status:"in-progress",progress:45,color:"purple",initials:"MA"},{id:"3",name:"Marketing Campaign",description:"Q1 marketing campaign planning and execution",status:"completed",progress:100,color:"green",initials:"MC"},{id:"4",name:"API Documentation",description:"Comprehensive API documentation and developer guides",status:"active",progress:85,color:"indigo",initials:"AD"},{id:"5",name:"Database Migration",description:"Migrate legacy database to new cloud infrastructure",status:"planning",progress:15,color:"red",initials:"DM"}].map(a=>(0,s.jsx)(p.Zp,{className:"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group",onClick:()=>e.push("/projects/".concat(a.id)),children:(0,s.jsxs)(p.Wu,{className:"px-4 py-0",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center ".concat(t(a.color)),children:(0,s.jsx)("span",{className:"font-semibold",children:a.initials})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(b.E,{variant:"secondary",className:r(a.status),children:a.status.replace("-"," ")}),(0,s.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-6 w-6",children:(0,s.jsx)(f.A,{className:"h-3 w-3"})})]})]}),(0,s.jsx)("h3",{className:"font-medium mb-2",children:a.name}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:a.description})]})},a.id))})})]})})}var w=t(14395),N=t(40646),k=t(14186),C=t(33109);function A(){let e=[{title:"Active Projects",value:"4",description:"Currently in progress",icon:w.A,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Completed Tasks",value:"23",description:"This month",icon:N.A,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Hours Saved",value:"47",description:"Through automation",icon:k.A,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Team Efficiency",value:"94%",description:"+12% from last month",icon:C.A,color:"text-indigo-600",bgColor:"bg-indigo-100 dark:bg-indigo-900/20"}];return(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)("div",{className:"flex flex-col gap-10",children:(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:e.map((e,r)=>(0,s.jsxs)(p.Zp,{className:"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer",children:[(0,s.jsxs)(p.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-1",children:[(0,s.jsx)(p.ZB,{className:"text-sm font-medium",children:e.title}),(0,s.jsx)("div",{className:"p-2 rounded-lg ".concat(e.bgColor),children:(0,s.jsx)(e.icon,{className:"h-4 w-4 ".concat(e.color)})})]}),(0,s.jsxs)(p.Wu,{className:"pt-1",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.value}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},r))})})})}var E=t(45885),z=t(16549),S=t(17739),B=t(90786),I=t(80733),T=t(51154);let F="force-dynamic";function M(){let e=(0,y.useRouter)(),{user:r,isSignedIn:t,isLoaded:i}=(0,B.P)(),{data:n,isLoading:c}=(0,S.Jd)(),{data:l}=(0,S.rE)(),o=(0,z.Zd)(),{currentStep:d}=(0,I.h)();return((0,a.useEffect)(()=>{if(i&&!t)return void e.replace("/")},[t,i,e]),(0,a.useEffect)(()=>{!t||!1!==l||n||c||o.isPending||(console.log("User not found in backend, auto-syncing..."),o.mutate())},[t,l,n,c,o]),i&&r)?(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 project-main-content"}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)(g,{}),(0,s.jsxs)("main",{className:"relative z-10",children:[(0,s.jsx)(h,{}),(0,s.jsxs)("div",{className:"container mx-auto px-4 space-y-8 pb-8",children:[(0,s.jsx)(A,{}),(0,s.jsx)(j,{})]})]}),"idle"!==d&&(0,s.jsx)(E.x,{})]}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(T.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading dashboard..."})]})})}},14395:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},40646:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},42030:(e,r,t)=>{Promise.resolve().then(t.bind(t,11496))},51154:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},66688:(e,r,t)=>{"use strict";t.d(r,{U:()=>d});var s=t(95155);t(12115);var a=t(62098),i=t(93509),n=t(51362),c=t(99912),l=t(91950),o=t(44838);function d(){let{setTheme:e}=(0,n.D)();return(0,s.jsxs)(o.rI,{children:[(0,s.jsx)(o.ty,{asChild:!0,children:(0,s.jsxs)("div",{children:[(0,s.jsx)(c.V,{icon:a.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:l.hS.lg,className:"dark:hidden"}),(0,s.jsx)(c.V,{icon:i.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",hoverScale:!0,iconClassName:l.hS.md,className:"hidden dark:block"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,s.jsxs)(o.SQ,{align:"end",className:"bg-background border-border",children:[(0,s.jsx)(o._2,{onClick:()=>e("light"),className:"hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary",children:"Light"}),(0,s.jsx)(o._2,{onClick:()=>e("dark"),className:"hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary",children:"Dark"}),(0,s.jsx)(o._2,{onClick:()=>e("system"),className:"hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary",children:"System"})]})]})}},91950:(e,r,t)=>{"use strict";t.d(r,{hS:()=>s});let s={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-7 w-7",xl:"h-8 w-8",badge:"h-4 w-4",navigation:"h-6 w-6",sidebar:"h-6 w-6",header:"h-6 w-6",button:"h-6 w-6",projectIcon:"h-6 w-6",actionIcon:"h-6 w-6",chatIcon:"h-6 w-6",profileIcon:"h-6 w-6",notificationIcon:"h-6 w-6"}},99912:(e,r,t)=>{"use strict";t.d(r,{V:()=>n});var s=t(95155),a=t(91950),i=t(59434);let n=t(12115).forwardRef((e,r)=>{let{className:t,icon:n,iconClassName:c,text:l,subText:o,textClassName:d,subTextClassName:m,badge:g,badgeClassName:x,trailing:h,variant:u="default",size:p="md",showBorder:b=!1,borderClassName:v,layout:f="horizontal",hoverColor:y="green",hoverScale:j=!0,children:w,...N}=e,k={sm:a.hS.sm,md:a.hS.md,lg:a.hS.lg};return(0,s.jsxs)("button",{ref:r,className:(0,i.cn)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer",{default:"bg-background hover:bg-sidebar-accent",ghost:"bg-transparent hover:bg-sidebar-accent",outline:"bg-background border border-input hover:bg-gray-100",accent:"bg-sidebar-accent hover:bg-sidebar-accent/80",secondary:"bg-gray-900 text-white hover:bg-gray-800"}[u],{sm:"icon-only"===f?"h-6 w-6 p-1":"h-8 px-2 py-1 text-xs",md:"icon-only"===f?"h-8 w-8 p-1.5":"h-10 px-3 py-2 text-sm",lg:"icon-only"===f?"h-10 w-10 p-2":"h-12 px-4 py-3 text-base"}[p],{green:"hover:text-[var(--primary-dark)]",grey:"hover:text-muted-foreground hover:bg-muted/50 hover:border-border",accent:"hover:text-accent-foreground"}[y],{horizontal:"flex-row gap-2",vertical:"flex-col gap-1","icon-only":"flex-row"}[f],b?"border ".concat(v||"border-border/30"):"","rounded-lg","horizontal"===f&&l?"justify-start":"","icon-only"===f?"justify-center":"",t),...N,children:[n&&(0,s.jsx)(n,{className:(0,i.cn)(c||k[p],j?"transition-transform duration-200 hover:scale-110":"")}),(l||o)&&"icon-only"!==f&&(0,s.jsxs)("div",{className:(0,i.cn)("flex flex-col","horizontal"===f?"flex-1 text-left":"text-center","vertical"===f?"items-center":"items-start"),children:[l&&(0,s.jsx)("span",{className:(0,i.cn)("font-medium truncate","sm"===p?"text-xs":"md"===p?"text-sm":"text-base",d),children:l}),o&&(0,s.jsx)("span",{className:(0,i.cn)("text-muted-foreground truncate","text-xs",m),children:o})]}),g&&(0,s.jsx)("span",{className:(0,i.cn)("bg-primary text-primary-foreground rounded-md flex items-center justify-center absolute -top-1 -right-1 z-10","sm"===p||"md"===p?"text-xs h-4 min-w-4":"text-sm h-5 min-w-5",x),children:g}),h&&"icon-only"!==f&&(0,s.jsx)("div",{className:"ml-auto",children:h}),w]})});n.displayName="SidebarButton"}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,8274,5123,7900,8198,3136,1917,8128,5748,3352,5885,3856,2716,8441,8229,1684,7358],()=>r(42030)),_N_E=e.O()}]);