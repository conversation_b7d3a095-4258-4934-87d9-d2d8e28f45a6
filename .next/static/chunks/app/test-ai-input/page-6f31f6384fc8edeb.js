(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8925],{51904:(e,r,t)=>{Promise.resolve().then(t.bind(t,86018))},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var a=t(52596),n=t(39688);function o(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}},86018:(e,r,t)=>{"use strict";t.d(r,{AI_Prompt:()=>c});var a=t(95155),n=t(99912),o=t(88539),i=t(59434),s=t(92138),l=t(12115);function c(){let[e,r]=(0,l.useState)(""),{textareaRef:t,adjustHeight:c}=function(e){let{minHeight:r,maxHeight:t}=e,a=(0,l.useRef)(null),n=(0,l.useCallback)(e=>{let n=a.current;if(!n)return;if(e){n.style.height="".concat(r,"px");return}n.style.height="".concat(r,"px");let o=Math.max(r,Math.min(n.scrollHeight,null!=t?t:Number.POSITIVE_INFINITY));n.style.height="".concat(o,"px")},[r,t]);return(0,l.useEffect)(()=>{let e=a.current;e&&(e.style.height="".concat(r,"px"))},[r]),(0,l.useEffect)(()=>{let e=()=>n();return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[n]),{textareaRef:a,adjustHeight:n}}({minHeight:60,maxHeight:200}),[d,h]=(0,l.useState)("GPT-4-1 Mini");return(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)("div",{className:"bg-black/5 dark:bg-white/5 rounded-2xl p-1.5",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"relative flex flex-col",children:(0,a.jsxs)("div",{className:"overflow-y-auto relative",style:{maxHeight:"400px"},children:[(0,a.jsx)(o.T,{id:"ai-input-15",value:e,placeholder:"What can I do for you?",className:(0,i.cn)("w-full bg-white/50 dark:bg-gray-700/60 rounded-xl px-3 py-2 pr-12 border border-gray-300/100 dark:border-gray-600/50 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 resize-none focus-visible:ring-0 focus-visible:ring-offset-0 transition-all duration-200","min-h-[120px]","hover:bg-gray-200/80 dark:hover:bg-gray-700/80 hover:border-gray-400 dark:hover:border-gray-500",e&&"bg-white/50 dark:bg-gray-700/90 border-gray-400 dark:border-gray-500"),ref:t,onKeyDown:t=>{"Enter"===t.key&&!t.shiftKey&&e.trim()&&(t.preventDefault(),r(""),c(!0))},onChange:e=>{r(e.target.value),c()}}),(0,a.jsx)("div",{className:"absolute bottom-2 right-2",children:(0,a.jsx)(n.V,{type:"button",icon:s.A,layout:"icon-only",size:"md",variant:e.trim()?"secondary":"ghost",iconClassName:"text-white",disabled:!e.trim(),"aria-label":"Send message",onClick:()=>{e.trim()&&(r(""),c(!0))},className:(0,i.cn)("transition-all duration-200",e.trim()?"bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg hover:shadow-xl":"opacity-40 bg-gray-300/50 dark:bg-gray-600/50 hover:opacity-60")})})]})})})})})}},88539:(e,r,t)=>{"use strict";t.d(r,{T:()=>i});var a=t(95155),n=t(12115),o=t(59434);let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,a.jsx)("textarea",{className:(0,o.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...n})});i.displayName="Textarea"},91950:(e,r,t)=>{"use strict";t.d(r,{hS:()=>a});let a={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-7 w-7",xl:"h-8 w-8",badge:"h-4 w-4",navigation:"h-6 w-6",sidebar:"h-6 w-6",header:"h-6 w-6",button:"h-6 w-6",projectIcon:"h-6 w-6",actionIcon:"h-6 w-6",chatIcon:"h-6 w-6",profileIcon:"h-6 w-6",notificationIcon:"h-6 w-6"}},92138:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},99912:(e,r,t)=>{"use strict";t.d(r,{V:()=>i});var a=t(95155),n=t(91950),o=t(59434);let i=t(12115).forwardRef((e,r)=>{let{className:t,icon:i,iconClassName:s,text:l,subText:c,textClassName:d,subTextClassName:h,badge:u,badgeClassName:g,trailing:x,variant:b="default",size:m="md",showBorder:f=!1,borderClassName:y,layout:p="horizontal",hoverColor:v="green",hoverScale:w=!0,children:k,...N}=e,j={sm:n.hS.sm,md:n.hS.md,lg:n.hS.lg};return(0,a.jsxs)("button",{ref:r,className:(0,o.cn)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer",{default:"bg-background hover:bg-sidebar-accent",ghost:"bg-transparent hover:bg-sidebar-accent",outline:"bg-background border border-input hover:bg-gray-100",accent:"bg-sidebar-accent hover:bg-sidebar-accent/80",secondary:"bg-gray-900 text-white hover:bg-gray-800"}[b],{sm:"icon-only"===p?"h-6 w-6 p-1":"h-8 px-2 py-1 text-xs",md:"icon-only"===p?"h-8 w-8 p-1.5":"h-10 px-3 py-2 text-sm",lg:"icon-only"===p?"h-10 w-10 p-2":"h-12 px-4 py-3 text-base"}[m],{green:"hover:text-[var(--primary-dark)]",grey:"hover:text-muted-foreground hover:bg-muted/50 hover:border-border",accent:"hover:text-accent-foreground"}[v],{horizontal:"flex-row gap-2",vertical:"flex-col gap-1","icon-only":"flex-row"}[p],f?"border ".concat(y||"border-border/30"):"","rounded-lg","horizontal"===p&&l?"justify-start":"","icon-only"===p?"justify-center":"",t),...N,children:[i&&(0,a.jsx)(i,{className:(0,o.cn)(s||j[m],w?"transition-transform duration-200 hover:scale-110":"")}),(l||c)&&"icon-only"!==p&&(0,a.jsxs)("div",{className:(0,o.cn)("flex flex-col","horizontal"===p?"flex-1 text-left":"text-center","vertical"===p?"items-center":"items-start"),children:[l&&(0,a.jsx)("span",{className:(0,o.cn)("font-medium truncate","sm"===m?"text-xs":"md"===m?"text-sm":"text-base",d),children:l}),c&&(0,a.jsx)("span",{className:(0,o.cn)("text-muted-foreground truncate","text-xs",h),children:c})]}),u&&(0,a.jsx)("span",{className:(0,o.cn)("bg-primary text-primary-foreground rounded-md flex items-center justify-center absolute -top-1 -right-1 z-10","sm"===m||"md"===m?"text-xs h-4 min-w-4":"text-sm h-5 min-w-5",g),children:u}),x&&"icon-only"!==p&&(0,a.jsx)("div",{className:"ml-auto",children:x}),k]})});i.displayName="SidebarButton"}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,8441,8229,1684,7358],()=>r(51904)),_N_E=e.O()}]);