(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9413],{2484:(e,t,r)=>{Promise.resolve().then(r.bind(r,96195))},27366:(e,t,r)=>{"use strict";r.d(t,{GF:()=>o,Ni:()=>s});var a=r(56671);let o=(e,t)=>{a.oR.success(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||4e3})},s=(e,t)=>{a.oR.error(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||5e3})}},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(95155),o=r(99708),s=r(74466);r(12115);var i=r(59434);let n=(0,s.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:s,asChild:d=!1,...l}=e,c=d?o.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(n({variant:r,size:s,className:t})),...l})}},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>n});var a=r(12115),o=r(63655),s=r(95155),i=a.forwardRef((e,t)=>(0,s.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var n=i},47614:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var a=r(95155),o=r(66695),s=r(92236);r(12115);let i=e=>{let{title:t,description:r,children:i,footer:n,header:d}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,a.jsx)("div",{className:"pt-8",children:(0,a.jsx)(s.g,{size:40,animated:!1,href:"/"})}),(0,a.jsxs)(o.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,a.jsxs)(o.aR,{className:"space-y-1",children:[d,(0,a.jsx)(o.ZB,{className:"text-2xl font-bold text-center",children:t}),r&&(0,a.jsx)(o.BT,{className:"text-center",children:r})]}),(0,a.jsx)(o.Wu,{className:"space-y-4",children:i}),n&&(0,a.jsx)(o.wL,{children:n})]})]})}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var a=r(52596),o=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var a=r(95155);r(12115);var o=r(59434);function s(e){let{className:t,type:r,...s}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>n});var a=r(12115),o=r(47650),s=r(99708),i=r(95155),n=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),o=a.forwardRef((e,a)=>{let{asChild:o,...s}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?r:t,{...s,ref:a})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function d(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>n,Zp:()=>s,aR:()=>i,wL:()=>c});var a=r(95155);r(12115);var o=r(59434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex px-6 [.border-t]:pt-6",t),...r})}},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var a=r(95155);r(12115);var o=r(40968),s=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)(o.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},92236:(e,t,r)=>{"use strict";r.d(t,{g:()=>d});var a=r(95155),o=r(11518),s=r.n(o);r(12115);var i=r(6874),n=r.n(i);let d=e=>{let{size:t=48,textSize:r=24,className:o="",animated:i=!1,showText:d=!1,href:l,animationSpeed:c=5}=e,u=t/48,g=32*u,m=20*u,f=+u,v=1.25*u,b=(0,a.jsxs)("div",{style:{position:"relative",width:t,height:t,display:"inline-block",animation:i?"logoFloat ".concat(3/c,"s ease-in-out infinite"):"none"},children:[(0,a.jsx)("div",{style:{position:"absolute",width:t,height:t,borderRadius:"50%",background:"#b4fd98",border:"".concat(f,"px solid #73ed47"),left:0,top:0,zIndex:1,transform:"rotate(70deg)",animation:i?"logoRotate ".concat(8/c,"s linear infinite"):"none"},children:(0,a.jsx)("div",{style:{position:"absolute",width:g,height:g,borderRadius:"50%",background:"#0A4000",border:"".concat(f,"px solid #73ed47"),left:t-g-f-v,top:f+v,zIndex:2,transform:"rotate(280deg)",animation:i?"logoPulse ".concat(2/c,"s ease-in-out infinite"):"none"},children:(0,a.jsx)("div",{style:{position:"absolute",width:m,height:m,borderRadius:"50%",background:"#fff",border:"".concat(f,"px solid #73ed47"),left:g-m-f-v,top:f+v,zIndex:3,animation:i?"logoGlow ".concat(4/c,"s ease-in-out infinite"):"none"}})})}),i&&(0,a.jsx)(s(),{id:"5428b1dfccf92ce4",children:"@-webkit-keyframes logoRotate{0%{-webkit-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);transform:rotate(430deg)}}@-moz-keyframes logoRotate{0%{-moz-transform:rotate(70deg);transform:rotate(70deg)}100%{-moz-transform:rotate(430deg);transform:rotate(430deg)}}@-o-keyframes logoRotate{0%{-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-o-transform:rotate(430deg);transform:rotate(430deg)}}@keyframes logoRotate{0%{-webkit-transform:rotate(70deg);-moz-transform:rotate(70deg);-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);-moz-transform:rotate(430deg);-o-transform:rotate(430deg);transform:rotate(430deg)}}"})]}),h=(0,a.jsxs)("div",{className:"flex items-center gap-3 ".concat(o),children:[b,(0,a.jsx)("span",{className:"font-bold text-foreground",style:{fontSize:"".concat(r,"px"),position:"relative",animation:i?"textGlow ".concat(3/c,"s ease-in-out infinite"):"none"},children:"siift.ai"})]}),x=d?h:b;return l?(0,a.jsx)(n(),{href:l,className:"hover:opacity-80 transition-opacity",children:x}):x}},96195:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k,dynamic:()=>y});var a=r(95155),o=r(63560),s=r(35169),i=r(28883),n=r(51154),d=r(6874),l=r.n(d),c=r(35695),u=r(12115),g=r(62177),m=r(70927),f=r(47614),v=r(30285),b=r(62523),h=r(85057),x=r(27366),p=r(31917);let y="force-dynamic",w=m.Ik({email:m.Yj().email("Please enter a valid email address")}),k=function(){let[e,t]=(0,u.useState)(!1),r=(0,c.useRouter)(),{register:d,handleSubmit:m,formState:{errors:y},getValues:k}=(0,g.mN)({resolver:(0,o.u)(w),defaultValues:{email:""}}),j=async e=>{t(!0);try{await p.N.forgotPassword(e.email),(0,x.GF)("Reset code sent!",{description:"Check your email for the verification code"}),r.push("/auth/reset-password?email=".concat(encodeURIComponent(e.email)))}catch(t){let e=t instanceof Error?t.message:"Failed to send reset code. Please try again.";(0,x.Ni)("Failed to send reset code",{description:e})}finally{t(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(f.V,{title:"Forgot your password?",description:"Enter your email address and we'll send you a verification code to reset your password",footer:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(l(),{href:"/auth/login",className:"inline-flex items-center text-sm font-medium text-primary hover:underline",children:[(0,a.jsx)(s.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})}),children:(0,a.jsxs)("form",{onSubmit:m(j),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.J,{htmlFor:"email",children:"Email address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{id:"email",type:"email",placeholder:"Enter your email address",...d("email"),className:"pl-10 ".concat(y.email?"border-destructive":"")})]}),y.email&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:y.email.message})]}),(0,a.jsx)(v.$,{type:"submit",className:"w-full",disabled:e,children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending code..."]}):"Send verification code"})]})})})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,4043,8988,1917,8441,8229,1684,7358],()=>t(2484)),_N_E=e.O()}]);