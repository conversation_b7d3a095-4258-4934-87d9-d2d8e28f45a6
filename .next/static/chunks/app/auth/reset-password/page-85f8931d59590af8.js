(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4089],{25311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A,dynamic:()=>k});var a=r(95155),s=r(63560),o=r(75525),n=r(35169),i=r(32919),d=r(78749),l=r(92657),c=r(51154),u=r(6874),m=r.n(u),g=r(35695),f=r(12115),x=r(62177),h=r(70927),p=r(47614),v=r(30285),b=r(62523),w=r(85057),y=r(27366),j=r(31917);let k="force-dynamic",N=h.Ik({code:h.Yj().min(6,"Verification code must be 6 digits").max(6,"Verification code must be 6 digits"),newPassword:h.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:h.Yj().min(8,"Password must be at least 8 characters")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function P(){let[e,t]=(0,f.useState)(!1),[r,u]=(0,f.useState)(!1),[h,k]=(0,f.useState)(!1),[P,A]=(0,f.useState)(null),[z,R]=(0,f.useState)(!1),[C,E]=(0,f.useState)(["","","","","",""]),S=(0,g.useRouter)(),D=(0,g.useSearchParams)().get("email")||"",{register:M,handleSubmit:_,formState:{errors:V},setValue:F,reset:$}=(0,x.mN)({resolver:(0,s.u)(N),defaultValues:{code:"",newPassword:"",confirmPassword:""}});(0,f.useEffect)(()=>{$(),E(["","","","","",""])},[$]);let L=async e=>{k(!0);try{await j.N.resetPassword(D,e.code,e.newPassword),(0,y.GF)("Password reset successful!",{description:"You can now log in with your new password"}),setTimeout(()=>{S.push("/auth/login")},2e3)}catch(t){let e=t instanceof Error?t.message:"Failed to reset password. Please try again.";(0,y.Ni)("Password reset failed",{description:e})}finally{k(!1)}},T=async()=>{if(D){k(!0),A(null);try{await j.N.forgotPassword(D),A("Verification code sent successfully!"),setTimeout(()=>A(null),3e3)}catch(e){A(e instanceof Error?e.message:"Failed to resend code. Please try again.")}finally{k(!1)}}};return z?(0,a.jsx)(p.V,{title:"Password reset successful!",header:(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"You can now sign in with your new password."})}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Redirecting to login page..."}),(0,a.jsx)(m(),{href:"/auth/login",className:"font-medium text-primary hover:underline",children:"Continue to login"})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(p.V,{title:"Reset your password",description:"Enter the verification code sent to your email address",footer:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["Didn't receive the code?"," ",(0,a.jsx)(v.$,{variant:"link",className:"px-0 font-normal",onClick:T,disabled:h||!D,children:"Resend code"})]}),(0,a.jsx)("div",{className:"flex w-full",children:(0,a.jsxs)(m(),{href:"/auth/forgot-password",className:"flex items-center text-sm font-medium text-primary hover:underline w-full pl-0",style:{marginLeft:0},children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]}),children:(0,a.jsxs)("form",{onSubmit:_(L),className:"space-y-4",children:[P&&(0,a.jsx)("div",{className:"p-3 text-sm border rounded-md ".concat(P.includes("successfully")?"text-green-700 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800":"text-destructive bg-destructive/10 border-destructive/20"),children:P}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Enter the 6-digit code sent to your email"}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:Array.from({length:6}).map((e,t)=>(0,a.jsx)(b.p,{type:"text",inputMode:"numeric",maxLength:1,value:C[t],autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:!1,className:"w-12 h-12 text-center text-lg font-semibold ".concat(V.code?"border-destructive":""),onChange:e=>{let r=e.target.value.replace(/\D/g,""),a=[...C];if(a[t]=r,E(a),F("code",a.join("")),t<5&&r){var s;let r=null==(s=e.target.parentElement)?void 0:s.children[t+1];null==r||r.focus()}},onKeyDown:e=>{if("Backspace"===e.key){if(!e.currentTarget.value&&t>0){var r;let a=null==(r=e.target.parentElement)?void 0:r.children[t-1];null==a||a.focus()}else if(e.currentTarget.value){let e=[...C];e[t]="",E(e),F("code",e.join(""))}}},onPaste:e=>{e.preventDefault();let t=e.clipboardData.getData("text").replace(/\D/g,"");if(t.length<=6){var r;E(t.split("").concat(Array(6).fill("")).slice(0,6)),F("code",t);let a=Math.min(t.length,5),s=null==(r=e.target.parentElement)?void 0:r.children[a];null==s||s.focus()}}},t))}),(0,a.jsx)("input",{type:"hidden",...M("code")}),V.code&&(0,a.jsx)("p",{className:"text-sm text-destructive text-center",children:V.code.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(w.J,{htmlFor:"newPassword",children:"New password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{id:"newPassword",type:e?"text":"password",placeholder:"Enter new password",autoComplete:"new-password",...M("newPassword"),className:"pl-10 pr-10 ".concat(V.newPassword?"border-destructive":"")}),(0,a.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>t(!e),children:e?(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):(0,a.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})})]}),V.newPassword&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:V.newPassword.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(w.J,{htmlFor:"confirmPassword",children:"Confirm new password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(b.p,{id:"confirmPassword",type:r?"text":"password",placeholder:"Confirm new password",autoComplete:"new-password",...M("confirmPassword"),className:"pl-10 pr-10 ".concat(V.confirmPassword?"border-destructive":"")}),(0,a.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>u(!r),children:r?(0,a.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):(0,a.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})})]}),V.confirmPassword&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:V.confirmPassword.message})]}),(0,a.jsx)(v.$,{type:"submit",className:"w-full",disabled:h,children:h?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Resetting password..."]}):"Reset password"})]})})})})}function A(){return(0,a.jsx)(f.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(P,{})})}},27366:(e,t,r)=>{"use strict";r.d(t,{GF:()=>s,Ni:()=>o});var a=r(56671);let s=(e,t)=>{a.oR.success(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||4e3})},o=(e,t)=>{a.oR.error(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||5e3})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var a=r(95155),s=r(99708),o=r(74466);r(12115);var n=r(59434);let i=(0,o.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:o,asChild:d=!1,...l}=e,c=d?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:o,className:t})),...l})}},32919:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var a=r(12115),s=r(63655),o=r(95155),n=a.forwardRef((e,t)=>(0,o.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},46178:(e,t,r)=>{Promise.resolve().then(r.bind(r,25311))},47614:(e,t,r)=>{"use strict";r.d(t,{V:()=>n});var a=r(95155),s=r(66695),o=r(92236);r(12115);let n=e=>{let{title:t,description:r,children:n,footer:i,header:d}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,a.jsx)("div",{className:"pt-8",children:(0,a.jsx)(o.g,{size:40,animated:!1,href:"/"})}),(0,a.jsxs)(s.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,a.jsxs)(s.aR,{className:"space-y-1",children:[d,(0,a.jsx)(s.ZB,{className:"text-2xl font-bold text-center",children:t}),r&&(0,a.jsx)(s.BT,{className:"text-center",children:r})]}),(0,a.jsx)(s.Wu,{className:"space-y-4",children:n}),i&&(0,a.jsx)(s.wL,{children:i})]})]})}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var a=r(52596),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var a=r(95155);r(12115);var s=r(59434);function o(e){let{className:t,type:r,...o}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>i});var a=r(12115),s=r(47650),o=r(99708),n=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),s=a.forwardRef((e,a)=>{let{asChild:s,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(s?r:t,{...o,ref:a})});return s.displayName=`Primitive.${t}`,{...e,[t]:s}},{});function d(e,t){e&&s.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>o,aR:()=>n,wL:()=>c});var a=r(95155);r(12115);var s=r(59434);function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...r})}function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex px-6 [.border-t]:pt-6",t),...r})}},75525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var a=r(95155);r(12115);var s=r(40968),o=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},92236:(e,t,r)=>{"use strict";r.d(t,{g:()=>d});var a=r(95155),s=r(11518),o=r.n(s);r(12115);var n=r(6874),i=r.n(n);let d=e=>{let{size:t=48,textSize:r=24,className:s="",animated:n=!1,showText:d=!1,href:l,animationSpeed:c=5}=e,u=t/48,m=32*u,g=20*u,f=+u,x=1.25*u,h=(0,a.jsxs)("div",{style:{position:"relative",width:t,height:t,display:"inline-block",animation:n?"logoFloat ".concat(3/c,"s ease-in-out infinite"):"none"},children:[(0,a.jsx)("div",{style:{position:"absolute",width:t,height:t,borderRadius:"50%",background:"#b4fd98",border:"".concat(f,"px solid #73ed47"),left:0,top:0,zIndex:1,transform:"rotate(70deg)",animation:n?"logoRotate ".concat(8/c,"s linear infinite"):"none"},children:(0,a.jsx)("div",{style:{position:"absolute",width:m,height:m,borderRadius:"50%",background:"#0A4000",border:"".concat(f,"px solid #73ed47"),left:t-m-f-x,top:f+x,zIndex:2,transform:"rotate(280deg)",animation:n?"logoPulse ".concat(2/c,"s ease-in-out infinite"):"none"},children:(0,a.jsx)("div",{style:{position:"absolute",width:g,height:g,borderRadius:"50%",background:"#fff",border:"".concat(f,"px solid #73ed47"),left:m-g-f-x,top:f+x,zIndex:3,animation:n?"logoGlow ".concat(4/c,"s ease-in-out infinite"):"none"}})})}),n&&(0,a.jsx)(o(),{id:"5428b1dfccf92ce4",children:"@-webkit-keyframes logoRotate{0%{-webkit-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);transform:rotate(430deg)}}@-moz-keyframes logoRotate{0%{-moz-transform:rotate(70deg);transform:rotate(70deg)}100%{-moz-transform:rotate(430deg);transform:rotate(430deg)}}@-o-keyframes logoRotate{0%{-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-o-transform:rotate(430deg);transform:rotate(430deg)}}@keyframes logoRotate{0%{-webkit-transform:rotate(70deg);-moz-transform:rotate(70deg);-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);-moz-transform:rotate(430deg);-o-transform:rotate(430deg);transform:rotate(430deg)}}"})]}),p=(0,a.jsxs)("div",{className:"flex items-center gap-3 ".concat(s),children:[h,(0,a.jsx)("span",{className:"font-bold text-foreground",style:{fontSize:"".concat(r,"px"),position:"relative",animation:n?"textGlow ".concat(3/c,"s ease-in-out infinite"):"none"},children:"siift.ai"})]}),v=d?p:h;return l?(0,a.jsx)(i(),{href:l,className:"hover:opacity-80 transition-opacity",children:v}):v}},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,4043,8988,1917,8441,8229,1684,7358],()=>t(46178)),_N_E=e.O()}]);