(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[983],{5041:(e,t,r)=>{"use strict";r.d(t,{n:()=>d});var s=r(12115),a=r(34560),o=r(7165),n=r(25910),i=r(52020),l=class extends n.Q{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#a()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,i.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,i.EN)(t.mutationKey)!==(0,i.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#a(),this.#o(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#a(),this.#o()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#a(){let e=this.#r?.state??(0,a.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#o(e){o.jG.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},c=r(26715);function d(e,t){let r=(0,c.jE)(t),[a]=s.useState(()=>new l(r,e));s.useEffect(()=>{a.setOptions(e)},[a,e]);let n=s.useSyncExternalStore(s.useCallback(e=>a.subscribe(o.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),d=s.useCallback((e,t)=>{a.mutate(e,t).catch(i.lQ)},[a]);if(n.error&&(0,i.GU)(a.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},13432:(e,t,r)=>{"use strict";r.d(t,{WG:()=>o,lH:()=>a,qQ:()=>s});let s=new(r(87017)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<3,refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,onError:e=>{console.error("Mutation error:",e)}}}}),a={user:e=>["user",e],userProfile:e=>["user","profile",e],projects:()=>["projects"],project:e=>["projects",e],projectTasks:e=>["projects",e,"tasks"],admin:{analytics:()=>["admin","analytics"],summary:()=>["admin","analytics","summary"],users:e=>["admin","users",e],feedback:e=>["admin","feedback",e]},businessSections:e=>["business-sections",e]},o={user:e=>{s.invalidateQueries({queryKey:a.user(e)}),s.invalidateQueries({queryKey:a.userProfile(e)})},projects:()=>{s.invalidateQueries({queryKey:a.projects()})},project:e=>{s.invalidateQueries({queryKey:a.project(e)}),s.invalidateQueries({queryKey:a.projectTasks(e)})},admin:()=>{s.invalidateQueries({queryKey:a.admin.analytics()}),s.invalidateQueries({queryKey:a.admin.summary()}),s.invalidateQueries({queryKey:a.admin.users()}),s.invalidateQueries({queryKey:a.admin.feedback()})}}},13512:(e,t,r)=>{Promise.resolve().then(r.bind(r,56117))},16549:(e,t,r)=>{"use strict";r.d(t,{Zd:()=>c,j9:()=>l});var s=r(26715),a=r(5041),o=r(90786),n=r(13432),i=r(56671);function l(){let{userId:e,getToken:t}=(0,o.P)();(0,s.jE)();let r=function(){let{userId:e,getToken:t}=(0,o.P)(),r=(0,s.jE)();return(0,a.n)({mutationFn:async r=>{let{internalUserId:s,userData:a}=r,o=await t(),n=await fetch("".concat("http://localhost:3000","/api/users/").concat(s),{method:"PATCH",headers:{"Content-Type":"application/json",...o&&{Authorization:"Bearer ".concat(o)}},body:JSON.stringify({...a,clerkId:e})});if(!n.ok){let e=await n.text();throw Error("Failed to update user: ".concat(n.status," ").concat(e))}return n.json()},onMutate:async t=>{let{userData:s}=t;await r.cancelQueries({queryKey:n.lH.user(e)});let a=r.getQueryData(n.lH.user(e));if(a){let t={...a,...s,updatedAt:new Date().toISOString()};r.setQueryData(n.lH.user(e),t)}return{previousUser:a}},onError:(t,s,a)=>{(null==a?void 0:a.previousUser)&&r.setQueryData(n.lH.user(e),a.previousUser),console.error("Failed to update user:",t),i.oR.error("Failed to update profile. Please try again.")},onSettled:()=>{n.WG.user(e)},onSuccess:e=>{i.oR.success("Profile updated successfully!"),console.log("User updated successfully:",e)}})}();return(0,a.n)({mutationFn:async s=>{let a=await t(),o=await fetch("".concat("http://localhost:3000","/api/users/clerk/").concat(e),{headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)}}});if(!o.ok)throw Error("User not found in backend. Please sync your account first.");let n=await o.json();return r.mutateAsync({internalUserId:n.id,userData:s})},onError:e=>{console.error("Failed to update user by Clerk ID:",e),i.oR.error(e.message||"Failed to update profile. Please try again.")}})}function c(){let{user:e,userId:t,email:r,firstName:l,lastName:c}=(0,o.P)(),d=function(){let{getToken:e}=(0,o.P)(),t=(0,s.jE)();return(0,a.n)({mutationFn:async t=>{let r=await e(),s="http://localhost:3000",a=await fetch("".concat(s,"/api/users"),{method:"POST",headers:{"Content-Type":"application/json",...r&&{Authorization:"Bearer ".concat(r)}},body:JSON.stringify({...t,role:t.role||"user",status:t.status||"active",timezone:t.timezone||"UTC",preferences:t.preferences||{notifications:!0,theme:"system",language:"en"}})});if(!a.ok){let e=await a.text();if(console.error("Backend response error:",{status:a.status,statusText:a.statusText,errorText:e,url:"".concat(s,"/api/users")}),400===a.status&&(e.includes("already exists")||e.includes("clerk ID already exists"))){console.log("User already exists, fetching existing user...");let e=await fetch("".concat(s,"/api/users/clerk/").concat(t.clerkId),{headers:{"Content-Type":"application/json",...r&&{Authorization:"Bearer ".concat(r)}}});if(e.ok){let t=await e.json();return console.log("Found existing user:",t),t}throw console.log("Could not fetch existing user, but user exists in backend"),Error("User already exists in backend")}throw Error("Failed to create user: ".concat(a.status," ").concat(a.statusText," - ").concat(e))}return a.json()},onSuccess:(e,r)=>{t.setQueryData(n.lH.user(r.clerkId),e),n.WG.user(r.clerkId),i.oR.success("Account synced successfully!"),console.log("User synced successfully in backend:",e)},onError:(e,t)=>{console.error("Failed to create user in backend:",e),console.error("User data that failed:",t),e.message&&e.message.includes("already exists")?console.log("User already exists in backend - this is expected"):i.oR.error("Failed to sync account: ".concat(e.message))}})}();return(0,a.n)({mutationFn:async()=>{if(!e||!t)throw Error("User not available");let s={email:r||"",firstName:l||"",lastName:c||"",clerkId:t,role:"user",status:"active",avatarUrl:e.imageUrl||"",bio:"",timezone:"UTC",preferences:{notifications:!0,theme:"system",language:"en"}};return d.mutateAsync(s)},onError:e=>{console.error("Failed to sync user to backend:",e),i.oR.error("Failed to sync account. Please contact support.")}})}},22346:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var s=r(95155);r(12115);var a=r(87489),o=r(59434);function n(e){let{className:t,orientation:r="horizontal",decorative:n=!0,...i}=e;return(0,s.jsx)(a.b,{"data-slot":"separator",decorative:n,orientation:r,className:(0,o.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...i})}},27016:(e,t,r)=>{"use strict";r.d(t,{PromisifiedAuthProvider:()=>l,d:()=>c});var s=r(48879),a=r(38572),o=r(35583),n=r(12115);let i=n.createContext(null);function l(e){let{authPromise:t,children:r}=e;return n.createElement(i.Provider,{value:t},r)}function c(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,o.useRouter)(),r=n.useContext(i),l=r;return(r&&"then"in r&&(l=n.use(r)),"undefined"!=typeof window)?(0,s.As)({...l,...e}):t?(0,s.As)(e):(0,a.hP)({...l,...e})}},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(95155),a=r(99708),o=r(74466);r(12115);var n=r(59434);let i=(0,o.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:o,asChild:l=!1,...c}=e,d=l?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:o,className:t})),...c})}},32919:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},35583:(e,t,r)=>{e.exports=r(63950)},38572:(e,t,r)=>{"use strict";r.d(t,{T5:()=>a.T5,hP:()=>s.hP,nO:()=>a.nO,yC:()=>o}),r(79419);var s=r(9693),a=r(62451);function o(e,t,r){let a=t.path||(null==r?void 0:r.path);return"path"===(t.routing||(null==r?void 0:r.routing)||"path")?a?{...r,...t,routing:"path"}:s.sb.throw((0,s.kd)(e)):t.path?s.sb.throw((0,s.s7)(e)):{...r,...t,path:void 0}}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var s=r(12115),a=r(63655),o=r(95155),n=s.forwardRef((e,t)=>(0,o.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},47614:(e,t,r)=>{"use strict";r.d(t,{V:()=>n});var s=r(95155),a=r(66695),o=r(92236);r(12115);let n=e=>{let{title:t,description:r,children:n,footer:i,header:l}=e;return(0,s.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,s.jsx)("div",{className:"pt-8",children:(0,s.jsx)(o.g,{size:40,animated:!1,href:"/"})}),(0,s.jsxs)(a.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(a.aR,{className:"space-y-1",children:[l,(0,s.jsx)(a.ZB,{className:"text-2xl font-bold text-center",children:t}),r&&(0,s.jsx)(a.BT,{className:"text-center",children:r})]}),(0,s.jsx)(a.Wu,{className:"space-y-4",children:n}),i&&(0,s.jsx)(a.wL,{children:i})]})]})}},56117:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_,dynamic:()=>P});var s=r(95155),a=r(48879),o=r(63560),n=r(71007),i=r(28883),l=r(32919),c=r(78749),d=r(92657),u=r(51154),m=r(57340),h=r(6874),f=r.n(h),p=r(35695),g=r(12115),v=r(62177),x=r(70927),b=r(47614),y=r(62523),w=r(85057),j=r(22346),k=r(56671),N=r(30285),R=r(99912),C=r(16549);let P="force-dynamic",A=x.Ik({firstName:x.Yj().min(1,"First name is required"),lastName:x.Yj().min(1,"Last name is required"),email:x.Yj().email("Please enter a valid email address"),password:x.Yj().min(8,"Password must be at least 8 characters")});function _(){let{isLoaded:e,signUp:t,setActive:r}=(0,a.yC)(),[h,x]=(0,g.useState)(!1),[P,_]=(0,g.useState)(!1),S=(0,p.useRouter)(),z=(0,C.Zd)(),{register:M,handleSubmit:O,formState:{errors:E}}=(0,v.mN)({resolver:(0,o.u)(A)}),F=async s=>{if(e){x(!0);try{console.log("Creating sign up with data:",{firstName:s.firstName,lastName:s.lastName,emailAddress:s.email});let e=await t.create({emailAddress:s.email,password:s.password});if(e&&(s.firstName||s.lastName))try{await t.update({firstName:s.firstName,lastName:s.lastName}),console.log("User profile updated with names")}catch(e){console.warn("Could not update user profile with names:",e)}if(console.log("Sign up created successfully:",e),"complete"===e.status){await r({session:e.createdSessionId});try{console.log("Syncing user to backend..."),await z.mutateAsync(),console.log("User synced to backend successfully")}catch(e){console.warn("Failed to sync user to backend:",e),e.message&&e.message.includes("already exists")?console.log("User already exists in backend, proceeding to dashboard"):console.error("Unexpected sync error:",e)}k.oR.success("Account created successfully!"),S.push("/user-dashboard")}else console.log("Sign up status:",e.status),console.log("Sign up result:",e),"missing_requirements"===e.status?k.oR.error("Please complete all required fields and try again."):"abandoned"===e.status?k.oR.error("Sign up was abandoned. Please try again."):(console.log("Redirecting to dashboard despite incomplete status"),k.oR.success("Account created successfully!"),S.push("/user-dashboard"))}catch(t){console.error("Sign up error:",t),console.error("Error details:",{errors:t.errors,message:t.message,status:t.status});let e="Sign up failed. Please try again.";if(t.errors&&t.errors.length>0){var a,o,n;let r=t.errors[0];switch(r.code){case"form_identifier_exists":e="An account with this email already exists. Please try signing in instead.";break;case"form_password_pwned":e="This password has been found in a data breach. Please choose a stronger password.";break;case"form_password_length_too_short":e="Password must be at least 8 characters long.";break;case"form_password_validation_failed":e="Password is too weak. Please include uppercase, lowercase, numbers, and special characters.";break;case"form_param_format_invalid":e=(null==(a=r.meta)?void 0:a.param_name)==="email_address"?"Please enter a valid email address.":"Invalid ".concat((null==(o=r.meta)?void 0:o.param_name)||"field",". Please check your input.");break;case"form_param_nil":e="".concat((null==(n=r.meta)?void 0:n.param_name)||"Required field"," is required.");break;default:!r.message||r.message.includes("first_name")||r.message.includes("last_name")?r.message&&(r.message.includes("first_name")||r.message.includes("last_name"))&&(e="There was an issue with the name fields. Please try again or contact support."):e=r.message}}else t.message&&(e=t.message);k.oR.error(e)}finally{x(!1)}}},U=async r=>{if(e)try{await t.authenticateWithRedirect({strategy:r,redirectUrl:"/sso-callback",redirectUrlComplete:"/user-dashboard"})}catch(e){console.error("Social sign up error:",e),k.oR.error("Social sign up failed. Please try again.")}};return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col items-center justify-center",children:[(0,s.jsxs)(b.V,{title:"Create your account",description:"Get started with your free account",footer:(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:["Already have an account?"," ",(0,s.jsx)(f(),{href:"/auth/login",className:"text-primary hover:text-primary/90 font-medium",children:"Sign in"})]}),children:[(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)(R.V,{variant:"outline",className:"w-full",layout:"horizontal",hoverColor:"green",hoverScale:!0,showBorder:!0,borderClassName:"grey border-1",onClick:()=>U("oauth_google"),disabled:!e,children:[(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)(j.w,{className:"w-full"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,s.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with email"})})]}),(0,s.jsxs)("form",{onSubmit:O(F),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"firstName",children:"First name"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(y.p,{id:"firstName",type:"text",placeholder:"John",...M("firstName"),className:"pl-10 ".concat(E.firstName?"border-destructive":"")})]}),E.firstName&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:E.firstName.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"lastName",children:"Last name"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(y.p,{id:"lastName",type:"text",placeholder:"Doe",...M("lastName"),className:"pl-10 ".concat(E.lastName?"border-destructive":"")})]}),E.lastName&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:E.lastName.message})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"email",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(y.p,{id:"email",type:"email",placeholder:"<EMAIL>",...M("email"),className:"pl-10 ".concat(E.email?"border-destructive":"")})]}),E.email&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:E.email.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(y.p,{id:"password",type:P?"text":"password",placeholder:"Create a strong password",...M("password"),className:"pl-10 pr-10 ".concat(E.password?"border-destructive":"")}),(0,s.jsx)("button",{type:"button",onClick:()=>_(!P),className:"absolute right-3 top-1.5 h-4 w-4 text-muted-foreground hover:text-foreground",children:P?(0,s.jsx)(c.A,{}):(0,s.jsx)(d.A,{})})]}),E.password&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:E.password.message})]}),(0,s.jsx)(N.$,{type:"submit",className:"w-full",disabled:h||!e,children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating account..."]}):"Create account"})]})]}),(0,s.jsx)("div",{className:"text-center mt-6",children:(0,s.jsxs)(f(),{href:"/",className:"inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),"Back to home"]})})]})}},57340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(52596),a=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var s=r(95155);r(12115);var a=r(59434);function o(e){let{className:t,type:r,...o}=e;return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>i});var s=r(12115),a=r(47650),o=r(99708),n=r(95155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...o,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},63950:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useRouter",{enumerable:!0,get:function(){return o}});let s=r(12115),a=r(70901);function o(){return(0,s.useContext)(a.RouterContext)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>n,wL:()=>d});var s=r(95155);r(12115);var a=r(59434);function o(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...r})}function n(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",t),...r})}},70901:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return s}});let s=r(88229)._(r(12115)).default.createContext(null)},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},78749:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85057:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var s=r(95155);r(12115);var a=r(40968),o=r(59434);function n(e){let{className:t,...r}=e;return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},87489:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var s=r(12115),a=r(63655),o=r(95155),n="horizontal",i=["horizontal","vertical"],l=s.forwardRef((e,t)=>{var r;let{decorative:s,orientation:l=n,...c}=e,d=(r=l,i.includes(r))?l:n;return(0,o.jsx)(a.sG.div,{"data-orientation":d,...s?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},90786:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var s=r(27016),a=r(48879),o=r(35695),n=r(56671);function i(){var e,t,r;let{isSignedIn:i,signOut:l,getToken:c}=(0,s.d)(),{user:d,isLoaded:u}=(0,a.Jd)(),{signIn:m,isLoaded:h}=(0,a.go)(),{signUp:f,isLoaded:p}=(0,a.yC)(),g=(0,o.useRouter)();return{isSignedIn:i,isLoaded:u,user:d,signOut:async()=>{try{await l(),n.oR.success("Signed out successfully"),g.push("/")}catch(e){n.oR.error("Failed to sign out"),console.error("Sign out error:",e)}},getToken:async()=>{try{return await c()}catch(e){return console.error("Error getting Clerk token:",e),null}},signIn:m,signUp:f,signInLoaded:h,signUpLoaded:p,userId:null==d?void 0:d.id,email:null==d||null==(e=d.emailAddresses[0])?void 0:e.emailAddress,firstName:null==d?void 0:d.firstName,lastName:null==d?void 0:d.lastName,fullName:null==d?void 0:d.fullName,imageUrl:null==d?void 0:d.imageUrl,isEmailVerified:(null==d||null==(r=d.emailAddresses[0])||null==(t=r.verification)?void 0:t.status)==="verified"}}},91950:(e,t,r)=>{"use strict";r.d(t,{hS:()=>s});let s={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-7 w-7",xl:"h-8 w-8",badge:"h-4 w-4",navigation:"h-6 w-6",sidebar:"h-6 w-6",header:"h-6 w-6",button:"h-6 w-6",projectIcon:"h-6 w-6",actionIcon:"h-6 w-6",chatIcon:"h-6 w-6",profileIcon:"h-6 w-6",notificationIcon:"h-6 w-6"}},92236:(e,t,r)=>{"use strict";r.d(t,{g:()=>l});var s=r(95155),a=r(11518),o=r.n(a);r(12115);var n=r(6874),i=r.n(n);let l=e=>{let{size:t=48,textSize:r=24,className:a="",animated:n=!1,showText:l=!1,href:c,animationSpeed:d=5}=e,u=t/48,m=32*u,h=20*u,f=+u,p=1.25*u,g=(0,s.jsxs)("div",{style:{position:"relative",width:t,height:t,display:"inline-block",animation:n?"logoFloat ".concat(3/d,"s ease-in-out infinite"):"none"},children:[(0,s.jsx)("div",{style:{position:"absolute",width:t,height:t,borderRadius:"50%",background:"#b4fd98",border:"".concat(f,"px solid #73ed47"),left:0,top:0,zIndex:1,transform:"rotate(70deg)",animation:n?"logoRotate ".concat(8/d,"s linear infinite"):"none"},children:(0,s.jsx)("div",{style:{position:"absolute",width:m,height:m,borderRadius:"50%",background:"#0A4000",border:"".concat(f,"px solid #73ed47"),left:t-m-f-p,top:f+p,zIndex:2,transform:"rotate(280deg)",animation:n?"logoPulse ".concat(2/d,"s ease-in-out infinite"):"none"},children:(0,s.jsx)("div",{style:{position:"absolute",width:h,height:h,borderRadius:"50%",background:"#fff",border:"".concat(f,"px solid #73ed47"),left:m-h-f-p,top:f+p,zIndex:3,animation:n?"logoGlow ".concat(4/d,"s ease-in-out infinite"):"none"}})})}),n&&(0,s.jsx)(o(),{id:"5428b1dfccf92ce4",children:"@-webkit-keyframes logoRotate{0%{-webkit-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);transform:rotate(430deg)}}@-moz-keyframes logoRotate{0%{-moz-transform:rotate(70deg);transform:rotate(70deg)}100%{-moz-transform:rotate(430deg);transform:rotate(430deg)}}@-o-keyframes logoRotate{0%{-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-o-transform:rotate(430deg);transform:rotate(430deg)}}@keyframes logoRotate{0%{-webkit-transform:rotate(70deg);-moz-transform:rotate(70deg);-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);-moz-transform:rotate(430deg);-o-transform:rotate(430deg);transform:rotate(430deg)}}"})]}),v=(0,s.jsxs)("div",{className:"flex items-center gap-3 ".concat(a),children:[g,(0,s.jsx)("span",{className:"font-bold text-foreground",style:{fontSize:"".concat(r,"px"),position:"relative",animation:n?"textGlow ".concat(3/d,"s ease-in-out infinite"):"none"},children:"siift.ai"})]}),x=l?v:g;return c?(0,s.jsx)(i(),{href:c,className:"hover:opacity-80 transition-opacity",children:x}):x}},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},99912:(e,t,r)=>{"use strict";r.d(t,{V:()=>n});var s=r(95155),a=r(91950),o=r(59434);let n=r(12115).forwardRef((e,t)=>{let{className:r,icon:n,iconClassName:i,text:l,subText:c,textClassName:d,subTextClassName:u,badge:m,badgeClassName:h,trailing:f,variant:p="default",size:g="md",showBorder:v=!1,borderClassName:x,layout:b="horizontal",hoverColor:y="green",hoverScale:w=!0,children:j,...k}=e,N={sm:a.hS.sm,md:a.hS.md,lg:a.hS.lg};return(0,s.jsxs)("button",{ref:t,className:(0,o.cn)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer",{default:"bg-background hover:bg-sidebar-accent",ghost:"bg-transparent hover:bg-sidebar-accent",outline:"bg-background border border-input hover:bg-gray-100",accent:"bg-sidebar-accent hover:bg-sidebar-accent/80",secondary:"bg-gray-900 text-white hover:bg-gray-800"}[p],{sm:"icon-only"===b?"h-6 w-6 p-1":"h-8 px-2 py-1 text-xs",md:"icon-only"===b?"h-8 w-8 p-1.5":"h-10 px-3 py-2 text-sm",lg:"icon-only"===b?"h-10 w-10 p-2":"h-12 px-4 py-3 text-base"}[g],{green:"hover:text-[var(--primary-dark)]",grey:"hover:text-muted-foreground hover:bg-muted/50 hover:border-border",accent:"hover:text-accent-foreground"}[y],{horizontal:"flex-row gap-2",vertical:"flex-col gap-1","icon-only":"flex-row"}[b],v?"border ".concat(x||"border-border/30"):"","rounded-lg","horizontal"===b&&l?"justify-start":"","icon-only"===b?"justify-center":"",r),...k,children:[n&&(0,s.jsx)(n,{className:(0,o.cn)(i||N[g],w?"transition-transform duration-200 hover:scale-110":"")}),(l||c)&&"icon-only"!==b&&(0,s.jsxs)("div",{className:(0,o.cn)("flex flex-col","horizontal"===b?"flex-1 text-left":"text-center","vertical"===b?"items-center":"items-start"),children:[l&&(0,s.jsx)("span",{className:(0,o.cn)("font-medium truncate","sm"===g?"text-xs":"md"===g?"text-sm":"text-base",d),children:l}),c&&(0,s.jsx)("span",{className:(0,o.cn)("text-muted-foreground truncate","text-xs",u),children:c})]}),m&&(0,s.jsx)("span",{className:(0,o.cn)("bg-primary text-primary-foreground rounded-md flex items-center justify-center absolute -top-1 -right-1 z-10","sm"===g||"md"===g?"text-xs h-4 min-w-4":"text-sm h-5 min-w-5",h),children:m}),f&&"icon-only"!==b&&(0,s.jsx)("div",{className:"ml-auto",children:f}),j]})});n.displayName="SidebarButton"}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,8879,4043,5123,8988,8441,8229,1684,7358],()=>t(13512)),_N_E=e.O()}]);