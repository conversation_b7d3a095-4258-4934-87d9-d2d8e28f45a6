(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3270],{27366:(e,t,r)=>{"use strict";r.d(t,{GF:()=>n,Ni:()=>i});var a=r(56671);let n=(e,t)=>{a.oR.success(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||4e3})},i=(e,t)=>{a.oR.error(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||5e3})}},28883:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(95155),n=r(99708),i=r(74466);r(12115);var s=r(59434);let o=(0,i.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:i,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,s.cn)(o({variant:r,size:i,className:t})),...d})}},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},46786:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>f,eh:()=>c,lt:()=>l});let a=new Map,n=e=>{let t=a.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},i=(e,t,r)=>{if(void 0===e)return{type:"untracked",connection:t.connect(r)};let n=a.get(r.name);if(n)return{type:"tracked",store:e,...n};let i={connection:t.connect(r),stores:{}};return a.set(r.name,i),{type:"tracked",store:e,...i}},s=(e,t)=>{if(void 0===t)return;let r=a.get(e);r&&(delete r.stores[t],0===Object.keys(r.stores).length&&a.delete(e))},o=e=>{var t,r;if(!e)return;let a=e.split("\n"),n=a.findIndex(e=>e.includes("api.setState"));if(n<0)return;let i=(null==(t=a[n+1])?void 0:t.trim())||"";return null==(r=/.+ (.+) .+/.exec(i))?void 0:r[1]},l=(e,t={})=>(r,a,l)=>{let c,{enabled:u,anonymousActionType:f,store:g,...m}=t;try{c=(null==u||u)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!c)return e(r,a,l);let{connection:h,...v}=i(g,c,m),p=!0;l.setState=(e,t,i)=>{let s=r(e,t);if(!p)return s;let d=void 0===i?{type:f||o(Error().stack)||"anonymous"}:"string"==typeof i?{type:i}:i;return void 0===g?null==h||h.send(d,a()):null==h||h.send({...d,type:`${g}/${d.type}`},{...n(m.name),[g]:l.getState()}),s},l.devtools={cleanup:()=>{h&&"function"==typeof h.unsubscribe&&h.unsubscribe(),s(m.name,g)}};let b=(...e)=>{let t=p;p=!1,r(...e),p=t},x=e(l.setState,a,l);if("untracked"===v.type?null==h||h.init(x):(v.stores[v.store]=l,null==h||h.init(Object.fromEntries(Object.entries(v.stores).map(([e,t])=>[e,e===v.store?x:t.getState()])))),l.dispatchFromDevtools&&"function"==typeof l.dispatch){let e=!1,t=l.dispatch;l.dispatch=(...r)=>{"__setState"!==r[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...r)}}return h.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return d(e.payload,e=>{if("__setState"===e.type){if(void 0===g)return void b(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[g];return void(null==t||JSON.stringify(l.getState())!==JSON.stringify(t)&&b(t))}l.dispatchFromDevtools&&"function"==typeof l.dispatch&&l.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(b(x),void 0===g)return null==h?void 0:h.init(l.getState());return null==h?void 0:h.init(n(m.name));case"COMMIT":if(void 0===g){null==h||h.init(l.getState());break}return null==h?void 0:h.init(n(m.name));case"ROLLBACK":return d(e.state,e=>{if(void 0===g){b(e),null==h||h.init(l.getState());return}b(e[g]),null==h||h.init(n(m.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return d(e.state,e=>{if(void 0===g)return void b(e);JSON.stringify(l.getState())!==JSON.stringify(e[g])&&b(e[g])});case"IMPORT_STATE":{let{nextLiftedState:r}=e.payload,a=null==(t=r.computedStates.slice(-1)[0])?void 0:t.state;if(!a)return;void 0===g?b(a):b(a[g]),null==h||h.send(null,r);break}case"PAUSE_RECORDING":return p=!p}return}}),x},d=(e,t)=>{let r;try{r=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==r&&t(r)},c=e=>(t,r,a)=>{let n=a.subscribe;return a.subscribe=(e,t,r)=>{let i=e;if(t){let n=(null==r?void 0:r.equalityFn)||Object.is,s=e(a.getState());i=r=>{let a=e(r);if(!n(s,a)){let e=s;t(s=a,e)}},(null==r?void 0:r.fireImmediately)&&t(s,s)}return n(i)},e(t,r,a)},u=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>u(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>u(t)(e)}}},f=(e,t)=>(r,a,n)=>{let i,s={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let a=e=>null===e?null:JSON.parse(e,void 0),n=null!=(t=r.getItem(e))?t:null;return n instanceof Promise?n.then(a):a(n)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},o=!1,l=new Set,d=new Set,c=s.storage;if(!c)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${s.name}', the given storage is currently unavailable.`),r(...e)},a,n);let f=()=>{let e=s.partialize({...a()});return c.setItem(s.name,{state:e,version:s.version})},g=n.setState;n.setState=(e,t)=>{g(e,t),f()};let m=e((...e)=>{r(...e),f()},a,n);n.getInitialState=()=>m;let h=()=>{var e,t;if(!c)return;o=!1,l.forEach(e=>{var t;return e(null!=(t=a())?t:m)});let n=(null==(t=s.onRehydrateStorage)?void 0:t.call(s,null!=(e=a())?e:m))||void 0;return u(c.getItem.bind(c))(s.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===s.version)return[!1,e.state];else{if(s.migrate){let t=s.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[n,o]=e;if(r(i=s.merge(o,null!=(t=a())?t:m),!0),n)return f()}).then(()=>{null==n||n(i,void 0),i=a(),o=!0,d.forEach(e=>e(i))}).catch(e=>{null==n||n(void 0,e)})};return n.persist={setOptions:e=>{s={...s,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(s.name)},getOptions:()=>s,rehydrate:()=>h(),hasHydrated:()=>o,onHydrate:e=>(l.add(e),()=>{l.delete(e)}),onFinishHydration:e=>(d.add(e),()=>{d.delete(e)})},s.skipHydration||h(),i||m}},47614:(e,t,r)=>{"use strict";r.d(t,{V:()=>s});var a=r(95155),n=r(66695),i=r(92236);r(12115);let s=e=>{let{title:t,description:r,children:s,footer:o,header:l}=e;return(0,a.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,a.jsx)("div",{className:"pt-8",children:(0,a.jsx)(i.g,{size:40,animated:!1,href:"/"})}),(0,a.jsxs)(n.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,a.jsxs)(n.aR,{className:"space-y-1",children:[l,(0,a.jsx)(n.ZB,{className:"text-2xl font-bold text-center",children:t}),r&&(0,a.jsx)(n.BT,{className:"text-center",children:r})]}),(0,a.jsx)(n.Wu,{className:"space-y-4",children:s}),o&&(0,a.jsx)(n.wL,{children:o})]})]})}},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>i});var a=r(52596),n=r(39688);function i(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,a.$)(t))}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},65453:(e,t,r)=>{"use strict";r.d(t,{v:()=>l});var a=r(12115);let n=e=>{let t,r=new Set,a=(e,a)=>{let n="function"==typeof e?e(t):e;if(!Object.is(n,t)){let e=t;t=(null!=a?a:"object"!=typeof n||null===n)?n:Object.assign({},t,n),r.forEach(r=>r(t,e))}},n=()=>t,i={setState:a,getState:n,getInitialState:()=>s,subscribe:e=>(r.add(e),()=>r.delete(e))},s=t=e(a,n,i);return i},i=e=>e?n(e):n,s=e=>e,o=e=>{let t=i(e),r=e=>(function(e,t=s){let r=a.useSyncExternalStore(e.subscribe,a.useCallback(()=>t(e.getState()),[e,t]),a.useCallback(()=>t(e.getInitialState()),[e,t]));return a.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},l=e=>e?o(e):o},66681:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(88128),n=r(35695),i=r(12115),s=r(27366);function o(){let{user:e,isAuthenticated:t,error:r,pendingEmailVerification:o,emailVerificationSent:l,actions:d}=(0,a.B)(),c=(0,n.useRouter)(),[u,f]=(0,i.useState)(!1),g=(0,i.useCallback)(async e=>{f(!0);try{await d.login(e);let{user:t}=a.B.getState();(0,s.GF)("Welcome back!",{description:"Logged in as ".concat(null==t?void 0:t.email)}),(null==t?void 0:t.role)==="admin"?c.replace("/admin"):c.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Login failed";throw(0,s.Ni)("Login Failed",{description:e}),t}finally{f(!1)}},[d,c,f]),m=(0,i.useCallback)(async e=>{f(!0);try{await d.signup(e);let{user:t}=a.B.getState();(0,s.GF)("Account created successfully!",{description:"Welcome ".concat((null==t?void 0:t.name)||(null==t?void 0:t.email),"!")}),(null==t?void 0:t.role)==="admin"?c.replace("/admin"):c.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Signup failed";throw(0,s.Ni)("Signup Failed",{description:e}),t}finally{f(!1)}},[d,c,f]),h=(0,i.useCallback)(()=>{d.logout(),c.push("/auth/login")},[d,c]),v=(0,i.useCallback)(()=>{d.setError(null)},[d]),p=(0,i.useCallback)(async(e,t)=>{try{await d.sendEmailVerification(e,t)}catch(e){throw e}},[d]);return{user:e,isAuthenticated:t,isLoading:u,error:r,pendingEmailVerification:o,emailVerificationSent:l,login:g,signup:m,logout:h,clearError:v,sendEmailVerification:p,verifyEmail:(0,i.useCallback)(async(e,t)=>{try{await d.verifyEmail(e,t)}catch(e){throw e}},[d]),resendEmailVerification:(0,i.useCallback)(async e=>{try{await d.resendEmailVerification(e)}catch(e){throw e}},[d]),updateUser:d.updateUser,refreshToken:d.refreshToken}}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>s,wL:()=>c});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex px-6 [.border-t]:pt-6",t),...r})}},75525:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},88527:(e,t,r)=>{Promise.resolve().then(r.bind(r,91392))},91392:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j,dynamic:()=>y});var a=r(95155),n=r(63560),i=r(75525),s=r(28883),o=r(35169),l=r(51154),d=r(6874),c=r.n(d),u=r(35695),f=r(12115),g=r(62177),m=r(70927),h=r(47614),v=r(30285),p=r(62523),b=r(66681),x=r(27366);let y="force-dynamic",w=m.Ik({code:m.Yj().min(6,"Verification code must be 6 digits").max(6,"Verification code must be 6 digits")});function k(){let[e,t]=(0,f.useState)(["","","","","",""]),[r,d]=(0,f.useState)(!1),[m,y]=(0,f.useState)(0),[k,j]=(0,f.useState)(!1),N=(0,u.useRouter)(),S=(0,u.useSearchParams)().get("email")||"",{verifyEmail:E,resendEmailVerification:_,isLoading:O,clearError:C,emailVerificationSent:z}=(0,b.A)(),{register:A,handleSubmit:I,formState:{errors:R},setValue:T,reset:V}=(0,g.mN)({resolver:(0,n.u)(w),defaultValues:{code:""}});(0,f.useEffect)(()=>{V(),t(["","","","","",""])},[V]),(0,f.useEffect)(()=>{if(m>0){let e=setTimeout(()=>{y(m-1)},1e3);return()=>clearTimeout(e)}},[m]);let F=async e=>{C(),j(!0);try{await E(S,e.code),d(!0),(0,x.GF)("Email verified successfully!",{description:"Redirecting to dashboard..."}),setTimeout(()=>{N.push("/dashboard")},2e3)}catch(t){let e=t instanceof Error?t.message:"Verification failed";(0,x.Ni)("Verification failed",{description:e}),console.error("Email verification failed:",t)}finally{j(!1)}},D=async()=>{if(!(m>0)&&S){C(),y(60);try{await _(S),(0,x.GF)("Verification code sent!",{description:"Check your email for the new code"})}catch(t){let e=t instanceof Error?t.message:"Failed to resend code";(0,x.Ni)("Failed to resend code",{description:e}),console.error("Failed to resend verification code:",t),y(0)}}};return r?(0,a.jsx)(h.V,{title:"Email verified successfully!",header:(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(i.A,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Your email has been verified. You now have full access to your account."})}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Redirecting to dashboard..."}),(0,a.jsx)(c(),{href:"/dashboard",className:"font-medium text-primary hover:underline",children:"Continue to dashboard"})]})}):(0,a.jsx)(h.V,{title:"Verify your email",description:"Enter the verification code sent to ".concat(S),header:(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(s.A,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),footer:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["Didn't receive the code?"," ",(0,a.jsx)(v.$,{variant:"link",className:"px-0 font-normal",onClick:D,disabled:O||m>0||!S,children:m>0?"Resend in ".concat(m,"s"):"Resend code"})]}),(0,a.jsx)("div",{className:"flex w-full",children:(0,a.jsxs)(c(),{href:"/auth/login",className:"flex items-center text-sm font-medium text-primary hover:underline w-full pl-0",style:{marginLeft:0},children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]}),children:(0,a.jsxs)("form",{onSubmit:I(F),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Enter the 6-digit code sent to your email"}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:Array.from({length:6}).map((r,n)=>(0,a.jsx)(p.p,{type:"text",inputMode:"numeric",maxLength:1,value:e[n],autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:!1,className:"w-12 h-12 text-center text-lg font-semibold ".concat(R.code?"border-destructive":""),onChange:r=>{let a=r.target.value.replace(/\D/g,""),i=[...e];if(i[n]=a,t(i),T("code",i.join("")),n<5&&a){var s;let e=null==(s=r.target.parentElement)?void 0:s.children[n+1];null==e||e.focus()}},onKeyDown:r=>{if("Backspace"===r.key){if(!r.currentTarget.value&&n>0){var a;let e=null==(a=r.target.parentElement)?void 0:a.children[n-1];null==e||e.focus()}else if(r.currentTarget.value){let r=[...e];r[n]="",t(r),T("code",r.join(""))}}},onPaste:e=>{e.preventDefault();let r=e.clipboardData.getData("text").replace(/\D/g,"");if(r.length<=6){var a;t(r.split("").concat(Array(6).fill("")).slice(0,6)),T("code",r);let n=Math.min(r.length,5),i=null==(a=e.target.parentElement)?void 0:a.children[n];null==i||i.focus()}}},n))}),(0,a.jsx)("input",{type:"hidden",...A("code")}),R.code&&(0,a.jsx)("p",{className:"text-sm text-destructive text-center",children:R.code.message})]}),(0,a.jsx)(v.$,{type:"submit",className:"w-full",disabled:k,children:k?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify Email"})]})})}function j(){return(0,a.jsx)(f.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(k,{})})}},92236:(e,t,r)=>{"use strict";r.d(t,{g:()=>l});var a=r(95155),n=r(11518),i=r.n(n);r(12115);var s=r(6874),o=r.n(s);let l=e=>{let{size:t=48,textSize:r=24,className:n="",animated:s=!1,showText:l=!1,href:d,animationSpeed:c=5}=e,u=t/48,f=32*u,g=20*u,m=+u,h=1.25*u,v=(0,a.jsxs)("div",{style:{position:"relative",width:t,height:t,display:"inline-block",animation:s?"logoFloat ".concat(3/c,"s ease-in-out infinite"):"none"},children:[(0,a.jsx)("div",{style:{position:"absolute",width:t,height:t,borderRadius:"50%",background:"#b4fd98",border:"".concat(m,"px solid #73ed47"),left:0,top:0,zIndex:1,transform:"rotate(70deg)",animation:s?"logoRotate ".concat(8/c,"s linear infinite"):"none"},children:(0,a.jsx)("div",{style:{position:"absolute",width:f,height:f,borderRadius:"50%",background:"#0A4000",border:"".concat(m,"px solid #73ed47"),left:t-f-m-h,top:m+h,zIndex:2,transform:"rotate(280deg)",animation:s?"logoPulse ".concat(2/c,"s ease-in-out infinite"):"none"},children:(0,a.jsx)("div",{style:{position:"absolute",width:g,height:g,borderRadius:"50%",background:"#fff",border:"".concat(m,"px solid #73ed47"),left:f-g-m-h,top:m+h,zIndex:3,animation:s?"logoGlow ".concat(4/c,"s ease-in-out infinite"):"none"}})})}),s&&(0,a.jsx)(i(),{id:"5428b1dfccf92ce4",children:"@-webkit-keyframes logoRotate{0%{-webkit-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);transform:rotate(430deg)}}@-moz-keyframes logoRotate{0%{-moz-transform:rotate(70deg);transform:rotate(70deg)}100%{-moz-transform:rotate(430deg);transform:rotate(430deg)}}@-o-keyframes logoRotate{0%{-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-o-transform:rotate(430deg);transform:rotate(430deg)}}@keyframes logoRotate{0%{-webkit-transform:rotate(70deg);-moz-transform:rotate(70deg);-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);-moz-transform:rotate(430deg);-o-transform:rotate(430deg);transform:rotate(430deg)}}"})]}),p=(0,a.jsxs)("div",{className:"flex items-center gap-3 ".concat(n),children:[v,(0,a.jsx)("span",{className:"font-bold text-foreground",style:{fontSize:"".concat(r,"px"),position:"relative",animation:s?"textGlow ".concat(3/c,"s ease-in-out infinite"):"none"},children:"siift.ai"})]}),b=l?p:v;return d?(0,a.jsx)(o(),{href:d,className:"hover:opacity-80 transition-opacity",children:b}):b}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,4043,8988,1917,8128,8441,8229,1684,7358],()=>t(88527)),_N_E=e.O()}]);