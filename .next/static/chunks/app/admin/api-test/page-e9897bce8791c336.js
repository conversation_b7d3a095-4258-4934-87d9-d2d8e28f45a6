(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3200],{3401:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var a=t(12115),r=t(46641),n=t(82396),l=["axis","item"],i=(0,a.forwardRef)((e,s)=>a.createElement(n.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:r.uN,categoricalChartProps:e,ref:s}))},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(95155);t(12115);var r=t(99708),n=t(74466),l=t(59434);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:n=!1,...c}=e,d=n?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(i({variant:t}),s),...c})}},33109:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},33651:(e,s,t)=>{Promise.resolve().then(t.bind(t,36753))},36753:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ey,dynamic:()=>ej});var a=t(95155),r=t(97724),n=t(26126),l=t(30285),i=t(66695),c=t(12115),d=t(46081),o=t(63655),u="Progress",[m,x]=(0,d.A)(u),[h,g]=m(u),p=c.forwardRef((e,s)=>{var t,r,n,l;let{__scopeProgress:i,value:c=null,max:d,getValueLabel:u=j,...m}=e;(d||0===d)&&!N(d)&&console.error((t="".concat(d),r="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(r,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let x=N(d)?d:100;null===c||A(c,x)||console.error((n="".concat(c),l="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let g=A(c,x)?c:null,p=b(g)?u(g,x):void 0;return(0,a.jsx)(h,{scope:i,value:g,max:x,children:(0,a.jsx)(o.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":b(g)?g:void 0,"aria-valuetext":p,role:"progressbar","data-state":y(g,x),"data-value":null!=g?g:void 0,"data-max":x,...m,ref:s})})});p.displayName=u;var v="ProgressIndicator",f=c.forwardRef((e,s)=>{var t;let{__scopeProgress:r,...n}=e,l=g(v,r);return(0,a.jsx)(o.sG.div,{"data-state":y(l.value,l.max),"data-value":null!=(t=l.value)?t:void 0,"data-max":l.max,...n,ref:s})});function j(e,s){return"".concat(Math.round(e/s*100),"%")}function y(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function b(e){return"number"==typeof e}function N(e){return b(e)&&!isNaN(e)&&e>0}function A(e,s){return b(e)&&!isNaN(e)&&e<=s&&e>=0}f.displayName=v;var w=t(59434);function k(e){let{className:s,value:t,showValue:r=!1,...n}=e;return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(p,{"data-slot":"progress",className:(0,w.cn)("relative h-2 w-full overflow-hidden rounded-full","bg-[var(--progress-bg)] border border-[var(--progress-border)]",s),...n,children:(0,a.jsx)(f,{"data-slot":"progress-indicator",className:"h-full w-full flex-1 transition-all bg-[var(--progress-fill)]",style:{transform:"translateX(-".concat(100-(t||0),"%)")}})}),r&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-medium text-[var(--progress-text)] drop-shadow-sm",children:[Math.round(t||0),"%"]})})]})}var T=t(85185),C=t(89196),R=t(28905),S=t(94315),D=t(5845),E=t(61285),I="Tabs",[Z,F]=(0,d.A)(I,[C.RG]),M=(0,C.RG)(),[P,U]=Z(I),_=c.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,onValueChange:n,defaultValue:l,orientation:i="horizontal",dir:c,activationMode:d="automatic",...u}=e,m=(0,S.jH)(c),[x,h]=(0,D.i)({prop:r,onChange:n,defaultProp:null!=l?l:"",caller:I});return(0,a.jsx)(P,{scope:t,baseId:(0,E.B)(),value:x,onValueChange:h,orientation:i,dir:m,activationMode:d,children:(0,a.jsx)(o.sG.div,{dir:m,"data-orientation":i,...u,ref:s})})});_.displayName=I;var W="TabsList",z=c.forwardRef((e,s)=>{let{__scopeTabs:t,loop:r=!0,...n}=e,l=U(W,t),i=M(t);return(0,a.jsx)(C.bL,{asChild:!0,...i,orientation:l.orientation,dir:l.dir,loop:r,children:(0,a.jsx)(o.sG.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:s})})});z.displayName=W;var B="TabsTrigger",V=c.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,disabled:n=!1,...l}=e,i=U(B,t),c=M(t),d=L(i.baseId,r),u=O(i.baseId,r),m=r===i.value;return(0,a.jsx)(C.q7,{asChild:!0,...c,focusable:!n,active:m,children:(0,a.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:d,...l,ref:s,onMouseDown:(0,T.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():i.onValueChange(r)}),onKeyDown:(0,T.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&i.onValueChange(r)}),onFocus:(0,T.m)(e.onFocus,()=>{let e="manual"!==i.activationMode;m||n||!e||i.onValueChange(r)})})})});V.displayName=B;var G="TabsContent",H=c.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:n,children:l,...i}=e,d=U(G,t),u=L(d.baseId,r),m=O(d.baseId,r),x=r===d.value,h=c.useRef(x);return c.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(R.C,{present:n||x,children:t=>{let{present:r}=t;return(0,a.jsx)(o.sG.div,{"data-state":x?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:m,tabIndex:0,...i,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&l})}})});function L(e,s){return"".concat(e,"-trigger-").concat(s)}function O(e,s){return"".concat(e,"-content-").concat(s)}function q(e){let{className:s,...t}=e;return(0,a.jsx)(_,{"data-slot":"tabs",className:(0,w.cn)("flex flex-col gap-2",s),...t})}function K(e){let{className:s,...t}=e;return(0,a.jsx)(z,{"data-slot":"tabs-list",className:(0,w.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",s),...t})}function X(e){let{className:s,...t}=e;return(0,a.jsx)(V,{"data-slot":"tabs-trigger",className:(0,w.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...t})}function J(e){let{className:s,...t}=e;return(0,a.jsx)(H,{"data-slot":"tabs-content",className:(0,w.cn)("flex-1 outline-none",s),...t})}H.displayName=G;var Y=t(31877),$=t(40646),Q=t(54861),ee=t(51154),es=t(72713),et=t(49376),ea=t(25487),er=t(17580),en=t(79397),el=t(33109),ei=t(71539),ec=t(55868),ed=t(14186);let eo=(0,t(19946).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var eu=t(54213),em=t(83540),ex=t(3401),eh=t(94754),eg=t(96025),ep=t(52071),ev=t(24021),ef=t(25244);let ej="force-dynamic";function ey(){let[e,s]=(0,c.useState)(!1),[t,d]=(0,c.useState)([]),[o,u]=(0,c.useState)(!1),[m,x]=(0,c.useState)(0);(0,c.useEffect)(()=>{u(!!(localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token")))},[]);let h=[{name:"Analytics Summary",endpoint:"/admin/analytics/summary",category:"analytics",method:()=>Y.i.getAnalyticsSummary()},{name:"User Analytics",endpoint:"/admin/analytics/users",category:"analytics",method:()=>Y.i.getUserAnalytics({limit:5})},{name:"Activity Metrics",endpoint:"/admin/analytics/activity-metrics",category:"analytics",method:()=>Y.i.getActivityMetrics()},{name:"Activity Trends",endpoint:"/admin/analytics/activity-trends",category:"analytics",method:()=>Y.i.getActivityTrends()},{name:"Agent Calls",endpoint:"/admin/agent-analytics/calls",category:"agent",method:()=>Y.i.getAgentCalls({limit:5})},{name:"Agent Usage Stats",endpoint:"/admin/agent-analytics/usage-stats",category:"agent",method:()=>Y.i.getAgentUsageStats()},{name:"Token Trends",endpoint:"/admin/agent-analytics/token-trends",category:"agent",method:()=>Y.i.getTokenTrends()},{name:"Health Check",endpoint:"/admin/health",category:"system",method:()=>Y.i.getHealthCheck()}],g=async()=>{s(!0),d([]),x(0);let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e){d([{endpoint:"Authentication",name:"Authentication",category:"system",status:"error",error:"No admin token found. Please login first."}]),s(!1);return}console.log("\uD83D\uDD11 Initializing admin API with token:",e?"".concat(e.substring(0,20),"..."):"null"),(0,Y.R)(e),console.log("\uD83D\uDD0D Admin API token after initialization:",Y.i.getToken()?"✅ Set":"❌ Not set");let t=[];for(let e=0;e<h.length;e++){let s=h[e];x(e);let a=Date.now();try{console.log("Testing ".concat(s.name,"..."));let e=await s.method(),r=Date.now()-a;t.push({endpoint:s.endpoint,name:s.name,category:s.category,status:"success",data:e,duration:r}),console.log("✅ ".concat(s.name," succeeded:"),e)}catch(r){let e=Date.now()-a;t.push({endpoint:s.endpoint,name:s.name,category:s.category,status:"error",error:r.message,duration:e}),console.log("❌ ".concat(s.name," failed:"),r.message)}d([...t])}s(!1),x(0)},p=e=>{switch(e){case"success":return(0,a.jsx)($.A,{className:"h-5 w-5 text-green-600"});case"error":return(0,a.jsx)(Q.A,{className:"h-5 w-5 text-red-600"});case"pending":return(0,a.jsx)(ee.A,{className:"h-5 w-5 animate-spin text-blue-600"})}},v=e=>{switch(e){case"success":return(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"Success"});case"error":return(0,a.jsx)(n.E,{className:"bg-red-100 text-red-800 hover:bg-red-100",children:"Error"});case"pending":return(0,a.jsx)(n.E,{className:"bg-blue-100 text-blue-800 hover:bg-blue-100",children:"Pending"})}},f=e=>{switch(e){case"analytics":return(0,a.jsx)(es.A,{className:"h-5 w-5"});case"agent":return(0,a.jsx)(et.A,{className:"h-5 w-5"});case"system":return(0,a.jsx)(ea.A,{className:"h-5 w-5"})}},j=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),y=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),b=e=>e<1e3?"".concat(e,"ms"):"".concat((e/1e3).toFixed(1),"s"),N=e=>{if(!e)return null;let s=[{label:"Total Users",value:e.totalUsers,icon:er.A,color:"text-blue-600"},{label:"Active Today",value:e.activeToday,icon:en.A,color:"text-green-600"},{label:"New This Week",value:e.newUsersThisWeek,icon:el.A,color:"text-purple-600"},{label:"Feedback Rate",value:"".concat(e.feedbackRate,"%"),icon:es.A,color:"text-orange-600"}];return(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:s.map((e,s)=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 ".concat(e.color)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]})]})})},s))})},A=e=>(null==e?void 0:e.data)?(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid gap-4",children:e.data.slice(0,5).map((e,s)=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Role: ",e.role]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:[e.actionCount," actions"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[b(1e3*e.averageSessionTime)," avg session"]}),(0,a.jsx)(n.E,{variant:e.hasSubmittedFeedback?"default":"secondary",children:e.hasSubmittedFeedback?"Has Feedback":"No Feedback"})]})]})})},s))})}):null,w=e=>{if(!e)return null;let s=[{label:"Daily Active Users",value:j(e.dau),change:e.dauChange},{label:"Weekly Active Users",value:j(e.wau),change:e.wauChange},{label:"Monthly Active Users",value:j(e.mau),change:e.mauChange},{label:"Yearly Active Users",value:j(e.yau),change:e.yauChange}];return(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:s.map((e,s)=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[e.change>0?(0,a.jsx)(el.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(el.A,{className:"h-4 w-4 text-red-600 rotate-180"}),(0,a.jsxs)("span",{className:"text-sm ".concat(e.change>0?"text-green-600":"text-red-600"),children:[e.change>0?"+":"",e.change.toFixed(1),"%"]})]})]})})},s))})},T=e=>{if(!e)return null;let s=[{label:"Total Calls",value:j(e.totalCalls),icon:ei.A},{label:"Success Rate",value:"".concat(e.successRate.toFixed(1),"%"),icon:$.A},{label:"Total Cost",value:y(e.totalCost),icon:ec.A},{label:"Avg Duration",value:b(e.averageDuration),icon:ed.A}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:s.map((e,s)=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]})]})})},s))}),e.callsByAgentType&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Calls by Agent Type"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(em.u,{width:"100%",height:"100%",children:(0,a.jsxs)(ex.E,{data:Object.entries(e.callsByAgentType).map(e=>{let[s,t]=e;return{type:s,count:t}}),children:[(0,a.jsx)(eh.d,{strokeDasharray:"3 3"}),(0,a.jsx)(eg.W,{dataKey:"type"}),(0,a.jsx)(ep.h,{}),(0,a.jsx)(ev.m,{}),(0,a.jsx)(ef.y,{dataKey:"count",fill:"#166534"})]})})})})]})]})},C=e=>(null==e?void 0:e.data)?(0,a.jsx)("div",{className:"space-y-4",children:e.data.slice(0,5).map((e,s)=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.E,{variant:"outline",children:e.agentType}),(0,a.jsx)(n.E,{variant:"secondary",children:e.callType}),(0,a.jsx)(n.E,{variant:"success"===e.status?"default":"destructive",children:e.status})]}),(0,a.jsxs)("p",{className:"text-sm font-medium",children:[e.modelProvider," - ",e.modelName]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[j(e.totalTokens)," tokens •"," ",y(e.cost)]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:b(e.duration)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.createdAt).toLocaleDateString()})]})]})})},s))}):null,R=e=>{if(!e.data)return null;switch(e.name){case"Analytics Summary":return N(e.data);case"User Analytics":return A(e.data);case"Activity Metrics":return w(e.data);case"Agent Usage Stats":return T(e.data);case"Agent Calls":return C(e.data);default:return(0,a.jsxs)("div",{className:"bg-muted/50 border rounded p-3 mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Raw Response:"}),(0,a.jsx)("pre",{className:"text-xs bg-background p-2 rounded border overflow-auto max-h-32",children:JSON.stringify(e.data,null,2)})]})}},S=e=>t.filter(s=>s.category===e),D=()=>{let e=t.length,s=t.filter(e=>"success"===e.status).length;return{total:e,successful:s,failed:t.filter(e=>"error"===e.status).length,avgDuration:t.length>0?t.reduce((e,s)=>e+(s.duration||0),0)/t.length:0}};return(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Admin Analytics Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Test and visualize admin analytics API endpoints with enhanced data presentation."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(k,{value:m/h.length*100,className:"w-32",showValue:!0}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[m+1,"/",h.length]})]}),(0,a.jsxs)(l.$,{onClick:g,disabled:e,children:[e?(0,a.jsx)(ee.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(eo,{className:"h-4 w-4 mr-2"}),"Run Tests"]})]})]}),t.length>0&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Test Overview"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eu.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Tests"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:D().total})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)($.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Successful"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:D().successful})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(Q.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Failed"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:D().failed})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ed.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Avg Duration"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:b(D().avgDuration)})]})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Configuration"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Admin API URL"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"http://localhost:3000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Auth Token"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:o?"✅ Present":"❌ Missing"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Backend Target"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"http://localhost:3000"})]})]})})]}),t.length>0&&(0,a.jsxs)(q,{defaultValue:"overview",className:"w-full",children:[(0,a.jsxs)(K,{className:"grid w-full grid-cols-4",children:[(0,a.jsx)(X,{value:"overview",children:"Overview"}),(0,a.jsx)(X,{value:"analytics",children:"Analytics"}),(0,a.jsx)(X,{value:"agent",children:"Agent Data"}),(0,a.jsx)(X,{value:"system",children:"System"})]}),(0,a.jsx)(J,{value:"overview",className:"space-y-4",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"All Test Results"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:t.map((e,s)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[f(e.category),p(e.status),(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(n.E,{variant:"outline",children:e.category})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.duration&&(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:b(e.duration)}),v(e.status)]})]}),e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mt-2",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&(0,a.jsx)("div",{className:"mt-4",children:R(e)})]},s))})})]})}),(0,a.jsx)(J,{value:"analytics",className:"space-y-4",children:S("analytics").map((e,s)=>(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[p(e.status),e.name,v(e.status)]})}),(0,a.jsxs)(i.Wu,{children:[e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&R(e)]})]},s))}),(0,a.jsx)(J,{value:"agent",className:"space-y-4",children:S("agent").map((e,s)=>(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[p(e.status),e.name,v(e.status)]})}),(0,a.jsxs)(i.Wu,{children:[e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&R(e)]})]},s))}),(0,a.jsx)(J,{value:"system",className:"space-y-4",children:S("system").map((e,s)=>(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[p(e.status),e.name,v(e.status)]})}),(0,a.jsxs)(i.Wu,{children:[e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&R(e)]})]},s))})]})]})})}},40646:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},54213:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},54861:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},55868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},71539:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},79397:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,8707,5854,1917,8128,5748,5973,7724,8441,8229,1684,7358],()=>s(33651)),_N_E=e.O()}]);