(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8808],{853:(e,s,a)=>{Promise.resolve().then(a.bind(a,67594))},67594:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>l,dynamic:()=>i});var n=a(95155),r=a(97724),c=a(96728);let i="force-dynamic";function l(){return(0,n.jsx)(r.U,{children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"Profile"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"Manage your admin profile and account settings."})]}),(0,n.jsx)(c.t,{activeTab:"profile"})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,1917,8128,5748,5973,7724,3660,8441,8229,1684,7358],()=>s(853)),_N_E=e.O()}]);