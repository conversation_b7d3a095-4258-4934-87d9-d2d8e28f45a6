(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7961],{3401:(e,s,t)=>{"use strict";t.d(s,{E:()=>n});var a=t(12115),r=t(46641),l=t(82396),i=["axis","item"],n=(0,a.forwardRef)((e,s)=>a.createElement(l.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:i,tooltipPayloadSearcher:r.uN,categoricalChartProps:e,ref:s}))},25623:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>S,dynamic:()=>D});var a=t(95155),r=t(97724),l=t(26126),i=t(30285),n=t(66695),d=t(31877),c=t(72713),o=t(54213),x=t(55868),h=t(33109),m=t(51154),u=t(53904),j=t(85339),g=t(12115),p=t(83540),v=t(99445),y=t(94754),f=t(96025),k=t(52071),b=t(24021),N=t(61667),T=t(93504),w=t(13279),C=t(3401),A=t(25244);let D="force-dynamic";function S(){var e,s;let[t,D]=(0,g.useState)(null),[S,W]=(0,g.useState)(!1),[E,Z]=(0,g.useState)(null),[R,K]=(0,g.useState)(null),_=async()=>{W(!0),Z(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,d.R)(e);let s=await d.i.getTokenTrends();D(s),K(new Date)}catch(e){Z(e instanceof Error?e.message:"Unknown error")}finally{W(!1)}};(0,g.useEffect)(()=>{_()},[]);let M=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),P=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),I=(null==t?void 0:t.summary)?[{label:"Total Days",value:t.summary.totalDays,icon:c.A,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Total Tokens",value:M(t.summary.totalTokens),icon:o.A,color:"text-green-600",bgColor:"bg-green-50"},{label:"Total Cost",value:P(t.summary.totalCost),icon:x.A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Growth Rate",value:"".concat(t.summary.growthRate.toFixed(1),"%"),icon:h.A,color:"text-indigo-600",bgColor:"bg-indigo-50"}]:[],O=(null==t?void 0:t.summary)?[{label:"Avg Tokens/Day",value:M(t.summary.averageTokensPerDay)},{label:"Avg Cost/Day",value:P(t.summary.averageCostPerDay)}]:[],B=(null==t||null==(e=t.data)?void 0:e.map(e=>({...e,date:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"})})))||[];return(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Token Trends"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track token usage patterns and cost trends over time."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[R&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",R.toLocaleTimeString()]}),(0,a.jsxs)(i.$,{onClick:_,disabled:S,variant:"outline",children:[S?(0,a.jsx)(m.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),E&&(0,a.jsx)(n.Zp,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:E})]})})}),S&&!t&&(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(m.A,{className:"h-8 w-8 animate-spin"})}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:I.map((e,s)=>(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-full ".concat(e.bgColor),children:(0,a.jsx)(e.icon,{className:"h-6 w-6 ".concat(e.color)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.value})]})]})})},s))}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Token Usage Over Time"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(p.u,{width:"100%",height:"100%",children:(0,a.jsxs)(v.Q,{data:B,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"date"}),(0,a.jsx)(k.h,{}),(0,a.jsx)(b.m,{formatter:(e,s)=>"totalTokens"===s?[M(e),"Total Tokens"]:"inputTokens"===s?[M(e),"Input Tokens"]:"outputTokens"===s?[M(e),"Output Tokens"]:[M(e),s]}),(0,a.jsx)(N.Gk,{type:"monotone",dataKey:"totalTokens",stackId:"1",stroke:"#166534",fill:"#166534",fillOpacity:.6}),(0,a.jsx)(N.Gk,{type:"monotone",dataKey:"inputTokens",stackId:"2",stroke:"#7c3aed",fill:"#7c3aed",fillOpacity:.6}),(0,a.jsx)(N.Gk,{type:"monotone",dataKey:"outputTokens",stackId:"3",stroke:"#ea580c",fill:"#ea580c",fillOpacity:.6})]})})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Cost Trends"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(p.u,{width:"100%",height:"100%",children:(0,a.jsxs)(T.b,{data:B,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"date"}),(0,a.jsx)(k.h,{}),(0,a.jsx)(b.m,{formatter:e=>[P(e),"Total Cost"]}),(0,a.jsx)(w.N,{type:"monotone",dataKey:"totalCost",stroke:"#7c3aed",strokeWidth:3,dot:{fill:"#7c3aed",strokeWidth:2,r:4}})]})})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Call Volume"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(p.u,{width:"100%",height:"100%",children:(0,a.jsxs)(C.E,{data:B,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"date"}),(0,a.jsx)(k.h,{}),(0,a.jsx)(b.m,{formatter:e=>[M(e),"Calls"]}),(0,a.jsx)(A.y,{dataKey:"callCount",fill:"#166534"})]})})})})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Efficiency Metrics"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(p.u,{width:"100%",height:"100%",children:(0,a.jsxs)(T.b,{data:B,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"date"}),(0,a.jsx)(k.h,{}),(0,a.jsx)(b.m,{formatter:e=>[M(e),"Avg Tokens/Call"]}),(0,a.jsx)(w.N,{type:"monotone",dataKey:"averageTokensPerCall",stroke:"#ea580c",strokeWidth:2,dot:{fill:"#ea580c",strokeWidth:2,r:4}})]})})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Summary Statistics"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[I.map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]},s)),O.map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]},"avg-".concat(s)))]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Recent Data"})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Total Tokens"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Input Tokens"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Output Tokens"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Total Cost"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Calls"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Avg Tokens/Call"})]})}),(0,a.jsx)("tbody",{children:null==(s=t.data)?void 0:s.slice(0,10).map((e,s)=>(0,a.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,a.jsx)("td",{className:"p-2",children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)("td",{className:"text-right p-2",children:M(e.totalTokens)}),(0,a.jsx)("td",{className:"text-right p-2",children:M(e.inputTokens)}),(0,a.jsx)("td",{className:"text-right p-2",children:M(e.outputTokens)}),(0,a.jsx)("td",{className:"text-right p-2",children:P(e.totalCost)}),(0,a.jsx)("td",{className:"text-right p-2",children:M(e.callCount)}),(0,a.jsx)("td",{className:"text-right p-2",children:M(e.averageTokensPerCall)})]},s))})]})}),t.data&&t.data.length>10&&(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)(l.E,{variant:"outline",children:["Showing first 10 of ",t.data.length," records"]})})]})]})]})]})})}},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(95155);t(12115);var r=t(99708),l=t(74466),i=t(59434);let n=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:l=!1,...d}=e,c=l?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,i.cn)(n({variant:t}),s),...d})}},39106:(e,s,t)=>{Promise.resolve().then(t.bind(t,25623))},53904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54213:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},55868:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,8707,5854,3622,43,1917,8128,5748,5973,7724,8441,8229,1684,7358],()=>s(39106)),_N_E=e.O()}]);