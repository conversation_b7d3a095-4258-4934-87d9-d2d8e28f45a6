(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5598],{18317:(e,s,l)=>{Promise.resolve().then(l.bind(l,39232))},39232:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>T,dynamic:()=>S});var a=l(95155),t=l(97724),c=l(30285),r=l(66695),i=l(31877),d=l(71539),n=l(40646),o=l(55868),x=l(14186),m=l(51154),h=l(53904),u=l(85339),j=l(54213),g=l(12115),p=l(83540),f=l(3401),v=l(94754),N=l(96025),b=l(52071),y=l(24021),C=l(25244),A=l(90170),w=l(18357),k=l(54811);let S="force-dynamic";function T(){var e;let[s,l]=(0,g.useState)(null),[S,T]=(0,g.useState)(!1),[R,Z]=(0,g.useState)(null),[B,F]=(0,g.useState)(null),P=async()=>{T(!0),Z(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,i.R)(e);let s=await i.i.getAgentUsageStats();l(s),F(new Date)}catch(e){Z(e.message)}finally{T(!1)}};(0,g.useEffect)(()=>{P()},[]);let _=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),E=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),U=s?[{label:"Total Calls",value:_(s.totalCalls),icon:d.A,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Success Rate",value:"".concat(s.successRate.toFixed(1),"%"),icon:n.A,color:"text-green-600",bgColor:"bg-green-50"},{label:"Total Cost",value:E(s.totalCost),icon:o.A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Avg Duration",value:(e=s.averageDuration)<1e3?"".concat(e,"ms"):"".concat((e/1e3).toFixed(1),"s"),icon:x.A,color:"text-indigo-600",bgColor:"bg-indigo-50"}]:[],W=s?[{label:"Successful Calls",value:_(s.successfulCalls)},{label:"Failed Calls",value:_(s.failedCalls)},{label:"Total Tokens",value:_(s.totalTokens)},{label:"Avg Tokens/Call",value:_(s.averageTokensPerCall)},{label:"Avg Cost/Call",value:E(s.averageCostPerCall)},{label:"Most Used Agent",value:s.mostUsedAgentType},{label:"Most Used Model",value:s.mostUsedModel}]:[],D=(null==s?void 0:s.callsByAgentType)?Object.entries(s.callsByAgentType).map(e=>{let[s,l]=e;return{type:s,count:l}}):[],M=(null==s?void 0:s.callsByProvider)?Object.entries(s.callsByProvider).map(e=>{let[s,l]=e;return{provider:s,count:l}}):[],K=(null==s?void 0:s.costByModel)?Object.entries(s.costByModel).map(e=>{let[s,l]=e;return{model:s,cost:l}}):[],O=["#166534","#7c3aed","#ea580c","#dc2626","#0891b2","#059669"];return(0,a.jsx)(t.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Agent Usage Statistics"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Comprehensive overview of agent performance and usage metrics."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[B&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",B.toLocaleTimeString()]}),(0,a.jsxs)(c.$,{onClick:P,disabled:S,variant:"outline",children:[S?(0,a.jsx)(m.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),R&&(0,a.jsx)(r.Zp,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(r.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:R})]})})}),S&&!s&&(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(m.A,{className:"h-8 w-8 animate-spin"})}),s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:U.map((e,s)=>(0,a.jsx)(r.Zp,{children:(0,a.jsx)(r.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-full ".concat(e.bgColor),children:(0,a.jsx)(e.icon,{className:"h-6 w-6 ".concat(e.color)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.value})]})]})})},s))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Calls by Agent Type"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(p.u,{width:"100%",height:"100%",children:(0,a.jsxs)(f.E,{data:D,children:[(0,a.jsx)(v.d,{strokeDasharray:"3 3"}),(0,a.jsx)(N.W,{dataKey:"type"}),(0,a.jsx)(b.h,{}),(0,a.jsx)(y.m,{formatter:e=>[_(e),"Calls"]}),(0,a.jsx)(C.y,{dataKey:"count",fill:"#166534"})]})})})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Calls by Provider"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(p.u,{width:"100%",height:"100%",children:(0,a.jsxs)(A.r,{children:[(0,a.jsx)(w.F,{data:M,cx:"50%",cy:"50%",labelLine:!1,label:e=>"".concat(e.provider," ").concat((100*(e.percent||0)).toFixed(0),"%"),outerRadius:80,fill:"#8884d8",dataKey:"count",children:M.map((e,s)=>(0,a.jsx)(k.f,{fill:O[s%O.length]},"cell-".concat(s)))}),(0,a.jsx)(y.m,{formatter:e=>[_(e),"Calls"]})]})})})})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Cost by Model"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(p.u,{width:"100%",height:"100%",children:(0,a.jsxs)(f.E,{data:K,children:[(0,a.jsx)(v.d,{strokeDasharray:"3 3"}),(0,a.jsx)(N.W,{dataKey:"model"}),(0,a.jsx)(b.h,{}),(0,a.jsx)(y.m,{formatter:e=>[E(e),"Cost"]}),(0,a.jsx)(C.y,{dataKey:"cost",fill:"#7c3aed"})]})})})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Detailed Statistics"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:W.map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]},s))})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Performance Summary"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Success Rate"})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:"".concat(s.successRate,"%")}})}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[s.successfulCalls," successful out of ",s.totalCalls," ","total calls"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Token Efficiency"})]}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:_(s.averageTokensPerCall)}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average tokens per call"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Cost Efficiency"})]}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:E(s.averageCostPerCall)}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average cost per call"})]})]})})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,8707,5854,7547,1917,8128,5748,5973,7724,8441,8229,1684,7358],()=>s(18317)),_N_E=e.O()}]);