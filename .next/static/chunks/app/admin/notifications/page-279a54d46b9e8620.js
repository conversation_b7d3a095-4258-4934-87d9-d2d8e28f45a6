(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[993],{37086:(e,s,a)=>{Promise.resolve().then(a.bind(a,69119))},69119:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>d,dynamic:()=>c});var i=a(95155),n=a(97724),t=a(96728);let c="force-dynamic";function d(){return(0,i.jsx)(n.U,{children:(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold",children:"Notifications"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"View and manage system notifications and alerts."})]}),(0,i.jsx)(t.t,{activeTab:"notifications"})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,1917,8128,5748,5973,7724,3660,8441,8229,1684,7358],()=>s(37086)),_N_E=e.O()}]);