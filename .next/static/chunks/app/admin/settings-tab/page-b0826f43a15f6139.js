(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[896],{13730:(e,s,n)=>{"use strict";n.r(s),n.d(s,{default:()=>a,dynamic:()=>i});var t=n(95155),r=n(97724),c=n(96728);let i="force-dynamic";function a(){return(0,t.jsx)(r.U,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Settings"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Configure system settings and preferences."})]}),(0,t.jsx)(c.t,{activeTab:"settings"})]})})}},85023:(e,s,n)=>{Promise.resolve().then(n.bind(n,13730))}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,1917,8128,5748,5973,7724,3660,8441,8229,1684,7358],()=>s(85023)),_N_E=e.O()}]);