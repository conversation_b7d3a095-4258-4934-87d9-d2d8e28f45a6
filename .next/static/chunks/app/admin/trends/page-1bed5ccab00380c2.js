(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3915],{12318:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},39346:(e,s,t)=>{Promise.resolve().then(t.bind(t,67673))},59409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>x,eb:()=>u,gC:()=>m,l6:()=>o,yv:()=>c});var a=t(95155);t(12115);var r=t(14582),i=t(66474),l=t(5196),n=t(47863),d=t(59434);function o(e){let{...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...s})}function c(e){let{...s}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...s})}function x(e){let{className:s,size:t="default",children:l,...n}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...n,children:[l,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:s,children:t,position:i="popper",...l}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:i,...l,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(j,{})]})})}function u(e){let{className:s,children:t,...i}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}function j(e){let{className:s,...t}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}},67673:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>D,dynamic:()=>R});var a=t(95155),r=t(97724),i=t(66695),l=t(59409),n=t(66681),d=t(31877),o=t(51154),c=t(33109),x=t(17580),m=t(12318),u=t(79397),h=t(12115),j=t(83540),f=t(46641),g=t(82396),v=["axis"],p=(0,h.forwardRef)((e,s)=>h.createElement(g.P,{chartName:"ComposedChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:v,tooltipPayloadSearcher:f.uN,categoricalChartProps:e,ref:s})),y=t(94754),w=t(96025),b=t(52071),N=t(24021),U=t(61667),k=t(25244),A=t(93504),S=t(13279),T=t(99445);let R="force-dynamic";function D(){let[e,s]=(0,h.useState)(null),[t,f]=(0,h.useState)(!0),[g,v]=(0,h.useState)(null),[R,D]=(0,h.useState)("day"),{user:q}=(0,n.A)();return((0,h.useEffect)(()=>{let e=async()=>{try{f(!0),v(null),(0,d.R)("mock-admin-token"),s({data:[{date:"2024-01-10",activeUsers:98,newUsers:5,totalRequests:1850,averageSessionTime:14.2},{date:"2024-01-11",activeUsers:105,newUsers:7,totalRequests:1950,averageSessionTime:15.1},{date:"2024-01-12",activeUsers:112,newUsers:9,totalRequests:2050,averageSessionTime:15.8},{date:"2024-01-13",activeUsers:120,newUsers:8,totalRequests:2100,averageSessionTime:16.2},{date:"2024-01-14",activeUsers:128,newUsers:11,totalRequests:2200,averageSessionTime:16.8},{date:"2024-01-15",activeUsers:135,newUsers:12,totalRequests:2350,averageSessionTime:17.1},{date:"2024-01-16",activeUsers:142,newUsers:15,totalRequests:2480,averageSessionTime:18.3},{date:"2024-01-17",activeUsers:158,newUsers:10,totalRequests:2650,averageSessionTime:19.1},{date:"2024-01-18",activeUsers:165,newUsers:8,totalRequests:2680,averageSessionTime:19.1},{date:"2024-01-19",activeUsers:150,newUsers:12,totalRequests:2450,averageSessionTime:18.3},{date:"2024-01-20",activeUsers:172,newUsers:14,totalRequests:2800,averageSessionTime:20.1}],summary:{totalDays:11,averageActiveUsers:140,totalNewUsers:111,growthRate:12.8}})}catch(e){console.error("Failed to fetch trends:",e),v(e.message||"Failed to fetch trends data")}finally{f(!1)}};(null==q?void 0:q.role)==="admin"&&e()},[q,R]),t)?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading trends data..."})]})})}):g?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:g}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"text-primary hover:underline",children:"Try again"})]})})}):(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Growth Trends"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Analyze growth patterns and user engagement trends over time."})]}),(0,a.jsxs)(l.l6,{value:R,onValueChange:e=>D(e),children:[(0,a.jsx)(l.bq,{className:"w-[180px]",children:(0,a.jsx)(l.yv,{placeholder:"Select granularity"})}),(0,a.jsxs)(l.gC,{children:[(0,a.jsx)(l.eb,{value:"day",children:"Daily"}),(0,a.jsx)(l.eb,{value:"week",children:"Weekly"}),(0,a.jsx)(l.eb,{value:"month",children:"Monthly"}),(0,a.jsx)(l.eb,{value:"year",children:"Yearly"})]})]})]}),e&&(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Growth Rate"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["+",e.summary.growthRate,"%"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Over ",e.summary.totalDays," days"]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Avg Active Users"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.summary.averageActiveUsers}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Daily average"})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Total New Users"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.summary.totalNewUsers}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["In ",e.summary.totalDays," days"]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Peak Active Users"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:Math.max(...e.data.map(e=>e.activeUsers))}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Highest single day"})]})]})]}),e&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"User Growth Trends"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(j.u,{width:"100%",height:400,children:(0,a.jsxs)(p,{data:e.data,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(w.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(b.h,{yAxisId:"left"}),(0,a.jsx)(b.h,{yAxisId:"right",orientation:"right"}),(0,a.jsx)(N.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(U.Gk,{yAxisId:"left",type:"monotone",dataKey:"activeUsers",fill:"#8884d8",stroke:"#8884d8",fillOpacity:.3,name:"Active Users"}),(0,a.jsx)(k.y,{yAxisId:"right",dataKey:"newUsers",fill:"#82ca9d",name:"New Users"})]})})})]}),e&&(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Session Time Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(j.u,{width:"100%",height:300,children:(0,a.jsxs)(A.b,{data:e.data,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(w.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(b.h,{}),(0,a.jsx)(N.m,{labelFormatter:e=>new Date(e).toLocaleDateString(),formatter:e=>["".concat(e,"m"),"Session Time"]}),(0,a.jsx)(S.N,{type:"monotone",dataKey:"averageSessionTime",stroke:"#ff7300",strokeWidth:3,name:"Avg Session Time (min)"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"API Usage Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(j.u,{width:"100%",height:300,children:(0,a.jsxs)(T.Q,{data:e.data,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(w.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(b.h,{}),(0,a.jsx)(N.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(U.Gk,{type:"monotone",dataKey:"totalRequests",stroke:"#ffc658",fill:"#ffc658",fillOpacity:.6,name:"Total Requests"})]})})})]})]})]})})}},79397:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,8062,8707,5854,3622,43,1917,8128,5748,5973,7724,8441,8229,1684,7358],()=>s(39346)),_N_E=e.O()}]);