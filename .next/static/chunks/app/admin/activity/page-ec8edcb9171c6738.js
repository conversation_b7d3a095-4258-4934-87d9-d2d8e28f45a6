(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2430],{3401:(e,t,s)=>{"use strict";s.d(t,{E:()=>n});var a=s(12115),r=s(46641),i=s(82396),l=["axis","item"],n=(0,a.forwardRef)((e,t)=>a.createElement(i.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:r.uN,categoricalChartProps:e,ref:t}))},39288:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>I,dynamic:()=>U});var a=s(95155),r=s(97724),i=s(66695),l=s(59409),n=s(66681),c=s(31877),d=s(73747),o=s(33109),x=s(68500),h=s(51154),m=s(17580),u=s(69074),g=s(79397),j=s(72713),f=s(12115),p=s(83540),v=s(93504),y=s(94754),N=s(96025),w=s(52071),b=s(24021),A=s(13279),k=s(99445),S=s(61667),T=s(3401),D=s(25244);let U="force-dynamic";function I(){let[e,t]=(0,f.useState)(null),[s,U]=(0,f.useState)(null),[I,Z]=(0,f.useState)(!0),[_,z]=(0,f.useState)(null),[R,W]=(0,f.useState)("day"),{user:C}=(0,n.A)();(0,f.useEffect)(()=>{let e=async()=>{try{Z(!0),z(null);let e=d.C.getAccessToken();if(e){(0,c.R)(e);try{console.log("Fetching real activity data from API...");let e=await c.i.getActivityMetrics({granularity:R}),s=await c.i.getActivityTrends({granularity:R});console.log("Real activity API data received:",{realMetrics:e,realTrends:s}),t(e),U(s);return}catch(t){let e=t instanceof Error?t.message:"Unknown error";console.log("Activity API fetch failed:",e),z("API Error: ".concat(e,". Please check if the backend is running."))}}else console.log("No admin token found"),z("No authentication token found. Please log in.")}catch(t){let e=t instanceof Error?t.message:"Unknown error";console.error("Failed to fetch activity data:",t),z(e||"Failed to fetch activity data")}finally{Z(!1)}};(null==C?void 0:C.role)==="admin"&&e()},[C,R]);let E=e=>{let t=e>0,s=t?o.A:x.A;return(0,a.jsxs)("div",{className:"flex items-center gap-1 ".concat(t?"text-green-600":"text-red-600"),children:[(0,a.jsx)(s,{className:"h-3 w-3"}),(0,a.jsxs)("span",{className:"text-xs",children:[Math.abs(e).toFixed(1),"%"]})]})};return I?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading activity metrics..."})]})})}):_?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:_}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"text-primary hover:underline",children:"Try again"})]})})}):(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Activity Metrics"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track user engagement and activity patterns."})]}),(0,a.jsxs)(l.l6,{value:R,onValueChange:e=>W(e),children:[(0,a.jsx)(l.bq,{className:"w-[180px]",children:(0,a.jsx)(l.yv,{placeholder:"Select granularity"})}),(0,a.jsxs)(l.gC,{children:[(0,a.jsx)(l.eb,{value:"day",children:"Daily"}),(0,a.jsx)(l.eb,{value:"week",children:"Weekly"}),(0,a.jsx)(l.eb,{value:"month",children:"Monthly"}),(0,a.jsx)(l.eb,{value:"year",children:"Yearly"})]})]})]}),e&&(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Daily Active Users"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.dau}),E(e.dauChange)]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Weekly Active Users"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.wau}),E(e.wauChange)]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Monthly Active Users"}),(0,a.jsx)(g.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.mau}),E(e.mauChange)]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Yearly Active Users"}),(0,a.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.yau}),E(e.yauChange)]})]})]}),s&&(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Active Users Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(p.u,{width:"100%",height:300,children:(0,a.jsxs)(v.b,{data:s.data,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(N.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(w.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(A.N,{type:"monotone",dataKey:"activeUsers",stroke:"#8884d8",strokeWidth:2,name:"Active Users"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"New Users Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(p.u,{width:"100%",height:300,children:(0,a.jsxs)(k.Q,{data:s.data,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(N.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(w.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(S.Gk,{type:"monotone",dataKey:"newUsers",stroke:"#82ca9d",fill:"#82ca9d",name:"New Users"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"API Requests Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(p.u,{width:"100%",height:300,children:(0,a.jsxs)(T.E,{data:s.data,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(N.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(w.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(D.y,{dataKey:"totalRequests",fill:"#ffc658",name:"Total Requests"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Average Session Time"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(p.u,{width:"100%",height:300,children:(0,a.jsxs)(v.b,{data:s.data,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(N.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(w.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString(),formatter:e=>["".concat(e,"m"),"Session Time"]}),(0,a.jsx)(A.N,{type:"monotone",dataKey:"averageSessionTime",stroke:"#ff7300",strokeWidth:2,name:"Avg Session Time (min)"})]})})})]})]}),s&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Summary Statistics"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.summary.totalDays}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Days Analyzed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.summary.averageActiveUsers}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Active Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.summary.totalNewUsers}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total New Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[s.summary.growthRate,"%"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Growth Rate"})]})]})})]})]})})}},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>m,gC:()=>h,l6:()=>d,yv:()=>o});var a=s(95155);s(12115);var r=s(14582),i=s(66474),l=s(5196),n=s(47863),c=s(59434);function d(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function o(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function x(e){let{className:t,size:s="default",children:l,...n}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":s,className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[l,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:s,position:i="popper",...l}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...l,children:[(0,a.jsx)(u,{}),(0,a.jsx)(r.LM,{className:(0,c.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(g,{})]})})}function m(e){let{className:t,children:s,...i}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(l.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function u(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(n.A,{className:"size-4"})})}function g(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}},68500:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},73747:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});let a="siift_access_token",r="siift_refresh_token",i="siift_user";class l{static setTokens(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],s=t?localStorage:sessionStorage;s.setItem(a,e.accessToken),e.refreshToken&&s.setItem(r,e.refreshToken)}static getAccessToken(){return localStorage.getItem(a)||sessionStorage.getItem(a)}static getRefreshToken(){return localStorage.getItem(r)||sessionStorage.getItem(r)}static setUser(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(t?localStorage:sessionStorage).setItem(i,JSON.stringify(e))}static getUser(){try{let e=localStorage.getItem(i)||sessionStorage.getItem(i);if(!e)return null;let t=JSON.parse(e);return t.createdAt&&"string"==typeof t.createdAt&&(t.createdAt=new Date(t.createdAt)),t.updatedAt&&"string"==typeof t.updatedAt&&(t.updatedAt=new Date(t.updatedAt)),t}catch(e){return console.error("Error parsing user data:",e),null}}static clearSession(){[localStorage,sessionStorage].forEach(e=>{e.removeItem(a),e.removeItem(r),e.removeItem(i)})}static clearInvalidSession(){let e=this.getAccessToken();e&&(!e.includes(".")||3!==e.split(".").length)&&(console.log("Clearing invalid token format"),this.clearSession())}static isAuthenticated(){return!!this.getAccessToken()}static getAuthHeaders(){let e=this.getAccessToken();return e?{Authorization:"Bearer ".concat(e)}:{}}}},78517:(e,t,s)=>{Promise.resolve().then(s.bind(s,39288))},79397:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,8062,8707,5854,3622,43,1917,8128,5748,5973,7724,8441,8229,1684,7358],()=>t(78517)),_N_E=e.O()}]);