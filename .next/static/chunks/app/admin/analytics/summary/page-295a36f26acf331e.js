(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6422],{10081:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},13052:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13416:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g,dynamic:()=>v});var s=a(95155),r=a(97724),l=a(26126),i=a(30285),d=a(66695),c=a(31877),n=a(17580),o=a(79397),h=a(33109),u=a(72713),y=a(51154),x=a(53904),m=a(85339),p=a(12115);let v="force-dynamic";function g(){let[e,t]=(0,p.useState)(null),[a,v]=(0,p.useState)(!1),[g,k]=(0,p.useState)(null),[f,b]=(0,p.useState)(null),A=async()=>{v(!0),k(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,c.R)(e);let a=await c.i.getAnalyticsSummary();t(a),b(new Date)}catch(e){k(e instanceof Error?e.message:"Unknown error")}finally{v(!1)}};(0,p.useEffect)(()=>{A()},[]);let j=e=>null==e?"0":e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),w=e=>{if(null==e)return"0s";if(e<60)return"".concat(e.toFixed(1),"s");let t=Math.floor(e/60),a=e%60;return"".concat(t,"m ").concat(a.toFixed(0),"s")},N=e?[{label:"Total Users",value:j(e.totalUsers),icon:n.A,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Active Today",value:j(e.activeToday),icon:o.A,color:"text-green-600",bgColor:"bg-green-50"},{label:"New This Week",value:j(e.newUsersThisWeek),icon:h.A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Feedback Rate",value:"".concat(e.feedbackRate,"%"),icon:u.A,color:"text-indigo-600",bgColor:"bg-indigo-50"}]:[],M=e?[{label:"Users with Feedback",value:j(e.usersWithFeedback)},{label:"Average Session Time",value:w(e.averageSessionTime)},{label:"Total Requests Today",value:j(e.totalRequestsToday)},{label:"Average Response Time",value:"".concat((e.averageResponseTime||0).toFixed(1),"ms")},{label:"System Uptime",value:w(e.systemUptime)}]:[];return(0,s.jsx)(r.U,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Analytics Summary"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Overview of key platform metrics and performance indicators."})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[f&&(0,s.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",f.toLocaleTimeString()]}),(0,s.jsxs)(i.$,{onClick:A,disabled:a,variant:"outline",className:"border",children:[a?(0,s.jsx)(y.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),g&&(0,s.jsx)(d.Zp,{className:"border-red-200 bg-red-50",children:(0,s.jsx)(d.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("p",{className:"text-red-800",children:g})]})})}),a&&!e&&(0,s.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,s.jsx)(y.A,{className:"h-8 w-8 animate-spin"})}),e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:N.map((e,t)=>(0,s.jsx)(d.Zp,{className:"bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:(0,s.jsx)(d.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)("div",{className:"p-3 rounded-full ".concat(e.bgColor),children:(0,s.jsx)(e.icon,{className:"h-6 w-6 ".concat(e.color)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,s.jsx)("p",{className:"text-3xl font-bold",children:e.value})]})]})})},t))}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Additional Metrics"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:M.map((e,t)=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,s.jsx)("p",{className:"text-2xl font-bold",children:e.value})]},t))})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsx)(d.ZB,{children:"Status"})}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.E,{variant:"default",className:"bg-green-100 text-green-800",children:"Data Loaded Successfully"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"All metrics are up to date"})]})})]})]})]})})}},14395:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},14738:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},23861:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},25487:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>c});var s=a(95155);a(12115);var r=a(99708),l=a(74466),i=a(59434);let d=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:a,asChild:l=!1,...c}=e,n=l?r.DX:"span";return(0,s.jsx)(n,{"data-slot":"badge",className:(0,i.cn)(d({variant:a}),t),...c})}},32919:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},33109:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},34869:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},49376:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},53311:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},53904:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},61106:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},69803:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},79397:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},81586:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},85237:(e,t,a)=>{Promise.resolve().then(a.bind(a,13416))},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},97200:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("file-chart-column-increasing",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 18v-2",key:"qcmpov"}],["path",{d:"M12 18v-4",key:"q1q25u"}],["path",{d:"M16 18v-6",key:"15y0np"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,1917,8128,5748,5973,7724,8441,8229,1684,7358],()=>t(85237)),_N_E=e.O()}]);