(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[154],{3401:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(12115),r=t(46641),i=t(82396),l=["axis","item"],d=(0,a.forwardRef)((e,s)=>a.createElement(i.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:r.uN,categoricalChartProps:e,ref:s}))},12318:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(95155);t(12115);var r=t(99708),i=t(74466),l=t(59434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:i=!1,...c}=e,n=i?r.DX:"span";return(0,a.jsx)(n,{"data-slot":"badge",className:(0,l.cn)(d({variant:t}),s),...c})}},51221:(e,s,t)=>{Promise.resolve().then(t.bind(t,56980))},53904:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},56980:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>T,dynamic:()=>S});var a=t(95155),r=t(97724),i=t(26126),l=t(30285),d=t(66695),c=t(31877),n=t(79397),o=t(17580),h=t(12318),x=t(33109),m=t(51154),u=t(53904),v=t(85339),j=t(12115),g=t(83540),p=t(93504),y=t(94754),f=t(96025),b=t(52071),N=t(24021),k=t(13279),w=t(3401),A=t(25244);let S="force-dynamic";function T(){var e,s,t;let[S,T]=(0,j.useState)(null),[U,W]=(0,j.useState)(!1),[D,R]=(0,j.useState)(null),[E,C]=(0,j.useState)(null),Z=async()=>{W(!0),R(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,c.R)(e);let s=await c.i.getActivityTrends();T(s),C(new Date)}catch(e){R(e.message)}finally{W(!1)}};(0,j.useEffect)(()=>{Z()},[]);let _=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),q=e=>{if(e<60)return"".concat(e.toFixed(1),"s");let s=Math.floor(e/60),t=e%60;return"".concat(s,"m ").concat(t.toFixed(0),"s")},K=(null==S?void 0:S.summary)?[{label:"Total Days",value:S.summary.totalDays,icon:n.A,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Avg Active Users",value:_(S.summary.averageActiveUsers),icon:o.A,color:"text-green-600",bgColor:"bg-green-50"},{label:"Total New Users",value:_(S.summary.totalNewUsers),icon:h.A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Growth Rate",value:"".concat(S.summary.growthRate.toFixed(1),"%"),icon:x.A,color:"text-orange-600",bgColor:"bg-orange-50"}]:[],M=(null==S||null==(e=S.data)?void 0:e.map(e=>({...e,date:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"})})))||[];return(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Activity Trends"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Visualize user activity patterns and trends over time."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[E&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",E.toLocaleTimeString()]}),(0,a.jsxs)(l.$,{onClick:Z,disabled:U,variant:"outline",children:[U?(0,a.jsx)(m.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),D&&(0,a.jsx)(d.Zp,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(d.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:D})]})})}),U&&!S&&(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(m.A,{className:"h-8 w-8 animate-spin"})}),S&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:K.map((e,s)=>(0,a.jsx)(d.Zp,{children:(0,a.jsx)(d.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-full ".concat(e.bgColor),children:(0,a.jsx)(e.icon,{className:"h-6 w-6 ".concat(e.color)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.value})]})]})})},s))}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Active Users Trend"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"h-80",children:(0,a.jsx)(g.u,{width:"100%",height:"100%",children:(0,a.jsxs)(p.b,{data:M,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"date"}),(0,a.jsx)(b.h,{}),(0,a.jsx)(N.m,{formatter:(e,s)=>[_(e),"activeUsers"===s?"Active Users":"New Users"]}),(0,a.jsx)(k.N,{type:"monotone",dataKey:"activeUsers",stroke:"#166534",strokeWidth:2,dot:{fill:"#166534",strokeWidth:2,r:4}}),(0,a.jsx)(k.N,{type:"monotone",dataKey:"newUsers",stroke:"#7c3aed",strokeWidth:2,dot:{fill:"#7c3aed",strokeWidth:2,r:4}})]})})})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Total Requests"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(g.u,{width:"100%",height:"100%",children:(0,a.jsxs)(w.E,{data:M,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"date"}),(0,a.jsx)(b.h,{}),(0,a.jsx)(N.m,{formatter:e=>[_(e),"Requests"]}),(0,a.jsx)(A.y,{dataKey:"totalRequests",fill:"#166534"})]})})})})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Average Session Time"})}),(0,a.jsx)(d.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(g.u,{width:"100%",height:"100%",children:(0,a.jsxs)(p.b,{data:M,children:[(0,a.jsx)(y.d,{strokeDasharray:"3 3"}),(0,a.jsx)(f.W,{dataKey:"date"}),(0,a.jsx)(b.h,{}),(0,a.jsx)(N.m,{formatter:e=>[q(e),"Session Time"]}),(0,a.jsx)(k.N,{type:"monotone",dataKey:"averageSessionTime",stroke:"#ea580c",strokeWidth:2,dot:{fill:"#ea580c",strokeWidth:2,r:4}})]})})})})]})]}),(0,a.jsxs)(d.Zp,{children:[(0,a.jsx)(d.aR,{children:(0,a.jsx)(d.ZB,{children:"Detailed Data"})}),(0,a.jsxs)(d.Wu,{children:[(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsxs)("table",{className:"w-full text-sm",children:[(0,a.jsx)("thead",{children:(0,a.jsxs)("tr",{className:"border-b",children:[(0,a.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Active Users"}),(0,a.jsx)("th",{className:"text-right p-2",children:"New Users"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Total Requests"}),(0,a.jsx)("th",{className:"text-right p-2",children:"Avg Session Time"})]})}),(0,a.jsx)("tbody",{children:null==(s=S.data)?void 0:s.slice(0,10).map((e,s)=>(0,a.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,a.jsx)("td",{className:"p-2",children:new Date(e.date).toLocaleDateString()}),(0,a.jsx)("td",{className:"text-right p-2",children:_(e.activeUsers)}),(0,a.jsx)("td",{className:"text-right p-2",children:_(e.newUsers)}),(0,a.jsx)("td",{className:"text-right p-2",children:_(e.totalRequests)}),(0,a.jsx)("td",{className:"text-right p-2",children:q(e.averageSessionTime)})]},s))})]})}),(null==(t=S.data)?void 0:t.length)>10&&(0,a.jsx)("div",{className:"mt-4 text-center",children:(0,a.jsxs)(i.E,{variant:"outline",children:["Showing first 10 of ",S.data.length," records"]})})]})]})]})]})})}},79397:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},85339:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,8707,5854,3622,1917,8128,5748,5973,7724,8441,8229,1684,7358],()=>s(51221)),_N_E=e.O()}]);