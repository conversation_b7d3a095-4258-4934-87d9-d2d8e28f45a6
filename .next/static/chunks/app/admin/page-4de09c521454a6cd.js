(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3698],{27366:(e,a,s)=>{"use strict";s.d(a,{GF:()=>t,Ni:()=>r});var i=s(56671);let t=(e,a)=>{i.oR.success(e,{description:null==a?void 0:a.description,duration:(null==a?void 0:a.duration)||4e3})},r=(e,a)=>{i.oR.error(e,{description:null==a?void 0:a.description,duration:(null==a?void 0:a.duration)||5e3})}},66681:(e,a,s)=>{"use strict";s.d(a,{A:()=>l});var i=s(88128),t=s(35695),r=s(12115),n=s(27366);function l(){let{user:e,isAuthenticated:a,error:s,pendingEmailVerification:l,emailVerificationSent:c,actions:d}=(0,i.B)(),o=(0,t.useRouter)(),[u,m]=(0,r.useState)(!1),h=(0,r.useCallback)(async e=>{m(!0);try{await d.login(e);let{user:a}=i.B.getState();(0,n.GF)("Welcome back!",{description:"Logged in as ".concat(null==a?void 0:a.email)}),(null==a?void 0:a.role)==="admin"?o.replace("/admin"):o.replace("/user-dashboard")}catch(a){let e=a instanceof Error?a.message:"Login failed";throw(0,n.Ni)("Login Failed",{description:e}),a}finally{m(!1)}},[d,o,m]),f=(0,r.useCallback)(async e=>{m(!0);try{await d.signup(e);let{user:a}=i.B.getState();(0,n.GF)("Account created successfully!",{description:"Welcome ".concat((null==a?void 0:a.name)||(null==a?void 0:a.email),"!")}),(null==a?void 0:a.role)==="admin"?o.replace("/admin"):o.replace("/user-dashboard")}catch(a){let e=a instanceof Error?a.message:"Signup failed";throw(0,n.Ni)("Signup Failed",{description:e}),a}finally{m(!1)}},[d,o,m]),x=(0,r.useCallback)(()=>{d.logout(),o.push("/auth/login")},[d,o]),b=(0,r.useCallback)(()=>{d.setError(null)},[d]),p=(0,r.useCallback)(async(e,a)=>{try{await d.sendEmailVerification(e,a)}catch(e){throw e}},[d]);return{user:e,isAuthenticated:a,isLoading:u,error:s,pendingEmailVerification:l,emailVerificationSent:c,login:h,signup:f,logout:x,clearError:b,sendEmailVerification:p,verifyEmail:(0,r.useCallback)(async(e,a)=>{try{await d.verifyEmail(e,a)}catch(e){throw e}},[d]),resendEmailVerification:(0,r.useCallback)(async e=>{try{await d.resendEmailVerification(e)}catch(e){throw e}},[d]),updateUser:d.updateUser,refreshToken:d.refreshToken}}},70794:(e,a,s)=>{"use strict";s.d(a,{h:()=>c});var i=s(95155),t=s(66681),r=s(75525),n=s(35695),l=s(12115);function c(e){var a;let{children:s}=e,{user:c,isAuthenticated:d,isLoading:o}=(0,t.A)(),u=(0,n.useRouter)();return((0,l.useEffect)(()=>{if(!o){if(!d)return void u.push("/");if((null==c?void 0:c.role)!=="admin")return void u.push("/user-dashboard")}},[d,o,c,u]),o)?(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):d?(null==c||null==(a=c.email)?void 0:a.includes("siift.ai"))?(0,i.jsx)(i.Fragment,{children:s}):(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(r.A,{className:"h-12 w-12 mx-auto mb-4 text-destructive"}),(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Access Denied"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"You need a siift.ai email address to access the admin panel."}),(0,i.jsx)("button",{onClick:()=>u.push("/user-dashboard"),className:"mt-4 text-primary hover:underline",children:"Go to Dashboard"})]})}):(0,i.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(r.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,i.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Authentication Required"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Please log in to access this page."})]})})}},77220:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>c,dynamic:()=>l});var i=s(95155),t=s(70794),r=s(35695),n=s(12115);let l="force-dynamic";function c(){let e=(0,r.useRouter)();return(0,n.useEffect)(()=>{e.replace("/admin/analytics/summary")},[e]),(0,i.jsx)(t.h,{children:(0,i.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)("p",{className:"text-muted-foreground",children:"Redirecting to admin dashboard..."})})})})}},83393:(e,a,s)=>{Promise.resolve().then(s.bind(s,77220))}},e=>{var a=a=>e(e.s=a);e.O(0,[6671,5596,1917,8128,8441,8229,1684,7358],()=>a(83393)),_N_E=e.O()}]);