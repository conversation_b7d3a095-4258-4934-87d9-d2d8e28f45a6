(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7173],{1475:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t,dynamic:()=>r});var c=a(95155),l=a(97724),n=a(96728);let r="force-dynamic";function t(){return(0,c.jsx)(l.U,{children:(0,c.jsxs)("div",{className:"space-y-6",children:[(0,c.jsxs)("div",{children:[(0,c.jsx)("h1",{className:"text-3xl font-bold",children:"All Projects"}),(0,c.jsx)("p",{className:"text-muted-foreground",children:"View and manage all existing projects."})]}),(0,c.jsx)(n.t,{activeTab:"projects"})]})})}},97230:(e,s,a)=>{Promise.resolve().then(a.bind(a,1475))}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8243,1917,8128,5748,5973,7724,3660,8441,8229,1684,7358],()=>s(97230)),_N_E=e.O()}]);