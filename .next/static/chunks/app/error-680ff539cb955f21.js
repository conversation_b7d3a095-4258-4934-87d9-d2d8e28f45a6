(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8039],{1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},7362:(e,t,r)=>{Promise.resolve().then(r.bind(r,51901))},11518:(e,t,r)=>{"use strict";e.exports=r(82269).style},51901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(95155),i=r(12115),s=r(6874),o=r.n(s),a=r(1243),c=r(53904),l=r(57340),d=r(92236),u=r(99912);function h(e){let{error:t,reset:r}=e;return(0,i.useEffect)(()=>{console.error("Application error:",t)},[t]),(0,n.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,n.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.02]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='27' cy='7' r='1'/%3E%3Ccircle cx='47' cy='7' r='1'/%3E%3Ccircle cx='7' cy='27' r='1'/%3E%3Ccircle cx='27' cy='27' r='1'/%3E%3Ccircle cx='47' cy='27' r='1'/%3E%3Ccircle cx='7' cy='47' r='1'/%3E%3Ccircle cx='27' cy='47' r='1'/%3E%3Ccircle cx='47' cy='47' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-background via-transparent to-background"}),(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-transparent to-background"}),(0,n.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.025] mix-blend-overlay",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,n.jsx)("div",{className:"relative z-10 flex min-h-screen items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"text-center space-y-8 max-w-md",children:[(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)(d.g,{size:64,animated:!1}),(0,n.jsx)("span",{className:"text-3xl font-bold text-foreground",children:"Siift"})]})}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsx)(a.A,{className:"h-16 w-16 text-destructive"})}),(0,n.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Something went wrong"}),(0,n.jsx)("p",{className:"text-muted-foreground",children:"An unexpected error occurred"})]}),(0,n.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,n.jsxs)(u.V,{onClick:r,size:"lg",className:"px-6",children:[(0,n.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Try Again"]}),(0,n.jsx)(o(),{href:"/",children:(0,n.jsxs)(u.V,{variant:"outline",size:"lg",className:"px-6",children:[(0,n.jsx)(l.A,{className:"h-5 w-5 mr-2"}),"Back to Main"]})})]})]})})]})}},53904:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57340:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},59434:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(52596),i=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}},68375:()=>{},82269:(e,t,r)=>{"use strict";var n=r(49509);r(68375);var i=r(12115),s=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},c=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,i=t.optimizeForSpeed,s=void 0===i?o:i;l(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",l("boolean"==typeof s,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=s,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var c="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=c?c.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(l(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(l(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];l(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];l(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&l(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(n,r):i.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return u[n]||(u[n]="jsx-"+d(e+"-"+r)),u[n]}function f(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return u[r]||(u[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[r]}var m=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,i=t.optimizeForSpeed,s=void 0!==i&&i;this._sheet=n||new c({name:"styled-jsx",optimizeForSpeed:s}),this._sheet.inject(),n&&"boolean"==typeof s&&(this._sheet.setOptimizeForSpeed(s),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,i=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var s=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=s,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return s.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var i=h(n,r);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return f(i,e)}):[f(i,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=i.createContext(null);p.displayName="StyleSheetContext";var g=s.default.useInsertionEffect||s.default.useLayoutEffect,v="undefined"!=typeof window?new m:void 0;function y(e){var t=v||i.useContext(p);return t&&("undefined"==typeof window?t.add(e):g(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}y.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=y},91950:(e,t,r)=>{"use strict";r.d(t,{hS:()=>n});let n={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-7 w-7",xl:"h-8 w-8",badge:"h-4 w-4",navigation:"h-6 w-6",sidebar:"h-6 w-6",header:"h-6 w-6",button:"h-6 w-6",projectIcon:"h-6 w-6",actionIcon:"h-6 w-6",chatIcon:"h-6 w-6",profileIcon:"h-6 w-6",notificationIcon:"h-6 w-6"}},92236:(e,t,r)=>{"use strict";r.d(t,{g:()=>c});var n=r(95155),i=r(11518),s=r.n(i);r(12115);var o=r(6874),a=r.n(o);let c=e=>{let{size:t=48,textSize:r=24,className:i="",animated:o=!1,showText:c=!1,href:l,animationSpeed:d=5}=e,u=t/48,h=32*u,f=20*u,m=+u,p=1.25*u,g=(0,n.jsxs)("div",{style:{position:"relative",width:t,height:t,display:"inline-block",animation:o?"logoFloat ".concat(3/d,"s ease-in-out infinite"):"none"},children:[(0,n.jsx)("div",{style:{position:"absolute",width:t,height:t,borderRadius:"50%",background:"#b4fd98",border:"".concat(m,"px solid #73ed47"),left:0,top:0,zIndex:1,transform:"rotate(70deg)",animation:o?"logoRotate ".concat(8/d,"s linear infinite"):"none"},children:(0,n.jsx)("div",{style:{position:"absolute",width:h,height:h,borderRadius:"50%",background:"#0A4000",border:"".concat(m,"px solid #73ed47"),left:t-h-m-p,top:m+p,zIndex:2,transform:"rotate(280deg)",animation:o?"logoPulse ".concat(2/d,"s ease-in-out infinite"):"none"},children:(0,n.jsx)("div",{style:{position:"absolute",width:f,height:f,borderRadius:"50%",background:"#fff",border:"".concat(m,"px solid #73ed47"),left:h-f-m-p,top:m+p,zIndex:3,animation:o?"logoGlow ".concat(4/d,"s ease-in-out infinite"):"none"}})})}),o&&(0,n.jsx)(s(),{id:"5428b1dfccf92ce4",children:"@-webkit-keyframes logoRotate{0%{-webkit-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);transform:rotate(430deg)}}@-moz-keyframes logoRotate{0%{-moz-transform:rotate(70deg);transform:rotate(70deg)}100%{-moz-transform:rotate(430deg);transform:rotate(430deg)}}@-o-keyframes logoRotate{0%{-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-o-transform:rotate(430deg);transform:rotate(430deg)}}@keyframes logoRotate{0%{-webkit-transform:rotate(70deg);-moz-transform:rotate(70deg);-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);-moz-transform:rotate(430deg);-o-transform:rotate(430deg);transform:rotate(430deg)}}"})]}),v=(0,n.jsxs)("div",{className:"flex items-center gap-3 ".concat(i),children:[g,(0,n.jsx)("span",{className:"font-bold text-foreground",style:{fontSize:"".concat(r,"px"),position:"relative",animation:o?"textGlow ".concat(3/d,"s ease-in-out infinite"):"none"},children:"siift.ai"})]}),y=c?v:g;return l?(0,n.jsx)(a(),{href:l,className:"hover:opacity-80 transition-opacity",children:y}):y}},99912:(e,t,r)=>{"use strict";r.d(t,{V:()=>o});var n=r(95155),i=r(91950),s=r(59434);let o=r(12115).forwardRef((e,t)=>{let{className:r,icon:o,iconClassName:a,text:c,subText:l,textClassName:d,subTextClassName:u,badge:h,badgeClassName:f,trailing:m,variant:p="default",size:g="md",showBorder:v=!1,borderClassName:y,layout:x="horizontal",hoverColor:_="green",hoverScale:b=!0,children:S,...w}=e,j={sm:i.hS.sm,md:i.hS.md,lg:i.hS.lg};return(0,n.jsxs)("button",{ref:t,className:(0,s.cn)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer",{default:"bg-background hover:bg-sidebar-accent",ghost:"bg-transparent hover:bg-sidebar-accent",outline:"bg-background border border-input hover:bg-gray-100",accent:"bg-sidebar-accent hover:bg-sidebar-accent/80",secondary:"bg-gray-900 text-white hover:bg-gray-800"}[p],{sm:"icon-only"===x?"h-6 w-6 p-1":"h-8 px-2 py-1 text-xs",md:"icon-only"===x?"h-8 w-8 p-1.5":"h-10 px-3 py-2 text-sm",lg:"icon-only"===x?"h-10 w-10 p-2":"h-12 px-4 py-3 text-base"}[g],{green:"hover:text-[var(--primary-dark)]",grey:"hover:text-muted-foreground hover:bg-muted/50 hover:border-border",accent:"hover:text-accent-foreground"}[_],{horizontal:"flex-row gap-2",vertical:"flex-col gap-1","icon-only":"flex-row"}[x],v?"border ".concat(y||"border-border/30"):"","rounded-lg","horizontal"===x&&c?"justify-start":"","icon-only"===x?"justify-center":"",r),...w,children:[o&&(0,n.jsx)(o,{className:(0,s.cn)(a||j[g],b?"transition-transform duration-200 hover:scale-110":"")}),(c||l)&&"icon-only"!==x&&(0,n.jsxs)("div",{className:(0,s.cn)("flex flex-col","horizontal"===x?"flex-1 text-left":"text-center","vertical"===x?"items-center":"items-start"),children:[c&&(0,n.jsx)("span",{className:(0,s.cn)("font-medium truncate","sm"===g?"text-xs":"md"===g?"text-sm":"text-base",d),children:c}),l&&(0,n.jsx)("span",{className:(0,s.cn)("text-muted-foreground truncate","text-xs",u),children:l})]}),h&&(0,n.jsx)("span",{className:(0,s.cn)("bg-primary text-primary-foreground rounded-md flex items-center justify-center absolute -top-1 -right-1 z-10","sm"===g||"md"===g?"text-xs h-4 min-w-4":"text-sm h-5 min-w-5",f),children:h}),m&&"icon-only"!==x&&(0,n.jsx)("div",{className:"ml-auto",children:m}),S]})});o.displayName="SidebarButton"}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,8441,8229,1684,7358],()=>t(7362)),_N_E=e.O()}]);