(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{4229:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},18175:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},22346:(e,t,a)=>{"use strict";a.d(t,{w:()=>i});var r=a(95155);a(12115);var s=a(87489),n=a(59434);function i(e){let{className:t,orientation:a="horizontal",decorative:i=!0,...l}=e;return(0,r.jsx)(s.b,{"data-slot":"separator",decorative:i,orientation:a,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...l})}},28883:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},33246:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S,dynamic:()=>A});var r=a(95155),s=a(63560),n=a(28883),i=a(69074),l=a(75525),c=a(51154),o=a(4229),d=a(12115),u=a(62177),m=a(70927),x=a(86473),f=a(91394),h=a(26126),p=a(30285),b=a(66695),g=a(62523),v=a(85057),N=a(22346),j=a(88539),y=a(90786),w=a(17739),k=a(16549);let A="force-dynamic",L=m.Ik({firstName:m.Yj().min(1,"First name is required"),lastName:m.Yj().min(1,"Last name is required"),bio:m.Yj().max(500,"Bio must be less than 500 characters").optional(),location:m.Yj().max(100,"Location must be less than 100 characters").optional(),website:m.Yj().url("Please enter a valid URL").optional().or(m.eu(""))});function S(){let{user:e,firstName:t,lastName:a,email:m,imageUrl:A,fullName:S}=(0,y.P)(),{data:z,isLoading:E}=(0,w.Jd)(),M=(0,k.j9)(),[C,F]=(0,d.useState)(!1),[P,R]=(0,d.useState)(null),[B,T]=(0,d.useState)({bio:"Software developer passionate about building great products",location:"San Francisco, CA",website:"https://example.com"}),{register:_,handleSubmit:U,formState:{errors:q,isDirty:J},reset:H,setValue:Y}=(0,u.mN)({resolver:(0,s.u)(L),defaultValues:{firstName:t||"",lastName:a||"",bio:B.bio||"",location:B.location||"",website:B.website||""}}),D=async t=>{if(e){F(!0),R(null);try{await M.mutateAsync({firstName:t.firstName,lastName:t.lastName,bio:t.bio,timezone:"UTC",preferences:{...B,location:t.location,website:t.website}}),T({bio:t.bio||"",location:t.location||"",website:t.website||""}),R({type:"success",text:"Profile updated successfully!"}),H(t)}catch(e){R({type:"error",text:e.message||"Failed to update profile"})}finally{F(!1)}}};return e?("".concat((null==t?void 0:t[0])||"").concat((null==a?void 0:a[0])||"").toUpperCase(),(0,r.jsx)(x.N,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Profile"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and profile information."})]}),(0,r.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,r.jsxs)(b.Zp,{className:"lg:col-span-1 bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,r.jsxs)(b.aR,{children:[(0,r.jsx)(b.ZB,{className:"text-card-foreground",children:"Profile Information"}),(0,r.jsx)(b.BT,{className:"text-muted-foreground",children:"Your personal information and account details."})]}),(0,r.jsxs)(b.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(f.eu,{className:"h-16 w-16 border-2 border-accent/20",children:[(0,r.jsx)(f.BK,{src:A||"",alt:S||"User"}),(0,r.jsxs)(f.q5,{className:"text-lg bg-accent text-accent-foreground font-semibold",children:[null==t?void 0:t[0],null==a?void 0:a[0]]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-card-foreground",children:S||"User"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:m}),(0,r.jsx)(h.E,{variant:"secondary",className:"mt-1 bg-accent/10 text-accent border-accent/20",children:"User"})]})]}),(0,r.jsx)(N.w,{className:"bg-border"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"Email:"}),(0,r.jsx)("span",{className:"text-card-foreground",children:m})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"Joined:"}),(0,r.jsx)("span",{className:"text-card-foreground",children:(null==e?void 0:e.createdAt)?new Date(e.createdAt).toLocaleDateString():"Recently"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,r.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,r.jsx)(h.E,{variant:"outline",className:"text-xs border-green-500/20 text-green-600 bg-green-500/10",children:"Active"})]})]})]})]}),(0,r.jsxs)(b.Zp,{className:"lg:col-span-2 bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,r.jsxs)(b.aR,{children:[(0,r.jsx)(b.ZB,{className:"text-card-foreground",children:"Edit Profile"}),(0,r.jsx)(b.BT,{className:"text-muted-foreground",children:"Update your personal information and preferences."})]}),(0,r.jsx)(b.Wu,{children:(0,r.jsxs)("form",{onSubmit:U(D),className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(v.J,{htmlFor:"firstName",className:"text-card-foreground",children:"First Name"}),(0,r.jsx)(g.p,{id:"firstName",..._("firstName"),placeholder:"Enter your first name",className:"border-border focus:border-accent focus:ring-accent/20"}),q.firstName&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:q.firstName.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(v.J,{htmlFor:"lastName",className:"text-card-foreground",children:"Last Name"}),(0,r.jsx)(g.p,{id:"lastName",..._("lastName"),placeholder:"Enter your last name",className:"border-border focus:border-accent focus:ring-accent/20"}),q.lastName&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:q.lastName.message})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(v.J,{htmlFor:"bio",className:"text-card-foreground",children:"Bio"}),(0,r.jsx)(j.T,{id:"bio",..._("bio"),placeholder:"Tell us about yourself...",rows:3,className:"border-border focus:border-accent focus:ring-accent/20"}),q.bio&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:q.bio.message})]}),(0,r.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(v.J,{htmlFor:"location",className:"text-card-foreground",children:"Location"}),(0,r.jsx)(g.p,{id:"location",..._("location"),placeholder:"City, Country",className:"border-border focus:border-accent focus:ring-accent/20"}),q.location&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:q.location.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(v.J,{htmlFor:"website",className:"text-card-foreground",children:"Website"}),(0,r.jsx)(g.p,{id:"website",..._("website"),placeholder:"https://example.com",className:"border-border focus:border-accent focus:ring-accent/20"}),q.website&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:q.website.message})]})]}),P&&(0,r.jsx)("div",{className:"p-3 rounded-md text-sm border ".concat("success"===P.type?"bg-green-500/10 text-green-700 border-green-500/20":"bg-red-500/10 text-red-700 border-red-500/20"),children:P.text}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsxs)(p.$,{type:"submit",disabled:C||M.isPending||!J,className:"bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border",children:[(C||M.isPending)&&(0,r.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,r.jsx)(o.A,{className:"mr-2 h-4 w-4"}),M.isPending?"Updating...":"Save Changes"]})})]})})]})]})]})})):(0,r.jsx)(x.N,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-destructive mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"You need to be signed in to view this page."})]})})})}},40968:(e,t,a)=>{"use strict";a.d(t,{b:()=>l});var r=a(12115),s=a(63655),n=a(95155),i=r.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var l=i},51635:(e,t,a)=>{Promise.resolve().then(a.bind(a,33246))},54011:(e,t,a)=>{"use strict";a.d(t,{H4:()=>k,_V:()=>w,bL:()=>y});var r=a(12115),s=a(46081),n=a(39033),i=a(52712),l=a(63655),c=a(49033);function o(){return()=>{}}var d=a(95155),u="Avatar",[m,x]=(0,s.A)(u),[f,h]=m(u),p=r.forwardRef((e,t)=>{let{__scopeAvatar:a,...s}=e,[n,i]=r.useState("idle");return(0,d.jsx)(f,{scope:a,imageLoadingStatus:n,onImageLoadingStatusChange:i,children:(0,d.jsx)(l.sG.span,{...s,ref:t})})});p.displayName=u;var b="AvatarImage",g=r.forwardRef((e,t)=>{let{__scopeAvatar:a,src:s,onLoadingStatusChange:u=()=>{},...m}=e,x=h(b,a),f=function(e,t){let{referrerPolicy:a,crossOrigin:s}=t,n=(0,c.useSyncExternalStore)(o,()=>!0,()=>!1),l=r.useRef(null),d=n?(l.current||(l.current=new window.Image),l.current):null,[u,m]=r.useState(()=>j(d,e));return(0,i.N)(()=>{m(j(d,e))},[d,e]),(0,i.N)(()=>{let e=e=>()=>{m(e)};if(!d)return;let t=e("loaded"),r=e("error");return d.addEventListener("load",t),d.addEventListener("error",r),a&&(d.referrerPolicy=a),"string"==typeof s&&(d.crossOrigin=s),()=>{d.removeEventListener("load",t),d.removeEventListener("error",r)}},[d,s,a]),u}(s,m),p=(0,n.c)(e=>{u(e),x.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==f&&p(f)},[f,p]),"loaded"===f?(0,d.jsx)(l.sG.img,{...m,ref:t,src:s}):null});g.displayName=b;var v="AvatarFallback",N=r.forwardRef((e,t)=>{let{__scopeAvatar:a,delayMs:s,...n}=e,i=h(v,a),[c,o]=r.useState(void 0===s);return r.useEffect(()=>{if(void 0!==s){let e=window.setTimeout(()=>o(!0),s);return()=>window.clearTimeout(e)}},[s]),c&&"loaded"!==i.imageLoadingStatus?(0,d.jsx)(l.sG.span,{...n,ref:t}):null});function j(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}N.displayName=v;var y=p,w=g,k=N},59099:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>d});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex px-6 [.border-t]:pt-6",t),...a})}},69074:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},72894:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(95155);a(12115);var s=a(40968),n=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},86473:(e,t,a)=>{"use strict";a.d(t,{N:()=>l});var r=a(95155),s=a(36996),n=a(92262);function i(e){let{children:t,showHeader:a=!0,showFooter:i=!0,constrainHeight:l=!1}=e;return(0,r.jsxs)("div",{className:"".concat(l?"h-screen":"min-h-screen"," flex flex-col bg-background"),children:[a&&(0,r.jsx)(n.Header,{}),(0,r.jsx)("main",{className:"flex-1 ".concat(l?"min-h-0":""),children:t}),i&&(0,r.jsx)(s.Footer,{})]})}function l(e){let{children:t}=e;return(0,r.jsx)(i,{showFooter:!1,constrainHeight:!0,children:(0,r.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:t})})}},87489:(e,t,a)=>{"use strict";a.d(t,{b:()=>o});var r=a(12115),s=a(63655),n=a(95155),i="horizontal",l=["horizontal","vertical"],c=r.forwardRef((e,t)=>{var a;let{decorative:r,orientation:c=i,...o}=e,d=(a=c,l.includes(a))?c:i;return(0,n.jsx)(s.sG.div,{"data-orientation":d,...r?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...o,ref:t})});c.displayName="Separator";var o=c},88539:(e,t,a)=>{"use strict";a.d(t,{T:()=>i});var r=a(95155),s=a(12115),n=a(59434);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:t,...s})});i.displayName="Textarea"},91394:(e,t,a)=>{"use strict";a.d(t,{BK:()=>l,eu:()=>i,q5:()=>c});var r=a(95155);a(12115);var s=a(54011),n=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)(s._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(s.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}},92262:(e,t,a)=>{"use strict";a.d(t,{Header:()=>o});var r=a(95155);a(12115);var s=a(18994),n=a(56611),i=a(41752),l=a(66688),c=a(92236);function o(){return(0,r.jsx)("header",{className:"w-full sticky top-0 z-30 bg-background/80 backdrop-blur border-b border-border",children:(0,r.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,r.jsx)(n.c,{})}),(0,r.jsx)(c.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,r.jsx)("div",{className:"hidden md:flex ml-6",children:(0,r.jsx)(s.N,{})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(l.U,{}),(0,r.jsx)(i.B,{})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,4043,5123,8198,1917,8128,5748,387,3856,8441,8229,1684,7358],()=>t(51635)),_N_E=e.O()}]);