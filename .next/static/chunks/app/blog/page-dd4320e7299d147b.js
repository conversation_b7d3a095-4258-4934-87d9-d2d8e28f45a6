(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3831],{14186:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},18375:(e,r,s)=>{"use strict";s.d(r,{BlogCard:()=>m});var t=s(95155),a=s(26126),n=s(66695),i=s(69074),o=s(14186),l=s(71469),d=s.n(l),c=s(6874),u=s.n(c);function m(e){let{post:r,className:s=""}=e;return(0,t.jsx)(u(),{href:"/blog/".concat(r.slug),className:"block ".concat(s),children:(0,t.jsxs)(n.Zp,{className:"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer border h-full flex flex-col p-2",children:[r.image&&(0,t.jsxs)("div",{className:"relative w-full h-48 overflow-hidden rounded-t-lg",children:[(0,t.jsx)(d(),{src:r.image,alt:r.title,fill:!0,className:"object-cover transition-transform duration-200 hover:scale-105",sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",unoptimized:!0}),r.featured&&(0,t.jsx)(a.E,{className:"absolute top-3 left-3 bg-[#166534] hover:bg-[#166534]/90",children:"Featured"})]}),(0,t.jsxs)(n.aR,{className:"pb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(i.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:new Date(r.publishedAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(o.A,{className:"w-4 h-4"}),(0,t.jsxs)("span",{children:[r.readTime," min read"]})]})]}),(0,t.jsx)(n.ZB,{className:"text-lg font-semibold line-clamp-2 leading-tight",children:r.title})]}),(0,t.jsx)(n.Wu,{className:"pt-0 flex-1",children:(0,t.jsx)(n.BT,{className:"text-sm leading-relaxed line-clamp-3",children:r.description})})]})})}},18994:(e,r,s)=>{"use strict";s.d(r,{N:()=>d});var t=s(95155),a=s(90786),n=s(59434),i=s(6874),o=s.n(i),l=s(35695);function d(){let e=(0,l.usePathname)(),{user:r}=(0,a.P)();return(0,t.jsx)("nav",{className:"flex items-center space-x-6",children:[{name:"Dashboard",href:"/user-dashboard"}].filter(r=>e!==r.href).map(r=>(0,t.jsxs)(o(),{href:r.href,className:(0,n.cn)("text-sm font-medium transition-colors relative",e===r.href?"text-[#166534] hover:text-[#166534]":"text-muted-foreground hover:text-[#166534]"),children:[r.name,e===r.href&&(0,t.jsx)("div",{className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full"})]},r.href))})}},26126:(e,r,s)=>{"use strict";s.d(r,{E:()=>l});var t=s(95155);s(12115);var a=s(99708),n=s(74466),i=s(59434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:s,asChild:n=!1,...l}=e,d=n?a.DX:"span";return(0,t.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:s}),r),...l})}},41752:(e,r,s)=>{"use strict";s.d(r,{B:()=>m});var t=s(95155),a=s(71007),n=s(381),i=s(34835),o=s(35695),l=s(99912),d=s(91950),c=s(44838),u=s(90786);function m(){let{user:e,signOut:r,firstName:s,email:m,fullName:h}=(0,u.P)(),x=(0,o.useRouter)();return e?(0,t.jsxs)(c.rI,{children:[(0,t.jsx)(c.ty,{asChild:!0,children:(0,t.jsx)("div",{children:(0,t.jsx)(l.V,{icon:a.A,text:s||"User",variant:"ghost",size:"md",layout:"horizontal",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:d.hS.md})})}),(0,t.jsxs)(c.SQ,{className:"w-56 bg-background border-border",align:"end",forceMount:!0,children:[(0,t.jsx)(c.lp,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none text-foreground",children:h||"User"}),(0,t.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:m||""})]})}),(0,t.jsx)(c.mB,{className:"bg-border"}),(0,t.jsxs)(c._2,{onClick:()=>{x.push("/profile")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,t.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Profile"})]}),(0,t.jsxs)(c._2,{onClick:()=>{x.push("/settings")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Settings"})]}),(0,t.jsx)(c.mB,{className:"bg-border"}),(0,t.jsxs)(c._2,{onClick:()=>{r()},className:"hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer",children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Log out"})]})]})]}):null}},56611:(e,r,s)=>{"use strict";s.d(r,{c:()=>x});var t=s(95155),a=s(75525),n=s(74783),i=s(6874),o=s.n(i),l=s(35695),d=s(12115),c=s(30285),u=s(38382),m=s(66681),h=s(59434);function x(){let[e,r]=(0,d.useState)(!1),s=(0,l.usePathname)(),{user:i}=(0,m.A)(),x=(null==i?void 0:i.role)==="admin"?[{name:"Admin",href:"/admin",icon:a.A}]:[{name:"Dashboard",href:"/user-dashboard",icon:void 0}];return(0,t.jsxs)(u.cj,{open:e,onOpenChange:r,children:[(0,t.jsx)(u.CG,{asChild:!0,children:(0,t.jsxs)(c.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors",children:[(0,t.jsx)(n.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,t.jsx)(u.h,{side:"left",className:"pr-0 bg-background border-r border-border",children:(0,t.jsx)("nav",{className:"flex flex-col space-y-3 mt-6",children:x.filter(e=>s!==e.href).map(e=>{let a="/admin"===e.href?s.startsWith("/admin"):s===e.href;return(0,t.jsxs)(o(),{href:e.href,onClick:()=>r(!1),className:(0,h.cn)("text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",a?"text-[#166534] bg-[#166534]/10":"text-muted-foreground hover:text-[#166534]"),children:[e.icon&&(0,t.jsx)(e.icon,{className:"h-4 w-4"}),e.name,a&&(0,t.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full"})]},e.href)})})})]})}},66695:(e,r,s)=>{"use strict";s.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var t=s(95155);s(12115);var a=s(59434);function n(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",r),...s})}function i(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...s})}function o(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...s})}function l(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...s})}function d(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...s})}function c(e){let{className:r,...s}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",r),...s})}},69074:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71469:(e,r,s)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var s in r)Object.defineProperty(e,s,{enumerable:!0,get:r[s]})}(r,{default:function(){return l},getImageProps:function(){return o}});let t=s(88229),a=s(38883),n=s(33063),i=t._(s(51193));function o(e){let{props:r}=(0,a.getImgProps)(e,{defaultLoader:i.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,s]of Object.entries(r))void 0===s&&delete r[e];return{props:r}}let l=n.Image},74783:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},79654:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,69243,23)),Promise.resolve().then(s.bind(s,18375)),Promise.resolve().then(s.bind(s,36996)),Promise.resolve().then(s.bind(s,92262))},92262:(e,r,s)=>{"use strict";s.d(r,{Header:()=>d});var t=s(95155);s(12115);var a=s(18994),n=s(56611),i=s(41752),o=s(66688),l=s(92236);function d(){return(0,t.jsx)("header",{className:"w-full sticky top-0 z-30 bg-background/80 backdrop-blur border-b border-border",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,t.jsx)(n.c,{})}),(0,t.jsx)(l.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,t.jsx)("div",{className:"hidden md:flex ml-6",children:(0,t.jsx)(a.N,{})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(o.U,{}),(0,t.jsx)(i.B,{})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,8862,1917,8128,5748,387,8441,8229,1684,7358],()=>r(79654)),_N_E=e.O()}]);