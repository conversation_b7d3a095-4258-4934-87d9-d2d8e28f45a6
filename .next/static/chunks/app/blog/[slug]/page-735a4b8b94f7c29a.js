(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5953],{18994:(e,s,r)=>{"use strict";r.d(s,{N:()=>c});var t=r(95155),n=r(90786),a=r(59434),i=r(6874),o=r.n(i),l=r(35695);function c(){let e=(0,l.usePathname)(),{user:s}=(0,n.P)();return(0,t.jsx)("nav",{className:"flex items-center space-x-6",children:[{name:"Dashboard",href:"/user-dashboard"}].filter(s=>e!==s.href).map(s=>(0,t.jsxs)(o(),{href:s.href,className:(0,a.cn)("text-sm font-medium transition-colors relative",e===s.href?"text-[#166534] hover:text-[#166534]":"text-muted-foreground hover:text-[#166534]"),children:[s.name,e===s.href&&(0,t.jsx)("div",{className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full"})]},s.href))})}},41752:(e,s,r)=>{"use strict";r.d(s,{B:()=>m});var t=r(95155),n=r(71007),a=r(381),i=r(34835),o=r(35695),l=r(99912),c=r(91950),d=r(44838),h=r(90786);function m(){let{user:e,signOut:s,firstName:r,email:m,fullName:x}=(0,h.P)(),u=(0,o.useRouter)();return e?(0,t.jsxs)(d.rI,{children:[(0,t.jsx)(d.ty,{asChild:!0,children:(0,t.jsx)("div",{children:(0,t.jsx)(l.V,{icon:n.A,text:r||"User",variant:"ghost",size:"md",layout:"horizontal",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:c.hS.md})})}),(0,t.jsxs)(d.SQ,{className:"w-56 bg-background border-border",align:"end",forceMount:!0,children:[(0,t.jsx)(d.lp,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none text-foreground",children:x||"User"}),(0,t.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:m||""})]})}),(0,t.jsx)(d.mB,{className:"bg-border"}),(0,t.jsxs)(d._2,{onClick:()=>{u.push("/profile")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Profile"})]}),(0,t.jsxs)(d._2,{onClick:()=>{u.push("/settings")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,t.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Settings"})]}),(0,t.jsx)(d.mB,{className:"bg-border"}),(0,t.jsxs)(d._2,{onClick:()=>{s()},className:"hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer",children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Log out"})]})]})]}):null}},48570:(e,s,r)=>{Promise.resolve().then(r.t.bind(r,6874,23)),Promise.resolve().then(r.t.bind(r,33063,23)),Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.bind(r,36996)),Promise.resolve().then(r.bind(r,92262))},56611:(e,s,r)=>{"use strict";r.d(s,{c:()=>u});var t=r(95155),n=r(75525),a=r(74783),i=r(6874),o=r.n(i),l=r(35695),c=r(12115),d=r(30285),h=r(38382),m=r(66681),x=r(59434);function u(){let[e,s]=(0,c.useState)(!1),r=(0,l.usePathname)(),{user:i}=(0,m.A)(),u=(null==i?void 0:i.role)==="admin"?[{name:"Admin",href:"/admin",icon:n.A}]:[{name:"Dashboard",href:"/user-dashboard",icon:void 0}];return(0,t.jsxs)(h.cj,{open:e,onOpenChange:s,children:[(0,t.jsx)(h.CG,{asChild:!0,children:(0,t.jsxs)(d.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors",children:[(0,t.jsx)(a.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,t.jsx)(h.h,{side:"left",className:"pr-0 bg-background border-r border-border",children:(0,t.jsx)("nav",{className:"flex flex-col space-y-3 mt-6",children:u.filter(e=>r!==e.href).map(e=>{let n="/admin"===e.href?r.startsWith("/admin"):r===e.href;return(0,t.jsxs)(o(),{href:e.href,onClick:()=>s(!1),className:(0,x.cn)("text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",n?"text-[#166534] bg-[#166534]/10":"text-muted-foreground hover:text-[#166534]"),children:[e.icon&&(0,t.jsx)(e.icon,{className:"h-4 w-4"}),e.name,n&&(0,t.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full"})]},e.href)})})})]})}},74783:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},92262:(e,s,r)=>{"use strict";r.d(s,{Header:()=>c});var t=r(95155);r(12115);var n=r(18994),a=r(56611),i=r(41752),o=r(66688),l=r(92236);function c(){return(0,t.jsx)("header",{className:"w-full sticky top-0 z-30 bg-background/80 backdrop-blur border-b border-border",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,t.jsx)(a.c,{})}),(0,t.jsx)(l.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,t.jsx)("div",{className:"hidden md:flex ml-6",children:(0,t.jsx)(n.N,{})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(o.U,{}),(0,t.jsx)(i.B,{})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,8862,1917,8128,5748,387,8441,8229,1684,7358],()=>s(48570)),_N_E=e.O()}]);