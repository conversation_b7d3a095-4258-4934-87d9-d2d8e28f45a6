(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{2643:(e,t,r)=>{"use strict";r.d(t,{ClerkSessionProvider:()=>c});var s=r(95155),a=r(48879),o=r(27016),i=r(12115),n=r(88128),l=r(35695),u=r(6653);function c(e){let{children:t}=e,{user:r,isLoaded:c}=(0,a.Jd)(),{isSignedIn:d,getToken:g}=(0,o.d)(),{actions:m,isAuthenticated:p}=(0,n.B)(),{identifyUser:f,setUserProperties:b}=(0,u.s)();return(0,l.useRouter)(),(0,i.useEffect)(()=>{(async()=>{if(c)if(d&&r){if(!p)try{var e,t,s,a,o,i;let l=await g();if(console.group("\uD83D\uDD10 Clerk Authentication Success"),console.log("\uD83D\uDC64 User:",null==(e=r.emailAddresses[0])?void 0:e.emailAddress),console.log("\uD83C\uDFAB Token:",l),console.log("\uD83D\uDCCF Token Length:",null==l?void 0:l.length),console.log("\uD83C\uDD94 User ID:",r.id),l)try{let e=JSON.parse(atob(l.split(".")[1]));console.log("⏰ Token Expires:",new Date(1e3*e.exp)),console.log("\uD83C\uDFF7️ Token Subject:",e.sub)}catch(e){console.log("Could not decode token payload")}console.groupEnd();let u={id:r.id,email:(null==(t=r.emailAddresses[0])?void 0:t.emailAddress)||"",firstName:r.firstName||"",lastName:r.lastName||"",name:"".concat(r.firstName||""," ").concat(r.lastName||"").trim(),avatarUrl:r.imageUrl||"",isEmailVerified:(null==(a=r.emailAddresses[0])||null==(s=a.verification)?void 0:s.status)==="verified",role:"user",status:"active",bio:"",timezone:"UTC",preferences:{notifications:!0,theme:"system",language:"en"},createdAt:(null==(o=r.createdAt)?void 0:o.toISOString())||new Date().toISOString(),updatedAt:(null==(i=r.updatedAt)?void 0:i.toISOString())||new Date().toISOString()},c={accessToken:l||"",refreshToken:"",expiresAt:Date.now()+36e5};m.setTokens(c),m.updateUser(u),n.B.setState({isAuthenticated:!0,user:u,tokens:c,isInitialized:!0}),f(r.id,{email:u.email,name:u.name,firstName:u.firstName,lastName:u.lastName,isEmailVerified:u.isEmailVerified,createdAt:u.createdAt,clerk_user_id:r.id})}catch(e){console.error("Error syncing Clerk user with session:",e)}}else p&&m.logout()})()},[d,r,c,p,m,g]),(0,i.useEffect)(()=>{let e=async()=>{if(d&&p)try{let e=await g();if(e){let t={accessToken:e,refreshToken:"",expiresAt:Date.now()+36e5};m.setTokens(t)}}catch(e){console.error("Error updating token:",e)}},t=setInterval(e,18e5);return e(),()=>clearInterval(t)},[d,p,g,m]),(0,s.jsx)(s.Fragment,{children:t})}},6653:(e,t,r)=>{"use strict";r.d(t,{s:()=>o});var s=r(12115),a=r(83731);function o(){let e=(0,a.sf)(),t=(0,s.useCallback)((t,r)=>{null==e||e.capture("$pageview",{$current_url:t,page_title:r})},[e]),r=(0,s.useCallback)((t,r)=>{null==e||e.capture("button_clicked",{element_name:t,location:r})},[e]),o=(0,s.useCallback)(function(t){let r=!(arguments.length>1)||void 0===arguments[1]||arguments[1];null==e||e.capture("form_submitted",{form_name:t,success:r})},[e]),i=(0,s.useCallback)((t,r)=>{null==e||e.capture("search_performed",{search_term:t,results_count:r})},[e]),n=(0,s.useCallback)(t=>{null==e||e.capture("user_signed_up",{method:t}),null==e||e.identify()},[e]),l=(0,s.useCallback)(t=>{null==e||e.capture("user_logged_in",{method:t})},[e]),u=(0,s.useCallback)(t=>{null==e||e.capture("project_created",{project_type:t})},[e]),c=(0,s.useCallback)((t,r)=>{null==e||e.capture("feature_used",{feature_name:t,context:r})},[e]),d=(0,s.useCallback)(function(t,r){let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];null==e||e.capture("error_occurred",{error_message:t,context:r,fatal:s})},[e]),g=(0,s.useCallback)((t,r,s)=>{null==e||e.capture("timing_measured",{timing_name:t,timing_value:r,category:s||"performance"})},[e]),m=(0,s.useCallback)((t,r)=>{null==e||e.capture(t,r)},[e]);return{trackPageView:t,trackClick:r,trackFormSubmit:o,trackSearch:i,trackSignUp:n,trackLogin:l,trackProjectCreated:u,trackFeatureUsed:c,trackError:d,trackTiming:g,trackCustomEvent:m,identifyUser:(0,s.useCallback)((t,r)=>{null==e||e.identify(t,r)},[e]),setUserProperties:(0,s.useCallback)(t=>{null==e||e.setPersonProperties(t)},[e])}}},8154:(e,t,r)=>{"use strict";r.d(t,{PHProvider:()=>u});var s=r(95155),a=r(72341),o=r(83731),i=r(12115),n=r(35695);function l(){let e=(0,n.usePathname)(),t=(0,n.useSearchParams)();return(0,i.useEffect)(()=>{if(e){let r=window.origin+e;t.toString()&&(r+="?".concat(t.toString())),a.Ay.capture("$pageview",{$current_url:r})}},[e,t]),null}function u(e){let{children:t}=e;return(0,s.jsxs)(o.so,{client:a.Ay,children:[(0,s.jsx)(i.Suspense,{fallback:null,children:(0,s.jsx)(l,{})}),t]})}a.Ay.init("phc_2oQSSzXgGcCmTZ0YigOv7yq9RfH49WlVktnZ6HC2vQA",{api_host:"https://us.i.posthog.com",person_profiles:"identified_only",capture_pageview:!1,capture_pageleave:!0,loaded:e=>{},debug:!1})},13432:(e,t,r)=>{"use strict";r.d(t,{WG:()=>o,lH:()=>a,qQ:()=>s});let s=new(r(87017)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<3,refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,onError:e=>{console.error("Mutation error:",e)}}}}),a={user:e=>["user",e],userProfile:e=>["user","profile",e],projects:()=>["projects"],project:e=>["projects",e],projectTasks:e=>["projects",e,"tasks"],admin:{analytics:()=>["admin","analytics"],summary:()=>["admin","analytics","summary"],users:e=>["admin","users",e],feedback:e=>["admin","feedback",e]},businessSections:e=>["business-sections",e]},o={user:e=>{s.invalidateQueries({queryKey:a.user(e)}),s.invalidateQueries({queryKey:a.userProfile(e)})},projects:()=>{s.invalidateQueries({queryKey:a.projects()})},project:e=>{s.invalidateQueries({queryKey:a.project(e)}),s.invalidateQueries({queryKey:a.projectTasks(e)})},admin:()=>{s.invalidateQueries({queryKey:a.admin.analytics()}),s.invalidateQueries({queryKey:a.admin.summary()}),s.invalidateQueries({queryKey:a.admin.users()}),s.invalidateQueries({queryKey:a.admin.feedback()})}}},19412:(e,t,r)=>{"use strict";r.d(t,{QueryProvider:()=>i});var s=r(95155),a=r(26715),o=r(13432);function i(e){let{children:t}=e;return(0,s.jsxs)(a.Ht,{client:o.qQ,children:[t,!1]})}},20210:(e,t,r)=>{"use strict";r.d(t,{BackgroundProvider:()=>n});var s=r(95155),a=r(12115);let o={enabled:!0},i=(0,a.createContext)(void 0);function n(e){let{children:t}=e,[r,n]=(0,a.useState)(o);return(0,a.useEffect)(()=>{let e=localStorage.getItem("background-settings");if(e)try{let t=JSON.parse(e);n({...o,...t})}catch(e){console.error("Failed to parse background settings:",e)}},[]),(0,a.useEffect)(()=>{localStorage.setItem("background-settings",JSON.stringify(r))},[r]),(0,s.jsx)(i.Provider,{value:{settings:r,updateSettings:e=>{n(t=>({...t,...e}))},resetSettings:()=>{n(o)}},children:t})}},30347:()=>{},44450:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>u});var s=r(95155),a=r(12115);let o=e=>{let{message:t="Loading...",size:r="md",fullScreen:a=!0}=e,o={sm:{circle:"w-12 h-12",text:"text-sm",spacing:"space-y-3"},md:{circle:"w-16 h-16",text:"text-base",spacing:"space-y-4"},lg:{circle:"w-16 h-16",text:"text-lg",spacing:"space-y-6"}}[r];return(0,s.jsx)("div",{className:a?"fixed inset-0 z-50 flex items-center justify-center":"flex items-center justify-center w-full h-full",children:(0,s.jsxs)("div",{className:"text-center ".concat(o.spacing),children:[(0,s.jsx)("div",{className:"relative flex items-center justify-center",children:(0,s.jsx)("div",{className:"".concat(o.circle," border-4 border-gray-200 border-t-green-500 rounded-full animate-spin")})}),t&&(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 font-medium ".concat(o.text),children:t})]})})},i=e=>{let{message:t}=e;return(0,s.jsx)(o,{message:t,size:"md",fullScreen:!0})};var n=r(88128),l=r(35695);function u(e){let{children:t}=e,{actions:r,isInitialized:o,isLoading:u}=(0,n.B)(),c=(0,l.usePathname)(),d=null==c?void 0:c.startsWith("/auth/");return((0,a.useEffect)(()=>{let e=async()=>{try{await r.initializeSession()}catch(e){console.error("Session initialization failed:",e),setTimeout(()=>{n.B.setState({isInitialized:!0,isLoading:!1})},100)}};if(d)o||n.B.setState({isInitialized:!0,isLoading:!1}),e();else{e();let t=setTimeout(()=>{o||(console.warn("Session initialization timeout, forcing completion"),n.B.setState({isInitialized:!0,isLoading:!1}))},5e3);return()=>clearTimeout(t)}},[r,o,d]),o&&!u||d)?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)(i,{message:"Initializing..."})}},47332:(e,t,r)=>{"use strict";r.d(t,{y:()=>a});var s=r(34477);let a=(0,s.createServerReference)("7f294d927378fcfaafe4b72d3c48c966515621b8b4",s.callServer,void 0,s.findSourceMapURL,"invalidateCacheAction")},61483:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var s=r(95155),a=r(51362);function o(e){let{children:t,...r}=e;return(0,s.jsx)(a.N,{...r,children:t})}r(12115)},72929:(e,t,r)=>{Promise.resolve().then(r.bind(r,83398)),Promise.resolve().then(r.bind(r,636)),Promise.resolve().then(r.bind(r,671)),Promise.resolve().then(r.bind(r,15256)),Promise.resolve().then(r.bind(r,27016)),Promise.resolve().then(r.bind(r,61657)),Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.t.bind(r,30347,23)),Promise.resolve().then(r.t.bind(r,66096,23)),Promise.resolve().then(r.t.bind(r,94255,23)),Promise.resolve().then(r.bind(r,8154)),Promise.resolve().then(r.bind(r,2643)),Promise.resolve().then(r.bind(r,19412)),Promise.resolve().then(r.bind(r,44450)),Promise.resolve().then(r.bind(r,61483)),Promise.resolve().then(r.bind(r,89074)),Promise.resolve().then(r.bind(r,20210))},89074:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>i});var s=r(95155),a=r(51362),o=r(56671);let i=e=>{let{...t}=e,{theme:r="system"}=(0,a.D)();return(0,s.jsx)(o.l$,{theme:r,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",error:"!group-[.toaster]:bg-red-50 !group-[.toaster]:text-red-900 !group-[.toaster]:border-red-200 dark:!group-[.toaster]:bg-red-950 dark:!group-[.toaster]:text-red-100 dark:!group-[.toaster]:border-red-800",success:"!group-[.toaster]:bg-green-50 !group-[.toaster]:text-green-900 !group-[.toaster]:border-green-200 dark:!group-[.toaster]:bg-green-950 dark:!group-[.toaster]:text-green-100 dark:!group-[.toaster]:border-green-800",warning:"!group-[.toaster]:bg-yellow-50 !group-[.toaster]:text-yellow-900 !group-[.toaster]:border-yellow-200 dark:!group-[.toaster]:bg-yellow-950 dark:!group-[.toaster]:text-yellow-100 dark:!group-[.toaster]:border-yellow-800",info:"!group-[.toaster]:bg-blue-50 !group-[.toaster]:text-blue-900 !group-[.toaster]:border-blue-200 dark:!group-[.toaster]:bg-blue-950 dark:!group-[.toaster]:text-blue-100 dark:!group-[.toaster]:border-blue-800"}},...t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7690,354,6671,8879,5123,5479,1917,8128,8441,8229,1684,7358],()=>t(72929)),_N_E=e.O()}]);