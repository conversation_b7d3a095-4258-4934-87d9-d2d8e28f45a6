(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4662],{18994:(e,s,r)=>{"use strict";r.d(s,{N:()=>o});var t=r(95155),a=r(90786),n=r(59434),i=r(6874),c=r.n(i),d=r(35695);function o(){let e=(0,d.usePathname)(),{user:s}=(0,a.P)();return(0,t.jsx)("nav",{className:"flex items-center space-x-6",children:[{name:"Dashboard",href:"/user-dashboard"}].filter(s=>e!==s.href).map(s=>(0,t.jsxs)(c(),{href:s.href,className:(0,n.cn)("text-sm font-medium transition-colors relative",e===s.href?"text-[#166534] hover:text-[#166534]":"text-muted-foreground hover:text-[#166534]"),children:[s.name,e===s.href&&(0,t.jsx)("div",{className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full"})]},s.href))})}},22346:(e,s,r)=>{"use strict";r.d(s,{w:()=>i});var t=r(95155);r(12115);var a=r(87489),n=r(59434);function i(e){let{className:s,orientation:r="horizontal",decorative:i=!0,...c}=e;return(0,t.jsx)(a.b,{"data-slot":"separator",decorative:i,orientation:r,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...c})}},22347:(e,s,r)=>{Promise.resolve().then(r.bind(r,72608))},26126:(e,s,r)=>{"use strict";r.d(s,{E:()=>d});var t=r(95155);r(12115);var a=r(99708),n=r(74466),i=r(59434);let c=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:r,asChild:n=!1,...d}=e,o=n?a.DX:"span";return(0,t.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(c({variant:r}),s),...d})}},41752:(e,s,r)=>{"use strict";r.d(s,{B:()=>x});var t=r(95155),a=r(71007),n=r(381),i=r(34835),c=r(35695),d=r(99912),o=r(91950),l=r(44838),m=r(90786);function x(){let{user:e,signOut:s,firstName:r,email:x,fullName:u}=(0,m.P)(),h=(0,c.useRouter)();return e?(0,t.jsxs)(l.rI,{children:[(0,t.jsx)(l.ty,{asChild:!0,children:(0,t.jsx)("div",{children:(0,t.jsx)(d.V,{icon:a.A,text:r||"User",variant:"ghost",size:"md",layout:"horizontal",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:o.hS.md})})}),(0,t.jsxs)(l.SQ,{className:"w-56 bg-background border-border",align:"end",forceMount:!0,children:[(0,t.jsx)(l.lp,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none text-foreground",children:u||"User"}),(0,t.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:x||""})]})}),(0,t.jsx)(l.mB,{className:"bg-border"}),(0,t.jsxs)(l._2,{onClick:()=>{h.push("/profile")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,t.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Profile"})]}),(0,t.jsxs)(l._2,{onClick:()=>{h.push("/settings")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,t.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Settings"})]}),(0,t.jsx)(l.mB,{className:"bg-border"}),(0,t.jsxs)(l._2,{onClick:()=>{s()},className:"hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer",children:[(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Log out"})]})]})]}):null}},47262:(e,s,r)=>{"use strict";r.d(s,{S:()=>c});var t=r(95155);r(12115);var a=r(76981),n=r(5196),i=r(59434);function c(e){let{className:s,...r}=e;return(0,t.jsx)(a.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[2px] border-2 transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...r,children:(0,t.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(n.A,{className:"size-3.5 stroke-[3]"})})})}},56611:(e,s,r)=>{"use strict";r.d(s,{c:()=>h});var t=r(95155),a=r(75525),n=r(74783),i=r(6874),c=r.n(i),d=r(35695),o=r(12115),l=r(30285),m=r(38382),x=r(66681),u=r(59434);function h(){let[e,s]=(0,o.useState)(!1),r=(0,d.usePathname)(),{user:i}=(0,x.A)(),h=(null==i?void 0:i.role)==="admin"?[{name:"Admin",href:"/admin",icon:a.A}]:[{name:"Dashboard",href:"/user-dashboard",icon:void 0}];return(0,t.jsxs)(m.cj,{open:e,onOpenChange:s,children:[(0,t.jsx)(m.CG,{asChild:!0,children:(0,t.jsxs)(l.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors",children:[(0,t.jsx)(n.A,{className:"h-6 w-6"}),(0,t.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,t.jsx)(m.h,{side:"left",className:"pr-0 bg-background border-r border-border",children:(0,t.jsx)("nav",{className:"flex flex-col space-y-3 mt-6",children:h.filter(e=>r!==e.href).map(e=>{let a="/admin"===e.href?r.startsWith("/admin"):r===e.href;return(0,t.jsxs)(c(),{href:e.href,onClick:()=>s(!1),className:(0,u.cn)("text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",a?"text-[#166534] bg-[#166534]/10":"text-muted-foreground hover:text-[#166534]"),children:[e.icon&&(0,t.jsx)(e.icon,{className:"h-4 w-4"}),e.name,a&&(0,t.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full"})]},e.href)})})})]})}},62523:(e,s,r)=>{"use strict";r.d(s,{p:()=>n});var t=r(95155);r(12115);var a=r(59434);function n(e){let{className:s,type:r,...n}=e;return(0,t.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...n})}},66695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>i,wL:()=>l});var t=r(95155);r(12115);var a=r(59434);function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",s),...r})}function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function c(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function d(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function o(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",s),...r})}},72608:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>z,dynamic:()=>P});var t=r(95155),a=r(63560),n=r(23861),i=r(51154),c=r(4229),d=r(75525),o=r(40646),l=r(78749),m=r(92657),x=r(34869),u=r(85339),h=r(12115),f=r(62177),g=r(70927),p=r(86473),b=r(26126),j=r(30285),v=r(66695),N=r(47262),w=r(62523),y=r(85057),k=r(22346),A=r(66681);let P="force-dynamic",S=g.Ik({emailNotifications:g.zM(),projectUpdates:g.zM(),securityAlerts:g.zM(),marketingEmails:g.zM()}),C=g.Ik({currentPassword:g.Yj().min(1,"Current password is required"),newPassword:g.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:g.Yj().min(1,"Please confirm your password")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),E=e=>{try{return(e instanceof Date?e:new Date(e)).toLocaleDateString()}catch(e){return"Invalid date"}};function z(){let{user:e}=(0,A.A)(),[s,r]=(0,h.useState)(!1),[g,P]=(0,h.useState)(!1),[z,B]=(0,h.useState)(null),{register:F,handleSubmit:J,formState:{isDirty:U},reset:_}=(0,f.mN)({resolver:(0,a.u)(S),defaultValues:{emailNotifications:!0,projectUpdates:!0,securityAlerts:!0,marketingEmails:!1}}),{register:D,handleSubmit:M,formState:{errors:T},reset:R}=(0,f.mN)({resolver:(0,a.u)(C)}),Z=async e=>{r(!0),B(null);try{await new Promise(e=>setTimeout(e,1e3)),B({type:"success",text:"Notification settings updated successfully!"}),_(e)}catch(e){B({type:"error",text:e.message||"Failed to update notification settings"})}finally{r(!1)}},I=async e=>{r(!0),B(null);try{await new Promise(e=>setTimeout(e,1e3)),B({type:"success",text:"Password updated successfully!"}),R()}catch(e){B({type:"error",text:e.message||"Failed to update password"})}finally{r(!1)}};return e?(0,t.jsx)(p.N,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Settings"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences."})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(v.Zp,{className:"bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,t.jsxs)(v.aR,{children:[(0,t.jsxs)(v.ZB,{className:"flex items-center space-x-2 text-card-foreground",children:[(0,t.jsx)(n.A,{className:"h-5 w-5 text-accent"}),(0,t.jsx)("span",{children:"Notification Settings"})]}),(0,t.jsx)(v.BT,{className:"text-muted-foreground",children:"Configure how you receive notifications and updates."})]}),(0,t.jsx)(v.Wu,{children:(0,t.jsxs)("form",{onSubmit:J(Z),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.S,{id:"emailNotifications",...F("emailNotifications"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,t.jsx)(y.J,{htmlFor:"emailNotifications",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Email Notifications"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Receive important updates and notifications via email."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.S,{id:"projectUpdates",...F("projectUpdates"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,t.jsx)(y.J,{htmlFor:"projectUpdates",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Project Updates"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Get notified when projects you're working on are updated."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.S,{id:"securityAlerts",...F("securityAlerts"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,t.jsx)(y.J,{htmlFor:"securityAlerts",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Security Alerts"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Receive notifications about security-related activities on your account."})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(N.S,{id:"marketingEmails",...F("marketingEmails"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,t.jsx)(y.J,{htmlFor:"marketingEmails",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Marketing Emails"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Receive promotional emails and newsletters about new features."})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(j.$,{type:"submit",disabled:s||!U,className:"bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border",children:[s&&(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Save Notification Settings"]})})]})})]}),(0,t.jsxs)(v.Zp,{className:"bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,t.jsxs)(v.aR,{children:[(0,t.jsxs)(v.ZB,{className:"flex items-center space-x-2 text-card-foreground",children:[(0,t.jsx)(d.A,{className:"h-5 w-5 text-accent"}),(0,t.jsx)("span",{children:"Security Settings"})]}),(0,t.jsx)(v.BT,{className:"text-muted-foreground",children:"Manage your account security and password."})]}),(0,t.jsx)(v.Wu,{children:(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-card-foreground",children:"Account Status"}),(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 text-green-500"}),(0,t.jsx)("span",{className:"text-sm text-card-foreground",children:"Email Verified"}),(0,t.jsx)(b.E,{variant:"outline",className:"text-xs border-green-500/20 text-green-600 bg-green-500/10",children:"Verified"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 text-blue-500"}),(0,t.jsx)("span",{className:"text-sm text-card-foreground",children:"Two-Factor Auth"}),(0,t.jsx)(b.E,{variant:"outline",className:"text-xs border-yellow-500/20 text-yellow-600 bg-yellow-500/10",children:"Disabled"})]})]})]}),(0,t.jsx)(k.w,{className:"bg-border"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-lg font-medium text-card-foreground",children:"Change Password"}),(0,t.jsxs)("form",{onSubmit:M(I),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(y.J,{htmlFor:"currentPassword",className:"text-card-foreground",children:"Current Password"}),(0,t.jsx)(w.p,{id:"currentPassword",type:"password",...D("currentPassword"),placeholder:"Enter your current password",className:"border-border focus:border-accent focus:ring-accent/20"}),T.currentPassword&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:T.currentPassword.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(y.J,{htmlFor:"newPassword",className:"text-card-foreground",children:"New Password"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w.p,{id:"newPassword",type:g?"text":"password",...D("newPassword"),placeholder:"Enter your new password",className:"border-border focus:border-accent focus:ring-accent/20"}),(0,t.jsx)(j.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-accent/10 hover:text-accent",onClick:()=>P(!g),children:g?(0,t.jsx)(l.A,{className:"h-4 w-4"}):(0,t.jsx)(m.A,{className:"h-4 w-4"})})]}),T.newPassword&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:T.newPassword.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(y.J,{htmlFor:"confirmPassword",className:"text-card-foreground",children:"Confirm New Password"}),(0,t.jsx)(w.p,{id:"confirmPassword",type:"password",...D("confirmPassword"),placeholder:"Confirm your new password",className:"border-border focus:border-accent focus:ring-accent/20"}),T.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-destructive",children:T.confirmPassword.message})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(j.$,{type:"submit",disabled:s,className:"bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border",children:[s&&(0,t.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,t.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Update Password"]})})]})]})]})})]}),(0,t.jsxs)(v.Zp,{className:"bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,t.jsxs)(v.aR,{children:[(0,t.jsxs)(v.ZB,{className:"flex items-center space-x-2 text-card-foreground",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-accent"}),(0,t.jsx)("span",{children:"Account Information"})]}),(0,t.jsx)(v.BT,{className:"text-muted-foreground",children:"Your account details and preferences."})]}),(0,t.jsx)(v.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium text-card-foreground",children:"Email Address"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium text-card-foreground",children:"Account Created"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:E(e.createdAt)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium text-card-foreground",children:"Last Updated"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:E(e.updatedAt)})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y.J,{className:"text-sm font-medium text-card-foreground",children:"Account Status"}),(0,t.jsx)(b.E,{variant:"outline",className:"text-xs border-green-500/20 text-green-600 bg-green-500/10",children:"Active"})]})]})})})]})]}),z&&(0,t.jsx)("div",{className:"p-4 rounded-md border ".concat("success"===z.type?"bg-green-500/10 text-green-700 border-green-500/20":"bg-red-500/10 text-red-700 border-red-500/20"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:["success"===z.type?(0,t.jsx)(o.A,{className:"h-4 w-4"}):(0,t.jsx)(u.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:z.text})]})})]})}):(0,t.jsx)(p.N,{children:(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-destructive mb-2",children:"Access Denied"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"You need to be signed in to view this page."})]})})})}},85057:(e,s,r)=>{"use strict";r.d(s,{J:()=>i});var t=r(95155);r(12115);var a=r(40968),n=r(59434);function i(e){let{className:s,...r}=e;return(0,t.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},86473:(e,s,r)=>{"use strict";r.d(s,{N:()=>c});var t=r(95155),a=r(36996),n=r(92262);function i(e){let{children:s,showHeader:r=!0,showFooter:i=!0,constrainHeight:c=!1}=e;return(0,t.jsxs)("div",{className:"".concat(c?"h-screen":"min-h-screen"," flex flex-col bg-background"),children:[r&&(0,t.jsx)(n.Header,{}),(0,t.jsx)("main",{className:"flex-1 ".concat(c?"min-h-0":""),children:s}),i&&(0,t.jsx)(a.Footer,{})]})}function c(e){let{children:s}=e;return(0,t.jsx)(i,{showFooter:!1,constrainHeight:!0,children:(0,t.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:s})})}},92262:(e,s,r)=>{"use strict";r.d(s,{Header:()=>o});var t=r(95155);r(12115);var a=r(18994),n=r(56611),i=r(41752),c=r(66688),d=r(92236);function o(){return(0,t.jsx)("header",{className:"w-full sticky top-0 z-30 bg-background/80 backdrop-blur border-b border-border",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,t.jsx)(n.c,{})}),(0,t.jsx)(d.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,t.jsx)("div",{className:"hidden md:flex ml-6",children:(0,t.jsx)(a.N,{})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(c.U,{}),(0,t.jsx)(i.B,{})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[7598,6874,6671,8879,6754,7343,4040,8798,4043,2417,1917,8128,5748,387,8441,8229,1684,7358],()=>s(22347)),_N_E=e.O()}]);