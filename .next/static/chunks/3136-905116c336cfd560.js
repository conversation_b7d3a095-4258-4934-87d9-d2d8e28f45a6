"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3136],{14186:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},16785:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17580:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},33109:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},53311:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(19946).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},71539:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},83731:(e,t,o)=>{o.d(t,{sf:()=>c,so:()=>i});var n=o(72341),r=o(12115),a=(0,r.createContext)({client:n.Ay});function i(e){var t=e.children,o=e.client,i=e.apiKey,c=e.options,l=(0,r.useRef)(null),s=(0,r.useMemo)(function(){return o?(i&&console.warn("[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `apiKey` will be ignored in favour of `client`."),c&&console.warn("[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`."),o):(i||console.warn("[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior."),n.Ay)},[o,i,JSON.stringify(c)]);return(0,r.useEffect)(function(){if(!o){var e=l.current;e?(i!==e.apiKey&&console.warn("[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop."),c&&!function e(t,o,n){if(void 0===n&&(n=new WeakMap),t===o)return!0;if("object"!=typeof t||null===t||"object"!=typeof o||null===o)return!1;if(n.has(t)&&n.get(t)===o)return!0;n.set(t,o);var r=Object.keys(t),a=Object.keys(o);if(r.length!==a.length)return!1;for(var i=0;i<r.length;i++){var c=r[i];if(!a.includes(c)||!e(t[c],o[c],n))return!1}return!0}(c,e.options)&&n.Ay.set_config(c)):(n.Ay.__loaded&&console.warn("[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues."),n.Ay.init(i,c)),l.current={apiKey:i,options:null!=c?c:{}}}},[o,i,JSON.stringify(c)]),r.createElement(a.Provider,{value:{client:s}},t)}var c=function(){return(0,r.useContext)(a).client},l=function(e,t){return(l=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])})(e,t)},s=function(){return(s=Object.assign||function(e){for(var t,o=1,n=arguments.length;o<n;o++)for(var r in t=arguments[o])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e}).apply(this,arguments)};function p(e,t){var o={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(o[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,n=Object.getOwnPropertySymbols(e);r<n.length;r++)0>t.indexOf(n[r])&&Object.prototype.propertyIsEnumerable.call(e,n[r])&&(o[n[r]]=e[n[r]]);return o}"function"==typeof SuppressedError&&SuppressedError;var u=function(e){return"function"==typeof e};function y(e){var t=e.flag,o=e.children,n=e.onIntersect,r=e.onClick,a=e.trackView,i=e.options,l=p(e,["flag","children","onIntersect","onClick","trackView","options"]),u=useRef(null);return useEffect(function(){if(null!==u.current&&a){var e=new IntersectionObserver(function(e){return n(e[0])},s({threshold:.1},i));return e.observe(u.current),function(){return e.disconnect()}}},[t,i,c(),u,a,n]),React.createElement("div",s({ref:u},l,{onClick:r}),o)}var d={componentStack:null,error:null},f={INVALID_FALLBACK:"[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element."};!function(e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=o}function o(t){var o=e.call(this,t)||this;return o.state=d,o}l(o,e),o.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),o.prototype.componentDidCatch=function(e,t){var o,n=t.componentStack,r=this.props.additionalProperties;this.setState({error:e,componentStack:n}),u(r)?o=r(e):"object"==typeof r&&(o=r),this.context.client.captureException(e,o)},o.prototype.render=function(){var e=this.props,t=e.children,o=e.fallback,n=this.state;if(null==n.componentStack)return u(t)?t():t;var a=u(o)?r.createElement(o,{error:n.error,componentStack:n.componentStack}):o;return r.isValidElement(a)?a:(console.warn(f.INVALID_FALLBACK),r.createElement(r.Fragment,null))},o.contextType=a}(r.Component)},92138:(e,t,o)=>{o.d(t,{A:()=>n});let n=(0,o(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}}]);