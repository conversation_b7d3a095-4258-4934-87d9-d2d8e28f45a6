"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7547],{3401:(e,t,r)=>{r.d(t,{E:()=>o});var n=r(12115),a=r(46641),l=r(82396),i=["axis","item"],o=(0,n.forwardRef)((e,t)=>n.createElement(l.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:i,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:t}))},18357:(e,t,r)=>{r.d(t,{F:()=>eM,L:()=>eA});var n=r(12115),a=r(95672),l=r.n(a),i=r(52596),o=r(68924),c=r(60356),u=r(58573),s=r(39827),d=r(14299),p=r(97238),f=r(66038),y=r(12287),m=r(18478),g=e=>e.graphicalItems.polarItems,v=(0,o.Mz)([f.N,y.E],d.eo),h=(0,o.Mz)([g,d.DP,v],d.ec),b=(0,o.Mz)([h],d.rj),A=(0,o.Mz)([b,c.z3],d.Nk),E=(0,o.Mz)([A,d.DP,h],d.fb),O=(0,o.Mz)([A,d.DP,h],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),P=()=>void 0,k=(0,o.Mz)([d.DP,d.AV,P,O,P,p.fz,f.N],d.wL),x=(0,o.Mz)([d.DP,p.fz,A,E,m.eC,f.N,k],d.tP),M=(0,o.Mz)([x,d.DP,d.xM],d.xp);function j(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?j(Object(r),!0).forEach(function(t){var n,a,l;n=e,a=t,l=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):n[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,o.Mz)([d.DP,x,M,f.N],d.g1);var S=(0,o.Mz)([g,(e,t)=>t],(e,t)=>e.filter(e=>"pie"===e.type).find(e=>e.id===t)),R=[],z=(e,t,r)=>(null==r?void 0:r.length)===0?R:r,N=(0,o.Mz)([c.z3,S,z],(e,t,r)=>{var n,{chartData:a}=e;if(null!=t&&((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:a)&&n.length||null==r||(n=r.map(e=>w(w({},t.presentationProps),e.props))),null!=n))return n}),D=(0,o.Mz)([N,S,z],(e,t,r)=>{if(null!=e&&null!=t)return e.map((e,n)=>{var a,l,i=(0,s.kr)(e,t.nameKey,t.name);return l=null!=r&&null!=(a=r[n])&&null!=(a=a.props)&&a.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.uM)(i,t.dataKey),color:l,payload:e,type:t.legendType}})}),T=(0,o.Mz)([N,S,z,u.HZ],(e,t,r,n)=>{if(null!=t&&null!=e)return eA({offset:n,pieSettings:t,displayedData:e,cells:r})}),C=r(81971),I=r(2348),K=r(70688),L=r(79095),F=r(54811),G=r(70788),q=r(41643),B=r(25641),V=r(16377),W=r(43597),_=r(48605),H=r(99129),J=r(56091),Z=r(20215),$=r(79020),U=r(84421),X=r(39426),Y=r(93389),Q=r(25838),ee=r(27119),et=r(37713),er=r(38500),en=["onMouseEnter","onClick","onMouseLeave"],ea=["id"],el=["id"];function ei(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(n=0;n<l.length;n++)r=l[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function eo(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ec(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eo(Object(r),!0).forEach(function(t){var n,a,l;n=e,a=t,l=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in n?Object.defineProperty(n,a,{value:l,enumerable:!0,configurable:!0,writable:!0}):n[a]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eo(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eu(){return(eu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function es(e){var t=(0,n.useMemo)(()=>(0,G.aS)(e.children,F.f),[e.children]),r=(0,C.G)(r=>D(r,e.id,t));return null==r?null:n.createElement($._,{legendPayload:r})}function ed(e){var{dataKey:t,nameKey:r,sectors:n,stroke:a,strokeWidth:l,fill:i,name:o,hide:c,tooltipType:u}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:a,strokeWidth:l,fill:i,dataKey:t,nameKey:r,name:(0,s.uM)(o,t),hide:c,type:u,color:i,unit:""}}}var ep=(e,t)=>e>t?"start":e<t?"end":"middle",ef=(e,t,r)=>"function"==typeof t?t(e):(0,V.F4)(t,r,.8*r),ey=(e,t,r)=>{var{top:n,left:a,width:l,height:i}=t,o=(0,B.lY)(l,i),c=a+(0,V.F4)(e.cx,l,l/2),u=n+(0,V.F4)(e.cy,i,i/2),s=(0,V.F4)(e.innerRadius,o,0);return{cx:c,cy:u,innerRadius:s,outerRadius:ef(r,e.outerRadius,o),maxRadius:e.maxRadius||Math.sqrt(l*l+i*i)/2}},em=(e,t)=>(0,V.sA)(t-e)*Math.min(Math.abs(t-e),360),eg=(e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,i.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(K.I,eu({},t,{type:"linear",className:r}))},ev=(e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var a=r;if("function"==typeof e&&(a=e(t),n.isValidElement(a)))return a;var l=(0,i.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(L.E,eu({},t,{alignmentBaseline:"middle",className:l}),a)};function eh(e){var{sectors:t,props:r,showLabels:a}=e,{label:l,labelLine:i,dataKey:o}=r;if(!a||!l||!t)return null;var c=(0,et.u)(r),u=(0,G.J9)(l,!1),d=(0,G.J9)(i,!1),p="object"==typeof l&&"offsetRadius"in l&&l.offsetRadius||20,f=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,a=(0,B.IZ)(e.cx,e.cy,e.outerRadius+p,r),f=ec(ec(ec(ec({},c),e),{},{stroke:"none"},u),{},{index:t,textAnchor:ep(a.x,e.cx)},a),y=ec(ec(ec(ec({},c),e),{},{fill:"none",stroke:e.fill},d),{},{index:t,points:[(0,B.IZ)(e.cx,e.cy,e.outerRadius,r),a],key:"line"});return n.createElement(I.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},i&&eg(i,y),ev(l,f,(0,s.kr)(e,o)))});return n.createElement(I.W,{className:"recharts-pie-labels"},f)}function eb(e){var{sectors:t,activeShape:r,inactiveShape:a,allOtherPieProps:l,showLabels:i}=e,o=(0,C.G)(Z.A2),{onMouseEnter:c,onClick:u,onMouseLeave:s}=l,d=ei(l,en),p=(0,H.Cj)(c,l.dataKey),f=(0,H.Pg)(s),y=(0,H.Ub)(u,l.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,i)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var c=r&&String(i)===o,u=c?r:o?a:null,s=ec(ec({},e),{},{stroke:e.stroke,tabIndex:-1,[U.F0]:i,[U.um]:l.dataKey});return n.createElement(I.W,eu({tabIndex:-1,className:"recharts-pie-sector"},(0,W.XC)(d,e,i),{onMouseEnter:p(e,i),onMouseLeave:f(e,i),onClick:y(e,i),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(i)}),n.createElement(_.y,eu({option:u,isActive:c,shapeType:"sector"},s)))}),n.createElement(eh,{sectors:t,props:l,showLabels:i}))}function eA(e){var t,r,n,{pieSettings:a,displayedData:l,cells:i,offset:o}=e,{cornerRadius:c,startAngle:u,endAngle:d,dataKey:p,nameKey:f,tooltipType:y}=a,m=Math.abs(a.minAngle),g=em(u,d),v=Math.abs(g),h=l.length<=1?0:null!=(t=a.paddingAngle)?t:0,b=l.filter(e=>0!==(0,s.kr)(e,p,0)).length,A=v-b*m-(v>=360?b:b-1)*h,E=l.reduce((e,t)=>{var r=(0,s.kr)(t,p,0);return e+((0,V.Et)(r)?r:0)},0);return E>0&&(r=l.map((e,t)=>{var r,l=(0,s.kr)(e,p,0),d=(0,s.kr)(e,f,t),v=ey(a,o,e),b=((0,V.Et)(l)?l:0)/E,O=ec(ec({},e),i&&i[t]&&i[t].props),P=(r=t?n.endAngle+(0,V.sA)(g)*h*(0!==l):u)+(0,V.sA)(g)*((0!==l?m:0)+b*A),k=(r+P)/2,x=(v.innerRadius+v.outerRadius)/2,M=[{name:d,value:l,payload:O,dataKey:p,type:y}],j=(0,B.IZ)(v.cx,v.cy,x,k);return n=ec(ec(ec(ec({},a.presentationProps),{},{percent:b,cornerRadius:c,name:d,tooltipPayload:M,midAngle:k,middleRadius:x,tooltipPosition:j},O),v),{},{value:(0,s.kr)(e,p),startAngle:r,endAngle:P,payload:O,paddingAngle:(0,V.sA)(g)*h})})),r}function eE(e){var{props:t,previousSectorsRef:r}=e,{sectors:a,isAnimationActive:i,animationBegin:o,animationDuration:c,animationEasing:u,activeShape:s,inactiveShape:d,onAnimationStart:p,onAnimationEnd:f}=t,y=(0,X.n)(t,"recharts-pie-"),m=r.current,[g,v]=(0,n.useState)(!0),h=(0,n.useCallback)(()=>{"function"==typeof f&&f(),v(!1)},[f]),b=(0,n.useCallback)(()=>{"function"==typeof p&&p(),v(!0)},[p]);return n.createElement(er.J,{begin:o,duration:c,isActive:i,easing:u,onAnimationStart:b,onAnimationEnd:h,key:y},e=>{var i=[],o=(a&&a[0]).startAngle;return a.forEach((t,r)=>{var n=m&&m[r],a=r>0?l()(t,"paddingAngle",0):0;if(n){var c=(0,V.Dj)(n.endAngle-n.startAngle,t.endAngle-t.startAngle),u=ec(ec({},t),{},{startAngle:o+a,endAngle:o+c(e)+a});i.push(u),o=u.endAngle}else{var{endAngle:s,startAngle:d}=t,p=(0,V.Dj)(0,s-d)(e),f=ec(ec({},t),{},{startAngle:o+a,endAngle:o+p+a});i.push(f),o=f.endAngle}}),r.current=i,n.createElement(I.W,null,n.createElement(eb,{sectors:i,activeShape:s,inactiveShape:d,allOtherPieProps:t,showLabels:!g}))})}function eO(e){var{sectors:t,isAnimationActive:r,activeShape:a,inactiveShape:l}=e,i=(0,n.useRef)(null),o=i.current;return r&&t&&t.length&&(!o||o!==t)?n.createElement(eE,{props:e,previousSectorsRef:i}):n.createElement(eb,{sectors:t,activeShape:a,inactiveShape:l,allOtherPieProps:e,showLabels:!0})}function eP(e){var{hide:t,className:r,rootTabIndex:a}=e,l=(0,i.$)("recharts-pie",r);return t?null:n.createElement(I.W,{tabIndex:a,className:l},n.createElement(eO,e))}var ek={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!q.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function ex(e){var{id:t}=e,r=ei(e,ea),a=(0,n.useMemo)(()=>(0,G.aS)(e.children,F.f),[e.children]),l=(0,C.G)(e=>T(e,t,a));return n.createElement(n.Fragment,null,n.createElement(J.r,{fn:ed,args:ec(ec({},e),{},{sectors:l})}),n.createElement(eP,eu({},r,{sectors:l})))}function eM(e){var t=(0,Y.e)(e,ek),{id:r}=t,a=ei(t,el),l=(0,et.u)(a);return n.createElement(Q.x,{id:r,type:"pie"},e=>n.createElement(n.Fragment,null,n.createElement(ee.v,{type:"pie",id:e,data:a.data,dataKey:a.dataKey,hide:a.hide,angleAxisId:0,radiusAxisId:0,name:a.name,nameKey:a.nameKey,tooltipType:a.tooltipType,legendType:a.legendType,fill:a.fill,cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,paddingAngle:a.paddingAngle,minAngle:a.minAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius,cornerRadius:a.cornerRadius,presentationProps:l}),n.createElement(es,eu({},a,{id:e})),n.createElement(ex,eu({},a,{id:e})),a.children))}eM.displayName="Pie"},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},53904:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},54213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},55868:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},71539:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},90170:(e,t,r)=>{r.d(t,{r:()=>E});var n=r(12115),a=r(46641),l=r(20442),i=r(59068),o=r(95932),c=r(73433),u=r(81971),s=r(2267);function d(e){var t=(0,u.j)();return(0,n.useEffect)(()=>{t((0,s.U)(e))},[t,e]),null}var p=r(33725),f=r(93389),y=r(78892),m=["width","height","layout"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},h=(0,n.forwardRef)(function(e,t){var r,a=(0,f.e)(e.categoricalChartProps,v),{width:u,height:s,layout:h}=a,b=function(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(n=0;n<l.length;n++)r=l[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(a,m);if(!(0,y.F)(u)||!(0,y.F)(s))return null;var{chartName:A,defaultTooltipEventType:E,validateTooltipEventTypes:O,tooltipPayloadSearcher:P}=e;return n.createElement(l.J,{preloadedState:{options:{chartName:A,defaultTooltipEventType:E,validateTooltipEventTypes:O,tooltipPayloadSearcher:P,eventEmitter:void 0}},reduxStoreName:null!=(r=a.id)?r:A},n.createElement(i.TK,{chartData:a.data}),n.createElement(o.s,{width:u,height:s,layout:h,margin:a.margin}),n.createElement(c.p,{accessibilityLayer:a.accessibilityLayer,barCategoryGap:a.barCategoryGap,maxBarSize:a.maxBarSize,stackOffset:a.stackOffset,barGap:a.barGap,barSize:a.barSize,syncId:a.syncId,syncMethod:a.syncMethod,className:a.className}),n.createElement(d,{cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius}),n.createElement(p.L,g({width:u,height:s},b,{ref:t})))}),b=["item"],A={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},E=(0,n.forwardRef)((e,t)=>{var r=(0,f.e)(e,A);return n.createElement(h,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:a.uN,categoricalChartProps:r,ref:t})})}}]);