"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8128],{31877:(e,t,n)=>{n.d(t,{R:()=>s,i:()=>a});let r="http://localhost:3000";console.log("\uD83D\uDD27 Admin API Base URL:",r),console.log("\uD83D\uDD27 Environment variable NEXT_PUBLIC_ADMIN_API_URL:","http://localhost:3000");class i{setToken(e){this.token=e,console.log("\uD83D\uDD11 Admin API token set:",e?"".concat(e.substring(0,20),"..."):"null")}getToken(){return this.token}async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n="".concat(r).concat(e),i={headers:{"Content-Type":"application/json",...this.token&&{Authorization:"Bearer ".concat(this.token)},...t.headers},...t};console.log("\uD83D\uDE80 Admin API Request: ".concat(t.method||"GET"," ").concat(n)),console.log("� Token available:",!!this.token),console.log("�\uD83D\uDCE4 Request config:",{headers:i.headers,method:t.method||"GET",body:t.body});try{let t=Date.now(),r=await fetch(n,i),a=Date.now()-t;if(console.log("\uD83D\uDCE5 Admin API Response: ".concat(e)),console.log("⏱️  Duration: ".concat(a,"ms")),console.log("\uD83D\uDCCA Status: ".concat(r.status," ").concat(r.statusText)),!r.ok){if(401===r.status)throw console.error("❌ Unauthorized - Please check your admin credentials"),Error("Unauthorized - Please check your admin credentials");if(403===r.status)throw console.error("❌ Forbidden - Admin access required"),Error("Forbidden - Admin access required");let e=await r.json().catch(()=>({}));throw console.error("❌ Error response:",e),Error(e.message||"HTTP error! status: ".concat(r.status))}let s=await r.json();return console.log("✅ Response data:",s),console.log("---"),s}catch(t){throw console.error("❌ Admin API request failed: ".concat(e),t),console.log("---"),t}}async getAnalyticsSummary(){return this.request("/admin/analytics/summary")}async getUserAnalytics(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/analytics/users".concat(n?"?".concat(n):""))}async getActivityMetrics(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/analytics/activity-metrics".concat(n?"?".concat(n):""))}async getActivityTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/analytics/activity-trends".concat(n?"?".concat(n):""))}async getHealthCheck(){return this.request("/admin/health")}async getAgentCalls(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/agent-analytics/calls".concat(n?"?".concat(n):""))}async getAgentUsageStats(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/agent-analytics/usage-stats".concat(n?"?".concat(n):""))}async getTokenTrends(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/agent-analytics/token-trends".concat(n?"?".concat(n):""))}async getUserCosts(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/agent-analytics/user-costs".concat(n?"?".concat(n):""))}async getModelPerformance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/agent-analytics/model-performance".concat(n?"?".concat(n):""))}async getAgentEfficiency(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=new URLSearchParams;Object.entries(e).forEach(e=>{let[n,r]=e;null!=r&&t.append(n,r.toString())});let n=t.toString();return this.request("/admin/agent-analytics/agent-efficiency".concat(n?"?".concat(n):""))}async runMigrations(){return this.request("/admin/migrations/run",{method:"POST"})}async cleanupTenantSchemas(){return this.request("/admin/cleanup/schemas",{method:"POST"})}async getFeedback(e){let t=new URLSearchParams;(null==e?void 0:e.page)&&t.append("page",e.page.toString()),(null==e?void 0:e.limit)&&t.append("limit",e.limit.toString()),(null==e?void 0:e.search)&&t.append("search",e.search),(null==e?void 0:e.isHighPriority)!==void 0&&t.append("isHighPriority",e.isHighPriority.toString()),(null==e?void 0:e.minSubmissionCount)&&t.append("minSubmissionCount",e.minSubmissionCount.toString()),(null==e?void 0:e.startDate)&&t.append("startDate",e.startDate),(null==e?void 0:e.endDate)&&t.append("endDate",e.endDate),(null==e?void 0:e.sortBy)&&t.append("sortBy",e.sortBy),(null==e?void 0:e.sortOrder)&&t.append("sortOrder",e.sortOrder);let n=t.toString();return this.request(n?"/admin/feedback?".concat(n):"/admin/feedback")}constructor(){this.token=null}}let a=new i,s=e=>{a.setToken(e)}},88128:(e,t,n)=>{n.d(t,{B:()=>k});var r=n(65453),i=n(46786),a=n(31877),s=n(31917),o=n(49509);let l="auth_tokens",c="user_data",d=o.env.NEXT_PUBLIC_TOKEN_ENCRYPTION_KEY||"fallback-key-change-in-production";class u{static initialize(){this.key=d}static encrypt(e){try{this.key||(this.key=d);let t="";for(let n=0;n<e.length;n++)t+=String.fromCharCode(e.charCodeAt(n)^this.key.charCodeAt(n%this.key.length));return btoa(t)}catch(t){return console.error("Encryption failed:",t),e}}static decrypt(e){try{this.key||(this.key=d);let t=atob(e),n="";for(let e=0;e<t.length;e++)n+=String.fromCharCode(t.charCodeAt(e)^this.key.charCodeAt(e%this.key.length));return n}catch(t){return console.error("Decryption failed:",t),e}}}u.key=d,u.initialize();class g{static setTokens(e){try{let t=u.encrypt(JSON.stringify(e));sessionStorage.setItem(l,t),e.expiresAt>Date.now()+6048e5&&localStorage.setItem(l,t)}catch(e){console.error("Failed to store tokens:",e)}}static getTokens(){try{let e=sessionStorage.getItem(l);if(e||(e=localStorage.getItem(l)),!e)return null;let t=u.decrypt(e),n=JSON.parse(t);if(n.expiresAt<Date.now())return this.clearTokens(),null;return n}catch(e){return console.error("Failed to retrieve tokens:",e),this.clearTokens(),null}}static clearTokens(){try{sessionStorage.removeItem(l),localStorage.removeItem(l)}catch(e){console.error("Failed to clear tokens:",e)}}static hasValidTokens(){let e=this.getTokens();return null!==e&&e.expiresAt>Date.now()}static getAccessToken(){let e=this.getTokens();return(null==e?void 0:e.accessToken)||null}static getRefreshToken(){let e=this.getTokens();return(null==e?void 0:e.refreshToken)||null}static setUser(e){try{let t=JSON.stringify(e);sessionStorage.setItem(c,t),localStorage.setItem(c,t)}catch(e){console.error("Failed to store user data:",e)}}static getUser(){try{let e=sessionStorage.getItem(c);return e||(e=localStorage.getItem(c)),e?JSON.parse(e):null}catch(e){return console.error("Failed to retrieve user data:",e),null}}static clearUser(){try{sessionStorage.removeItem(c),localStorage.removeItem(c)}catch(e){console.error("Failed to clear user data:",e)}}static clearAll(){this.clearTokens(),this.clearUser()}}let h={user:null,tokens:null,isAuthenticated:!1,isInitialized:!1,pendingEmailVerification:void 0,emailVerificationSent:!1},m=null,y=e=>{m&&clearTimeout(m);let t=Math.max(e-Date.now()-3e5,6e4);t>0&&(m=setTimeout(()=>{k.getState().actions.refreshToken()},t))},k=(0,r.v)()((0,i.lt)((0,i.eh)((0,i.Zr)((e,t)=>({...h,isLoading:!1,error:null,actions:{login:async t=>{e({isLoading:!0,error:null});try{let n=await s.N.login(t.email,t.password);if(g.setTokens(n.tokens),g.setUser(n.user),n.tokens.accessToken)try{(0,a.R)(n.tokens.accessToken)}catch(e){console.warn("Failed to initialize admin API:",e)}e({user:n.user,tokens:n.tokens,isAuthenticated:!0,isLoading:!1,error:null}),y(n.tokens.expiresAt)}catch(t){throw e({error:{code:"LOGIN_FAILED",message:t instanceof Error?t.message:"Login failed"},isLoading:!1}),t}},signup:async t=>{e({isLoading:!0,error:null});try{let n=await s.N.register(t.email,t.password,t.name.split(" ")[0]||"User",t.name.split(" ").slice(1).join(" ")||"");g.setTokens(n.tokens),g.setUser(n.user),e({user:n.user,tokens:n.tokens,isAuthenticated:!0,isLoading:!1,error:null}),y(n.tokens.expiresAt)}catch(t){throw e({error:{code:"SIGNUP_FAILED",message:t instanceof Error?t.message:"Signup failed"},isLoading:!1}),t}},logout:()=>{m&&(clearTimeout(m),m=null),g.clearAll(),e({...h,isInitialized:!0})},refreshToken:async()=>{try{let t=g.getTokens();if(!(null==t?void 0:t.refreshToken))throw Error("No refresh token available");let n=await s.N.refreshToken(t.refreshToken);g.setTokens(n.tokens),e({tokens:n.tokens}),y(n.tokens.expiresAt)}catch(e){throw t().actions.logout(),e}},initializeSession:async()=>{try{e({isLoading:!0});let t=g.getTokens(),n=g.getUser();if(t&&n)if(t.expiresAt>Date.now()){if(t.accessToken)try{(0,a.R)(t.accessToken)}catch(e){console.warn("Failed to initialize admin API:",e)}e({user:n,tokens:t,isAuthenticated:!0,isInitialized:!0,isLoading:!1}),y(t.expiresAt);return}else g.clearAll();e({...h,isInitialized:!0,isLoading:!1})}catch(t){console.error("Session initialization failed:",t),e({...h,isInitialized:!0,isLoading:!1})}},clearSession:()=>{m&&(clearTimeout(m),m=null),g.clearAll(),e(h)},updateUser:n=>{let r=t().user;if(r){let t={...r,...n};g.setUser(t),e({user:t})}},setTokens:t=>{g.setTokens(t),e({tokens:t}),y(t.expiresAt)},clearTokens:()=>{m&&(clearTimeout(m),m=null),g.clearTokens(),e({tokens:null,isAuthenticated:!1})},sendEmailVerification:async(t,n)=>{e({isLoading:!0,error:null});try{await s.N.sendEmailVerification(t,n),e({isLoading:!1,pendingEmailVerification:t,emailVerificationSent:!0})}catch(t){throw e({error:{code:"EMAIL_VERIFICATION_FAILED",message:t instanceof Error?t.message:"Failed to send verification email"},isLoading:!1}),t}},verifyEmail:async(n,r)=>{e({isLoading:!0,error:null});try{await s.N.verifyEmail(n,r);let i=t().user;if(i&&i.email===n){let t={...i,isEmailVerified:!0};g.setUser(t),e({user:t,pendingEmailVerification:void 0,emailVerificationSent:!1,isLoading:!1})}else e({pendingEmailVerification:void 0,emailVerificationSent:!1,isLoading:!1})}catch(t){throw e({error:{code:"EMAIL_VERIFICATION_FAILED",message:t instanceof Error?t.message:"Email verification failed"},isLoading:!1}),t}},resendEmailVerification:async t=>{e({isLoading:!0,error:null});try{await s.N.resendEmailVerification(t),e({isLoading:!1,emailVerificationSent:!0})}catch(t){throw e({error:{code:"EMAIL_VERIFICATION_FAILED",message:t instanceof Error?t.message:"Failed to resend verification email"},isLoading:!1}),t}},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})}}}),{name:"session-store",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated,isInitialized:e.isInitialized})})),{name:"session-store"}))}}]);