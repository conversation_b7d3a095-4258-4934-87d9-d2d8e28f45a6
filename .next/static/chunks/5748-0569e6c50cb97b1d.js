"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5748],{27366:(e,t,a)=>{a.d(t,{GF:()=>o,Ni:()=>s});var r=a(56671);let o=(e,t)=>{r.oR.success(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||4e3})},s=(e,t)=>{r.oR.error(e,{description:null==t?void 0:t.description,duration:(null==t?void 0:t.duration)||5e3})}},30285:(e,t,a)=>{a.d(t,{$:()=>d});var r=a(95155),o=a(99708),s=a(74466);a(12115);var n=a(59434);let i=(0,s.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:s,asChild:d=!1,...l}=e,c=d?o.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:a,size:s,className:t})),...l})}},38382:(e,t,a)=>{a.d(t,{CG:()=>d,Fm:()=>g,Qs:()=>f,cj:()=>i,h:()=>u,qp:()=>v});var r=a(95155);a(12115);var o=a(15452),s=a(54416),n=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(o.bL,{"data-slot":"sheet",...t})}function d(e){let{...t}=e;return(0,r.jsx)(o.l9,{"data-slot":"sheet-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(o.ZL,{"data-slot":"sheet-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(o.hJ,{"data-slot":"sheet-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,children:a,side:i="right",...d}=e;return(0,r.jsxs)(l,{children:[(0,r.jsx)(c,{}),(0,r.jsxs)(o.UC,{"data-slot":"sheet-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===i&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===i&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===i&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===i&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...d,children:[a,(0,r.jsxs)(o.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(s.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,n.cn)("flex flex-col gap-1.5 p-4",t),...a})}function v(e){let{className:t,...a}=e;return(0,r.jsx)(o.hE,{"data-slot":"sheet-title",className:(0,n.cn)("text-foreground font-semibold",t),...a})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(o.VY,{"data-slot":"sheet-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}},44838:(e,t,a)=>{a.d(t,{I:()=>l,SQ:()=>d,V0:()=>v,_2:()=>c,lp:()=>u,mB:()=>g,rI:()=>n,ty:()=>i});var r=a(95155);a(12115);var o=a(48698),s=a(59434);function n(e){let{...t}=e;return(0,r.jsx)(o.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,r.jsx)(o.l9,{"data-slot":"dropdown-menu-trigger",...t})}function d(e){let{className:t,sideOffset:a=4,...n}=e;return(0,r.jsx)(o.ZL,{children:(0,r.jsx)(o.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function l(e){let{...t}=e;return(0,r.jsx)(o.YJ,{"data-slot":"dropdown-menu-group",...t})}function c(e){let{className:t,inset:a,variant:n="default",...i}=e;return(0,r.jsx)(o.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,s.cn)("focus:bg-gray-100 border border-transparent focus:text-gray-900 data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function u(e){let{className:t,inset:a,...n}=e;return(0,r.jsx)(o.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,s.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(o.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function v(e){let{className:t,...a}=e;return(0,r.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,s.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...a})}},59434:(e,t,a)=>{a.d(t,{cn:()=>s});var r=a(52596),o=a(39688);function s(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,o.QP)((0,r.$)(t))}},66681:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(88128),o=a(35695),s=a(12115),n=a(27366);function i(){let{user:e,isAuthenticated:t,error:a,pendingEmailVerification:i,emailVerificationSent:d,actions:l}=(0,r.B)(),c=(0,o.useRouter)(),[u,g]=(0,s.useState)(!1),v=(0,s.useCallback)(async e=>{g(!0);try{await l.login(e);let{user:t}=r.B.getState();(0,n.GF)("Welcome back!",{description:"Logged in as ".concat(null==t?void 0:t.email)}),(null==t?void 0:t.role)==="admin"?c.replace("/admin"):c.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Login failed";throw(0,n.Ni)("Login Failed",{description:e}),t}finally{g(!1)}},[l,c,g]),f=(0,s.useCallback)(async e=>{g(!0);try{await l.signup(e);let{user:t}=r.B.getState();(0,n.GF)("Account created successfully!",{description:"Welcome ".concat((null==t?void 0:t.name)||(null==t?void 0:t.email),"!")}),(null==t?void 0:t.role)==="admin"?c.replace("/admin"):c.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Signup failed";throw(0,n.Ni)("Signup Failed",{description:e}),t}finally{g(!1)}},[l,c,g]),h=(0,s.useCallback)(()=>{l.logout(),c.push("/auth/login")},[l,c]),b=(0,s.useCallback)(()=>{l.setError(null)},[l]),m=(0,s.useCallback)(async(e,t)=>{try{await l.sendEmailVerification(e,t)}catch(e){throw e}},[l]);return{user:e,isAuthenticated:t,isLoading:u,error:a,pendingEmailVerification:i,emailVerificationSent:d,login:v,signup:f,logout:h,clearError:b,sendEmailVerification:m,verifyEmail:(0,s.useCallback)(async(e,t)=>{try{await l.verifyEmail(e,t)}catch(e){throw e}},[l]),resendEmailVerification:(0,s.useCallback)(async e=>{try{await l.resendEmailVerification(e)}catch(e){throw e}},[l]),updateUser:l.updateUser,refreshToken:l.refreshToken}}},90786:(e,t,a)=>{a.d(t,{P:()=>i});var r=a(27016),o=a(48879),s=a(35695),n=a(56671);function i(){var e,t,a;let{isSignedIn:i,signOut:d,getToken:l}=(0,r.d)(),{user:c,isLoaded:u}=(0,o.Jd)(),{signIn:g,isLoaded:v}=(0,o.go)(),{signUp:f,isLoaded:h}=(0,o.yC)(),b=(0,s.useRouter)();return{isSignedIn:i,isLoaded:u,user:c,signOut:async()=>{try{await d(),n.oR.success("Signed out successfully"),b.push("/")}catch(e){n.oR.error("Failed to sign out"),console.error("Sign out error:",e)}},getToken:async()=>{try{return await l()}catch(e){return console.error("Error getting Clerk token:",e),null}},signIn:g,signUp:f,signInLoaded:v,signUpLoaded:h,userId:null==c?void 0:c.id,email:null==c||null==(e=c.emailAddresses[0])?void 0:e.emailAddress,firstName:null==c?void 0:c.firstName,lastName:null==c?void 0:c.lastName,fullName:null==c?void 0:c.fullName,imageUrl:null==c?void 0:c.imageUrl,isEmailVerified:(null==c||null==(a=c.emailAddresses[0])||null==(t=a.verification)?void 0:t.status)==="verified"}}}}]);