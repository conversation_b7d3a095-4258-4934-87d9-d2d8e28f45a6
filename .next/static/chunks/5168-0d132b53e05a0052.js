(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5168],{1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("triangle-alert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},5623:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},11518:(e,t,r)=>{"use strict";e.exports=r(82269).style},13062:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("megaphone",[["path",{d:"M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z",key:"q8bfy3"}],["path",{d:"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14",key:"1853fq"}],["path",{d:"M8 6v8",key:"15ugcq"}]])},15452:(e,t,r)=>{"use strict";r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>X,bm:()=>ei,hE:()=>en,hJ:()=>et,l9:()=>$});var n=r(12115),o=r(85185),i=r(6101),s=r(46081),l=r(61285),a=r(5845),c=r(19178),u=r(25519),d=r(34378),p=r(28905),h=r(63655),f=r(92293),y=r(93795),m=r(38168),g=r(99708),v=r(95155),_="Dialog",[k,x]=(0,s.A)(_),[b,w]=k(_),S=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:i,onOpenChange:s,modal:c=!0}=e,u=n.useRef(null),d=n.useRef(null),[p,h]=(0,a.i)({prop:o,defaultProp:null!=i&&i,onChange:s,caller:_});return(0,v.jsx)(b,{scope:t,triggerRef:u,contentRef:d,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),modal:c,children:r})};S.displayName=_;var A="DialogTrigger",j=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=w(A,r),l=(0,i.s)(t,s.triggerRef);return(0,v.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":Y(s.open),...n,ref:l,onClick:(0,o.m)(e.onClick,s.onOpenToggle)})});j.displayName=A;var R="DialogPortal",[C,M]=k(R,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:i}=e,s=w(R,t);return(0,v.jsx)(C,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(p.C,{present:r||s.open,children:(0,v.jsx)(d.Z,{asChild:!0,container:i,children:e})}))})};O.displayName=R;var E="DialogOverlay",F=n.forwardRef((e,t)=>{let r=M(E,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=w(E,e.__scopeDialog);return i.modal?(0,v.jsx)(p.C,{present:n||i.open,children:(0,v.jsx)(P,{...o,ref:t})}):null});F.displayName=E;var z=(0,g.TL)("DialogOverlay.RemoveScroll"),P=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(E,r);return(0,v.jsx)(y.A,{as:z,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(h.sG.div,{"data-state":Y(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),I="DialogContent",D=n.forwardRef((e,t)=>{let r=M(I,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=w(I,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||i.open,children:i.modal?(0,v.jsx)(N,{...o,ref:t}):(0,v.jsx)(T,{...o,ref:t})})});D.displayName=I;var N=n.forwardRef((e,t)=>{let r=w(I,e.__scopeDialog),s=n.useRef(null),l=(0,i.s)(t,r.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,m.Eq)(e)},[]),(0,v.jsx)(B,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=w(I,e.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,v.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,s;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(s=r.triggerRef.current)||s.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var n,s;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let l=t.target;(null==(s=r.triggerRef.current)?void 0:s.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:l,...a}=e,d=w(I,r),p=n.useRef(null),h=(0,i.s)(t,p);return(0,f.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,v.jsx)(c.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":Y(d.open),...a,ref:h,onDismiss:()=>d.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:d.titleId}),(0,v.jsx)(Q,{contentRef:p,descriptionId:d.descriptionId})]})]})}),H="DialogTitle",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(H,r);return(0,v.jsx)(h.sG.h2,{id:o.titleId,...n,ref:t})});L.displayName=H;var q="DialogDescription",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(q,r);return(0,v.jsx)(h.sG.p,{id:o.descriptionId,...n,ref:t})});V.displayName=q;var K="DialogClose",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=w(K,r);return(0,v.jsx)(h.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function Y(e){return e?"open":"closed"}W.displayName=K;var G="DialogTitleWarning",[U,Z]=(0,s.q)(G,{contentName:I,titleName:H,docsSlug:"dialog"}),J=e=>{let{titleId:t}=e,r=Z(G),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},Q=e=>{let{contentRef:t,descriptionId:r}=e,o=Z("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(i))},[i,t,r]),null},X=S,$=j,ee=O,et=F,er=D,en=L,eo=V,ei=W},16785:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17576:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},22436:(e,t,r)=>{"use strict";var n=r(12115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=n.useState,s=n.useEffect,l=n.useLayoutEffect,a=n.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=i({inst:{value:r,getSnapshot:t}}),o=n[0].inst,u=n[1];return l(function(){o.value=r,o.getSnapshot=t,c(o)&&u({inst:o})},[e,r,t]),s(function(){return c(o)&&u({inst:o}),e(function(){c(o)&&u({inst:o})})},[e]),a(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:u},27380:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=function(e){var t=[],r=null,n=function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];t=o,r||(r=requestAnimationFrame(function(){r=null,e.apply(void 0,t)}))};return n.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},n}},27809:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},29418:(e,t,r)=>{"use strict";r.d(t,{SQ:()=>d,YH:()=>h,a:()=>p,cY:()=>u,fT:()=>i,ge:()=>a,l:()=>o});var n=r(93179),o=function(e){var t=e.top,r=e.right,n=e.bottom,o=e.left;return{top:t,right:r,bottom:n,left:o,width:r-o,height:n-t,x:o,y:t,center:{x:(r+o)/2,y:(n+t)/2}}},i=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},s=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},l={top:0,right:0,bottom:0,left:0},a=function(e){var t=e.borderBox,r=e.margin,n=void 0===r?l:r,a=e.border,c=void 0===a?l:a,u=e.padding,d=void 0===u?l:u,p=o(i(t,n)),h=o(s(t,c)),f=o(s(h,d));return{marginBox:p,borderBox:o(t),paddingBox:h,contentBox:f,margin:n,border:c,padding:d}},c=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&(0,n.A)(!1),r},u=function(e,t){var r=e.borderBox,n=e.border,o=e.margin,i=e.padding;return a({borderBox:{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x},border:n,margin:o,padding:i})},d=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),u(e,t)},p=function(e,t){return a({borderBox:e,margin:{top:c(t.marginTop),right:c(t.marginRight),bottom:c(t.marginBottom),left:c(t.marginLeft)},padding:{top:c(t.paddingTop),right:c(t.paddingRight),bottom:c(t.paddingBottom),left:c(t.paddingLeft)},border:{top:c(t.borderTopWidth),right:c(t.borderRightWidth),bottom:c(t.borderBottomWidth),left:c(t.borderLeftWidth)}})},h=function(e){return p(e.getBoundingClientRect(),window.getComputedStyle(e))}},33109:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},33127:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]])},34861:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("panel-left-close",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]])},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},37108:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},38564:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},44020:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},48021:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},48136:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},49033:(e,t,r)=>{"use strict";e.exports=r(22436)},51976:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55868:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},57340:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},58832:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},59947:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},60760:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var n=r(95155),o=r(12115),i=r(90869),s=r(82885),l=r(97494),a=r(80845),c=r(27351),u=r(51508);class d extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,c.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(e){let{children:t,isPresent:r,anchorX:i,root:s}=e,l=(0,o.useId)(),a=(0,o.useRef)(null),c=(0,o.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=(0,o.useContext)(u.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o,right:u}=c.current;if(r||!a.current||!e||!t)return;a.current.dataset.motionPopId=l;let d=document.createElement("style");p&&(d.nonce=p);let h=null!=s?s:document.head;return h.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(l,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===i?"left: ".concat(o):"right: ".concat(u),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{h.contains(d)&&h.removeChild(d)}},[r]),(0,n.jsx)(d,{isPresent:r,childRef:a,sizeRef:c,children:o.cloneElement(t,{ref:a})})}let h=e=>{let{children:t,initial:r,isPresent:i,onExitComplete:l,custom:c,presenceAffectsLayout:u,mode:d,anchorX:h,root:y}=e,m=(0,s.M)(f),g=(0,o.useId)(),v=!0,_=(0,o.useMemo)(()=>(v=!1,{id:g,initial:r,isPresent:i,custom:c,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;l&&l()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[i,m,l]);return u&&v&&(_={..._}),(0,o.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[i]),o.useEffect(()=>{i||m.size||!l||l()},[i]),"popLayout"===d&&(t=(0,n.jsx)(p,{isPresent:i,anchorX:h,root:y,children:t})),(0,n.jsx)(a.t.Provider,{value:_,children:t})};function f(){return new Map}var y=r(32082);let m=e=>e.key||"";function g(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}let v=e=>{let{children:t,custom:r,initial:a=!0,onExitComplete:c,presenceAffectsLayout:u=!0,mode:d="sync",propagate:p=!1,anchorX:f="left",root:v}=e,[_,k]=(0,y.xQ)(p),x=(0,o.useMemo)(()=>g(t),[t]),b=p&&!_?[]:x.map(m),w=(0,o.useRef)(!0),S=(0,o.useRef)(x),A=(0,s.M)(()=>new Map),[j,R]=(0,o.useState)(x),[C,M]=(0,o.useState)(x);(0,l.E)(()=>{w.current=!1,S.current=x;for(let e=0;e<C.length;e++){let t=m(C[e]);b.includes(t)?A.delete(t):!0!==A.get(t)&&A.set(t,!1)}},[C,b.length,b.join("-")]);let O=[];if(x!==j){let e=[...x];for(let t=0;t<C.length;t++){let r=C[t],n=m(r);b.includes(n)||(e.splice(t,0,r),O.push(r))}return"wait"===d&&O.length&&(e=O),M(g(e)),R(x),null}let{forceRender:E}=(0,o.useContext)(i.L);return(0,n.jsx)(n.Fragment,{children:C.map(e=>{let t=m(e),o=(!p||!!_)&&(x===C||b.includes(t));return(0,n.jsx)(h,{isPresent:o,initial:(!w.current||!!a)&&void 0,custom:r,presenceAffectsLayout:u,mode:d,root:v,onExitComplete:o?void 0:()=>{if(!A.has(t))return;A.set(t,!0);let e=!0;A.forEach(t=>{t||(e=!1)}),e&&(null==E||E(),M(S.current),p&&(null==k||k()),c&&c())},anchorX:f,children:e},t)})})}},62525:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},68375:()=>{},70463:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},71007:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71366:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},71539:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},79630:(e,t,r)=>{"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{A:()=>n})},82269:(e,t,r)=>{"use strict";var n=r(49509);r(68375);var o=r(12115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(o),s=void 0!==n&&n.env&&!0,l=function(e){return"[object String]"===Object.prototype.toString.call(e)},a=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,o=t.optimizeForSpeed,i=void 0===o?s:o;c(l(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",c("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var a="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=a?a.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(s||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(c(l(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return s||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];c(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&c(l(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var o=document.head||document.getElementsByTagName("head")[0];return r?o.insertBefore(n,r):o.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var u=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function p(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return d[n]||(d[n]="jsx-"+u(e+"-"+r)),d[n]}function h(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var f=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,o=t.optimizeForSpeed,i=void 0!==o&&o;this._sheet=n||new a({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),n&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,o=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var i=o.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=i,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var o=p(n,r);return{styleId:o,rules:Array.isArray(t)?t.map(function(e){return h(o,e)}):[h(o,t)]}}return{styleId:p(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),y=o.createContext(null);y.displayName="StyleSheetContext";var m=i.default.useInsertionEffect||i.default.useLayoutEffect,g="undefined"!=typeof window?new f:void 0;function v(e){var t=g||o.useContext(y);return t&&("undefined"==typeof window?t.add(e):m(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}v.dynamic=function(e){return e.map(function(e){return p(e[0],e[1])}).join(" ")},t.style=v},83731:(e,t,r)=>{"use strict";r.d(t,{sf:()=>l,so:()=>s});var n=r(72341),o=r(12115),i=(0,o.createContext)({client:n.Ay});function s(e){var t=e.children,r=e.client,s=e.apiKey,l=e.options,a=(0,o.useRef)(null),c=(0,o.useMemo)(function(){return r?(s&&console.warn("[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `apiKey` will be ignored in favour of `client`."),l&&console.warn("[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`."),r):(s||console.warn("[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior."),n.Ay)},[r,s,JSON.stringify(l)]);return(0,o.useEffect)(function(){if(!r){var e=a.current;e?(s!==e.apiKey&&console.warn("[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop."),l&&!function e(t,r,n){if(void 0===n&&(n=new WeakMap),t===r)return!0;if("object"!=typeof t||null===t||"object"!=typeof r||null===r)return!1;if(n.has(t)&&n.get(t)===r)return!0;n.set(t,r);var o=Object.keys(t),i=Object.keys(r);if(o.length!==i.length)return!1;for(var s=0;s<o.length;s++){var l=o[s];if(!i.includes(l)||!e(t[l],r[l],n))return!1}return!0}(l,e.options)&&n.Ay.set_config(l)):(n.Ay.__loaded&&console.warn("[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues."),n.Ay.init(s,l)),a.current={apiKey:s,options:null!=l?l:{}}}},[r,s,JSON.stringify(l)]),o.createElement(i.Provider,{value:{client:c}},t)}var l=function(){return(0,o.useContext)(i).client},a=function(e,t){return(a=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},c=function(){return(c=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function u(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}"function"==typeof SuppressedError&&SuppressedError;var d=function(e){return"function"==typeof e};function p(e){var t=e.flag,r=e.children,n=e.onIntersect,o=e.onClick,i=e.trackView,s=e.options,a=u(e,["flag","children","onIntersect","onClick","trackView","options"]),d=useRef(null);return useEffect(function(){if(null!==d.current&&i){var e=new IntersectionObserver(function(e){return n(e[0])},c({threshold:.1},s));return e.observe(d.current),function(){return e.disconnect()}}},[t,s,l(),d,i,n]),React.createElement("div",c({ref:d},a,{onClick:o}),r)}var h={componentStack:null,error:null},f={INVALID_FALLBACK:"[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element."};!function(e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=r}function r(t){var r=e.call(this,t)||this;return r.state=h,r}a(r,e),r.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),r.prototype.componentDidCatch=function(e,t){var r,n=t.componentStack,o=this.props.additionalProperties;this.setState({error:e,componentStack:n}),d(o)?r=o(e):"object"==typeof o&&(r=o),this.context.client.captureException(e,r)},r.prototype.render=function(){var e=this.props,t=e.children,r=e.fallback,n=this.state;if(null==n.componentStack)return d(t)?t():t;var i=d(r)?o.createElement(r,{error:n.error,componentStack:n.componentStack}):r;return o.isValidElement(i)?i:(console.warn(f.INVALID_FALLBACK),o.createElement(o.Fragment,null))},r.contextType=i}(o.Component)},92138:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},92657:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);