"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3856],{13432:(e,r,t)=>{t.d(r,{WG:()=>o,lH:()=>a,qQ:()=>s});let s=new(t(87017)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,r)=>(!((null==r?void 0:r.status)>=400)||!((null==r?void 0:r.status)<500))&&e<3,refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,onError:e=>{console.error("Mutation error:",e)}}}}),a={user:e=>["user",e],userProfile:e=>["user","profile",e],projects:()=>["projects"],project:e=>["projects",e],projectTasks:e=>["projects",e,"tasks"],admin:{analytics:()=>["admin","analytics"],summary:()=>["admin","analytics","summary"],users:e=>["admin","users",e],feedback:e=>["admin","feedback",e]},businessSections:e=>["business-sections",e]},o={user:e=>{s.invalidateQueries({queryKey:a.user(e)}),s.invalidateQueries({queryKey:a.userProfile(e)})},projects:()=>{s.invalidateQueries({queryKey:a.projects()})},project:e=>{s.invalidateQueries({queryKey:a.project(e)}),s.invalidateQueries({queryKey:a.projectTasks(e)})},admin:()=>{s.invalidateQueries({queryKey:a.admin.analytics()}),s.invalidateQueries({queryKey:a.admin.summary()}),s.invalidateQueries({queryKey:a.admin.users()}),s.invalidateQueries({queryKey:a.admin.feedback()})}}},16549:(e,r,t)=>{t.d(r,{Zd:()=>l,j9:()=>c});var s=t(26715),a=t(5041),o=t(90786),n=t(13432),i=t(56671);function c(){let{userId:e,getToken:r}=(0,o.P)();(0,s.jE)();let t=function(){let{userId:e,getToken:r}=(0,o.P)(),t=(0,s.jE)();return(0,a.n)({mutationFn:async t=>{let{internalUserId:s,userData:a}=t,o=await r(),n=await fetch("".concat("http://localhost:3000","/api/users/").concat(s),{method:"PATCH",headers:{"Content-Type":"application/json",...o&&{Authorization:"Bearer ".concat(o)}},body:JSON.stringify({...a,clerkId:e})});if(!n.ok){let e=await n.text();throw Error("Failed to update user: ".concat(n.status," ").concat(e))}return n.json()},onMutate:async r=>{let{userData:s}=r;await t.cancelQueries({queryKey:n.lH.user(e)});let a=t.getQueryData(n.lH.user(e));if(a){let r={...a,...s,updatedAt:new Date().toISOString()};t.setQueryData(n.lH.user(e),r)}return{previousUser:a}},onError:(r,s,a)=>{(null==a?void 0:a.previousUser)&&t.setQueryData(n.lH.user(e),a.previousUser),console.error("Failed to update user:",r),i.oR.error("Failed to update profile. Please try again.")},onSettled:()=>{n.WG.user(e)},onSuccess:e=>{i.oR.success("Profile updated successfully!"),console.log("User updated successfully:",e)}})}();return(0,a.n)({mutationFn:async s=>{let a=await r(),o=await fetch("".concat("http://localhost:3000","/api/users/clerk/").concat(e),{headers:{"Content-Type":"application/json",...a&&{Authorization:"Bearer ".concat(a)}}});if(!o.ok)throw Error("User not found in backend. Please sync your account first.");let n=await o.json();return t.mutateAsync({internalUserId:n.id,userData:s})},onError:e=>{console.error("Failed to update user by Clerk ID:",e),i.oR.error(e.message||"Failed to update profile. Please try again.")}})}function l(){let{user:e,userId:r,email:t,firstName:c,lastName:l}=(0,o.P)(),u=function(){let{getToken:e}=(0,o.P)(),r=(0,s.jE)();return(0,a.n)({mutationFn:async r=>{let t=await e(),s="http://localhost:3000",a=await fetch("".concat(s,"/api/users"),{method:"POST",headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}},body:JSON.stringify({...r,role:r.role||"user",status:r.status||"active",timezone:r.timezone||"UTC",preferences:r.preferences||{notifications:!0,theme:"system",language:"en"}})});if(!a.ok){let e=await a.text();if(console.error("Backend response error:",{status:a.status,statusText:a.statusText,errorText:e,url:"".concat(s,"/api/users")}),400===a.status&&(e.includes("already exists")||e.includes("clerk ID already exists"))){console.log("User already exists, fetching existing user...");let e=await fetch("".concat(s,"/api/users/clerk/").concat(r.clerkId),{headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}}});if(e.ok){let r=await e.json();return console.log("Found existing user:",r),r}throw console.log("Could not fetch existing user, but user exists in backend"),Error("User already exists in backend")}throw Error("Failed to create user: ".concat(a.status," ").concat(a.statusText," - ").concat(e))}return a.json()},onSuccess:(e,t)=>{r.setQueryData(n.lH.user(t.clerkId),e),n.WG.user(t.clerkId),i.oR.success("Account synced successfully!"),console.log("User synced successfully in backend:",e)},onError:(e,r)=>{console.error("Failed to create user in backend:",e),console.error("User data that failed:",r),e.message&&e.message.includes("already exists")?console.log("User already exists in backend - this is expected"):i.oR.error("Failed to sync account: ".concat(e.message))}})}();return(0,a.n)({mutationFn:async()=>{if(!e||!r)throw Error("User not available");let s={email:t||"",firstName:c||"",lastName:l||"",clerkId:r,role:"user",status:"active",avatarUrl:e.imageUrl||"",bio:"",timezone:"UTC",preferences:{notifications:!0,theme:"system",language:"en"}};return u.mutateAsync(s)},onError:e=>{console.error("Failed to sync user to backend:",e),i.oR.error("Failed to sync account. Please contact support.")}})}},17739:(e,r,t)=>{t.d(r,{Jd:()=>n,rE:()=>i});var s=t(90786),a=t(13432),o=t(32960);function n(){let{userId:e,getToken:r,isSignedIn:t}=(0,s.P)();return(0,o.I)({queryKey:a.lH.user(e),queryFn:async()=>{if(!e)throw Error("User ID not available");let t=await r(),s="".concat("http://localhost:3000","/api/users/clerk/").concat(e);console.log("Fetching user from backend:",{url:s,userId:e,hasToken:!!t});let a=new AbortController,o=setTimeout(()=>a.abort(),1e4),n=await fetch(s,{headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}},signal:a.signal});if(clearTimeout(o),console.log("Backend response:",{status:n.status,statusText:n.statusText,ok:n.ok}),!n.ok){if(404===n.status)throw Error("User not found in backend");throw Error("Failed to fetch user: ".concat(n.status," ").concat(n.statusText))}return n.json()},enabled:!!e&&t,staleTime:3e5,gcTime:6e5,retry:(e,r)=>{var t,s;return!((null==r||null==(t=r.message)?void 0:t.includes("User not found"))||(null==r||null==(s=r.message)?void 0:s.includes("Failed to fetch")))&&(null==r?void 0:r.name)!=="AbortError"&&e<2},throwOnError:!1})}function i(){let{userId:e,getToken:r,isSignedIn:t}=(0,s.P)();return(0,o.I)({queryKey:["user","exists",e],queryFn:async()=>{if(!e)return!1;try{let t=await r(),s="http://localhost:3000";s.includes("localhost")&&console.log("Attempting to connect to local backend:",s);let a=new AbortController,o=setTimeout(()=>a.abort(),5e3),n=await fetch("".concat(s,"/api/users/clerk/").concat(e),{method:"GET",headers:{"Content-Type":"application/json",...t&&{Authorization:"Bearer ".concat(t)}},signal:a.signal});return clearTimeout(o),console.log("User exists check:",{userId:e,status:n.status,exists:n.ok}),n.ok}catch(e){return console.error("Error checking if user exists:",e),!1}},enabled:!!e&&t,staleTime:3e4,retry:!1,throwOnError:!1})}},18994:(e,r,t)=>{t.d(r,{N:()=>l});var s=t(95155),a=t(90786),o=t(59434),n=t(6874),i=t.n(n),c=t(35695);function l(){let e=(0,c.usePathname)(),{user:r}=(0,a.P)();return(0,s.jsx)("nav",{className:"flex items-center space-x-6",children:[{name:"Dashboard",href:"/user-dashboard"}].filter(r=>e!==r.href).map(r=>(0,s.jsxs)(i(),{href:r.href,className:(0,o.cn)("text-sm font-medium transition-colors relative",e===r.href?"text-[#166534] hover:text-[#166534]":"text-muted-foreground hover:text-[#166534]"),children:[r.name,e===r.href&&(0,s.jsx)("div",{className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full"})]},r.href))})}},26126:(e,r,t)=>{t.d(r,{E:()=>c});var s=t(95155);t(12115);var a=t(99708),o=t(74466),n=t(59434);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:r,variant:t,asChild:o=!1,...c}=e,l=o?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(i({variant:t}),r),...c})}},41752:(e,r,t)=>{t.d(r,{B:()=>h});var s=t(95155),a=t(71007),o=t(381),n=t(34835),i=t(35695),c=t(99912),l=t(91950),u=t(44838),d=t(90786);function h(){let{user:e,signOut:r,firstName:t,email:h,fullName:m}=(0,d.P)(),f=(0,i.useRouter)();return e?(0,s.jsxs)(u.rI,{children:[(0,s.jsx)(u.ty,{asChild:!0,children:(0,s.jsx)("div",{children:(0,s.jsx)(c.V,{icon:a.A,text:t||"User",variant:"ghost",size:"md",layout:"horizontal",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:l.hS.md})})}),(0,s.jsxs)(u.SQ,{className:"w-56 bg-background border-border",align:"end",forceMount:!0,children:[(0,s.jsx)(u.lp,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none text-foreground",children:m||"User"}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:h||""})]})}),(0,s.jsx)(u.mB,{className:"bg-border"}),(0,s.jsxs)(u._2,{onClick:()=>{f.push("/profile")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]}),(0,s.jsxs)(u._2,{onClick:()=>{f.push("/settings")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Settings"})]}),(0,s.jsx)(u.mB,{className:"bg-border"}),(0,s.jsxs)(u._2,{onClick:()=>{r()},className:"hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]}):null}},56611:(e,r,t)=>{t.d(r,{c:()=>f});var s=t(95155),a=t(75525),o=t(74783),n=t(6874),i=t.n(n),c=t(35695),l=t(12115),u=t(30285),d=t(38382),h=t(66681),m=t(59434);function f(){let[e,r]=(0,l.useState)(!1),t=(0,c.usePathname)(),{user:n}=(0,h.A)(),f=(null==n?void 0:n.role)==="admin"?[{name:"Admin",href:"/admin",icon:a.A}]:[{name:"Dashboard",href:"/user-dashboard",icon:void 0}];return(0,s.jsxs)(d.cj,{open:e,onOpenChange:r,children:[(0,s.jsx)(d.CG,{asChild:!0,children:(0,s.jsxs)(u.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors",children:[(0,s.jsx)(o.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,s.jsx)(d.h,{side:"left",className:"pr-0 bg-background border-r border-border",children:(0,s.jsx)("nav",{className:"flex flex-col space-y-3 mt-6",children:f.filter(e=>t!==e.href).map(e=>{let a="/admin"===e.href?t.startsWith("/admin"):t===e.href;return(0,s.jsxs)(i(),{href:e.href,onClick:()=>r(!1),className:(0,m.cn)("text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",a?"text-[#166534] bg-[#166534]/10":"text-muted-foreground hover:text-[#166534]"),children:[e.icon&&(0,s.jsx)(e.icon,{className:"h-4 w-4"}),e.name,a&&(0,s.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full"})]},e.href)})})})]})}}}]);