"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8198],{5041:(t,e,s)=>{s.d(e,{n:()=>c});var r=s(12115),i=s(34560),n=s(7165),u=s(25910),a=s(52020),h=class extends u.Q{#t;#e=void 0;#s;#r;constructor(t,e){super(),this.#t=t,this.setOptions(e),this.bindMethods(),this.#i()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(t){let e=this.options;this.options=this.#t.defaultMutationOptions(t),(0,a.f8)(this.options,e)||this.#t.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#s,observer:this}),e?.mutationKey&&this.options.mutationKey&&(0,a.EN)(e.mutationKey)!==(0,a.EN)(this.options.mutationKey)?this.reset():this.#s?.state.status==="pending"&&this.#s.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#s?.removeObserver(this)}onMutationUpdate(t){this.#i(),this.#n(t)}getCurrentResult(){return this.#e}reset(){this.#s?.removeObserver(this),this.#s=void 0,this.#i(),this.#n()}mutate(t,e){return this.#r=e,this.#s?.removeObserver(this),this.#s=this.#t.getMutationCache().build(this.#t,this.options),this.#s.addObserver(this),this.#s.execute(t)}#i(){let t=this.#s?.state??(0,i.$)();this.#e={...t,isPending:"pending"===t.status,isSuccess:"success"===t.status,isError:"error"===t.status,isIdle:"idle"===t.status,mutate:this.mutate,reset:this.reset}}#n(t){n.jG.batch(()=>{if(this.#r&&this.hasListeners()){let e=this.#e.variables,s=this.#e.context;t?.type==="success"?(this.#r.onSuccess?.(t.data,e,s),this.#r.onSettled?.(t.data,null,e,s)):t?.type==="error"&&(this.#r.onError?.(t.error,e,s),this.#r.onSettled?.(void 0,t.error,e,s))}this.listeners.forEach(t=>{t(this.#e)})})}},o=s(26715);function c(t,e){let s=(0,o.jE)(e),[i]=r.useState(()=>new h(s,t));r.useEffect(()=>{i.setOptions(t)},[i,t]);let u=r.useSyncExternalStore(r.useCallback(t=>i.subscribe(n.jG.batchCalls(t)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),c=r.useCallback((t,e)=>{i.mutate(t,e).catch(a.lQ)},[i]);if(u.error&&(0,a.GU)(i.options.throwOnError,[u.error]))throw u.error;return{...u,mutate:c,mutateAsync:u.mutate}}},32960:(t,e,s)=>{s.d(e,{I:()=>T});var r=s(50920),i=s(7165),n=s(39853),u=s(25910),a=s(73504),h=s(52020),o=class extends u.Q{constructor(t,e){super(),this.options=e,this.#t=t,this.#u=null,this.#a=(0,a.T)(),this.options.experimental_prefetchInRender||this.#a.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(e)}#t;#h=void 0;#o=void 0;#e=void 0;#c;#l;#a;#u;#d;#p;#f;#y;#R;#b;#m=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#h.addObserver(this),c(this.#h,this.options)?this.#v():this.updateResult(),this.#Q())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.#h,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.#h,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#O(),this.#g(),this.#h.removeObserver(this)}setOptions(t){let e=this.options,s=this.#h;if(this.options=this.#t.defaultQueryOptions(t),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,h.Eh)(this.options.enabled,this.#h))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#I(),this.#h.setOptions(this.options),e._defaulted&&!(0,h.f8)(this.options,e)&&this.#t.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#h,observer:this});let r=this.hasListeners();r&&d(this.#h,s,this.options,e)&&this.#v(),this.updateResult(),r&&(this.#h!==s||(0,h.Eh)(this.options.enabled,this.#h)!==(0,h.Eh)(e.enabled,this.#h)||(0,h.d2)(this.options.staleTime,this.#h)!==(0,h.d2)(e.staleTime,this.#h))&&this.#E();let i=this.#S();r&&(this.#h!==s||(0,h.Eh)(this.options.enabled,this.#h)!==(0,h.Eh)(e.enabled,this.#h)||i!==this.#b)&&this.#C(i)}getOptimisticResult(t){var e,s;let r=this.#t.getQueryCache().build(this.#t,t),i=this.createResult(r,t);return e=this,s=i,(0,h.f8)(e.getCurrentResult(),s)||(this.#e=i,this.#l=this.options,this.#c=this.#h.state),i}getCurrentResult(){return this.#e}trackResult(t,e){return new Proxy(t,{get:(t,s)=>(this.trackProp(s),e?.(s),Reflect.get(t,s))})}trackProp(t){this.#m.add(t)}getCurrentQuery(){return this.#h}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){let e=this.#t.defaultQueryOptions(t),s=this.#t.getQueryCache().build(this.#t,e);return s.fetch().then(()=>this.createResult(s,e))}fetch(t){return this.#v({...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#e))}#v(t){this.#I();let e=this.#h.fetch(this.options,t);return t?.throwOnError||(e=e.catch(h.lQ)),e}#E(){this.#O();let t=(0,h.d2)(this.options.staleTime,this.#h);if(h.S$||this.#e.isStale||!(0,h.gn)(t))return;let e=(0,h.j3)(this.#e.dataUpdatedAt,t);this.#y=setTimeout(()=>{this.#e.isStale||this.updateResult()},e+1)}#S(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#h):this.options.refetchInterval)??!1}#C(t){this.#g(),this.#b=t,!h.S$&&!1!==(0,h.Eh)(this.options.enabled,this.#h)&&(0,h.gn)(this.#b)&&0!==this.#b&&(this.#R=setInterval(()=>{(this.options.refetchIntervalInBackground||r.m.isFocused())&&this.#v()},this.#b))}#Q(){this.#E(),this.#C(this.#S())}#O(){this.#y&&(clearTimeout(this.#y),this.#y=void 0)}#g(){this.#R&&(clearInterval(this.#R),this.#R=void 0)}createResult(t,e){let s,r=this.#h,i=this.options,u=this.#e,o=this.#c,l=this.#l,f=t!==r?t.state:this.#o,{state:y}=t,R={...y},b=!1;if(e._optimisticResults){let s=this.hasListeners(),u=!s&&c(t,e),a=s&&d(t,r,e,i);(u||a)&&(R={...R,...(0,n.k)(y.data,t.options)}),"isRestoring"===e._optimisticResults&&(R.fetchStatus="idle")}let{error:m,errorUpdatedAt:v,status:Q}=R;s=R.data;let O=!1;if(void 0!==e.placeholderData&&void 0===s&&"pending"===Q){let t;u?.isPlaceholderData&&e.placeholderData===l?.placeholderData?(t=u.data,O=!0):t="function"==typeof e.placeholderData?e.placeholderData(this.#f?.state.data,this.#f):e.placeholderData,void 0!==t&&(Q="success",s=(0,h.pl)(u?.data,t,e),b=!0)}if(e.select&&void 0!==s&&!O)if(u&&s===o?.data&&e.select===this.#d)s=this.#p;else try{this.#d=e.select,s=e.select(s),s=(0,h.pl)(u?.data,s,e),this.#p=s,this.#u=null}catch(t){this.#u=t}this.#u&&(m=this.#u,s=this.#p,v=Date.now(),Q="error");let g="fetching"===R.fetchStatus,I="pending"===Q,E="error"===Q,S=I&&g,C=void 0!==s,T={status:Q,fetchStatus:R.fetchStatus,isPending:I,isSuccess:"success"===Q,isError:E,isInitialLoading:S,isLoading:S,data:s,dataUpdatedAt:R.dataUpdatedAt,error:m,errorUpdatedAt:v,failureCount:R.fetchFailureCount,failureReason:R.fetchFailureReason,errorUpdateCount:R.errorUpdateCount,isFetched:R.dataUpdateCount>0||R.errorUpdateCount>0,isFetchedAfterMount:R.dataUpdateCount>f.dataUpdateCount||R.errorUpdateCount>f.errorUpdateCount,isFetching:g,isRefetching:g&&!I,isLoadingError:E&&!C,isPaused:"paused"===R.fetchStatus,isPlaceholderData:b,isRefetchError:E&&C,isStale:p(t,e),refetch:this.refetch,promise:this.#a,isEnabled:!1!==(0,h.Eh)(e.enabled,t)};if(this.options.experimental_prefetchInRender){let e=t=>{"error"===T.status?t.reject(T.error):void 0!==T.data&&t.resolve(T.data)},s=()=>{e(this.#a=T.promise=(0,a.T)())},i=this.#a;switch(i.status){case"pending":t.queryHash===r.queryHash&&e(i);break;case"fulfilled":("error"===T.status||T.data!==i.value)&&s();break;case"rejected":("error"!==T.status||T.error!==i.reason)&&s()}}return T}updateResult(){let t=this.#e,e=this.createResult(this.#h,this.options);this.#c=this.#h.state,this.#l=this.options,void 0!==this.#c.data&&(this.#f=this.#h),(0,h.f8)(e,t)||(this.#e=e,this.#n({listeners:(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,s="function"==typeof e?e():e;if("all"===s||!s&&!this.#m.size)return!0;let r=new Set(s??this.#m);return this.options.throwOnError&&r.add("error"),Object.keys(this.#e).some(e=>this.#e[e]!==t[e]&&r.has(e))})()}))}#I(){let t=this.#t.getQueryCache().build(this.#t,this.options);if(t===this.#h)return;let e=this.#h;this.#h=t,this.#o=t.state,this.hasListeners()&&(e?.removeObserver(this),t.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#Q()}#n(t){i.jG.batch(()=>{t.listeners&&this.listeners.forEach(t=>{t(this.#e)}),this.#t.getQueryCache().notify({query:this.#h,type:"observerResultsUpdated"})})}};function c(t,e){return!1!==(0,h.Eh)(e.enabled,t)&&void 0===t.state.data&&("error"!==t.state.status||!1!==e.retryOnMount)||void 0!==t.state.data&&l(t,e,e.refetchOnMount)}function l(t,e,s){if(!1!==(0,h.Eh)(e.enabled,t)&&"static"!==(0,h.d2)(e.staleTime,t)){let r="function"==typeof s?s(t):s;return"always"===r||!1!==r&&p(t,e)}return!1}function d(t,e,s,r){return(t!==e||!1===(0,h.Eh)(r.enabled,t))&&(!s.suspense||"error"!==t.state.status)&&p(t,s)}function p(t,e){return!1!==(0,h.Eh)(e.enabled,t)&&t.isStaleByTime((0,h.d2)(e.staleTime,t))}var f=s(12115),y=s(26715);s(95155);var R=f.createContext(function(){let t=!1;return{clearReset:()=>{t=!1},reset:()=>{t=!0},isReset:()=>t}}()),b=()=>f.useContext(R),m=(t,e)=>{(t.suspense||t.throwOnError||t.experimental_prefetchInRender)&&!e.isReset()&&(t.retryOnMount=!1)},v=t=>{f.useEffect(()=>{t.clearReset()},[t])},Q=t=>{let{result:e,errorResetBoundary:s,throwOnError:r,query:i,suspense:n}=t;return e.isError&&!s.isReset()&&!e.isFetching&&i&&(n&&void 0===e.data||(0,h.GU)(r,[e.error,i]))},O=f.createContext(!1),g=()=>f.useContext(O);O.Provider;var I=t=>{if(t.suspense){let e=t=>"static"===t?t:Math.max(t??1e3,1e3),s=t.staleTime;t.staleTime="function"==typeof s?(...t)=>e(s(...t)):e(s),"number"==typeof t.gcTime&&(t.gcTime=Math.max(t.gcTime,1e3))}},E=(t,e)=>t.isLoading&&t.isFetching&&!e,S=(t,e)=>t?.suspense&&e.isPending,C=(t,e,s)=>e.fetchOptimistic(t).catch(()=>{s.clearReset()});function T(t,e){return function(t,e,s){var r,n,u,a,o;let c=g(),l=b(),d=(0,y.jE)(s),p=d.defaultQueryOptions(t);null==(n=d.getDefaultOptions().queries)||null==(r=n._experimental_beforeQuery)||r.call(n,p),p._optimisticResults=c?"isRestoring":"optimistic",I(p),m(p,l),v(l);let R=!d.getQueryCache().get(p.queryHash),[O]=f.useState(()=>new e(d,p)),T=O.getOptimisticResult(p),M=!c&&!1!==t.subscribed;if(f.useSyncExternalStore(f.useCallback(t=>{let e=M?O.subscribe(i.jG.batchCalls(t)):h.lQ;return O.updateResult(),e},[O,M]),()=>O.getCurrentResult(),()=>O.getCurrentResult()),f.useEffect(()=>{O.setOptions(p)},[p,O]),S(p,T))throw C(p,O,l);if(Q({result:T,errorResetBoundary:l,throwOnError:p.throwOnError,query:d.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw T.error;if(null==(a=d.getDefaultOptions().queries)||null==(u=a._experimental_afterQuery)||u.call(a,p,T),p.experimental_prefetchInRender&&!h.S$&&E(T,c)){let t=R?C(p,O,l):null==(o=d.getQueryCache().get(p.queryHash))?void 0:o.promise;null==t||t.catch(h.lQ).finally(()=>{O.updateResult()})}return p.notifyOnChangeProps?T:O.trackResult(T)}(t,o,e)}},74783:(t,e,s)=>{s.d(e,{A:()=>r});let r=(0,s(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])}}]);