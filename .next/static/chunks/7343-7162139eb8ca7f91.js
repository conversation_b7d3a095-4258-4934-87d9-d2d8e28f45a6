"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7343],{381:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19178:(e,t,n)=>{n.d(t,{qW:()=>f});var r,o=n(12115),i=n(85185),a=n(63655),l=n(6101),u=n(39033),c=n(95155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(d),[S,R]=o.useState(null),C=null!=(f=null==S?void 0:S.ownerDocument)?f:null==(n=globalThis)?void 0:n.document,[,M]=o.useState({}),A=(0,l.s)(t,e=>R(e)),T=Array.from(E.layers),[k]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),P=T.indexOf(k),O=S?T.indexOf(S):-1,L=E.layersWithOutsidePointerEventsDisabled.size>0,D=O>=P,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));D&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},C),N=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},C);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{O===E.layers.size-1&&(null==h||h(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},C),o.useEffect(()=>{if(S)return v&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(S)),E.layers.add(S),p(),()=>{v&&1===E.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=r)}},[S,C,v,E]),o.useEffect(()=>()=>{S&&(E.layers.delete(S),E.layersWithOutsidePointerEventsDisabled.delete(S),p())},[S,E]),o.useEffect(()=>{let e=()=>M({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.sG.div,{...x,ref:A,style:{pointerEvents:L?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.hO)(i,l):i.dispatchEvent(l)}f.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,c.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},25519:(e,t,n)=>{n.d(t,{n:()=>d});var r=n(12115),o=n(6101),i=n(63655),a=n(39033),l=n(95155),u="focusScope.autoFocusOnMount",c="focusScope.autoFocusOnUnmount",s={bubbles:!1,cancelable:!0},d=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:d=!1,onMountAutoFocus:h,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(h),E=(0,a.c)(g),S=r.useRef(null),R=(0,o.s)(t,e=>b(e)),C=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(d){let e=function(e){if(C.paused||!w)return;let t=e.target;w.contains(t)?S.current=t:m(S.current,{select:!0})},t=function(e){if(C.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||m(S.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[d,w,C.paused]),r.useEffect(()=>{if(w){v.add(C);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,s);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(f(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(c,s);w.addEventListener(c,E),w.dispatchEvent(t),t.defaultPrevented||m(null!=e?e:document.body,{select:!0}),w.removeEventListener(c,E),v.remove(C)},0)}}},[w,x,E,C]);let M=r.useCallback(e=>{if(!n&&!d||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=f(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(o,{select:!0})):r===t&&e.preventDefault()}},[n,d,C.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:R,onKeyDown:M})});function f(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=h(e,t)).unshift(t)},remove(t){var n;null==(n=(e=h(e,t))[0])||n.resume()}}}();function h(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},34378:(e,t,n)=>{n.d(t,{Z:()=>u});var r=n(12115),o=n(47650),i=n(63655),a=n(52712),l=n(95155),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...s}=e,[d,f]=r.useState(!1);(0,a.N)(()=>f(!0),[]);let p=c||d&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,l.jsx)(i.sG.div,{...s,ref:t}),p):null});u.displayName="Portal"},35152:(e,t,n)=>{n.d(t,{Mz:()=>te,i3:()=>tn,UC:()=>tt,bL:()=>e4,Bk:()=>eX});var r=n(12115);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,l=Math.round,u=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},d={start:"end",end:"start"};function f(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function m(e){return e.split("-")[1]}function v(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}let g=new Set(["top","bottom"]);function y(e){return g.has(p(e))?"y":"x"}function w(e){return e.replace(/start|end/g,e=>d[e])}let b=["left","right"],x=["right","left"],E=["top","bottom"],S=["bottom","top"];function R(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function C(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function M(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function A(e,t,n){let r,{reference:o,floating:i}=e,a=y(t),l=v(y(t)),u=h(l),c=p(t),s="y"===a,d=o.x+o.width/2-i.width/2,f=o.y+o.height/2-i.height/2,g=o[u]/2-i[u]/2;switch(c){case"top":r={x:d,y:o.y-i.height};break;case"bottom":r={x:d,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:f};break;case"left":r={x:o.x-i.width,y:f};break;default:r={x:o.x,y:o.y}}switch(m(t)){case"start":r[l]-=g*(n&&s?-1:1);break;case"end":r[l]+=g*(n&&s?-1:1)}return r}let T=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:d}=A(c,r,u),f=r,p={},m=0;for(let n=0;n<l.length;n++){let{name:i,fn:v}=l[n],{x:h,y:g,data:y,reset:w}=await v({x:s,y:d,initialPlacement:r,placement:f,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=h?h:s,d=null!=g?g:d,p={...p,[i]:{...p[i],...y}},w&&m<=50&&(m++,"object"==typeof w&&(w.placement&&(f=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:d}=A(c,f,u)),n=-1)}return{x:s,y:d,placement:f,strategy:o,middlewareData:p}};async function k(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:d="floating",altBoundary:p=!1,padding:m=0}=f(t,e),v=C(m),h=l[p?"floating"===d?"reference":"floating":d],g=M(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(h)))||n?h:h.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:s,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),b=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},x=M(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:w,strategy:u}):y);return{top:(g.top-x.top+v.top)/b.y,bottom:(x.bottom-g.bottom+v.bottom)/b.y,left:(g.left-x.left+v.left)/b.x,right:(x.right-g.right+v.right)/b.x}}function P(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function O(e){return o.some(t=>e[t]>=0)}let L=new Set(["left","top"]);async function D(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),l=m(n),u="y"===y(n),c=L.has(a)?-1:1,s=i&&u?-1:1,d=f(t,e),{mainAxis:v,crossAxis:h,alignmentAxis:g}="number"==typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&"number"==typeof g&&(h="end"===l?-1*g:g),u?{x:h*s,y:v*c}:{x:v*c,y:h*s}}function j(){return"undefined"!=typeof window}function N(e){return F(e)?(e.nodeName||"").toLowerCase():"#document"}function I(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function _(e){var t;return null==(t=(F(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function F(e){return!!j()&&(e instanceof Node||e instanceof I(e).Node)}function W(e){return!!j()&&(e instanceof Element||e instanceof I(e).Element)}function B(e){return!!j()&&(e instanceof HTMLElement||e instanceof I(e).HTMLElement)}function K(e){return!!j()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof I(e).ShadowRoot)}let G=new Set(["inline","contents"]);function H(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ee(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!G.has(o)}let z=new Set(["table","td","th"]),U=[":popover-open",":modal"];function V(e){return U.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let X=["transform","translate","scale","rotate","perspective"],q=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function J(e){let t=Z(),n=W(e)?ee(e):e;return X.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||q.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let $=new Set(["html","body","#document"]);function Q(e){return $.has(N(e))}function ee(e){return I(e).getComputedStyle(e)}function et(e){return W(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function en(e){if("html"===N(e))return e;let t=e.assignedSlot||e.parentNode||K(e)&&e.host||_(e);return K(t)?t.host:t}function er(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=en(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:B(n)&&H(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=I(o);if(i){let e=eo(a);return t.concat(a,a.visualViewport||[],H(o)?o:[],e&&n?er(e):[])}return t.concat(o,er(o,[],n))}function eo(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ei(e){let t=ee(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=B(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,u=l(n)!==i||l(r)!==a;return u&&(n=i,r=a),{width:n,height:r,$:u}}function ea(e){return W(e)?e:e.contextElement}function el(e){let t=ea(e);if(!B(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=ei(t),a=(i?l(n.width):n.width)/r,u=(i?l(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),u&&Number.isFinite(u)||(u=1),{x:a,y:u}}let eu=c(0);function ec(e){let t=I(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eu}function es(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=ea(e),l=c(1);t&&(r?W(r)&&(l=el(r)):l=el(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===I(a))&&o)?ec(a):c(0),s=(i.left+u.x)/l.x,d=(i.top+u.y)/l.y,f=i.width/l.x,p=i.height/l.y;if(a){let e=I(a),t=r&&W(r)?I(r):r,n=e,o=eo(n);for(;o&&r&&t!==n;){let e=el(o),t=o.getBoundingClientRect(),r=ee(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,d*=e.y,f*=e.x,p*=e.y,s+=i,d+=a,o=eo(n=I(o))}}return M({width:f,height:p,x:s,y:d})}function ed(e,t){let n=et(e).scrollLeft;return t?t.left+n:es(_(e)).left+n}function ef(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ed(e,r)),y:r.top+t.scrollTop}}let ep=new Set(["absolute","fixed"]);function em(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=I(e),r=_(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=Z();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=_(e),n=et(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ed(e),u=-n.scrollTop;return"rtl"===ee(r).direction&&(l+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:l,y:u}}(_(e));else if(W(t))r=function(e,t){let n=es(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=B(e)?el(e):c(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=ec(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return M(r)}function ev(e){return"static"===ee(e).position}function eh(e,t){if(!B(e)||"fixed"===ee(e).position)return null;if(t)return t(e);let n=e.offsetParent;return _(e)===n&&(n=n.ownerDocument.body),n}function eg(e,t){var n;let r=I(e);if(V(e))return r;if(!B(e)){let t=en(e);for(;t&&!Q(t);){if(W(t)&&!ev(t))return t;t=en(t)}return r}let o=eh(e,t);for(;o&&(n=o,z.has(N(n)))&&ev(o);)o=eh(o,t);return o&&Q(o)&&ev(o)&&!J(o)?r:o||function(e){let t=en(e);for(;B(t)&&!Q(t);){if(J(t))return t;if(V(t))break;t=en(t)}return null}(e)||r}let ey=async function(e){let t=this.getOffsetParent||eg,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=B(t),o=_(t),i="fixed"===n,a=es(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=c(0);if(r||!r&&!i)if(("body"!==N(t)||H(o))&&(l=et(t)),r){let e=es(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ed(o));i&&!r&&o&&(u.x=ed(o));let s=!o||r||i?c(0):ef(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ew={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=_(r),l=!!t&&V(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=c(1),d=c(0),f=B(r);if((f||!f&&!i)&&(("body"!==N(r)||H(a))&&(u=et(r)),B(r))){let e=es(r);s=el(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}let p=!a||f||i?c(0):ef(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+d.x+p.x,y:n.y*s.y-u.scrollTop*s.y+d.y+p.y}},getDocumentElement:_,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,l=[..."clippingAncestors"===n?V(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=er(e,[],!1).filter(e=>W(e)&&"body"!==N(e)),o=null,i="fixed"===ee(e).position,a=i?en(e):e;for(;W(a)&&!Q(a);){let t=ee(a),n=J(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ep.has(o.position)||H(a)&&!n&&function e(t,n){let r=en(t);return!(r===n||!W(r)||Q(r))&&("fixed"===ee(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=en(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],u=l[0],c=l.reduce((e,n)=>{let r=em(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},em(t,u,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:eg,getElementRects:ey,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=ei(e);return{width:t,height:n}},getScale:el,isElement:W,isRTL:function(e){return"rtl"===ee(e).direction}};function eb(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ex=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:l,platform:u,elements:c,middlewareData:s}=t,{element:d,padding:p=0}=f(e,t)||{};if(null==d)return{};let g=C(p),w={x:n,y:r},b=v(y(o)),x=h(b),E=await u.getDimensions(d),S="y"===b,R=S?"clientHeight":"clientWidth",M=l.reference[x]+l.reference[b]-w[b]-l.floating[x],A=w[b]-l.reference[b],T=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),k=T?T[R]:0;k&&await (null==u.isElement?void 0:u.isElement(T))||(k=c.floating[R]||l.floating[x]);let P=k/2-E[x]/2-1,O=i(g[S?"top":"left"],P),L=i(g[S?"bottom":"right"],P),D=k-E[x]-L,j=k/2-E[x]/2+(M/2-A/2),N=a(O,i(j,D)),I=!s.arrow&&null!=m(o)&&j!==N&&l.reference[x]/2-(j<O?O:L)-E[x]/2<0,_=I?j<O?j-O:j-D:0;return{[b]:w[b]+_,data:{[b]:N,centerOffset:j-N-_,...I&&{alignmentOffset:_}},reset:I}}}),eE=(e,t,n)=>{let r=new Map,o={platform:ew,...n},i={...o.platform,_c:r};return T(e,t,{...o,platform:i})};var eS=n(47650),eR="undefined"!=typeof document?r.useLayoutEffect:function(){};function eC(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eC(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eC(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eM(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eA(e,t){let n=eM(e);return Math.round(t*n)/n}function eT(e){let t=r.useRef(e);return eR(()=>{t.current=e}),t}let ek=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ex({element:n.current,padding:r}).fn(t):{}:n?ex({element:n,padding:r}).fn(t):{}}}),eP=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await D(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}),eO=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=f(e,t),d={x:n,y:r},m=await k(t,s),h=y(p(o)),g=v(h),w=d[g],b=d[h];if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=w+m[e],r=w-m[t];w=a(n,i(w,r))}if(u){let e="y"===h?"top":"left",t="y"===h?"bottom":"right",n=b+m[e],r=b-m[t];b=a(n,i(b,r))}let x=c.fn({...t,[g]:w,[h]:b});return{...x,data:{x:x.x-n,y:x.y-r,enabled:{[g]:l,[h]:u}}}}}}(e),options:[e,t]}),eL=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=f(e,t),s={x:n,y:r},d=y(o),m=v(d),h=s[m],g=s[d],w=f(l,t),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+b.mainAxis,n=i.reference[m]+i.reference[e]-b.mainAxis;h<t?h=t:h>n&&(h=n)}if(c){var x,E;let e="y"===m?"width":"height",t=L.has(p(o)),n=i.reference[d]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[d])||0)+(t?0:b.crossAxis),r=i.reference[d]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[d])||0)-(t?b.crossAxis:0);g<n?g=n:g>r&&(g=r)}return{[m]:h,[d]:g}}}}(e),options:[e,t]}),eD=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:s,platform:d,elements:g}=t,{mainAxis:C=!0,crossAxis:M=!0,fallbackPlacements:A,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:P="none",flipAlignment:O=!0,...L}=f(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let D=p(l),j=y(s),N=p(s)===s,I=await (null==d.isRTL?void 0:d.isRTL(g.floating)),_=A||(N||!O?[R(s)]:function(e){let t=R(e);return[w(e),t,w(t)]}(s)),F="none"!==P;!A&&F&&_.push(...function(e,t,n,r){let o=m(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?x:b;return t?b:x;case"left":case"right":return t?E:S;default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(w)))),i}(s,O,P,I));let W=[s,..._],B=await k(t,L),K=[],G=(null==(r=u.flip)?void 0:r.overflows)||[];if(C&&K.push(B[D]),M){let e=function(e,t,n){void 0===n&&(n=!1);let r=m(e),o=v(y(e)),i=h(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=R(a)),[a,R(a)]}(l,c,I);K.push(B[e[0]],B[e[1]])}if(G=[...G,{placement:l,overflows:K}],!K.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=W[e];if(t&&("alignment"!==M||j===y(t)||G.every(e=>y(e.placement)!==j||e.overflows[0]>0)))return{data:{index:e,overflows:G},reset:{placement:t}};let n=null==(i=G.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(T){case"bestFit":{let e=null==(a=G.filter(e=>{if(F){let t=y(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ej=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,l,{placement:u,rects:c,platform:s,elements:d}=t,{apply:v=()=>{},...h}=f(e,t),g=await k(t,h),w=p(u),b=m(u),x="y"===y(u),{width:E,height:S}=c.floating;"top"===w||"bottom"===w?(o=w,l=b===(await (null==s.isRTL?void 0:s.isRTL(d.floating))?"start":"end")?"left":"right"):(l=w,o="end"===b?"top":"bottom");let R=S-g.top-g.bottom,C=E-g.left-g.right,M=i(S-g[o],R),A=i(E-g[l],C),T=!t.middlewareData.shift,P=M,O=A;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(O=C),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=R),T&&!b){let e=a(g.left,0),t=a(g.right,0),n=a(g.top,0),r=a(g.bottom,0);x?O=E-2*(0!==e||0!==t?e+t:a(g.left,g.right)):P=S-2*(0!==n||0!==r?n+r:a(g.top,g.bottom))}await v({...t,availableWidth:O,availableHeight:P});let L=await s.getDimensions(d.floating);return E!==L.width||S!==L.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eN=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=f(e,t);switch(r){case"referenceHidden":{let e=P(await k(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:O(e)}}}case"escaped":{let e=P(await k(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:O(e)}}}default:return{}}}}}(e),options:[e,t]}),eI=(e,t)=>({...ek(e),options:[e,t]});var e_=n(63655),eF=n(95155),eW=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eF.jsx)(e_.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eF.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eW.displayName="Arrow";var eB=n(6101),eK=n(46081),eG=n(39033),eH=n(52712),ez=n(11275),eU="Popper",[eV,eX]=(0,eK.A)(eU),[eq,eY]=eV(eU),eJ=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eF.jsx)(eq,{scope:t,anchor:o,onAnchorChange:i,children:n})};eJ.displayName=eU;var eZ="PopperAnchor",e$=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eY(eZ,n),l=r.useRef(null),u=(0,eB.s)(t,l);return r.useEffect(()=>{a.onAnchorChange((null==o?void 0:o.current)||l.current)}),o?null:(0,eF.jsx)(e_.sG.div,{...i,ref:u})});e$.displayName=eZ;var eQ="PopperContent",[e0,e1]=eV(eQ),e2=r.forwardRef((e,t)=>{var n,o,l,c,s,d,f,p;let{__scopePopper:m,side:v="bottom",sideOffset:h=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:S="partial",hideWhenDetached:R=!1,updatePositionStrategy:C="optimized",onPlaced:M,...A}=e,T=eY(eQ,m),[k,P]=r.useState(null),O=(0,eB.s)(t,e=>P(e)),[L,D]=r.useState(null),j=(0,ez.X)(L),N=null!=(f=null==j?void 0:j.width)?f:0,I=null!=(p=null==j?void 0:j.height)?p:0,F="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},W=Array.isArray(x)?x:[x],B=W.length>0,K={padding:F,boundary:W.filter(e9),altBoundary:B},{refs:G,floatingStyles:H,placement:z,isPositioned:U,middlewareData:V}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[d,f]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,m]=r.useState(o);eC(p,o)||m(o);let[v,h]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==S.current&&(S.current=e,h(e))},[]),b=r.useCallback(e=>{e!==R.current&&(R.current=e,y(e))},[]),x=a||v,E=l||g,S=r.useRef(null),R=r.useRef(null),C=r.useRef(d),M=null!=c,A=eT(c),T=eT(i),k=eT(s),P=r.useCallback(()=>{if(!S.current||!R.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),eE(S.current,R.current,e).then(e=>{let t={...e,isPositioned:!1!==k.current};O.current&&!eC(C.current,t)&&(C.current=t,eS.flushSync(()=>{f(t)}))})},[p,t,n,T,k]);eR(()=>{!1===s&&C.current.isPositioned&&(C.current.isPositioned=!1,f(e=>({...e,isPositioned:!1})))},[s]);let O=r.useRef(!1);eR(()=>(O.current=!0,()=>{O.current=!1}),[]),eR(()=>{if(x&&(S.current=x),E&&(R.current=E),x&&E){if(A.current)return A.current(x,E,P);P()}},[x,E,P,A,M]);let L=r.useMemo(()=>({reference:S,floating:R,setReference:w,setFloating:b}),[w,b]),D=r.useMemo(()=>({reference:x,floating:E}),[x,E]),j=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=eA(D.floating,d.x),r=eA(D.floating,d.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eM(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,D.floating,d.x,d.y]);return r.useMemo(()=>({...d,update:P,refs:L,elements:D,floatingStyles:j}),[d,P,L,D,j])}({strategy:"fixed",placement:v+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:l=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:d="function"==typeof IntersectionObserver,animationFrame:f=!1}=r,p=ea(e),m=l||c?[...p?er(p):[],...er(t)]:[];m.forEach(e=>{l&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let v=p&&d?function(e,t){let n,r=null,o=_(e);function l(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,d){void 0===s&&(s=!1),void 0===d&&(d=1),l();let f=e.getBoundingClientRect(),{left:p,top:m,width:v,height:h}=f;if(s||t(),!v||!h)return;let g=u(m),y=u(o.clientWidth-(p+v)),w={rootMargin:-g+"px "+-y+"px "+-u(o.clientHeight-(m+h))+"px "+-u(p)+"px",threshold:a(0,i(1,d))||1},b=!0;function x(t){let r=t[0].intersectionRatio;if(r!==d){if(!b)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||eb(f,e.getBoundingClientRect())||c(),b=!1}try{r=new IntersectionObserver(x,{...w,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(x,w)}r.observe(e)}(!0),l}(p,n):null,h=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!f&&g.observe(p),g.observe(t));let y=f?es(e):null;return f&&function t(){let r=es(e);y&&!eb(y,r)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;m.forEach(e=>{l&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==v||v(),null==(e=g)||e.disconnect(),g=null,f&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===C})},elements:{reference:T.anchor},middleware:[eP({mainAxis:h+I,alignmentAxis:y}),b&&eO({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?eL():void 0,...K}),b&&eD({...K}),ej({...K,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),L&&eI({element:L,padding:w}),e8({arrowWidth:N,arrowHeight:I}),R&&eN({strategy:"referenceHidden",...K})]}),[X,q]=e6(z),Y=(0,eG.c)(M);(0,eH.N)(()=>{U&&(null==Y||Y())},[U,Y]);let J=null==(n=V.arrow)?void 0:n.x,Z=null==(o=V.arrow)?void 0:o.y,$=(null==(l=V.arrow)?void 0:l.centerOffset)!==0,[Q,ee]=r.useState();return(0,eH.N)(()=>{k&&ee(window.getComputedStyle(k).zIndex)},[k]),(0,eF.jsx)("div",{ref:G.setFloating,"data-radix-popper-content-wrapper":"",style:{...H,transform:U?H.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Q,"--radix-popper-transform-origin":[null==(c=V.transformOrigin)?void 0:c.x,null==(s=V.transformOrigin)?void 0:s.y].join(" "),...(null==(d=V.hide)?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eF.jsx)(e0,{scope:m,placedSide:X,onArrowChange:D,arrowX:J,arrowY:Z,shouldHideArrow:$,children:(0,eF.jsx)(e_.sG.div,{"data-side":X,"data-align":q,...A,ref:O,style:{...A.style,animation:U?void 0:"none"}})})})});e2.displayName=eQ;var e5="PopperArrow",e3={top:"bottom",right:"left",bottom:"top",left:"right"},e7=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e1(e5,n),i=e3[o.placedSide];return(0,eF.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eF.jsx)(eW,{...r,ref:t,style:{...r.style,display:"block"}})})});function e9(e){return null!==e}e7.displayName=e5;var e8=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,d=s?0:e.arrowWidth,f=s?0:e.arrowHeight,[p,m]=e6(l),v={start:"0%",center:"50%",end:"100%"}[m],h=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+d/2,g=(null!=(a=null==(o=c.arrow)?void 0:o.y)?a:0)+f/2,y="",w="";return"bottom"===p?(y=s?v:"".concat(h,"px"),w="".concat(-f,"px")):"top"===p?(y=s?v:"".concat(h,"px"),w="".concat(u.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),w=s?v:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+f,"px"),w=s?v:"".concat(g,"px")),{data:{x:y,y:w}}}});function e6(e){let[t,n="center"]=e.split("-");return[t,n]}var e4=eJ,te=e$,tt=e2,tn=e7},35695:(e,t,n)=>{var r=n(18999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}}),n.o(r,"useSelectedLayoutSegments")&&n.d(t,{useSelectedLayoutSegments:function(){return r.useSelectedLayoutSegments}})},37328:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function i(e,t,n){var o=r(e,t,"set");if(o.set)o.set.call(e,n);else{if(!o.writable)throw TypeError("attempted to set read only private field");o.value=n}return n}n.d(t,{N:()=>f});var a,l=n(12115),u=n(46081),c=n(6101),s=n(99708),d=n(95155);function f(e){let t=e+"CollectionProvider",[n,r]=(0,u.A)(t),[o,i]=n(t,{collectionRef:{current:null},itemMap:new Map}),a=e=>{let{scope:t,children:n}=e,r=l.useRef(null),i=l.useRef(new Map).current;return(0,d.jsx)(o,{scope:t,itemMap:i,collectionRef:r,children:n})};a.displayName=t;let f=e+"CollectionSlot",p=(0,s.TL)(f),m=l.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=i(f,n),a=(0,c.s)(t,o.collectionRef);return(0,d.jsx)(p,{ref:a,children:r})});m.displayName=f;let v=e+"CollectionItemSlot",h="data-radix-collection-item",g=(0,s.TL)(v),y=l.forwardRef((e,t)=>{let{scope:n,children:r,...o}=e,a=l.useRef(null),u=(0,c.s)(t,a),s=i(v,n);return l.useEffect(()=>(s.itemMap.set(a,{ref:a,...o}),()=>void s.itemMap.delete(a))),(0,d.jsx)(g,{...{[h]:""},ref:u,children:r})});return y.displayName=v,[{Provider:a,Slot:m,ItemSlot:y},function(t){let n=i(e+"CollectionConsumer",t);return l.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var p=new WeakMap;function m(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=v(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function v(e){return e!=e||0===e?0:Math.trunc(e)}a=new WeakMap},38168:(e,t,n)=>{n.d(t,{Eq:()=>s});var r=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},o=new WeakMap,i=new WeakMap,a={},l=0,u=function(e){return e&&(e.host||u(e.parentNode))},c=function(e,t,n,r){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});a[n]||(a[n]=new WeakMap);var s=a[n],d=[],f=new Set,p=new Set(c),m=function(e){!e||f.has(e)||(f.add(e),m(e.parentNode))};c.forEach(m);var v=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(f.has(e))v(e);else try{var t=e.getAttribute(r),a=null!==t&&"false"!==t,l=(o.get(e)||0)+1,u=(s.get(e)||0)+1;o.set(e,l),s.set(e,u),d.push(e),1===l&&a&&i.set(e,!0),1===u&&e.setAttribute(n,"true"),a||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return v(t),f.clear(),l++,function(){d.forEach(function(e){var t=o.get(e)-1,a=s.get(e)-1;o.set(e,t),s.set(e,a),t||(i.has(e)||e.removeAttribute(r),i.delete(e)),a||e.removeAttribute(n)}),--l||(o=new WeakMap,o=new WeakMap,i=new WeakMap,a={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var o=Array.from(Array.isArray(e)?e:[e]),i=t||r(e);return i?(o.push.apply(o,Array.from(i.querySelectorAll("[aria-live], script"))),c(o,i,n,"aria-hidden")):function(){return null}}},39033:(e,t,n)=>{n.d(t,{c:()=>o});var r=n(12115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},46786:(e,t,n)=>{n.d(t,{Zr:()=>f,eh:()=>s,lt:()=>u});let r=new Map,o=e=>{let t=r.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},i=(e,t,n)=>{if(void 0===e)return{type:"untracked",connection:t.connect(n)};let o=r.get(n.name);if(o)return{type:"tracked",store:e,...o};let i={connection:t.connect(n),stores:{}};return r.set(n.name,i),{type:"tracked",store:e,...i}},a=(e,t)=>{if(void 0===t)return;let n=r.get(e);n&&(delete n.stores[t],0===Object.keys(n.stores).length&&r.delete(e))},l=e=>{var t,n;if(!e)return;let r=e.split("\n"),o=r.findIndex(e=>e.includes("api.setState"));if(o<0)return;let i=(null==(t=r[o+1])?void 0:t.trim())||"";return null==(n=/.+ (.+) .+/.exec(i))?void 0:n[1]},u=(e,t={})=>(n,r,u)=>{let s,{enabled:d,anonymousActionType:f,store:p,...m}=t;try{s=(null==d||d)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!s)return e(n,r,u);let{connection:v,...h}=i(p,s,m),g=!0;u.setState=(e,t,i)=>{let a=n(e,t);if(!g)return a;let c=void 0===i?{type:f||l(Error().stack)||"anonymous"}:"string"==typeof i?{type:i}:i;return void 0===p?null==v||v.send(c,r()):null==v||v.send({...c,type:`${p}/${c.type}`},{...o(m.name),[p]:u.getState()}),a},u.devtools={cleanup:()=>{v&&"function"==typeof v.unsubscribe&&v.unsubscribe(),a(m.name,p)}};let y=(...e)=>{let t=g;g=!1,n(...e),g=t},w=e(u.setState,r,u);if("untracked"===h.type?null==v||v.init(w):(h.stores[h.store]=u,null==v||v.init(Object.fromEntries(Object.entries(h.stores).map(([e,t])=>[e,e===h.store?w:t.getState()])))),u.dispatchFromDevtools&&"function"==typeof u.dispatch){let e=!1,t=u.dispatch;u.dispatch=(...n)=>{"__setState"!==n[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...n)}}return v.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return c(e.payload,e=>{if("__setState"===e.type){if(void 0===p)return void y(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[p];return void(null==t||JSON.stringify(u.getState())!==JSON.stringify(t)&&y(t))}u.dispatchFromDevtools&&"function"==typeof u.dispatch&&u.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(y(w),void 0===p)return null==v?void 0:v.init(u.getState());return null==v?void 0:v.init(o(m.name));case"COMMIT":if(void 0===p){null==v||v.init(u.getState());break}return null==v?void 0:v.init(o(m.name));case"ROLLBACK":return c(e.state,e=>{if(void 0===p){y(e),null==v||v.init(u.getState());return}y(e[p]),null==v||v.init(o(m.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return c(e.state,e=>{if(void 0===p)return void y(e);JSON.stringify(u.getState())!==JSON.stringify(e[p])&&y(e[p])});case"IMPORT_STATE":{let{nextLiftedState:n}=e.payload,r=null==(t=n.computedStates.slice(-1)[0])?void 0:t.state;if(!r)return;void 0===p?y(r):y(r[p]),null==v||v.send(null,n);break}case"PAUSE_RECORDING":return g=!g}return}}),w},c=(e,t)=>{let n;try{n=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==n&&t(n)},s=e=>(t,n,r)=>{let o=r.subscribe;return r.subscribe=(e,t,n)=>{let i=e;if(t){let o=(null==n?void 0:n.equalityFn)||Object.is,a=e(r.getState());i=n=>{let r=e(n);if(!o(a,r)){let e=a;t(a=r,e)}},(null==n?void 0:n.fireImmediately)&&t(a,a)}return o(i)},e(t,n,r)},d=e=>t=>{try{let n=e(t);if(n instanceof Promise)return n;return{then:e=>d(e)(n),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>d(t)(e)}}},f=(e,t)=>(n,r,o)=>{let i,a={storage:function(e,t){let n;try{n=e()}catch(e){return}return{getItem:e=>{var t;let r=e=>null===e?null:JSON.parse(e,void 0),o=null!=(t=n.getItem(e))?t:null;return o instanceof Promise?o.then(r):r(o)},setItem:(e,t)=>n.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>n.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,u=new Set,c=new Set,s=a.storage;if(!s)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${a.name}', the given storage is currently unavailable.`),n(...e)},r,o);let f=()=>{let e=a.partialize({...r()});return s.setItem(a.name,{state:e,version:a.version})},p=o.setState;o.setState=(e,t)=>{p(e,t),f()};let m=e((...e)=>{n(...e),f()},r,o);o.getInitialState=()=>m;let v=()=>{var e,t;if(!s)return;l=!1,u.forEach(e=>{var t;return e(null!=(t=r())?t:m)});let o=(null==(t=a.onRehydrateStorage)?void 0:t.call(a,null!=(e=r())?e:m))||void 0;return d(s.getItem.bind(s))(a.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===a.version)return[!1,e.state];else{if(a.migrate){let t=a.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[o,l]=e;if(n(i=a.merge(l,null!=(t=r())?t:m),!0),o)return f()}).then(()=>{null==o||o(i,void 0),i=r(),l=!0,c.forEach(e=>e(i))}).catch(e=>{null==o||o(void 0,e)})};return o.persist={setOptions:e=>{a={...a,...e},e.storage&&(s=e.storage)},clearStorage:()=>{null==s||s.removeItem(a.name)},getOptions:()=>a,rehydrate:()=>v(),hasHydrated:()=>l,onHydrate:e=>(u.add(e),()=>{u.delete(e)}),onFinishHydration:e=>(c.add(e),()=>{c.delete(e)})},a.skipHydration||v(),i||m}},48698:(e,t,n)=>{n.d(t,{UC:()=>eY,YJ:()=>eJ,q7:()=>e$,JU:()=>eZ,ZL:()=>eq,bL:()=>eV,wv:()=>eQ,l9:()=>eX});var r=n(12115),o=n(85185),i=n(6101),a=n(46081),l=n(5845),u=n(63655),c=n(37328),s=n(94315),d=n(19178),f=n(92293),p=n(25519),m=n(61285),v=n(35152),h=n(34378),g=n(28905),y=n(89196),w=n(99708),b=n(39033),x=n(38168),E=n(93795),S=n(95155),R=["Enter"," "],C=["ArrowUp","PageDown","End"],M=["ArrowDown","PageUp","Home",...C],A={ltr:[...R,"ArrowRight"],rtl:[...R,"ArrowLeft"]},T={ltr:["ArrowLeft"],rtl:["ArrowRight"]},k="Menu",[P,O,L]=(0,c.N)(k),[D,j]=(0,a.A)(k,[L,v.Bk,y.RG]),N=(0,v.Bk)(),I=(0,y.RG)(),[_,F]=D(k),[W,B]=D(k),K=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:l=!0}=e,u=N(t),[c,d]=r.useState(null),f=r.useRef(!1),p=(0,b.c)(a),m=(0,s.jH)(i);return r.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,S.jsx)(v.bL,{...u,children:(0,S.jsx)(_,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,S.jsx)(W,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:m,modal:l,children:o})})})};K.displayName=k;var G=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,S.jsx)(v.Mz,{...o,...r,ref:t})});G.displayName="MenuAnchor";var H="MenuPortal",[z,U]=D(H,{forceMount:void 0}),V=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=F(H,t);return(0,S.jsx)(z,{scope:t,forceMount:n,children:(0,S.jsx)(g.C,{present:n||i.open,children:(0,S.jsx)(h.Z,{asChild:!0,container:o,children:r})})})};V.displayName=H;var X="MenuContent",[q,Y]=D(X),J=r.forwardRef((e,t)=>{let n=U(X,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=F(X,e.__scopeMenu),a=B(X,e.__scopeMenu);return(0,S.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(g.C,{present:r||i.open,children:(0,S.jsx)(P.Slot,{scope:e.__scopeMenu,children:a.modal?(0,S.jsx)(Z,{...o,ref:t}):(0,S.jsx)($,{...o,ref:t})})})})}),Z=r.forwardRef((e,t)=>{let n=F(X,e.__scopeMenu),a=r.useRef(null),l=(0,i.s)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Eq)(e)},[]),(0,S.jsx)(ee,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),$=r.forwardRef((e,t)=>{let n=F(X,e.__scopeMenu);return(0,S.jsx)(ee,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=(0,w.TL)("MenuContent.ScrollLock"),ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:m,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,disableOutsideScroll:R,...A}=e,T=F(X,n),k=B(X,n),P=N(n),L=I(n),D=O(n),[j,_]=r.useState(null),W=r.useRef(null),K=(0,i.s)(t,W,T.onContentChange),G=r.useRef(0),H=r.useRef(""),z=r.useRef(0),U=r.useRef(null),V=r.useRef("right"),Y=r.useRef(0),J=R?E.A:r.Fragment,Z=e=>{var t,n;let r=H.current+e,o=D().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),u=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){H.current=t,window.clearTimeout(G.current),""!==t&&(G.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())};r.useEffect(()=>()=>window.clearTimeout(G.current),[]),(0,f.Oh)();let $=r.useCallback(e=>{var t,n;return V.current===(null==(t=U.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,c=a.y,s=l.x,d=l.y;c>r!=d>r&&n<(s-u)*(r-c)/(d-c)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=U.current)?void 0:n.area)},[]);return(0,S.jsx)(q,{scope:n,searchRef:H,onItemEnter:r.useCallback(e=>{$(e)&&e.preventDefault()},[$]),onItemLeave:r.useCallback(e=>{var t;$(e)||(null==(t=W.current)||t.focus(),_(null))},[$]),onTriggerLeave:r.useCallback(e=>{$(e)&&e.preventDefault()},[$]),pointerGraceTimerRef:z,onPointerGraceIntentChange:r.useCallback(e=>{U.current=e},[]),children:(0,S.jsx)(J,{...R?{as:Q,allowPinchZoom:!0}:void 0,children:(0,S.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(u,e=>{var t;e.preventDefault(),null==(t=W.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,S.jsx)(d.qW,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:w,onInteractOutside:b,onDismiss:x,children:(0,S.jsx)(y.bL,{asChild:!0,...L,dir:k.dir,orientation:"vertical",loop:a,currentTabStopId:j,onCurrentTabStopIdChange:_,onEntryFocus:(0,o.m)(m,e=>{k.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,S.jsx)(v.UC,{role:"menu","aria-orientation":"vertical","data-state":eM(T.open),"data-radix-menu-content":"",dir:k.dir,...P,...A,ref:K,style:{outline:"none",...A.style},onKeyDown:(0,o.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Z(e.key));let o=W.current;if(e.target!==o||!M.includes(e.key))return;e.preventDefault();let i=D().filter(e=>!e.disabled).map(e=>e.ref.current);C.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(G.current),H.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{let t=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(t)&&n&&(V.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});J.displayName=X;var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(u.sG.div,{role:"group",...r,ref:t})});et.displayName="MenuGroup";var en=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(u.sG.div,{...r,ref:t})});en.displayName="MenuLabel";var er="MenuItem",eo="menu.itemSelect",ei=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...l}=e,c=r.useRef(null),s=B(er,e.__scopeMenu),d=Y(er,e.__scopeMenu),f=(0,i.s)(t,c),p=r.useRef(!1);return(0,S.jsx)(ea,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==a?void 0:a(e),{once:!0}),(0,u.hO)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=""!==d.searchRef.current;n||t&&" "===e.key||R.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=er;var ea=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:l,...c}=e,s=Y(er,n),d=I(n),f=r.useRef(null),p=(0,i.s)(t,f),[m,v]=r.useState(!1),[h,g]=r.useState("");return r.useEffect(()=>{let e=f.current;if(e){var t;g((null!=(t=e.textContent)?t:"").trim())}},[c.children]),(0,S.jsx)(P.ItemSlot,{scope:n,disabled:a,textValue:null!=l?l:h,children:(0,S.jsx)(y.q7,{asChild:!0,...d,focusable:!a,children:(0,S.jsx)(u.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>s.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>v(!0)),onBlur:(0,o.m)(e.onBlur,()=>v(!1))})})})}),el=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,S.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,S.jsx)(ei,{role:"menuitemcheckbox","aria-checked":eA(n)?"mixed":n,...i,ref:t,"data-state":eT(n),onSelect:(0,o.m)(i.onSelect,()=>null==r?void 0:r(!!eA(n)||!n),{checkForDefaultPrevented:!1})})})});el.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[ec,es]=D(eu,{value:void 0,onValueChange:()=>{}}),ed=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,b.c)(r);return(0,S.jsx)(ec,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,S.jsx)(et,{...o,ref:t})})});ed.displayName=eu;var ef="MenuRadioItem",ep=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=es(ef,e.__scopeMenu),a=n===i.value;return(0,S.jsx)(ev,{scope:e.__scopeMenu,checked:a,children:(0,S.jsx)(ei,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eT(a),onSelect:(0,o.m)(r.onSelect,()=>{var e;return null==(e=i.onValueChange)?void 0:e.call(i,n)},{checkForDefaultPrevented:!1})})})});ep.displayName=ef;var em="MenuItemIndicator",[ev,eh]=D(em,{checked:!1}),eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=eh(em,n);return(0,S.jsx)(g.C,{present:r||eA(i.checked)||!0===i.checked,children:(0,S.jsx)(u.sG.span,{...o,ref:t,"data-state":eT(i.checked)})})});eg.displayName=em;var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,S.jsx)(u.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});ey.displayName="MenuSeparator";var ew=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=N(n);return(0,S.jsx)(v.i3,{...o,...r,ref:t})});ew.displayName="MenuArrow";var[eb,ex]=D("MenuSub"),eE="MenuSubTrigger",eS=r.forwardRef((e,t)=>{let n=F(eE,e.__scopeMenu),a=B(eE,e.__scopeMenu),l=ex(eE,e.__scopeMenu),u=Y(eE,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=u,f={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,S.jsx)(G,{asChild:!0,...f,children:(0,S.jsx)(ea,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eM(n.open),...e,ref:(0,i.t)(t,l.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ek(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||c.current||(u.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],l=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,t=>{let r=""!==u.searchRef.current;if(!e.disabled&&(!r||" "!==t.key)&&A[a.dir].includes(t.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),t.preventDefault()}})})})});eS.displayName=eE;var eR="MenuSubContent",eC=r.forwardRef((e,t)=>{let n=U(X,e.__scopeMenu),{forceMount:a=n.forceMount,...l}=e,u=F(X,e.__scopeMenu),c=B(X,e.__scopeMenu),s=ex(eR,e.__scopeMenu),d=r.useRef(null),f=(0,i.s)(t,d);return(0,S.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,S.jsx)(g.C,{present:a||u.open,children:(0,S.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,S.jsx)(ee,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;c.isUsingKeyboardRef.current&&(null==(t=d.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==s.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=T[c.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null==(r=s.trigger)||r.focus(),e.preventDefault()}})})})})})});function eM(e){return e?"open":"closed"}function eA(e){return"indeterminate"===e}function eT(e){return eA(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return t=>"mouse"===t.pointerType?e(t):void 0}eC.displayName=eR;var eP="DropdownMenu",[eO,eL]=(0,a.A)(eP,[j]),eD=j(),[ej,eN]=eO(eP),eI=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:u,modal:c=!0}=e,s=eD(t),d=r.useRef(null),[f,p]=(0,l.i)({prop:i,defaultProp:null!=a&&a,onChange:u,caller:eP});return(0,S.jsx)(ej,{scope:t,triggerId:(0,m.B)(),triggerRef:d,contentId:(0,m.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,S.jsx)(K,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eI.displayName=eP;var e_="DropdownMenuTrigger",eF=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,l=eN(e_,n),c=eD(n);return(0,S.jsx)(G,{asChild:!0,...c,children:(0,S.jsx)(u.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.t)(t,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eF.displayName=e_;var eW=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eD(t);return(0,S.jsx)(V,{...r,...n})};eW.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",eK=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eN(eB,n),l=eD(n),u=r.useRef(!1);return(0,S.jsx)(J,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...i,ref:t,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=a.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eK.displayName=eB;var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(et,{...o,...r,ref:t})});eG.displayName="DropdownMenuGroup";var eH=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(en,{...o,...r,ref:t})});eH.displayName="DropdownMenuLabel";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(ei,{...o,...r,ref:t})});ez.displayName="DropdownMenuItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(el,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(ed,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(ep,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(eg,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(ey,{...o,...r,ref:t})});eU.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(ew,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(eS,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eD(n);return(0,S.jsx)(eC,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eV=eI,eX=eF,eq=eW,eY=eK,eJ=eG,eZ=eH,e$=ez,eQ=eU},61285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(12115),i=n(52712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},65453:(e,t,n)=>{n.d(t,{v:()=>u});var r=n(12115);let o=e=>{let t,n=new Set,r=(e,r)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=r?r:"object"!=typeof o||null===o)?o:Object.assign({},t,o),n.forEach(n=>n(t,e))}},o=()=>t,i={setState:r,getState:o,getInitialState:()=>a,subscribe:e=>(n.add(e),()=>n.delete(e))},a=t=e(r,o,i);return i},i=e=>e?o(e):o,a=e=>e,l=e=>{let t=i(e),n=e=>(function(e,t=a){let n=r.useSyncExternalStore(e.subscribe,r.useCallback(()=>t(e.getState()),[e,t]),r.useCallback(()=>t(e.getInitialState()),[e,t]));return r.useDebugValue(n),n})(t,e);return Object.assign(n,t),n},u=e=>e?l(e):l},75525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},89196:(e,t,n)=>{n.d(t,{RG:()=>x,bL:()=>P,q7:()=>O});var r=n(12115),o=n(85185),i=n(37328),a=n(6101),l=n(46081),u=n(61285),c=n(63655),s=n(39033),d=n(5845),f=n(94315),p=n(95155),m="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[g,y,w]=(0,i.N)(h),[b,x]=(0,l.A)(h,[w]),[E,S]=b(h),R=r.forwardRef((e,t)=>(0,p.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(C,{...e,ref:t})})}));R.displayName=h;var C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:u,currentTabStopId:g,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:x,preventScrollOnEntryFocus:S=!1,...R}=e,C=r.useRef(null),M=(0,a.s)(t,C),A=(0,f.jH)(u),[T,P]=(0,d.i)({prop:g,defaultProp:null!=w?w:null,onChange:b,caller:h}),[O,L]=r.useState(!1),D=(0,s.c)(x),j=y(n),N=r.useRef(!1),[I,_]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(m,D),()=>e.removeEventListener(m,D)},[D]),(0,p.jsx)(E,{scope:n,orientation:i,dir:A,loop:l,currentTabStopId:T,onItemFocus:r.useCallback(e=>P(e),[P]),onItemShiftTab:r.useCallback(()=>L(!0),[]),onFocusableItemAdd:r.useCallback(()=>_(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>_(e=>e-1),[]),children:(0,p.jsx)(c.sG.div,{tabIndex:O||0===I?-1:0,"data-orientation":i,...R,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!N.current;if(e.target===e.currentTarget&&t&&!O){let t=new CustomEvent(m,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=j().filter(e=>e.focusable);k([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),S)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>L(!1))})})}),M="RovingFocusGroupItem",A=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:l,children:s,...d}=e,f=(0,u.B)(),m=l||f,v=S(M,n),h=v.currentTabStopId===m,w=y(n),{onFocusableItemAdd:b,onFocusableItemRemove:x,currentTabStopId:E}=v;return r.useEffect(()=>{if(i)return b(),()=>x()},[i,b,x]),(0,p.jsx)(g.ItemSlot,{scope:n,id:m,focusable:i,active:a,children:(0,p.jsx)(c.sG.span,{tabIndex:h?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?v.onItemFocus(m):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(m)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return T[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>k(n))}}),children:"function"==typeof s?s({isCurrentTabStop:h,hasTabStop:null!=E}):s})})});A.displayName=M;var T={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function k(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var P=R,O=A},92293:(e,t,n)=>{n.d(t,{Oh:()=>i});var r=n(12115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},93795:(e,t,n)=>{n.d(t,{A:()=>V});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(12115)),u="right-scroll-bar-position",c="width-before-scroll-bar";function s(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var d="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,f=new WeakMap;function p(e){return e}var m=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=p),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),r={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),r}}}});return a.options=i({async:!0,ssr:!1},e),a}(),v=function(){},h=l.forwardRef(function(e,t){var n,r,o,u,c=l.useRef(null),p=l.useState({onScrollCapture:v,onWheelCapture:v,onTouchMoveCapture:v}),h=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,S=e.shards,R=e.sideCar,C=e.noRelative,M=e.noIsolation,A=e.inert,T=e.allowPinchZoom,k=e.as,P=e.gapMode,O=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),L=(n=[c,t],r=function(e){return n.forEach(function(t){return s(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,d(function(){var e=f.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||s(e,null)}),r.forEach(function(e){t.has(e)||s(e,o)})}f.set(u,n)},[n]),u),D=i(i({},O),h);return l.createElement(l.Fragment,null,E&&l.createElement(R,{sideCar:m,removeScrollBar:x,shards:S,noRelative:C,noIsolation:M,inert:A,setCallbacks:g,allowPinchZoom:!!T,lockRef:c,gapMode:P}),y?l.cloneElement(l.Children.only(w),i(i({},D),{ref:L})):l.createElement(void 0===k?"div":k,i({},D,{className:b,ref:L}),w))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:c,zeroRight:u};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},R=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},C=b(),M="data-scroll-locked",A=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(M,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(c," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(c," .").concat(c," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(M,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(M)||"0",10);return isFinite(e)?e:0},k=function(){l.useEffect(function(){return document.body.setAttribute(M,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(M):document.body.setAttribute(M,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;k();var i=l.useMemo(function(){return R(o)},[o]);return l.createElement(C,{styles:A(i,!t,o,n?"":"!important")})},O=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return O=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){O=!1}var D=!!O&&{passive:!1},j=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},N=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),I(e,r)){var o=_(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},I=function(e,t){return"v"===e?j(t,"overflowY"):j(t,"overflowX")},_=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,d=l>0,f=0,p=0;do{if(!u)break;var m=_(e,u),v=m[0],h=m[1]-m[2]-a*v;(v||h)&&I(e,u)&&(f+=h,p+=v);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&l>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},K=function(e){return e&&"current"in e?e.current:e},G=0,H=[];let z=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(G++)[0],i=l.useState(b)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(K),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],c="deltaY"in e?e.deltaY:l[1]-i[1],s=e.target,d=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=N(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=N(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:c,!0)},[]),c=l.useCallback(function(e){if(H.length&&H[H.length-1]===i){var n="deltaY"in e?B(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(K).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),d=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),f=l.useCallback(function(t){s(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){s(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return H.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,D),document.addEventListener("touchmove",c,D),document.addEventListener("touchstart",d,D),function(){H=H.filter(function(e){return e!==i}),document.removeEventListener("wheel",c,D),document.removeEventListener("touchmove",c,D),document.removeEventListener("touchstart",d,D)}},[]);var m=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},m.useMedium(r),g);var U=l.forwardRef(function(e,t){return l.createElement(h,i({},e,{ref:t,sideCar:z}))});U.classNames=h.classNames;let V=U},94315:(e,t,n)=>{n.d(t,{jH:()=>i});var r=n(12115);n(95155);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}}}]);