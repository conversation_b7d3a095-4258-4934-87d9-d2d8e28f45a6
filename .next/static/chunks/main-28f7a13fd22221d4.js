(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8792],{1025:(e,t,r)=>{"use strict";function i(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return i}}),r(16023),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2326:(e,t)=>{"use strict";function r(e){return"/api"===e||!!(null==e?void 0:e.startsWith("/api/"))}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAPIRoute",{enumerable:!0,get:function(){return r}})},2746:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return m},MissingStaticPage:function(){return v},NormalizeError:function(){return _},PageNotFoundError:function(){return g},SP:function(){return h},ST:function(){return f},WEB_VITALS:function(){return r},execOnce:function(){return i},getDisplayName:function(){return l},getLocationOrigin:function(){return a},getURL:function(){return o},isAbsoluteUrl:function(){return s},isResSent:function(){return u},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function i(e){let t,r=!1;return function(){for(var i=arguments.length,n=Array(i),s=0;s<i;s++)n[s]=arguments[s];return r||(r=!0,t=e(...n)),t}}let n=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,s=e=>n.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=a();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let i=await e.getInitialProps(t);if(r&&u(r))return i;if(!i)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+i+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i}let h="undefined"!=typeof performance,f=h&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class _ extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class v extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class m extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},2792:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return h}});let i=r(64252),n=r(62092),s=r(98069),a=i._(r(71827)),o=r(54591),l=r(49163),u=r(20541),c=r(54902),d=r(37176);r(43802);class h{getPageList(){return(0,d.getClientBuildManifest)().then(e=>e.sortedPages)}getMiddleware(){return window.__MIDDLEWARE_MATCHERS=[{regexp:"^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!.*\\..*|_next).*))(\\.json)?[\\/#\\?]?$",originalSource:"/((?!.*\\..*|_next).*)"},{regexp:"^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$",originalSource:"/"},{regexp:"^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$",originalSource:"/(api|trpc)(.*)"}],window.__MIDDLEWARE_MATCHERS}getDataHref(e){let{asPath:t,href:r,locale:i}=e,{pathname:d,query:h,search:f}=(0,u.parseRelativeUrl)(r),{pathname:p}=(0,u.parseRelativeUrl)(t),_=(0,c.removeTrailingSlash)(d);if("/"!==_[0])throw Object.defineProperty(Error('Route name should start with a "/", got "'+_+'"'),"__NEXT_ERROR_CODE",{value:"E303",enumerable:!1,configurable:!0});var g=e.skipInterpolation?p:(0,l.isDynamicRoute)(_)?(0,s.interpolateAs)(d,p,h).result:_;let v=(0,a.default)((0,c.removeTrailingSlash)((0,o.addLocale)(g,i)),".json");return(0,n.addBasePath)("/_next/data/"+this.buildId+v+f,!0)}_isSsg(e){return this.promisedSsgManifest.then(t=>t.has(e))}loadPage(e){return this.routeLoader.loadRoute(e).then(e=>{if("component"in e)return{page:e.component,mod:e.exports,styleSheets:e.styles.map(e=>({href:e.href,text:e.content}))};throw e.error})}prefetch(e){return this.routeLoader.prefetch(e)}constructor(e,t){this.routeLoader=(0,d.createRouteLoader)(t),this.buildId=e,this.assetPrefix=t,this.promisedSsgManifest=new Promise(e=>{window.__SSG_MANIFEST?e(window.__SSG_MANIFEST):window.__SSG_MANIFEST_CB=()=>{e(window.__SSG_MANIFEST)}})}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2850:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{AppRouterContext:function(){return n},GlobalLayoutRouterContext:function(){return a},LayoutRouterContext:function(){return s},MissingSlotContext:function(){return l},TemplateContext:function(){return o}});let i=r(64252)._(r(14232)),n=i.default.createContext(null),s=i.default.createContext(null),a=i.default.createContext(null),o=i.default.createContext(null),l=i.default.createContext(new Set)},3996:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},handleClientScriptLoad:function(){return _},initScriptLoader:function(){return g}});let i=r(64252),n=r(88365),s=r(37876),a=i._(r(98477)),o=n._(r(14232)),l=r(68831),u=r(79611),c=r(16959),d=new Map,h=new Set,f=e=>{if(a.default.preinit)return void e.forEach(e=>{a.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},p=e=>{let{src:t,id:r,onLoad:i=()=>{},onReady:n=null,dangerouslySetInnerHTML:s,children:a="",strategy:o="afterInteractive",onError:l,stylesheets:c}=e,p=r||t;if(p&&h.has(p))return;if(d.has(t)){h.add(p),d.get(t).then(i,l);return}let _=()=>{n&&n(),h.add(p)},g=document.createElement("script"),v=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),i&&i.call(this,t),_()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});s?(g.innerHTML=s.__html||"",_()):a?(g.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):"",_()):t&&(g.src=t,d.set(t,v)),(0,u.setAttributesFromProps)(g,e),"worker"===o&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",o),c&&f(c),document.body.appendChild(g)};function _(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}):p(e)}function g(e){e.forEach(_),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");h.add(t)})}function v(e){let{id:t,src:r="",onLoad:i=()=>{},onReady:n=null,strategy:u="afterInteractive",onError:d,stylesheets:f,..._}=e,{updateScripts:g,scripts:v,getIsSsr:m,appDir:y,nonce:b}=(0,o.useContext)(l.HeadManagerContext),E=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||r;E.current||(n&&e&&h.has(e)&&n(),E.current=!0)},[n,t,r]);let P=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{if(!P.current){if("afterInteractive"===u)p(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}));P.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(g?(v[u]=(v[u]||[]).concat([{id:t,src:r,onLoad:i,onReady:n,onError:d,..._}]),g(v)):m&&m()?h.add(t||r):m&&!m()&&p(e)),y){if(f&&f.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return _.dangerouslySetInnerHTML&&(_.children=_.dangerouslySetInnerHTML.__html,delete _.dangerouslySetInnerHTML),(0,s.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{..._,id:t}])+")"}});else return a.default.preload(r,_.integrity?{as:"script",integrity:_.integrity,nonce:b,crossOrigin:_.crossOrigin}:{as:"script",nonce:b,crossOrigin:_.crossOrigin}),(0,s.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{..._,id:t}])+")"}});"afterInteractive"===u&&r&&a.default.preload(r,_.integrity?{as:"script",integrity:_.integrity,nonce:b,crossOrigin:_.crossOrigin}:{as:"script",nonce:b,crossOrigin:_.crossOrigin})}return null}Object.defineProperty(v,"__nextScript",{value:!0});let m=v;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"denormalizePagePath",{enumerable:!0,get:function(){return s}});let i=r(63069),n=r(85419);function s(e){let t=(0,n.normalizePathSep)(e);return t.startsWith("/index/")&&!(0,i.isDynamicRoute)(t)?t.slice(6):"/index"!==t?t:"/"}},5679:(e,t,r)=>{"use strict";var i=r(65364);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},defaultHead:function(){return h}});let n=r(64252),s=r(88365),a=r(37876),o=s._(r(14232)),l=n._(r(33776)),u=r(80303),c=r(68831),d=r(76807);function h(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(96079);let p=["name","httpEquiv","charSet","itemProp"];function _(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(h(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,i={};return n=>{let s=!0,a=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){a=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?s=!1:t.add(n.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?s=!1:r.add(t);else{let e=n.props[t],r=i[t]||new Set;("name"!==t||!a)&&r.has(e)?s=!1:(r.add(e),i[t]=r)}}}return s}}()).reverse().map((e,t)=>{let n=e.key||t;if(i.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:n})})}let g=function(e){let{children:t}=e,r=(0,o.useContext)(u.AmpStateContext),i=(0,o.useContext)(c.HeadManagerContext);return(0,a.jsx)(l.default,{reduceComponentsToState:_,headManager:i,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5931:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PathParamsContext:function(){return a},PathnameContext:function(){return s},SearchParamsContext:function(){return n}});let i=r(14232),n=(0,i.createContext)(null),s=(0,i.createContext)(null),a=(0,i.createContext)(null)},8480:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return s},formatWithValidation:function(){return o},urlObjectKeys:function(){return a}});let i=r(88365)._(r(78040)),n=/https?|ftp|gopher|file/;function s(e){let{auth:t,hostname:r}=e,s=e.protocol||"",a=e.pathname||"",o=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:r&&(u=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(i.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return s&&!s.endsWith(":")&&(s+=":"),e.slashes||(!s||n.test(s))&&!1!==u?(u="//"+(u||""),a&&"/"!==a[0]&&(a="/"+a)):u||(u=""),o&&"#"!==o[0]&&(o="#"+o),c&&"?"!==c[0]&&(c="?"+c),""+s+u+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+o}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function o(e){return s(e)}},8677:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ImageConfigContext",{enumerable:!0,get:function(){return s}});let i=r(64252)._(r(14232)),n=r(17539),s=i.default.createContext(n.imageConfigDefault)},12917:(e,t)=>{"use strict";let r;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return i},setConfig:function(){return n}});let i=()=>r;function n(e){r=e}},15861:e=>{!function(){var t={229:function(e){var t,r,i,n=e.exports={};function s(){throw Error("setTimeout has not been defined")}function a(){throw Error("clearTimeout has not been defined")}try{t="function"==typeof setTimeout?setTimeout:s}catch(e){t=s}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(e){r=a}function o(e){if(t===setTimeout)return setTimeout(e,0);if((t===s||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}var l=[],u=!1,c=-1;function d(){u&&i&&(u=!1,i.length?l=i.concat(l):c=-1,l.length&&h())}function h(){if(!u){var e=o(d);u=!0;for(var t=l.length;t;){for(i=l,l=[];++c<t;)i&&i[c].run();c=-1,t=l.length}i=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function p(){}n.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new f(e,t)),1!==l.length||u||o(h)},f.prototype.run=function(){this.fun.apply(null,this.array)},n.title="browser",n.browser=!0,n.env={},n.argv=[],n.version="",n.versions={},n.on=p,n.addListener=p,n.once=p,n.off=p,n.removeListener=p,n.removeAllListeners=p,n.emit=p,n.prependListener=p,n.prependOnceListener=p,n.listeners=function(e){return[]},n.binding=function(e){throw Error("process.binding is not supported")},n.cwd=function(){return"/"},n.chdir=function(e){throw Error("process.chdir is not supported")},n.umask=function(){return 0}}},r={};function i(e){var n=r[e];if(void 0!==n)return n.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,i),a=!1}finally{a&&delete r[e]}return s.exports}i.ab="//",e.exports=i(229)}()},16023:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return n}});let i=r(73716);function n(e){return(0,i.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16959:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return i},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},i="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17539:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},20541:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return s}});let i=r(2746),n=r(78040);function s(e,t,r){void 0===r&&(r=!0);let s=new URL((0,i.getLocationOrigin)()),a=t?new URL(t,s):e.startsWith(".")?new URL(window.location.href):s,{pathname:o,searchParams:l,search:u,hash:c,href:d,origin:h}=new URL(e,a);if(h!==s.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:o,query:r?(0,n.searchParamsToUrlQuery)(l):void 0,search:u,hash:c,href:d.slice(h.length)}}},21017:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}});var r=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},21291:()=>{"trimStart"in String.prototype||(String.prototype.trimStart=String.prototype.trimLeft),"trimEnd"in String.prototype||(String.prototype.trimEnd=String.prototype.trimRight),"description"in Symbol.prototype||Object.defineProperty(Symbol.prototype,"description",{configurable:!0,get:function(){var e=/\((.*)\)/.exec(this.toString());return e?e[1]:void 0}}),Array.prototype.flat||(Array.prototype.flat=function(e,t){return t=this.concat.apply([],this),e>1&&t.some(Array.isArray)?t.flat(e-1):t},Array.prototype.flatMap=function(e,t){return this.map(e,t).flat()}),Promise.prototype.finally||(Promise.prototype.finally=function(e){if("function"!=typeof e)return this.then(e,e);var t=this.constructor||Promise;return this.then(function(r){return t.resolve(e()).then(function(){return r})},function(r){return t.resolve(e()).then(function(){throw r})})}),Object.fromEntries||(Object.fromEntries=function(e){return Array.from(e).reduce(function(e,t){return e[t[0]]=t[1],e},{})}),Array.prototype.at||(Array.prototype.at=function(e){var t=Math.trunc(e)||0;if(t<0&&(t+=this.length),!(t<0||t>=this.length))return this[t]}),Object.hasOwn||(Object.hasOwn=function(e,t){if(null==e)throw TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(Object(e),t)}),"canParse"in URL||(URL.canParse=function(e,t){try{return new URL(e,t),!0}catch(e){return!1}})},24609:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let i=r(64252)._(r(29871));class n{end(e){if("ended"===this.state.state)throw Object.defineProperty(Error("Span has already ended"),"__NEXT_ERROR_CODE",{value:"E17",enumerable:!1,configurable:!0});this.state={state:"ended",endTime:null!=e?e:Date.now()},this.onSpanEnd(this)}constructor(e,t,r){var i,n;this.name=e,this.attributes=null!=(i=t.attributes)?i:{},this.startTime=null!=(n=t.startTime)?n:Date.now(),this.onSpanEnd=r,this.state={state:"inprogress"}}}class s{startSpan(e,t){return new n(e,t,this.handleSpanEnd)}onSpanEnd(e){return this._emitter.on("spanend",e),()=>{this._emitter.off("spanend",e)}}constructor(){this._emitter=(0,i.default)(),this.handleSpanEnd=e=>{this._emitter.emit("spanend",e)}}}let a=new s;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25842:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(33718),r(67647);let i=r(39525);window.next={version:i.version,get router(){return i.router},emitter:i.emitter},(0,i.initialize)({}).then(()=>(0,i.hydrate)()).catch(console.error),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26252:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return n}});let i=r(29509);function n(e,t){let r=[],n=(0,i.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),s=(0,i.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(n.source),n.flags):n,r);return(e,i)=>{if("string"!=typeof e)return!1;let n=s(e);if(!n)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete n.params[e.name];return{...i,...n.params}}}},29509:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var i=e[r];if("*"===i||"+"===i||"?"===i){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===i){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===i){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===i){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===i){for(var n="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){n+=e[s++];continue}break}if(!n)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:n}),r=s;continue}if("("===i){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '+s);for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at "+s);l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),i=t.prefixes,s=void 0===i?"./":i,a="[^"+n(t.delimiter||"/#?")+"]+?",o=[],l=0,u=0,c="",d=function(e){if(u<r.length&&r[u].type===e)return r[u++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var i=r[u];throw TypeError("Unexpected "+i.type+" at "+i.index+", expected "+e)},f=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t};u<r.length;){var p=d("CHAR"),_=d("NAME"),g=d("PATTERN");if(_||g){var v=p||"";-1===s.indexOf(v)&&(c+=v,v=""),c&&(o.push(c),c=""),o.push({name:_||l++,prefix:v,suffix:"",pattern:g||a,modifier:d("MODIFIER")||""});continue}var m=p||d("ESCAPED_CHAR");if(m){c+=m;continue}if(c&&(o.push(c),c=""),d("OPEN")){var v=f(),y=d("NAME")||"",b=d("PATTERN")||"",E=f();h("CLOSE"),o.push({name:y||(b?l++:""),pattern:y&&!b?a:b,prefix:v,suffix:E,modifier:d("MODIFIER")||""});continue}h("END")}return o}function r(e,t){void 0===t&&(t={});var r=s(t),i=t.encode,n=void 0===i?function(e){return e}:i,a=t.validate,o=void 0===a||a,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",i=0;i<e.length;i++){var s=e[i];if("string"==typeof s){r+=s;continue}var a=t?t[s.name]:void 0,u="?"===s.modifier||"*"===s.modifier,c="*"===s.modifier||"+"===s.modifier;if(Array.isArray(a)){if(!c)throw TypeError('Expected "'+s.name+'" to not repeat, but got an array');if(0===a.length){if(u)continue;throw TypeError('Expected "'+s.name+'" to not be empty')}for(var d=0;d<a.length;d++){var h=n(a[d],s);if(o&&!l[i].test(h))throw TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');r+=s.prefix+h+s.suffix}continue}if("string"==typeof a||"number"==typeof a){var h=n(String(a),s);if(o&&!l[i].test(h))throw TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but got "'+h+'"');r+=s.prefix+h+s.suffix;continue}if(!u){var f=c?"an array":"a string";throw TypeError('Expected "'+s.name+'" to be '+f)}}return r}}function i(e,t,r){void 0===r&&(r={});var i=r.decode,n=void 0===i?function(e){return e}:i;return function(r){var i=e.exec(r);if(!i)return!1;for(var s=i[0],a=i.index,o=Object.create(null),l=1;l<i.length;l++)!function(e){if(void 0!==i[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=i[e].split(r.prefix+r.suffix).map(function(e){return n(e,r)}):o[r.name]=n(i[e],r)}}(l);return{path:s,index:a,params:o}}}function n(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function s(e){return e&&e.sensitive?"":"i"}function a(e,t,r){void 0===r&&(r={});for(var i=r.strict,a=void 0!==i&&i,o=r.start,l=r.end,u=r.encode,c=void 0===u?function(e){return e}:u,d="["+n(r.endsWith||"")+"]|$",h="["+n(r.delimiter||"/#?")+"]",f=void 0===o||o?"^":"",p=0;p<e.length;p++){var _=e[p];if("string"==typeof _)f+=n(c(_));else{var g=n(c(_.prefix)),v=n(c(_.suffix));if(_.pattern)if(t&&t.push(_),g||v)if("+"===_.modifier||"*"===_.modifier){var m="*"===_.modifier?"?":"";f+="(?:"+g+"((?:"+_.pattern+")(?:"+v+g+"(?:"+_.pattern+"))*)"+v+")"+m}else f+="(?:"+g+"("+_.pattern+")"+v+")"+_.modifier;else f+="("+_.pattern+")"+_.modifier;else f+="(?:"+g+v+")"+_.modifier}}if(void 0===l||l)a||(f+=h+"?"),f+=r.endsWith?"(?="+d+")":"$";else{var y=e[e.length-1],b="string"==typeof y?h.indexOf(y[y.length-1])>-1:void 0===y;a||(f+="(?:"+h+"(?="+d+"))?"),b||(f+="(?="+h+"|"+d+")")}return new RegExp(f,s(r))}function o(t,r,i){if(t instanceof RegExp){if(!r)return t;var n=t.source.match(/\((?!\?)/g);if(n)for(var l=0;l<n.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,i).source}).join("|")+")",s(i)):a(e(t,i),r,i)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,i){return r(e(t,i),i)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return i(o(e,r,t),r,t)},t.regexpToFunction=i,t.tokensToRegexp=a,t.pathToRegexp=o})(),e.exports=t})()},29663:(e,t,r)=>{"use strict";function i(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:i}=r(55040);return i(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return i}})},29871:(e,t)=>{"use strict";function r(){let e=Object.create(null);return{on(t,r){(e[t]||(e[t]=[])).push(r)},off(t,r){e[t]&&e[t].splice(e[t].indexOf(r)>>>0,1)},emit(t){for(var r=arguments.length,i=Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];(e[t]||[]).slice().map(e=>{e(...i)})}}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},32959:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return s},normalizeRscURL:function(){return a}});let i=r(50938),n=r(68714);function s(e){return(0,i.ensureLeadingSlash)(e.split("/").reduce((e,t,r,i)=>!t||(0,n.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===i.length-1?e:e+"/"+t,""))}function a(e){return e.replace(/\.rsc($|\?)/,"$1")}},33703:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n},getSortedRoutes:function(){return i}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").'),"__NEXT_ERROR_CODE",{value:"E458",enumerable:!1,configurable:!0});r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,i){if(0===e.length){this.placeholder=!1;return}if(i)throw Object.defineProperty(Error("Catch-all must be the last part of the URL."),"__NEXT_ERROR_CODE",{value:"E392",enumerable:!1,configurable:!0});let n=e[0];if(n.startsWith("[")&&n.endsWith("]")){let r=n.slice(1,-1),a=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),a=!0),r.startsWith("…"))throw Object.defineProperty(Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?"),"__NEXT_ERROR_CODE",{value:"E147",enumerable:!1,configurable:!0});if(r.startsWith("...")&&(r=r.substring(3),i=!0),r.startsWith("[")||r.endsWith("]"))throw Object.defineProperty(Error("Segment names may not start or end with extra brackets ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E421",enumerable:!1,configurable:!0});if(r.startsWith("."))throw Object.defineProperty(Error("Segment names may not start with erroneous periods ('"+r+"')."),"__NEXT_ERROR_CODE",{value:"E288",enumerable:!1,configurable:!0});function s(e,r){if(null!==e&&e!==r)throw Object.defineProperty(Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"')."),"__NEXT_ERROR_CODE",{value:"E337",enumerable:!1,configurable:!0});t.forEach(e=>{if(e===r)throw Object.defineProperty(Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E247",enumerable:!1,configurable:!0});if(e.replace(/\W/g,"")===n.replace(/\W/g,""))throw Object.defineProperty(Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path'),"__NEXT_ERROR_CODE",{value:"E499",enumerable:!1,configurable:!0})}),t.push(r)}if(i)if(a){if(null!=this.restSlugName)throw Object.defineProperty(Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).'),"__NEXT_ERROR_CODE",{value:"E299",enumerable:!1,configurable:!0});s(this.optionalRestSlugName,r),this.optionalRestSlugName=r,n="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Object.defineProperty(Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E300",enumerable:!1,configurable:!0});s(this.restSlugName,r),this.restSlugName=r,n="[...]"}else{if(a)throw Object.defineProperty(Error('Optional route parameters are not yet supported ("'+e[0]+'").'),"__NEXT_ERROR_CODE",{value:"E435",enumerable:!1,configurable:!0});s(this.slugName,r),this.slugName=r,n="[]"}}this.children.has(n)||this.children.set(n,new r),this.children.get(n)._insert(e.slice(1),t,i)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function i(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function n(e,t){let r={},n=[];for(let i=0;i<e.length;i++){let s=t(e[i]);r[s]=i,n[i]=s}return i(n).map(t=>e[r[t]])}},33718:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),r(78757),self.__next_set_public_path__=e=>{r.p=e},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},33776:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return a}});let i=r(14232),n=i.useLayoutEffect,s=i.useEffect;function a(e){let{headManager:t,reduceComponentsToState:r}=e;function a(){if(t&&t.mountedInstances){let n=i.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(r(n,e))}}return n(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),n(()=>(t&&(t._pendingUpdate=a),()=>{t&&(t._pendingUpdate=a)})),s(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},36818:(e,t)=>{"use strict";let r;function i(e){var t;return(null==(t=function(){if(void 0===r){var e;r=(null==(e=window.trustedTypes)?void 0:e.createPolicy("nextjs",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e}))||null}return r}())?void 0:t.createScriptURL(e))||e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"__unsafeCreateTrustedScriptURL",{enumerable:!0,get:function(){return i}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37176:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createRouteLoader:function(){return g},getClientBuildManifest:function(){return p},isAssetError:function(){return c},markAssetError:function(){return u}}),r(64252),r(71827);let i=r(36818),n=r(16959),s=r(78757),a=r(70536);function o(e,t,r){let i,n=t.get(e);if(n)return"future"in n?n.future:Promise.resolve(n);let s=new Promise(e=>{i=e});return t.set(e,{resolve:i,future:s}),r?r().then(e=>(i(e),e)).catch(r=>{throw t.delete(e),r}):s}let l=Symbol("ASSET_LOAD_ERROR");function u(e){return Object.defineProperty(e,l,{})}function c(e){return e&&l in e}let d=function(e){try{return e=document.createElement("link"),!!window.MSInputMethodContext&&!!document.documentMode||e.relList.supports("prefetch")}catch(e){return!1}}(),h=()=>(0,s.getDeploymentIdQueryOrEmptyString)();function f(e,t,r){return new Promise((i,s)=>{let a=!1;e.then(e=>{a=!0,i(e)}).catch(s),(0,n.requestIdleCallback)(()=>setTimeout(()=>{a||s(r)},t))})}function p(){return self.__BUILD_MANIFEST?Promise.resolve(self.__BUILD_MANIFEST):f(new Promise(e=>{let t=self.__BUILD_MANIFEST_CB;self.__BUILD_MANIFEST_CB=()=>{e(self.__BUILD_MANIFEST),t&&t()}}),3800,u(Object.defineProperty(Error("Failed to load client build manifest"),"__NEXT_ERROR_CODE",{value:"E273",enumerable:!1,configurable:!0})))}function _(e,t){return p().then(r=>{if(!(t in r))throw u(Object.defineProperty(Error("Failed to lookup route: "+t),"__NEXT_ERROR_CODE",{value:"E446",enumerable:!1,configurable:!0}));let n=r[t].map(t=>e+"/_next/"+(0,a.encodeURIPath)(t));return{scripts:n.filter(e=>e.endsWith(".js")).map(e=>(0,i.__unsafeCreateTrustedScriptURL)(e)+h()),css:n.filter(e=>e.endsWith(".css")).map(e=>e+h())}})}function g(e){let t=new Map,r=new Map,i=new Map,s=new Map;function a(e){{var t;let i=r.get(e.toString());return i?i:document.querySelector('script[src^="'+e+'"]')?Promise.resolve():(r.set(e.toString(),i=new Promise((r,i)=>{(t=document.createElement("script")).onload=r,t.onerror=()=>i(u(Object.defineProperty(Error("Failed to load script: "+e),"__NEXT_ERROR_CODE",{value:"E74",enumerable:!1,configurable:!0}))),t.crossOrigin=void 0,t.src=e,document.body.appendChild(t)})),i)}}function l(e){let t=i.get(e);return t||i.set(e,t=fetch(e,{credentials:"same-origin"}).then(t=>{if(!t.ok)throw Object.defineProperty(Error("Failed to load stylesheet: "+e),"__NEXT_ERROR_CODE",{value:"E189",enumerable:!1,configurable:!0});return t.text().then(t=>({href:e,content:t}))}).catch(e=>{throw u(e)})),t}return{whenEntrypoint:e=>o(e,t),onEntrypoint(e,r){(r?Promise.resolve().then(()=>r()).then(e=>({component:e&&e.default||e,exports:e}),e=>({error:e})):Promise.resolve(void 0)).then(r=>{let i=t.get(e);i&&"resolve"in i?r&&(t.set(e,r),i.resolve(r)):(r?t.set(e,r):t.delete(e),s.delete(e))})},loadRoute(r,i){return o(r,s,()=>{let n;return f(_(e,r).then(e=>{let{scripts:i,css:n}=e;return Promise.all([t.has(r)?[]:Promise.all(i.map(a)),Promise.all(n.map(l))])}).then(e=>this.whenEntrypoint(r).then(t=>({entrypoint:t,styles:e[1]}))),3800,u(Object.defineProperty(Error("Route did not complete loading: "+r),"__NEXT_ERROR_CODE",{value:"E12",enumerable:!1,configurable:!0}))).then(e=>{let{entrypoint:t,styles:r}=e,i=Object.assign({styles:r},t);return"error"in t?t:i}).catch(e=>{if(i)throw e;return{error:e}}).finally(()=>null==n?void 0:n())})},prefetch(t){let r;return(r=navigator.connection)&&(r.saveData||/2g/.test(r.effectiveType))?Promise.resolve():_(e,t).then(e=>Promise.all(d?e.scripts.map(e=>{var t,r,i;return t=e.toString(),r="script",new Promise((e,n)=>{let s='\n      link[rel="prefetch"][href^="'+t+'"],\n      link[rel="preload"][href^="'+t+'"],\n      script[src^="'+t+'"]';if(document.querySelector(s))return e();i=document.createElement("link"),r&&(i.as=r),i.rel="prefetch",i.crossOrigin=void 0,i.onload=e,i.onerror=()=>n(u(Object.defineProperty(Error("Failed to prefetch: "+t),"__NEXT_ERROR_CODE",{value:"E268",enumerable:!1,configurable:!0}))),i.href=t,document.head.appendChild(i)})}):[])).then(()=>{(0,n.requestIdleCallback)(()=>this.loadRoute(t,!0).catch(()=>{}))}).catch(()=>{})}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37188:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return n},extractInterceptionRouteInformation:function(){return a},isInterceptionRouteAppPath:function(){return s}});let i=r(32959),n=["(..)(..)","(.)","(..)","(...)"];function s(e){return void 0!==e.split("/").find(e=>n.find(t=>e.startsWith(t)))}function a(e){let t,r,s;for(let i of e.split("/"))if(r=n.find(e=>i.startsWith(e))){[t,s]=e.split(r,2);break}if(!t||!r||!s)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,i.normalizeAppPath)(t),r){case"(.)":s="/"===t?"/"+s:t+"/"+s;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});s=t.split("/").slice(0,-1).concat(s).join("/");break;case"(...)":s="/"+s;break;case"(..)(..)":let a=t.split("/");if(a.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});s=a.slice(0,-2).concat(s).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:s}}},38089:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{copyNextErrorCode:function(){return i},createDigestWithErrorCode:function(){return r},extractNextErrorCode:function(){return n}});let r=(e,t)=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e?`${t}@${e.__NEXT_ERROR_CODE}`:t,i=(e,t)=>{let r=n(e);r&&"object"==typeof t&&null!==t&&Object.defineProperty(t,"__NEXT_ERROR_CODE",{value:r,enumerable:!1,configurable:!0})},n=e=>"object"==typeof e&&null!==e&&"__NEXT_ERROR_CODE"in e&&"string"==typeof e.__NEXT_ERROR_CODE?e.__NEXT_ERROR_CODE:"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest?e.digest.split("@").find(e=>e.startsWith("E")):void 0},38096:(e,t)=>{"use strict";function r(e){return Object.prototype.toString.call(e)}function i(e){if("[object Object]"!==r(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t.hasOwnProperty("isPrototypeOf")}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getObjectClassLabel:function(){return r},isPlainObject:function(){return i}})},39308:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return A},CACHE_ONE_YEAR:function(){return S},DOT_NEXT_ALIAS:function(){return C},ESLINT_DEFAULT_DIRS:function(){return K},GSP_NO_RETURNED_VALUE:function(){return W},GSSP_COMPONENT_MEMBER_ERROR:function(){return X},GSSP_NO_RETURNED_VALUE:function(){return G},INFINITE_CACHE:function(){return w},INSTRUMENTATION_HOOK_FILENAME:function(){return O},MATCHED_PATH_HEADER:function(){return n},MIDDLEWARE_FILENAME:function(){return R},MIDDLEWARE_LOCATION_REGEXP:function(){return x},NEXT_BODY_SUFFIX:function(){return p},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return P},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return g},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return v},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return E},NEXT_CACHE_TAGS_HEADER:function(){return _},NEXT_CACHE_TAG_MAX_ITEMS:function(){return y},NEXT_CACHE_TAG_MAX_LENGTH:function(){return b},NEXT_DATA_SUFFIX:function(){return h},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return i},NEXT_META_SUFFIX:function(){return f},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return m},NON_STANDARD_NODE_ENV:function(){return V},PAGES_DIR_ALIAS:function(){return T},PRERENDER_REVALIDATE_HEADER:function(){return s},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return a},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return D},ROOT_DIR_ALIAS:function(){return I},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return F},RSC_ACTION_ENCRYPTION_ALIAS:function(){return L},RSC_ACTION_PROXY_ALIAS:function(){return M},RSC_ACTION_VALIDATE_ALIAS:function(){return j},RSC_CACHE_WRAPPER_ALIAS:function(){return N},RSC_MOD_REF_PROXY_ALIAS:function(){return k},RSC_PREFETCH_SUFFIX:function(){return o},RSC_SEGMENTS_DIR_SUFFIX:function(){return l},RSC_SEGMENT_SUFFIX:function(){return u},RSC_SUFFIX:function(){return c},SERVER_PROPS_EXPORT_ERROR:function(){return q},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return U},SERVER_PROPS_SSG_CONFLICT:function(){return B},SERVER_RUNTIME:function(){return J},SSG_FALLBACK_EXPORT_ERROR:function(){return Y},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return $},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return H},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return z},WEBPACK_LAYERS:function(){return Q},WEBPACK_RESOURCE_QUERIES:function(){return ee}});let r="nxtP",i="nxtI",n="x-matched-path",s="x-prerender-revalidate",a="x-prerender-revalidate-if-generated",o=".prefetch.rsc",l=".segments",u=".segment.rsc",c=".rsc",d=".action",h=".json",f=".meta",p=".body",_="x-next-cache-tags",g="x-next-revalidated-tags",v="x-next-revalidate-tag-token",m="next-resume",y=128,b=256,E=1024,P="_N_T_",S=31536e3,w=0xfffffffe,R="middleware",x=`(?:src/)?${R}`,O="instrumentation",T="private-next-pages",C="private-dot-next",I="private-next-root-dir",A="private-next-app-dir",k="private-next-rsc-mod-ref-proxy",j="private-next-rsc-action-validate",M="private-next-rsc-server-reference",N="private-next-rsc-cache-wrapper",L="private-next-rsc-action-encryption",F="private-next-rsc-action-client-wrapper",D="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",$="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",U="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",B="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",H="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",q="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",W="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",G="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",z="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",X="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",V='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',Y="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",K=["app","pages","components","lib","src"],J={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Z={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"},Q={...Z,GROUP:{builtinReact:[Z.reactServerComponents,Z.actionBrowser],serverOnly:[Z.reactServerComponents,Z.actionBrowser,Z.instrument,Z.middleware],neutralTarget:[Z.apiNode,Z.apiEdge],clientOnly:[Z.serverSideRendering,Z.appPagesBrowser],bundled:[Z.reactServerComponents,Z.actionBrowser,Z.serverSideRendering,Z.appPagesBrowser,Z.shared,Z.instrument,Z.middleware],appPages:[Z.reactServerComponents,Z.serverSideRendering,Z.appPagesBrowser,Z.actionBrowser]}},ee={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},39525:(e,t,r)=>{"use strict";let i,n,s,a,o,l,u,c,d,h,f,p;Object.defineProperty(t,"__esModule",{value:!0});let _=r(88365);Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{emitter:function(){return H},hydrate:function(){return el},initialize:function(){return z},router:function(){return i},version:function(){return B}});let g=r(64252),v=r(37876);r(21291);let m=g._(r(14232)),y=g._(r(78944)),b=r(68831),E=g._(r(29871)),P=r(99948),S=r(53132),w=r(49163),R=r(78040),x=r(12917),O=r(2746),T=r(93090),C=g._(r(84547)),I=g._(r(2792)),A=r(41318),k=r(84294),j=r(66240),M=r(8677),N=r(1025),L=r(16023),F=r(2850),D=r(69609),$=r(5931),U=r(77207);r(24609),r(76999);let B="15.3.5",H=(0,E.default)(),q=e=>[].slice.call(e),W=!1;class G extends m.default.Component{componentDidCatch(e,t){this.props.fn(e,t)}componentDidMount(){this.scrollToHash(),i.isSsr&&(n.isFallback||n.nextExport&&((0,w.isDynamicRoute)(i.pathname)||location.search||1)||n.props&&n.props.__N_SSG&&(location.search||1))&&i.replace(i.pathname+"?"+String((0,R.assign)((0,R.urlQueryToSearchParams)(i.query),new URLSearchParams(location.search))),s,{_h:1,shallow:!n.isFallback&&!W}).catch(e=>{if(!e.cancelled)throw e})}componentDidUpdate(){this.scrollToHash()}scrollToHash(){let{hash:e}=location;if(!(e=e&&e.substring(1)))return;let t=document.getElementById(e);t&&setTimeout(()=>t.scrollIntoView(),0)}render(){return this.props.children}}async function z(e){void 0===e&&(e={}),n=JSON.parse(document.getElementById("__NEXT_DATA__").textContent),window.__NEXT_DATA__=n,p=n.defaultLocale;let t=n.assetPrefix||"";if(self.__next_set_public_path__(""+t+"/_next/"),(0,x.setConfig)({serverRuntimeConfig:{},publicRuntimeConfig:n.runtimeConfig||{}}),s=(0,O.getURL)(),(0,L.hasBasePath)(s)&&(s=(0,N.removeBasePath)(s)),n.scriptLoader){let{initScriptLoader:e}=r(3996);e(n.scriptLoader)}a=new I.default(n.buildId,t);let u=e=>{let[t,r]=e;return a.routeLoader.onEntrypoint(t,r)};return window.__NEXT_P&&window.__NEXT_P.map(e=>setTimeout(()=>u(e),0)),window.__NEXT_P=[],window.__NEXT_P.push=u,(l=(0,C.default)()).getIsSsr=()=>i.isSsr,o=document.getElementById("__next"),{assetPrefix:t}}function X(e,t){return(0,v.jsx)(e,{...t})}function V(e){var t;let{children:r}=e,n=m.default.useMemo(()=>(0,D.adaptForAppRouterInstance)(i),[]);return(0,v.jsx)(G,{fn:e=>K({App:d,err:e}).catch(e=>console.error("Error rendering page: ",e)),children:(0,v.jsx)(F.AppRouterContext.Provider,{value:n,children:(0,v.jsx)($.SearchParamsContext.Provider,{value:(0,D.adaptForSearchParams)(i),children:(0,v.jsx)(D.PathnameContextProviderAdapter,{router:i,isAutoExport:null!=(t=self.__NEXT_DATA__.autoExport)&&t,children:(0,v.jsx)($.PathParamsContext.Provider,{value:(0,D.adaptForPathParams)(i),children:(0,v.jsx)(P.RouterContext.Provider,{value:(0,k.makePublicRouterInstance)(i),children:(0,v.jsx)(b.HeadManagerContext.Provider,{value:l,children:(0,v.jsx)(M.ImageConfigContext.Provider,{value:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1},children:r})})})})})})})})}let Y=e=>t=>{let r={...t,Component:f,err:n.err,router:i};return(0,v.jsx)(V,{children:X(e,r)})};function K(e){let{App:t,err:o}=e;return console.error(o),console.error("A client-side exception has occurred, see here for more info: https://nextjs.org/docs/messages/client-side-exception-occurred"),a.loadPage("/_error").then(i=>{let{page:n,styleSheets:s}=i;return(null==u?void 0:u.Component)===n?Promise.resolve().then(()=>_._(r(99341))).then(i=>Promise.resolve().then(()=>_._(r(90472))).then(r=>(e.App=t=r.default,i))).then(e=>({ErrorComponent:e.default,styleSheets:[]})):{ErrorComponent:n,styleSheets:s}}).then(r=>{var a;let{ErrorComponent:l,styleSheets:u}=r,c=Y(t),d={Component:l,AppTree:c,router:i,ctx:{err:o,pathname:n.page,query:n.query,asPath:s,AppTree:c}};return Promise.resolve((null==(a=e.props)?void 0:a.err)?e.props:(0,O.loadGetInitialProps)(t,d)).then(t=>ea({...e,err:o,Component:l,styleSheets:u,props:t}))})}function J(e){let{callback:t}=e;return m.default.useLayoutEffect(()=>t(),[t]),null}let Z={navigationStart:"navigationStart",beforeRender:"beforeRender",afterRender:"afterRender",afterHydrate:"afterHydrate",routeChange:"routeChange"},Q={hydration:"Next.js-hydration",beforeHydration:"Next.js-before-hydration",routeChangeToRender:"Next.js-route-change-to-render",render:"Next.js-render"},ee=null,et=!0;function er(){[Z.beforeRender,Z.afterHydrate,Z.afterRender,Z.routeChange].forEach(e=>performance.clearMarks(e))}function ei(){O.ST&&(performance.mark(Z.afterHydrate),performance.getEntriesByName(Z.beforeRender,"mark").length&&(performance.measure(Q.beforeHydration,Z.navigationStart,Z.beforeRender),performance.measure(Q.hydration,Z.beforeRender,Z.afterHydrate)),h&&performance.getEntriesByName(Q.hydration).forEach(h),er())}function en(){if(!O.ST)return;performance.mark(Z.afterRender);let e=performance.getEntriesByName(Z.routeChange,"mark");e.length&&(performance.getEntriesByName(Z.beforeRender,"mark").length&&(performance.measure(Q.routeChangeToRender,e[0].name,Z.beforeRender),performance.measure(Q.render,Z.beforeRender,Z.afterRender),h&&(performance.getEntriesByName(Q.render).forEach(h),performance.getEntriesByName(Q.routeChangeToRender).forEach(h))),er(),[Q.routeChangeToRender,Q.render].forEach(e=>performance.clearMeasures(e)))}function es(e){let{callbacks:t,children:r}=e;return m.default.useLayoutEffect(()=>t.forEach(e=>e()),[t]),r}function ea(e){let t,r,{App:n,Component:s,props:a,err:l}=e,d="initial"in e?void 0:e.styleSheets;s=s||u.Component;let h={...a=a||u.props,Component:s,err:l,router:i};u=h;let f=!1,p=new Promise((e,t)=>{c&&c(),r=()=>{c=null,e()},c=()=>{f=!0,c=null;let e=Object.defineProperty(Error("Cancel rendering route"),"__NEXT_ERROR_CODE",{value:"E503",enumerable:!1,configurable:!0});e.cancelled=!0,t(e)}});function _(){r()}!function(){if(!d)return;let e=new Set(q(document.querySelectorAll("style[data-n-href]")).map(e=>e.getAttribute("data-n-href"))),t=document.querySelector("noscript[data-n-css]"),r=null==t?void 0:t.getAttribute("data-n-css");d.forEach(t=>{let{href:i,text:n}=t;if(!e.has(i)){let e=document.createElement("style");e.setAttribute("data-n-href",i),e.setAttribute("media","x"),r&&e.setAttribute("nonce",r),document.head.appendChild(e),e.appendChild(document.createTextNode(n))}})}();let g=(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{callback:function(){if(d&&!f){let e=new Set(d.map(e=>e.href)),t=q(document.querySelectorAll("style[data-n-href]")),r=t.map(e=>e.getAttribute("data-n-href"));for(let i=0;i<r.length;++i)e.has(r[i])?t[i].removeAttribute("media"):t[i].setAttribute("media","x");let i=document.querySelector("noscript[data-n-css]");i&&d.forEach(e=>{let{href:t}=e,r=document.querySelector('style[data-n-href="'+t+'"]');r&&(i.parentNode.insertBefore(r,i.nextSibling),i=r)}),q(document.querySelectorAll("link[data-n-p]")).forEach(e=>{e.parentNode.removeChild(e)})}if(e.scroll){let{x:t,y:r}=e.scroll;(0,S.handleSmoothScroll)(()=>{window.scrollTo(t,r)})}}}),(0,v.jsxs)(V,{children:[X(n,h),(0,v.jsx)(T.Portal,{type:"next-route-announcer",children:(0,v.jsx)(A.RouteAnnouncer,{})})]})]});var b=o;O.ST&&performance.mark(Z.beforeRender);let E=(t=et?ei:en,(0,v.jsx)(es,{callbacks:[t,_],children:g}));return ee?(0,m.default.startTransition)(()=>{ee.render(E)}):(ee=y.default.hydrateRoot(b,E,{onRecoverableError:U.onRecoverableError}),et=!1),p}async function eo(e){if(e.err&&(void 0===e.Component||!e.isHydratePass))return void await K(e);try{await ea(e)}catch(r){let t=(0,j.getProperError)(r);if(t.cancelled)throw t;await K({...e,err:t})}}async function el(e){let t=n.err;try{let e=await a.routeLoader.whenEntrypoint("/_app");if("error"in e)throw e.error;let{component:t,exports:r}=e;d=t,r&&r.reportWebVitals&&(h=e=>{let t,{id:i,name:n,startTime:s,value:a,duration:o,entryType:l,entries:u,attribution:c}=e,d=Date.now()+"-"+(Math.floor(Math.random()*(9e12-1))+1e12);u&&u.length&&(t=u[0].startTime);let h={id:i||d,name:n,startTime:s||t,value:null==a?o:a,label:"mark"===l||"measure"===l?"custom":"web-vital"};c&&(h.attribution=c),r.reportWebVitals(h)});let i=await a.routeLoader.whenEntrypoint(n.page);if("error"in i)throw i.error;f=i.component}catch(e){t=(0,j.getProperError)(e)}window.__NEXT_PRELOADREADY&&await window.__NEXT_PRELOADREADY(n.dynamicIds),i=(0,k.createRouter)(n.page,n.query,s,{initialProps:n.props,pageLoader:a,App:d,Component:f,wrapApp:Y,err:t,isFallback:!!n.isFallback,subscription:(e,t,r)=>eo(Object.assign({},e,{App:t,scroll:r})),locale:n.locale,locales:n.locales,defaultLocale:p,domainLocales:n.domainLocales,isPreview:n.isPreview}),W=await i._initialMatchesMiddlewarePromise;let r={App:d,initial:!0,Component:f,props:n.props,err:t,isHydratePass:!0};(null==e?void 0:e.beforeRender)&&await e.beforeRender(),eo(r)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},40990:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(i=>{t.includes(i)||(r[i]=e[i])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},41318:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RouteAnnouncer:function(){return l},default:function(){return u}});let i=r(64252),n=r(37876),s=i._(r(14232)),a=r(84294),o={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"absolute",top:0,width:"1px",whiteSpace:"nowrap",wordWrap:"normal"},l=()=>{let{asPath:e}=(0,a.useRouter)(),[t,r]=s.default.useState(""),i=s.default.useRef(e);return s.default.useEffect(()=>{if(i.current!==e)if(i.current=e,document.title)r(document.title);else{var t;let i=document.querySelector("h1");r((null!=(t=null==i?void 0:i.innerText)?t:null==i?void 0:i.textContent)||e)}},[e]),(0,n.jsx)("p",{"aria-live":"assertive",id:"__next-route-announcer__",role:"alert",style:o,children:t})},u=l;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},41862:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizeLocalePath",{enumerable:!0,get:function(){return i}});let r=new WeakMap;function i(e,t){let i;if(!t)return{pathname:e};let n=r.get(t);n||(n=t.map(e=>e.toLowerCase()),r.set(t,n));let s=e.split("/",2);if(!s[1])return{pathname:e};let a=s[1].toLowerCase(),o=n.indexOf(a);return o<0?{pathname:e}:(i=t[o],{pathname:e=e.slice(i.length+1)||"/",detectedLocale:i})}},41921:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let i=r(78040),n=r(8480),s=r(40990),a=r(2746),o=r(68205),l=r(51533),u=r(63069),c=r(98069);function d(e,t,r){let d,h="string"==typeof t?t:(0,n.formatWithValidation)(t),f=h.match(/^[a-zA-Z]{1,}:\/\//),p=f?h.slice(f[0].length):h;if((p.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+h+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,a.normalizeRepeatedSlashes)(p);h=(f?f[0]:"")+t}if(!(0,l.isLocalURL)(h))return r?[h]:h;try{d=new URL(h.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(h,d);e.pathname=(0,o.normalizePathTrailingSlash)(e.pathname);let t="";if((0,u.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,i.searchParamsToUrlQuery)(e.searchParams),{result:a,params:o}=(0,c.interpolateAs)(e.pathname,e.pathname,r);a&&(t=(0,n.formatWithValidation)({pathname:a,hash:e.hash,query:(0,s.omit)(r,o)}))}let a=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[a,t||a]:a}catch(e){return r?[h]:h}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},42616:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"detectDomainLocale",{enumerable:!0,get:function(){return r}});let r=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r]};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},43802:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{APP_BUILD_MANIFEST:function(){return y},APP_CLIENT_INTERNALS:function(){return J},APP_PATHS_MANIFEST:function(){return g},APP_PATH_ROUTES_MANIFEST:function(){return v},BARREL_OPTIMIZATION_PREFIX:function(){return H},BLOCKED_PAGES:function(){return F},BUILD_ID_FILE:function(){return L},BUILD_MANIFEST:function(){return m},CLIENT_PUBLIC_FILES_PATH:function(){return D},CLIENT_REFERENCE_MANIFEST:function(){return q},CLIENT_STATIC_FILES_PATH:function(){return $},CLIENT_STATIC_FILES_RUNTIME_AMP:function(){return Q},CLIENT_STATIC_FILES_RUNTIME_MAIN:function(){return Y},CLIENT_STATIC_FILES_RUNTIME_MAIN_APP:function(){return K},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS:function(){return et},CLIENT_STATIC_FILES_RUNTIME_POLYFILLS_SYMBOL:function(){return er},CLIENT_STATIC_FILES_RUNTIME_REACT_REFRESH:function(){return Z},CLIENT_STATIC_FILES_RUNTIME_WEBPACK:function(){return ee},COMPILER_INDEXES:function(){return s},COMPILER_NAMES:function(){return n},CONFIG_FILES:function(){return N},DEFAULT_RUNTIME_WEBPACK:function(){return ei},DEFAULT_SANS_SERIF_FONT:function(){return el},DEFAULT_SERIF_FONT:function(){return eo},DEV_CLIENT_MIDDLEWARE_MANIFEST:function(){return k},DEV_CLIENT_PAGES_MANIFEST:function(){return C},DYNAMIC_CSS_MANIFEST:function(){return V},EDGE_RUNTIME_WEBPACK:function(){return en},EDGE_UNSUPPORTED_NODE_APIS:function(){return ef},EXPORT_DETAIL:function(){return w},EXPORT_MARKER:function(){return S},FUNCTIONS_CONFIG_MANIFEST:function(){return b},IMAGES_MANIFEST:function(){return O},INTERCEPTION_ROUTE_REWRITE_MANIFEST:function(){return X},MIDDLEWARE_BUILD_MANIFEST:function(){return G},MIDDLEWARE_MANIFEST:function(){return I},MIDDLEWARE_REACT_LOADABLE_MANIFEST:function(){return z},MODERN_BROWSERSLIST_TARGET:function(){return i.default},NEXT_BUILTIN_DOCUMENT:function(){return B},NEXT_FONT_MANIFEST:function(){return P},PAGES_MANIFEST:function(){return p},PHASE_DEVELOPMENT_SERVER:function(){return d},PHASE_EXPORT:function(){return l},PHASE_INFO:function(){return f},PHASE_PRODUCTION_BUILD:function(){return u},PHASE_PRODUCTION_SERVER:function(){return c},PHASE_TEST:function(){return h},PRERENDER_MANIFEST:function(){return R},REACT_LOADABLE_MANIFEST:function(){return j},ROUTES_MANIFEST:function(){return x},RSC_MODULE_TYPES:function(){return eh},SERVER_DIRECTORY:function(){return M},SERVER_FILES_MANIFEST:function(){return T},SERVER_PROPS_ID:function(){return ea},SERVER_REFERENCE_MANIFEST:function(){return W},STATIC_PROPS_ID:function(){return es},STATIC_STATUS_PAGES:function(){return eu},STRING_LITERAL_DROP_BUNDLE:function(){return U},SUBRESOURCE_INTEGRITY_MANIFEST:function(){return E},SYSTEM_ENTRYPOINTS:function(){return ep},TRACE_OUTPUT_VERSION:function(){return ec},TURBOPACK_CLIENT_MIDDLEWARE_MANIFEST:function(){return A},TURBO_TRACE_DEFAULT_MEMORY_LIMIT:function(){return ed},UNDERSCORE_NOT_FOUND_ROUTE:function(){return a},UNDERSCORE_NOT_FOUND_ROUTE_ENTRY:function(){return o},WEBPACK_STATS:function(){return _}});let i=r(64252)._(r(86582)),n={client:"client",server:"server",edgeServer:"edge-server"},s={[n.client]:0,[n.server]:1,[n.edgeServer]:2},a="/_not-found",o=""+a+"/page",l="phase-export",u="phase-production-build",c="phase-production-server",d="phase-development-server",h="phase-test",f="phase-info",p="pages-manifest.json",_="webpack-stats.json",g="app-paths-manifest.json",v="app-path-routes-manifest.json",m="build-manifest.json",y="app-build-manifest.json",b="functions-config-manifest.json",E="subresource-integrity-manifest",P="next-font-manifest",S="export-marker.json",w="export-detail.json",R="prerender-manifest.json",x="routes-manifest.json",O="images-manifest.json",T="required-server-files.json",C="_devPagesManifest.json",I="middleware-manifest.json",A="_clientMiddlewareManifest.json",k="_devMiddlewareManifest.json",j="react-loadable-manifest.json",M="server",N=["next.config.js","next.config.mjs","next.config.ts"],L="BUILD_ID",F=["/_document","/_app","/_error"],D="public",$="static",U="__NEXT_DROP_CLIENT_FILE__",B="__NEXT_BUILTIN_DOCUMENT__",H="__barrel_optimize__",q="client-reference-manifest",W="server-reference-manifest",G="middleware-build-manifest",z="middleware-react-loadable-manifest",X="interception-route-rewrite-manifest",V="dynamic-css-manifest",Y="main",K=""+Y+"-app",J="app-pages-internals",Z="react-refresh",Q="amp",ee="webpack",et="polyfills",er=Symbol(et),ei="webpack-runtime",en="edge-runtime-webpack",es="__N_SSG",ea="__N_SSP",eo={name:"Times New Roman",xAvgCharWidth:821,azAvgWidth:854.3953488372093,unitsPerEm:2048},el={name:"Arial",xAvgCharWidth:904,azAvgWidth:934.5116279069767,unitsPerEm:2048},eu=["/500"],ec=1,ed=6e3,eh={client:"client",server:"server"},ef=["clearImmediate","setImmediate","BroadcastChannel","ByteLengthQueuingStrategy","CompressionStream","CountQueuingStrategy","DecompressionStream","DomException","MessageChannel","MessageEvent","MessagePort","ReadableByteStreamController","ReadableStreamBYOBRequest","ReadableStreamDefaultController","TransformStreamDefaultController","WritableStreamDefaultController"],ep=new Set([Y,Z,Q,K]);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44181:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTTPAccessErrorStatus:function(){return r},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return n},getAccessFallbackErrorTypeByStatus:function(){return o},getAccessFallbackHTTPStatus:function(){return a},isHTTPAccessFallbackError:function(){return s}});let r={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},i=new Set(Object.values(r)),n="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===n&&i.has(Number(r))}function a(e){return Number(e.digest.split(";")[1])}function o(e){switch(e){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},46711:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return s}});let i=r(82889),n=r(73716);function s(e,t,r,s){if(!t||t===r)return e;let a=e.toLowerCase();return!s&&((0,n.pathHasPrefix)(a,"/api")||(0,n.pathHasPrefix)(a,"/"+t.toLowerCase()))?e:(0,i.addPathPrefix)(e,"/"+t)}},49163:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return a}});let i=r(37188),n=/\/[^/]*\[[^/]+\][^/]*(?=\/|$)/,s=/\/\[[^/]+\](?=\/|$)/;function a(e,t){return(void 0===t&&(t=!0),(0,i.isInterceptionRouteAppPath)(e)&&(e=(0,i.extractInterceptionRouteInformation)(e).interceptedRoute),t)?s.test(e):n.test(e)}},50938:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},51533:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return s}});let i=r(2746),n=r(16023);function s(e){if(!(0,i.isAbsoluteUrl)(e))return!0;try{let t=(0,i.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,n.hasBasePath)(r.pathname)}catch(e){return!1}}},51924:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return n}});let r=/[|\\{}()[\]^$+*?.-]/,i=/[|\\{}()[\]^$+*?.-]/g;function n(e){return r.test(e)?e.replace(i,"\\$&"):e}},53132:(e,t)=>{"use strict";function r(e,t){if(void 0===t&&(t={}),t.onlyHashChange)return void e();let r=document.documentElement,i=r.style.scrollBehavior;r.style.scrollBehavior="auto",t.dontForceLayout||r.getClientRects(),e(),r.style.scrollBehavior=i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSmoothScroll",{enumerable:!0,get:function(){return r}})},54591:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return i}}),r(68205);let i=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54902:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},55040:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},s=t.split(i),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],u=l.indexOf("=");if(!(u<0)){var c=l.substr(0,u).trim(),d=l.substr(++u,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==n[c]&&(n[c]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return n},t.serialize=function(e,t,i){var s=i||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var u=s.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(u)}if(s.domain){if(!n.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!n.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},62092:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return s}});let i=r(82889),n=r(68205);function s(e,t){return(0,n.normalizePathTrailingSlash)((0,i.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},62591:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{REDIRECT_ERROR_CODE:function(){return n},RedirectType:function(){return s},isRedirectError:function(){return a}});let i=r(21017),n="NEXT_REDIRECT";var s=function(e){return e.push="push",e.replace="replace",e}({});function a(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,s]=t,a=t.slice(2,-2).join(";"),o=Number(t.at(-2));return r===n&&("replace"===s||"push"===s)&&"string"==typeof a&&!isNaN(o)&&o in i.RedirectStatusCode}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return i.getSortedRouteObjects},getSortedRoutes:function(){return i.getSortedRoutes},isDynamicRoute:function(){return n.isDynamicRoute}});let i=r(33703),n=r(49163)},63123:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{BailoutToCSRError:function(){return i},isBailoutToCSRError:function(){return n}});let r="BAILOUT_TO_CLIENT_SIDE_RENDERING";class i extends Error{constructor(e){super("Bail out to client-side rendering: "+e),this.reason=e,this.digest=r}}function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}},63836:(e,t,r)=>{"use strict";function i(e,t){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeLocale",{enumerable:!0,get:function(){return i}}),r(83670),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64252:(e,t,r)=>{"use strict";function i(e){return e&&e.__esModule?e:{default:e}}r.r(t),r.d(t,{_:()=>i})},64359:(e,t)=>{"use strict";function r(e,t){let r=Object.keys(e);if(r.length!==Object.keys(t).length)return!1;for(let i=r.length;i--;){let n=r[i];if("query"===n){let r=Object.keys(e.query);if(r.length!==Object.keys(t.query).length)return!1;for(let i=r.length;i--;){let n=r[i];if(!t.query.hasOwnProperty(n)||e.query[n]!==t.query[n])return!1}}else if(!t.hasOwnProperty(n)||e[n]!==t[n])return!1}return!0}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"compareRouterStates",{enumerable:!0,get:function(){return r}})},65364:(e,t,r)=>{"use strict";var i,n;e.exports=(null==(i=r.g.process)?void 0:i.env)&&"object"==typeof(null==(n=r.g.process)?void 0:n.env)?r.g.process:r(15861)},66240:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},getProperError:function(){return s}});let i=r(38096);function n(e){return"object"==typeof e&&null!==e&&"name"in e&&"message"in e}function s(e){return n(e)?e:Object.defineProperty(Error((0,i.isPlainObject)(e)?function(e){let t=new WeakSet;return JSON.stringify(e,(e,r)=>{if("object"==typeof r&&null!==r){if(t.has(r))return"[Circular]";t.add(r)}return r})}(e):e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},67647:(e,t,r)=>{"use strict";e.exports=r(68036)},67952:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathSuffix",{enumerable:!0,get:function(){return n}});let i=r(83670);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,i.parsePath)(e);return""+r+t+n+s}},68036:()=>{"use strict";var e,t="undefined"!=typeof window?window:void 0,r="undefined"!=typeof globalThis?globalThis:t,i=Array.prototype,n=i.forEach,s=i.indexOf,a=null==r?void 0:r.navigator,o=null==r?void 0:r.document,l=null==r?void 0:r.location,u=null==r?void 0:r.fetch,c=null!=r&&r.XMLHttpRequest&&"withCredentials"in new r.XMLHttpRequest?r.XMLHttpRequest:void 0,d=null==r?void 0:r.AbortController,h=null==a?void 0:a.userAgent,f=null!=t?t:{},p={DEBUG:!1,LIB_VERSION:"1.258.6"},_="$copy_autocapture",g=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"],v=function(e){return e.GZipJS="gzip-js",e.Base64="base64",e}({}),m=["fatal","error","warning","log","info","debug"];function y(e,t){return -1!==e.indexOf(t)}var b=function(e){return e.trim()},E=function(e){return e.replace(/^\$/,"")},P=Array.isArray,S=Object.prototype,w=S.hasOwnProperty,R=S.toString,x=P||function(e){return"[object Array]"===R.call(e)},O=e=>"function"==typeof e,T=e=>e===Object(e)&&!x(e),C=e=>{if(T(e)){for(var t in e)if(w.call(e,t))return!1;return!0}return!1},I=e=>void 0===e,A=e=>"[object String]"==R.call(e),k=e=>A(e)&&0===e.trim().length,j=e=>null===e,M=e=>I(e)||j(e),N=e=>"[object Number]"==R.call(e),L=e=>"[object Boolean]"===R.call(e),F=e=>e instanceof FormData,D=e=>y(g,e),$=e=>{var r={t:function(r){if(t&&(p.DEBUG||f.POSTHOG_DEBUG)&&!I(t.console)&&t.console){for(var i=("__rrweb_original__"in t.console[r])?t.console[r].__rrweb_original__:t.console[r],n=arguments.length,s=Array(n>1?n-1:0),a=1;a<n;a++)s[a-1]=arguments[a];i(e,...s)}},info:function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];r.t("log",...t)},warn:function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];r.t("warn",...t)},error:function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];r.t("error",...t)},critical:function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];console.error(e,...r)},uninitializedWarning:e=>{r.error("You must initialize PostHog before calling "+e)},createLogger:t=>$(e+" "+t)};return r},U=$("[PostHog.js]"),B=U.createLogger,H=B("[ExternalScriptsLoader]"),q=(e,t,r)=>{if(e.config.disable_external_dependency_loading)return H.warn(t+" was requested but loading of external scripts is disabled."),r("Loading of external scripts is disabled");var i=null==o?void 0:o.querySelectorAll("script");if(i){for(var n=0;n<i.length;n++)if(i[n].src===t)return r()}var s=()=>{if(!o)return r("document not found");var i=o.createElement("script");if(i.type="text/javascript",i.crossOrigin="anonymous",i.src=t,i.onload=e=>r(void 0,e),i.onerror=e=>r(e),e.config.prepare_external_dependency_script&&(i=e.config.prepare_external_dependency_script(i)),!i)return r("prepare_external_dependency_script returned null");var n,s=o.querySelectorAll("body > script");s.length>0?null==(n=s[0].parentNode)||n.insertBefore(i,s[0]):o.body.appendChild(i)};null!=o&&o.body?s():null==o||o.addEventListener("DOMContentLoaded",s)};function W(){return(W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)({}).hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e}).apply(null,arguments)}function G(e,t){if(null==e)return{};var r={};for(var i in e)if(({}).hasOwnProperty.call(e,i)){if(-1!==t.indexOf(i))continue;r[i]=e[i]}return r}f.__PosthogExtensions__=f.__PosthogExtensions__||{},f.__PosthogExtensions__.loadExternalDependency=(e,t,r)=>{var i="/static/"+t+".js?v="+e.version;"remote-config"===t&&(i="/array/"+e.config.token+"/config.js"),"toolbar"===t&&(i=i+"&t="+3e5*Math.floor(Date.now()/3e5));var n=e.requestRouter.endpointFor("assets",i);q(e,n,r)},f.__PosthogExtensions__.loadSiteApp=(e,t,r)=>{var i=e.requestRouter.endpointFor("api",t);q(e,i,r)};var z={};function X(e,t,r){if(x(e)){if(n&&e.forEach===n)e.forEach(t,r);else if("length"in e&&e.length===+e.length){for(var i=0,s=e.length;i<s;i++)if(i in e&&t.call(r,e[i],i)===z)return}}}function V(e,t,r){if(!M(e)){if(x(e))return X(e,t,r);if(F(e)){for(var i of e.entries())if(t.call(r,i[1],i[0])===z)return}else for(var n in e)if(w.call(e,n)&&t.call(r,e[n],n)===z)return}}var Y=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return X(r,function(t){for(var r in t)void 0!==t[r]&&(e[r]=t[r])}),e},K=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];return X(r,function(t){X(t,function(t){e.push(t)})}),e};function J(e){for(var t=Object.keys(e),r=t.length,i=Array(r);r--;)i[r]=[t[r],e[t[r]]];return i}var Z=function(e){try{return e()}catch(e){return}},Q=function(e){return function(){try{for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return e.apply(this,r)}catch(e){U.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),U.critical(e)}}},ee=function(e){var t={};return V(e,function(e,r){(A(e)&&e.length>0||N(e))&&(t[r]=e)}),t},et=["herokuapp.com","vercel.app","netlify.app"];function er(e,t){for(var r=0;r<e.length;r++)if(t(e[r]))return e[r]}function ei(e,t,r,i){var{capture:n=!1,passive:s=!0}=null!=i?i:{};null==e||e.addEventListener(t,r,{capture:n,passive:s})}var en="$people_distinct_id",es="__alias",ea="__timers",eo="$autocapture_disabled_server_side",el="$heatmaps_enabled_server_side",eu="$exception_capture_enabled_server_side",ec="$error_tracking_suppression_rules",ed="$error_tracking_capture_extension_exceptions",eh="$web_vitals_enabled_server_side",ef="$dead_clicks_enabled_server_side",ep="$web_vitals_allowed_metrics",e_="$session_recording_enabled_server_side",eg="$console_log_recording_enabled_server_side",ev="$session_recording_network_payload_capture",em="$session_recording_masking",ey="$session_recording_canvas_recording",eb="$replay_sample_rate",eE="$replay_minimum_duration",eP="$replay_script_config",eS="$sesid",ew="$session_is_sampled",eR="$session_recording_url_trigger_activated_session",ex="$session_recording_event_trigger_activated_session",eO="$enabled_feature_flags",eT="$early_access_features",eC="$feature_flag_details",eI="$stored_person_properties",eA="$stored_group_properties",ek="$surveys",ej="$surveys_activated",eM="$flag_call_reported",eN="$user_state",eL="$client_session_props",eF="$capture_rate_limit",eD="$initial_campaign_params",e$="$initial_referrer_info",eU="$initial_person_info",eB="$epp",eH="__POSTHOG_TOOLBAR__",eq="$posthog_cookieless",eW=[en,es,"__cmpns",ea,e_,el,eS,eO,ec,eN,eT,eC,eA,eI,ek,eM,eL,eF,eD,e$,eB,eU];function eG(e){return e instanceof Element&&(e.id===eH||!(null==e.closest||!e.closest(".toolbar-global-fade-container")))}function ez(e){return!!e&&1===e.nodeType}function eX(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function eV(e){return!!e&&3===e.nodeType}function eY(e){return!!e&&11===e.nodeType}function eK(e){return e?b(e).split(/\s+/):[]}function eJ(e){var r=null==t?void 0:t.location.href;return!!(r&&e&&e.some(e=>r.match(e)))}function eZ(e){var t="";switch(typeof e.className){case"string":t=e.className;break;case"object":t=(e.className&&"baseVal"in e.className?e.className.baseVal:null)||e.getAttribute("class")||"";break;default:t=""}return eK(t)}function eQ(e){return M(e)?null:b(e).split(/(\s+)/).filter(e=>tr(e)).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function e0(e){var t="";return e4(e)&&!e6(e)&&e.childNodes&&e.childNodes.length&&V(e.childNodes,function(e){var r;eV(e)&&e.textContent&&(t+=null!=(r=eQ(e.textContent))?r:"")}),b(t)}function e1(e){var t;return I(e.target)?e.srcElement||null:null!=(t=e.target)&&t.shadowRoot?e.composedPath()[0]||null:e.target||null}var e2=["a","button","form","input","select","textarea","label"];function e3(e){var t=e.parentNode;return!(!t||!ez(t))&&t}function e4(e){for(var t=e;t.parentNode&&!eX(t,"body");t=t.parentNode){var r=eZ(t);if(y(r,"ph-sensitive")||y(r,"ph-no-capture"))return!1}if(y(eZ(e),"ph-include"))return!0;var i=e.type||"";if(A(i))switch(i.toLowerCase()){case"hidden":case"password":return!1}var n=e.name||e.id||"";return!(A(n)&&/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(n.replace(/[^a-zA-Z0-9]/g,"")))}function e6(e){return!!(eX(e,"input")&&!["button","checkbox","submit","reset"].includes(e.type)||eX(e,"select")||eX(e,"textarea")||"true"===e.getAttribute("contenteditable"))}var e5="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",e8=RegExp("^(?:"+e5+")$"),e9=new RegExp(e5),e7="\\d{3}-?\\d{2}-?\\d{4}",te=RegExp("^("+e7+")$"),tt=RegExp("("+e7+")");function tr(e,t){return void 0===t&&(t=!0),!(M(e)||A(e)&&(e=b(e),(t?e8:e9).test((e||"").replace(/[- ]/g,""))||(t?te:tt).test(e)))&&!0}function ti(e){var t=e0(e);return tr(t=(t+" "+function e(t){var r="";return t&&t.childNodes&&t.childNodes.length&&V(t.childNodes,function(t){var i;if(t&&"span"===(null==(i=t.tagName)?void 0:i.toLowerCase()))try{var n=e0(t);r=(r+" "+n).trim(),t.childNodes&&t.childNodes.length&&(r=(r+" "+e(t)).trim())}catch(e){U.error("[AutoCapture]",e)}}),r}(e)).trim())?t:""}function tn(e){return e.replace(/"|\\"/g,'\\"')}class ts{constructor(){this.clicks=[]}isRageClick(e,t,r){var i=this.clicks[this.clicks.length-1];if(i&&Math.abs(e-i.x)+Math.abs(t-i.y)<30&&r-i.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:r}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:r}];return!1}}var ta=["localhost","127.0.0.1"],to=e=>{var t=null==o?void 0:o.createElement("a");return I(t)?null:(t.href=e,t)},tl=function(e,t){void 0===t&&(t="&");var r,i,n=[];return V(e,function(e,t){I(e)||I(t)||"undefined"===t||(r=encodeURIComponent(e instanceof File?e.name:e.toString()),i=encodeURIComponent(t),n[n.length]=i+"="+r)}),n.join(t)},tu=function(e,t){for(var r,i=((e.split("#")[0]||"").split(/\?(.*)/)[1]||"").replace(/^\?+/g,"").split("&"),n=0;n<i.length;n++){var s=i[n].split("=");if(s[0]===t){r=s;break}}if(!x(r)||r.length<2)return"";var a=r[1];try{a=decodeURIComponent(a)}catch(e){U.error("Skipping decoding for malformed query param: "+a)}return a.replace(/\+/g," ")},tc=function(e,t,r){if(!e||!t||!t.length)return e;for(var i=e.split("#"),n=i[0]||"",s=i[1],a=n.split("?"),o=a[1],l=a[0],u=(o||"").split("&"),c=[],d=0;d<u.length;d++){var h=u[d].split("=");x(h)&&(t.includes(h[0])?c.push(h[0]+"="+r):c.push(u[d]))}var f=l;return null!=o&&(f+="?"+c.join("&")),null!=s&&(f+="#"+s),f},td=function(e,t){var r=e.match(RegExp(t+"=([^&]*)"));return r?r[1]:null},th=B("[AutoCapture]");function tf(e,t){return t.length>e?t.slice(0,e)+"...":t}class tp{constructor(e){this.i=!1,this.o=null,this.rageclicks=new ts,this.h=!1,this.instance=e,this.m=null}get S(){var e,t,r=T(this.instance.config.autocapture)?this.instance.config.autocapture:{};return r.url_allowlist=null==(e=r.url_allowlist)?void 0:e.map(e=>new RegExp(e)),r.url_ignorelist=null==(t=r.url_ignorelist)?void 0:t.map(e=>new RegExp(e)),r}$(){if(this.isBrowserSupported()){if(t&&o){var e=e=>{e=e||(null==t?void 0:t.event);try{this.k(e)}catch(e){th.error("Failed to capture event",e)}};if(ei(o,"submit",e,{capture:!0}),ei(o,"change",e,{capture:!0}),ei(o,"click",e,{capture:!0}),this.S.capture_copied_text){var r=e=>{e=e||(null==t?void 0:t.event),this.k(e,_)};ei(o,"copy",r,{capture:!0}),ei(o,"cut",r,{capture:!0})}}}else th.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this.i&&(this.$(),this.i=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this.h=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[eo]:!!e.autocapture_opt_out}),this.o=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this.m=e}getElementSelectors(e){var t,r=[];return null==(t=this.m)||t.forEach(t=>{var i=null==o?void 0:o.querySelectorAll(t);null==i||i.forEach(i=>{e===i&&r.push(t)})}),r}get isEnabled(){var e,t,r=null==(e=this.instance.persistence)?void 0:e.props[eo];if(j(this.o)&&!L(r)&&!this.instance.I())return!1;var i=null!=(t=this.o)?t:!!r;return!!this.instance.config.autocapture&&!i}k(e,r){if(void 0===r&&(r="$autocapture"),this.isEnabled){var i,n=e1(e);eV(n)&&(n=n.parentNode||null),"$autocapture"===r&&"click"===e.type&&e instanceof MouseEvent&&this.instance.config.rageclick&&null!=(i=this.rageclicks)&&i.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this.k(e,"$rageclick");var s=r===_;if(n&&function(e,r,i,n,s){if(void 0===i&&(i=void 0),!t||!e||eX(e,"html")||!ez(e)||null!=(a=i)&&a.url_allowlist&&!eJ(i.url_allowlist)||null!=(o=i)&&o.url_ignorelist&&eJ(i.url_ignorelist))return!1;if(null!=(l=i)&&l.dom_event_allowlist){var a,o,l,u=i.dom_event_allowlist;if(u&&!u.some(e=>r.type===e))return!1}for(var c=!1,d=[e],h=!0,f=e;f.parentNode&&!eX(f,"body");)if(eY(f.parentNode))d.push(f.parentNode.host),f=f.parentNode.host;else{if(!(h=e3(f)))break;if(n||e2.indexOf(h.tagName.toLowerCase())>-1)c=!0;else{var p=t.getComputedStyle(h);p&&"pointer"===p.getPropertyValue("cursor")&&(c=!0)}d.push(h),f=h}if(!function(e,t){var r=null==t?void 0:t.element_allowlist;if(I(r))return!0;var i,n=function(e){if(r.some(t=>e.tagName.toLowerCase()===t))return{v:!0}};for(var s of e)if(i=n(s))return i.v;return!1}(d,i)||!function(e,t){var r=null==t?void 0:t.css_selector_allowlist;if(I(r))return!0;var i,n=function(e){if(r.some(t=>e.matches(t)))return{v:!0}};for(var s of e)if(i=n(s))return i.v;return!1}(d,i))return!1;var _=t.getComputedStyle(e);if(_&&"pointer"===_.getPropertyValue("cursor")&&"click"===r.type)return!0;var g=e.tagName.toLowerCase();switch(g){case"html":return!1;case"form":return(s||["submit"]).indexOf(r.type)>=0;case"input":case"select":case"textarea":return(s||["change","click"]).indexOf(r.type)>=0;default:return c?(s||["click"]).indexOf(r.type)>=0:(s||["click"]).indexOf(r.type)>=0&&(e2.indexOf(g)>-1||"true"===e.getAttribute("contenteditable"))}}(n,e,this.S,s,s?["copy","cut"]:void 0)){var{props:a,explicitNoCapture:o}=function(e,r){for(var i,n,{e:s,maskAllElementAttributes:a,maskAllText:o,elementAttributeIgnoreList:l,elementsChainAsString:u}=r,c=[e],d=e;d.parentNode&&!eX(d,"body");)eY(d.parentNode)?(c.push(d.parentNode.host),d=d.parentNode.host):(c.push(d.parentNode),d=d.parentNode);var h,f=[],p={},_=!1,g=!1;if(V(c,e=>{var t=e4(e);"a"===e.tagName.toLowerCase()&&(_=e.getAttribute("href"),_=t&&_&&tr(_)&&_),y(eZ(e),"ph-no-capture")&&(g=!0),f.push(function(e,t,r,i){var n=e.tagName.toLowerCase(),s={tag_name:n};e2.indexOf(n)>-1&&!r&&("a"===n.toLowerCase()||"button"===n.toLowerCase()?s.$el_text=tf(1024,ti(e)):s.$el_text=tf(1024,e0(e)));var a=eZ(e);a.length>0&&(s.classes=a.filter(function(e){return""!==e})),V(e.attributes,function(r){var n;if((!e6(e)||-1!==["name","id","class","aria-label"].indexOf(r.name))&&(null==i||!i.includes(r.name))&&!t&&tr(r.value)&&(!A(n=r.name)||"_ngcontent"!==n.substring(0,10)&&"_nghost"!==n.substring(0,7))){var a=r.value;"class"===r.name&&(a=eK(a).join(" ")),s["attr__"+r.name]=tf(1024,a)}});for(var o=1,l=1,u=e;u=function(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do t=t.previousSibling;while(t&&!ez(t));return t}(u);)o++,u.tagName===e.tagName&&l++;return s.nth_child=o,s.nth_of_type=l,s}(e,a,o,l)),Y(p,function(e){if(!e4(e))return{};var t={};return V(e.attributes,function(e){if(e.name&&0===e.name.indexOf("data-ph-capture-attribute")){var r=e.name.replace("data-ph-capture-attribute-",""),i=e.value;r&&i&&tr(i)&&(t[r]=i)}}),t}(e))}),g)return{props:{},explicitNoCapture:g};if(o||("a"===e.tagName.toLowerCase()||"button"===e.tagName.toLowerCase()?f[0].$el_text=ti(e):f[0].$el_text=e0(e)),_){f[0].attr__href=_;var v,m,b=null==(v=to(_))?void 0:v.host,E=null==t||null==(m=t.location)?void 0:m.host;b&&E&&b!==E&&(h=_)}return{props:Y({$event_type:s.type,$ce_version:1},u?{}:{$elements:f},{$elements_chain:f.map(e=>{var t,r,i,n={text:null==(r=e.$el_text)?void 0:r.slice(0,400),tag_name:e.tag_name,href:null==(i=e.attr__href)?void 0:i.slice(0,2048),attr_class:(t=e.attr__class)?x(t)?t:eK(t):void 0,attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return J(e).filter(e=>{var[t]=e;return 0===t.indexOf("attr__")}).forEach(e=>{var[t,r]=e;return n.attributes[t]=r}),n}).map(e=>{var t,r,i="";if(e.tag_name&&(i+=e.tag_name),e.attr_class)for(var n of(e.attr_class.sort(),e.attr_class))i+="."+n.replace(/"/g,"");var s=W({},e.text?{text:e.text}:{},{"nth-child":null!=(t=e.nth_child)?t:0,"nth-of-type":null!=(r=e.nth_of_type)?r:0},e.href?{href:e.href}:{},e.attr_id?{attr_id:e.attr_id}:{},e.attributes),a={};return J(s).sort((e,t)=>{var[r]=e,[i]=t;return r.localeCompare(i)}).forEach(e=>{var[t,r]=e;return a[tn(t.toString())]=tn(r.toString())}),i+=":",i+=J(a).map(e=>{var[t,r]=e;return t+'="'+r+'"'}).join("")}).join(";")},null!=(i=f[0])&&i.$el_text?{$el_text:null==(n=f[0])?void 0:n.$el_text}:{},h&&"click"===s.type?{$external_click_url:h}:{},p)}}(n,{e:e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.S.element_attribute_ignorelist,elementsChainAsString:this.h});if(o)return!1;var l=this.getElementSelectors(n);if(l&&l.length>0&&(a.$element_selectors=l),r===_){var u,c=eQ(null==t||null==(u=t.getSelection())?void 0:u.toString()),d=e.type||"clipboard";if(!c)return!1;a.$selected_content=c,a.$copy_type=d}return this.instance.capture(r,a),!0}}}isBrowserSupported(){return O(null==o?void 0:o.querySelectorAll)}}Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return N(e)&&isFinite(e)&&Math.floor(e)===e});var t_="0123456789abcdef";class tg{constructor(e){if(this.bytes=e,16!==e.length)throw TypeError("not 128-bit length")}static fromFieldsV7(e,t,r,i){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(r)||!Number.isInteger(i)||e<0||t<0||r<0||i<0||e>0xffffffffffff||t>4095||r>0x3fffffff||i>0xffffffff)throw RangeError("invalid field value");var n=new Uint8Array(16);return n[0]=e/0x10000000000,n[1]=e/0x100000000,n[2]=e/0x1000000,n[3]=e/65536,n[4]=e/256,n[5]=e,n[6]=112|t>>>8,n[7]=t,n[8]=128|r>>>24,n[9]=r>>>16,n[10]=r>>>8,n[11]=r,n[12]=i>>>24,n[13]=i>>>16,n[14]=i>>>8,n[15]=i,new tg(n)}toString(){for(var e="",t=0;t<this.bytes.length;t++)e=e+t_.charAt(this.bytes[t]>>>4)+t_.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+="-");if(36!==e.length)throw Error("Invalid UUIDv7 was generated");return e}clone(){return new tg(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(var t=0;t<16;t++){var r=this.bytes[t]-e.bytes[t];if(0!==r)return Math.sign(r)}return 0}}class tv{constructor(){this.P=0,this.R=0,this.T=new tb}generate(){var e=this.generateOrAbort();if(I(e)){this.P=0;var t=this.generateOrAbort();if(I(t))throw Error("Could not generate UUID after timestamp reset");return t}return e}generateOrAbort(){var e=Date.now();if(e>this.P)this.P=e,this.M();else{if(!(e+1e4>this.P))return;this.R++,this.R>0x3ffffffffff&&(this.P++,this.M())}return tg.fromFieldsV7(this.P,Math.trunc(this.R/0x40000000),0x3fffffff&this.R,this.T.nextUint32())}M(){this.R=1024*this.T.nextUint32()+(1023&this.T.nextUint32())}}var tm,ty=e=>{if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw Error("no cryptographically strong RNG available");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};t&&!I(t.crypto)&&crypto.getRandomValues&&(ty=e=>crypto.getRandomValues(e));class tb{constructor(){this.C=new Uint32Array(8),this.F=1/0}nextUint32(){return this.F>=this.C.length&&(ty(this.C),this.F=0),this.C[this.F++]}}var tE=()=>tP().toString(),tP=()=>(tm||(tm=new tv)).generate(),tS="",tw=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i,tR={O:()=>!!o,A:function(e){U.error("cookieStore error: "+e)},D:function(e){if(o){try{for(var t=e+"=",r=o.cookie.split(";").filter(e=>e.length),i=0;i<r.length;i++){for(var n=r[i];" "==n.charAt(0);)n=n.substring(1,n.length);if(0===n.indexOf(t))return decodeURIComponent(n.substring(t.length,n.length))}}catch(e){}return null}},L:function(e){var t;try{t=JSON.parse(tR.D(e))||{}}catch(e){}return t},j:function(e,t,r,i,n){if(o)try{var s="",a="",l=function(e,t){if(t){var r=function(e,t){if(void 0===t&&(t=o),tS)return tS;if(!t||["localhost","127.0.0.1"].includes(e))return"";for(var r=e.split("."),i=Math.min(r.length,8),n="dmn_chk_"+tE();!tS&&i--;){var s=r.slice(i).join("."),a=n+"=1;domain=."+s+";path=/";t.cookie=a+";max-age=3",t.cookie.includes(n)&&(t.cookie=a+";max-age=0",tS=s)}return tS}(e);if(!r){var i,n=(i=e.match(tw))?i[0]:"";n!==r&&U.info("Warning: cookie subdomain discovery mismatch",n,r),r=n}return r?"; domain=."+r:""}return""}(o.location.hostname,i);if(r){var u=new Date;u.setTime(u.getTime()+24*r*36e5),s="; expires="+u.toUTCString()}n&&(a="; secure");var c=e+"="+encodeURIComponent(JSON.stringify(t))+s+"; SameSite=Lax; path=/"+l+a;return c.length>3686.4&&U.warn("cookieStore warning: large cookie, len="+c.length),o.cookie=c,c}catch(e){return}},N:function(e,t){try{tR.j(e,"",-1,t)}catch(e){return}}},tx=null,tO={O:function(){if(!j(tx))return tx;var e=!0;if(I(t))e=!1;else try{var r="__mplssupport__";tO.j(r,"xyz"),'"xyz"'!==tO.D(r)&&(e=!1),tO.N(r)}catch(t){e=!1}return e||U.error("localStorage unsupported; falling back to cookie store"),tx=e,e},A:function(e){U.error("localStorage error: "+e)},D:function(e){try{return null==t?void 0:t.localStorage.getItem(e)}catch(e){tO.A(e)}return null},L:function(e){try{return JSON.parse(tO.D(e))||{}}catch(e){}return null},j:function(e,r){try{null==t||t.localStorage.setItem(e,JSON.stringify(r))}catch(e){tO.A(e)}},N:function(e){try{null==t||t.localStorage.removeItem(e)}catch(e){tO.A(e)}}},tT=["distinct_id",eS,ew,eB,eU],tC=W({},tO,{L:function(e){try{var t={};try{t=tR.L(e)||{}}catch(e){}var r=Y(t,JSON.parse(tO.D(e)||"{}"));return tO.j(e,r),r}catch(e){}return null},j:function(e,t,r,i,n,s){try{tO.j(e,t,void 0,void 0,s);var a={};tT.forEach(e=>{t[e]&&(a[e]=t[e])}),Object.keys(a).length&&tR.j(e,a,r,i,n,s)}catch(e){tO.A(e)}},N:function(e,r){try{null==t||t.localStorage.removeItem(e),tR.N(e,r)}catch(e){tO.A(e)}}}),tI={},tA={O:function(){return!0},A:function(e){U.error("memoryStorage error: "+e)},D:function(e){return tI[e]||null},L:function(e){return tI[e]||null},j:function(e,t){tI[e]=t},N:function(e){delete tI[e]}},tk=null,tj={O:function(){if(!j(tk))return tk;if(tk=!0,I(t))tk=!1;else try{var e="__support__";tj.j(e,"xyz"),'"xyz"'!==tj.D(e)&&(tk=!1),tj.N(e)}catch(e){tk=!1}return tk},A:function(e){U.error("sessionStorage error: ",e)},D:function(e){try{return null==t?void 0:t.sessionStorage.getItem(e)}catch(e){tj.A(e)}return null},L:function(e){try{return JSON.parse(tj.D(e))||null}catch(e){}return null},j:function(e,r){try{null==t||t.sessionStorage.setItem(e,JSON.stringify(r))}catch(e){tj.A(e)}},N:function(e){try{null==t||t.sessionStorage.removeItem(e)}catch(e){tj.A(e)}}},tM=function(e){return e[e.PENDING=-1]="PENDING",e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED",e}({});class tN{constructor(e){this._instance=e}get S(){return this._instance.config}get consent(){return this.U()?tM.DENIED:this.q}isOptedOut(){return this.consent===tM.DENIED||this.consent===tM.PENDING&&this.S.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this.B.j(this.H,+!!e,this.S.cookie_expiration,this.S.cross_subdomain_cookie,this.S.secure_cookie)}reset(){this.B.N(this.H,this.S.cross_subdomain_cookie)}get H(){var{token:e,opt_out_capturing_cookie_prefix:t}=this._instance.config;return(t||"__ph_opt_in_out_")+e}get q(){var e=this.B.D(this.H);return"1"===e?tM.GRANTED:"0"===e?tM.DENIED:tM.PENDING}get B(){if(!this.W){var e=this.S.opt_out_capturing_persistence_type;this.W="localStorage"===e?tO:tR;var t="localStorage"===e?tR:tO;t.D(this.H)&&(this.W.D(this.H)||this.optInOut("1"===t.D(this.H)),t.N(this.H,this.S.cross_subdomain_cookie))}return this.W}U(){return!!this.S.respect_dnt&&!!er([null==a?void 0:a.doNotTrack,null==a?void 0:a.msDoNotTrack,f.doNotTrack],e=>y([!0,1,"1","yes"],e))}}var tL=B("[Dead Clicks]"),tF=()=>!0,tD=e=>{var t,r=!(null==(t=e.instance.persistence)||!t.get_property(ef)),i=e.instance.config.capture_dead_clicks;return L(i)?i:r};class t${get lazyLoadedDeadClicksAutocapture(){return this.G}constructor(e,t,r){this.instance=e,this.isEnabled=t,this.onCapture=r,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[ef]:null==e?void 0:e.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.J(()=>{this.V()})}J(e){var t,r;null!=(t=f.__PosthogExtensions__)&&t.initDeadClicksAutocapture&&e(),null==(r=f.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this.instance,"dead-clicks-autocapture",t=>{t?tL.error("failed to load script",t):e()})}V(){var e;if(o){if(!this.G&&null!=(e=f.__PosthogExtensions__)&&e.initDeadClicksAutocapture){var t=T(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this.G=f.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this.G.start(o),tL.info("starting...")}}else tL.error("`document` not found. Cannot start.")}stop(){this.G&&(this.G.stop(),this.G=void 0,tL.info("stopping..."))}}function tU(e,t,r,i,n){return t>r&&(U.warn("min cannot be greater than max."),t=r),N(e)?e>r?(i&&U.warn(i+" cannot be  greater than max: "+r+". Using max value instead."),r):e<t?(i&&U.warn(i+" cannot be less than min: "+t+". Using min value instead."),t):e:(i&&U.warn(i+" must be a number. using max or fallback. max: "+r+", fallback: "+n),tU(n||r,t,r,i))}class tB{constructor(e){this.K={},this.Y=()=>{Object.keys(this.K).forEach(e=>{var t=this.X(e)+this.Z;t>=this.tt?delete this.K[e]:this.it(e,t)})},this.X=e=>this.K[String(e)],this.it=(e,t)=>{this.K[String(e)]=t},this.consumeRateLimit=e=>{var t,r=null!=(t=this.X(e))?t:this.tt;if(0===(r=Math.max(r-1,0)))return!0;this.it(e,r);var i,n=0===r;return n&&(null==(i=this.et)||i.call(this,e)),n},this.rt=e,this.et=this.rt.et,this.tt=tU(this.rt.bucketSize,0,100,"rate limiter bucket size"),this.Z=tU(this.rt.refillRate,0,this.tt,"rate limiter refill rate"),this.st=tU(this.rt.refillInterval,0,864e5,"rate limiter refill interval"),setInterval(()=>{this.Y()},this.st)}}var tH=B("[ExceptionAutocapture]");class tq{constructor(e){var r,i,n;this.nt=()=>{var e;if(t&&this.isEnabled&&null!=(e=f.__PosthogExtensions__)&&e.errorWrappingFunctions){var r=f.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,i=f.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,n=f.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.ot&&this.S.capture_unhandled_errors&&(this.ot=r(this.captureException.bind(this))),!this.lt&&this.S.capture_unhandled_rejections&&(this.lt=i(this.captureException.bind(this))),!this.ut&&this.S.capture_console_errors&&(this.ut=n(this.captureException.bind(this)))}catch(e){tH.error("failed to start",e),this.ht()}}},this._instance=e,this.dt=!(null==(r=this._instance.persistence)||!r.props[eu]),this.S=this.vt(),this.ct=new tB({refillRate:null!=(i=this._instance.config.error_tracking.__exceptionRateLimiterRefillRate)?i:1,bucketSize:null!=(n=this._instance.config.error_tracking.__exceptionRateLimiterBucketSize)?n:10,refillInterval:1e4}),this.startIfEnabled()}vt(){var e=this._instance.config.capture_exceptions,t={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return T(e)?t=W({},t,e):(I(e)?this.dt:e)&&(t=W({},t,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),t}get isEnabled(){return this.S.capture_console_errors||this.S.capture_unhandled_errors||this.S.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(tH.info("enabled"),this.J(this.nt))}J(e){var t,r;null!=(t=f.__PosthogExtensions__)&&t.errorWrappingFunctions&&e(),null==(r=f.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"exception-autocapture",t=>{if(t)return tH.error("failed to load script",t);e()})}ht(){var e,t,r;null==(e=this.ot)||e.call(this),this.ot=void 0,null==(t=this.lt)||t.call(this),this.lt=void 0,null==(r=this.ut)||r.call(this),this.ut=void 0}onRemoteConfig(e){var t=e.autocaptureExceptions;this.dt=!!t,this.S=this.vt(),this._instance.persistence&&this._instance.persistence.register({[eu]:this.dt}),this.startIfEnabled()}captureException(e){e.$exception_personURL=this._instance.requestRouter.endpointFor("ui")+"/project/"+this._instance.config.token+"/person/"+this._instance.get_distinct_id();var t,r=null!=(t=e.$exception_list[0].type)?t:"Exception";this.ct.consumeRateLimit(r)?tH.info("Skipping exception capture because of client rate limiting.",{exception:e.$exception_list[0].type}):this._instance.exceptions.sendExceptionEvent(e)}}function tW(e){return!I(Event)&&tG(e,Event)}function tG(e,t){try{return e instanceof t}catch(e){return!1}}function tz(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return tG(e,Error)}}function tX(e,t){return Object.prototype.toString.call(e)==="[object "+t+"]"}function tV(e){return tX(e,"DOMError")}var tY=/\(error: (.*)\)/;function tK(e,t,r,i){var n={platform:"web:javascript",filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return I(r)||(n.lineno=r),I(i)||(n.colno=i),n}var tJ,tZ,tQ,t0=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,t1=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,t2=/\((\S*)(?::(\d+))(?::(\d+))\)/,t3=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,t4=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,t6=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var i=t.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return function(e,t){void 0===t&&(t=0);for(var r=[],n=e.split("\n"),s=t;s<n.length;s++){var a=n[s];if(!(a.length>1024)){var o=tY.test(a)?a.replace(tY,"$1"):a;if(!o.match(/\S*Error: /)){for(var l of i){var u=l(o);if(u){r.push(u);break}}if(r.length>=50)break}}}if(!r.length)return[];var c=Array.from(r);return c.reverse(),c.slice(0,50).map(e=>{var t;return W({},e,{filename:e.filename||((t=c)[t.length-1]||{}).filename,function:e.function||"?"})})}}([30,e=>{var t=t0.exec(e);if(t){var[,r,i,n]=t;return tK(r,"?",+i,+n)}var s=t1.exec(e);if(s){if(s[2]&&0===s[2].indexOf("eval")){var a=t2.exec(s[2]);a&&(s[2]=a[1],s[3]=a[2],s[4]=a[3])}var[o,l]=t5(s[1]||"?",s[2]);return tK(l,o,s[3]?+s[3]:void 0,s[4]?+s[4]:void 0)}}],[50,e=>{var t=t3.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){var r=t4.exec(t[3]);r&&(t[1]=t[1]||"eval",t[3]=r[1],t[4]=r[2],t[5]="")}var i=t[3],n=t[1]||"?";return[n,i]=t5(n,i),tK(i,n,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),t5=(e,t)=>{var r=-1!==e.indexOf("safari-extension"),i=-1!==e.indexOf("safari-web-extension");return r||i?[-1!==e.indexOf("@")?e.split("@")[0]:"?",r?"safari-extension:"+t:"safari-web-extension:"+t]:[e,t]},t8=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function t9(e,t){void 0===t&&(t=0);var r=e.stacktrace||e.stack||"",i=e&&t7.test(e.message)?1:0;try{var n,s,a=(n=t6(r,i),s=function(e){var t=globalThis._posthogChunkIds;if(!t)return{};var r=Object.keys(t);return tQ&&r.length===tZ||(tZ=r.length,tQ=r.reduce((r,i)=>{tJ||(tJ={});var n=tJ[i];if(n)r[n[0]]=n[1];else for(var s=e(i),a=s.length-1;a>=0;a--){var o=s[a],l=null==o?void 0:o.filename,u=t[i];if(l&&u){r[l]=u,tJ[i]=[l,u];break}}return r},{})),tQ}(t6),n.forEach(e=>{e.filename&&(e.chunk_id=s[e.filename])}),n);return a.slice(0,a.length-t)}catch(e){}return[]}var t7=/Minified React error #\d+;/i;function re(e,t){return{$exception_list:function e(t,r){var i,n,s,a,o,l,u,c,d=(i=t,n=r,l=t9(i),u=null==(a=null==n?void 0:n.handled)||a,c=null!=(o=null==n?void 0:n.synthetic)&&o,{type:null!=n&&n.overrideExceptionType?n.overrideExceptionType:i.name,value:(s=i.message).error&&"string"==typeof s.error.message?String(s.error.message):String(s),stacktrace:{frames:l,type:"raw"},mechanism:{handled:u,synthetic:c}});return t.cause&&tz(t.cause)&&t.cause!==t?[d,...e(t.cause,{handled:null==r?void 0:r.handled,synthetic:null==r?void 0:r.synthetic})]:[d]}(e,t),$exception_level:"error"}}function rt(e,t){var r,i,n,s=null==(r=null==t?void 0:t.handled)||r,a=null==(i=null==t?void 0:t.synthetic)||i,o={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:null!=(n=null==t?void 0:t.defaultExceptionType)?n:"Error",value:e||(null==t?void 0:t.defaultExceptionMessage),mechanism:{handled:s,synthetic:a}};if(null!=t&&t.syntheticException){var l=t9(t.syntheticException,1);l.length&&(o.stacktrace={frames:l,type:"raw"})}return{$exception_list:[o],$exception_level:"error"}}function rr(e,t,r){try{if(!(t in e))return()=>{};var i=e[t],n=r(i);return O(n)&&(n.prototype=n.prototype||{},Object.defineProperties(n,{__posthog_wrapped__:{enumerable:!1,value:!0}})),e[t]=n,()=>{e[t]=i}}catch(e){return()=>{}}}class ri{constructor(e){var r;this._instance=e,this.ft=(null==t||null==(r=t.location)?void 0:r.pathname)||""}get isEnabled(){return"history_change"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(U.info("History API monitoring enabled, starting..."),this.monitorHistoryChanges())}stop(){this._t&&this._t(),this._t=void 0,U.info("History API monitoring stopped")}monitorHistoryChanges(){var e,r;if(t&&t.history){var i=this;null!=(e=t.history.pushState)&&e.__posthog_wrapped__||rr(t.history,"pushState",e=>function(t,r,n){e.call(this,t,r,n),i.gt("pushState")}),null!=(r=t.history.replaceState)&&r.__posthog_wrapped__||rr(t.history,"replaceState",e=>function(t,r,n){e.call(this,t,r,n),i.gt("replaceState")}),this.bt()}}gt(e){try{var r,i=null==t||null==(r=t.location)?void 0:r.pathname;if(!i)return;i!==this.ft&&this.isEnabled&&this._instance.capture("$pageview",{navigation_type:e}),this.ft=i}catch(t){U.error("Error capturing "+e+" pageview",t)}}bt(){if(!this._t){var e=()=>{this.gt("popstate")};ei(t,"popstate",e),this._t=()=>{t&&t.removeEventListener("popstate",e)}}}}function rn(e){var t,r;return(null==(t=JSON.stringify(e,(r=[],function(e,t){if(T(t)){for(;r.length>0&&r[r.length-1]!==this;)r.pop();return r.includes(t)?"[Circular]":(r.push(t),t)}return t})))?void 0:t.length)||0}var rs=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(rs||{}),ra=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(ra||{}),ro="[SessionRecording]",rl="redacted",ru={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:e=>e,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},rc=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],rd=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],rh=["/s/","/e/","/i/"];function rf(e,t,r,i){if(M(e))return e;var n=(null==t?void 0:t["content-length"])||new Blob([e]).size;return A(n)&&(n=parseInt(n)),n>r?ro+" "+i+" body too large to record ("+n+" bytes)":e}function rp(e,t){if(M(e))return e;var r=e;return tr(r,!1)||(r=ro+" "+t+" body "+rl),V(rd,e=>{var i,n;null!=(i=r)&&i.length&&-1!==(null==(n=r)?void 0:n.indexOf(e))&&(r=ro+" "+t+" body "+rl+" as might contain: "+e)}),r}var r_=(e,t)=>{var r,i,n={payloadSizeLimitBytes:ru.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...ru.performanceEntryTypeToObserve],payloadHostDenyList:[...t.payloadHostDenyList||[],...ru.payloadHostDenyList]},s=!1!==e.session_recording.recordHeaders&&t.recordHeaders,a=!1!==e.session_recording.recordBody&&t.recordBody,o=!1!==e.capture_performance&&t.recordPerformance,l=(i=Math.min(1e6,null!=(r=n.payloadSizeLimitBytes)?r:1e6),e=>(null!=e&&e.requestBody&&(e.requestBody=rf(e.requestBody,e.requestHeaders,i,"Request")),null!=e&&e.responseBody&&(e.responseBody=rf(e.responseBody,e.responseHeaders,i,"Response")),e)),u=t=>{var r;return l(((e,t)=>{var r,i=to(e.name),n=0===t.indexOf("http")?null==(r=to(t))?void 0:r.pathname:t;"/"===n&&(n="");var s=null==i?void 0:i.pathname.replace(n||"","");if(!(i&&s&&rh.some(e=>0===s.indexOf(e))))return e})((M(r=t.requestHeaders)||V(Object.keys(null!=r?r:{}),e=>{rc.includes(e.toLowerCase())&&(r[e]=rl)}),t),e.api_host))},c=O(e.session_recording.maskNetworkRequestFn);return c&&O(e.session_recording.maskCapturedNetworkRequestFn)&&U.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),c&&(e.session_recording.maskCapturedNetworkRequestFn=t=>{var r=e.session_recording.maskNetworkRequestFn({url:t.name});return W({},t,{name:null==r?void 0:r.url})}),n.maskRequestFn=O(e.session_recording.maskCapturedNetworkRequestFn)?t=>{var r,i=u(t);return i&&null!=(r=null==e.session_recording.maskCapturedNetworkRequestFn?void 0:e.session_recording.maskCapturedNetworkRequestFn(i))?r:void 0}:e=>(function(e){if(!I(e))return e.requestBody=rp(e.requestBody,"Request"),e.responseBody=rp(e.responseBody,"Response"),e})(u(e)),W({},ru,n,{recordHeaders:s,recordBody:a,recordPerformance:o,recordInitialRequests:o})};class rg{constructor(e,t){var r,i;void 0===t&&(t={}),this.yt={},this.wt=e=>{if(!this.yt[e]){this.yt[e]=!0;var t,r,i=this.St(e);null==(t=(r=this.rt).onBlockedNode)||t.call(r,e,i)}},this.$t=e=>{var t=this.St(e);if("svg"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var r=t.closest("svg");if(r)return[this._rrweb.mirror.getId(r),r]}return[e,t]},this.St=e=>this._rrweb.mirror.getNode(e),this.xt=e=>{var t,r,i,n,s,a,o,l;return(null!=(t=null==(r=e.removes)?void 0:r.length)?t:0)+(null!=(i=null==(n=e.attributes)?void 0:n.length)?i:0)+(null!=(s=null==(a=e.texts)?void 0:a.length)?s:0)+(null!=(o=null==(l=e.adds)?void 0:l.length)?o:0)},this.throttleMutations=e=>{if(3!==e.type||0!==e.data.source)return e;var t=e.data,r=this.xt(t);t.attributes&&(t.attributes=t.attributes.filter(e=>{var[t]=this.$t(e.id);return!this.ct.consumeRateLimit(t)&&e}));var i=this.xt(t);return 0!==i||r===i?e:void 0},this._rrweb=e,this.rt=t,this.ct=new tB({bucketSize:null!=(r=this.rt.bucketSize)?r:100,refillRate:null!=(i=this.rt.refillRate)?i:10,refillInterval:1e3,et:this.wt})}}var rv=Uint8Array,rm=Uint16Array,ry=Uint32Array,rb=new rv([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),rE=new rv([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),rP=new rv([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),rS=function(e,t){for(var r=new rm(31),i=0;i<31;++i)r[i]=t+=1<<e[i-1];var n=new ry(r[30]);for(i=1;i<30;++i)for(var s=r[i];s<r[i+1];++s)n[s]=s-r[i]<<5|i;return[r,n]},rw=rS(rb,2),rR=rw[0],rx=rw[1];rR[28]=258,rx[258]=28;for(var rO=rS(rE,0)[1],rT=new rm(32768),rC=0;rC<32768;++rC){var rI=(43690&rC)>>>1|(21845&rC)<<1;rI=(61680&(rI=(52428&rI)>>>2|(13107&rI)<<2))>>>4|(3855&rI)<<4,rT[rC]=((65280&rI)>>>8|(255&rI)<<8)>>>1}var rA=function(e,t,r){for(var i=e.length,n=0,s=new rm(t);n<i;++n)++s[e[n]-1];var a,o=new rm(t);for(n=0;n<t;++n)o[n]=o[n-1]+s[n-1]<<1;if(r){a=new rm(1<<t);var l=15-t;for(n=0;n<i;++n)if(e[n])for(var u=n<<4|e[n],c=t-e[n],d=o[e[n]-1]++<<c,h=d|(1<<c)-1;d<=h;++d)a[rT[d]>>>l]=u}else for(a=new rm(i),n=0;n<i;++n)a[n]=rT[o[e[n]-1]++]>>>15-e[n];return a},rk=new rv(288);for(rC=0;rC<144;++rC)rk[rC]=8;for(rC=144;rC<256;++rC)rk[rC]=9;for(rC=256;rC<280;++rC)rk[rC]=7;for(rC=280;rC<288;++rC)rk[rC]=8;var rj=new rv(32);for(rC=0;rC<32;++rC)rj[rC]=5;var rM=rA(rk,9,0),rN=rA(rj,5,0),rL=function(e){return(e/8|0)+(7&e&&1)},rF=function(e,t,r){(null==r||r>e.length)&&(r=e.length);var i=new(e instanceof rm?rm:e instanceof ry?ry:rv)(r-t);return i.set(e.subarray(t,r)),i},rD=function(e,t,r){r<<=7&t;var i=t/8|0;e[i]|=r,e[i+1]|=r>>>8},r$=function(e,t,r){r<<=7&t;var i=t/8|0;e[i]|=r,e[i+1]|=r>>>8,e[i+2]|=r>>>16},rU=function(e,t){for(var r=[],i=0;i<e.length;++i)e[i]&&r.push({s:i,f:e[i]});var n=r.length,s=r.slice();if(!n)return[new rv(0),0];if(1==n){var a=new rv(r[0].s+1);return a[r[0].s]=1,[a,1]}r.sort(function(e,t){return e.f-t.f}),r.push({s:-1,f:25001});var o=r[0],l=r[1],u=0,c=1,d=2;for(r[0]={s:-1,f:o.f+l.f,l:o,r:l};c!=n-1;)o=r[r[u].f<r[d].f?u++:d++],l=r[u!=c&&r[u].f<r[d].f?u++:d++],r[c++]={s:-1,f:o.f+l.f,l:o,r:l};var h=s[0].s;for(i=1;i<n;++i)s[i].s>h&&(h=s[i].s);var f=new rm(h+1),p=rB(r[c-1],f,0);if(p>t){i=0;var _=0,g=p-t,v=1<<g;for(s.sort(function(e,t){return f[t.s]-f[e.s]||e.f-t.f});i<n;++i){var m=s[i].s;if(!(f[m]>t))break;_+=v-(1<<p-f[m]),f[m]=t}for(_>>>=g;_>0;){var y=s[i].s;f[y]<t?_-=1<<t-f[y]++-1:++i}for(;i>=0&&_;--i){var b=s[i].s;f[b]==t&&(--f[b],++_)}p=t}return[new rv(f),p]},rB=function(e,t,r){return -1==e.s?Math.max(rB(e.l,t,r+1),rB(e.r,t,r+1)):t[e.s]=r},rH=function(e){for(var t=e.length;t&&!e[--t];);for(var r=new rm(++t),i=0,n=e[0],s=1,a=function(e){r[i++]=e},o=1;o<=t;++o)if(e[o]==n&&o!=t)++s;else{if(!n&&s>2){for(;s>138;s-=138)a(32754);s>2&&(a(s>10?s-11<<5|28690:s-3<<5|12305),s=0)}else if(s>3){for(a(n),--s;s>6;s-=6)a(8304);s>2&&(a(s-3<<5|8208),s=0)}for(;s--;)a(n);s=1,n=e[o]}return[r.subarray(0,i),t]},rq=function(e,t){for(var r=0,i=0;i<t.length;++i)r+=e[i]*t[i];return r},rW=function(e,t,r){var i=r.length,n=rL(t+2);e[n]=255&i,e[n+1]=i>>>8,e[n+2]=255^e[n],e[n+3]=255^e[n+1];for(var s=0;s<i;++s)e[n+s+4]=r[s];return 8*(n+4+i)},rG=function(e,t,r,i,n,s,a,o,l,u,c){rD(t,c++,r),++n[256];for(var d=rU(n,15),h=d[0],f=d[1],p=rU(s,15),_=p[0],g=p[1],v=rH(h),m=v[0],y=v[1],b=rH(_),E=b[0],P=b[1],S=new rm(19),w=0;w<m.length;++w)S[31&m[w]]++;for(w=0;w<E.length;++w)S[31&E[w]]++;for(var R=rU(S,7),x=R[0],O=R[1],T=19;T>4&&!x[rP[T-1]];--T);var C,I,A,k,j=u+5<<3,M=rq(n,rk)+rq(s,rj)+a,N=rq(n,h)+rq(s,_)+a+14+3*T+rq(S,x)+(2*S[16]+3*S[17]+7*S[18]);if(j<=M&&j<=N)return rW(t,c,e.subarray(l,l+u));if(rD(t,c,1+(N<M)),c+=2,N<M){C=rA(h,f,0),I=h,A=rA(_,g,0),k=_;var L=rA(x,O,0);for(rD(t,c,y-257),rD(t,c+5,P-1),rD(t,c+10,T-4),c+=14,w=0;w<T;++w)rD(t,c+3*w,x[rP[w]]);c+=3*T;for(var F=[m,E],D=0;D<2;++D){var $=F[D];for(w=0;w<$.length;++w){var U=31&$[w];rD(t,c,L[U]),c+=x[U],U>15&&(rD(t,c,$[w]>>>5&127),c+=$[w]>>>12)}}}else C=rM,I=rk,A=rN,k=rj;for(w=0;w<o;++w)if(i[w]>255){r$(t,c,C[(U=i[w]>>>18&31)+257]),c+=I[U+257],U>7&&(rD(t,c,i[w]>>>23&31),c+=rb[U]);var B=31&i[w];r$(t,c,A[B]),c+=k[B],B>3&&(r$(t,c,i[w]>>>5&8191),c+=rE[B])}else r$(t,c,C[i[w]]),c+=I[i[w]];return r$(t,c,C[256]),c+I[256]},rz=new ry([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),rX=function(){for(var e=new ry(256),t=0;t<256;++t){for(var r=t,i=9;--i;)r=(1&r&&0xedb88320)^r>>>1;e[t]=r}return e}(),rV=function(){var e=0xffffffff;return{p:function(t){for(var r=e,i=0;i<t.length;++i)r=rX[255&r^t[i]]^r>>>8;e=r},d:function(){return 0xffffffff^e}}},rY=function(e,t,r){for(;r;++t)e[t]=r,r>>>=8},rK=function(e,t){var r=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:2*(9==t.level),e[9]=3,0!=t.mtime&&rY(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),r){e[3]=8;for(var i=0;i<=r.length;++i)e[i+10]=r.charCodeAt(i)}};function rJ(e,t){void 0===t&&(t={});var r,i,n,s=rV(),a=e.length;s.p(e);var o=(i=t,n=10+((r=t).filename&&r.filename.length+1||0),function(e,t,r,i,n,s){var a=e.length,o=new rv(i+a+5*(1+Math.floor(a/7e3))+8),l=o.subarray(i,o.length-n),u=0;if(!t||a<8)for(var c=0;c<=a;c+=65535){var d=c+65535;d<a?u=rW(l,u,e.subarray(c,d)):(l[c]=s,u=rW(l,u,e.subarray(c,a)))}else{for(var h=rz[t-1],f=h>>>13,p=8191&h,_=(1<<r)-1,g=new rm(32768),v=new rm(_+1),m=Math.ceil(r/3),y=2*m,b=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<y)&_},E=new ry(25e3),P=new rm(288),S=new rm(32),w=0,R=0,x=(c=0,0),O=0,T=0;c<a;++c){var C=b(c),I=32767&c,A=v[C];if(g[I]=A,v[C]=I,O<=c){var k=a-c;if((w>7e3||x>24576)&&k>423){u=rG(e,l,0,E,P,S,R,x,T,c-T,u),x=w=R=0,T=c;for(var j=0;j<286;++j)P[j]=0;for(j=0;j<30;++j)S[j]=0}var M=2,N=0,L=p,F=I-A&32767;if(k>2&&C==b(c-F))for(var D=Math.min(f,k)-1,$=Math.min(32767,c),U=Math.min(258,k);F<=$&&--L&&I!=A;){if(e[c+M]==e[c+M-F]){for(var B=0;B<U&&e[c+B]==e[c+B-F];++B);if(B>M){if(M=B,N=F,B>D)break;var H=Math.min(F,B-2),q=0;for(j=0;j<H;++j){var W=c-F+j+32768&32767,G=W-g[W]+32768&32767;G>q&&(q=G,A=W)}}}F+=(I=A)-(A=g[I])+32768&32767}if(N){E[x++]=0x10000000|rx[M]<<18|rO[N];var z=31&rx[M],X=31&rO[N];R+=rb[z]+rE[X],++P[257+z],++S[X],O=c+M,++w}else E[x++]=e[c],++P[e[c]]}}u=rG(e,l,s,E,P,S,R,x,T,c-T,u)}return rF(o,0,i+rL(u)+n)}(e,null==i.level?6:i.level,null==i.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+i.mem,n,8,!0)),l=o.length;return rK(o,t),rY(o,l-8,s.d()),rY(o,l-4,a),o}function rZ(e,t){var r=e.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var i=new rv(e.length+(e.length>>>1)),n=0,s=function(e){i[n++]=e},a=0;a<r;++a){if(n+5>i.length){var o=new rv(n+8+(r-a<<1));o.set(i),i=o}var l=e.charCodeAt(a);l<128||t?s(l):(l<2048?s(192|l>>>6):(l>55295&&l<57344?(s(240|(l=65536+(1047552&l)|1023&e.charCodeAt(++a))>>>18),s(128|l>>>12&63)):s(224|l>>>12),s(128|l>>>6&63)),s(128|63&l))}return rF(i,0,n)}var rQ="disabled",r0="sampled",r1="active",r2="buffering",r3="paused",r4="trigger",r6=r4+"_activated",r5=r4+"_pending",r8=r4+"_"+rQ;function r9(e,t){return t.some(t=>"regex"===t.matching&&new RegExp(t.url).test(e))}class r7{constructor(e){this.kt=e}triggerStatus(e){var t=this.kt.map(t=>t.triggerStatus(e));return t.includes(r6)?r6:t.includes(r5)?r5:r8}stop(){this.kt.forEach(e=>e.stop())}}class ie{constructor(e){this.kt=e}triggerStatus(e){var t=new Set;for(var r of this.kt)t.add(r.triggerStatus(e));switch(t.delete(r8),t.size){case 0:return r8;case 1:return Array.from(t)[0];default:return r5}}stop(){this.kt.forEach(e=>e.stop())}}class it{triggerStatus(){return r5}stop(){}}class ir{constructor(e){this.Et=[],this.It=[],this.urlBlocked=!1,this._instance=e}onRemoteConfig(e){var t,r;this.Et=(null==(t=e.sessionRecording)?void 0:t.urlTriggers)||[],this.It=(null==(r=e.sessionRecording)?void 0:r.urlBlocklist)||[]}Pt(e){var t;return 0===this.Et.length?r8:(null==(t=this._instance)?void 0:t.get_property(eR))===e?r6:r5}triggerStatus(e){var t=this.Pt(e),r=t===r6?r6:t===r5?r5:r8;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:r}),r}checkUrlTriggerConditions(e,r,i){if(void 0!==t&&t.location.href){var n=t.location.href,s=this.urlBlocked,a=r9(n,this.It);s&&a||(a&&!s?e():!a&&s&&r(),r9(n,this.Et)&&i("url"))}}stop(){}}class ii{constructor(e){this.linkedFlag=null,this.linkedFlagSeen=!1,this.Rt=()=>{},this._instance=e}triggerStatus(){var e=r5;return M(this.linkedFlag)&&(e=r8),this.linkedFlagSeen&&(e=r6),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:e}),e}onRemoteConfig(e,t){var r;if(this.linkedFlag=(null==(r=e.sessionRecording)?void 0:r.linkedFlag)||null,!M(this.linkedFlag)&&!this.linkedFlagSeen){var i=A(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,n=A(this.linkedFlag)?null:this.linkedFlag.variant;this.Rt=this._instance.onFeatureFlags((e,r)=>{var s=!1;if(T(r)&&i in r){var a=r[i];s=L(a)?!0===a:n?a===n:!!a}this.linkedFlagSeen=s,s&&t(i,n)})}}stop(){this.Rt()}}class is{constructor(e){this.Tt=[],this._instance=e}onRemoteConfig(e){var t;this.Tt=(null==(t=e.sessionRecording)?void 0:t.eventTriggers)||[]}Mt(e){var t;return 0===this.Tt.length?r8:(null==(t=this._instance)?void 0:t.get_property(ex))===e?r6:r5}triggerStatus(e){var t=this.Mt(e),r=t===r6?r6:t===r5?r5:r8;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:r}),r}stop(){}}function ia(e){return e.isRecordingEnabled?r2:rQ}function io(e){if(!e.receivedFlags)return r2;if(!e.isRecordingEnabled)return rQ;if(e.urlTriggerMatching.urlBlocked)return r3;var t=!0===e.isSampled,r=new r7([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId);return t?r0:r===r6?r1:r===r5?r2:!1===e.isSampled?rQ:r1}function il(e){if(!e.receivedFlags)return r2;if(!e.isRecordingEnabled)return rQ;if(e.urlTriggerMatching.urlBlocked)return r3;var t=new ie([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId),r=t!==r8,i=L(e.isSampled);return r&&t===r5?r2:r&&t===r8||i&&!e.isSampled?rQ:!0===e.isSampled?r0:r1}var iu="[SessionRecording]",ic=B(iu);function id(){var e;return null==f||null==(e=f.__PosthogExtensions__)||null==(e=e.rrweb)?void 0:e.record}var ih=[ra.MouseMove,ra.MouseInteraction,ra.Scroll,ra.ViewportResize,ra.Input,ra.TouchMove,ra.MediaInteraction,ra.Drag],ip=e=>({rrwebMethod:e,enqueuedAt:Date.now(),attempt:1});function i_(e){return function(e,t){for(var r="",i=0;i<e.length;){var n=e[i++];n<128||t?r+=String.fromCharCode(n):n<224?r+=String.fromCharCode((31&n)<<6|63&e[i++]):n<240?r+=String.fromCharCode((15&n)<<12|(63&e[i++])<<6|63&e[i++]):r+=String.fromCharCode(55296|(n=((15&n)<<18|(63&e[i++])<<12|(63&e[i++])<<6|63&e[i++])-65536)>>10,56320|1023&n)}return r}(rJ(rZ(JSON.stringify(e))),!0)}function ig(e){return e.type===rs.Custom&&"sessionIdle"===e.data.tag}class iv{get sessionId(){return this.Ct}get Ft(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.Ot}get At(){if(!this._instance.sessionManager)throw Error(iu+" must be started with a valid sessionManager.");return this._instance.sessionManager}get Dt(){var e,t;return this.Lt.triggerStatus(this.sessionId)===r5?6e4:null!=(e=null==(t=this._instance.config.session_recording)?void 0:t.full_snapshot_interval_millis)?e:3e5}get jt(){var e=this._instance.get_property(ew);return L(e)?e:null}get Nt(){var e,t,r=null==(e=this.C)?void 0:e.data[(null==(t=this.C)?void 0:t.data.length)-1],{sessionStartTimestamp:i}=this.At.checkAndGetSessionAndWindowId(!0);return r?r.timestamp-i:null}get zt(){var e=!!this._instance.get_property(e_),r=!this._instance.config.disable_session_recording;return t&&e&&r}get Ut(){var e=!!this._instance.get_property(eg),t=this._instance.config.enable_recording_console_log;return null!=t?t:e}get qt(){var e,t,r,i,n,s,a=this._instance.config.session_recording.captureCanvas,o=this._instance.get_property(ey),l=null!=(e=null!=(t=null==a?void 0:a.recordCanvas)?t:null==o?void 0:o.enabled)&&e,u=null!=(r=null!=(i=null==a?void 0:a.canvasFps)?i:null==o?void 0:o.fps)?r:4,c=null!=(n=null!=(s=null==a?void 0:a.canvasQuality)?s:null==o?void 0:o.quality)?n:.4;if("string"==typeof c){var d=parseFloat(c);c=isNaN(d)?.4:d}return{enabled:l,fps:tU(u,0,12,"canvas recording fps",4),quality:tU(c,0,1,"canvas recording quality",.4)}}get Bt(){var e,t,r=this._instance.get_property(ev),i={recordHeaders:null==(e=this._instance.config.session_recording)?void 0:e.recordHeaders,recordBody:null==(t=this._instance.config.session_recording)?void 0:t.recordBody},n=(null==i?void 0:i.recordHeaders)||(null==r?void 0:r.recordHeaders),s=(null==i?void 0:i.recordBody)||(null==r?void 0:r.recordBody),a=T(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,o=!!(L(a)?a:null==r?void 0:r.capturePerformance);return n||s||o?{recordHeaders:n,recordBody:s,recordPerformance:o}:void 0}get Ht(){var e,t,r,i,n,s,a=this._instance.get_property(em),o={maskAllInputs:null==(e=this._instance.config.session_recording)?void 0:e.maskAllInputs,maskTextSelector:null==(t=this._instance.config.session_recording)?void 0:t.maskTextSelector,blockSelector:null==(r=this._instance.config.session_recording)?void 0:r.blockSelector},l=null!=(i=null==o?void 0:o.maskAllInputs)?i:null==a?void 0:a.maskAllInputs,u=null!=(n=null==o?void 0:o.maskTextSelector)?n:null==a?void 0:a.maskTextSelector,c=null!=(s=null==o?void 0:o.blockSelector)?s:null==a?void 0:a.blockSelector;return I(l)&&I(u)&&I(c)?void 0:{maskAllInputs:null==l||l,maskTextSelector:u,blockSelector:c}}get Wt(){var e=this._instance.get_property(eb);return N(e)?e:null}get Gt(){var e=this._instance.get_property(eE);return N(e)?e:null}get status(){return this.Jt?this.Vt({receivedFlags:this.Jt,isRecordingEnabled:this.zt,isSampled:this.jt,urlTriggerMatching:this.Kt,eventTriggerMatching:this.Yt,linkedFlagMatching:this.Xt,sessionId:this.sessionId}):r2}constructor(e){if(this.Vt=ia,this.Jt=!1,this.Qt=[],this.Zt="unknown",this.ti=Date.now(),this.Lt=new it,this.ii=void 0,this.ei=void 0,this.ri=void 0,this.si=void 0,this.ni=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.oi=()=>{this.ai()},this.li=()=>{this.ui("browser offline",{})},this.hi=()=>{this.ui("browser online",{})},this.di=()=>{if(null!=o&&o.visibilityState){var e="window "+o.visibilityState;this.ui(e,{})}},this._instance=e,this.Ot=!1,this.vi="/s/",this.ci=void 0,this.Jt=!1,!this._instance.sessionManager)throw ic.error("started without valid sessionManager"),Error(iu+" started without valid sessionManager. This is a bug.");if(this._instance.config.__preview_experimental_cookieless_mode)throw Error(iu+" cannot be used with __preview_experimental_cookieless_mode.");this.Xt=new ii(this._instance),this.Kt=new ir(this._instance),this.Yt=new is(this._instance);var{sessionId:t,windowId:r}=this.At.checkAndGetSessionAndWindowId();this.Ct=t,this.fi=r,this.C=this.pi(),this.Ft>=this.At.sessionTimeoutMs&&ic.warn("session_idle_threshold_ms ("+this.Ft+") is greater than the session timeout ("+this.At.sessionTimeoutMs+"). Session will never be detected as idle")}startIfEnabledOrStop(e){this.zt?(this.gi(e),ei(t,"beforeunload",this.oi),ei(t,"offline",this.li),ei(t,"online",this.hi),ei(t,"visibilitychange",this.di),this.mi(),this.bi(),M(this.ii)&&(this.ii=this._instance.on("eventCaptured",e=>{try{if("$pageview"===e.event){var t=null!=e&&e.properties.$current_url?this.yi(null==e?void 0:e.properties.$current_url):"";if(!t)return;this.ui("$pageview",{href:t})}}catch(e){ic.error("Could not add $pageview to rrweb session",e)}})),this.ei||(this.ei=this.At.onSessionId((e,t,r)=>{var i,n;r&&(this.ui("$session_id_change",{sessionId:e,windowId:t,changeReason:r}),null==(i=this._instance)||null==(i=i.persistence)||i.unregister(ex),null==(n=this._instance)||null==(n=n.persistence)||n.unregister(eR))}))):this.stopRecording()}stopRecording(){var e,r,i,n;this.Ot&&this.ci&&(this.ci(),this.ci=void 0,this.Ot=!1,null==t||t.removeEventListener("beforeunload",this.oi),null==t||t.removeEventListener("offline",this.li),null==t||t.removeEventListener("online",this.hi),null==t||t.removeEventListener("visibilitychange",this.di),this.pi(),clearInterval(this.wi),null==(e=this.ii)||e.call(this),this.ii=void 0,null==(r=this.ni)||r.call(this),this.ni=void 0,null==(i=this.ei)||i.call(this),this.ei=void 0,null==(n=this.si)||n.call(this),this.si=void 0,this.Yt.stop(),this.Kt.stop(),this.Xt.stop(),ic.info("stopped"))}Si(){var e;null==(e=this._instance.persistence)||e.unregister(ew)}$i(e){var t,r=this.Ct!==e,i=this.Wt;if(N(i)){var n=this.jt,s=r||!L(n),a=s?function(e){for(var t=0,r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r)|0;return Math.abs(t)}(e)%100<tU(100*i,0,100):n;s&&(a?this.xi(r0):ic.warn("Sample rate ("+i+") has determined that this sessionId ("+e+") will not be sent to the server."),this.ui("samplingDecisionMade",{sampleRate:i,isSampled:a})),null==(t=this._instance.persistence)||t.register({[ew]:a})}else this.Si()}onRemoteConfig(e){var t,r,i,n;this.ui("$remote_config_received",e),this.ki(e),null!=(t=e.sessionRecording)&&t.endpoint&&(this.vi=null==(n=e.sessionRecording)?void 0:n.endpoint),this.mi(),"any"===(null==(r=e.sessionRecording)?void 0:r.triggerMatchType)?(this.Vt=io,this.Lt=new r7([this.Yt,this.Kt])):(this.Vt=il,this.Lt=new ie([this.Yt,this.Kt])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(i=e.sessionRecording)?void 0:i.triggerMatchType}),this.Kt.onRemoteConfig(e),this.Yt.onRemoteConfig(e),this.Xt.onRemoteConfig(e,(e,t)=>{this.xi("linked_flag_matched",{flag:e,variant:t})}),this.Jt=!0,this.startIfEnabledOrStop()}mi(){N(this.Wt)&&M(this.si)&&(this.si=this.At.onSessionId(e=>{this.$i(e)}))}ki(e){if(this._instance.persistence){var t,r=this._instance.persistence,i=()=>{var t,i,n,s,a,o,l,u,c,d=null==(t=e.sessionRecording)?void 0:t.sampleRate,h=M(d)?null:parseFloat(d);M(h)&&this.Si();var f=null==(i=e.sessionRecording)?void 0:i.minimumDurationMilliseconds;r.register({[e_]:!!e.sessionRecording,[eg]:null==(n=e.sessionRecording)?void 0:n.consoleLogRecordingEnabled,[ev]:W({capturePerformance:e.capturePerformance},null==(s=e.sessionRecording)?void 0:s.networkPayloadCapture),[em]:null==(a=e.sessionRecording)?void 0:a.masking,[ey]:{enabled:null==(o=e.sessionRecording)?void 0:o.recordCanvas,fps:null==(l=e.sessionRecording)?void 0:l.canvasFps,quality:null==(u=e.sessionRecording)?void 0:u.canvasQuality},[eb]:h,[eE]:I(f)?null:f,[eP]:null==(c=e.sessionRecording)?void 0:c.scriptConfig})};i(),null==(t=this.ri)||t.call(this),this.ri=this.At.onSessionId(i)}}log(e,t){var r;void 0===t&&(t="log"),null==(r=this._instance.sessionRecording)||r.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:t,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}gi(e){if(!I(Object.assign)&&!I(Array.from)&&!(this.Ot||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var t;(this.Ot=!0,this.At.checkAndGetSessionAndWindowId(),id())?this.Ei():null==(t=f.__PosthogExtensions__)||null==t.loadExternalDependency||t.loadExternalDependency(this._instance,this.Ii,e=>{if(e)return ic.error("could not load recorder",e);this.Ei()}),ic.info("starting"),this.status===r1&&this.xi(e||"recording_initialized")}}get Ii(){var e;return(null==(e=this._instance)||null==(e=e.persistence)||null==(e=e.get_property(eP))?void 0:e.script)||"recorder"}Pi(e){var t;return 3===e.type&&-1!==ih.indexOf(null==(t=e.data)?void 0:t.source)}Ri(e){var t=this.Pi(e);t||this.Zt||e.timestamp-this.ti>this.Ft&&(this.Zt=!0,clearInterval(this.wi),this.ui("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this.ti,threshold:this.Ft,bufferLength:this.C.data.length,bufferSize:this.C.size}),this.ai());var r=!1;if(t&&(this.ti=e.timestamp,this.Zt)){var i="unknown"===this.Zt;this.Zt=!1,i||(this.ui("sessionNoLongerIdle",{reason:"user activity",type:e.type}),r=!0)}if(!this.Zt){var{windowId:n,sessionId:s}=this.At.checkAndGetSessionAndWindowId(!t,e.timestamp),a=this.Ct!==s,o=this.fi!==n;this.fi=n,this.Ct=s,a||o?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):r&&this.Ti()}}Mi(e){try{return e.rrwebMethod(),!0}catch(t){return this.Qt.length<10?this.Qt.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):ic.warn("could not emit queued rrweb event.",t,e),!1}}ui(e,t){return this.Mi(ip(()=>id().addCustomEvent(e,t)))}Ci(){return this.Mi(ip(()=>id().takeFullSnapshot()))}Ei(){var e,t,r,i,n={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1};for(var[s,a]of Object.entries(this._instance.config.session_recording||{}))s in n&&("maskInputOptions"===s?n.maskInputOptions=W({password:!0},a):n[s]=a);this.qt&&this.qt.enabled&&(n.recordCanvas=!0,n.sampling={canvas:this.qt.fps},n.dataURLOptions={type:"image/webp",quality:this.qt.quality}),this.Ht&&(n.maskAllInputs=null==(t=this.Ht.maskAllInputs)||t,n.maskTextSelector=null!=(r=this.Ht.maskTextSelector)?r:void 0,n.blockSelector=null!=(i=this.Ht.blockSelector)?i:void 0);var o=id();if(o){this.Fi=null!=(e=this.Fi)?e:new rg(o,{refillRate:this._instance.config.session_recording.__mutationThrottlerRefillRate,bucketSize:this._instance.config.session_recording.__mutationThrottlerBucketSize,onBlockedNode:(e,t)=>{var r="Too many mutations on node '"+e+"'. Rate limiting. This could be due to SVG animations or something similar";ic.info(r,{node:t}),this.log(iu+" "+r,"warn")}});var l=this.Oi();this.ci=o(W({emit:e=>{this.onRRwebEmit(e)},plugins:l},n)),this.ti=Date.now(),this.Zt=L(this.Zt)?this.Zt:"unknown",this.ui("$session_options",{sessionRecordingOptions:n,activePlugins:l.map(e=>null==e?void 0:e.name)}),this.ui("$posthog_config",{config:this._instance.config})}else ic.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}Ti(){if(this.wi&&clearInterval(this.wi),!0!==this.Zt){var e=this.Dt;e&&(this.wi=setInterval(()=>{this.Ci()},e))}}Oi(){var e,t,r=[],i=null==(e=f.__PosthogExtensions__)||null==(e=e.rrwebPlugins)?void 0:e.getRecordConsolePlugin;i&&this.Ut&&r.push(i());var n=null==(t=f.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordNetworkPlugin;return this.Bt&&O(n)&&(!ta.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?r.push(n(r_(this._instance.config,this.Bt))):ic.info("NetworkCapture not started because we are on localhost.")),r}onRRwebEmit(e){var t;if(this.Ai(),e&&T(e)){if(e.type===rs.Meta){var r=this.yi(e.data.href);if(this.Di=r,!r)return;e.data.href=r}else this.Li();if(this.Kt.checkUrlTriggerConditions(()=>this.ji(),()=>this.Ni(),e=>this.zi(e)),!this.Kt.urlBlocked||e.type===rs.Custom&&"recording paused"===e.data.tag){e.type===rs.FullSnapshot&&this.Ti(),e.type===rs.FullSnapshot&&this.Jt&&this.Lt.triggerStatus(this.sessionId)===r5&&this.pi();var i=this.Fi?this.Fi.throttleMutations(e):e;if(i){var n=function(e){if(e&&T(e)&&6===e.type&&T(e.data)&&"rrweb/console@1"===e.data.plugin){e.data.payload.payload.length>10&&(e.data.payload.payload=e.data.payload.payload.slice(0,10),e.data.payload.payload.push("...[truncated]"));for(var t=[],r=0;r<e.data.payload.payload.length;r++)e.data.payload.payload[r]&&e.data.payload.payload[r].length>2e3?t.push(e.data.payload.payload[r].slice(0,2e3)+"...[truncated]"):t.push(e.data.payload.payload[r]);return e.data.payload.payload=t,e}return e}(i);if(this.Ri(n),!0!==this.Zt||ig(n)){if(ig(n)){var s=n.data.payload;s&&(n.timestamp=s.lastActivityTimestamp+s.threshold)}var a=null==(t=this._instance.config.session_recording.compress_events)||t?function(e){if(1024>rn(e))return e;try{if(e.type===rs.FullSnapshot)return W({},e,{data:i_(e.data),cv:"2024-10"});if(e.type===rs.IncrementalSnapshot&&e.data.source===ra.Mutation)return W({},e,{cv:"2024-10",data:W({},e.data,{texts:i_(e.data.texts),attributes:i_(e.data.attributes),removes:i_(e.data.removes),adds:i_(e.data.adds)})});if(e.type===rs.IncrementalSnapshot&&e.data.source===ra.StyleSheetRule)return W({},e,{cv:"2024-10",data:W({},e.data,{adds:e.data.adds?i_(e.data.adds):void 0,removes:e.data.removes?i_(e.data.removes):void 0})})}catch(e){ic.error("could not compress event - will use uncompressed event",e)}return e}(n):n,o={$snapshot_bytes:rn(a),$snapshot_data:a,$session_id:this.Ct,$window_id:this.fi};this.status!==rQ?this.Ui(o):this.pi()}}}}}Li(){if(!this._instance.config.capture_pageview&&t){var e=this.yi(t.location.href);this.Di!==e&&(this.ui("$url_changed",{href:e}),this.Di=e)}}Ai(){if(this.Qt.length){var e=[...this.Qt];this.Qt=[],e.forEach(e=>{Date.now()-e.enqueuedAt<=2e3&&this.Mi(e)})}}yi(e){var t=this._instance.config.session_recording;if(t.maskNetworkRequestFn){var r,i={url:e};return null==(r=i=t.maskNetworkRequestFn(i))?void 0:r.url}return e}pi(){return this.C={size:0,data:[],sessionId:this.Ct,windowId:this.fi},this.C}ai(){this.qi&&(clearTimeout(this.qi),this.qi=void 0);var e=this.Gt,t=this.Nt,r=N(t)&&t>=0,i=N(e)&&r&&t<e;return this.status===r2||this.status===r3||this.status===rQ||i?(this.qi=setTimeout(()=>{this.ai()},2e3),this.C):(this.C.data.length>0&&(function e(t,r){if(void 0===r&&(r=6606028.8),t.size>=r&&t.data.length>1){var i=Math.floor(t.data.length/2),n=t.data.slice(0,i),s=t.data.slice(i);return[e({size:rn(n),data:n,sessionId:t.sessionId,windowId:t.windowId}),e({size:rn(s),data:s,sessionId:t.sessionId,windowId:t.windowId})].flatMap(e=>e)}return[t]})(this.C).forEach(e=>{this.Bi({$snapshot_bytes:e.size,$snapshot_data:e.data,$session_id:e.sessionId,$window_id:e.windowId,$lib:"web",$lib_version:p.LIB_VERSION})}),this.pi())}Ui(e){var t,r=2+((null==(t=this.C)?void 0:t.data.length)||0);!this.Zt&&(this.C.size+e.$snapshot_bytes+r>943718.4||this.C.sessionId!==this.Ct)&&(this.C=this.ai()),this.C.size+=e.$snapshot_bytes,this.C.data.push(e.$snapshot_data),this.qi||this.Zt||(this.qi=setTimeout(()=>{this.ai()},2e3))}Bi(e){this._instance.capture("$snapshot",e,{_url:this._instance.requestRouter.endpointFor("api",this.vi),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}zi(e){var t;this.Lt.triggerStatus(this.sessionId)===r5&&(null==(t=this._instance)||null==(t=t.persistence)||t.register({["url"===e?eR:ex]:this.Ct}),this.ai(),this.xi(e+"_trigger_matched"))}ji(){this.Kt.urlBlocked||(this.Kt.urlBlocked=!0,clearInterval(this.wi),ic.info("recording paused due to URL blocker"),this.ui("recording paused",{reason:"url blocker"}))}Ni(){this.Kt.urlBlocked&&(this.Kt.urlBlocked=!1,this.Ci(),this.Ti(),this.ui("recording resumed",{reason:"left blocked url"}),ic.info("recording resumed"))}bi(){0!==this.Yt.Tt.length&&M(this.ni)&&(this.ni=this._instance.on("eventCaptured",e=>{try{this.Yt.Tt.includes(e.event)&&this.zi("event")}catch(e){ic.error("Could not activate event trigger",e)}}))}overrideLinkedFlag(){this.Xt.linkedFlagSeen=!0,this.Ci(),this.xi("linked_flag_overridden")}overrideSampling(){var e;null==(e=this._instance.persistence)||e.register({[ew]:!0}),this.Ci(),this.xi("sampling_overridden")}overrideTrigger(e){this.zi(e)}xi(e,t){this._instance.register_for_session({$session_recording_start_reason:e}),ic.info(e.replace("_"," "),t),y(["recording_initialized","session_id_changed"],e)||this.ui(e,t)}get sdkDebugProperties(){var{sessionStartTimestamp:e}=this.At.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.C.data.length,$sdk_debug_replay_internal_buffer_size:this.C.size,$sdk_debug_current_session_duration:this.Nt,$sdk_debug_session_start:e}}}var im=B("[SegmentIntegration]"),iy="posthog-js";function ib(e,t){var{organization:r,projectId:i,prefix:n,severityAllowList:s=["error"]}=void 0===t?{}:t;return t=>{if(!("*"===s||s.includes(t.level))||!e.__loaded)return t;t.tags||(t.tags={});var a,o,l,u,c,d=e.requestRouter.endpointFor("ui","/project/"+e.config.token+"/person/"+e.get_distinct_id());t.tags["PostHog Person URL"]=d,e.sessionRecordingStarted()&&(t.tags["PostHog Recording URL"]=e.get_session_replay_url({withTimestamp:!0}));var h=(null==(a=t.exception)?void 0:a.values)||[],f=h.map(e=>W({},e,{stacktrace:e.stacktrace?W({},e.stacktrace,{type:"raw",frames:(e.stacktrace.frames||[]).map(e=>W({},e,{platform:"web:javascript"}))}):void 0})),p={$exception_message:(null==(o=h[0])?void 0:o.value)||t.message,$exception_type:null==(l=h[0])?void 0:l.type,$exception_personURL:d,$exception_level:t.level,$exception_list:f,$sentry_event_id:t.event_id,$sentry_exception:t.exception,$sentry_exception_message:(null==(u=h[0])?void 0:u.value)||t.message,$sentry_exception_type:null==(c=h[0])?void 0:c.type,$sentry_tags:t.tags};return r&&i&&(p.$sentry_url=(n||"https://sentry.io/organizations/")+r+"/issues/?project="+i+"&query="+t.event_id),e.exceptions.sendExceptionEvent(p),t}}class iE{constructor(e,t,r,i,n){this.name=iy,this.setupOnce=function(s){s(ib(e,{organization:t,projectId:r,prefix:i,severityAllowList:n}))}}}var iP=null!=t&&t.location?td(t.location.hash,"__posthog")||td(location.hash,"state"):null,iS="_postHogToolbarParams",iw=B("[Toolbar]"),iR=function(e){return e[e.UNINITIALIZED=0]="UNINITIALIZED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED",e}(iR||{});class ix{constructor(e){this.instance=e}Hi(e){f.ph_toolbar_state=e}Wi(){var e;return null!=(e=f.ph_toolbar_state)?e:iR.UNINITIALIZED}maybeLoadToolbar(e,r,i){if(void 0===e&&(e=void 0),void 0===r&&(r=void 0),void 0===i&&(i=void 0),!t||!o)return!1;e=null!=e?e:t.location,i=null!=i?i:t.history;try{if(!r){try{t.localStorage.setItem("test","test"),t.localStorage.removeItem("test")}catch(e){return!1}r=null==t?void 0:t.localStorage}var n,s=iP||td(e.hash,"__posthog")||td(e.hash,"state"),a=s?Z(()=>JSON.parse(atob(decodeURIComponent(s))))||Z(()=>JSON.parse(decodeURIComponent(s))):null;return a&&"ph_authorize"===a.action?((n=a).source="url",n&&Object.keys(n).length>0&&(a.desiredHash?e.hash=a.desiredHash:i?i.replaceState(i.state,"",e.pathname+e.search):e.hash="")):((n=JSON.parse(r.getItem(iS)||"{}")).source="localstorage",delete n.userIntent),!(!n.token||this.instance.config.token!==n.token)&&(this.loadToolbar(n),!0)}catch(e){return!1}}Gi(e){var t=f.ph_load_toolbar||f.ph_load_editor;!M(t)&&O(t)?t(e,this.instance):iw.warn("No toolbar load function found")}loadToolbar(e){var r,i=!(null==o||!o.getElementById(eH));if(!t||i)return!1;var n="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,s=W({token:this.instance.config.token},e,{apiURL:this.instance.requestRouter.endpointFor("ui")},n?{instrument:!1}:{});return(t.localStorage.setItem(iS,JSON.stringify(W({},s,{source:void 0}))),this.Wi()===iR.LOADED)?this.Gi(s):this.Wi()===iR.UNINITIALIZED&&(this.Hi(iR.LOADING),null==(r=f.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this.instance,"toolbar",e=>{if(e)return iw.error("[Toolbar] Failed to load",e),void this.Hi(iR.UNINITIALIZED);this.Hi(iR.LOADED),this.Gi(s)}),ei(t,"turbolinks:load",()=>{this.Hi(iR.UNINITIALIZED),this.loadToolbar(s)})),!0}Ji(e){return this.loadToolbar(e)}maybeLoadEditor(e,t,r){return void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===r&&(r=void 0),this.maybeLoadToolbar(e,t,r)}}var iO=B("[TracingHeaders]");class iT{constructor(e){this.Vi=void 0,this.Ki=void 0,this.nt=()=>{var e,t;I(this.Vi)&&(null==(e=f.__PosthogExtensions__)||null==(e=e.tracingHeadersPatchFns)||e._patchXHR(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager)),I(this.Ki)&&(null==(t=f.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchFetch(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager))},this._instance=e}J(e){var t,r;null!=(t=f.__PosthogExtensions__)&&t.tracingHeadersPatchFns&&e(),null==(r=f.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"tracing-headers",t=>{if(t)return iO.error("failed to load script",t);e()})}startIfEnabledOrStop(){var e,t;this._instance.config.__add_tracing_headers?this.J(this.nt):(null==(e=this.Vi)||e.call(this),null==(t=this.Ki)||t.call(this),this.Vi=void 0,this.Ki=void 0)}}var iC=B("[Web Vitals]");class iI{constructor(e){var t;this.Yi=!1,this.i=!1,this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Xi=()=>{clearTimeout(this.Qi),0!==this.C.metrics.length&&(this._instance.capture("$web_vitals",this.C.metrics.reduce((e,t)=>W({},e,{["$web_vitals_"+t.name+"_event"]:W({},t),["$web_vitals_"+t.name+"_value"]:t.value}),{})),this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Zi=e=>{var t,r=null==(t=this._instance.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0);if(I(r))iC.error("Could not read session ID. Dropping metrics!");else{this.C=this.C||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var i=this.te();I(i)||(M(null==e?void 0:e.name)||M(null==e?void 0:e.value)?iC.error("Invalid metric received",e):this.ie&&e.value>=this.ie?iC.error("Ignoring metric with value >= "+this.ie,e):(this.C.url!==i&&(this.Xi(),this.Qi=setTimeout(this.Xi,this.flushToCaptureTimeoutMs)),I(this.C.url)&&(this.C.url=i),this.C.firstMetricTimestamp=I(this.C.firstMetricTimestamp)?Date.now():this.C.firstMetricTimestamp,e.attribution&&e.attribution.interactionTargetElement&&(e.attribution.interactionTargetElement=void 0),this.C.metrics.push(W({},e,{$current_url:i,$session_id:r.sessionId,$window_id:r.windowId,timestamp:Date.now()})),this.C.metrics.length===this.allowedMetrics.length&&this.Xi()))}},this.nt=()=>{var e,t,r,i,n=f.__PosthogExtensions__;I(n)||I(n.postHogWebVitalsCallbacks)||({onLCP:e,onCLS:t,onFCP:r,onINP:i}=n.postHogWebVitalsCallbacks),e&&t&&r&&i?(this.allowedMetrics.indexOf("LCP")>-1&&e(this.Zi.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&t(this.Zi.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&r(this.Zi.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&i(this.Zi.bind(this)),this.i=!0):iC.error("web vitals callbacks not loaded - not starting")},this._instance=e,this.Yi=!(null==(t=this._instance.persistence)||!t.props[eh]),this.startIfEnabled()}get allowedMetrics(){var e,t,r=T(this._instance.config.capture_performance)?null==(e=this._instance.config.capture_performance)?void 0:e.web_vitals_allowed_metrics:void 0;return I(r)?(null==(t=this._instance.persistence)?void 0:t.props[ep])||["CLS","FCP","INP","LCP"]:r}get flushToCaptureTimeoutMs(){return(T(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get ie(){var e=T(this._instance.config.capture_performance)&&N(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:9e5;return 0<e&&e<=6e4?9e5:e}get isEnabled(){var e=null==l?void 0:l.protocol;if("http:"!==e&&"https:"!==e)return iC.info("Web Vitals are disabled on non-http/https protocols"),!1;var t=T(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:L(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return L(t)?t:this.Yi}startIfEnabled(){this.isEnabled&&!this.i&&(iC.info("enabled, starting..."),this.J(this.nt))}onRemoteConfig(e){var t=T(e.capturePerformance)&&!!e.capturePerformance.web_vitals,r=T(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[eh]:t}),this._instance.persistence.register({[ep]:r})),this.Yi=t,this.startIfEnabled()}J(e){var t,r;null!=(t=f.__PosthogExtensions__)&&t.postHogWebVitalsCallbacks&&e(),null==(r=f.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"web-vitals",t=>{t?iC.error("failed to load script",t):e()})}te(){var e=t?t.location.href:void 0;return e||iC.error("Could not determine current URL"),e}}var iA=B("[Heatmaps]");function ik(e){return T(e)&&"clientX"in e&&"clientY"in e&&N(e.clientX)&&N(e.clientY)}class ij{constructor(e){var t;this.rageclicks=new ts,this.Yi=!1,this.i=!1,this.ee=null,this.instance=e,this.Yi=!(null==(t=this.instance.persistence)||!t.props[el])}get flushIntervalMilliseconds(){var e=5e3;return T(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return I(this.instance.config.capture_heatmaps)?I(this.instance.config.enable_heatmaps)?this.Yi:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled)this.i||(iA.info("starting..."),this.re(),this.ee=setInterval(this.se.bind(this),this.flushIntervalMilliseconds));else{var e,t;clearInterval(null!=(e=this.ee)?e:void 0),null==(t=this.ne)||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[el]:t}),this.Yi=t,this.startIfEnabled()}getAndClearBuffer(){var e=this.C;return this.C=void 0,e}oe(e){this.ae(e.originalEvent,"deadclick")}re(){t&&o&&(ei(t,"beforeunload",this.se.bind(this)),ei(o,"click",e=>this.ae(e||(null==t?void 0:t.event)),{capture:!0}),ei(o,"mousemove",e=>this.le(e||(null==t?void 0:t.event)),{capture:!0}),this.ne=new t$(this.instance,tF,this.oe.bind(this)),this.ne.startIfEnabled(),this.i=!0)}ue(e,r){var i=this.instance.scrollManager.scrollY(),n=this.instance.scrollManager.scrollX(),s=this.instance.scrollManager.scrollElement(),a=function(e,r,i){for(var n=e;n&&ez(n)&&!eX(n,"body")&&n!==i;){if(y(r,null==t?void 0:t.getComputedStyle(n).position))return!0;n=e3(n)}return!1}(e1(e),["fixed","sticky"],s);return{x:e.clientX+(a?0:n),y:e.clientY+(a?0:i),target_fixed:a,type:r}}ae(e,t){var r;if(void 0===t&&(t="click"),!eG(e.target)&&ik(e)){var i=this.ue(e,t);null!=(r=this.rageclicks)&&r.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this.he(W({},i,{type:"rageclick"})),this.he(i)}}le(e){!eG(e.target)&&ik(e)&&(clearTimeout(this.de),this.de=setTimeout(()=>{this.he(this.ue(e,"mousemove"))},500))}he(e){if(t){var r=t.location.href;this.C=this.C||{},this.C[r]||(this.C[r]=[]),this.C[r].push(e)}}se(){this.C&&!C(this.C)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class iM{constructor(e){this._instance=e}doPageView(e,r){var i,n=this.ve(e,r);return this.ce={pathname:null!=(i=null==t?void 0:t.location.pathname)?i:"",pageViewId:r,timestamp:e},this._instance.scrollManager.resetContext(),n}doPageLeave(e){var t;return this.ve(e,null==(t=this.ce)?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:null==(e=this.ce)?void 0:e.pageViewId}}ve(e,t){var r=this.ce;if(!r)return{$pageview_id:t};var i={$pageview_id:t,$prev_pageview_id:r.pageViewId},n=this._instance.scrollManager.getContext();if(n&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:s,lastScrollY:a,maxScrollY:o,maxContentHeight:l,lastContentY:u,maxContentY:c}=n;if(!(I(s)||I(a)||I(o)||I(l)||I(u)||I(c))){s=Math.ceil(s),a=Math.ceil(a),o=Math.ceil(o),l=Math.ceil(l),u=Math.ceil(u),c=Math.ceil(c);var d=s<=1?1:tU(a/s,0,1),h=s<=1?1:tU(o/s,0,1),f=l<=1?1:tU(u/l,0,1),p=l<=1?1:tU(c/l,0,1);i=Y(i,{$prev_pageview_last_scroll:a,$prev_pageview_last_scroll_percentage:d,$prev_pageview_max_scroll:o,$prev_pageview_max_scroll_percentage:h,$prev_pageview_last_content:u,$prev_pageview_last_content_percentage:f,$prev_pageview_max_content:c,$prev_pageview_max_content_percentage:p})}}return r.pathname&&(i.$prev_pageview_pathname=r.pathname),r.timestamp&&(i.$prev_pageview_duration=(e.getTime()-r.timestamp.getTime())/1e3),i}}var iN=function(e){var t,r,i,n,s="";for(t=r=0,i=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,n=0;n<i;n++){var a=e.charCodeAt(n),o=null;a<128?r++:o=a>127&&a<2048?String.fromCharCode(a>>6|192,63&a|128):String.fromCharCode(a>>12|224,a>>6&63|128,63&a|128),j(o)||(r>t&&(s+=e.substring(t,r)),s+=o,t=r=n+1)}return r>t&&(s+=e.substring(t,e.length)),s},iL=!!c||!!u,iF="text/plain",iD=(e,t)=>{var[r,i]=e.split("?"),n=W({},t);null==i||i.split("&").forEach(e=>{var[t]=e.split("=");delete n[t]});var s=tl(n);return r+"?"+(s=s?(i?i+"&":"")+s:i)},i$=(e,t)=>JSON.stringify(e,(e,t)=>"bigint"==typeof t?t.toString():t,t),iU=e=>{var{data:t,compression:r}=e;if(t){if(r===v.GZipJS){var i=new Blob([rJ(rZ(i$(t)),{mtime:0})],{type:iF});return{contentType:iF,body:i,estimatedSize:i.size}}if(r===v.Base64){var n=(e=>"data="+encodeURIComponent("string"==typeof e?e:i$(e)))(function(e){var t,r,i,n,s,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o=0,l=0,u="",c=[];if(!e)return e;e=iN(e);do t=(s=e.charCodeAt(o++)<<16|e.charCodeAt(o++)<<8|e.charCodeAt(o++))>>18&63,r=s>>12&63,i=s>>6&63,n=63&s,c[l++]=a.charAt(t)+a.charAt(r)+a.charAt(i)+a.charAt(n);while(o<e.length);switch(u=c.join(""),e.length%3){case 1:u=u.slice(0,-2)+"==";break;case 2:u=u.slice(0,-1)+"="}return u}(i$(t)));return{contentType:"application/x-www-form-urlencoded",body:n,estimatedSize:new Blob([n]).size}}var s=i$(t);return{contentType:"application/json",body:s,estimatedSize:new Blob([s]).size}}},iB=[];u&&iB.push({transport:"fetch",method:e=>{var t,r,{contentType:i,body:n,estimatedSize:s}=null!=(t=iU(e))?t:{},a=new Headers;V(e.headers,function(e,t){a.append(t,e)}),i&&a.append("Content-Type",i);var o=e.url,l=null;if(d){var c=new d;l={signal:c.signal,timeout:setTimeout(()=>c.abort(),e.timeout)}}u(o,W({method:(null==e?void 0:e.method)||"GET",headers:a,keepalive:"POST"===e.method&&52428.8>(s||0),body:n,signal:null==(r=l)?void 0:r.signal},e.fetchOptions)).then(t=>t.text().then(r=>{var i={statusCode:t.status,text:r};if(200===t.status)try{i.json=JSON.parse(r)}catch(e){U.error(e)}null==e.callback||e.callback(i)})).catch(t=>{U.error(t),null==e.callback||e.callback({statusCode:0,text:t})}).finally(()=>l?clearTimeout(l.timeout):null)}}),c&&iB.push({transport:"XHR",method:e=>{var t,r=new c;r.open(e.method||"GET",e.url,!0);var{contentType:i,body:n}=null!=(t=iU(e))?t:{};V(e.headers,function(e,t){r.setRequestHeader(t,e)}),i&&r.setRequestHeader("Content-Type",i),e.timeout&&(r.timeout=e.timeout),r.withCredentials=!0,r.onreadystatechange=()=>{if(4===r.readyState){var t={statusCode:r.status,text:r.responseText};if(200===r.status)try{t.json=JSON.parse(r.responseText)}catch(e){}null==e.callback||e.callback(t)}},r.send(n)}}),null!=a&&a.sendBeacon&&iB.push({transport:"sendBeacon",method:e=>{var t=iD(e.url,{beacon:"1"});try{var r,{contentType:i,body:n}=null!=(r=iU(e))?r:{},s="string"==typeof n?new Blob([n],{type:i}):n;a.sendBeacon(t,s)}catch(e){}}});var iH=function(e,t){if(!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t))return!1;try{return new RegExp(t).test(e)}catch(e){return!1}};function iq(e,t,r){return i$({distinct_id:e,userPropertiesToSet:t,userPropertiesToSetOnce:r})}var iW={exact:(e,t)=>t.some(t=>e.some(e=>t===e)),is_not:(e,t)=>t.every(t=>e.every(e=>t!==e)),regex:(e,t)=>t.some(t=>e.some(e=>iH(t,e))),not_regex:(e,t)=>t.every(t=>e.every(e=>!iH(t,e))),icontains:(e,t)=>t.map(iG).some(t=>e.map(iG).some(e=>t.includes(e))),not_icontains:(e,t)=>t.map(iG).every(t=>e.map(iG).every(e=>!t.includes(e)))},iG=e=>e.toLowerCase(),iz=B("[Error tracking]");class iX{constructor(e){var t,r;this.fe=[],this._instance=e,this.fe=null!=(t=null==(r=this._instance.persistence)?void 0:r.get_property(ec))?t:[]}onRemoteConfig(e){var t,r,i,n=null!=(t=null==(r=e.errorTracking)?void 0:r.suppressionRules)?t:[],s=null==(i=e.errorTracking)?void 0:i.captureExtensionExceptions;this.fe=n,this._instance.persistence&&this._instance.persistence.register({[ec]:this.fe,[ed]:s})}get pe(){var e,t=!!this._instance.get_property(ed),r=this._instance.config.error_tracking.captureExtensionExceptions;return null!=(e=null!=r?r:t)&&e}sendExceptionEvent(e){if(this._e(e))iz.info("Skipping exception capture because a suppression rule matched");else{if(this.pe||!this.ge(e))return this._instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"});iz.info("Skipping exception capture because it was thrown by an extension")}}_e(e){var t=e.$exception_list;if(!t||!x(t)||0===t.length)return!1;var r=t.reduce((e,t)=>{var{type:r,value:i}=t;return A(r)&&r.length>0&&e.$exception_types.push(r),A(i)&&i.length>0&&e.$exception_values.push(i),e},{$exception_types:[],$exception_values:[]});return this.fe.some(e=>{var t=e.values.map(e=>{var t,i=iW[e.operator],n=x(e.value)?e.value:[e.value],s=null!=(t=r[e.key])?t:[];return n.length>0&&i(n,s)});return"OR"===e.type?t.some(Boolean):t.every(Boolean)})}ge(e){var t=e.$exception_list;return!(!t||!x(t))&&t.flatMap(e=>{var t,r;return null!=(t=null==(r=e.stacktrace)?void 0:r.frames)?t:[]}).some(e=>e.filename&&e.filename.startsWith("chrome-extension://"))}}var iV="Mobile",iY="Android",iK="Tablet",iJ=iY+" "+iK,iZ="iPad",iQ="Apple",i0=iQ+" Watch",i1="Safari",i2="BlackBerry",i3="Samsung",i4=i3+"Browser",i6=i3+" Internet",i5="Chrome",i8=i5+" OS",i9=i5+" iOS",i7="Internet Explorer",ne=i7+" "+iV,nt="Opera",nr=nt+" Mini",ni="Edge",nn="Microsoft "+ni,ns="Firefox",na=ns+" iOS",no="Nintendo",nl="PlayStation",nu="Xbox",nc=iY+" "+iV,nd=iV+" "+i1,nh="Windows",nf=nh+" Phone",np="Nokia",n_="Ouya",ng="Generic",nv=ng+" "+iV.toLowerCase(),nm=ng+" "+iK.toLowerCase(),ny="Konqueror",nb="(\\d+(\\.\\d+)?)",nE=RegExp("Version/"+nb),nP=RegExp(nu,"i"),nS=RegExp(nl+" \\w+","i"),nw=RegExp(no+" \\w+","i"),nR=RegExp(i2+"|PlayBook|BB10","i"),nx={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"},nO=(e,t)=>t&&y(t,iQ)||function(e){return y(e,i1)&&!y(e,i5)&&!y(e,iY)}(e),nT=function(e,t){return t=t||"",y(e," OPR/")&&y(e,"Mini")?nr:y(e," OPR/")?nt:nR.test(e)?i2:y(e,"IE"+iV)||y(e,"WPDesktop")?ne:y(e,i4)?i6:y(e,ni)||y(e,"Edg/")?nn:y(e,"FBIOS")?"Facebook "+iV:y(e,"UCWEB")||y(e,"UCBrowser")?"UC Browser":y(e,"CriOS")?i9:y(e,"CrMo")||y(e,i5)?i5:y(e,iY)&&y(e,i1)?nc:y(e,"FxiOS")?na:y(e.toLowerCase(),ny.toLowerCase())?ny:nO(e,t)?y(e,iV)?nd:i1:y(e,ns)?ns:y(e,"MSIE")||y(e,"Trident/")?i7:y(e,"Gecko")?ns:""},nC={[ne]:[RegExp("rv:"+nb)],[nn]:[RegExp(ni+"?\\/"+nb)],[i5]:[RegExp("("+i5+"|CrMo)\\/"+nb)],[i9]:[RegExp("CriOS\\/"+nb)],"UC Browser":[RegExp("(UCBrowser|UCWEB)\\/"+nb)],[i1]:[nE],[nd]:[nE],[nt]:[RegExp("(Opera|OPR)\\/"+nb)],[ns]:[RegExp(ns+"\\/"+nb)],[na]:[RegExp("FxiOS\\/"+nb)],[ny]:[RegExp("Konqueror[:/]?"+nb,"i")],[i2]:[RegExp(i2+" "+nb),nE],[nc]:[RegExp("android\\s"+nb,"i")],[i6]:[RegExp(i4+"\\/"+nb)],[i7]:[RegExp("(rv:|MSIE )"+nb)],Mozilla:[RegExp("rv:"+nb)]},nI=function(e,t){var r=nC[nT(e,t)];if(I(r))return null;for(var i=0;i<r.length;i++){var n=r[i],s=e.match(n);if(s)return parseFloat(s[s.length-2])}return null},nA=[[RegExp(nu+"; "+nu+" (.*?)[);]","i"),e=>[nu,e&&e[1]||""]],[RegExp(no,"i"),[no,""]],[RegExp(nl,"i"),[nl,""]],[nR,[i2,""]],[RegExp(nh,"i"),(e,t)=>{if(/Phone/.test(t)||/WPDesktop/.test(t))return[nf,""];if(new RegExp(iV).test(t)&&!/IEMobile\b/.test(t))return[nh+" "+iV,""];var r=/Windows NT ([0-9.]+)/i.exec(t);if(r&&r[1]){var i=nx[r[1]]||"";return/arm/i.test(t)&&(i="RT"),[nh,i]}return[nh,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,e=>e&&e[3]?["iOS",[e[3],e[4],e[5]||"0"].join(".")]:["iOS",""]],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,e=>{var t="";return e&&e.length>=3&&(t=I(e[2])?e[3]:e[2]),["watchOS",t]}],[RegExp("("+iY+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+iY+")","i"),e=>e&&e[2]?[iY,[e[2],e[3],e[4]||"0"].join(".")]:[iY,""]],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,e=>{var t=["Mac OS X",""];if(e&&e[1]){var r=[e[1],e[2],e[3]||"0"];t[1]=r.join(".")}return t}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[i8,""]],[/Linux|debian/i,["Linux",""]]],nk=function(e){return nw.test(e)?no:nS.test(e)?nl:nP.test(e)?nu:RegExp(n_,"i").test(e)?n_:RegExp("("+nf+"|WPDesktop)","i").test(e)?nf:/iPad/.test(e)?iZ:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?i0:nR.test(e)?i2:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":RegExp(np,"i").test(e)?np:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(iV).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?iY:iJ:iY:RegExp("(pda|"+iV+")","i").test(e)?nv:RegExp(iK,"i").test(e)&&!RegExp(iK+" pc","i").test(e)?nm:""},nj="https?://(.*)",nM=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","epik","qclid","sccid","irclid","_kx"],nN=K(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],nM),nL="<masked>",nF=["li_fat_id"];function nD(e,t,r){if(!o)return{};var i,n=t?K([],nM,r||[]):[],s=n$(tc(o.URL,n,nL),e);return Y((i={},V(nF,function(e){var t=tR.D(e);i[e]=t||null}),i),s)}function n$(e,t){var r=nN.concat(t||[]),i={};return V(r,function(t){var r=tu(e,t);i[t]=r||null}),i}function nU(e){var t=e?0===e.search(nj+"google.([^/?]*)")?"google":0===e.search(nj+"bing.com")?"bing":0===e.search(nj+"yahoo.com")?"yahoo":0===e.search(nj+"duckduckgo.com")?"duckduckgo":null:null,r={};if(!j(t)){r.$search_engine=t;var i=o?tu(o.referrer,"yahoo"!=t?"q":"p"):"";i.length&&(r.ph_keyword=i)}return r}function nB(){return navigator.language||navigator.userLanguage}function nH(){return(null==o?void 0:o.referrer)||"$direct"}function nq(e,t){var r=e?K([],nM,t||[]):[],i=null==l?void 0:l.href.substring(0,1e3);return{r:nH().substring(0,1e3),u:i?tc(i,r,nL):void 0}}function nW(e){var t,{r:r,u:i}=e,n={$referrer:r,$referring_domain:null==r?void 0:"$direct"==r?"$direct":null==(t=to(r))?void 0:t.host};if(i){n.$current_url=i;var s=to(i);n.$host=null==s?void 0:s.host,n.$pathname=null==s?void 0:s.pathname,Y(n,n$(i))}return r&&Y(n,nU(r)),n}function nG(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return}}var nz=B("[FeatureFlags]"),nX="$active_feature_flags",nV="$override_feature_flags",nY="$feature_flag_payloads",nK="$override_feature_flag_payloads",nJ="$feature_flag_request_id",nZ=e=>{var t={};for(var[r,i]of J(e||{}))i&&(t[r]=i);return t},nQ=e=>{var t=e.flags;return t?(e.featureFlags=Object.fromEntries(Object.keys(t).map(e=>{var r;return[e,null!=(r=t[e].variant)?r:t[e].enabled]})),e.featureFlagPayloads=Object.fromEntries(Object.keys(t).filter(e=>t[e].enabled).filter(e=>{var r;return null==(r=t[e].metadata)?void 0:r.payload}).map(e=>{var r;return[e,null==(r=t[e].metadata)?void 0:r.payload]}))):nz.warn("Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version"),e},n0=function(e){return e.FeatureFlags="feature_flags",e.Recordings="recordings",e}({});class n1{constructor(e){this.me=!1,this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this._instance=e,this.featureFlagEventHandlers=[]}flags(){if(this._instance.config.__preview_remote_config)this.$e=!0;else{var e=!this.ke&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this.Ee({disableFlags:e})}}get hasLoadedFlags(){return this.be}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var e=this._instance.get_property(eC),t=this._instance.get_property(nV),r=this._instance.get_property(nK);if(!r&&!t)return e||{};var i=Y({},e||{});for(var n of[...new Set([...Object.keys(r||{}),...Object.keys(t||{})])]){var s,a,o=i[n],l=null==t?void 0:t[n],u=I(l)?null!=(s=null==o?void 0:o.enabled)&&s:!!l,c=I(l)?o.variant:"string"==typeof l?l:void 0,d=null==r?void 0:r[n],h=W({},o,{enabled:u,variant:u?null!=c?c:null==o?void 0:o.variant:void 0});u!==(null==o?void 0:o.enabled)&&(h.original_enabled=null==o?void 0:o.enabled),c!==(null==o?void 0:o.variant)&&(h.original_variant=null==o?void 0:o.variant),d&&(h.metadata=W({},null==o?void 0:o.metadata,{payload:d,original_payload:null==o||null==(a=o.metadata)?void 0:a.payload})),i[n]=h}return this.me||(nz.warn(" Overriding feature flag details!",{flagDetails:e,overriddenPayloads:r,finalDetails:i}),this.me=!0),i}getFlagVariants(){var e=this._instance.get_property(eO),t=this._instance.get_property(nV);if(!t)return e||{};for(var r=Y({},e),i=Object.keys(t),n=0;n<i.length;n++)r[i[n]]=t[i[n]];return this.me||(nz.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:r}),this.me=!0),r}getFlagPayloads(){var e=this._instance.get_property(nY),t=this._instance.get_property(nK);if(!t)return e||{};for(var r=Y({},e||{}),i=Object.keys(t),n=0;n<i.length;n++)r[i[n]]=t[i[n]];return this.me||(nz.warn(" Overriding feature flag payloads!",{flagPayloads:e,overriddenPayloads:t,finalPayloads:r}),this.me=!0),r}reloadFeatureFlags(){this.we||this._instance.config.advanced_disable_feature_flags||this.ke||(this.ke=setTimeout(()=>{this.Ee()},5))}Ie(){clearTimeout(this.ke),this.ke=void 0}ensureFlagsLoaded(){this.be||this.ye||this.ke||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this.we=e}Ee(e){var t;if(this.Ie(),!this._instance.I())if(this.ye)this.Se=!0;else{var r={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:W({},(null==(t=this._instance.persistence)?void 0:t.get_initial_props())||{},this._instance.get_property(eI)||{}),group_properties:this._instance.get_property(eA)};(null!=e&&e.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(r.disable_flags=!0);var i=this._instance.config.__preview_remote_config,n=this._instance.config.advanced_only_evaluate_survey_feature_flags?"&only_evaluate_survey_feature_flags=true":"",s=this._instance.requestRouter.endpointFor("api",(i?"/flags/?v=2":"/flags/?v=2&config=true")+n);i&&(r.timezone=nG()),this.ye=!0,this._instance.Pe({method:"POST",url:s,data:r,compression:this._instance.config.disable_compression?void 0:v.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:e=>{var t,i,n,s=!0;200===e.statusCode&&(this.Se||(this.$anon_distinct_id=void 0),s=!1),this.ye=!1,this.$e||(this.$e=!0,this._instance.Re(null!=(n=e.json)?n:{})),(!r.disable_flags||this.Se)&&((this.xe=!s,e.json&&null!=(i=e.json.quotaLimited)&&i.includes(n0.FeatureFlags))?nz.warn("You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more."):(r.disable_flags||this.receivedFeatureFlags(null!=(t=e.json)?t:{},s),this.Se&&(this.Se=!1,this.Ee())))}})}}getFeatureFlag(e,t){if(void 0===t&&(t={}),this.be||this.getFlags()&&this.getFlags().length>0){var r=this.getFlagVariants()[e],i=""+r,n=this._instance.get_property(nJ)||void 0,s=this._instance.get_property(eM)||{};if((t.send_event||!("send_event"in t))&&(!(e in s)||!s[e].includes(i))){x(s[e])?s[e].push(i):s[e]=[i],null==(l=this._instance.persistence)||l.register({[eM]:s});var a=this.getFeatureFlagDetails(e),o={$feature_flag:e,$feature_flag_response:r,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_request_id:n,$feature_flag_bootstrapped_response:(null==(u=this._instance.config.bootstrap)||null==(u=u.featureFlags)?void 0:u[e])||null,$feature_flag_bootstrapped_payload:(null==(c=this._instance.config.bootstrap)||null==(c=c.featureFlagPayloads)?void 0:c[e])||null,$used_bootstrap_value:!this.xe};I(null==a||null==(d=a.metadata)?void 0:d.version)||(o.$feature_flag_version=a.metadata.version);var l,u,c,d,h,f,p,_,g,v,m=null!=(h=null==a||null==(f=a.reason)?void 0:f.description)?h:null==a||null==(p=a.reason)?void 0:p.code;m&&(o.$feature_flag_reason=m),null!=a&&null!=(_=a.metadata)&&_.id&&(o.$feature_flag_id=a.metadata.id),I(null==a?void 0:a.original_variant)&&I(null==a?void 0:a.original_enabled)||(o.$feature_flag_original_response=I(a.original_variant)?a.original_enabled:a.original_variant),null!=a&&null!=(g=a.metadata)&&g.original_payload&&(o.$feature_flag_original_payload=null==a||null==(v=a.metadata)?void 0:v.original_payload),this._instance.capture("$feature_flag_called",o)}return r}nz.warn('getFeatureFlag for key "'+e+"\" failed. Feature flags didn't load in time.")}getFeatureFlagDetails(e){return this.getFlagsWithDetails()[e]}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}getRemoteConfigPayload(e,t){var r=this._instance.config.token;this._instance.Pe({method:"POST",url:this._instance.requestRouter.endpointFor("api","/flags/?v=2&config=true"),data:{distinct_id:this._instance.get_distinct_id(),token:r},compression:this._instance.config.disable_compression?void 0:v.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:r=>{var i,n=null==(i=r.json)?void 0:i.featureFlagPayloads;t((null==n?void 0:n[e])||void 0)}})}isFeatureEnabled(e,t){if(void 0===t&&(t={}),this.be||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);nz.warn('isFeatureEnabled for key "'+e+"\" failed. Feature flags didn't load in time.")}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter(t=>t!==e)}receivedFeatureFlags(e,t){if(this._instance.persistence){this.be=!0;var r=this.getFlagVariants(),i=this.getFlagPayloads(),n=this.getFlagsWithDetails();!function(e,t,r,i,n){void 0===r&&(r={}),void 0===i&&(i={}),void 0===n&&(n={});var s=nQ(e),a=s.flags,o=s.featureFlags,l=s.featureFlagPayloads;if(o){var u=e.requestId;if(x(o)){nz.warn("v1 of the feature flags endpoint is deprecated. Please use the latest version.");var c={};if(o)for(var d=0;d<o.length;d++)c[o[d]]=!0;t&&t.register({[nX]:o,[eO]:c})}else{var h=o,f=l,p=a;e.errorsWhileComputingFlags&&(h=W({},r,h),f=W({},i,f),p=W({},n,p)),t&&t.register(W({[nX]:Object.keys(nZ(h)),[eO]:h||{},[nY]:f||{},[eC]:p||{}},u?{[nJ]:u}:{}))}}}(e,this._instance.persistence,r,i,n),this.Te(t)}}override(e,t){void 0===t&&(t=!1),nz.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:e,suppressWarning:t})}overrideFeatureFlags(e){if(!this._instance.__loaded||!this._instance.persistence)return nz.uninitializedWarning("posthog.featureFlags.overrideFeatureFlags");if(!1===e)return this._instance.persistence.unregister(nV),this._instance.persistence.unregister(nK),void this.Te();if(e&&"object"==typeof e&&("flags"in e||"payloads"in e)){var t;if(this.me=!!(null!=(t=e.suppressWarning)&&t),"flags"in e){if(!1===e.flags)this._instance.persistence.unregister(nV);else if(e.flags)if(x(e.flags)){for(var r={},i=0;i<e.flags.length;i++)r[e.flags[i]]=!0;this._instance.persistence.register({[nV]:r})}else this._instance.persistence.register({[nV]:e.flags})}return"payloads"in e&&(!1===e.payloads?this._instance.persistence.unregister(nK):e.payloads&&this._instance.persistence.register({[nK]:e.payloads})),void this.Te()}this.Te()}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this.be){var{flags:t,flagVariants:r}=this.Me();e(t,r)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t,r){var i,n=(this._instance.get_property(eT)||[]).find(t=>t.flagKey===e),s={["$feature_enrollment/"+e]:t},a={$feature_flag:e,$feature_enrollment:t,$set:s};n&&(a.$early_access_feature_name=n.name),r&&(a.$feature_enrollment_stage=r),this._instance.capture("$feature_enrollment_update",a),this.setPersonPropertiesForFlags(s,!1);var o=W({},this.getFlagVariants(),{[e]:t});null==(i=this._instance.persistence)||i.register({[nX]:Object.keys(nZ(o)),[eO]:o}),this.Te()}getEarlyAccessFeatures(e,t,r){void 0===t&&(t=!1);var i=this._instance.get_property(eT),n=r?"&"+r.map(e=>"stage="+e).join("&"):"";if(i&&!t)return e(i);this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/early_access_features/?token="+this._instance.config.token+n),method:"GET",callback:t=>{var r,i;if(t.json){var n=t.json.earlyAccessFeatures;return null==(r=this._instance.persistence)||r.unregister(eT),null==(i=this._instance.persistence)||i.register({[eT]:n}),e(n)}}})}Me(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter(e=>t[e]),flagVariants:Object.keys(t).filter(e=>t[e]).reduce((e,r)=>(e[r]=t[r],e),{})}}Te(e){var{flags:t,flagVariants:r}=this.Me();this.featureFlagEventHandlers.forEach(i=>i(t,r,{errorsLoading:e}))}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0);var r=this._instance.get_property(eI)||{};this._instance.register({[eI]:W({},r,e)}),t&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(eI)}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0);var r=this._instance.get_property(eA)||{};0!==Object.keys(r).length&&Object.keys(r).forEach(t=>{r[t]=W({},r[t],e[t]),delete e[t]}),this._instance.register({[eA]:W({},r,e)}),t&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this._instance.get_property(eA)||{};this._instance.register({[eA]:W({},t,{[e]:{}})})}else this._instance.unregister(eA)}reset(){this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this.$anon_distinct_id=void 0,this.Ie(),this.me=!1}}var n2=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class n3{constructor(e,t){this.S=e,this.props={},this.Ce=!1,this.Fe=(e=>{var t="";return e.token&&(t=e.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),e.persistence_name?"ph_"+e.persistence_name:"ph_"+t+"_posthog"})(e),this.B=this.Oe(e),this.load(),e.debug&&U.info("Persistence loaded",e.persistence,W({},this.props)),this.update_config(e,e,t),this.save()}isDisabled(){return!!this.Ae}Oe(e){-1===n2.indexOf(e.persistence.toLowerCase())&&(U.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return"localstorage"===t&&tO.O()?tO:"localstorage+cookie"===t&&tC.O()?tC:"sessionstorage"===t&&tj.O()?tj:"memory"===t?tA:"cookie"===t?tR:tC.O()?tC:tR}properties(){var e={};return V(this.props,function(t,r){if(r===eO&&T(t))for(var i,n=Object.keys(t),a=0;a<n.length;a++)e["$feature/"+n[a]]=t[n[a]];else i=!1,(j(eW)?i:s&&eW.indexOf===s?-1!=eW.indexOf(r):(V(eW,function(e){if(i||(i=e===r))return z}),i))||(e[r]=t)}),e}load(){if(!this.Ae){var e=this.B.L(this.Fe);e&&(this.props=Y({},e))}}save(){this.Ae||this.B.j(this.Fe,this.props,this.De,this.Le,this.je,this.S.debug)}remove(){this.B.N(this.Fe,!1),this.B.N(this.Fe,!0)}clear(){this.remove(),this.props={}}register_once(e,t,r){if(T(e)){I(t)&&(t="None"),this.De=I(r)?this.Ne:r;var i=!1;if(V(e,(e,r)=>{this.props.hasOwnProperty(r)&&this.props[r]!==t||(this.props[r]=e,i=!0)}),i)return this.save(),!0}return!1}register(e,t){if(T(e)){this.De=I(t)?this.Ne:t;var r=!1;if(V(e,(t,i)=>{e.hasOwnProperty(i)&&this.props[i]!==t&&(this.props[i]=t,r=!0)}),r)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this.Ce){var e=nD(this.S.custom_campaign_params,this.S.mask_personal_data_properties,this.S.custom_personal_data_properties);C(ee(e))||this.register(e),this.Ce=!0}}update_search_keyword(){var e;this.register((e=null==o?void 0:o.referrer)?nU(e):{})}update_referrer_info(){var e;this.register_once({$referrer:nH(),$referring_domain:null!=o&&o.referrer&&(null==(e=to(o.referrer))?void 0:e.host)||"$direct"},void 0)}set_initial_person_info(){this.props[eD]||this.props[e$]||this.register_once({[eU]:nq(this.S.mask_personal_data_properties,this.S.custom_personal_data_properties)},void 0)}get_initial_props(){var e={};V([e$,eD],t=>{var r=this.props[t];r&&V(r,function(t,r){e["$initial_"+E(r)]=t})});var t,r,i=this.props[eU];return i&&Y(e,(t=nW(i),r={},V(t,function(e,t){r["$initial_"+E(t)]=e}),r)),e}safe_merge(e){return V(this.props,function(t,r){r in e||(e[r]=t)}),e}update_config(e,t,r){if(this.Ne=this.De=e.cookie_expiration,this.set_disabled(e.disable_persistence||!!r),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var i=this.Oe(e),n=this.props;this.clear(),this.B=i,this.props=n,this.save()}}set_disabled(e){this.Ae=e,this.Ae?this.remove():this.save()}set_cross_subdomain(e){e!==this.Le&&(this.Le=e,this.remove(),this.save())}set_secure(e){e!==this.je&&(this.je=e,this.remove(),this.save())}set_event_timer(e,t){var r=this.props[ea]||{};r[e]=t,this.props[ea]=r,this.save()}remove_event_timer(e){var t=(this.props[ea]||{})[e];return I(t)||(delete this.props[ea][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}class n4{constructor(){this.ze={},this.ze={}}on(e,t){return this.ze[e]||(this.ze[e]=[]),this.ze[e].push(t),()=>{this.ze[e]=this.ze[e].filter(e=>e!==t)}}emit(e,t){for(var r of this.ze[e]||[])r(t);for(var i of this.ze["*"]||[])i(e,t)}}class n6{constructor(e){this.Ue=new n4,this.qe=(e,t)=>this.Be(e,t)&&this.He(e,t)&&this.We(e,t),this.Be=(e,t)=>null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event),this._instance=e,this.Ge=new Set,this.Je=new Set}init(){var e,t;I(null==(e=this._instance)?void 0:e.Ve)||null==(t=this._instance)||t.Ve((e,t)=>{this.on(e,t)})}register(e){var t,r;if(!I(null==(t=this._instance)?void 0:t.Ve)&&(e.forEach(e=>{var t,r;null==(t=this.Je)||t.add(e),null==(r=e.steps)||r.forEach(e=>{var t;null==(t=this.Ge)||t.add((null==e?void 0:e.event)||"")})}),null!=(r=this._instance)&&r.autocapture)){var i,n=new Set;e.forEach(e=>{var t;null==(t=e.steps)||t.forEach(e=>{null!=e&&e.selector&&n.add(null==e?void 0:e.selector)})}),null==(i=this._instance)||i.autocapture.setElementSelectors(n)}}on(e,t){var r;null!=t&&0!=e.length&&(this.Ge.has(e)||this.Ge.has(null==t?void 0:t.event))&&this.Je&&(null==(r=this.Je)?void 0:r.size)>0&&this.Je.forEach(e=>{this.Ke(t,e)&&this.Ue.emit("actionCaptured",e.name)})}Ye(e){this.onAction("actionCaptured",t=>e(t))}Ke(e,t){if(null==(null==t?void 0:t.steps))return!1;for(var r of t.steps)if(this.qe(e,r))return!0;return!1}onAction(e,t){return this.Ue.on(e,t)}He(e,t){if(null!=t&&t.url){var r,i=null==e||null==(r=e.properties)?void 0:r.$current_url;if(!i||"string"!=typeof i||!n6.Xe(i,null==t?void 0:t.url,(null==t?void 0:t.url_matching)||"contains"))return!1}return!0}static Xe(e,r,i){switch(i){case"regex":return!!t&&iH(e,r);case"exact":return r===e;case"contains":return iH(e,n6.Qe(r).replace(/_/g,".").replace(/%/g,".*"));default:return!1}}static Qe(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}We(e,t){if((null!=t&&t.href||null!=t&&t.tag_name||null!=t&&t.text)&&!this.Ze(e).some(e=>!(null!=t&&t.href&&!n6.Xe(e.href||"",null==t?void 0:t.href,(null==t?void 0:t.href_matching)||"exact"))&&(null==t||!t.tag_name||e.tag_name===(null==t?void 0:t.tag_name))&&!(null!=t&&t.text&&!n6.Xe(e.text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact")&&!n6.Xe(e.$el_text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact"))))return!1;if(null!=t&&t.selector){var r,i=null==e||null==(r=e.properties)?void 0:r.$element_selectors;if(!i||!i.includes(null==t?void 0:t.selector))return!1}return!0}Ze(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements}}(function(e){e.Button="button",e.Tab="tab",e.Selector="selector"})({}),function(e){e.TopLeft="top_left",e.TopRight="top_right",e.TopCenter="top_center",e.MiddleLeft="middle_left",e.MiddleRight="middle_right",e.MiddleCenter="middle_center",e.Left="left",e.Center="center",e.Right="right",e.NextToTrigger="next_to_trigger"}({});var n5=function(e){return e.Popover="popover",e.API="api",e.Widget="widget",e.ExternalSurvey="external_survey",e}({}),n8=(function(e){e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link"}({}),function(e){e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question"}({}),function(e){e.Once="once",e.Recurring="recurring",e.Always="always"}({}),function(e){return e.SHOWN="survey shown",e.DISMISSED="survey dismissed",e.SENT="survey sent",e}({})),n9=function(e){return e.SURVEY_ID="$survey_id",e.SURVEY_NAME="$survey_name",e.SURVEY_RESPONSE="$survey_response",e.SURVEY_ITERATION="$survey_iteration",e.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",e.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",e.SURVEY_SUBMISSION_ID="$survey_submission_id",e.SURVEY_QUESTIONS="$survey_questions",e.SURVEY_COMPLETED="$survey_completed",e}({}),n7=B("[Surveys]"),se="seenSurvey_",st=(e,t)=>{var r="$survey_"+t+"/"+e.id;return e.current_iteration&&e.current_iteration>0&&(r="$survey_"+t+"/"+e.id+"/"+e.current_iteration),r},sr=[n5.Popover,n5.Widget,n5.API];class si{constructor(e){this._instance=e,this.tr=new Map,this.ir=new Map}register(e){var t;I(null==(t=this._instance)?void 0:t.Ve)||(this.er(e),this.rr(e))}rr(e){var t=e.filter(e=>{var t,r;return(null==(t=e.conditions)?void 0:t.actions)&&(null==(r=e.conditions)||null==(r=r.actions)||null==(r=r.values)?void 0:r.length)>0});0!==t.length&&(null==this.sr&&(this.sr=new n6(this._instance),this.sr.init(),this.sr.Ye(e=>{this.onAction(e)})),t.forEach(e=>{var t,r,i,n,s;e.conditions&&null!=(t=e.conditions)&&t.actions&&null!=(r=e.conditions)&&null!=(r=r.actions)&&r.values&&(null==(i=e.conditions)||null==(i=i.actions)||null==(i=i.values)?void 0:i.length)>0&&(null==(n=this.sr)||n.register(e.conditions.actions.values),null==(s=e.conditions)||null==(s=s.actions)||null==(s=s.values)||s.forEach(t=>{if(t&&t.name){var r=this.ir.get(t.name);r&&r.push(e.id),this.ir.set(t.name,r||[e.id])}}))}))}er(e){var t;0!==e.filter(e=>{var t,r;return(null==(t=e.conditions)?void 0:t.events)&&(null==(r=e.conditions)||null==(r=r.events)||null==(r=r.values)?void 0:r.length)>0}).length&&(null==(t=this._instance)||t.Ve((e,t)=>{this.onEvent(e,t)}),e.forEach(e=>{var t;null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||t.forEach(t=>{if(t&&t.name){var r=this.tr.get(t.name);r&&r.push(e.id),this.tr.set(t.name,r||[e.id])}})}))}onEvent(e,t){var r,i=(null==(r=this._instance)||null==(r=r.persistence)?void 0:r.props[ej])||[];if("survey shown"===e&&t&&i.length>0){n7.info("survey event matched, removing survey from activated surveys",{event:e,eventPayload:t,existingActivatedSurveys:i});var n,s=null==t||null==(n=t.properties)?void 0:n.$survey_id;if(s){var a=i.indexOf(s);a>=0&&(i.splice(a,1),this.nr(i))}}else this.tr.has(e)&&(n7.info("survey event matched, updating activated surveys",{event:e,surveys:this.tr.get(e)}),this.nr(i.concat(this.tr.get(e)||[])))}onAction(e){var t,r=(null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[ej])||[];this.ir.has(e)&&this.nr(r.concat(this.ir.get(e)||[]))}nr(e){var t;null==(t=this._instance)||null==(t=t.persistence)||t.register({[ej]:[...new Set(e)]})}getSurveys(){var e;return(null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[ej])||[]}getEventToSurveys(){return this.tr}ar(){return this.sr}}class sn{constructor(e){this.lr=void 0,this._surveyManager=null,this.ur=!1,this.hr=!1,this.dr=[],this._instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){var t=e.surveys;if(M(t))return n7.warn("Flags not loaded yet. Not loading surveys.");var r=x(t);this.lr=r?t.length>0:t,n7.info("flags response received, isSurveysEnabled: "+this.lr),this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate");for(var e=[],t=0;t<localStorage.length;t++){var r=localStorage.key(t);(null!=r&&r.startsWith(se)||null!=r&&r.startsWith("inProgressSurvey_"))&&e.push(r)}e.forEach(e=>localStorage.removeItem(e))}loadIfEnabled(){if(!this._surveyManager)if(this.hr)n7.info("Already initializing surveys, skipping...");else if(this._instance.config.disable_surveys)n7.info("Disabled. Not loading surveys.");else{var e=null==f?void 0:f.__PosthogExtensions__;if(e){if(!I(this.lr)||this._instance.config.advanced_enable_surveys){var t=this.lr||this._instance.config.advanced_enable_surveys;this.hr=!0;try{var r=e.generateSurveys;if(r)return void this.vr(r,t);var i=e.loadExternalDependency;if(!i)return void this.cr("PostHog loadExternalDependency extension not found.");i(this._instance,"surveys",r=>{r||!e.generateSurveys?this.cr("Could not load surveys script",r):this.vr(e.generateSurveys,t)})}catch(e){throw this.cr("Error initializing surveys",e),e}finally{this.hr=!1}}}else n7.error("PostHog Extensions not found.")}}vr(e,t){this._surveyManager=e(this._instance,t),this._surveyEventReceiver=new si(this._instance),n7.info("Surveys loaded successfully"),this.pr({isLoaded:!0})}cr(e,t){n7.error(e,t),this.pr({isLoaded:!1,error:e})}onSurveysLoaded(e){return this.dr.push(e),this._surveyManager&&this.pr({isLoaded:!0}),()=>{this.dr=this.dr.filter(t=>t!==e)}}getSurveys(e,t){if(void 0===t&&(t=!1),this._instance.config.disable_surveys)return n7.info("Disabled. Not loading surveys."),e([]);var r=this._instance.get_property(ek);if(r&&!t)return e(r,{isLoaded:!0});if(this.ur)return e([],{isLoaded:!1,error:"Surveys are already being loaded"});try{this.ur=!0,this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/surveys/?token="+this._instance.config.token),method:"GET",timeout:this._instance.config.surveys_request_timeout_ms,callback:t=>{this.ur=!1;var r=t.statusCode;if(200!==r||!t.json){var i="Surveys API could not be loaded, status: "+r;return n7.error(i),e([],{isLoaded:!1,error:i})}var n,s,a=t.json.surveys||[],o=a.filter(e=>{var t,r;return!(!e.start_date||e.end_date)&&(!(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||!t.length)||!(null==(r=e.conditions)||null==(r=r.actions)||null==(r=r.values)||!r.length))});return o.length>0&&(null==(s=this._surveyEventReceiver)||s.register(o)),null==(n=this._instance.persistence)||n.register({[ek]:a}),e(a,{isLoaded:!0})}})}catch(e){throw this.ur=!1,e}}pr(e){for(var t of this.dr)try{if(!e.isLoaded)return t([],e);this.getSurveys(t)}catch(e){n7.error("Error in survey callback",e)}}getActiveMatchingSurveys(e,t){if(void 0===t&&(t=!1),!M(this._surveyManager))return this._surveyManager.getActiveMatchingSurveys(e,t);n7.warn("init was not called")}_r(e){var t=null;return this.getSurveys(r=>{var i;t=null!=(i=r.find(t=>t.id===e))?i:null}),t}gr(e){if(M(this._surveyManager))return{eligible:!1,reason:"SDK is not enabled or survey functionality is not yet loaded"};var t="string"==typeof e?this._r(e):e;return t?this._surveyManager.checkSurveyEligibility(t):{eligible:!1,reason:"Survey not found"}}canRenderSurvey(e){if(M(this._surveyManager))return n7.warn("init was not called"),{visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"};var t=this.gr(e);return{visible:t.eligible,disabledReason:t.reason}}canRenderSurveyAsync(e,t){return M(this._surveyManager)?(n7.warn("init was not called"),Promise.resolve({visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"})):new Promise(r=>{this.getSurveys(t=>{var i,n=null!=(i=t.find(t=>t.id===e))?i:null;if(n){var s=this.gr(n);r({visible:s.eligible,disabledReason:s.reason})}else r({visible:!1,disabledReason:"Survey not found"})},t)})}renderSurvey(e,t){if(M(this._surveyManager))n7.warn("init was not called");else{var r=this._r(e);if(r)if(sr.includes(r.type)){var i=null==o?void 0:o.querySelector(t);i?this._surveyManager.renderSurvey(r,i):n7.warn("Survey element not found")}else n7.warn("Surveys of type "+r.type+" cannot be rendered in the app");else n7.warn("Survey not found")}}}var ss=B("[RateLimiter]");class sa{constructor(e){var t,r;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=e=>{var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach(e=>{ss.info((e||"events")+" is quota limited."),this.serverLimits[e]=(new Date).getTime()+6e4})}catch(e){return void ss.warn('could not rate limit - continuing. Error: "'+(null==e?void 0:e.message)+'"',{text:t})}},this.instance=e,this.captureEventsPerSecond=(null==(t=e.config.rate_limiting)?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(r=e.config.rate_limiting)?void 0:r.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(e){void 0===e&&(e=!1);var t,r,i,n=(new Date).getTime(),s=null!=(t=null==(r=this.instance.persistence)?void 0:r.get_property(eF))?t:{tokens:this.captureEventsBurstLimit,last:n};s.tokens+=(n-s.last)/1e3*this.captureEventsPerSecond,s.last=n,s.tokens>this.captureEventsBurstLimit&&(s.tokens=this.captureEventsBurstLimit);var a=s.tokens<1;return a||e||(s.tokens=Math.max(0,s.tokens-1)),!a||this.lastEventRateLimited||e||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to "+this.captureEventsPerSecond+" events per second and "+this.captureEventsBurstLimit+" events burst limit."},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=a,null==(i=this.instance.persistence)||i.set_property(eF,s),{isRateLimited:a,remainingTokens:s.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||"events"]||!1;return!1!==t&&(new Date).getTime()<t}}var so=B("[RemoteConfig]");class sl{constructor(e){this._instance=e}get remoteConfig(){var e;return null==(e=f._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.config}mr(e){var t,r;null!=(t=f.__PosthogExtensions__)&&t.loadExternalDependency?null==(r=f.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,"remote-config",()=>e(this.remoteConfig)):(so.error("PostHog Extensions not found. Cannot load remote config."),e())}br(e){this._instance.Pe({method:"GET",url:this._instance.requestRouter.endpointFor("assets","/array/"+this._instance.config.token+"/config"),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return so.info("Using preloaded remote config",this.remoteConfig),void this.Re(this.remoteConfig);if(this._instance.I())return void so.warn("Remote config is disabled. Falling back to local config.");this.mr(e=>{if(!e)return so.info("No config found after loading remote JS config. Falling back to JSON."),void this.br(e=>{this.Re(e)});this.Re(e)})}catch(e){so.error("Error loading remote config",e)}}Re(e){e?this._instance.config.__preview_remote_config?(this._instance.Re(e),!1!==e.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):so.info("__preview_remote_config is disabled. Logging config instead",e):so.error("Failed to fetch remote config from PostHog.")}}class su{constructor(e,t){this.yr=!0,this.wr=[],this.Sr=tU((null==t?void 0:t.flush_interval_ms)||3e3,250,5e3,"flush interval",3e3),this.$r=e}enqueue(e){this.wr.push(e),this.kr||this.Er()}unload(){this.Ir();var e=Object.values(this.wr.length>0?this.Pr():{});[...e.filter(e=>0===e.url.indexOf("/e")),...e.filter(e=>0!==e.url.indexOf("/e"))].map(e=>{this.$r(W({},e,{transport:"sendBeacon"}))})}enable(){this.yr=!1,this.Er()}Er(){var e=this;this.yr||(this.kr=setTimeout(()=>{if(this.Ir(),this.wr.length>0){var t=this.Pr();for(var r in t)!function(){var i=t[r],n=(new Date).getTime();i.data&&x(i.data)&&V(i.data,e=>{e.offset=Math.abs(e.timestamp-n),delete e.timestamp}),e.$r(i)}()}},this.Sr))}Ir(){clearTimeout(this.kr),this.kr=void 0}Pr(){var e={};return V(this.wr,t=>{var r,i=(t?t.batchKey:null)||t.url;I(e[i])&&(e[i]=W({},t,{data:[]})),null==(r=e[i].data)||r.push(t.data)}),this.wr=[],e}}var sc=["retriesPerformedSoFar"];class sd{constructor(e){this.Rr=!1,this.Tr=3e3,this.wr=[],this._instance=e,this.wr=[],this.Mr=!0,!I(t)&&"onLine"in t.navigator&&(this.Mr=t.navigator.onLine,ei(t,"online",()=>{this.Mr=!0,this.se()}),ei(t,"offline",()=>{this.Mr=!1}))}get length(){return this.wr.length}retriableRequest(e){var{retriesPerformedSoFar:t}=e,r=G(e,sc);N(t)&&t>0&&(r.url=iD(r.url,{retry_count:t})),this._instance.Pe(W({},r,{callback:e=>{200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=t?t:0)<10?this.Cr(W({retriesPerformedSoFar:t},r)):null==r.callback||r.callback(e)}}))}Cr(e){var t,r,i,n=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=n+1;var s=(i=(Math.random()-.5)*((r=Math.min(18e5,t=3e3*Math.pow(2,n)))-t/2),Math.ceil(r+i)),a=Date.now()+s;this.wr.push({retryAt:a,requestOptions:e});var o="Enqueued failed request for retry in "+s;navigator.onLine||(o+=" (Browser is offline)"),U.warn(o),this.Rr||(this.Rr=!0,this.Fr())}Fr(){this.Or&&clearTimeout(this.Or),this.Or=setTimeout(()=>{this.Mr&&this.wr.length>0&&this.se(),this.Fr()},this.Tr)}se(){var e=Date.now(),t=[],r=this.wr.filter(r=>r.retryAt<e||(t.push(r),!1));if(this.wr=t,r.length>0)for(var{requestOptions:i}of r)this.retriableRequest(i)}unload(){for(var{requestOptions:e}of(this.Or&&(clearTimeout(this.Or),this.Or=void 0),this.wr))try{this._instance.Pe(W({},e,{transport:"sendBeacon"}))}catch(e){U.error(e)}this.wr=[]}}class sh{constructor(e){this.Ar=()=>{this.Dr||(this.Dr={});var e,t,r,i,n=this.scrollElement(),s=this.scrollY(),a=n?Math.max(0,n.scrollHeight-n.clientHeight):0,o=s+((null==n?void 0:n.clientHeight)||0),l=(null==n?void 0:n.scrollHeight)||0;this.Dr.lastScrollY=Math.ceil(s),this.Dr.maxScrollY=Math.max(s,null!=(e=this.Dr.maxScrollY)?e:0),this.Dr.maxScrollHeight=Math.max(a,null!=(t=this.Dr.maxScrollHeight)?t:0),this.Dr.lastContentY=o,this.Dr.maxContentY=Math.max(o,null!=(r=this.Dr.maxContentY)?r:0),this.Dr.maxContentHeight=Math.max(l,null!=(i=this.Dr.maxContentHeight)?i:0)},this._instance=e}getContext(){return this.Dr}resetContext(){var e=this.Dr;return setTimeout(this.Ar,0),e}startMeasuringScrollPosition(){ei(t,"scroll",this.Ar,{capture:!0}),ei(t,"scrollend",this.Ar,{capture:!0}),ei(t,"resize",this.Ar)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==t?void 0:t.document.documentElement;for(var e of x(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector]){var r=null==t?void 0:t.document.querySelector(e);if(r)return r}}scrollY(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return t&&(t.scrollY||t.pageYOffset||t.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return t&&(t.scrollX||t.pageXOffset||t.document.documentElement.scrollLeft)||0}}var sf=e=>nq(null==e?void 0:e.config.mask_personal_data_properties,null==e?void 0:e.config.custom_personal_data_properties);class sp{constructor(e,t,r,i){this.Lr=e=>{var t=this.jr();if(!t||t.sessionId!==e){var r={sessionId:e,props:this.Nr(this._instance)};this.zr.register({[eL]:r})}},this._instance=e,this.Ur=t,this.zr=r,this.Nr=i||sf,this.Ur.onSessionId(this.Lr)}jr(){return this.zr.props[eL]}getSetOnceProps(){var e,t=null==(e=this.jr())?void 0:e.props;return t?"r"in t?nW(t):{$referring_domain:t.referringDomain,$pathname:t.initialPathName,utm_source:t.utm_source,utm_campaign:t.utm_campaign,utm_medium:t.utm_medium,utm_content:t.utm_content,utm_term:t.utm_term}:{}}getSessionProps(){var e={};return V(ee(this.getSetOnceProps()),(t,r)=>{"$current_url"===r&&(r="url"),e["$session_entry_"+E(r)]=t}),e}}var s_=B("[SessionId]");class sg{constructor(e,t,r){if(this.qr=[],this.Br=(e,t)=>Math.abs(e-t)>this.sessionTimeoutMs,!e.persistence)throw Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this.S=e.config,this.zr=e.persistence,this.fi=void 0,this.Ct=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.Hr=t||tE,this.Wr=r||tE;var i,n=this.S.persistence_name||this.S.token,s=this.S.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*tU(s,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Gr(),this.Jr="ph_"+n+"_window_id",this.Vr="ph_"+n+"_primary_window_exists",this.Kr()){var a=tj.L(this.Jr),o=tj.L(this.Vr);a&&!o?this.fi=a:tj.N(this.Jr),tj.j(this.Vr,!0)}if(null!=(i=this.S.bootstrap)&&i.sessionID)try{var l=(e=>{var t=e.replace(/-/g,"");if(32!==t.length)throw Error("Not a valid UUID");if("7"!==t[12])throw Error("Not a UUIDv7");return parseInt(t.substring(0,12),16)})(this.S.bootstrap.sessionID);this.Yr(this.S.bootstrap.sessionID,(new Date).getTime(),l)}catch(e){s_.error("Invalid sessionID in bootstrap",e)}this.Xr()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return I(this.qr)&&(this.qr=[]),this.qr.push(e),this.Ct&&e(this.Ct,this.fi),()=>{this.qr=this.qr.filter(t=>t!==e)}}Kr(){return"memory"!==this.S.persistence&&!this.zr.Ae&&tj.O()}Qr(e){e!==this.fi&&(this.fi=e,this.Kr()&&tj.j(this.Jr,e))}Zr(){return this.fi?this.fi:this.Kr()?tj.L(this.Jr):null}Yr(e,t,r){e===this.Ct&&t===this._sessionActivityTimestamp&&r===this._sessionStartTimestamp||(this._sessionStartTimestamp=r,this._sessionActivityTimestamp=t,this.Ct=e,this.zr.register({[eS]:[t,e,r]}))}ts(){if(this.Ct&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.Ct,this._sessionStartTimestamp];var e=this.zr.props[eS];return x(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this.Yr(null,null,null)}Xr(){ei(t,"beforeunload",()=>{this.Kr()&&tj.N(this.Vr)},{capture:!1})}checkAndGetSessionAndWindowId(e,t){if(void 0===e&&(e=!1),void 0===t&&(t=null),this.S.__preview_experimental_cookieless_mode)throw Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var r=t||(new Date).getTime(),[i,n,s]=this.ts(),a=this.Zr(),o=N(s)&&s>0&&Math.abs(r-s)>864e5,l=!1,u=!n,c=!e&&this.Br(r,i);u||c||o?(n=this.Hr(),a=this.Wr(),s_.info("new session ID generated",{sessionId:n,windowId:a,changeReason:{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:o}}),s=r,l=!0):a||(a=this.Wr(),l=!0);var d=0===i||!e||o?r:i,h=0===s?(new Date).getTime():s;return this.Qr(a),this.Yr(n,d,h),e||this.Gr(),l&&this.qr.forEach(e=>e(n,a,l?{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:o}:void 0)),{sessionId:n,windowId:a,sessionStartTimestamp:h,changeReason:l?{noSessionId:u,activityTimeout:c,sessionPastMaximumLength:o}:void 0,lastActivityTimestamp:i}}Gr(){clearTimeout(this.es),this.es=setTimeout(()=>{var[e]=this.ts();this.Br((new Date).getTime(),e)&&this.resetSessionId()},1.1*this.sessionTimeoutMs)}}var sv=["$set_once","$set"],sm=B("[SiteApps]");class sy{constructor(e){this._instance=e,this.rs=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}ss(e,t){if(t){var r=this.globalsForEvent(t);this.rs.push(r),this.rs.length>1e3&&(this.rs=this.rs.slice(10))}}get siteAppLoaders(){var e;return null==(e=f._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.siteApps}init(){if(this.isEnabled){var e=this._instance.Ve(this.ss.bind(this));this.ns=()=>{e(),this.rs=[],this.ns=void 0}}}globalsForEvent(e){if(!e)throw Error("Event payload is required");var t,r,i,n,s,a,o,l={},u=this._instance.get_property("$groups")||[];for(var[c,d]of Object.entries(this._instance.get_property("$stored_group_properties")||{}))l[c]={id:u[c],type:c,properties:d};var{$set_once:h,$set:f}=e;return{event:W({},G(e,sv),{properties:W({},e.properties,f?{$set:W({},null!=(t=null==(r=e.properties)?void 0:r.$set)?t:{},f)}:{},h?{$set_once:W({},null!=(i=null==(n=e.properties)?void 0:n.$set_once)?i:{},h)}:{}),elements_chain:null!=(s=null==(a=e.properties)?void 0:a.$elements_chain)?s:"",distinct_id:null==(o=e.properties)?void 0:o.distinct_id}),person:{properties:this._instance.get_property("$stored_person_properties")},groups:l}}setupSiteApp(e){var t=this.apps[e.id],r=()=>{var r;!t.errored&&this.rs.length&&(sm.info("Processing "+this.rs.length+" events for site app with id "+e.id),this.rs.forEach(e=>null==t.processEvent?void 0:t.processEvent(e)),t.processedBuffer=!0),Object.values(this.apps).every(e=>e.processedBuffer||e.errored)&&(null==(r=this.ns)||r.call(this))},i=!1,n=n=>{t.errored=!n,t.loaded=!0,sm.info("Site app with id "+e.id+" "+(n?"loaded":"errored")),i&&r()};try{var{processEvent:s}=e.init({posthog:this._instance,callback:e=>{n(e)}});s&&(t.processEvent=s),i=!0}catch(t){sm.error("Error while initializing PostHog app with config id "+e.id,t),n(!1)}if(i&&t.loaded)try{r()}catch(r){sm.error("Error while processing buffered events PostHog app with config id "+e.id,r),t.errored=!0}}os(){var e=this.siteAppLoaders||[];for(var t of e)this.apps[t.id]={id:t.id,loaded:!1,errored:!1,processedBuffer:!1};for(var r of e)this.setupSiteApp(r)}ls(e){if(0!==Object.keys(this.apps).length){var t=this.globalsForEvent(e);for(var r of Object.values(this.apps))try{null==r.processEvent||r.processEvent(t)}catch(t){sm.error("Error while processing event "+e.event+" for site app "+r.id,t)}}}onRemoteConfig(e){var t,r,i,n=this;if(null!=(t=this.siteAppLoaders)&&t.length)return this.isEnabled?(this.os(),void this._instance.on("eventCaptured",e=>this.ls(e))):void sm.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');if(null==(r=this.ns)||r.call(this),null!=(i=e.siteApps)&&i.length)if(this.isEnabled){var s=function(e){var t;f["__$$ph_site_app_"+e]=n._instance,null==(t=f.__PosthogExtensions__)||null==t.loadSiteApp||t.loadSiteApp(n._instance,o,t=>{if(t)return sm.error("Error while initializing PostHog app with config id "+e,t)})};for(var{id:a,url:o}of e.siteApps)s(a)}else sm.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}var sb=["amazonbot","amazonproductbot","app.hypefactors.com","applebot","archive.org_bot","awariobot","backlinksextendedbot","baiduspider","bingbot","bingpreview","chrome-lighthouse","dataforseobot","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","leikibot","linkedinbot","meta-externalagent","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","sebot-wa","sitebulb","slackbot","slurp","trendictionbot","turnitin","twitterbot","vercel-screenshot","vercelbot","yahoo! slurp","yandexbot","zoombot","bot.htm","bot.php","(bot;","bot/","crawler","ahrefsbot","ahrefssiteaudit","semrushbot","siteauditbot","splitsignalbot","gptbot","oai-searchbot","chatgpt-user","perplexitybot","better uptime bot","sentryuptimebot","uptimerobot","headlesschrome","cypress","google-hoteladsverifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleother","google-cloudvertexbot","googleweblight","mediapartners-google","storebot-google","google-inspectiontool","bytespider"],sE=function(e,t){if(!e)return!1;var r=e.toLowerCase();return sb.concat(t||[]).some(e=>{var t=e.toLowerCase();return -1!==r.indexOf(t)})},sP=function(e,t){if(!e)return!1;var r=e.userAgent;if(r&&sE(r,t))return!0;try{var i=null==e?void 0:e.userAgentData;if(null!=i&&i.brands&&i.brands.some(e=>sE(null==e?void 0:e.brand,t)))return!0}catch(e){}return!!e.webdriver},sS=function(e){return e.US="us",e.EU="eu",e.CUSTOM="custom",e}({}),sw="i.posthog.com";class sR{constructor(e){this.us={},this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===e?"https://us.i.posthog.com":e}get uiHost(){var e,t=null==(e=this.instance.config.ui_host)?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace("."+sw,".posthog.com")),"https://app.posthog.com"===t?"https://us.posthog.com":t}get region(){return this.us[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.us[this.apiHost]=sS.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.us[this.apiHost]=sS.EU:this.us[this.apiHost]=sS.CUSTOM),this.us[this.apiHost]}endpointFor(e,t){if(void 0===t&&(t=""),t&&(t="/"===t[0]?t:"/"+t),"ui"===e)return this.uiHost+t;if(this.region===sS.CUSTOM)return this.apiHost+t;var r=sw+t;switch(e){case"assets":return"https://"+this.region+"-assets."+r;case"api":return"https://"+this.region+"."+r}}}var sx={icontains:(e,r)=>!!t&&r.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:(e,r)=>!!t&&-1===r.href.toLowerCase().indexOf(e.toLowerCase()),regex:(e,r)=>!!t&&iH(r.href,e),not_regex:(e,r)=>!!t&&!iH(r.href,e),exact:(e,t)=>t.href===e,is_not:(e,t)=>t.href!==e};class sO{constructor(e){var t=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(e){void 0===e&&(e=!1),t.getWebExperiments(e=>{sO.hs("retrieved web experiments from the server"),t.ds=new Map,e.forEach(e=>{if(e.feature_flag_key){t.ds&&(sO.hs("setting flag key ",e.feature_flag_key," to web experiment ",e),null==(r=t.ds)||r.set(e.feature_flag_key,e));var r,i=t._instance.getFeatureFlag(e.feature_flag_key);A(i)&&e.variants[i]&&t.vs(e.name,i,e.variants[i].transforms)}else if(e.variants)for(var n in e.variants){var s=e.variants[n];sO.cs(s)&&t.vs(e.name,n,s.transforms)}})},e)},this._instance=e,this._instance.onFeatureFlags(e=>{this.onFeatureFlags(e)})}onFeatureFlags(e){if(this._is_bot())sO.hs("Refusing to render web experiment since the viewer is a likely bot");else if(!this._instance.config.disable_web_experiments){if(M(this.ds))return this.ds=new Map,this.loadIfEnabled(),void this.previewWebExperiment();sO.hs("applying feature flags",e),e.forEach(e=>{var t;if(this.ds&&null!=(t=this.ds)&&t.has(e)){var r,i=this._instance.getFeatureFlag(e),n=null==(r=this.ds)?void 0:r.get(e);i&&null!=n&&n.variants[i]&&this.vs(n.name,i,n.variants[i].transforms)}})}}previewWebExperiment(){var e=sO.getWindowLocation();if(null!=e&&e.search){var t=tu(null==e?void 0:e.search,"__experiment_id"),r=tu(null==e?void 0:e.search,"__experiment_variant");t&&r&&(sO.hs("previewing web experiments "+t+" && "+r),this.getWebExperiments(e=>{this.fs(parseInt(t),r,e)},!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,r){if(this._instance.config.disable_web_experiments&&!r)return e([]);var i=this._instance.get_property("$web_experiments");if(i&&!t)return e(i);this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/web_experiments/?token="+this._instance.config.token),method:"GET",callback:t=>200===t.statusCode&&t.json?e(t.json.experiments||[]):e([])})}fs(e,t,r){var i=r.filter(t=>t.id===e);i&&i.length>0&&(sO.hs("Previewing web experiment ["+i[0].name+"] with variant ["+t+"]"),this.vs(i[0].name,t,i[0].variants[t].transforms))}static cs(e){return!M(e.conditions)&&sO.ps(e)&&sO._s(e)}static ps(e){if(M(e.conditions)||M(null==(t=e.conditions)?void 0:t.url))return!0;var t,r,i,n,s=sO.getWindowLocation();return!!s&&(null==(r=e.conditions)||!r.url||sx[null!=(i=null==(n=e.conditions)?void 0:n.urlMatchType)?i:"icontains"](e.conditions.url,s))}static getWindowLocation(){return null==t?void 0:t.location}static _s(e){if(M(e.conditions)||M(null==(r=e.conditions)?void 0:r.utm))return!0;var t=nD();if(t.utm_source){var r,i,n,s,a,o,l,u,c,d=null==(i=e.conditions)||null==(i=i.utm)||!i.utm_campaign||(null==(n=e.conditions)||null==(n=n.utm)?void 0:n.utm_campaign)==t.utm_campaign,h=null==(s=e.conditions)||null==(s=s.utm)||!s.utm_source||(null==(a=e.conditions)||null==(a=a.utm)?void 0:a.utm_source)==t.utm_source,f=null==(o=e.conditions)||null==(o=o.utm)||!o.utm_medium||(null==(l=e.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==t.utm_medium,p=null==(u=e.conditions)||null==(u=u.utm)||!u.utm_term||(null==(c=e.conditions)||null==(c=c.utm)?void 0:c.utm_term)==t.utm_term;return d&&f&&p&&h}return!1}static hs(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];U.info("[WebExperiments] "+e,r)}vs(e,t,r){this._is_bot()?sO.hs("Refusing to render web experiment since the viewer is a likely bot"):"control"!==t?r.forEach(r=>{if(r.selector){sO.hs("applying transform of variant "+t+" for experiment "+e+" ",r);var i,n=null==(i=document)?void 0:i.querySelectorAll(r.selector);null==n||n.forEach(e=>{r.html&&(e.innerHTML=r.html),r.css&&e.setAttribute("style",r.css)})}}):sO.hs("Control variants leave the page unmodified.")}_is_bot(){return a&&this._instance?sP(a,this._instance.config.custom_blocked_useragents):void 0}}var sT=B("[PostHog ExternalIntegrations]"),sC={intercom:"intercom-integration",crispChat:"crisp-chat-integration"};class sI{constructor(e){this._instance=e}J(e,t){var r;null==(r=f.__PosthogExtensions__)||null==r.loadExternalDependency||r.loadExternalDependency(this._instance,e,e=>{if(e)return sT.error("failed to load script",e);t()})}startIfEnabledOrStop(){var e,t=this,r=function(e){var r,i,s;!n||null!=(r=f.__PosthogExtensions__)&&null!=(r=r.integrations)&&r[e]||t.J(sC[e],()=>{var r;null==(r=f.__PosthogExtensions__)||null==(r=r.integrations)||null==(r=r[e])||r.start(t._instance)}),!n&&null!=(i=f.__PosthogExtensions__)&&null!=(i=i.integrations)&&i[e]&&(null==(s=f.__PosthogExtensions__)||null==(s=s.integrations)||null==(s=s[e])||s.stop())};for(var[i,n]of Object.entries(null!=(e=this._instance.config.integrations)?e:{}))r(i)}}var sA={},sk=()=>{},sj="posthog",sM=!iL&&-1===(null==h?void 0:h.indexOf("MSIE"))&&-1===(null==h?void 0:h.indexOf("Mozilla")),sN=e=>{var r;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:function(e){var t=null==e?void 0:e.hostname;if(!A(t))return!1;var r=t.split(".").slice(-2).join(".");for(var i of et)if(r===i)return!1;return!0}(null==o?void 0:o.location),persistence:"localStorage+cookie",persistence_name:"",loaded:sk,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:"2025-05-24"!==e||"history_change",capture_pageleave:"if_capture_pageview",defaults:null!=e?e:"unset",debug:l&&A(null==l?void 0:l.search)&&-1!==l.search.indexOf("__posthog_debug=true")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_surveys_automatic_display:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==t||null==(r=t.location)?void 0:r.protocol),ip:!1,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_flags:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_enable_surveys:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:e=>{var t="Bad HTTP status: "+e.statusCode+" "+e.text;U.error(t)},get_device_id:e=>e,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:3e3},error_tracking:{},_onCapture:sk}},sL=e=>{var t={};I(e.process_person)||(t.person_profiles=e.process_person),I(e.xhr_headers)||(t.request_headers=e.xhr_headers),I(e.cookie_name)||(t.persistence_name=e.cookie_name),I(e.disable_cookie)||(t.disable_persistence=e.disable_cookie),I(e.store_google)||(t.save_campaign_params=e.store_google),I(e.verbose)||(t.debug=e.verbose);var r=Y({},t,e);return x(e.property_blacklist)&&(I(e.property_denylist)?r.property_denylist=e.property_blacklist:x(e.property_denylist)?r.property_denylist=[...e.property_blacklist,...e.property_denylist]:U.error("Invalid value for property_denylist config: "+e.property_denylist)),r};class sF{constructor(){this.__forceAllowLocalhost=!1}get gs(){return this.__forceAllowLocalhost}set gs(e){U.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}class sD{get decideEndpointWasHit(){var e,t;return null!=(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&e}get flagsEndpointWasHit(){var e,t;return null!=(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&e}constructor(){this.webPerformance=new sF,this.bs=!1,this.version=p.LIB_VERSION,this.ys=new n4,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=sN(),this.SentryIntegration=iE,this.sentryIntegration=e=>(function(e,t){var r=ib(e,t);return{name:iy,processEvent:e=>r(e)}})(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this.ws=!1,this.Ss=null,this.$s=null,this.xs=null,this.featureFlags=new n1(this),this.toolbar=new ix(this),this.scrollManager=new sh(this),this.pageViewManager=new iM(this),this.surveys=new sn(this),this.experiments=new sO(this),this.exceptions=new iX(this),this.rateLimiter=new sa(this),this.requestRouter=new sR(this),this.consent=new tN(this),this.externalIntegrations=new sI(this),this.people={set:(e,t,r)=>{var i=A(e)?{[e]:t}:e;this.setPersonProperties(i),null==r||r({})},set_once:(e,t,r)=>{var i=A(e)?{[e]:t}:e;this.setPersonProperties(void 0,i),null==r||r({})}},this.on("eventCaptured",e=>U.info('send "'+(null==e?void 0:e.event)+'"',e))}init(e,t,r){if(r&&r!==sj){var i,n=null!=(i=sA[r])?i:new sD;return n._init(e,t,r),sA[r]=n,sA[sj][r]=n,n}return this._init(e,t,r)}_init(e,r,i){if(void 0===r&&(r={}),I(e)||k(e))return U.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return U.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this.ks=r,this.Es=[],r.person_profiles&&(this.$s=r.person_profiles),this.set_config(Y({},sN(r.defaults),sL(r),{name:i,token:e})),this.config.on_xhr_error&&U.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=r.disable_compression?void 0:v.GZipJS;var n=this.Is();this.persistence=new n3(this.config,n),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new n3(W({},this.config,{persistence:"sessionStorage"}),n);var s=W({},this.persistence.props),a=W({},this.sessionPersistence.props);if(this.register({$initialization_time:(new Date).toISOString()}),this.Ps=new su(e=>this.Rs(e),this.config.request_queue_config),this.Ts=new sd(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new sg(this),this.sessionPropsManager=new sp(this,this.sessionManager,this.persistence)),new iT(this).startIfEnabledOrStop(),this.siteApps=new sy(this),null==(o=this.siteApps)||o.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new iv(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new tp(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new ij(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new iI(this),this.exceptionObserver=new tq(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new t$(this,tD),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new ri(this),this.historyAutocapture.startIfEnabled(),p.DEBUG=p.DEBUG||this.config.debug,p.DEBUG&&U.info("Starting in debug mode",{this:this,config:r,thisC:W({},this.config),p:s,s:a}),void 0!==(null==(l=r.bootstrap)?void 0:l.distinctID)){var o,l,u,c,d=this.config.get_device_id(tE()),h=null!=(u=r.bootstrap)&&u.isIdentifiedID?d:r.bootstrap.distinctID;this.persistence.set_property(eN,null!=(c=r.bootstrap)&&c.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:r.bootstrap.distinctID,$device_id:h})}if(this.Ms()){var f,_,g=Object.keys((null==(f=r.bootstrap)?void 0:f.featureFlags)||{}).filter(e=>{var t;return!(null==(t=r.bootstrap)||null==(t=t.featureFlags)||!t[e])}).reduce((e,t)=>{var i;return e[t]=(null==(i=r.bootstrap)||null==(i=i.featureFlags)?void 0:i[t])||!1,e},{}),m=Object.keys((null==(_=r.bootstrap)?void 0:_.featureFlagPayloads)||{}).filter(e=>g[e]).reduce((e,t)=>{var i,n;return null!=(i=r.bootstrap)&&null!=(i=i.featureFlagPayloads)&&i[t]&&(e[t]=null==(n=r.bootstrap)||null==(n=n.featureFlagPayloads)?void 0:n[t]),e},{});this.featureFlags.receivedFeatureFlags({featureFlags:g,featureFlagPayloads:m})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:eq,$device_id:null},"");else if(!this.get_distinct_id()){var y=this.config.get_device_id(tE());this.register_once({distinct_id:y,$device_id:y},""),this.persistence.set_property(eN,"anonymous")}return ei(t,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),r.segment?function(e,t){var r=e.config.segment;if(!r)return t();!function(e,t){var r=e.config.segment;if(!r)return t();var i=r=>{var i=()=>r.anonymousId()||tE();e.config.get_device_id=i,r.id()&&(e.register({distinct_id:r.id(),$device_id:i()}),e.persistence.set_property(eN,"identified")),t()},n=r.user();"then"in n&&O(n.then)?n.then(e=>i(e)):i(n)}(e,()=>{var i;r.register((Promise&&Promise.resolve||im.warn("This browser does not have Promise support, and can not use the segment integration"),i=(t,r)=>{if(!r)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||(im.info("No userId set, resetting PostHog"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&(im.info("UserId set, identifying with PostHog"),e.identify(t.event.userId));var i=e.calculateEventProperties(r,t.event.properties);return t.event.properties=Object.assign({},i,t.event.properties),t},{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:e=>i(e,e.event.event),page:e=>i(e,"$pageview"),identify:e=>i(e,"$identify"),screen:e=>i(e,"$screen")})).then(()=>{t()})})}(this,()=>this.Cs()):this.Cs(),O(this.config._onCapture)&&this.config._onCapture!==sk&&(U.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",e=>this.config._onCapture(e.event,e))),this.config.ip&&U.warn('The `ip` config option has NO EFFECT AT ALL and has been deprecated. Use a custom transformation or "Discard IP data" project setting instead. See https://posthog.com/tutorials/web-redact-properties#hiding-customer-ip-address for more information.'),this}Re(e){var t,r,i,n,s,a,l,u;if(!o||!o.body)return U.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout(()=>{this.Re(e)},500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=y(e.supportedCompression,v.GZipJS)?v.GZipJS:y(e.supportedCompression,v.Base64)?v.Base64:void 0),null!=(t=e.analytics)&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this.$s?this.$s:"identified_only"}),null==(r=this.siteApps)||r.onRemoteConfig(e),null==(i=this.sessionRecording)||i.onRemoteConfig(e),null==(n=this.autocapture)||n.onRemoteConfig(e),null==(s=this.heatmaps)||s.onRemoteConfig(e),this.surveys.onRemoteConfig(e),null==(a=this.webVitalsAutocapture)||a.onRemoteConfig(e),null==(l=this.exceptionObserver)||l.onRemoteConfig(e),this.exceptions.onRemoteConfig(e),null==(u=this.deadClicksAutocapture)||u.onRemoteConfig(e)}Cs(){try{this.config.loaded(this)}catch(e){U.critical("`loaded` function failed",e)}this.Fs(),this.config.capture_pageview&&setTimeout(()=>{this.consent.isOptedIn()&&this.Os()},1),new sl(this).load(),this.featureFlags.flags()}Fs(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null==(e=this.Ps)||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||X(this.__request_queue,e=>this.Rs(e)),this.__request_queue=[],this.Fs()}_handle_unload(){var e,t;this.config.request_batching?(this.As()&&this.capture("$pageleave"),null==(e=this.Ps)||e.unload(),null==(t=this.Ts)||t.unload()):this.As()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}Pe(e){this.__loaded&&(sM?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=iD(e.url,{ip:+!!this.config.ip}),e.headers=W({},this.config.request_headers),e.compression="best-available"===e.compression?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(e=>{var t,r,i,n=W({},e);n.timeout=n.timeout||6e4,n.url=iD(n.url,{_:(new Date).getTime().toString(),ver:p.LIB_VERSION,compression:n.compression});var s=null!=(t=n.transport)?t:"fetch",a=null!=(r=null==(i=er(iB,e=>e.transport===s))?void 0:i.method)?r:iB[0].method;if(!a)throw Error("No available transport method");a(n)})(W({},e,{callback:t=>{var r,i;this.rateLimiter.checkForLimiting(t),t.statusCode>=400&&(null==(r=(i=this.config).on_request_error)||r.call(i,t)),null==e.callback||e.callback(t)}}))))}Rs(e){this.Ts?this.Ts.retriableRequest(e):this.Pe(e)}_execute_array(e){var t,r=[],i=[],n=[];X(e,e=>{e&&(x(t=e[0])?n.push(e):O(e)?e.call(this):x(e)&&"alias"===t?r.push(e):x(e)&&-1!==t.indexOf("capture")&&O(this[t])?n.push(e):i.push(e))});var s=function(e,t){X(e,function(e){if(x(e[0])){var r=t;V(e,function(e){r=r[e[0]].apply(r,e.slice(1))})}else this[e[0]].apply(this,e.slice(1))},t)};s(r,this),s(i,this),s(n,this)}Ms(){var e,t;return(null==(e=this.config.bootstrap)?void 0:e.featureFlags)&&Object.keys(null==(t=this.config.bootstrap)?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,r){var i;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.Ps){if(!this.consent.isOptedOut())if(!I(e)&&A(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var n=null!=r&&r.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==n||!n.isRateLimited){null!=t&&t.$current_url&&!A(null==t?void 0:t.$current_url)&&(U.error("Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value."),null==t||delete t.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var s,a,o,l,u=new Date,c=(null==r?void 0:r.timestamp)||u,d=tE(),h={uuid:d,event:e,properties:this.calculateEventProperties(e,t||{},c,d)};n&&(h.properties.$lib_rate_limit_remaining_tokens=n.remainingTokens),(null==r?void 0:r.$set)&&(h.$set=null==r?void 0:r.$set);var f,p,_=this.Ds(null==r?void 0:r.$set_once);if(_&&(h.$set_once=_),(s=h,a=null!=r&&r._noTruncate?null:this.config.properties_string_max_length,o=e=>A(e)&&!j(a)?e.slice(0,a):e,l=new Set,h=function e(t,r){var i;return t!==Object(t)?o?o(t,r):t:l.has(t)?void 0:(l.add(t),x(t)?(i=[],X(t,t=>{i.push(e(t))})):(i={},V(t,(t,r)=>{l.has(t)||(i[r]=e(t,r))})),i)}(s)).timestamp=c,I(null==r?void 0:r.timestamp)||(h.properties.$event_time_override_provided=!0,h.properties.$event_time_override_system_time=u),e===n8.DISMISSED||e===n8.SENT){var g=null==t?void 0:t[n9.SURVEY_ID],v=null==t?void 0:t[n9.SURVEY_ITERATION];localStorage.setItem((p=""+se+(f={id:g,current_iteration:v}).id,f.current_iteration&&f.current_iteration>0&&(p=""+se+f.id+"_"+f.current_iteration),p),"true"),h.$set=W({},h.$set,{[st({id:g,current_iteration:v},e===n8.SENT?"responded":"dismissed")]:!0})}var m=W({},h.properties.$set,h.$set);if(C(m)||this.setPersonPropertiesForFlags(m),!M(this.config.before_send)){var y=this.Ls(h);if(!y)return;h=y}this.ys.emit("eventCaptured",h);var b={method:"POST",url:null!=(i=null==r?void 0:r._url)?i:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:h,compression:"best-available",batchKey:null==r?void 0:r._batchKey};return!this.config.request_batching||r&&(null==r||!r._batchKey)||null!=r&&r.send_instantly?this.Rs(b):this.Ps.enqueue(b),h}U.critical("This capture call is ignored due to client rate limiting.")}}else U.error("No event name provided to posthog.capture")}else U.uninitializedWarning("posthog.capture")}Ve(e){return this.on("eventCaptured",t=>e(t.event,t))}calculateEventProperties(e,r,i,n,s){if(i=i||new Date,!this.persistence||!this.sessionPersistence)return r;var a,u=s?void 0:this.persistence.remove_event_timer(e),c=W({},r);if(c.token=this.config.token,c.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(c.$cookieless_mode=!0),"$snapshot"===e){var d=W({},this.persistence.properties(),this.sessionPersistence.properties());return c.distinct_id=d.distinct_id,(!A(c.distinct_id)&&!N(c.distinct_id)||k(c.distinct_id))&&U.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),c}var f,_=function(e,r){if(!h)return{};var i,n,s=e?K([],nM,r||[]):[],[a,o]=function(e){for(var t=0;t<nA.length;t++){var[r,i]=nA[t],n=r.exec(e),s=n&&(O(i)?i(n,e):i);if(s)return s}return["",""]}(h);return Y(ee({$os:a,$os_version:o,$browser:nT(h,navigator.vendor),$device:nk(h),$device_type:(n=nk(h))===iZ||n===iJ||"Kobo"===n||"Kindle Fire"===n||n===nm?iK:n===no||n===nu||n===nl||n===n_?"Console":n===i0?"Wearable":n?iV:"Desktop",$timezone:nG(),$timezone_offset:function(){try{return(new Date).getTimezoneOffset()}catch(e){return}}()}),{$current_url:tc(null==l?void 0:l.href,s,nL),$host:null==l?void 0:l.host,$pathname:null==l?void 0:l.pathname,$raw_user_agent:h.length>1e3?h.substring(0,997)+"...":h,$browser_version:nI(h,navigator.vendor),$browser_language:nB(),$browser_language_prefix:"string"==typeof(i=nB())?i.split("-")[0]:void 0,$screen_height:null==t?void 0:t.screen.height,$screen_width:null==t?void 0:t.screen.width,$viewport_height:null==t?void 0:t.innerHeight,$viewport_width:null==t?void 0:t.innerWidth,$lib:"web",$lib_version:p.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:g,windowId:v}=this.sessionManager.checkAndGetSessionAndWindowId(s,i.getTime());c.$session_id=g,c.$window_id=v}this.sessionPropsManager&&Y(c,this.sessionPropsManager.getSessionProps());try{this.sessionRecording&&Y(c,this.sessionRecording.sdkDebugProperties),c.$sdk_debug_retry_queue_size=null==(a=this.Ts)?void 0:a.length}catch(e){c.$sdk_debug_error_capturing_properties=String(e)}if(this.requestRouter.region===sS.CUSTOM&&(c.$lib_custom_api_host=this.config.api_host),f="$pageview"!==e||s?"$pageleave"!==e||s?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(i):this.pageViewManager.doPageView(i,n),c=Y(c,f),"$pageview"===e&&o&&(c.title=o.title),!I(u)){var m=i.getTime()-u;c.$duration=parseFloat((m/1e3).toFixed(3))}h&&this.config.opt_out_useragent_filter&&(c.$browser_type=this._is_bot()?"bot":"browser"),(c=Y({},_,this.persistence.properties(),this.sessionPersistence.properties(),c)).$is_identified=this._isIdentified(),x(this.config.property_denylist)?V(this.config.property_denylist,function(e){delete c[e]}):U.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var y=this.config.sanitize_properties;y&&(U.error("sanitize_properties is deprecated. Use before_send instead"),c=y(c,e));var b=this.js();return c.$process_person_profile=b,b&&!s&&this.Ns("_calculate_event_properties"),c}Ds(e){if(!this.persistence||!this.js()||this.bs)return e;var t,r=Y({},this.persistence.get_initial_props(),(null==(t=this.sessionPropsManager)?void 0:t.getSetOnceProps())||{},e||{}),i=this.config.sanitize_properties;return i&&(U.error("sanitize_properties is deprecated. Use before_send instead"),r=i(r,"$set_once")),this.bs=!0,C(r)?void 0:r}register(e,t){var r;null==(r=this.persistence)||r.register(e,t)}register_once(e,t,r){var i;null==(i=this.persistence)||i.register_once(e,t,r)}register_for_session(e){var t;null==(t=this.sessionPersistence)||t.register(e)}unregister(e){var t;null==(t=this.persistence)||t.unregister(e)}unregister_for_session(e){var t;null==(t=this.sessionPersistence)||t.unregister(e)}zs(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t,r){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t,r)}getEarlyAccessFeatures(e,t,r){return void 0===t&&(t=!1),this.featureFlags.getEarlyAccessFeatures(e,t,r)}on(e,t){return this.ys.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSurveysLoaded(e){return this.surveys.onSurveysLoaded(e)}onSessionId(e){var t,r;return null!=(t=null==(r=this.sessionManager)?void 0:r.onSessionId(e))?t:()=>{}}getSurveys(e,t){void 0===t&&(t=!1),this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e,t){void 0===t&&(t=!1),this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){return this.surveys.canRenderSurvey(e)}canRenderSurveyAsync(e,t){return void 0===t&&(t=!1),this.surveys.canRenderSurveyAsync(e,t)}identify(e,t,r){if(!this.__loaded||!this.persistence)return U.uninitializedWarning("posthog.identify");if(N(e)&&(e=e.toString(),U.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e)if(["distinct_id","distinctid"].includes(e.toLowerCase()))U.critical('The string "'+e+'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(e!==eq){if(this.Ns("posthog.identify")){var i=this.get_distinct_id();this.register({$user_id:e}),this.get_property("$device_id")||this.register_once({$had_persisted_distinct_id:!0,$device_id:i},""),e!==i&&e!==this.get_property(es)&&(this.unregister(es),this.register({distinct_id:e}));var n="anonymous"===(this.persistence.get_property(eN)||"anonymous");e!==i&&n?(this.persistence.set_property(eN,"identified"),this.setPersonPropertiesForFlags(W({},r||{},t||{}),!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:i},{$set:t||{},$set_once:r||{}}),this.xs=iq(e,t,r),this.featureFlags.setAnonymousDistinctId(i)):(t||r)&&this.setPersonProperties(t,r),e!==i&&(this.reloadFeatureFlags(),this.unregister(eM))}}else U.critical('The string "'+eq+'" was set in posthog.identify which indicates an error. This ID is only used as a sentinel value.');else U.error("Unique user id has not been set in posthog.identify")}setPersonProperties(e,t){if((e||t)&&this.Ns("posthog.setPersonProperties")){var r=iq(this.get_distinct_id(),e,t);this.xs!==r?(this.setPersonPropertiesForFlags(W({},t||{},e||{})),this.capture("$set",{$set:e||{},$set_once:t||{}}),this.xs=r):U.info("A duplicate setPersonProperties call was made with the same properties. It has been ignored.")}}group(e,t,r){if(e&&t){if(this.Ns("posthog.group")){var i=this.getGroups();i[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:W({},i,{[e]:t})}),r&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:r}),this.setGroupPropertiesForFlags({[e]:r})),i[e]===t||r||this.reloadFeatureFlags()}}else U.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0),this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0),this.Ns("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){if(U.info("reset"),!this.__loaded)return U.uninitializedWarning("posthog.reset");var t,r,i,n,s=this.get_property("$device_id");if(this.consent.reset(),null==(t=this.persistence)||t.clear(),null==(r=this.sessionPersistence)||r.clear(),this.surveys.reset(),this.featureFlags.reset(),null==(i=this.persistence)||i.set_property(eN,"anonymous"),null==(n=this.sessionManager)||n.resetSessionId(),this.xs=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:eq,$device_id:null},"");else{var a=this.config.get_device_id(tE());this.register_once({distinct_id:a,$device_id:e?a:s},"")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var e,t;return null!=(e=null==(t=this.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)?e:""}get_session_replay_url(e){if(!this.sessionManager)return"";var{sessionId:t,sessionStartTimestamp:r}=this.sessionManager.checkAndGetSessionAndWindowId(!0),i=this.requestRouter.endpointFor("ui","/project/"+this.config.token+"/replay/"+t);if(null!=e&&e.withTimestamp&&r){var n,s=null!=(n=e.timestampLookBack)?n:10;if(!r)return i;i+="?t="+Math.max(Math.floor(((new Date).getTime()-r)/1e3)-s,0)}return i}alias(e,t){return e===this.get_property(en)?(U.critical("Attempting to create alias for existing People user - aborting."),-2):this.Ns("posthog.alias")?(I(t)&&(t=this.get_distinct_id()),e!==t?(this.zs(es,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(U.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}set_config(e){var t=W({},this.config);if(T(e)){Y(this.config,sL(e));var r,i,n,s,a,o=this.Is();null==(r=this.persistence)||r.update_config(this.config,t,o),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new n3(W({},this.config,{persistence:"sessionStorage"}),o),tO.O()&&"true"===tO.D("ph_debug")&&(this.config.debug=!0),this.config.debug&&(p.DEBUG=!0,U.info("set_config",{config:e,oldConfig:t,newConfig:W({},this.config)})),null==(i=this.sessionRecording)||i.startIfEnabledOrStop(),null==(n=this.autocapture)||n.startIfEnabled(),null==(s=this.heatmaps)||s.startIfEnabled(),this.surveys.loadIfEnabled(),this.Us(),null==(a=this.externalIntegrations)||a.startIfEnabledOrStop()}}startSessionRecording(e){var t,r,i,n,s,a=!0===e,o={sampling:a||!(null==e||!e.sampling),linked_flag:a||!(null==e||!e.linked_flag),url_trigger:a||!(null==e||!e.url_trigger),event_trigger:a||!(null==e||!e.event_trigger)};Object.values(o).some(Boolean)&&(null==(t=this.sessionManager)||t.checkAndGetSessionAndWindowId(),o.sampling&&(null==(r=this.sessionRecording)||r.overrideSampling()),o.linked_flag&&(null==(i=this.sessionRecording)||i.overrideLinkedFlag()),o.url_trigger&&(null==(n=this.sessionRecording)||n.overrideTrigger("url")),o.event_trigger&&(null==(s=this.sessionRecording)||s.overrideTrigger("event"))),this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!(null==(e=this.sessionRecording)||!e.started)}captureException(e,t){var r=Error("PostHog syntheticException");return this.exceptions.sendExceptionEvent(W({},function(e,t){var{error:r,event:i}=e,n={$exception_list:[]},s=r||i;if(tV(s)||tX(s,"DOMException")){if("stack"in s)n=re(s,t);else{var a=s.name||(tV(s)?"DOMError":"DOMException"),o=s.message?a+": "+s.message:a;n=rt(o,W({},t,{overrideExceptionType:tV(s)?"DOMError":"DOMException",defaultExceptionMessage:o}))}return"code"in s&&(n.$exception_DOMException_code=""+s.code),n}if(tX(s,"ErrorEvent")&&s.error)return re(s.error,t);if(tz(s))return re(s,t);if(tX(s,"Object")||tW(s))return function(e,t){var r,i,n,s=null==(i=null==t?void 0:t.handled)||i,a=null==(n=null==t?void 0:t.synthetic)||n,o={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:tW(e)?e.constructor.name:"Error",value:"Non-Error 'exception' captured with keys: "+function(e,t){void 0===t&&(t=40);var r=Object.keys(e);if(r.sort(),!r.length)return"[object has no keys]";for(var i=r.length;i>0;i--){var n=r.slice(0,i).join(", ");if(!(n.length>t))return i===r.length||n.length<=t?n:n.slice(0,t)+"..."}return""}(e),mechanism:{handled:s,synthetic:a}};if(null!=t&&t.syntheticException){var l=t9(null==t?void 0:t.syntheticException,1);l.length&&(o.stacktrace={frames:l,type:"raw"})}return{$exception_list:[o],$exception_level:A(r=e.level)&&!k(r)&&m.indexOf(r)>=0?e.level:"error"}}(s,t);if(I(r)&&A(i)){var l="Error",u=i,c=i.match(t8);return c&&(l=c[1],u=c[2]),rt(u,W({},t,{overrideExceptionType:l,defaultExceptionMessage:u}))}return rt(s,t)}(e instanceof Error?{error:e,event:e.message}:{event:e},{syntheticException:r}),t))}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return null==(t=this.persistence)?void 0:t.props[e]}getSessionProperty(e){var t;return null==(t=this.sessionPersistence)?void 0:t.props[e]}toString(){var e,t=null!=(e=this.config.name)?e:sj;return t!==sj&&(t=sj+"."+t),t}_isIdentified(){var e,t;return"identified"===(null==(e=this.persistence)?void 0:e.get_property(eN))||"identified"===(null==(t=this.sessionPersistence)?void 0:t.get_property(eN))}js(){var e,t;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&C(this.getGroups())&&(null==(e=this.persistence)||null==(e=e.props)||!e[es])&&(null==(t=this.persistence)||null==(t=t.props)||!t[eB]))}As(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||"history_change"===this.config.capture_pageview)}createPersonProfile(){this.js()||this.Ns("posthog.createPersonProfile")&&this.setPersonProperties({},{})}Ns(e){return"never"===this.config.person_profiles?(U.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this.zs(eB,!0),!0)}Is(){var e=this.consent.isOptedOut(),t=this.config.opt_out_persistence_by_default;return this.config.disable_persistence||e&&!!t}Us(){var e,t,r,i,n=this.Is();return(null==(e=this.persistence)?void 0:e.Ae)!==n&&(null==(r=this.persistence)||r.set_disabled(n)),(null==(t=this.sessionPersistence)?void 0:t.Ae)!==n&&(null==(i=this.sessionPersistence)||i.set_disabled(n)),n}opt_in_capturing(e){var t;this.consent.optInOut(!0),this.Us(),(I(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!=(t=null==e?void 0:e.captureEventName)?t:"$opt_in",null==e?void 0:e.captureProperties,{send_instantly:!0}),this.config.capture_pageview&&this.Os()}opt_out_capturing(){this.consent.optInOut(!1),this.Us()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.Us()}_is_bot(){return a?sP(a,this.config.custom_blocked_useragents):void 0}Os(){o&&("visible"===o.visibilityState?this.ws||(this.ws=!0,this.capture("$pageview",{title:o.title},{send_instantly:!0}),this.Ss&&(o.removeEventListener("visibilitychange",this.Ss),this.Ss=null)):this.Ss||(this.Ss=this.Os.bind(this),ei(o,"visibilitychange",this.Ss)))}debug(e){!1===e?(null==t||t.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==t||t.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}I(){var e,t,r,i,n=this.ks||{};return"advanced_disable_flags"in n?!!n.advanced_disable_flags:!1!==this.config.advanced_disable_flags?!!this.config.advanced_disable_flags:!0===this.config.advanced_disable_decide?(U.warn("Config field 'advanced_disable_decide' is deprecated. Please use 'advanced_disable_flags' instead. The old field will be removed in a future major version."),!0):(t="advanced_disable_decide",r=(e="advanced_disable_flags")in n&&!I(n[e]),i=t in n&&!I(n[t]),r?n[e]:!!i&&(U&&U.warn("Config field '"+t+"' is deprecated. Please use '"+e+"' instead. The old field will be removed in a future major version."),n[t]))}Ls(e){if(M(this.config.before_send))return e;var t=x(this.config.before_send)?this.config.before_send:[this.config.before_send],r=e;for(var i of t){if(M(r=i(r))){var n="Event '"+e.event+"' was rejected in beforeSend function";return D(e.event)?U.warn(n+". This can cause unexpected behavior."):U.info(n),null}r.properties&&!C(r.properties)||U.warn("Event '"+e.event+"' has no properties after beforeSend function, this is likely an error.")}return r}getPageViewId(){var e;return null==(e=this.pageViewManager.ce)?void 0:e.pageViewId}captureTraceFeedback(e,t){this.capture("$ai_feedback",{$ai_trace_id:String(e),$ai_feedback_text:t})}captureTraceMetric(e,t,r){this.capture("$ai_metric",{$ai_trace_id:String(e),$ai_metric_name:t,$ai_metric_value:String(r)})}}!function(e,t){for(var r=0;r<t.length;r++)e.prototype[t[r]]=Q(e.prototype[t[r]])}(sD,["identify"]),(e=sA[sj]=new sD,function(){function e(){e.done||(e.done=!0,sM=!1,V(sA,function(e){e._dom_loaded()}))}null!=o&&o.addEventListener?"complete"===o.readyState?e():ei(o,"DOMContentLoaded",e,{capture:!1}):t&&U.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}(),e).init("phc_2oQSSzXgGcCmTZ0YigOv7yq9RfH49WlVktnZ6HC2vQA",{api_host:"https://us.i.posthog.com",defaults:"2025-05-24"})},68205:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return i}}),r(54902),r(83670);let i=e=>(e.startsWith("/"),e);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68276:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{createKey:function(){return G},default:function(){return V},matchesMiddleware:function(){return F}});let i=r(64252),n=r(88365),s=r(54902),a=r(37176),o=r(3996),l=n._(r(66240)),u=r(5195),c=r(41862),d=i._(r(29871)),h=r(2746),f=r(49163),p=r(20541),_=i._(r(80365)),g=r(85519),v=r(95214),m=r(8480);r(42616);let y=r(83670),b=r(54591),E=r(63836),P=r(1025),S=r(62092),w=r(16023),R=r(41921),x=r(2326),O=r(73407),T=r(84980),C=r(64359),I=r(51533),A=r(87407),k=r(40990),j=r(98069),M=r(53132),N=r(39308);function L(){return Object.assign(Object.defineProperty(Error("Route Cancelled"),"__NEXT_ERROR_CODE",{value:"E315",enumerable:!1,configurable:!0}),{cancelled:!0})}async function F(e){let t=await Promise.resolve(e.router.pageLoader.getMiddleware());if(!t)return!1;let{pathname:r}=(0,y.parsePath)(e.asPath),i=(0,w.hasBasePath)(r)?(0,P.removeBasePath)(r):r,n=(0,S.addBasePath)((0,b.addLocale)(i,e.locale));return t.some(e=>new RegExp(e.regexp).test(n))}function D(e){let t=(0,h.getLocationOrigin)();return e.startsWith(t)?e.substring(t.length):e}function $(e,t,r){let[i,n]=(0,R.resolveHref)(e,t,!0),s=(0,h.getLocationOrigin)(),a=i.startsWith(s),o=n&&n.startsWith(s);i=D(i),n=n?D(n):n;let l=a?i:(0,S.addBasePath)(i),u=r?D((0,R.resolveHref)(e,r)):n||i;return{url:l,as:o?u:(0,S.addBasePath)(u)}}function U(e,t){let r=(0,s.removeTrailingSlash)((0,u.denormalizePagePath)(e));return"/404"===r||"/_error"===r?e:(t.includes(r)||t.some(t=>{if((0,f.isDynamicRoute)(t)&&(0,v.getRouteRegex)(t).re.test(r))return e=t,!0}),(0,s.removeTrailingSlash)(e))}async function B(e){if(!await F(e)||!e.fetchData)return null;let t=await e.fetchData(),r=await function(e,t,r){let i={basePath:r.router.basePath,i18n:{locales:r.router.locales},trailingSlash:!1},n=t.headers.get("x-nextjs-rewrite"),o=n||t.headers.get("x-nextjs-matched-path"),l=t.headers.get(N.MATCHED_PATH_HEADER);if(!l||o||l.includes("__next_data_catchall")||l.includes("/_error")||l.includes("/404")||(o=l),o){if(o.startsWith("/")){let t=(0,p.parseRelativeUrl)(o),l=(0,O.getNextPathnameInfo)(t.pathname,{nextConfig:i,parseData:!0}),u=(0,s.removeTrailingSlash)(l.pathname);return Promise.all([r.router.pageLoader.getPageList(),(0,a.getClientBuildManifest)()]).then(i=>{let[s,{__rewrites:a}]=i,o=(0,b.addLocale)(l.pathname,l.locale);if((0,f.isDynamicRoute)(o)||!n&&s.includes((0,c.normalizeLocalePath)((0,P.removeBasePath)(o),r.router.locales).pathname)){let r=(0,O.getNextPathnameInfo)((0,p.parseRelativeUrl)(e).pathname,{nextConfig:void 0,parseData:!0});t.pathname=o=(0,S.addBasePath)(r.pathname)}{let e=(0,_.default)(o,s,a,t.query,e=>U(e,s),r.router.locales);e.matchedPage&&(t.pathname=e.parsedAs.pathname,o=t.pathname,Object.assign(t.query,e.parsedAs.query))}let d=s.includes(u)?u:U((0,c.normalizeLocalePath)((0,P.removeBasePath)(t.pathname),r.router.locales).pathname,s);if((0,f.isDynamicRoute)(d)){let e=(0,g.getRouteMatcher)((0,v.getRouteRegex)(d))(o);Object.assign(t.query,e||{})}return{type:"rewrite",parsedAs:t,resolvedHref:d}})}let t=(0,y.parsePath)(e);return Promise.resolve({type:"redirect-external",destination:""+(0,T.formatNextPathnameInfo)({...(0,O.getNextPathnameInfo)(t.pathname,{nextConfig:i,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""})+t.query+t.hash})}let u=t.headers.get("x-nextjs-redirect");if(u){if(u.startsWith("/")){let e=(0,y.parsePath)(u),t=(0,T.formatNextPathnameInfo)({...(0,O.getNextPathnameInfo)(e.pathname,{nextConfig:i,parseData:!0}),defaultLocale:r.router.defaultLocale,buildId:""});return Promise.resolve({type:"redirect-internal",newAs:""+t+e.query+e.hash,newUrl:""+t+e.query+e.hash})}return Promise.resolve({type:"redirect-external",destination:u})}return Promise.resolve({type:"next"})}(t.dataHref,t.response,e);return{dataHref:t.dataHref,json:t.json,response:t.response,text:t.text,cacheKey:t.cacheKey,effect:r}}let H=Symbol("SSG_DATA_NOT_FOUND");function q(e){try{return JSON.parse(e)}catch(e){return null}}function W(e){let{dataHref:t,inflightCache:r,isPrefetch:i,hasMiddleware:n,isServerRender:s,parseJSON:o,persistCache:l,isBackground:u,unstable_skipClientCache:c}=e,{href:d}=new URL(t,window.location.href),h=e=>{var u;return(function e(t,r,i){return fetch(t,{credentials:"same-origin",method:i.method||"GET",headers:Object.assign({},i.headers,{"x-nextjs-data":"1"})}).then(n=>!n.ok&&r>1&&n.status>=500?e(t,r-1,i):n)})(t,s?3:1,{headers:Object.assign({},i?{purpose:"prefetch"}:{},i&&n?{"x-middleware-prefetch":"1"}:{},{}),method:null!=(u=null==e?void 0:e.method)?u:"GET"}).then(r=>r.ok&&(null==e?void 0:e.method)==="HEAD"?{dataHref:t,response:r,text:"",json:{},cacheKey:d}:r.text().then(e=>{if(!r.ok){if(n&&[301,302,307,308].includes(r.status))return{dataHref:t,response:r,text:e,json:{},cacheKey:d};if(404===r.status){var i;if(null==(i=q(e))?void 0:i.notFound)return{dataHref:t,json:{notFound:H},response:r,text:e,cacheKey:d}}let o=Object.defineProperty(Error("Failed to load static props"),"__NEXT_ERROR_CODE",{value:"E124",enumerable:!1,configurable:!0});throw s||(0,a.markAssetError)(o),o}return{dataHref:t,json:o?q(e):null,response:r,text:e,cacheKey:d}})).then(e=>(l&&"no-cache"!==e.response.headers.get("x-middleware-cache")||delete r[d],e)).catch(e=>{throw c||delete r[d],("Failed to fetch"===e.message||"NetworkError when attempting to fetch resource."===e.message||"Load failed"===e.message)&&(0,a.markAssetError)(e),e})};return c&&l?h({}).then(e=>("no-cache"!==e.response.headers.get("x-middleware-cache")&&(r[d]=Promise.resolve(e)),e)):void 0!==r[d]?r[d]:r[d]=h(u?{method:"HEAD"}:{})}function G(){return Math.random().toString(36).slice(2,10)}function z(e){let{url:t,router:r}=e;if(t===(0,S.addBasePath)((0,b.addLocale)(r.asPath,r.locale)))throw Object.defineProperty(Error("Invariant: attempted to hard navigate to the same URL "+t+" "+location.href),"__NEXT_ERROR_CODE",{value:"E282",enumerable:!1,configurable:!0});window.location.href=t}let X=e=>{let{route:t,router:r}=e,i=!1,n=r.clc=()=>{i=!0};return()=>{if(i){let e=Object.defineProperty(Error('Abort fetching component for route: "'+t+'"'),"__NEXT_ERROR_CODE",{value:"E483",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}n===r.clc&&(r.clc=null)}};class V{reload(){window.location.reload()}back(){window.history.back()}forward(){window.history.forward()}push(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=$(this,e,t),this.change("pushState",e,t,r)}replace(e,t,r){return void 0===r&&(r={}),{url:e,as:t}=$(this,e,t),this.change("replaceState",e,t,r)}async _bfl(e,t,i,n){{if(!this._bfl_s&&!this._bfl_d){let t,s,{BloomFilter:o}=r(94069);try{({__routerFilterStatic:t,__routerFilterDynamic:s}=await (0,a.getClientBuildManifest)())}catch(t){if(console.error(t),n)return!0;return z({url:(0,S.addBasePath)((0,b.addLocale)(e,i||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}(null==t?void 0:t.numHashes)&&(this._bfl_s=new o(t.numItems,t.errorRate),this._bfl_s.import(t)),(null==s?void 0:s.numHashes)&&(this._bfl_d=new o(s.numItems,s.errorRate),this._bfl_d.import(s))}let c=!1,d=!1;for(let{as:r,allowMatchCurrent:a}of[{as:e},{as:t}])if(r){let t=(0,s.removeTrailingSlash)(new URL(r,"http://n").pathname),h=(0,S.addBasePath)((0,b.addLocale)(t,i||this.locale));if(a||t!==(0,s.removeTrailingSlash)(new URL(this.asPath,"http://n").pathname)){var o,l,u;for(let e of(c=c||!!(null==(o=this._bfl_s)?void 0:o.contains(t))||!!(null==(l=this._bfl_s)?void 0:l.contains(h)),[t,h])){let t=e.split("/");for(let e=0;!d&&e<t.length+1;e++){let r=t.slice(0,e).join("/");if(r&&(null==(u=this._bfl_d)?void 0:u.contains(r))){d=!0;break}}}if(c||d){if(n)return!0;return z({url:(0,S.addBasePath)((0,b.addLocale)(e,i||this.locale,this.defaultLocale)),router:this}),new Promise(()=>{})}}}}return!1}async change(e,t,r,i,n){var u,c,d,R,x,O,T,A,M;let N,D;if(!(0,I.isLocalURL)(t))return z({url:t,router:this}),!1;let B=1===i._h;B||i.shallow||await this._bfl(r,void 0,i.locale);let q=B||i._shouldResolveHref||(0,y.parsePath)(t).pathname===(0,y.parsePath)(r).pathname,W={...this.state},G=!0!==this.isReady;this.isReady=!0;let X=this.isSsr;if(B||(this.isSsr=!1),B&&this.clc)return!1;let Y=W.locale;h.ST&&performance.mark("routeChange");let{shallow:K=!1,scroll:J=!0}=i,Z={shallow:K};this._inFlightRoute&&this.clc&&(X||V.events.emit("routeChangeError",L(),this._inFlightRoute,Z),this.clc(),this.clc=null),r=(0,S.addBasePath)((0,b.addLocale)((0,w.hasBasePath)(r)?(0,P.removeBasePath)(r):r,i.locale,this.defaultLocale));let Q=(0,E.removeLocale)((0,w.hasBasePath)(r)?(0,P.removeBasePath)(r):r,W.locale);this._inFlightRoute=r;let ee=Y!==W.locale;if(!B&&this.onlyAHashChange(Q)&&!ee){W.asPath=Q,V.events.emit("hashChangeStart",r,Z),this.changeState(e,t,r,{...i,scroll:!1}),J&&this.scrollToHash(Q);try{await this.set(W,this.components[W.route],null)}catch(e){throw(0,l.default)(e)&&e.cancelled&&V.events.emit("routeChangeError",e,Q,Z),e}return V.events.emit("hashChangeComplete",r,Z),!0}let et=(0,p.parseRelativeUrl)(t),{pathname:er,query:ei}=et;try{[N,{__rewrites:D}]=await Promise.all([this.pageLoader.getPageList(),(0,a.getClientBuildManifest)(),this.pageLoader.getMiddleware()])}catch(e){return z({url:r,router:this}),!1}this.urlIsNew(Q)||ee||(e="replaceState");let en=r;er=er?(0,s.removeTrailingSlash)((0,P.removeBasePath)(er)):er;let es=(0,s.removeTrailingSlash)(er),ea=r.startsWith("/")&&(0,p.parseRelativeUrl)(r).pathname;if(null==(u=this.components[er])?void 0:u.__appRouter)return z({url:r,router:this}),new Promise(()=>{});let eo=!!(ea&&es!==ea&&(!(0,f.isDynamicRoute)(es)||!(0,g.getRouteMatcher)((0,v.getRouteRegex)(es))(ea))),el=!i.shallow&&await F({asPath:r,locale:W.locale,router:this});if(B&&el&&(q=!1),q&&"/_error"!==er)if(i._shouldResolveHref=!0,r.startsWith("/")){let e=(0,_.default)((0,S.addBasePath)((0,b.addLocale)(Q,W.locale),!0),N,D,ei,e=>U(e,N),this.locales);if(e.externalDest)return z({url:r,router:this}),!0;el||(en=e.asPath),e.matchedPage&&e.resolvedHref&&(er=e.resolvedHref,et.pathname=(0,S.addBasePath)(er),el||(t=(0,m.formatWithValidation)(et)))}else et.pathname=U(er,N),et.pathname!==er&&(er=et.pathname,et.pathname=(0,S.addBasePath)(er),el||(t=(0,m.formatWithValidation)(et)));if(!(0,I.isLocalURL)(r))return z({url:r,router:this}),!1;en=(0,E.removeLocale)((0,P.removeBasePath)(en),W.locale),es=(0,s.removeTrailingSlash)(er);let eu=!1;if((0,f.isDynamicRoute)(es)){let e=(0,p.parseRelativeUrl)(en),i=e.pathname,n=(0,v.getRouteRegex)(es);eu=(0,g.getRouteMatcher)(n)(i);let s=es===i,a=s?(0,j.interpolateAs)(es,i,ei):{};if(eu&&(!s||a.result))s?r=(0,m.formatWithValidation)(Object.assign({},e,{pathname:a.result,query:(0,k.omit)(ei,a.params)})):Object.assign(ei,eu);else{let e=Object.keys(n.groups).filter(e=>!ei[e]&&!n.groups[e].optional);if(e.length>0&&!el)throw Object.defineProperty(Error((s?"The provided `href` ("+t+") value is missing query values ("+e.join(", ")+") to be interpolated properly. ":"The provided `as` value ("+i+") is incompatible with the `href` value ("+es+"). ")+"Read more: https://nextjs.org/docs/messages/"+(s?"href-interpolation-failed":"incompatible-href-as")),"__NEXT_ERROR_CODE",{value:"E344",enumerable:!1,configurable:!0})}}B||V.events.emit("routeChangeStart",r,Z);let ec="/404"===this.pathname||"/_error"===this.pathname;try{let s=await this.getRouteInfo({route:es,pathname:er,query:ei,as:r,resolvedAs:en,routeProps:Z,locale:W.locale,isPreview:W.isPreview,hasMiddleware:el,unstable_skipClientCache:i.unstable_skipClientCache,isQueryUpdating:B&&!this.isFallback,isMiddlewareRewrite:eo});if(B||i.shallow||await this._bfl(r,"resolvedAs"in s?s.resolvedAs:void 0,W.locale),"route"in s&&el){es=er=s.route||es,Z.shallow||(ei=Object.assign({},s.query||{},ei));let e=(0,w.hasBasePath)(et.pathname)?(0,P.removeBasePath)(et.pathname):et.pathname;if(eu&&er!==e&&Object.keys(eu).forEach(e=>{eu&&ei[e]===eu[e]&&delete ei[e]}),(0,f.isDynamicRoute)(er)){let e=!Z.shallow&&s.resolvedAs?s.resolvedAs:(0,S.addBasePath)((0,b.addLocale)(new URL(r,location.href).pathname,W.locale),!0);(0,w.hasBasePath)(e)&&(e=(0,P.removeBasePath)(e));let t=(0,v.getRouteRegex)(er),i=(0,g.getRouteMatcher)(t)(new URL(e,location.href).pathname);i&&Object.assign(ei,i)}}if("type"in s)if("redirect-internal"===s.type)return this.change(e,s.newUrl,s.newAs,i);else return z({url:s.destination,router:this}),new Promise(()=>{});let a=s.Component;if(a&&a.unstable_scriptLoader&&[].concat(a.unstable_scriptLoader()).forEach(e=>{(0,o.handleClientScriptLoad)(e.props)}),(s.__N_SSG||s.__N_SSP)&&s.props){if(s.props.pageProps&&s.props.pageProps.__N_REDIRECT){i.locale=!1;let t=s.props.pageProps.__N_REDIRECT;if(t.startsWith("/")&&!1!==s.props.pageProps.__N_REDIRECT_BASE_PATH){let r=(0,p.parseRelativeUrl)(t);r.pathname=U(r.pathname,N);let{url:n,as:s}=$(this,t,t);return this.change(e,n,s,i)}return z({url:t,router:this}),new Promise(()=>{})}if(W.isPreview=!!s.props.__N_PREVIEW,s.props.notFound===H){let e;try{await this.fetchComponent("/404"),e="/404"}catch(t){e="/_error"}if(s=await this.getRouteInfo({route:e,pathname:e,query:ei,as:r,resolvedAs:en,routeProps:{shallow:!1},locale:W.locale,isPreview:W.isPreview,isNotFound:!0}),"type"in s)throw Object.defineProperty(Error("Unexpected middleware effect on /404"),"__NEXT_ERROR_CODE",{value:"E158",enumerable:!1,configurable:!0})}}B&&"/_error"===this.pathname&&(null==(d=self.__NEXT_DATA__.props)||null==(c=d.pageProps)?void 0:c.statusCode)===500&&(null==(R=s.props)?void 0:R.pageProps)&&(s.props.pageProps.statusCode=500);let u=i.shallow&&W.route===(null!=(x=s.route)?x:es),h=null!=(O=i.scroll)?O:!B&&!u,_=null!=n?n:h?{x:0,y:0}:null,m={...W,route:es,pathname:er,query:ei,asPath:Q,isFallback:!1};if(B&&ec){if(s=await this.getRouteInfo({route:this.pathname,pathname:this.pathname,query:ei,as:r,resolvedAs:en,routeProps:{shallow:!1},locale:W.locale,isPreview:W.isPreview,isQueryUpdating:B&&!this.isFallback}),"type"in s)throw Object.defineProperty(Error("Unexpected middleware effect on "+this.pathname),"__NEXT_ERROR_CODE",{value:"E225",enumerable:!1,configurable:!0});"/_error"===this.pathname&&(null==(A=self.__NEXT_DATA__.props)||null==(T=A.pageProps)?void 0:T.statusCode)===500&&(null==(M=s.props)?void 0:M.pageProps)&&(s.props.pageProps.statusCode=500);try{await this.set(m,s,_)}catch(e){throw(0,l.default)(e)&&e.cancelled&&V.events.emit("routeChangeError",e,Q,Z),e}return!0}if(V.events.emit("beforeHistoryChange",r,Z),this.changeState(e,t,r,i),!(B&&!_&&!G&&!ee&&(0,C.compareRouterStates)(m,this.state))){try{await this.set(m,s,_)}catch(e){if(e.cancelled)s.error=s.error||e;else throw e}if(s.error)throw B||V.events.emit("routeChangeError",s.error,Q,Z),s.error;B||V.events.emit("routeChangeComplete",r,Z),h&&/#.+$/.test(r)&&this.scrollToHash(r)}return!0}catch(e){if((0,l.default)(e)&&e.cancelled)return!1;throw e}}changeState(e,t,r,i){void 0===i&&(i={}),("pushState"!==e||(0,h.getURL)()!==r)&&(this._shallow=i.shallow,window.history[e]({url:t,as:r,options:i,__N:!0,key:this._key="pushState"!==e?this._key:G()},"",r))}async handleRouteInfoError(e,t,r,i,n,s){if(e.cancelled)throw e;if((0,a.isAssetError)(e)||s)throw V.events.emit("routeChangeError",e,i,n),z({url:i,router:this}),L();console.error(e);try{let i,{page:n,styleSheets:s}=await this.fetchComponent("/_error"),a={props:i,Component:n,styleSheets:s,err:e,error:e};if(!a.props)try{a.props=await this.getInitialProps(n,{err:e,pathname:t,query:r})}catch(e){console.error("Error in error page `getInitialProps`: ",e),a.props={}}return a}catch(e){return this.handleRouteInfoError((0,l.default)(e)?e:Object.defineProperty(Error(e+""),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0}),t,r,i,n,!0)}}async getRouteInfo(e){let{route:t,pathname:r,query:i,as:n,resolvedAs:a,routeProps:o,locale:u,hasMiddleware:d,isPreview:h,unstable_skipClientCache:f,isQueryUpdating:p,isMiddlewareRewrite:_,isNotFound:g}=e,v=t;try{var y,b,E,S;let e=this.components[v];if(o.shallow&&e&&this.route===v)return e;let t=X({route:v,router:this});d&&(e=void 0);let l=!e||"initial"in e?void 0:e,w={dataHref:this.pageLoader.getDataHref({href:(0,m.formatWithValidation)({pathname:r,query:i}),skipInterpolation:!0,asPath:g?"/404":a,locale:u}),hasMiddleware:!0,isServerRender:this.isSsr,parseJSON:!0,inflightCache:p?this.sbc:this.sdc,persistCache:!h,isPrefetch:!1,unstable_skipClientCache:f,isBackground:p},R=p&&!_?null:await B({fetchData:()=>W(w),asPath:g?"/404":a,locale:u,router:this}).catch(e=>{if(p)return null;throw e});if(R&&("/_error"===r||"/404"===r)&&(R.effect=void 0),p&&(R?R.json=self.__NEXT_DATA__.props:R={json:self.__NEXT_DATA__.props}),t(),(null==R||null==(y=R.effect)?void 0:y.type)==="redirect-internal"||(null==R||null==(b=R.effect)?void 0:b.type)==="redirect-external")return R.effect;if((null==R||null==(E=R.effect)?void 0:E.type)==="rewrite"){let t=(0,s.removeTrailingSlash)(R.effect.resolvedHref),n=await this.pageLoader.getPageList();if((!p||n.includes(t))&&(v=t,r=R.effect.resolvedHref,i={...i,...R.effect.parsedAs.query},a=(0,P.removeBasePath)((0,c.normalizeLocalePath)(R.effect.parsedAs.pathname,this.locales).pathname),e=this.components[v],o.shallow&&e&&this.route===v&&!d))return{...e,route:v}}if((0,x.isAPIRoute)(v))return z({url:n,router:this}),new Promise(()=>{});let O=l||await this.fetchComponent(v).then(e=>({Component:e.page,styleSheets:e.styleSheets,__N_SSG:e.mod.__N_SSG,__N_SSP:e.mod.__N_SSP})),T=null==R||null==(S=R.response)?void 0:S.headers.get("x-middleware-skip"),C=O.__N_SSG||O.__N_SSP;T&&(null==R?void 0:R.dataHref)&&delete this.sdc[R.dataHref];let{props:I,cacheKey:A}=await this._getData(async()=>{if(C){if((null==R?void 0:R.json)&&!T)return{cacheKey:R.cacheKey,props:R.json};let e=(null==R?void 0:R.dataHref)?R.dataHref:this.pageLoader.getDataHref({href:(0,m.formatWithValidation)({pathname:r,query:i}),asPath:a,locale:u}),t=await W({dataHref:e,isServerRender:this.isSsr,parseJSON:!0,inflightCache:T?{}:this.sdc,persistCache:!h,isPrefetch:!1,unstable_skipClientCache:f});return{cacheKey:t.cacheKey,props:t.json||{}}}return{headers:{},props:await this.getInitialProps(O.Component,{pathname:r,query:i,asPath:n,locale:u,locales:this.locales,defaultLocale:this.defaultLocale})}});return O.__N_SSP&&w.dataHref&&A&&delete this.sdc[A],this.isPreview||!O.__N_SSG||p||W(Object.assign({},w,{isBackground:!0,persistCache:!1,inflightCache:this.sbc})).catch(()=>{}),I.pageProps=Object.assign({},I.pageProps),O.props=I,O.route=v,O.query=i,O.resolvedAs=a,this.components[v]=O,O}catch(e){return this.handleRouteInfoError((0,l.getProperError)(e),r,i,n,o)}}set(e,t,r){return this.state=e,this.sub(t,this.components["/_app"].Component,r)}beforePopState(e){this._bps=e}onlyAHashChange(e){if(!this.asPath)return!1;let[t,r]=this.asPath.split("#",2),[i,n]=e.split("#",2);return!!n&&t===i&&r===n||t===i&&r!==n}scrollToHash(e){let[,t=""]=e.split("#",2);(0,M.handleSmoothScroll)(()=>{if(""===t||"top"===t)return void window.scrollTo(0,0);let e=decodeURIComponent(t),r=document.getElementById(e);if(r)return void r.scrollIntoView();let i=document.getElementsByName(e)[0];i&&i.scrollIntoView()},{onlyHashChange:this.onlyAHashChange(e)})}urlIsNew(e){return this.asPath!==e}async prefetch(e,t,r){if(void 0===t&&(t=e),void 0===r&&(r={}),(0,A.isBot)(window.navigator.userAgent))return;let i=(0,p.parseRelativeUrl)(e),n=i.pathname,{pathname:o,query:l}=i,u=o,c=await this.pageLoader.getPageList(),d=t,h=void 0!==r.locale?r.locale||void 0:this.locale,w=await F({asPath:t,locale:h,router:this});if(t.startsWith("/")){let r;({__rewrites:r}=await (0,a.getClientBuildManifest)());let n=(0,_.default)((0,S.addBasePath)((0,b.addLocale)(t,this.locale),!0),c,r,i.query,e=>U(e,c),this.locales);if(n.externalDest)return;w||(d=(0,E.removeLocale)((0,P.removeBasePath)(n.asPath),this.locale)),n.matchedPage&&n.resolvedHref&&(i.pathname=o=n.resolvedHref,w||(e=(0,m.formatWithValidation)(i)))}i.pathname=U(i.pathname,c),(0,f.isDynamicRoute)(i.pathname)&&(o=i.pathname,i.pathname=o,Object.assign(l,(0,g.getRouteMatcher)((0,v.getRouteRegex)(i.pathname))((0,y.parsePath)(t).pathname)||{}),w||(e=(0,m.formatWithValidation)(i)));let R=await B({fetchData:()=>W({dataHref:this.pageLoader.getDataHref({href:(0,m.formatWithValidation)({pathname:u,query:l}),skipInterpolation:!0,asPath:d,locale:h}),hasMiddleware:!0,isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0}),asPath:t,locale:h,router:this});if((null==R?void 0:R.effect.type)==="rewrite"&&(i.pathname=R.effect.resolvedHref,o=R.effect.resolvedHref,l={...l,...R.effect.parsedAs.query},d=R.effect.parsedAs.pathname,e=(0,m.formatWithValidation)(i)),(null==R?void 0:R.effect.type)==="redirect-external")return;let x=(0,s.removeTrailingSlash)(o);await this._bfl(t,d,r.locale,!0)&&(this.components[n]={__appRouter:!0}),await Promise.all([this.pageLoader._isSsg(x).then(t=>!!t&&W({dataHref:(null==R?void 0:R.json)?null==R?void 0:R.dataHref:this.pageLoader.getDataHref({href:e,asPath:d,locale:h}),isServerRender:!1,parseJSON:!0,inflightCache:this.sdc,persistCache:!this.isPreview,isPrefetch:!0,unstable_skipClientCache:r.unstable_skipClientCache||r.priority&&!0}).then(()=>!1).catch(()=>!1)),this.pageLoader[r.priority?"loadPage":"prefetch"](x)])}async fetchComponent(e){let t=X({route:e,router:this});try{let r=await this.pageLoader.loadPage(e);return t(),r}catch(e){throw t(),e}}_getData(e){let t=!1,r=()=>{t=!0};return this.clc=r,e().then(e=>{if(r===this.clc&&(this.clc=null),t){let e=Object.defineProperty(Error("Loading initial props cancelled"),"__NEXT_ERROR_CODE",{value:"E405",enumerable:!1,configurable:!0});throw e.cancelled=!0,e}return e})}getInitialProps(e,t){let{Component:r}=this.components["/_app"],i=this._wrapApp(r);return t.AppTree=i,(0,h.loadGetInitialProps)(r,{AppTree:i,Component:e,router:this,ctx:t})}get route(){return this.state.route}get pathname(){return this.state.pathname}get query(){return this.state.query}get asPath(){return this.state.asPath}get locale(){return this.state.locale}get isFallback(){return this.state.isFallback}get isPreview(){return this.state.isPreview}constructor(e,t,r,{initialProps:i,pageLoader:n,App:a,wrapApp:o,Component:l,err:u,subscription:c,isFallback:d,locale:_,locales:g,defaultLocale:v,domainLocales:y,isPreview:b}){this.sdc={},this.sbc={},this.isFirstPopStateEvent=!0,this._key=G(),this.onPopState=e=>{let t,{isFirstPopStateEvent:r}=this;this.isFirstPopStateEvent=!1;let i=e.state;if(!i){let{pathname:e,query:t}=this;this.changeState("replaceState",(0,m.formatWithValidation)({pathname:(0,S.addBasePath)(e),query:t}),(0,h.getURL)());return}if(i.__NA)return void window.location.reload();if(!i.__N||r&&this.locale===i.options.locale&&i.as===this.asPath)return;let{url:n,as:s,options:a,key:o}=i;this._key=o;let{pathname:l}=(0,p.parseRelativeUrl)(n);(!this.isSsr||s!==(0,S.addBasePath)(this.asPath)||l!==(0,S.addBasePath)(this.pathname))&&(!this._bps||this._bps(i))&&this.change("replaceState",n,s,Object.assign({},a,{shallow:a.shallow&&this._shallow,locale:a.locale||this.defaultLocale,_h:0}),t)};let E=(0,s.removeTrailingSlash)(e);this.components={},"/_error"!==e&&(this.components[E]={Component:l,initial:!0,props:i,err:u,__N_SSG:i&&i.__N_SSG,__N_SSP:i&&i.__N_SSP}),this.components["/_app"]={Component:a,styleSheets:[]},this.events=V.events,this.pageLoader=n;let P=(0,f.isDynamicRoute)(e)&&self.__NEXT_DATA__.autoExport;if(this.basePath="",this.sub=c,this.clc=null,this._wrapApp=o,this.isSsr=!0,this.isLocaleDomain=!1,this.isReady=!!(self.__NEXT_DATA__.gssp||self.__NEXT_DATA__.gip||self.__NEXT_DATA__.isExperimentalCompile||self.__NEXT_DATA__.appGip&&!self.__NEXT_DATA__.gsp||!P&&!self.location.search&&0),this.state={route:E,pathname:e,query:t,asPath:P?e:r,isPreview:!!b,locale:void 0,isFallback:d},this._initialMatchesMiddlewarePromise=Promise.resolve(!1),!r.startsWith("//")){let i={locale:_},n=(0,h.getURL)();this._initialMatchesMiddlewarePromise=F({router:this,locale:_,asPath:n}).then(s=>(i._shouldResolveHref=r!==e,this.changeState("replaceState",s?n:(0,m.formatWithValidation)({pathname:(0,S.addBasePath)(e),query:t}),n,i),s))}window.addEventListener("popstate",this.onPopState)}}V.events=(0,d.default)()},68714:(e,t)=>{"use strict";function r(e){return"("===e[0]&&e.endsWith(")")}function i(e){return e.startsWith("@")&&"@children"!==e}function n(e,t){if(e.includes(s)){let e=JSON.stringify(t);return"{}"!==e?s+"?"+e:s}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_SEGMENT_KEY:function(){return a},PAGE_SEGMENT_KEY:function(){return s},addSearchParamsIfPageSegment:function(){return n},isGroupSegment:function(){return r},isParallelRouteSegment:function(){return i}});let s="__PAGE__",a="__DEFAULT__"},68831:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HeadManagerContext",{enumerable:!0,get:function(){return i}});let i=r(64252)._(r(14232)).default.createContext({})},69609:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{PathnameContextProviderAdapter:function(){return f},adaptForAppRouterInstance:function(){return c},adaptForPathParams:function(){return h},adaptForSearchParams:function(){return d}});let i=r(88365),n=r(37876),s=i._(r(14232)),a=r(5931),o=r(63069),l=r(88213),u=r(95214);function c(e){return{back(){e.back()},forward(){e.forward()},refresh(){e.reload()},hmrRefresh(){},push(t,r){let{scroll:i}=void 0===r?{}:r;e.push(t,void 0,{scroll:i})},replace(t,r){let{scroll:i}=void 0===r?{}:r;e.replace(t,void 0,{scroll:i})},prefetch(t){e.prefetch(t)}}}function d(e){return e.isReady&&e.query?(0,l.asPathToSearchParams)(e.asPath):new URLSearchParams}function h(e){if(!e.isReady||!e.query)return null;let t={};for(let r of Object.keys((0,u.getRouteRegex)(e.pathname).groups))t[r]=e.query[r];return t}function f(e){let{children:t,router:r,...i}=e,l=(0,s.useRef)(i.isAutoExport),u=(0,s.useMemo)(()=>{let e,t=l.current;if(t&&(l.current=!1),(0,o.isDynamicRoute)(r.pathname)&&(r.isFallback||t&&!r.isReady))return null;try{e=new URL(r.asPath,"http://f")}catch(e){return"/"}return e.pathname},[r.asPath,r.isFallback,r.isReady,r.pathname]);return(0,n.jsx)(a.PathnameContext.Provider,{value:u,children:t})}},70536:(e,t)=>{"use strict";function r(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return r}})},71827:(e,t)=>{"use strict";function r(e,t){return void 0===t&&(t=""),("/"===e?"/index":/^\/index(\/|$)/.test(e)?"/index"+e:e)+t}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}})},73407:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getNextPathnameInfo",{enumerable:!0,get:function(){return a}});let i=r(41862),n=r(96292),s=r(73716);function a(e,t){var r,a;let{basePath:o,i18n:l,trailingSlash:u}=null!=(r=t.nextConfig)?r:{},c={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):u};o&&(0,s.pathHasPrefix)(c.pathname,o)&&(c.pathname=(0,n.removePathPrefix)(c.pathname,o),c.basePath=o);let d=c.pathname;if(c.pathname.startsWith("/_next/data/")&&c.pathname.endsWith(".json")){let e=c.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");c.buildId=e[0],d="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(c.pathname=d)}if(l){let e=t.i18nProvider?t.i18nProvider.analyze(c.pathname):(0,i.normalizeLocalePath)(c.pathname,l.locales);c.locale=e.detectedLocale,c.pathname=null!=(a=e.pathname)?a:c.pathname,!e.detectedLocale&&c.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(d):(0,i.normalizeLocalePath)(d,l.locales)).detectedLocale&&(c.locale=e.detectedLocale)}return c}},73716:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return n}});let i=r(83670);function n(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,i.parsePath)(e);return r===t||r.startsWith(t+"/")}},76807:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||r&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},76999:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNextRouterError",{enumerable:!0,get:function(){return s}});let i=r(44181),n=r(62591);function s(e){return(0,n.isRedirectError)(e)||(0,i.isHTTPAccessFallbackError)(e)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77207:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"onRecoverableError",{enumerable:!0,get:function(){return l}});let i=r(64252),n=r(63123),s=r(94569),a=r(83575),o=i._(r(66240)),l=(e,t)=>{let r=(0,o.default)(e)&&"cause"in e?e.cause:e,i=(0,a.getReactStitchedError)(r);(0,n.isBailoutToCSRError)(r)||(0,s.reportGlobalError)(i)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78040:(e,t)=>{"use strict";function r(e){let t={};for(let[r,i]of e.entries()){let e=t[r];void 0===e?t[r]=i:Array.isArray(e)?e.push(i):t[r]=[e,i]}return t}function i(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function n(e){let t=new URLSearchParams;for(let[r,n]of Object.entries(e))if(Array.isArray(n))for(let e of n)t.append(r,i(e));else t.set(r,i(n));return t}function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,i]of t.entries())e.append(r,i)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return s},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return n}})},78757:(e,t)=>{"use strict";function r(){return""}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDeploymentIdQueryOrEmptyString",{enumerable:!0,get:function(){return r}})},79611:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return s}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},i=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function n(e){return["async","defer","noModule"].includes(e)}function s(e,t){for(let[s,a]of Object.entries(t)){if(!t.hasOwnProperty(s)||i.includes(s)||void 0===a)continue;let o=r[s]||s.toLowerCase();"SCRIPT"===e.tagName&&n(o)?e[o]=!!a:e.setAttribute(o,String(a)),(!1===a||"SCRIPT"===e.tagName&&n(o)&&(!a||"false"===a))&&(e.setAttribute(o,""),e.removeAttribute(o))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},80303:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AmpStateContext",{enumerable:!0,get:function(){return i}});let i=r(64252)._(r(14232)).default.createContext({})},80365:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return u}});let i=r(26252),n=r(83971),s=r(54902),a=r(41862),o=r(1025),l=r(20541);function u(e,t,r,u,c,d){let h,f=!1,p=!1,_=(0,l.parseRelativeUrl)(e),g=(0,s.removeTrailingSlash)((0,a.normalizeLocalePath)((0,o.removeBasePath)(_.pathname),d).pathname),v=r=>{let l=(0,i.getPathMatch)(r.source+"",{removeUnnamedParams:!0,strict:!0})(_.pathname);if((r.has||r.missing)&&l){let e=(0,n.matchHas)({headers:{host:document.location.hostname,"user-agent":navigator.userAgent},cookies:document.cookie.split("; ").reduce((e,t)=>{let[r,...i]=t.split("=");return e[r]=i.join("="),e},{})},_.query,r.has,r.missing);e?Object.assign(l,e):l=!1}if(l){if(!r.destination)return p=!0,!0;let i=(0,n.prepareDestination)({appendParamsToQuery:!0,destination:r.destination,params:l,query:u});if(_=i.parsedDestination,e=i.newUrl,Object.assign(u,i.parsedDestination.query),g=(0,s.removeTrailingSlash)((0,a.normalizeLocalePath)((0,o.removeBasePath)(e),d).pathname),t.includes(g))return f=!0,h=g,!0;if((h=c(g))!==e&&t.includes(h))return f=!0,!0}},m=!1;for(let e=0;e<r.beforeFiles.length;e++)v(r.beforeFiles[e]);if(!(f=t.includes(g))){if(!m){for(let e=0;e<r.afterFiles.length;e++)if(v(r.afterFiles[e])){m=!0;break}}if(m||(h=c(g),m=f=t.includes(h)),!m){for(let e=0;e<r.fallback.length;e++)if(v(r.fallback[e])){m=!0;break}}}return{asPath:e,parsedAs:_,matchedPage:f,resolvedHref:h,externalDest:p}}},82889:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return n}});let i=r(83670);function n(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:s}=(0,i.parsePath)(e);return""+t+r+n+s}},83575:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getReactStitchedError",{enumerable:!0,get:function(){return u}});let i=r(64252),n=i._(r(14232)),s=i._(r(66240)),a=r(38089),o="react-stack-bottom-frame",l=RegExp("(at "+o+" )|("+o+"\\@)");function u(e){let t=(0,s.default)(e),r=t&&e.stack||"",i=t?e.message:"",o=r.split("\n"),u=o.findIndex(e=>l.test(e)),c=u>=0?o.slice(0,u).join("\n"):r,d=Object.defineProperty(Error(i),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return Object.assign(d,e),(0,a.copyNextErrorCode)(e,d),d.stack=c,function(e){if(!n.default.captureOwnerStack)return;let t=e.stack||"",r=n.default.captureOwnerStack();r&&!1===t.endsWith(r)&&(e.stack=t+=r)}(d),d}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83670:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},83971:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return c},matchHas:function(){return u},parseDestination:function(){return d},prepareDestination:function(){return h}});let i=r(29509),n=r(51924),s=r(98422),a=r(37188),o=r(29663);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function u(e,t,r,i){void 0===r&&(r=[]),void 0===i&&(i=[]);let n={},s=r=>{let i,s=r.key;switch(r.type){case"header":s=s.toLowerCase(),i=e.headers[s];break;case"cookie":i="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":i=t[s];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};i=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&i)return n[function(e){let t="";for(let r=0;r<e.length;r++){let i=e.charCodeAt(r);(i>64&&i<91||i>96&&i<123)&&(t+=e[r])}return t}(s)]=i,!0;if(i){let e=RegExp("^"+r.value+"$"),t=Array.isArray(i)?i.slice(-1)[0].match(e):i.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{n[e]=t.groups[e]}):"host"===r.type&&t[0]&&(n.host=t[0])),!0}return!1};return!(!r.every(e=>s(e))||i.some(e=>s(e)))&&n}function c(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,i.compile)("/"+e,{validate:!1})(t).slice(1)}function d(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,n.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,s.parseUrl)(t),i=r.pathname;i&&(i=l(i));let a=r.href;a&&(a=l(a));let o=r.hostname;o&&(o=l(o));let u=r.hash;return u&&(u=l(u)),{...r,pathname:i,hostname:o,href:a,hash:u}}function h(e){let t,r,n=Object.assign({},e.query),s=d(e),{hostname:o,query:u}=s,h=s.pathname;s.hash&&(h=""+h+s.hash);let f=[],p=[];for(let e of((0,i.pathToRegexp)(h,p),p))f.push(e.name);if(o){let e=[];for(let t of((0,i.pathToRegexp)(o,e),e))f.push(t.name)}let _=(0,i.compile)(h,{validate:!1});for(let[r,n]of(o&&(t=(0,i.compile)(o,{validate:!1})),Object.entries(u)))Array.isArray(n)?u[r]=n.map(t=>c(l(t),e.params)):"string"==typeof n&&(u[r]=c(l(n),e.params));let g=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!g.some(e=>f.includes(e)))for(let t of g)t in u||(u[t]=e.params[t]);if((0,a.isInterceptionRouteAppPath)(h))for(let t of h.split("/")){let r=a.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[i,n]=(r=_(e.params)).split("#",2);t&&(s.hostname=t(e.params)),s.pathname=i,s.hash=(n?"#":"")+(n||""),delete s.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return s.query={...n,...s.query},{newUrl:r,destQuery:u,parsedDestination:s}}},84294:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{Router:function(){return s.default},createRouter:function(){return _},default:function(){return f},makePublicRouterInstance:function(){return g},useRouter:function(){return p},withRouter:function(){return l.default}});let i=r(64252),n=i._(r(14232)),s=i._(r(68276)),a=r(99948),o=i._(r(66240)),l=i._(r(88147)),u={router:null,readyCallbacks:[],ready(e){if(this.router)return e();this.readyCallbacks.push(e)}},c=["pathname","route","query","asPath","components","isFallback","basePath","locale","locales","defaultLocale","isReady","isPreview","isLocaleDomain","domainLocales"],d=["push","replace","reload","back","prefetch","beforePopState"];function h(){if(!u.router)throw Object.defineProperty(Error('No router instance found.\nYou should only use "next/router" on the client side of your app.\n'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return u.router}Object.defineProperty(u,"events",{get:()=>s.default.events}),c.forEach(e=>{Object.defineProperty(u,e,{get:()=>h()[e]})}),d.forEach(e=>{u[e]=function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];return h()[e](...r)}}),["routeChangeStart","beforeHistoryChange","routeChangeComplete","routeChangeError","hashChangeStart","hashChangeComplete"].forEach(e=>{u.ready(()=>{s.default.events.on(e,function(){for(var t=arguments.length,r=Array(t),i=0;i<t;i++)r[i]=arguments[i];let n="on"+e.charAt(0).toUpperCase()+e.substring(1);if(u[n])try{u[n](...r)}catch(e){console.error("Error when running the Router event: "+n),console.error((0,o.default)(e)?e.message+"\n"+e.stack:e+"")}})})});let f=u;function p(){let e=n.default.useContext(a.RouterContext);if(!e)throw Object.defineProperty(Error("NextRouter was not mounted. https://nextjs.org/docs/messages/next-router-not-mounted"),"__NEXT_ERROR_CODE",{value:"E509",enumerable:!1,configurable:!0});return e}function _(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return u.router=new s.default(...t),u.readyCallbacks.forEach(e=>e()),u.readyCallbacks=[],u.router}function g(e){let t={};for(let r of c){if("object"==typeof e[r]){t[r]=Object.assign(Array.isArray(e[r])?[]:{},e[r]);continue}t[r]=e[r]}return t.events=s.default.events,d.forEach(r=>{t[r]=function(){for(var t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];return e[r](...i)}}),t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84547:(e,t,r)=>{"use strict";let i;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return a},isEqualNode:function(){return s}});let n=r(79611);function s(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let r=t.getAttribute("nonce");if(r&&!e.getAttribute("nonce")){let i=t.cloneNode(!0);return i.setAttribute("nonce",""),i.nonce=r,r===e.nonce&&e.isEqualNode(i)}}return e.isEqualNode(t)}function a(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"])if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;else e.props.href=e.props["data-href"],e.props["data-href"]=void 0;let r=t[e.type]||[];r.push(e),t[e.type]=r});let r=t.title?t.title[0]:null,n="";if(r){let{children:e}=r.props;n="string"==typeof e?e:Array.isArray(e)?e.join(""):""}n!==document.title&&(document.title=n),["meta","base","link","style","script"].forEach(e=>{i(e,t[e]||[])})}}}i=(e,t)=>{let r=document.querySelector("head");if(!r)return;let i=new Set(r.querySelectorAll(""+e+"[data-next-head]"));if("meta"===e){let e=r.querySelector("meta[charset]");null!==e&&i.add(e)}let a=[];for(let e=0;e<t.length;e++){let r=function(e){let{type:t,props:r}=e,i=document.createElement(t);(0,n.setAttributesFromProps)(i,r);let{children:s,dangerouslySetInnerHTML:a}=r;return a?i.innerHTML=a.__html||"":s&&(i.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):""),i}(t[e]);r.setAttribute("data-next-head","");let o=!0;for(let e of i)if(s(e,r)){i.delete(e),o=!1;break}o&&a.push(r)}for(let e of i){var o;null==(o=e.parentNode)||o.removeChild(e)}for(let e of a)"meta"===e.tagName.toLowerCase()&&null!==e.getAttribute("charset")&&r.prepend(e),r.appendChild(e)},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},84980:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"formatNextPathnameInfo",{enumerable:!0,get:function(){return o}});let i=r(54902),n=r(82889),s=r(67952),a=r(46711);function o(e){let t=(0,a.addLocale)(e.pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix);return(e.buildId||!e.trailingSlash)&&(t=(0,i.removeTrailingSlash)(t)),e.buildId&&(t=(0,s.addPathSuffix)((0,n.addPathPrefix)(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=(0,n.addPathPrefix)(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:(0,s.addPathSuffix)(t,"/"):(0,i.removeTrailingSlash)(t)}},85419:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},85519:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return n}});let i=r(2746);function n(e){let{re:t,groups:r}=e;return e=>{let n=t.exec(e);if(!n)return!1;let s=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new i.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},a={};for(let[e,t]of Object.entries(r)){let r=n[t.pos];void 0!==r&&(t.repeat?a[e]=r.split("/").map(e=>s(e)):a[e]=s(r))}return a}}},86582:e=>{"use strict";e.exports=["chrome 64","edge 79","firefox 67","opera 51","safari 12"]},87407:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return i.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return s},getBotType:function(){return l},isBot:function(){return o}});let i=r(92455),n=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,s=i.HTML_LIMITED_BOT_UA_RE.source;function a(e){return i.HTML_LIMITED_BOT_UA_RE.test(e)}function o(e){return n.test(e)||a(e)}function l(e){return n.test(e)?"dom":a(e)?"html":void 0}},88147:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r(64252);let i=r(37876);r(14232);let n=r(84294);function s(e){function t(t){return(0,i.jsx)(e,{router:(0,n.useRouter)(),...t})}return t.getInitialProps=e.getInitialProps,t.origGetInitialProps=e.origGetInitialProps,t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88213:(e,t)=>{"use strict";function r(e){return new URL(e,"http://n").searchParams}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"asPathToSearchParams",{enumerable:!0,get:function(){return r}})},88365:(e,t,r)=>{"use strict";function i(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(i=function(e){return e?r:t})(e)}function n(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=i(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},s=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var o=s?Object.getOwnPropertyDescriptor(e,a):null;o&&(o.get||o.set)?Object.defineProperty(n,a,o):n[a]=e[a]}return n.default=e,r&&r.set(e,n),n}r.r(t),r.d(t,{_:()=>n})},90472:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let i=r(64252),n=r(37876),s=i._(r(14232)),a=r(2746);async function o(e){let{Component:t,ctx:r}=e;return{pageProps:await (0,a.loadGetInitialProps)(t,r)}}class l extends s.default.Component{render(){let{Component:e,pageProps:t}=this.props;return(0,n.jsx)(e,{...t})}}l.origGetInitialProps=o,l.getInitialProps=o,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},92455:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return r}});let r=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},93090:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Portal",{enumerable:!0,get:function(){return s}});let i=r(14232),n=r(98477),s=e=>{let{children:t,type:r}=e,[s,a]=(0,i.useState)(null);return(0,i.useEffect)(()=>{let e=document.createElement(r);return document.body.appendChild(e),a(e),()=>{document.body.removeChild(e)}},[r]),s?(0,n.createPortal)(t,s):null};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94069:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BloomFilter",{enumerable:!0,get:function(){return r}});class r{static from(e,t){void 0===t&&(t=1e-4);let i=new r(e.length,t);for(let t of e)i.add(t);return i}export(){return{numItems:this.numItems,errorRate:this.errorRate,numBits:this.numBits,numHashes:this.numHashes,bitArray:this.bitArray}}import(e){this.numItems=e.numItems,this.errorRate=e.errorRate,this.numBits=e.numBits,this.numHashes=e.numHashes,this.bitArray=e.bitArray}add(e){this.getHashValues(e).forEach(e=>{this.bitArray[e]=1})}contains(e){return this.getHashValues(e).every(e=>this.bitArray[e])}getHashValues(e){let t=[];for(let r=1;r<=this.numHashes;r++){let i=function(e){let t=0;for(let r=0;r<e.length;r++)t=Math.imul(t^e.charCodeAt(r),0x5bd1e995),t^=t>>>13,t=Math.imul(t,0x5bd1e995);return t>>>0}(""+e+r)%this.numBits;t.push(i)}return t}constructor(e,t=1e-4){this.numItems=e,this.errorRate=t,this.numBits=Math.ceil(-(e*Math.log(t))/(Math.log(2)*Math.log(2))),this.numHashes=Math.ceil(this.numBits/e*Math.log(2)),this.bitArray=Array(this.numBits).fill(0)}}},94569:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reportGlobalError",{enumerable:!0,get:function(){return r}});let r="function"==typeof reportError?reportError:e=>{globalThis.console.error(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95214:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return _},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return d},parseParameter:function(){return l}});let i=r(39308),n=r(37188),s=r(51924),a=r(54902),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?u(t[2]):u(e)}function u(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e,t,r){let i={},l=1,c=[];for(let d of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.find(e=>d.startsWith(e)),a=d.match(o);if(e&&a&&a[2]){let{key:t,optional:r,repeat:n}=u(a[2]);i[t]={pos:l++,repeat:n,optional:r},c.push("/"+(0,s.escapeStringRegexp)(e)+"([^/]+?)")}else if(a&&a[2]){let{key:e,repeat:t,optional:n}=u(a[2]);i[e]={pos:l++,repeat:t,optional:n},r&&a[1]&&c.push("/"+(0,s.escapeStringRegexp)(a[1]));let o=t?n?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&a[1]&&(o=o.substring(1)),c.push(o)}else c.push("/"+(0,s.escapeStringRegexp)(d));t&&a&&a[3]&&c.push((0,s.escapeStringRegexp)(a[3]))}return{parameterizedRoute:c.join(""),groups:i}}function d(e,t){let{includeSuffix:r=!1,includePrefix:i=!1,excludeOptionalTrailingSlash:n=!1}=void 0===t?{}:t,{parameterizedRoute:s,groups:a}=c(e,r,i),o=s;return n||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:a}}function h(e){let t,{interceptionMarker:r,getSafeRouteKey:i,segment:n,routeKeys:a,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:c,optional:d,repeat:h}=u(n),f=c.replace(/\W/g,"");o&&(f=""+o+f);let p=!1;(0===f.length||f.length>30)&&(p=!0),isNaN(parseInt(f.slice(0,1)))||(p=!0),p&&(f=i());let _=f in a;o?a[f]=""+o+c:a[f]=c;let g=r?(0,s.escapeStringRegexp)(r):"";return t=_&&l?"\\k<"+f+">":h?"(?<"+f+">.+?)":"(?<"+f+">[^/]+?)",d?"(?:/"+g+t+")?":"/"+g+t}function f(e,t,r,l,u){let c,d=(c=0,()=>{let e="",t=++c;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),f={},p=[];for(let c of(0,a.removeTrailingSlash)(e).slice(1).split("/")){let e=n.INTERCEPTION_ROUTE_MARKERS.some(e=>c.startsWith(e)),a=c.match(o);if(e&&a&&a[2])p.push(h({getSafeRouteKey:d,interceptionMarker:a[1],segment:a[2],routeKeys:f,keyPrefix:t?i.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:u}));else if(a&&a[2]){l&&a[1]&&p.push("/"+(0,s.escapeStringRegexp)(a[1]));let e=h({getSafeRouteKey:d,segment:a[2],routeKeys:f,keyPrefix:t?i.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:u});l&&a[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,s.escapeStringRegexp)(c));r&&a&&a[3]&&p.push((0,s.escapeStringRegexp)(a[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:f}}function p(e,t){var r,i,n;let s=f(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(i=t.includePrefix)&&i,null!=(n=t.backreferenceDuplicateKeys)&&n),a=s.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(a+="(?:/)?"),{...d(e,t),namedRegex:"^"+a+"$",routeKeys:s.routeKeys}}function _(e,t){let{parameterizedRoute:r}=c(e,!1,!1),{catchAll:i=!0}=t;if("/"===r)return{namedRegex:"^/"+(i?".*":"")+"$"};let{namedParameterizedRoute:n}=f(e,!1,!1,!1,!1);return{namedRegex:"^"+n+(i?"(?:(/.*)?)":"")+"$"}}},96079:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},96292:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removePathPrefix",{enumerable:!0,get:function(){return n}});let i=r(73716);function n(e,t){if(!(0,i.pathHasPrefix)(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}},98069:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return s}});let i=r(85519),n=r(95214);function s(e,t,r){let s="",a=(0,n.getRouteRegex)(e),o=a.groups,l=(t!==e?(0,i.getRouteMatcher)(a)(t):"")||r;s=e;let u=Object.keys(o);return u.every(e=>{let t=l[e]||"",{repeat:r,optional:i}=o[e],n="["+(r?"...":"")+e+"]";return i&&(n=(t?"":"/")+"["+n+"]"),r&&!Array.isArray(t)&&(t=[t]),(i||e in l)&&(s=s.replace(n,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(s=""),{params:u,result:s}}},98422:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return s}});let i=r(78040),n=r(20541);function s(e){if(e.startsWith("/"))return(0,n.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,i.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},99341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let i=r(64252),n=r(37876),s=i._(r(14232)),a=i._(r(5679)),o={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function l(e){let{req:t,res:r,err:i}=e;return{statusCode:r&&r.statusCode?r.statusCode:i?i.statusCode:404,hostname:window.location.hostname}}let u={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class c extends s.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||o[e]||"An unexpected error has occurred";return(0,n.jsxs)("div",{style:u.error,children:[(0,n.jsx)(a.default,{children:(0,n.jsx)("title",{children:e?e+": "+r:"Application error: a client-side exception has occurred"})}),(0,n.jsxs)("div",{style:u.desc,children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,n.jsx)("h1",{className:"next-error-h1",style:u.h1,children:e}):null,(0,n.jsx)("div",{style:u.wrap,children:(0,n.jsxs)("h2",{style:u.h2,children:[this.props.title||e?r:(0,n.jsxs)(n.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,n.jsxs)(n.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}c.displayName="ErrorPage",c.getInitialProps=l,c.origGetInitialProps=l,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},99948:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return i}});let i=r(64252)._(r(14232)).default.createContext(null)}},e=>{var t=t=>e(e.s=t);e.O(0,[6593],()=>t(25842)),_N_E=e.O()}]);