"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6754],{5845:(e,t,n)=>{n.d(t,{i:()=>u});var r,l=n(12115),o=n(52712),i=(r||(r=n.t(l,2)))[" useInsertionEffect ".trim().toString()]||o.N;function u({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,u,a]=function({defaultProp:e,onChange:t}){let[n,r]=l.useState(e),o=l.useRef(n),u=l.useRef(t);return i(()=>{u.current=t},[t]),l.useEffect(()=>{o.current!==n&&(u.current?.(n),o.current=n)},[n,o]),[n,r,u]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,l.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&a.current?.(n)}else u(t)},[s,e,u,a])]}Symbol("RADIX:SYNC_STATE")},6101:(e,t,n)=>{n.d(t,{s:()=>i,t:()=>o});var r=n(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}function i(...e){return r.useCallback(o(...e),e)}},11275:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(12115),l=n(52712);function o(e){let[t,n]=r.useState(void 0);return(0,l.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,l;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,l=t.blockSize}else r=e.offsetWidth,l=e.offsetHeight;n({width:r,height:l})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},28905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(12115),l=n(6101),o=n(52712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[l,i]=r.useState(),a=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(a.current);c.current="mounted"===d?e:"none"},[d]),(0,o.N)(()=>{let t=a.current,n=s.current;if(n!==e){let r=c.current,l=u(t);e?f("MOUNT"):"none"===l||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==l?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,o.N)(()=>{if(l){var e;let t,n=null!=(e=l.ownerDocument.defaultView)?e:window,r=e=>{let r=u(a.current).includes(e.animationName);if(e.target===l&&r&&(f("ANIMATION_END"),!s.current)){let e=l.style.animationFillMode;l.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===l.style.animationFillMode&&(l.style.animationFillMode=e)})}},o=e=>{e.target===l&&(c.current=u(a.current))};return l.addEventListener("animationstart",o),l.addEventListener("animationcancel",r),l.addEventListener("animationend",r),()=>{n.clearTimeout(t),l.removeEventListener("animationstart",o),l.removeEventListener("animationcancel",r),l.removeEventListener("animationend",r)}}f("ANIMATION_END")},[l,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{a.current=e?getComputedStyle(e):null,i(e)},[])}}(t),a="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,l.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,l=r&&"isReactWarning"in r&&r.isReactWarning;return l?e.ref:(l=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof n||i.isPresent?r.cloneElement(a,{ref:s}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},46081:(e,t,n)=>{n.d(t,{A:()=>i,q:()=>o});var r=n(12115),l=n(95155);function o(e,t){let n=r.createContext(t),o=e=>{let{children:t,...o}=e,i=r.useMemo(()=>o,Object.values(o));return(0,l.jsx)(n.Provider,{value:i,children:t})};return o.displayName=e+"Provider",[o,function(l){let o=r.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function i(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let l=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:l}}),[n,l])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),u=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,s=n?.[e]?.[u]||i,c=r.useMemo(()=>a,Object.values(a));return(0,l.jsx)(s.Provider,{value:c,children:o})};return a.displayName=t+"Provider",[a,function(n,l){let a=l?.[e]?.[u]||i,s=r.useContext(a);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=n.reduce((t,{useScope:n,scopeName:r})=>{let l=n(e)[`__scope${r}`];return{...t,...l}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return n.scopeName=t.scopeName,n}(o,...t)]}},52712:(e,t,n)=>{n.d(t,{N:()=>l});var r=n(12115),l=globalThis?.document?r.useLayoutEffect:()=>{}},63655:(e,t,n)=>{n.d(t,{hO:()=>a,sG:()=>u});var r=n(12115),l=n(47650),o=n(99708),i=n(95155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,o.TL)(`Primitive.${t}`),l=r.forwardRef((e,r)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l?n:t,{...o,ref:r})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function a(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},74466:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=r.$,i=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:u}=t,a=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],r=null==u?void 0:u[e];if(null===t)return null;let o=l(t)||l(r);return i[e][o]}),s=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return o(e,a,null==t||null==(r=t.compoundVariants)?void 0:r.reduce((e,t)=>{let{class:n,className:r,...l}=t;return Object.entries(l).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...u,...s}[t]):({...u,...s})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},85185:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},99708:(e,t,n)=>{n.d(t,{DX:()=>u,Dc:()=>s,TL:()=>i});var r=n(12115),l=n(6101),o=n(95155);function i(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var i;let e,u,a=(i=n,(u=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(u=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),s=function(e,t){let n={...t};for(let r in t){let l=e[r],o=t[r];/^on[A-Z]/.test(r)?l&&o?n[r]=(...e)=>{let t=o(...e);return l(...e),t}:l&&(n[r]=l):"style"===r?n[r]={...l,...o}:"className"===r&&(n[r]=[l,o].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?(0,l.t)(t,a):a),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:l,...i}=e,u=r.Children.toArray(l),a=u.find(c);if(a){let e=a.props.children,l=u.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...i,ref:n,children:l})});return n.displayName=`${e}.Slot`,n}var u=i("Slot"),a=Symbol("radix.slottable");function s(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function c(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);