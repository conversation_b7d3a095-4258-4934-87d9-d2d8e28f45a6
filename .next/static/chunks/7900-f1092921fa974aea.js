"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7900],{5196:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},13052:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},42355:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},45503:(e,t,n)=>{n.d(t,{Z:()=>s});var r=n(12115);function s(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},60760:(e,t,n)=>{n.d(t,{N:()=>k});var r=n(95155),s=n(12115),o=n(90869),l=n(82885),i=n(97494),u=n(80845),c=n(27351),a=n(51508);class d extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,n=(0,c.s)(e)&&e.offsetWidth||0,r=this.props.sizeRef.current;r.height=t.offsetHeight||0,r.width=t.offsetWidth||0,r.top=t.offsetTop,r.left=t.offsetLeft,r.right=n-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f(e){let{children:t,isPresent:n,anchorX:o,root:l}=e,i=(0,s.useId)(),u=(0,s.useRef)(null),c=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:f}=(0,s.useContext)(a.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:t,top:r,left:s,right:a}=c.current;if(n||!u.current||!e||!t)return;u.current.dataset.motionPopId=i;let d=document.createElement("style");f&&(d.nonce=f);let p=null!=l?l:document.head;return p.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(i,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            ").concat("left"===o?"left: ".concat(s):"right: ".concat(a),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{p.contains(d)&&p.removeChild(d)}},[n]),(0,r.jsx)(d,{isPresent:n,childRef:u,sizeRef:c,children:s.cloneElement(t,{ref:u})})}let p=e=>{let{children:t,initial:n,isPresent:o,onExitComplete:i,custom:c,presenceAffectsLayout:a,mode:d,anchorX:p,root:m}=e,v=(0,l.M)(h),x=(0,s.useId)(),k=!0,g=(0,s.useMemo)(()=>(k=!1,{id:x,initial:n,isPresent:o,custom:c,onExitComplete:e=>{for(let t of(v.set(e,!0),v.values()))if(!t)return;i&&i()},register:e=>(v.set(e,!1),()=>v.delete(e))}),[o,v,i]);return a&&k&&(g={...g}),(0,s.useMemo)(()=>{v.forEach((e,t)=>v.set(t,!1))},[o]),s.useEffect(()=>{o||v.size||!i||i()},[o]),"popLayout"===d&&(t=(0,r.jsx)(f,{isPresent:o,anchorX:p,root:m,children:t})),(0,r.jsx)(u.t.Provider,{value:g,children:t})};function h(){return new Map}var m=n(32082);let v=e=>e.key||"";function x(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}let k=e=>{let{children:t,custom:n,initial:u=!0,onExitComplete:c,presenceAffectsLayout:a=!0,mode:d="sync",propagate:f=!1,anchorX:h="left",root:k}=e,[g,y]=(0,m.xQ)(f),E=(0,s.useMemo)(()=>x(t),[t]),b=f&&!g?[]:E.map(v),w=(0,s.useRef)(!0),C=(0,s.useRef)(E),R=(0,l.M)(()=>new Map),[j,P]=(0,s.useState)(E),[M,A]=(0,s.useState)(E);(0,i.E)(()=>{w.current=!1,C.current=E;for(let e=0;e<M.length;e++){let t=v(M[e]);b.includes(t)?R.delete(t):!0!==R.get(t)&&R.set(t,!1)}},[M,b.length,b.join("-")]);let I=[];if(E!==j){let e=[...E];for(let t=0;t<M.length;t++){let n=M[t],r=v(n);b.includes(r)||(e.splice(t,0,n),I.push(n))}return"wait"===d&&I.length&&(e=I),A(x(e)),P(E),null}let{forceRender:L}=(0,s.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:M.map(e=>{let t=v(e),s=(!f||!!g)&&(E===M||b.includes(t));return(0,r.jsx)(p,{isPresent:s,initial:(!w.current||!!u)&&void 0,custom:n,presenceAffectsLayout:a,mode:d,root:k,onExitComplete:s?void 0:()=>{if(!R.has(t))return;R.set(t,!0);let e=!0;R.forEach(t=>{t||(e=!1)}),e&&(null==L||L(),A(C.current),f&&(null==y||y()),c&&c())},anchorX:h,children:e},t)})})}},76981:(e,t,n)=>{n.d(t,{C1:()=>w,bL:()=>E});var r=n(12115),s=n(6101),o=n(46081),l=n(85185),i=n(5845),u=n(45503),c=n(11275),a=n(28905),d=n(63655),f=n(95155),p="Checkbox",[h,m]=(0,o.A)(p),[v,x]=h(p);function k(e){let{__scopeCheckbox:t,checked:n,children:s,defaultChecked:o,disabled:l,form:u,name:c,onCheckedChange:a,required:d,value:h="on",internal_do_not_use_render:m}=e,[x,k]=(0,i.i)({prop:n,defaultProp:null!=o&&o,onChange:a,caller:p}),[g,y]=r.useState(null),[E,b]=r.useState(null),w=r.useRef(!1),C=!g||!!u||!!g.closest("form"),R={checked:x,disabled:l,setChecked:k,control:g,setControl:y,name:c,form:u,value:h,hasConsumerStoppedPropagationRef:w,required:d,defaultChecked:!j(o)&&o,isFormControl:C,bubbleInput:E,setBubbleInput:b};return(0,f.jsx)(v,{scope:t,...R,children:"function"==typeof m?m(R):s})}var g="CheckboxTrigger",y=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,onKeyDown:o,onClick:i,...u}=e,{control:c,value:a,disabled:p,checked:h,required:m,setControl:v,setChecked:k,hasConsumerStoppedPropagationRef:y,isFormControl:E,bubbleInput:b}=x(g,n),w=(0,s.s)(t,v),C=r.useRef(h);return r.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>k(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,k]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":j(h)?"mixed":h,"aria-required":m,"data-state":P(h),"data-disabled":p?"":void 0,disabled:p,value:a,...u,ref:w,onKeyDown:(0,l.m)(o,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(i,e=>{k(e=>!!j(e)||!e),b&&E&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});y.displayName=g;var E=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:r,checked:s,defaultChecked:o,required:l,disabled:i,value:u,onCheckedChange:c,form:a,...d}=e;return(0,f.jsx)(k,{__scopeCheckbox:n,checked:s,defaultChecked:o,disabled:i,required:l,onCheckedChange:c,name:r,form:a,value:u,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(y,{...d,ref:t,__scopeCheckbox:n}),r&&(0,f.jsx)(R,{__scopeCheckbox:n})]})}})});E.displayName=p;var b="CheckboxIndicator",w=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...s}=e,o=x(b,n);return(0,f.jsx)(a.C,{present:r||j(o.checked)||!0===o.checked,children:(0,f.jsx)(d.sG.span,{"data-state":P(o.checked),"data-disabled":o.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var C="CheckboxBubbleInput",R=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,...o}=e,{control:l,hasConsumerStoppedPropagationRef:i,checked:a,defaultChecked:p,required:h,disabled:m,name:v,value:k,form:g,bubbleInput:y,setBubbleInput:E}=x(C,n),b=(0,s.s)(t,E),w=(0,u.Z)(a),R=(0,c.X)(l);r.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(w!==a&&e){let n=new Event("click",{bubbles:t});y.indeterminate=j(a),e.call(y,!j(a)&&a),y.dispatchEvent(n)}},[y,w,a,i]);let P=r.useRef(!j(a)&&a);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:P.current,required:h,disabled:m,name:v,value:k,form:g,...o,tabIndex:-1,ref:b,style:{...o.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function j(e){return"indeterminate"===e}function P(e){return j(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=C}}]);