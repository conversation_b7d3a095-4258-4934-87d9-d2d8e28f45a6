"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5885],{14989:(e,t,s)=>{s.d(t,{a8:()=>n});class i{getMockProgressSteps(e){return[{id:"1",message:"Analyzing your idea...",completed:!1,timestamp:Date.now()},{id:"2",message:"Generating project structure...",completed:!1,timestamp:Date.now()},{id:"3",message:"Setting up development environment...",completed:!1,timestamp:Date.now()},{id:"4",message:"Creating initial codebase...",completed:!1,timestamp:Date.now()},{id:"5",message:"Configuring project settings...",completed:!1,timestamp:Date.now()},{id:"6",message:"Finalizing project setup...",completed:!1,timestamp:Date.now()}]}startProjectCreation(e,t){this.isActive&&this.stop(),this.isActive=!0;let s=this.getMockProgressSteps(e);this.dispatchEvent("setup",{totalSteps:s.length});let i=1e3/s.length;s.forEach((a,n)=>{let r=setTimeout(()=>{if(!this.isActive)return;this.dispatchEvent("progress",a);let i=setTimeout(()=>{if(!this.isActive)return;let i={...a,completed:!0,timestamp:Date.now()};if(this.dispatchEvent("progress",i),this.dispatchEvent("step_complete",{stepIndex:n}),n===s.length-1){let s=setTimeout(()=>{this.isActive&&this.dispatchEvent("complete",{projectId:"proj_".concat(Date.now()),name:t||this.generateProjectName(e),description:e})},200);this.timeouts.push(s)}},200);this.timeouts.push(i)},n*i);this.timeouts.push(r)})}generateProjectName(e){let t=e.toLowerCase().split(" ").filter(e=>e.length>2).slice(0,3);return 0===t.length?"New Project":t.map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")+" Project"}addEventListener(e,t){this.eventSource.addEventListener(e,t)}removeEventListener(e,t){this.eventSource.removeEventListener(e,t)}dispatchEvent(e,t){let s=new CustomEvent("message",{detail:{type:e,data:t}});this.eventSource.dispatchEvent(s)}stop(){this.isActive=!1,this.timeouts.forEach(e=>clearTimeout(e)),this.timeouts=[]}simulateError(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"Something went wrong during project creation";this.stop(),this.dispatchEvent("error",{error:e})}constructor(){this.timeouts=[],this.isActive=!1,this.eventSource=new EventTarget}}let a=new i,n=()=>({startCreation:(e,t)=>{a.startProjectCreation(e,t)},stopCreation:()=>{a.stop()},addEventListener:e=>{a.addEventListener("message",e)},removeEventListener:e=>{a.removeEventListener("message",e)}})},45885:(e,t,s)=>{s.d(t,{x:()=>p});var i=s(95155),a=s(92236),n=s(80733),r=s(60760),o=s(38274),l=s(63352),c=s(14989),d=s(35695),u=s(12115);function m(){let{ideaText:e,setQuestionnaireAnswer:t,completeQuestionnaire:s,createdProject:r,questionnaireData:m,setCreatedProject:p,setCurrentProgress:h,setTotalSteps:g,incrementCompletedSteps:x,setCurrentStep:v}=(0,n.h)(),[b,j]=(0,u.useState)(!1),[y,f]=(0,u.useState)(!1),[w,N]=(0,u.useState)(null),[C,E]=(0,u.useState)(!1),P=(0,d.useRouter)(),{startCreation:k,addEventListener:S,removeEventListener:T}=(0,c.a8)();(0,u.useEffect)(()=>{m.projectName&&m.projectName.trim()&&!y&&(console.log("Starting project creation with name:",m.projectName),N(Date.now()),k(e,m.projectName),f(!0))},[m.projectName,e,k,y]),(0,u.useEffect)(()=>{let e=e=>{let{type:t,data:s}=e.detail;switch(t){case"setup":g(s.totalSteps);break;case"progress":h(s);break;case"step_complete":x();break;case"complete":console.log("Project creation completed:",s),p({id:s.projectId,name:s.name,description:s.description}),v("completed");break;case"error":console.error("Project creation error:",s.error)}};return S(e),()=>{T(e)}},[S,T,p,h,g,x,v]),(0,u.useEffect)(()=>{b&&r&&setTimeout(()=>{P.push("/user-dashboard")},2e3)},[b,r,P]);let z={questions:[{id:"isFirstTimeFounder",title:"Are you a first-time founder?",subtitle:"This helps us tune the tone of our agents",type:"single",options:[{value:!0,label:"Yes"},{value:!1,label:"No"}]},{id:"stage",title:"What stage are you at?",subtitle:"Understanding your current progress",type:"single",options:[{value:"idea",label:"Just an Idea"},{value:"building",label:"Building"},{value:"launched",label:"Launched & Testing"},{value:"revenue",label:"Generating Revenue"}]},{id:"timeWorking",title:"How long have you been working on this?",subtitle:"This helps us understand your timeline",type:"single",options:[{value:"<1day",label:"Less than 1 day"},{value:"1-7days",label:"1-7 days"},{value:"7-30days",label:"7-30 days"},{value:"30-90days",label:"30-90 days"},{value:"90+days",label:"90+ days"}]},{id:"projectName",title:"What would you like to name your project?",subtitle:"Choose a name that represents your vision",type:"text",placeholder:"Enter your project name..."}],onComplete:i=>{Object.entries(i).forEach(e=>{let[s,i]=e;t(s,i)});let a=Date.now(),n=w?a-w:0;console.log("Questionnaire completed in ".concat(n,"ms")),n>=1e3?(console.log("Project should be ready, simulating patch update"),setTimeout(()=>{p({id:"project_".concat(Date.now()),name:i.projectName||"My Project",description:e}),E(!0)},200)):(console.log("Project still creating, showing loading"),j(!0)),s()},onStepChange:(s,i)=>{Object.entries(i).forEach(e=>{let[s,i]=e;t(s,i)}),i.projectName&&i.projectName.trim()&&!y&&(console.log("Starting project creation with name:",i.projectName),N(Date.now()),k(e,i.projectName),f(!0))}};return b&&!r?(0,i.jsxs)("div",{className:"fixed inset-0 z-50 bg-background overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20"}),(0,i.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,i.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(o.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},transition:{duration:.5},className:"mb-8",children:(0,i.jsx)(a.g,{size:64,animated:!0,showText:!1,className:"mx-auto"})}),(0,i.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"space-y-4",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-[#166534]",children:"Creating your project..."}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"We're setting up everything for you"})]})]})})]}):C&&r?(0,i.jsxs)("div",{className:"fixed inset-0 z-50 bg-background overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20"}),(0,i.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,i.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:200,damping:20},className:"w-16 h-16 mx-auto mb-6 rounded-full bg-[#166534] flex items-center justify-center",children:(0,i.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},className:"text-white text-2xl",children:"✓"})}),(0,i.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"space-y-6",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-[#166534]",children:"Project Created!"}),(0,i.jsxs)("p",{className:"text-muted-foreground",children:[r.name," is ready to go"]})]}),(0,i.jsx)(o.P.button,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},onClick:()=>P.push("/user-dashboard"),className:"bg-[#166534] hover:bg-[#166534]/90 text-white px-8 py-3 rounded-lg font-medium transition-colors",children:"Go to Dashboard"})]})]})})]}):b&&r?(0,i.jsxs)("div",{className:"fixed inset-0 z-50 bg-background overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-background via-background to-muted/20"}),(0,i.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,i.jsx)("div",{className:"relative z-10 flex items-center justify-center min-h-screen",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:200,damping:20},className:"w-16 h-16 mx-auto mb-6 rounded-full bg-[#166534] flex items-center justify-center",children:(0,i.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},className:"text-white text-2xl",children:"✓"})}),(0,i.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"space-y-4",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-[#166534]",children:"Your project is ready!"}),(0,i.jsxs)("p",{className:"text-muted-foreground",children:[r.name," has been created successfully"]}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Redirecting to dashboard..."})]})]})})]}):(0,i.jsx)(l.C,{config:z})}function p(){let{currentStep:e,currentProgress:t,totalSteps:s,completedSteps:l,showLogo:c,logoRotating:d,showGlow:p}=(0,n.h)();return((0,u.useEffect)(()=>(document.body.style.overflow="hidden",()=>{document.body.style.overflow="unset"}),[]),(0,u.useEffect)(()=>{if("completed"===e){console.log("Project completed, stopping logo animation...");let{setLogoRotating:e}=n.h.getState();e(!1)}},[e]),"idle"===e)?null:"questionnaire"===e?(0,i.jsx)(m,{}):(0,i.jsxs)("div",{className:"fixed inset-0 z-50 bg-background overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,i.jsxs)("div",{className:"relative z-30 flex flex-col items-center justify-center min-h-screen p-8",children:[(0,i.jsx)(r.N,{children:c&&(0,i.jsxs)(o.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.5},className:"relative mb-12",children:[p&&(0,i.jsxs)(o.P.div,{initial:{opacity:0},animate:{opacity:1},className:"absolute inset-0 -m-8",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-[#166534]/20 rounded-full blur-3xl animate-pulse"}),(0,i.jsx)("div",{className:"absolute inset-0 bg-[#22c55e]/10 rounded-full blur-2xl animate-pulse"})]}),(0,i.jsx)("div",{className:"relative z-10",children:(0,i.jsx)(a.g,{size:120,animated:d})})]})}),"completed"===e?(0,i.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"text-center space-y-4",children:[(0,i.jsx)("p",{className:"text-lg font-semibold text-[#166534]",children:"Project is created"}),(0,i.jsx)(o.P.button,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.2,duration:.3},whileHover:{scale:1.05},whileTap:{scale:.95},className:"bg-[#166534] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#166534]/90 transition-all duration-200",onClick:()=>{window.location.href="/user-dashboard"},children:"Go to Dashboard"})]}):(0,i.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3,duration:.5},className:"w-full max-w-md space-y-4",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-center text-foreground mb-8",children:"Creating Your Project"}),(0,i.jsx)("div",{className:"space-y-3",children:(0,i.jsx)(r.N,{mode:"wait",children:t?(0,i.jsxs)(o.P.div,{initial:{opacity:0,x:-20,scale:.95},animate:{opacity:1,x:0,scale:1},exit:{opacity:0,x:20,scale:.95},transition:{duration:.4,ease:"easeOut"},className:"flex items-center gap-4 p-6 rounded-xl backdrop-blur border-2 transition-all duration-300 ".concat(t.completed?"bg-[#166534]/10 border-[#166534]/30 shadow-lg shadow-[#166534]/20":"bg-card/50 border-border"),children:[(0,i.jsx)("div",{className:"flex-shrink-0",children:t.completed?(0,i.jsx)(o.P.div,{initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500,damping:30,delay:.1},className:"w-7 h-7 bg-[#166534] rounded-full flex items-center justify-center",children:(0,i.jsx)("svg",{className:"w-4 h-4 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:(0,i.jsx)("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})}):(0,i.jsx)("div",{className:"w-7 h-7 border-2 border-muted-foreground rounded-full animate-pulse"})}),(0,i.jsx)("span",{className:"text-lg font-medium transition-colors duration-300 ".concat(t.completed?"text-[#166534] font-semibold":"text-foreground"),children:t.message})]},t.id):(0,i.jsx)(o.P.div,{initial:{opacity:0},animate:{opacity:1},className:"flex items-center justify-center p-6 rounded-xl bg-card/30 backdrop-blur border border-dashed",children:(0,i.jsx)("span",{className:"text-muted-foreground",children:"Initializing project creation..."})})})}),(0,i.jsxs)("div",{className:"mt-6",children:[(0,i.jsxs)("div",{className:"flex justify-between text-sm text-muted-foreground mb-2",children:[(0,i.jsx)("span",{children:"Progress"}),(0,i.jsxs)("span",{children:[l," / ",s]})]}),(0,i.jsx)("div",{className:"w-full bg-[var(--progress-bg)] border border-[var(--progress-border)] rounded-full h-2",children:(0,i.jsx)(o.P.div,{initial:{width:0},animate:{width:"".concat(s>0?l/s*100:0,"%")},transition:{duration:.5,ease:"easeInOut"},className:"bg-[var(--progress-fill)] h-2 rounded-full"})})]})]})]})]})}},80733:(e,t,s)=>{s.d(t,{h:()=>r});var i=s(65453),a=s(46786);let n={ideaText:"",showQuestionnaire:!1,currentQuestionIndex:0,questionnaireData:{isFirstTimeFounder:null,stage:null,timeWorking:null,projectName:""},questionnaireCompleted:!1,isCreating:!1,currentStep:"idle",currentProgress:null,totalSteps:0,completedSteps:0,showLogo:!0,logoRotating:!1,showGlow:!1,circleExpanding:!1,showCompletionMessage:!1,createdProject:null,error:null},r=(0,i.v)()((0,a.lt)((e,t)=>({...n,setIdeaText:t=>e({ideaText:t}),startQuestionnaire:()=>{e({showQuestionnaire:!0,currentStep:"questionnaire",currentQuestionIndex:0,questionnaireCompleted:!1})},setQuestionnaireAnswer:(t,s)=>{e(e=>({questionnaireData:{...e.questionnaireData,[t]:s}}))},nextQuestion:()=>{e(e=>({currentQuestionIndex:Math.min(e.currentQuestionIndex+1,3)}))},previousQuestion:()=>{e(e=>({currentQuestionIndex:Math.max(e.currentQuestionIndex-1,0)}))},completeQuestionnaire:()=>{t(),e({questionnaireCompleted:!0,showQuestionnaire:!1})},startCreation:()=>{e({isCreating:!0,currentStep:"creating",currentProgress:null,totalSteps:0,completedSteps:0,logoRotating:!0,showGlow:!0,error:null})},setCurrentProgress:t=>{e({currentProgress:t})},setTotalSteps:t=>{e({totalSteps:t})},incrementCompletedSteps:()=>{e(e=>({completedSteps:e.completedSteps+1}))},setCurrentStep:t=>{e({currentStep:t})},setLogoRotating:t=>e({logoRotating:t}),setShowGlow:t=>e({showGlow:t}),setCircleExpanding:t=>e({circleExpanding:t}),setShowCompletionMessage:t=>e({showCompletionMessage:t}),setCreatedProject:t=>{e({createdProject:t})},reset:()=>e(n),setError:t=>e({error:t})}),{name:"project-creation-store"}))}}]);