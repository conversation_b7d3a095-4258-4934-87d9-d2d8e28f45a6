(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5479],{636:(e,t,r)=>{"use strict";r.r(t),r.d(t,{KeylessCookieSync:()=>i});var n=r(35695),o=r(12115),a=r(89331);function i(e){var t;let i=(null==(t=(0,n.useSelectedLayoutSegments)()[0])?void 0:t.startsWith("/_not-found"))||!1;return(0,o.useEffect)(()=>{a.I&&!i&&r.e(4582).then(r.bind(r,92201)).then(t=>t.syncKeylessConfigAction({...e,returnUrl:window.location.href}))},[i]),e.children}},671:(e,t,r)=>{"use strict";r.d(t,{AuthenticateWithRedirectCallback:()=>n.B$,ClerkDegraded:()=>n.wF,ClerkFailed:()=>n.lT,ClerkLoaded:()=>n.z0,ClerkLoading:()=>n.A0,RedirectToCreateOrganization:()=>n.rm,RedirectToOrganizationProfile:()=>n.m2,RedirectToSignIn:()=>n.W5,RedirectToSignUp:()=>n.mO,RedirectToTask:()=>n.Xn,RedirectToUserProfile:()=>n.eG});var n=r(48879);r(38572)},4579:(e,t,r)=>{"use strict";r.d(t,{zz:()=>n.zz});var n=r(30852);r(85649)},10255:(e,t,r)=>{"use strict";function n(e){let{moduleIds:t}=e;return null}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return n}}),r(95155),r(47650),r(85744),r(20589)},15256:(e,t,r)=>{"use strict";r.d(t,{useAuth:()=>o.d,useClerk:()=>n.ho,useEmailLink:()=>n.ui,useOrganization:()=>n.Z5,useOrganizationList:()=>n.D_,useReverification:()=>n.Wp,useSession:()=>n.wV,useSessionList:()=>n.g7,useSignIn:()=>n.go,useSignUp:()=>n.yC,useUser:()=>n.Jd});var n=r(48879);r(23789);var o=r(27016)},17828:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorageInstance",{enumerable:!0,get:function(){return n}});let n=(0,r(64054).createAsyncLocalStorage)()},27016:(e,t,r)=>{"use strict";r.d(t,{PromisifiedAuthProvider:()=>s,d:()=>u});var n=r(48879),o=r(38572),a=r(35583),i=r(12115);let l=i.createContext(null);function s(e){let{authPromise:t,children:r}=e;return i.createElement(l.Provider,{value:t},r)}function u(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=(0,a.useRouter)(),r=i.useContext(l),s=r;return(r&&"then"in r&&(s=i.use(r)),"undefined"!=typeof window)?(0,n.As)({...s,...e}):t?(0,n.As)(e):(0,o.hP)({...s,...e})}},34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return a},findSourceMapURL:function(){return o.findSourceMapURL}});let n=r(53806),o=r(31818),a=r(34979).createServerReference},35583:(e,t,r)=>{e.exports=r(63950)},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}}),r.o(n,"useSelectedLayoutSegments")&&r.d(t,{useSelectedLayoutSegments:function(){return n.useSelectedLayoutSegments}})},36645:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let n=r(88229)._(r(67357));function o(e,t){var r;let o={};"function"==typeof e&&(o.loader=e);let a={...o,...t};return(0,n.default)({...a,modules:null==(r=a.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},37895:(e,t,r)=>{"use strict";r.d(t,{M:()=>o});var n=r(42206);let o=n.rE.startsWith("13.")||n.rE.startsWith("14.0")},38572:(e,t,r)=>{"use strict";r.d(t,{T5:()=>o.T5,hP:()=>n.hP,nO:()=>o.nO,yC:()=>a}),r(79419);var n=r(9693),o=r(62451);function a(e,t,r){let o=t.path||(null==r?void 0:r.path);return"path"===(t.routing||(null==r?void 0:r.routing)||"path")?o?{...r,...t,routing:"path"}:n.sb.throw((0,n.kd)(e)):t.path?n.sb.throw((0,n.s7)(e)):{...r,...t,path:void 0}}},42206:e=>{"use strict";e.exports={rE:"15.3.5"}},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function o(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,i]of Object.entries(t)){if(!t.hasOwnProperty(a)||n.includes(a)||void 0===i)continue;let l=r[a]||a.toLowerCase();"SCRIPT"===e.tagName&&o(l)?e[l]=!!i:e.setAttribute(l,String(i)),(!1===i||"SCRIPT"===e.tagName&&o(l)&&(!i||"false"===i))&&(e.setAttribute(l,""),e.removeAttribute(l))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},45766:(e,t,r)=>{"use strict";r.d(t,{ev:()=>s,tm:()=>l});var n=r(76829),o=r(82163);r(85649);var a=r(4579),i=r(49509);i.env.NEXT_PUBLIC_CLERK_JS_VERSION,i.env.NEXT_PUBLIC_CLERK_JS_URL,i.env.CLERK_API_VERSION,i.env.CLERK_SECRET_KEY,i.env.CLERK_ENCRYPTION_KEY,i.env.CLERK_API_URL||(e=>{let t=(0,n.q5)(e)?.frontendApi;return t?.startsWith("clerk.")&&o.iM.some(e=>t?.endsWith(e))?o.FW:o.mG.some(e=>t?.endsWith(e))?o.Vc:o.ub.some(e=>t?.endsWith(e))?o.HG:o.FW})("pk_test_Zmlyc3QtbW9uaXRvci03NC5jbGVyay5hY2NvdW50cy5kZXYk"),i.env.NEXT_PUBLIC_CLERK_DOMAIN,i.env.NEXT_PUBLIC_CLERK_PROXY_URL,(0,a.zz)(i.env.NEXT_PUBLIC_CLERK_IS_SATELLITE);let l={name:"@clerk/nextjs",version:"6.29.0",environment:"production"};(0,a.zz)(i.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),(0,a.zz)(i.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG);let s=(0,a.zz)(i.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1},46786:(e,t,r)=>{"use strict";r.d(t,{Zr:()=>f,eh:()=>c,lt:()=>s});let n=new Map,o=e=>{let t=n.get(e);return t?Object.fromEntries(Object.entries(t.stores).map(([e,t])=>[e,t.getState()])):{}},a=(e,t,r)=>{if(void 0===e)return{type:"untracked",connection:t.connect(r)};let o=n.get(r.name);if(o)return{type:"tracked",store:e,...o};let a={connection:t.connect(r),stores:{}};return n.set(r.name,a),{type:"tracked",store:e,...a}},i=(e,t)=>{if(void 0===t)return;let r=n.get(e);r&&(delete r.stores[t],0===Object.keys(r.stores).length&&n.delete(e))},l=e=>{var t,r;if(!e)return;let n=e.split("\n"),o=n.findIndex(e=>e.includes("api.setState"));if(o<0)return;let a=(null==(t=n[o+1])?void 0:t.trim())||"";return null==(r=/.+ (.+) .+/.exec(a))?void 0:r[1]},s=(e,t={})=>(r,n,s)=>{let c,{enabled:d,anonymousActionType:f,store:p,...h}=t;try{c=(null==d||d)&&window.__REDUX_DEVTOOLS_EXTENSION__}catch(e){}if(!c)return e(r,n,s);let{connection:m,...v}=a(p,c,h),y=!0;s.setState=(e,t,a)=>{let i=r(e,t);if(!y)return i;let u=void 0===a?{type:f||l(Error().stack)||"anonymous"}:"string"==typeof a?{type:a}:a;return void 0===p?null==m||m.send(u,n()):null==m||m.send({...u,type:`${p}/${u.type}`},{...o(h.name),[p]:s.getState()}),i},s.devtools={cleanup:()=>{m&&"function"==typeof m.unsubscribe&&m.unsubscribe(),i(h.name,p)}};let g=(...e)=>{let t=y;y=!1,r(...e),y=t},_=e(s.setState,n,s);if("untracked"===v.type?null==m||m.init(_):(v.stores[v.store]=s,null==m||m.init(Object.fromEntries(Object.entries(v.stores).map(([e,t])=>[e,e===v.store?_:t.getState()])))),s.dispatchFromDevtools&&"function"==typeof s.dispatch){let e=!1,t=s.dispatch;s.dispatch=(...r)=>{"__setState"!==r[0].type||e||(console.warn('[zustand devtools middleware] "__setState" action type is reserved to set state from the devtools. Avoid using it.'),e=!0),t(...r)}}return m.subscribe(e=>{var t;switch(e.type){case"ACTION":if("string"!=typeof e.payload)return void console.error("[zustand devtools middleware] Unsupported action format");return u(e.payload,e=>{if("__setState"===e.type){if(void 0===p)return void g(e.state);1!==Object.keys(e.state).length&&console.error(`
                    [zustand devtools middleware] Unsupported __setState action format.
                    When using 'store' option in devtools(), the 'state' should have only one key, which is a value of 'store' that was passed in devtools(),
                    and value of this only key should be a state object. Example: { "type": "__setState", "state": { "abc123Store": { "foo": "bar" } } }
                    `);let t=e.state[p];return void(null==t||JSON.stringify(s.getState())!==JSON.stringify(t)&&g(t))}s.dispatchFromDevtools&&"function"==typeof s.dispatch&&s.dispatch(e)});case"DISPATCH":switch(e.payload.type){case"RESET":if(g(_),void 0===p)return null==m?void 0:m.init(s.getState());return null==m?void 0:m.init(o(h.name));case"COMMIT":if(void 0===p){null==m||m.init(s.getState());break}return null==m?void 0:m.init(o(h.name));case"ROLLBACK":return u(e.state,e=>{if(void 0===p){g(e),null==m||m.init(s.getState());return}g(e[p]),null==m||m.init(o(h.name))});case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return u(e.state,e=>{if(void 0===p)return void g(e);JSON.stringify(s.getState())!==JSON.stringify(e[p])&&g(e[p])});case"IMPORT_STATE":{let{nextLiftedState:r}=e.payload,n=null==(t=r.computedStates.slice(-1)[0])?void 0:t.state;if(!n)return;void 0===p?g(n):g(n[p]),null==m||m.send(null,r);break}case"PAUSE_RECORDING":return y=!y}return}}),_},u=(e,t)=>{let r;try{r=JSON.parse(e)}catch(e){console.error("[zustand devtools middleware] Could not parse the received json",e)}void 0!==r&&t(r)},c=e=>(t,r,n)=>{let o=n.subscribe;return n.subscribe=(e,t,r)=>{let a=e;if(t){let o=(null==r?void 0:r.equalityFn)||Object.is,i=e(n.getState());a=r=>{let n=e(r);if(!o(i,n)){let e=i;t(i=n,e)}},(null==r?void 0:r.fireImmediately)&&t(i,i)}return o(a)},e(t,r,n)},d=e=>t=>{try{let r=e(t);if(r instanceof Promise)return r;return{then:e=>d(e)(r),catch(e){return this}}}catch(e){return{then(e){return this},catch:t=>d(t)(e)}}},f=(e,t)=>(r,n,o)=>{let a,i={storage:function(e,t){let r;try{r=e()}catch(e){return}return{getItem:e=>{var t;let n=e=>null===e?null:JSON.parse(e,void 0),o=null!=(t=r.getItem(e))?t:null;return o instanceof Promise?o.then(n):n(o)},setItem:(e,t)=>r.setItem(e,JSON.stringify(t,void 0)),removeItem:e=>r.removeItem(e)}}(()=>localStorage),partialize:e=>e,version:0,merge:(e,t)=>({...t,...e}),...t},l=!1,s=new Set,u=new Set,c=i.storage;if(!c)return e((...e)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...e)},n,o);let f=()=>{let e=i.partialize({...n()});return c.setItem(i.name,{state:e,version:i.version})},p=o.setState;o.setState=(e,t)=>{p(e,t),f()};let h=e((...e)=>{r(...e),f()},n,o);o.getInitialState=()=>h;let m=()=>{var e,t;if(!c)return;l=!1,s.forEach(e=>{var t;return e(null!=(t=n())?t:h)});let o=(null==(t=i.onRehydrateStorage)?void 0:t.call(i,null!=(e=n())?e:h))||void 0;return d(c.getItem.bind(c))(i.name).then(e=>{if(e)if("number"!=typeof e.version||e.version===i.version)return[!1,e.state];else{if(i.migrate){let t=i.migrate(e.state,e.version);return t instanceof Promise?t.then(e=>[!0,e]):[!0,t]}console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}return[!1,void 0]}).then(e=>{var t;let[o,l]=e;if(r(a=i.merge(l,null!=(t=n())?t:h),!0),o)return f()}).then(()=>{null==o||o(a,void 0),a=n(),l=!0,u.forEach(e=>e(a))}).catch(e=>{null==o||o(void 0,e)})};return o.persist={setOptions:e=>{i={...i,...e},e.storage&&(c=e.storage)},clearStorage:()=>{null==c||c.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>m(),hasHydrated:()=>l,onHydrate:e=>(s.add(e),()=>{s.delete(e)}),onFinishHydration:e=>(u.add(e),()=>{u.delete(e)})},i.skipHydration||m(),a||h}},51362:(e,t,r)=>{"use strict";r.d(t,{D:()=>u,N:()=>c});var n=r(12115),o=(e,t,r,n,o,a,i,l)=>{let s=document.documentElement,u=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,n=r&&a?o.map(e=>a[e]||e):o;r?(s.classList.remove(...n),s.classList.add(a&&a[t]?a[t]:t)):s.setAttribute(e,t)}),r=t,l&&u.includes(r)&&(s.style.colorScheme=r)}if(n)c(n);else try{let e=localStorage.getItem(t)||r,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},a=["light","dark"],i="(prefers-color-scheme: dark)",l=n.createContext(void 0),s={setTheme:e=>{},themes:[]},u=()=>{var e;return null!=(e=n.useContext(l))?e:s},c=e=>n.useContext(l)?n.createElement(n.Fragment,null,e.children):n.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:o=!0,enableColorScheme:s=!0,storageKey:u="theme",themes:c=d,defaultTheme:f=o?"system":"light",attribute:y="data-theme",value:g,children:_,nonce:b,scriptProps:S}=e,[E,w]=n.useState(()=>h(u,f)),[O,C]=n.useState(()=>"system"===E?v():E),P=g?Object.values(g):c,I=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&o&&(t=v());let n=g?g[t]:t,i=r?m(b):null,l=document.documentElement,u=e=>{"class"===e?(l.classList.remove(...P),n&&l.classList.add(n)):e.startsWith("data-")&&(n?l.setAttribute(e,n):l.removeAttribute(e))};if(Array.isArray(y)?y.forEach(u):u(y),s){let e=a.includes(f)?f:null,r=a.includes(t)?t:e;l.style.colorScheme=r}null==i||i()},[b]),k=n.useCallback(e=>{let t="function"==typeof e?e(E):e;w(t);try{localStorage.setItem(u,t)}catch(e){}},[E]),L=n.useCallback(e=>{C(v(e)),"system"===E&&o&&!t&&I("system")},[E,t]);n.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(L),L(e),()=>e.removeListener(L)},[L]),n.useEffect(()=>{let e=e=>{e.key===u&&(e.newValue?w(e.newValue):k(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[k]),n.useEffect(()=>{I(null!=t?t:E)},[t,E]);let R=n.useMemo(()=>({theme:E,setTheme:k,forcedTheme:t,resolvedTheme:"system"===E?O:E,themes:o?[...c,"system"]:c,systemTheme:o?O:void 0}),[E,k,t,O,o,c]);return n.createElement(l.Provider,{value:R},n.createElement(p,{forcedTheme:t,storageKey:u,attribute:y,enableSystem:o,enableColorScheme:s,defaultTheme:f,value:g,themes:c,nonce:b,scriptProps:S}),_)},p=n.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:i,enableColorScheme:l,defaultTheme:s,value:u,themes:c,nonce:d,scriptProps:f}=e,p=JSON.stringify([a,r,s,t,c,u,i,l]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(p,")")}})}),h=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},v=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},61657:(e,t,r)=>{"use strict";r.d(t,{APIKeys:()=>n.Lq,CreateOrganization:()=>n.ul,GoogleOneTap:()=>n.PQ,OrganizationList:()=>n.oE,OrganizationProfile:()=>f,OrganizationSwitcher:()=>n.NC,PricingTable:()=>n.nm,SignIn:()=>p,SignInButton:()=>n.hZ,SignInWithMetamaskButton:()=>n.M_,SignOutButton:()=>n.ct,SignUp:()=>h,SignUpButton:()=>n.Ny,TaskSelectOrganization:()=>n.Rv,UserButton:()=>n.uF,UserProfile:()=>d,Waitlist:()=>n.cP});var n=r(48879),o=r(12115),a=r(38572),i=r(48416),l=r(66299);let s=(e,t,r,a=!0)=>{let s=o.useRef(0),{pagesRouter:u}=(0,l.r)(),{session:c,isLoaded:d}=(0,n.wV)();(0,i.Fj)()||o.useEffect(()=>{if(!d||r&&"path"!==r||a&&!c)return;let n=new AbortController,o=()=>{let r=u?`${t}/[[...index]].tsx`:`${t}/[[...rest]]/page.tsx`;throw Error(`
Clerk: The <${e}/> component is not configured correctly. The most likely reasons for this error are:

1. The "${t}" route is not a catch-all route.
It is recommended to convert this route to a catch-all route, eg: "${r}". Alternatively, you can update the <${e}/> component to use hash-based routing by setting the "routing" prop to "hash".

2. The <${e}/> component is mounted in a catch-all route, but all routes under "${t}" are protected by the middleware.
To resolve this, ensure that the middleware does not protect the catch-all route or any of its children. If you are using the "createRouteMatcher" helper, consider adding "(.*)" to the end of the route pattern, eg: "${t}(.*)". For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#create-route-matcher
`)};return u?u.pathname.match(/\[\[\.\.\..+]]/)||o():(async()=>{let t;if(s.current++,!(s.current>1)){try{let r=`${window.location.origin}${window.location.pathname}/${e}_clerk_catchall_check_${Date.now()}`;t=await fetch(r,{signal:n.signal})}catch{}(null==t?void 0:t.status)===404&&o()}})(),()=>{s.current>1&&n.abort()}},[d])},u=()=>{let e=o.useRef(),{pagesRouter:t}=(0,l.r)();if(t)if(e.current)return e.current;else return e.current=t.pathname.replace(/\/\[\[\.\.\..*/,""),e.current;let n=r(35695).usePathname,a=r(35695).useParams,i=(n()||"").split("/").filter(Boolean),s=Object.values(a()||{}).filter(e=>Array.isArray(e)).flat(1/0);return e.current||(e.current=`/${i.slice(0,i.length-s.length).join("/")}`),e.current};function c(e,t,r=!0){let n=u(),o=(0,a.yC)(e,t,{path:n});return s(e,n,o.routing,r),o}let d=Object.assign(e=>o.createElement(n.Fv,{...c("UserProfile",e)}),{...n.Fv}),f=Object.assign(e=>o.createElement(n.nC,{...c("OrganizationProfile",e)}),{...n.nC}),p=e=>o.createElement(n.Ls,{...c("SignIn",e,!1)}),h=e=>o.createElement(n.Hx,{...c("SignUp",e,!1)})},62146:(e,t,r)=>{"use strict";function n(e){let{reason:t,children:r}=e;return r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return n}}),r(45262)},63950:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useRouter",{enumerable:!0,get:function(){return a}});let n=r(12115),o=r(70901);function a(){return(0,n.useContext)(o.RouterContext)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},64054:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return a},createSnapshot:function(){return l}});let r=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class n{disable(){throw r}getStore(){}run(){throw r}exit(){throw r}enterWith(){throw r}static bind(e){return e}}let o="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return o?new o:new n}function i(e){return o?o.bind(e):n.bind(e)}function l(){return o?o.snapshot():function(e,...t){return e(...t)}}},65453:(e,t,r)=>{"use strict";r.d(t,{v:()=>s});var n=r(12115);let o=e=>{let t,r=new Set,n=(e,n)=>{let o="function"==typeof e?e(t):e;if(!Object.is(o,t)){let e=t;t=(null!=n?n:"object"!=typeof o||null===o)?o:Object.assign({},t,o),r.forEach(r=>r(t,e))}},o=()=>t,a={setState:n,getState:o,getInitialState:()=>i,subscribe:e=>(r.add(e),()=>r.delete(e))},i=t=e(n,o,a);return a},a=e=>e?o(e):o,i=e=>e,l=e=>{let t=a(e),r=e=>(function(e,t=i){let r=n.useSyncExternalStore(e.subscribe,n.useCallback(()=>t(e.getState()),[e,t]),n.useCallback(()=>t(e.getInitialState()),[e,t]));return n.useDebugValue(r),r})(t,e);return Object.assign(r,t),r},s=e=>e?l(e):l},66096:e=>{e.exports={style:{fontFamily:"'outfit', 'outfit Fallback'"},className:"__className_0faefe",variable:"__variable_0faefe"}},66299:(e,t,r)=>{"use strict";r.d(t,{r:()=>o});var n=r(35583);let o=()=>({pagesRouter:(0,n.useRouter)()})},67357:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(95155),o=r(12115),a=r(62146);function i(e){return{default:e&&"default"in e?e.default:e}}r(10255);let l={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},s=function(e){let t={...l,...e},r=(0,o.lazy)(()=>t.loader().then(i)),s=t.loading;function u(e){let i=s?(0,n.jsx)(s,{isLoading:!0,pastDelay:!0,error:null}):null,l=!t.ssr||!!t.loading,u=l?o.Suspense:o.Fragment,c=t.ssr?(0,n.jsxs)(n.Fragment,{children:[null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(u,{...l?{fallback:i}:{},children:c})}return u.displayName="LoadableComponent",u}},69243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return g},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return v}});let n=r(88229),o=r(6966),a=r(95155),i=n._(r(47650)),l=o._(r(12115)),s=r(82830),u=r(42714),c=r(92374),d=new Map,f=new Set,p=e=>{if(i.default.preinit)return void e.forEach(e=>{i.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},h=e=>{let{src:t,id:r,onLoad:n=()=>{},onReady:o=null,dangerouslySetInnerHTML:a,children:i="",strategy:l="afterInteractive",onError:s,stylesheets:c}=e,h=r||t;if(h&&f.has(h))return;if(d.has(t)){f.add(h),d.get(t).then(n,s);return}let m=()=>{o&&o(),f.add(h)},v=document.createElement("script"),y=new Promise((e,t)=>{v.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),v.addEventListener("error",function(e){t(e)})}).catch(function(e){s&&s(e)});a?(v.innerHTML=a.__html||"",m()):i?(v.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",m()):t&&(v.src=t,d.set(t,y)),(0,u.setAttributesFromProps)(v,e),"worker"===l&&v.setAttribute("type","text/partytown"),v.setAttribute("data-nscript",l),c&&p(c),document.body.appendChild(v)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>h(e))}):h(e)}function v(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function y(e){let{id:t,src:r="",onLoad:n=()=>{},onReady:o=null,strategy:u="afterInteractive",onError:d,stylesheets:p,...m}=e,{updateScripts:v,scripts:y,getIsSsr:g,appDir:_,nonce:b}=(0,l.useContext)(s.HeadManagerContext),S=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||r;S.current||(o&&e&&f.has(e)&&o(),S.current=!0)},[o,t,r]);let E=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{if(!E.current){if("afterInteractive"===u)h(e);else"lazyOnload"===u&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>h(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>h(e))}));E.current=!0}},[e,u]),("beforeInteractive"===u||"worker"===u)&&(v?(y[u]=(y[u]||[]).concat([{id:t,src:r,onLoad:n,onReady:o,onError:d,...m}]),v(y)):g&&g()?f.add(t||r):g&&!g()&&h(e)),_){if(p&&p.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===u)if(!r)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return i.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin}),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...m,id:t}])+")"}});"afterInteractive"===u&&r&&i.default.preload(r,m.integrity?{as:"script",integrity:m.integrity,nonce:b,crossOrigin:m.crossOrigin}:{as:"script",nonce:b,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(y,"__nextScript",{value:!0});let g=y;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70901:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RouterContext",{enumerable:!0,get:function(){return n}});let n=r(88229)._(r(12115)).default.createContext(null)},83398:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientClerkProvider:()=>A});var n=r(48879),o=r(43877),a=new Set,i={warnOnce:e=>{a.has(e)||(a.add(e),console.warn(e))},logOnce:e=>{a.has(e)||(console.log(e),a.add(e))}};r(85649);var l=r(36645),s=r.n(l),u=r(35695),c=r(42206),d=r(12115);let f="undefined"!=typeof window?d.useLayoutEffect:d.useEffect,p=d.createContext(void 0);p.displayName="ClerkNextOptionsCtx";let h=()=>{let e=d.useContext(p);return null==e?void 0:e.value},m=e=>{let{children:t,options:r}=e;return d.createElement(p.Provider,{value:{value:r}},t)};var v=r(38572),y=r(69243),g=r.n(y);function _(e){let{publishableKey:t,clerkJSUrl:r,clerkJSVersion:o,clerkJSVariant:a,nonce:i}=h(),{domain:l,proxyUrl:s}=(0,n.ho)();if(!t)return null;let u={domain:l,proxyUrl:s,publishableKey:t,clerkJSUrl:r,clerkJSVersion:o,clerkJSVariant:a,nonce:i},c=(0,v.nO)(u),f="app"===e.router?"script":g();return d.createElement(f,{src:c,"data-clerk-js-script":!0,async:!0,defer:"pages"!==e.router&&void 0,crossOrigin:"anonymous",strategy:"pages"===e.router?"beforeInteractive":void 0,...(0,v.T5)(u)})}var b=r(89331),S=r(4579),E=r(45766),w=r(49509);let O=e=>{var t;return{...e,publishableKey:e.publishableKey||"pk_test_Zmlyc3QtbW9uaXRvci03NC5jbGVyay5hY2NvdW50cy5kZXYk",clerkJSUrl:e.clerkJSUrl||w.env.NEXT_PUBLIC_CLERK_JS_URL,clerkJSVersion:e.clerkJSVersion||w.env.NEXT_PUBLIC_CLERK_JS_VERSION,proxyUrl:e.proxyUrl||w.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",domain:e.domain||w.env.NEXT_PUBLIC_CLERK_DOMAIN||"",isSatellite:e.isSatellite||(0,S.zz)(w.env.NEXT_PUBLIC_CLERK_IS_SATELLITE),signInUrl:e.signInUrl||"/auth/login",signUpUrl:e.signUpUrl||"/auth/register",signInForceRedirectUrl:e.signInForceRedirectUrl||w.env.NEXT_PUBLIC_CLERK_SIGN_IN_FORCE_REDIRECT_URL||"",signUpForceRedirectUrl:e.signUpForceRedirectUrl||w.env.NEXT_PUBLIC_CLERK_SIGN_UP_FORCE_REDIRECT_URL||"",signInFallbackRedirectUrl:e.signInFallbackRedirectUrl||"/user-dashboard",signUpFallbackRedirectUrl:e.signUpFallbackRedirectUrl||"/user-dashboard",afterSignInUrl:e.afterSignInUrl||"/user-dashboard",afterSignUpUrl:e.afterSignUpUrl||"/user-dashboard",newSubscriptionRedirectUrl:e.newSubscriptionRedirectUrl||w.env.NEXT_PUBLIC_CLERK_CHECKOUT_CONTINUE_URL||"",telemetry:null!=(t=e.telemetry)?t:{disabled:(0,S.zz)(w.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),debug:(0,S.zz)(w.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG)},sdkMetadata:E.tm}};var C=r(36898),P=r(66299);let I=()=>{var e,t;let r=(0,n.ho)(),{pagesRouter:o}=(0,P.r)();return null==(t=r.telemetry)||t.record((0,C.YF)({router:o?"pages":"app",...(null==(e=null==globalThis?void 0:globalThis.next)?void 0:e.version)?{nextjsVersion:globalThis.next.version}:{}})),null};var k=r(37895),L=r(47332);let R=e=>{var t;return null!=window.__clerk_internal_navigations||(window.__clerk_internal_navigations={}),null!=(t=window.__clerk_internal_navigations)[e]||(t[e]={}),window.__clerk_internal_navigations[e]},T=e=>{let{windowNav:t,routerNav:r,name:n}=e,o=(0,u.usePathname)(),[a,i]=(0,d.useTransition)();t&&(R(n).fun=(e,o)=>new Promise(a=>{var l,s;null!=(l=R(n)).promisesBuffer||(l.promisesBuffer=[]),null==(s=R(n).promisesBuffer)||s.push(a),i(()=>{var n,a,i,l;(null==(n=null==o?void 0:o.__internal_metadata)?void 0:n.navigationType)==="internal"?t((null!=(i=null==(a=window.next)?void 0:a.version)?i:"")<"14.1.0"?history.state:null,"",e):r(l=e)})}));let l=()=>{var e;null==(e=R(n).promisesBuffer)||e.forEach(e=>e()),R(n).promisesBuffer=[]};return(0,d.useEffect)(()=>(l(),l),[]),(0,d.useEffect)(()=>{a||l()},[o,a]),(0,d.useCallback)((e,t)=>R(n).fun(e,t),[])},j=()=>{let e=(0,u.useRouter)();return T({windowNav:"undefined"!=typeof window?window.history.pushState.bind(window.history):void 0,routerNav:e.push.bind(e),name:"push"})},U=()=>{let e=(0,u.useRouter)();return T({windowNav:"undefined"!=typeof window?window.history.replaceState.bind(window.history):void 0,routerNav:e.replace.bind(e),name:"replace"})},N=s()(()=>r.e(2642).then(r.bind(r,22642)).then(e=>e.KeylessCreatorOrReader),{loadableGenerated:{webpack:()=>[22642]}}),x=e=>{if(k.M){let e="Clerk:\nYour current Next.js version (".concat(c.rE,') will be deprecated in the next major release of "@clerk/nextjs". Please upgrade to next@14.1.0 or later.');(0,o.M)()?i.warnOnce(e):i.logOnce("\n\x1b[43m----------\n".concat(e,"\n----------\x1b[0m\n"))}let{__unstable_invokeMiddlewareOnAuthStateChange:t=!0,children:r}=e,a=(0,u.useRouter)(),l=j(),s=U(),[p,v]=(0,d.useTransition)();if(h())return e.children;(0,d.useEffect)(()=>{var e;p||null==(e=window.__clerk_internal_invalidateCachePromise)||e.call(window)},[p]),f(()=>{window.__unstable__onBeforeSetActive=e=>new Promise(t=>{var r;window.__clerk_internal_invalidateCachePromise=t;let n=(null==(r=null==window?void 0:window.next)?void 0:r.version)||"";n.startsWith("13")?v(()=>{a.refresh()}):n.startsWith("15")&&"sign-out"===e?t():(0,L.y)().then(()=>t())}),window.__unstable__onAfterSetActive=()=>{if(t)return a.refresh()}},[]);let y=O({...e,routerPush:l,routerReplace:s});return d.createElement(m,{options:y},d.createElement(n.lJ,{...y},d.createElement(I,null),d.createElement(_,{router:"app"}),r))},A=e=>{let{children:t,disableKeyless:r=!1,...n}=e;return O(n).publishableKey||!b.I||r?d.createElement(x,{...n},t):d.createElement(N,null,d.createElement(x,{...n},t))}},83731:(e,t,r)=>{"use strict";r.d(t,{sf:()=>l,so:()=>i});var n=r(72341),o=r(12115),a=(0,o.createContext)({client:n.Ay});function i(e){var t=e.children,r=e.client,i=e.apiKey,l=e.options,s=(0,o.useRef)(null),u=(0,o.useMemo)(function(){return r?(i&&console.warn("[PostHog.js] You have provided both `client` and `apiKey` to `PostHogProvider`. `apiKey` will be ignored in favour of `client`."),l&&console.warn("[PostHog.js] You have provided both `client` and `options` to `PostHogProvider`. `options` will be ignored in favour of `client`."),r):(i||console.warn("[PostHog.js] No `apiKey` or `client` were provided to `PostHogProvider`. Using default global `window.posthog` instance. You must initialize it manually. This is not recommended behavior."),n.Ay)},[r,i,JSON.stringify(l)]);return(0,o.useEffect)(function(){if(!r){var e=s.current;e?(i!==e.apiKey&&console.warn("[PostHog.js] You have provided a different `apiKey` to `PostHogProvider` than the one that was already initialized. This is not supported by our provider and we'll keep using the previous key. If you need to toggle between API Keys you need to control the `client` yourself and pass it in as a prop rather than an `apiKey` prop."),l&&!function e(t,r,n){if(void 0===n&&(n=new WeakMap),t===r)return!0;if("object"!=typeof t||null===t||"object"!=typeof r||null===r)return!1;if(n.has(t)&&n.get(t)===r)return!0;n.set(t,r);var o=Object.keys(t),a=Object.keys(r);if(o.length!==a.length)return!1;for(var i=0;i<o.length;i++){var l=o[i];if(!a.includes(l)||!e(t[l],r[l],n))return!1}return!0}(l,e.options)&&n.Ay.set_config(l)):(n.Ay.__loaded&&console.warn("[PostHog.js] `posthog` was already loaded elsewhere. This may cause issues."),n.Ay.init(i,l)),s.current={apiKey:i,options:null!=l?l:{}}}},[r,i,JSON.stringify(l)]),o.createElement(a.Provider,{value:{client:u}},t)}var l=function(){return(0,o.useContext)(a).client},s=function(e,t){return(s=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},u=function(){return(u=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function c(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}"function"==typeof SuppressedError&&SuppressedError;var d=function(e){return"function"==typeof e};function f(e){var t=e.flag,r=e.children,n=e.onIntersect,o=e.onClick,a=e.trackView,i=e.options,s=c(e,["flag","children","onIntersect","onClick","trackView","options"]),d=useRef(null);return useEffect(function(){if(null!==d.current&&a){var e=new IntersectionObserver(function(e){return n(e[0])},u({threshold:.1},i));return e.observe(d.current),function(){return e.disconnect()}}},[t,i,l(),d,a,n]),React.createElement("div",u({ref:d},s,{onClick:o}),r)}var p={componentStack:null,error:null},h={INVALID_FALLBACK:"[PostHog.js][PostHogErrorBoundary] Invalid fallback prop, provide a valid React element or a function that returns a valid React element."};!function(e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=r}function r(t){var r=e.call(this,t)||this;return r.state=p,r}s(r,e),r.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t),r.prototype.componentDidCatch=function(e,t){var r,n=t.componentStack,o=this.props.additionalProperties;this.setState({error:e,componentStack:n}),d(o)?r=o(e):"object"==typeof o&&(r=o),this.context.client.captureException(e,r)},r.prototype.render=function(){var e=this.props,t=e.children,r=e.fallback,n=this.state;if(null==n.componentStack)return d(t)?t():t;var a=d(r)?o.createElement(r,{error:n.error,componentStack:n.componentStack}):r;return o.isValidElement(a)?a:(console.warn(h.INVALID_FALLBACK),o.createElement(o.Fragment,null))},r.contextType=a}(o.Component)},85744:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"workAsyncStorage",{enumerable:!0,get:function(){return n.workAsyncStorageInstance}});let n=r(17828)},89331:(e,t,r)=>{"use strict";r.d(t,{I:()=>a});var n=r(48416),o=r(45766);let a=!r(37895).M&&(0,n.b_)()&&!o.ev},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},94255:e=>{e.exports={style:{fontFamily:"'outfitVariable', 'outfitVariable Fallback'"},className:"__className_b0e34d",variable:"__variable_b0e34d"}}}]);