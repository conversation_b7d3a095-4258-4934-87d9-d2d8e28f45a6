"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2417],{4229:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5196:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},18175:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},23861:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},34869:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>d});var a=r(12115),n=r(63655),l=r(95155),c=a.forwardRef((e,t)=>(0,l.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));c.displayName="Label";var d=c},45503:(e,t,r)=>{r.d(t,{Z:()=>n});var a=r(12115);function n(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},59099:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},72894:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},74783:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},76981:(e,t,r)=>{r.d(t,{C1:()=>w,bL:()=>m});var a=r(12115),n=r(6101),l=r(46081),c=r(85185),d=r(5845),o=r(45503),i=r(11275),s=r(28905),u=r(63655),p=r(95155),h="Checkbox",[y,k]=(0,l.A)(h),[f,v]=y(h);function b(e){let{__scopeCheckbox:t,checked:r,children:n,defaultChecked:l,disabled:c,form:o,name:i,onCheckedChange:s,required:u,value:y="on",internal_do_not_use_render:k}=e,[v,b]=(0,d.i)({prop:r,defaultProp:null!=l&&l,onChange:s,caller:h}),[x,A]=a.useState(null),[m,M]=a.useState(null),w=a.useRef(!1),g=!x||!!o||!!x.closest("form"),j={checked:v,disabled:c,setChecked:b,control:x,setControl:A,name:i,form:o,value:y,hasConsumerStoppedPropagationRef:w,required:u,defaultChecked:!C(l)&&l,isFormControl:g,bubbleInput:m,setBubbleInput:M};return(0,p.jsx)(f,{scope:t,...j,children:"function"==typeof k?k(j):n})}var x="CheckboxTrigger",A=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:l,onClick:d,...o}=e,{control:i,value:s,disabled:h,checked:y,required:k,setControl:f,setChecked:b,hasConsumerStoppedPropagationRef:A,isFormControl:m,bubbleInput:M}=v(x,r),w=(0,n.s)(t,f),g=a.useRef(y);return a.useEffect(()=>{let e=null==i?void 0:i.form;if(e){let t=()=>b(g.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[i,b]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":C(y)?"mixed":y,"aria-required":k,"data-state":E(y),"data-disabled":h?"":void 0,disabled:h,value:s,...o,ref:w,onKeyDown:(0,c.m)(l,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,c.m)(d,e=>{b(e=>!!C(e)||!e),M&&m&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})})});A.displayName=x;var m=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:a,checked:n,defaultChecked:l,required:c,disabled:d,value:o,onCheckedChange:i,form:s,...u}=e;return(0,p.jsx)(b,{__scopeCheckbox:r,checked:n,defaultChecked:l,disabled:d,required:c,onCheckedChange:i,name:a,form:s,value:o,internal_do_not_use_render:e=>{let{isFormControl:a}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(A,{...u,ref:t,__scopeCheckbox:r}),a&&(0,p.jsx)(j,{__scopeCheckbox:r})]})}})});m.displayName=h;var M="CheckboxIndicator",w=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:a,...n}=e,l=v(M,r);return(0,p.jsx)(s.C,{present:a||C(l.checked)||!0===l.checked,children:(0,p.jsx)(u.sG.span,{"data-state":E(l.checked),"data-disabled":l.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=M;var g="CheckboxBubbleInput",j=a.forwardRef((e,t)=>{let{__scopeCheckbox:r,...l}=e,{control:c,hasConsumerStoppedPropagationRef:d,checked:s,defaultChecked:h,required:y,disabled:k,name:f,value:b,form:x,bubbleInput:A,setBubbleInput:m}=v(g,r),M=(0,n.s)(t,m),w=(0,o.Z)(s),j=(0,i.X)(c);a.useEffect(()=>{if(!A)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!d.current;if(w!==s&&e){let r=new Event("click",{bubbles:t});A.indeterminate=C(s),e.call(A,!C(s)&&s),A.dispatchEvent(r)}},[A,w,s,d]);let E=a.useRef(!C(s)&&s);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=h?h:E.current,required:y,disabled:k,name:f,value:b,form:x,...l,tabIndex:-1,ref:M,style:{...l.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function E(e){return C(e)?"indeterminate":e?"checked":"unchecked"}j.displayName=g},78749:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87489:(e,t,r)=>{r.d(t,{b:()=>i});var a=r(12115),n=r(63655),l=r(95155),c="horizontal",d=["horizontal","vertical"],o=a.forwardRef((e,t)=>{var r;let{decorative:a,orientation:o=c,...i}=e,s=(r=o,d.includes(r))?o:c;return(0,l.jsx)(n.sG.div,{"data-orientation":s,...a?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...i,ref:t})});o.displayName="Separator";var i=o},92657:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(19946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);