"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8229],{72341:(e,t,i)=>{i.d(t,{Ay:()=>nH});var s="undefined"!=typeof window?window:void 0,r="undefined"!=typeof globalThis?globalThis:s,n=Array.prototype,a=n.forEach,o=n.indexOf,l=null==r?void 0:r.navigator,c=null==r?void 0:r.document,u=null==r?void 0:r.location,h=null==r?void 0:r.fetch,d=null!=r&&r.XMLHttpRequest&&"withCredentials"in new r.XMLHttpRequest?r.XMLHttpRequest:void 0,p=null==r?void 0:r.<PERSON>bortController,g=null==l?void 0:l.userAgent,_=null!=s?s:{},v={DEBUG:!1,LIB_VERSION:"1.258.6"},f="$copy_autocapture",m=["$snapshot","$pageview","$pageleave","$set","survey dismissed","survey sent","survey shown","$identify","$groupidentify","$create_alias","$$client_ingestion_warning","$web_experiment_applied","$feature_enrollment_update","$feature_flag_called"],y=function(e){return e.GZipJS="gzip-js",e.Base64="base64",e}({}),b=["fatal","error","warning","log","info","debug"];function w(e,t){return -1!==e.indexOf(t)}var E=function(e){return e.trim()},S=function(e){return e.replace(/^\$/,"")},x=Array.isArray,k=Object.prototype,$=k.hasOwnProperty,I=k.toString,F=x||function(e){return"[object Array]"===I.call(e)},C=e=>"function"==typeof e,P=e=>e===Object(e)&&!F(e),R=e=>{if(P(e)){for(var t in e)if($.call(e,t))return!1;return!0}return!1},T=e=>void 0===e,O=e=>"[object String]"==I.call(e),A=e=>O(e)&&0===e.trim().length,M=e=>null===e,D=e=>T(e)||M(e),L=e=>"[object Number]"==I.call(e),N=e=>"[object Boolean]"===I.call(e),q=e=>e instanceof FormData,j=e=>w(m,e),B=e=>{var t={t:function(t){if(s&&(v.DEBUG||_.POSTHOG_DEBUG)&&!T(s.console)&&s.console){for(var i=("__rrweb_original__"in s.console[t])?s.console[t].__rrweb_original__:s.console[t],r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];i(e,...n)}},info:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t.t("log",...i)},warn:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t.t("warn",...i)},error:function(){for(var e=arguments.length,i=Array(e),s=0;s<e;s++)i[s]=arguments[s];t.t("error",...i)},critical:function(){for(var t=arguments.length,i=Array(t),s=0;s<t;s++)i[s]=arguments[s];console.error(e,...i)},uninitializedWarning:e=>{t.error("You must initialize PostHog before calling "+e)},createLogger:t=>B(e+" "+t)};return t},H=B("[PostHog.js]"),z=H.createLogger,U=z("[ExternalScriptsLoader]"),G=(e,t,i)=>{if(e.config.disable_external_dependency_loading)return U.warn(t+" was requested but loading of external scripts is disabled."),i("Loading of external scripts is disabled");var s=null==c?void 0:c.querySelectorAll("script");if(s){for(var r=0;r<s.length;r++)if(s[r].src===t)return i()}var n=()=>{if(!c)return i("document not found");var s=c.createElement("script");if(s.type="text/javascript",s.crossOrigin="anonymous",s.src=t,s.onload=e=>i(void 0,e),s.onerror=e=>i(e),e.config.prepare_external_dependency_script&&(s=e.config.prepare_external_dependency_script(s)),!s)return i("prepare_external_dependency_script returned null");var r,n=c.querySelectorAll("body > script");n.length>0?null==(r=n[0].parentNode)||r.insertBefore(s,n[0]):c.body.appendChild(s)};null!=c&&c.body?n():null==c||c.addEventListener("DOMContentLoaded",n)};function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var s in i)({}).hasOwnProperty.call(i,s)&&(e[s]=i[s])}return e}).apply(null,arguments)}function W(e,t){if(null==e)return{};var i={};for(var s in e)if(({}).hasOwnProperty.call(e,s)){if(-1!==t.indexOf(s))continue;i[s]=e[s]}return i}_.__PosthogExtensions__=_.__PosthogExtensions__||{},_.__PosthogExtensions__.loadExternalDependency=(e,t,i)=>{var s="/static/"+t+".js?v="+e.version;"remote-config"===t&&(s="/array/"+e.config.token+"/config.js"),"toolbar"===t&&(s=s+"&t="+3e5*Math.floor(Date.now()/3e5));var r=e.requestRouter.endpointFor("assets",s);G(e,r,i)},_.__PosthogExtensions__.loadSiteApp=(e,t,i)=>{var s=e.requestRouter.endpointFor("api",t);G(e,s,i)};var Y={};function J(e,t,i){if(F(e)){if(a&&e.forEach===a)e.forEach(t,i);else if("length"in e&&e.length===+e.length){for(var s=0,r=e.length;s<r;s++)if(s in e&&t.call(i,e[s],s)===Y)return}}}function K(e,t,i){if(!D(e)){if(F(e))return J(e,t,i);if(q(e)){for(var s of e.entries())if(t.call(i,s[1],s[0])===Y)return}else for(var r in e)if($.call(e,r)&&t.call(i,e[r],r)===Y)return}}var Z=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];return J(i,function(t){for(var i in t)void 0!==t[i]&&(e[i]=t[i])}),e},X=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];return J(i,function(t){J(t,function(t){e.push(t)})}),e};function Q(e){for(var t=Object.keys(e),i=t.length,s=Array(i);i--;)s[i]=[t[i],e[t[i]]];return s}var ee=function(e){try{return e()}catch(e){return}},et=function(e){return function(){try{for(var t=arguments.length,i=Array(t),s=0;s<t;s++)i[s]=arguments[s];return e.apply(this,i)}catch(e){H.critical("Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A."),H.critical(e)}}},ei=function(e){var t={};return K(e,function(e,i){(O(e)&&e.length>0||L(e))&&(t[i]=e)}),t},es=["herokuapp.com","vercel.app","netlify.app"];function er(e,t){for(var i=0;i<e.length;i++)if(t(e[i]))return e[i]}function en(e,t,i,s){var{capture:r=!1,passive:n=!0}=null!=s?s:{};null==e||e.addEventListener(t,i,{capture:r,passive:n})}var ea="$people_distinct_id",eo="__alias",el="__timers",ec="$autocapture_disabled_server_side",eu="$heatmaps_enabled_server_side",eh="$exception_capture_enabled_server_side",ed="$error_tracking_suppression_rules",ep="$error_tracking_capture_extension_exceptions",eg="$web_vitals_enabled_server_side",e_="$dead_clicks_enabled_server_side",ev="$web_vitals_allowed_metrics",ef="$session_recording_enabled_server_side",em="$console_log_recording_enabled_server_side",ey="$session_recording_network_payload_capture",eb="$session_recording_masking",ew="$session_recording_canvas_recording",eE="$replay_sample_rate",eS="$replay_minimum_duration",ex="$replay_script_config",ek="$sesid",e$="$session_is_sampled",eI="$session_recording_url_trigger_activated_session",eF="$session_recording_event_trigger_activated_session",eC="$enabled_feature_flags",eP="$early_access_features",eR="$feature_flag_details",eT="$stored_person_properties",eO="$stored_group_properties",eA="$surveys",eM="$surveys_activated",eD="$flag_call_reported",eL="$user_state",eN="$client_session_props",eq="$capture_rate_limit",ej="$initial_campaign_params",eB="$initial_referrer_info",eH="$initial_person_info",ez="$epp",eU="__POSTHOG_TOOLBAR__",eG="$posthog_cookieless",eV=[ea,eo,"__cmpns",el,ef,eu,ek,eC,ed,eL,eP,eR,eO,eT,eA,eD,eN,eq,ej,eB,ez,eH];function eW(e){return e instanceof Element&&(e.id===eU||!(null==e.closest||!e.closest(".toolbar-global-fade-container")))}function eY(e){return!!e&&1===e.nodeType}function eJ(e,t){return!!e&&!!e.tagName&&e.tagName.toLowerCase()===t.toLowerCase()}function eK(e){return!!e&&3===e.nodeType}function eZ(e){return!!e&&11===e.nodeType}function eX(e){return e?E(e).split(/\s+/):[]}function eQ(e){var t=null==s?void 0:s.location.href;return!!(t&&e&&e.some(e=>t.match(e)))}function e0(e){var t="";switch(typeof e.className){case"string":t=e.className;break;case"object":t=(e.className&&"baseVal"in e.className?e.className.baseVal:null)||e.getAttribute("class")||"";break;default:t=""}return eX(t)}function e1(e){return D(e)?null:E(e).split(/(\s+)/).filter(e=>tr(e)).join("").replace(/[\r\n]/g," ").replace(/[ ]+/g," ").substring(0,255)}function e2(e){var t="";return e8(e)&&!e4(e)&&e.childNodes&&e.childNodes.length&&K(e.childNodes,function(e){var i;eK(e)&&e.textContent&&(t+=null!=(i=e1(e.textContent))?i:"")}),E(t)}function e3(e){var t;return T(e.target)?e.srcElement||null:null!=(t=e.target)&&t.shadowRoot?e.composedPath()[0]||null:e.target||null}var e5=["a","button","form","input","select","textarea","label"];function e6(e){var t=e.parentNode;return!(!t||!eY(t))&&t}function e8(e){for(var t=e;t.parentNode&&!eJ(t,"body");t=t.parentNode){var i=e0(t);if(w(i,"ph-sensitive")||w(i,"ph-no-capture"))return!1}if(w(e0(e),"ph-include"))return!0;var s=e.type||"";if(O(s))switch(s.toLowerCase()){case"hidden":case"password":return!1}var r=e.name||e.id||"";return!(O(r)&&/^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i.test(r.replace(/[^a-zA-Z0-9]/g,"")))}function e4(e){return!!(eJ(e,"input")&&!["button","checkbox","submit","reset"].includes(e.type)||eJ(e,"select")||eJ(e,"textarea")||"true"===e.getAttribute("contenteditable"))}var e7="(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})",e9=RegExp("^(?:"+e7+")$"),te=new RegExp(e7),tt="\\d{3}-?\\d{2}-?\\d{4}",ti=RegExp("^("+tt+")$"),ts=RegExp("("+tt+")");function tr(e,t){return void 0===t&&(t=!0),!(D(e)||O(e)&&(e=E(e),(t?e9:te).test((e||"").replace(/[- ]/g,""))||(t?ti:ts).test(e)))&&!0}function tn(e){var t=e2(e);return tr(t=(t+" "+function e(t){var i="";return t&&t.childNodes&&t.childNodes.length&&K(t.childNodes,function(t){var s;if(t&&"span"===(null==(s=t.tagName)?void 0:s.toLowerCase()))try{var r=e2(t);i=(i+" "+r).trim(),t.childNodes&&t.childNodes.length&&(i=(i+" "+e(t)).trim())}catch(e){H.error("[AutoCapture]",e)}}),i}(e)).trim())?t:""}function ta(e){return e.replace(/"|\\"/g,'\\"')}class to{constructor(){this.clicks=[]}isRageClick(e,t,i){var s=this.clicks[this.clicks.length-1];if(s&&Math.abs(e-s.x)+Math.abs(t-s.y)<30&&i-s.timestamp<1e3){if(this.clicks.push({x:e,y:t,timestamp:i}),3===this.clicks.length)return!0}else this.clicks=[{x:e,y:t,timestamp:i}];return!1}}var tl=["localhost","127.0.0.1"],tc=e=>{var t=null==c?void 0:c.createElement("a");return T(t)?null:(t.href=e,t)},tu=function(e,t){void 0===t&&(t="&");var i,s,r=[];return K(e,function(e,t){T(e)||T(t)||"undefined"===t||(i=encodeURIComponent(e instanceof File?e.name:e.toString()),s=encodeURIComponent(t),r[r.length]=s+"="+i)}),r.join(t)},th=function(e,t){for(var i,s=((e.split("#")[0]||"").split(/\?(.*)/)[1]||"").replace(/^\?+/g,"").split("&"),r=0;r<s.length;r++){var n=s[r].split("=");if(n[0]===t){i=n;break}}if(!F(i)||i.length<2)return"";var a=i[1];try{a=decodeURIComponent(a)}catch(e){H.error("Skipping decoding for malformed query param: "+a)}return a.replace(/\+/g," ")},td=function(e,t,i){if(!e||!t||!t.length)return e;for(var s=e.split("#"),r=s[0]||"",n=s[1],a=r.split("?"),o=a[1],l=a[0],c=(o||"").split("&"),u=[],h=0;h<c.length;h++){var d=c[h].split("=");F(d)&&(t.includes(d[0])?u.push(d[0]+"="+i):u.push(c[h]))}var p=l;return null!=o&&(p+="?"+u.join("&")),null!=n&&(p+="#"+n),p},tp=function(e,t){var i=e.match(RegExp(t+"=([^&]*)"));return i?i[1]:null},tg=z("[AutoCapture]");function t_(e,t){return t.length>e?t.slice(0,e)+"...":t}class tv{constructor(e){this.i=!1,this.o=null,this.rageclicks=new to,this.h=!1,this.instance=e,this.m=null}get S(){var e,t,i=P(this.instance.config.autocapture)?this.instance.config.autocapture:{};return i.url_allowlist=null==(e=i.url_allowlist)?void 0:e.map(e=>new RegExp(e)),i.url_ignorelist=null==(t=i.url_ignorelist)?void 0:t.map(e=>new RegExp(e)),i}$(){if(this.isBrowserSupported()){if(s&&c){var e=e=>{e=e||(null==s?void 0:s.event);try{this.k(e)}catch(e){tg.error("Failed to capture event",e)}};if(en(c,"submit",e,{capture:!0}),en(c,"change",e,{capture:!0}),en(c,"click",e,{capture:!0}),this.S.capture_copied_text){var t=e=>{e=e||(null==s?void 0:s.event),this.k(e,f)};en(c,"copy",t,{capture:!0}),en(c,"cut",t,{capture:!0})}}}else tg.info("Disabling Automatic Event Collection because this browser is not supported")}startIfEnabled(){this.isEnabled&&!this.i&&(this.$(),this.i=!0)}onRemoteConfig(e){e.elementsChainAsString&&(this.h=e.elementsChainAsString),this.instance.persistence&&this.instance.persistence.register({[ec]:!!e.autocapture_opt_out}),this.o=!!e.autocapture_opt_out,this.startIfEnabled()}setElementSelectors(e){this.m=e}getElementSelectors(e){var t,i=[];return null==(t=this.m)||t.forEach(t=>{var s=null==c?void 0:c.querySelectorAll(t);null==s||s.forEach(s=>{e===s&&i.push(t)})}),i}get isEnabled(){var e,t,i=null==(e=this.instance.persistence)?void 0:e.props[ec];if(M(this.o)&&!N(i)&&!this.instance.I())return!1;var s=null!=(t=this.o)?t:!!i;return!!this.instance.config.autocapture&&!s}k(e,t){if(void 0===t&&(t="$autocapture"),this.isEnabled){var i,r=e3(e);eK(r)&&(r=r.parentNode||null),"$autocapture"===t&&"click"===e.type&&e instanceof MouseEvent&&this.instance.config.rageclick&&null!=(i=this.rageclicks)&&i.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this.k(e,"$rageclick");var n=t===f;if(r&&function(e,t,i,r,n){if(void 0===i&&(i=void 0),!s||!e||eJ(e,"html")||!eY(e)||null!=(a=i)&&a.url_allowlist&&!eQ(i.url_allowlist)||null!=(o=i)&&o.url_ignorelist&&eQ(i.url_ignorelist))return!1;if(null!=(l=i)&&l.dom_event_allowlist){var a,o,l,c=i.dom_event_allowlist;if(c&&!c.some(e=>t.type===e))return!1}for(var u=!1,h=[e],d=!0,p=e;p.parentNode&&!eJ(p,"body");)if(eZ(p.parentNode))h.push(p.parentNode.host),p=p.parentNode.host;else{if(!(d=e6(p)))break;if(r||e5.indexOf(d.tagName.toLowerCase())>-1)u=!0;else{var g=s.getComputedStyle(d);g&&"pointer"===g.getPropertyValue("cursor")&&(u=!0)}h.push(d),p=d}if(!function(e,t){var i=null==t?void 0:t.element_allowlist;if(T(i))return!0;var s,r=function(e){if(i.some(t=>e.tagName.toLowerCase()===t))return{v:!0}};for(var n of e)if(s=r(n))return s.v;return!1}(h,i)||!function(e,t){var i=null==t?void 0:t.css_selector_allowlist;if(T(i))return!0;var s,r=function(e){if(i.some(t=>e.matches(t)))return{v:!0}};for(var n of e)if(s=r(n))return s.v;return!1}(h,i))return!1;var _=s.getComputedStyle(e);if(_&&"pointer"===_.getPropertyValue("cursor")&&"click"===t.type)return!0;var v=e.tagName.toLowerCase();switch(v){case"html":return!1;case"form":return(n||["submit"]).indexOf(t.type)>=0;case"input":case"select":case"textarea":return(n||["change","click"]).indexOf(t.type)>=0;default:return u?(n||["click"]).indexOf(t.type)>=0:(n||["click"]).indexOf(t.type)>=0&&(e5.indexOf(v)>-1||"true"===e.getAttribute("contenteditable"))}}(r,e,this.S,n,n?["copy","cut"]:void 0)){var{props:a,explicitNoCapture:o}=function(e,t){for(var i,r,{e:n,maskAllElementAttributes:a,maskAllText:o,elementAttributeIgnoreList:l,elementsChainAsString:c}=t,u=[e],h=e;h.parentNode&&!eJ(h,"body");)eZ(h.parentNode)?(u.push(h.parentNode.host),h=h.parentNode.host):(u.push(h.parentNode),h=h.parentNode);var d,p=[],g={},_=!1,v=!1;if(K(u,e=>{var t=e8(e);"a"===e.tagName.toLowerCase()&&(_=e.getAttribute("href"),_=t&&_&&tr(_)&&_),w(e0(e),"ph-no-capture")&&(v=!0),p.push(function(e,t,i,s){var r=e.tagName.toLowerCase(),n={tag_name:r};e5.indexOf(r)>-1&&!i&&("a"===r.toLowerCase()||"button"===r.toLowerCase()?n.$el_text=t_(1024,tn(e)):n.$el_text=t_(1024,e2(e)));var a=e0(e);a.length>0&&(n.classes=a.filter(function(e){return""!==e})),K(e.attributes,function(i){var r;if((!e4(e)||-1!==["name","id","class","aria-label"].indexOf(i.name))&&(null==s||!s.includes(i.name))&&!t&&tr(i.value)&&(!O(r=i.name)||"_ngcontent"!==r.substring(0,10)&&"_nghost"!==r.substring(0,7))){var a=i.value;"class"===i.name&&(a=eX(a).join(" ")),n["attr__"+i.name]=t_(1024,a)}});for(var o=1,l=1,c=e;c=function(e){if(e.previousElementSibling)return e.previousElementSibling;var t=e;do t=t.previousSibling;while(t&&!eY(t));return t}(c);)o++,c.tagName===e.tagName&&l++;return n.nth_child=o,n.nth_of_type=l,n}(e,a,o,l)),Z(g,function(e){if(!e8(e))return{};var t={};return K(e.attributes,function(e){if(e.name&&0===e.name.indexOf("data-ph-capture-attribute")){var i=e.name.replace("data-ph-capture-attribute-",""),s=e.value;i&&s&&tr(s)&&(t[i]=s)}}),t}(e))}),v)return{props:{},explicitNoCapture:v};if(o||("a"===e.tagName.toLowerCase()||"button"===e.tagName.toLowerCase()?p[0].$el_text=tn(e):p[0].$el_text=e2(e)),_){p[0].attr__href=_;var f,m,y=null==(f=tc(_))?void 0:f.host,b=null==s||null==(m=s.location)?void 0:m.host;y&&b&&y!==b&&(d=_)}return{props:Z({$event_type:n.type,$ce_version:1},c?{}:{$elements:p},{$elements_chain:p.map(e=>{var t,i,s,r={text:null==(i=e.$el_text)?void 0:i.slice(0,400),tag_name:e.tag_name,href:null==(s=e.attr__href)?void 0:s.slice(0,2048),attr_class:(t=e.attr__class)?F(t)?t:eX(t):void 0,attr_id:e.attr__id,nth_child:e.nth_child,nth_of_type:e.nth_of_type,attributes:{}};return Q(e).filter(e=>{var[t]=e;return 0===t.indexOf("attr__")}).forEach(e=>{var[t,i]=e;return r.attributes[t]=i}),r}).map(e=>{var t,i,s="";if(e.tag_name&&(s+=e.tag_name),e.attr_class)for(var r of(e.attr_class.sort(),e.attr_class))s+="."+r.replace(/"/g,"");var n=V({},e.text?{text:e.text}:{},{"nth-child":null!=(t=e.nth_child)?t:0,"nth-of-type":null!=(i=e.nth_of_type)?i:0},e.href?{href:e.href}:{},e.attr_id?{attr_id:e.attr_id}:{},e.attributes),a={};return Q(n).sort((e,t)=>{var[i]=e,[s]=t;return i.localeCompare(s)}).forEach(e=>{var[t,i]=e;return a[ta(t.toString())]=ta(i.toString())}),s+=":",s+=Q(a).map(e=>{var[t,i]=e;return t+'="'+i+'"'}).join("")}).join(";")},null!=(i=p[0])&&i.$el_text?{$el_text:null==(r=p[0])?void 0:r.$el_text}:{},d&&"click"===n.type?{$external_click_url:d}:{},g)}}(r,{e:e,maskAllElementAttributes:this.instance.config.mask_all_element_attributes,maskAllText:this.instance.config.mask_all_text,elementAttributeIgnoreList:this.S.element_attribute_ignorelist,elementsChainAsString:this.h});if(o)return!1;var l=this.getElementSelectors(r);if(l&&l.length>0&&(a.$element_selectors=l),t===f){var c,u=e1(null==s||null==(c=s.getSelection())?void 0:c.toString()),h=e.type||"clipboard";if(!u)return!1;a.$selected_content=u,a.$copy_type=h}return this.instance.capture(t,a),!0}}}isBrowserSupported(){return C(null==c?void 0:c.querySelectorAll)}}Math.trunc||(Math.trunc=function(e){return e<0?Math.ceil(e):Math.floor(e)}),Number.isInteger||(Number.isInteger=function(e){return L(e)&&isFinite(e)&&Math.floor(e)===e});var tf="0123456789abcdef";class tm{constructor(e){if(this.bytes=e,16!==e.length)throw TypeError("not 128-bit length")}static fromFieldsV7(e,t,i,s){if(!Number.isInteger(e)||!Number.isInteger(t)||!Number.isInteger(i)||!Number.isInteger(s)||e<0||t<0||i<0||s<0||e>0xffffffffffff||t>4095||i>0x3fffffff||s>0xffffffff)throw RangeError("invalid field value");var r=new Uint8Array(16);return r[0]=e/0x10000000000,r[1]=e/0x100000000,r[2]=e/0x1000000,r[3]=e/65536,r[4]=e/256,r[5]=e,r[6]=112|t>>>8,r[7]=t,r[8]=128|i>>>24,r[9]=i>>>16,r[10]=i>>>8,r[11]=i,r[12]=s>>>24,r[13]=s>>>16,r[14]=s>>>8,r[15]=s,new tm(r)}toString(){for(var e="",t=0;t<this.bytes.length;t++)e=e+tf.charAt(this.bytes[t]>>>4)+tf.charAt(15&this.bytes[t]),3!==t&&5!==t&&7!==t&&9!==t||(e+="-");if(36!==e.length)throw Error("Invalid UUIDv7 was generated");return e}clone(){return new tm(this.bytes.slice(0))}equals(e){return 0===this.compareTo(e)}compareTo(e){for(var t=0;t<16;t++){var i=this.bytes[t]-e.bytes[t];if(0!==i)return Math.sign(i)}return 0}}class ty{constructor(){this.P=0,this.R=0,this.T=new tE}generate(){var e=this.generateOrAbort();if(T(e)){this.P=0;var t=this.generateOrAbort();if(T(t))throw Error("Could not generate UUID after timestamp reset");return t}return e}generateOrAbort(){var e=Date.now();if(e>this.P)this.P=e,this.M();else{if(!(e+1e4>this.P))return;this.R++,this.R>0x3ffffffffff&&(this.P++,this.M())}return tm.fromFieldsV7(this.P,Math.trunc(this.R/0x40000000),0x3fffffff&this.R,this.T.nextUint32())}M(){this.R=1024*this.T.nextUint32()+(1023&this.T.nextUint32())}}var tb,tw=e=>{if("undefined"!=typeof UUIDV7_DENY_WEAK_RNG&&UUIDV7_DENY_WEAK_RNG)throw Error("no cryptographically strong RNG available");for(var t=0;t<e.length;t++)e[t]=65536*Math.trunc(65536*Math.random())+Math.trunc(65536*Math.random());return e};s&&!T(s.crypto)&&crypto.getRandomValues&&(tw=e=>crypto.getRandomValues(e));class tE{constructor(){this.C=new Uint32Array(8),this.F=1/0}nextUint32(){return this.F>=this.C.length&&(tw(this.C),this.F=0),this.C[this.F++]}}var tS=()=>tx().toString(),tx=()=>(tb||(tb=new ty)).generate(),tk="",t$=/[a-z0-9][a-z0-9-]+\.[a-z]{2,}$/i,tI={O:()=>!!c,A:function(e){H.error("cookieStore error: "+e)},D:function(e){if(c){try{for(var t=e+"=",i=c.cookie.split(";").filter(e=>e.length),s=0;s<i.length;s++){for(var r=i[s];" "==r.charAt(0);)r=r.substring(1,r.length);if(0===r.indexOf(t))return decodeURIComponent(r.substring(t.length,r.length))}}catch(e){}return null}},L:function(e){var t;try{t=JSON.parse(tI.D(e))||{}}catch(e){}return t},j:function(e,t,i,s,r){if(c)try{var n="",a="",o=function(e,t){if(t){var i=function(e,t){if(void 0===t&&(t=c),tk)return tk;if(!t||["localhost","127.0.0.1"].includes(e))return"";for(var i=e.split("."),s=Math.min(i.length,8),r="dmn_chk_"+tS();!tk&&s--;){var n=i.slice(s).join("."),a=r+"=1;domain=."+n+";path=/";t.cookie=a+";max-age=3",t.cookie.includes(r)&&(t.cookie=a+";max-age=0",tk=n)}return tk}(e);if(!i){var s,r=(s=e.match(t$))?s[0]:"";r!==i&&H.info("Warning: cookie subdomain discovery mismatch",r,i),i=r}return i?"; domain=."+i:""}return""}(c.location.hostname,s);if(i){var l=new Date;l.setTime(l.getTime()+24*i*36e5),n="; expires="+l.toUTCString()}r&&(a="; secure");var u=e+"="+encodeURIComponent(JSON.stringify(t))+n+"; SameSite=Lax; path=/"+o+a;return u.length>3686.4&&H.warn("cookieStore warning: large cookie, len="+u.length),c.cookie=u,u}catch(e){return}},N:function(e,t){try{tI.j(e,"",-1,t)}catch(e){return}}},tF=null,tC={O:function(){if(!M(tF))return tF;var e=!0;if(T(s))e=!1;else try{var t="__mplssupport__";tC.j(t,"xyz"),'"xyz"'!==tC.D(t)&&(e=!1),tC.N(t)}catch(t){e=!1}return e||H.error("localStorage unsupported; falling back to cookie store"),tF=e,e},A:function(e){H.error("localStorage error: "+e)},D:function(e){try{return null==s?void 0:s.localStorage.getItem(e)}catch(e){tC.A(e)}return null},L:function(e){try{return JSON.parse(tC.D(e))||{}}catch(e){}return null},j:function(e,t){try{null==s||s.localStorage.setItem(e,JSON.stringify(t))}catch(e){tC.A(e)}},N:function(e){try{null==s||s.localStorage.removeItem(e)}catch(e){tC.A(e)}}},tP=["distinct_id",ek,e$,ez,eH],tR=V({},tC,{L:function(e){try{var t={};try{t=tI.L(e)||{}}catch(e){}var i=Z(t,JSON.parse(tC.D(e)||"{}"));return tC.j(e,i),i}catch(e){}return null},j:function(e,t,i,s,r,n){try{tC.j(e,t,void 0,void 0,n);var a={};tP.forEach(e=>{t[e]&&(a[e]=t[e])}),Object.keys(a).length&&tI.j(e,a,i,s,r,n)}catch(e){tC.A(e)}},N:function(e,t){try{null==s||s.localStorage.removeItem(e),tI.N(e,t)}catch(e){tC.A(e)}}}),tT={},tO={O:function(){return!0},A:function(e){H.error("memoryStorage error: "+e)},D:function(e){return tT[e]||null},L:function(e){return tT[e]||null},j:function(e,t){tT[e]=t},N:function(e){delete tT[e]}},tA=null,tM={O:function(){if(!M(tA))return tA;if(tA=!0,T(s))tA=!1;else try{var e="__support__";tM.j(e,"xyz"),'"xyz"'!==tM.D(e)&&(tA=!1),tM.N(e)}catch(e){tA=!1}return tA},A:function(e){H.error("sessionStorage error: ",e)},D:function(e){try{return null==s?void 0:s.sessionStorage.getItem(e)}catch(e){tM.A(e)}return null},L:function(e){try{return JSON.parse(tM.D(e))||null}catch(e){}return null},j:function(e,t){try{null==s||s.sessionStorage.setItem(e,JSON.stringify(t))}catch(e){tM.A(e)}},N:function(e){try{null==s||s.sessionStorage.removeItem(e)}catch(e){tM.A(e)}}},tD=function(e){return e[e.PENDING=-1]="PENDING",e[e.DENIED=0]="DENIED",e[e.GRANTED=1]="GRANTED",e}({});class tL{constructor(e){this._instance=e}get S(){return this._instance.config}get consent(){return this.U()?tD.DENIED:this.q}isOptedOut(){return this.consent===tD.DENIED||this.consent===tD.PENDING&&this.S.opt_out_capturing_by_default}isOptedIn(){return!this.isOptedOut()}optInOut(e){this.B.j(this.H,+!!e,this.S.cookie_expiration,this.S.cross_subdomain_cookie,this.S.secure_cookie)}reset(){this.B.N(this.H,this.S.cross_subdomain_cookie)}get H(){var{token:e,opt_out_capturing_cookie_prefix:t}=this._instance.config;return(t||"__ph_opt_in_out_")+e}get q(){var e=this.B.D(this.H);return"1"===e?tD.GRANTED:"0"===e?tD.DENIED:tD.PENDING}get B(){if(!this.W){var e=this.S.opt_out_capturing_persistence_type;this.W="localStorage"===e?tC:tI;var t="localStorage"===e?tI:tC;t.D(this.H)&&(this.W.D(this.H)||this.optInOut("1"===t.D(this.H)),t.N(this.H,this.S.cross_subdomain_cookie))}return this.W}U(){return!!this.S.respect_dnt&&!!er([null==l?void 0:l.doNotTrack,null==l?void 0:l.msDoNotTrack,_.doNotTrack],e=>w([!0,1,"1","yes"],e))}}var tN=z("[Dead Clicks]"),tq=()=>!0,tj=e=>{var t,i=!(null==(t=e.instance.persistence)||!t.get_property(e_)),s=e.instance.config.capture_dead_clicks;return N(s)?s:i};class tB{get lazyLoadedDeadClicksAutocapture(){return this.G}constructor(e,t,i){this.instance=e,this.isEnabled=t,this.onCapture=i,this.startIfEnabled()}onRemoteConfig(e){this.instance.persistence&&this.instance.persistence.register({[e_]:null==e?void 0:e.captureDeadClicks}),this.startIfEnabled()}startIfEnabled(){this.isEnabled(this)&&this.J(()=>{this.V()})}J(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.initDeadClicksAutocapture&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this.instance,"dead-clicks-autocapture",t=>{t?tN.error("failed to load script",t):e()})}V(){var e;if(c){if(!this.G&&null!=(e=_.__PosthogExtensions__)&&e.initDeadClicksAutocapture){var t=P(this.instance.config.capture_dead_clicks)?this.instance.config.capture_dead_clicks:{};t.__onCapture=this.onCapture,this.G=_.__PosthogExtensions__.initDeadClicksAutocapture(this.instance,t),this.G.start(c),tN.info("starting...")}}else tN.error("`document` not found. Cannot start.")}stop(){this.G&&(this.G.stop(),this.G=void 0,tN.info("stopping..."))}}function tH(e,t,i,s,r){return t>i&&(H.warn("min cannot be greater than max."),t=i),L(e)?e>i?(s&&H.warn(s+" cannot be  greater than max: "+i+". Using max value instead."),i):e<t?(s&&H.warn(s+" cannot be less than min: "+t+". Using min value instead."),t):e:(s&&H.warn(s+" must be a number. using max or fallback. max: "+i+", fallback: "+r),tH(r||i,t,i,s))}class tz{constructor(e){this.K={},this.Y=()=>{Object.keys(this.K).forEach(e=>{var t=this.X(e)+this.Z;t>=this.tt?delete this.K[e]:this.it(e,t)})},this.X=e=>this.K[String(e)],this.it=(e,t)=>{this.K[String(e)]=t},this.consumeRateLimit=e=>{var t,i=null!=(t=this.X(e))?t:this.tt;if(0===(i=Math.max(i-1,0)))return!0;this.it(e,i);var s,r=0===i;return r&&(null==(s=this.et)||s.call(this,e)),r},this.rt=e,this.et=this.rt.et,this.tt=tH(this.rt.bucketSize,0,100,"rate limiter bucket size"),this.Z=tH(this.rt.refillRate,0,this.tt,"rate limiter refill rate"),this.st=tH(this.rt.refillInterval,0,864e5,"rate limiter refill interval"),setInterval(()=>{this.Y()},this.st)}}var tU=z("[ExceptionAutocapture]");class tG{constructor(e){var t,i,r;this.nt=()=>{var e;if(s&&this.isEnabled&&null!=(e=_.__PosthogExtensions__)&&e.errorWrappingFunctions){var t=_.__PosthogExtensions__.errorWrappingFunctions.wrapOnError,i=_.__PosthogExtensions__.errorWrappingFunctions.wrapUnhandledRejection,r=_.__PosthogExtensions__.errorWrappingFunctions.wrapConsoleError;try{!this.ot&&this.S.capture_unhandled_errors&&(this.ot=t(this.captureException.bind(this))),!this.lt&&this.S.capture_unhandled_rejections&&(this.lt=i(this.captureException.bind(this))),!this.ut&&this.S.capture_console_errors&&(this.ut=r(this.captureException.bind(this)))}catch(e){tU.error("failed to start",e),this.ht()}}},this._instance=e,this.dt=!(null==(t=this._instance.persistence)||!t.props[eh]),this.S=this.vt(),this.ct=new tz({refillRate:null!=(i=this._instance.config.error_tracking.__exceptionRateLimiterRefillRate)?i:1,bucketSize:null!=(r=this._instance.config.error_tracking.__exceptionRateLimiterBucketSize)?r:10,refillInterval:1e4}),this.startIfEnabled()}vt(){var e=this._instance.config.capture_exceptions,t={capture_unhandled_errors:!1,capture_unhandled_rejections:!1,capture_console_errors:!1};return P(e)?t=V({},t,e):(T(e)?this.dt:e)&&(t=V({},t,{capture_unhandled_errors:!0,capture_unhandled_rejections:!0})),t}get isEnabled(){return this.S.capture_console_errors||this.S.capture_unhandled_errors||this.S.capture_unhandled_rejections}startIfEnabled(){this.isEnabled&&(tU.info("enabled"),this.J(this.nt))}J(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.errorWrappingFunctions&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"exception-autocapture",t=>{if(t)return tU.error("failed to load script",t);e()})}ht(){var e,t,i;null==(e=this.ot)||e.call(this),this.ot=void 0,null==(t=this.lt)||t.call(this),this.lt=void 0,null==(i=this.ut)||i.call(this),this.ut=void 0}onRemoteConfig(e){var t=e.autocaptureExceptions;this.dt=!!t,this.S=this.vt(),this._instance.persistence&&this._instance.persistence.register({[eh]:this.dt}),this.startIfEnabled()}captureException(e){e.$exception_personURL=this._instance.requestRouter.endpointFor("ui")+"/project/"+this._instance.config.token+"/person/"+this._instance.get_distinct_id();var t,i=null!=(t=e.$exception_list[0].type)?t:"Exception";this.ct.consumeRateLimit(i)?tU.info("Skipping exception capture because of client rate limiting.",{exception:e.$exception_list[0].type}):this._instance.exceptions.sendExceptionEvent(e)}}function tV(e){return!T(Event)&&tW(e,Event)}function tW(e,t){try{return e instanceof t}catch(e){return!1}}function tY(e){switch(Object.prototype.toString.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object DOMError]":return!0;default:return tW(e,Error)}}function tJ(e,t){return Object.prototype.toString.call(e)==="[object "+t+"]"}function tK(e){return tJ(e,"DOMError")}var tZ=/\(error: (.*)\)/;function tX(e,t,i,s){var r={platform:"web:javascript",filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return T(i)||(r.lineno=i),T(s)||(r.colno=s),r}var tQ,t0,t1,t2=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,t3=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,t5=/\((\S*)(?::(\d+))(?::(\d+))\)/,t6=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,t8=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,t4=function(){for(var e=arguments.length,t=Array(e),i=0;i<e;i++)t[i]=arguments[i];var s=t.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return function(e,t){void 0===t&&(t=0);for(var i=[],r=e.split("\n"),n=t;n<r.length;n++){var a=r[n];if(!(a.length>1024)){var o=tZ.test(a)?a.replace(tZ,"$1"):a;if(!o.match(/\S*Error: /)){for(var l of s){var c=l(o);if(c){i.push(c);break}}if(i.length>=50)break}}}if(!i.length)return[];var u=Array.from(i);return u.reverse(),u.slice(0,50).map(e=>{var t;return V({},e,{filename:e.filename||((t=u)[t.length-1]||{}).filename,function:e.function||"?"})})}}([30,e=>{var t=t2.exec(e);if(t){var[,i,s,r]=t;return tX(i,"?",+s,+r)}var n=t3.exec(e);if(n){if(n[2]&&0===n[2].indexOf("eval")){var a=t5.exec(n[2]);a&&(n[2]=a[1],n[3]=a[2],n[4]=a[3])}var[o,l]=t7(n[1]||"?",n[2]);return tX(l,o,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,e=>{var t=t6.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){var i=t8.exec(t[3]);i&&(t[1]=t[1]||"eval",t[3]=i[1],t[4]=i[2],t[5]="")}var s=t[3],r=t[1]||"?";return[r,s]=t7(r,s),tX(s,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),t7=(e,t)=>{var i=-1!==e.indexOf("safari-extension"),s=-1!==e.indexOf("safari-web-extension");return i||s?[-1!==e.indexOf("@")?e.split("@")[0]:"?",i?"safari-extension:"+t:"safari-web-extension:"+t]:[e,t]},t9=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?(.*)$/i;function ie(e,t){void 0===t&&(t=0);var i=e.stacktrace||e.stack||"",s=e&&it.test(e.message)?1:0;try{var r,n,a=(r=t4(i,s),n=function(e){var t=globalThis._posthogChunkIds;if(!t)return{};var i=Object.keys(t);return t1&&i.length===t0||(t0=i.length,t1=i.reduce((i,s)=>{tQ||(tQ={});var r=tQ[s];if(r)i[r[0]]=r[1];else for(var n=e(s),a=n.length-1;a>=0;a--){var o=n[a],l=null==o?void 0:o.filename,c=t[s];if(l&&c){i[l]=c,tQ[s]=[l,c];break}}return i},{})),t1}(t4),r.forEach(e=>{e.filename&&(e.chunk_id=n[e.filename])}),r);return a.slice(0,a.length-t)}catch(e){}return[]}var it=/Minified React error #\d+;/i;function ii(e,t){return{$exception_list:function e(t,i){var s,r,n,a,o,l,c,u,h=(s=t,r=i,l=ie(s),c=null==(a=null==r?void 0:r.handled)||a,u=null!=(o=null==r?void 0:r.synthetic)&&o,{type:null!=r&&r.overrideExceptionType?r.overrideExceptionType:s.name,value:(n=s.message).error&&"string"==typeof n.error.message?String(n.error.message):String(n),stacktrace:{frames:l,type:"raw"},mechanism:{handled:c,synthetic:u}});return t.cause&&tY(t.cause)&&t.cause!==t?[h,...e(t.cause,{handled:null==i?void 0:i.handled,synthetic:null==i?void 0:i.synthetic})]:[h]}(e,t),$exception_level:"error"}}function is(e,t){var i,s,r,n=null==(i=null==t?void 0:t.handled)||i,a=null==(s=null==t?void 0:t.synthetic)||s,o={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:null!=(r=null==t?void 0:t.defaultExceptionType)?r:"Error",value:e||(null==t?void 0:t.defaultExceptionMessage),mechanism:{handled:n,synthetic:a}};if(null!=t&&t.syntheticException){var l=ie(t.syntheticException,1);l.length&&(o.stacktrace={frames:l,type:"raw"})}return{$exception_list:[o],$exception_level:"error"}}function ir(e,t,i){try{if(!(t in e))return()=>{};var s=e[t],r=i(s);return C(r)&&(r.prototype=r.prototype||{},Object.defineProperties(r,{__posthog_wrapped__:{enumerable:!1,value:!0}})),e[t]=r,()=>{e[t]=s}}catch(e){return()=>{}}}class ia{constructor(e){var t;this._instance=e,this.ft=(null==s||null==(t=s.location)?void 0:t.pathname)||""}get isEnabled(){return"history_change"===this._instance.config.capture_pageview}startIfEnabled(){this.isEnabled&&(H.info("History API monitoring enabled, starting..."),this.monitorHistoryChanges())}stop(){this._t&&this._t(),this._t=void 0,H.info("History API monitoring stopped")}monitorHistoryChanges(){var e,t;if(s&&s.history){var i=this;null!=(e=s.history.pushState)&&e.__posthog_wrapped__||ir(s.history,"pushState",e=>function(t,s,r){e.call(this,t,s,r),i.gt("pushState")}),null!=(t=s.history.replaceState)&&t.__posthog_wrapped__||ir(s.history,"replaceState",e=>function(t,s,r){e.call(this,t,s,r),i.gt("replaceState")}),this.bt()}}gt(e){try{var t,i=null==s||null==(t=s.location)?void 0:t.pathname;if(!i)return;i!==this.ft&&this.isEnabled&&this._instance.capture("$pageview",{navigation_type:e}),this.ft=i}catch(t){H.error("Error capturing "+e+" pageview",t)}}bt(){if(!this._t){var e=()=>{this.gt("popstate")};en(s,"popstate",e),this._t=()=>{s&&s.removeEventListener("popstate",e)}}}}function io(e){var t,i;return(null==(t=JSON.stringify(e,(i=[],function(e,t){if(P(t)){for(;i.length>0&&i[i.length-1]!==this;)i.pop();return i.includes(t)?"[Circular]":(i.push(t),t)}return t})))?void 0:t.length)||0}var il=(e=>(e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e))(il||{}),ic=(e=>(e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e[e.CustomElement=16]="CustomElement",e))(ic||{}),iu="[SessionRecording]",ih="redacted",id={initiatorTypes:["audio","beacon","body","css","early-hint","embed","fetch","frame","iframe","icon","image","img","input","link","navigation","object","ping","script","track","video","xmlhttprequest"],maskRequestFn:e=>e,recordHeaders:!1,recordBody:!1,recordInitialRequests:!1,recordPerformance:!1,performanceEntryTypeToObserve:["first-input","navigation","paint","resource"],payloadSizeLimitBytes:1e6,payloadHostDenyList:[".lr-ingest.io",".ingest.sentry.io",".clarity.ms","analytics.google.com","bam.nr-data.net"]},ip=["authorization","x-forwarded-for","authorization","cookie","set-cookie","x-api-key","x-real-ip","remote-addr","forwarded","proxy-authorization","x-csrf-token","x-csrftoken","x-xsrf-token"],ig=["password","secret","passwd","api_key","apikey","auth","credentials","mysql_pwd","privatekey","private_key","token"],i_=["/s/","/e/","/i/"];function iv(e,t,i,s){if(D(e))return e;var r=(null==t?void 0:t["content-length"])||new Blob([e]).size;return O(r)&&(r=parseInt(r)),r>i?iu+" "+s+" body too large to record ("+r+" bytes)":e}function im(e,t){if(D(e))return e;var i=e;return tr(i,!1)||(i=iu+" "+t+" body "+ih),K(ig,e=>{var s,r;null!=(s=i)&&s.length&&-1!==(null==(r=i)?void 0:r.indexOf(e))&&(i=iu+" "+t+" body "+ih+" as might contain: "+e)}),i}var iy=(e,t)=>{var i,s,r={payloadSizeLimitBytes:id.payloadSizeLimitBytes,performanceEntryTypeToObserve:[...id.performanceEntryTypeToObserve],payloadHostDenyList:[...t.payloadHostDenyList||[],...id.payloadHostDenyList]},n=!1!==e.session_recording.recordHeaders&&t.recordHeaders,a=!1!==e.session_recording.recordBody&&t.recordBody,o=!1!==e.capture_performance&&t.recordPerformance,l=(s=Math.min(1e6,null!=(i=r.payloadSizeLimitBytes)?i:1e6),e=>(null!=e&&e.requestBody&&(e.requestBody=iv(e.requestBody,e.requestHeaders,s,"Request")),null!=e&&e.responseBody&&(e.responseBody=iv(e.responseBody,e.responseHeaders,s,"Response")),e)),c=t=>{var i;return l(((e,t)=>{var i,s=tc(e.name),r=0===t.indexOf("http")?null==(i=tc(t))?void 0:i.pathname:t;"/"===r&&(r="");var n=null==s?void 0:s.pathname.replace(r||"","");if(!(s&&n&&i_.some(e=>0===n.indexOf(e))))return e})((D(i=t.requestHeaders)||K(Object.keys(null!=i?i:{}),e=>{ip.includes(e.toLowerCase())&&(i[e]=ih)}),t),e.api_host))},u=C(e.session_recording.maskNetworkRequestFn);return u&&C(e.session_recording.maskCapturedNetworkRequestFn)&&H.warn("Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored."),u&&(e.session_recording.maskCapturedNetworkRequestFn=t=>{var i=e.session_recording.maskNetworkRequestFn({url:t.name});return V({},t,{name:null==i?void 0:i.url})}),r.maskRequestFn=C(e.session_recording.maskCapturedNetworkRequestFn)?t=>{var i,s=c(t);return s&&null!=(i=null==e.session_recording.maskCapturedNetworkRequestFn?void 0:e.session_recording.maskCapturedNetworkRequestFn(s))?i:void 0}:e=>(function(e){if(!T(e))return e.requestBody=im(e.requestBody,"Request"),e.responseBody=im(e.responseBody,"Response"),e})(c(e)),V({},id,r,{recordHeaders:n,recordBody:a,recordPerformance:o,recordInitialRequests:o})};class ib{constructor(e,t){var i,s;void 0===t&&(t={}),this.yt={},this.wt=e=>{if(!this.yt[e]){this.yt[e]=!0;var t,i,s=this.St(e);null==(t=(i=this.rt).onBlockedNode)||t.call(i,e,s)}},this.$t=e=>{var t=this.St(e);if("svg"!==(null==t?void 0:t.nodeName)&&t instanceof Element){var i=t.closest("svg");if(i)return[this._rrweb.mirror.getId(i),i]}return[e,t]},this.St=e=>this._rrweb.mirror.getNode(e),this.xt=e=>{var t,i,s,r,n,a,o,l;return(null!=(t=null==(i=e.removes)?void 0:i.length)?t:0)+(null!=(s=null==(r=e.attributes)?void 0:r.length)?s:0)+(null!=(n=null==(a=e.texts)?void 0:a.length)?n:0)+(null!=(o=null==(l=e.adds)?void 0:l.length)?o:0)},this.throttleMutations=e=>{if(3!==e.type||0!==e.data.source)return e;var t=e.data,i=this.xt(t);t.attributes&&(t.attributes=t.attributes.filter(e=>{var[t]=this.$t(e.id);return!this.ct.consumeRateLimit(t)&&e}));var s=this.xt(t);return 0!==s||i===s?e:void 0},this._rrweb=e,this.rt=t,this.ct=new tz({bucketSize:null!=(i=this.rt.bucketSize)?i:100,refillRate:null!=(s=this.rt.refillRate)?s:10,refillInterval:1e3,et:this.wt})}}var iw=Uint8Array,iE=Uint16Array,iS=Uint32Array,ix=new iw([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),ik=new iw([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),i$=new iw([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),iI=function(e,t){for(var i=new iE(31),s=0;s<31;++s)i[s]=t+=1<<e[s-1];var r=new iS(i[30]);for(s=1;s<30;++s)for(var n=i[s];n<i[s+1];++n)r[n]=n-i[s]<<5|s;return[i,r]},iF=iI(ix,2),iC=iF[0],iP=iF[1];iC[28]=258,iP[258]=28;for(var iR=iI(ik,0)[1],iT=new iE(32768),iO=0;iO<32768;++iO){var iA=(43690&iO)>>>1|(21845&iO)<<1;iA=(61680&(iA=(52428&iA)>>>2|(13107&iA)<<2))>>>4|(3855&iA)<<4,iT[iO]=((65280&iA)>>>8|(255&iA)<<8)>>>1}var iM=function(e,t,i){for(var s=e.length,r=0,n=new iE(t);r<s;++r)++n[e[r]-1];var a,o=new iE(t);for(r=0;r<t;++r)o[r]=o[r-1]+n[r-1]<<1;if(i){a=new iE(1<<t);var l=15-t;for(r=0;r<s;++r)if(e[r])for(var c=r<<4|e[r],u=t-e[r],h=o[e[r]-1]++<<u,d=h|(1<<u)-1;h<=d;++h)a[iT[h]>>>l]=c}else for(a=new iE(s),r=0;r<s;++r)a[r]=iT[o[e[r]-1]++]>>>15-e[r];return a},iD=new iw(288);for(iO=0;iO<144;++iO)iD[iO]=8;for(iO=144;iO<256;++iO)iD[iO]=9;for(iO=256;iO<280;++iO)iD[iO]=7;for(iO=280;iO<288;++iO)iD[iO]=8;var iL=new iw(32);for(iO=0;iO<32;++iO)iL[iO]=5;var iN=iM(iD,9,0),iq=iM(iL,5,0),ij=function(e){return(e/8|0)+(7&e&&1)},iB=function(e,t,i){(null==i||i>e.length)&&(i=e.length);var s=new(e instanceof iE?iE:e instanceof iS?iS:iw)(i-t);return s.set(e.subarray(t,i)),s},iH=function(e,t,i){i<<=7&t;var s=t/8|0;e[s]|=i,e[s+1]|=i>>>8},iz=function(e,t,i){i<<=7&t;var s=t/8|0;e[s]|=i,e[s+1]|=i>>>8,e[s+2]|=i>>>16},iU=function(e,t){for(var i=[],s=0;s<e.length;++s)e[s]&&i.push({s:s,f:e[s]});var r=i.length,n=i.slice();if(!r)return[new iw(0),0];if(1==r){var a=new iw(i[0].s+1);return a[i[0].s]=1,[a,1]}i.sort(function(e,t){return e.f-t.f}),i.push({s:-1,f:25001});var o=i[0],l=i[1],c=0,u=1,h=2;for(i[0]={s:-1,f:o.f+l.f,l:o,r:l};u!=r-1;)o=i[i[c].f<i[h].f?c++:h++],l=i[c!=u&&i[c].f<i[h].f?c++:h++],i[u++]={s:-1,f:o.f+l.f,l:o,r:l};var d=n[0].s;for(s=1;s<r;++s)n[s].s>d&&(d=n[s].s);var p=new iE(d+1),g=iG(i[u-1],p,0);if(g>t){s=0;var _=0,v=g-t,f=1<<v;for(n.sort(function(e,t){return p[t.s]-p[e.s]||e.f-t.f});s<r;++s){var m=n[s].s;if(!(p[m]>t))break;_+=f-(1<<g-p[m]),p[m]=t}for(_>>>=v;_>0;){var y=n[s].s;p[y]<t?_-=1<<t-p[y]++-1:++s}for(;s>=0&&_;--s){var b=n[s].s;p[b]==t&&(--p[b],++_)}g=t}return[new iw(p),g]},iG=function(e,t,i){return -1==e.s?Math.max(iG(e.l,t,i+1),iG(e.r,t,i+1)):t[e.s]=i},iV=function(e){for(var t=e.length;t&&!e[--t];);for(var i=new iE(++t),s=0,r=e[0],n=1,a=function(e){i[s++]=e},o=1;o<=t;++o)if(e[o]==r&&o!=t)++n;else{if(!r&&n>2){for(;n>138;n-=138)a(32754);n>2&&(a(n>10?n-11<<5|28690:n-3<<5|12305),n=0)}else if(n>3){for(a(r),--n;n>6;n-=6)a(8304);n>2&&(a(n-3<<5|8208),n=0)}for(;n--;)a(r);n=1,r=e[o]}return[i.subarray(0,s),t]},iW=function(e,t){for(var i=0,s=0;s<t.length;++s)i+=e[s]*t[s];return i},iY=function(e,t,i){var s=i.length,r=ij(t+2);e[r]=255&s,e[r+1]=s>>>8,e[r+2]=255^e[r],e[r+3]=255^e[r+1];for(var n=0;n<s;++n)e[r+n+4]=i[n];return 8*(r+4+s)},iJ=function(e,t,i,s,r,n,a,o,l,c,u){iH(t,u++,i),++r[256];for(var h=iU(r,15),d=h[0],p=h[1],g=iU(n,15),_=g[0],v=g[1],f=iV(d),m=f[0],y=f[1],b=iV(_),w=b[0],E=b[1],S=new iE(19),x=0;x<m.length;++x)S[31&m[x]]++;for(x=0;x<w.length;++x)S[31&w[x]]++;for(var k=iU(S,7),$=k[0],I=k[1],F=19;F>4&&!$[i$[F-1]];--F);var C,P,R,T,O=c+5<<3,A=iW(r,iD)+iW(n,iL)+a,M=iW(r,d)+iW(n,_)+a+14+3*F+iW(S,$)+(2*S[16]+3*S[17]+7*S[18]);if(O<=A&&O<=M)return iY(t,u,e.subarray(l,l+c));if(iH(t,u,1+(M<A)),u+=2,M<A){C=iM(d,p,0),P=d,R=iM(_,v,0),T=_;var D=iM($,I,0);for(iH(t,u,y-257),iH(t,u+5,E-1),iH(t,u+10,F-4),u+=14,x=0;x<F;++x)iH(t,u+3*x,$[i$[x]]);u+=3*F;for(var L=[m,w],N=0;N<2;++N){var q=L[N];for(x=0;x<q.length;++x){var j=31&q[x];iH(t,u,D[j]),u+=$[j],j>15&&(iH(t,u,q[x]>>>5&127),u+=q[x]>>>12)}}}else C=iN,P=iD,R=iq,T=iL;for(x=0;x<o;++x)if(s[x]>255){iz(t,u,C[(j=s[x]>>>18&31)+257]),u+=P[j+257],j>7&&(iH(t,u,s[x]>>>23&31),u+=ix[j]);var B=31&s[x];iz(t,u,R[B]),u+=T[B],B>3&&(iz(t,u,s[x]>>>5&8191),u+=ik[B])}else iz(t,u,C[s[x]]),u+=P[s[x]];return iz(t,u,C[256]),u+P[256]},iK=new iS([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),iZ=function(){for(var e=new iS(256),t=0;t<256;++t){for(var i=t,s=9;--s;)i=(1&i&&0xedb88320)^i>>>1;e[t]=i}return e}(),iX=function(){var e=0xffffffff;return{p:function(t){for(var i=e,s=0;s<t.length;++s)i=iZ[255&i^t[s]]^i>>>8;e=i},d:function(){return 0xffffffff^e}}},iQ=function(e,t,i){for(;i;++t)e[t]=i,i>>>=8},i0=function(e,t){var i=t.filename;if(e[0]=31,e[1]=139,e[2]=8,e[8]=t.level<2?4:2*(9==t.level),e[9]=3,0!=t.mtime&&iQ(e,4,Math.floor(new Date(t.mtime||Date.now())/1e3)),i){e[3]=8;for(var s=0;s<=i.length;++s)e[s+10]=i.charCodeAt(s)}};function i1(e,t){void 0===t&&(t={});var i,s,r,n=iX(),a=e.length;n.p(e);var o=(s=t,r=10+((i=t).filename&&i.filename.length+1||0),function(e,t,i,s,r,n){var a=e.length,o=new iw(s+a+5*(1+Math.floor(a/7e3))+8),l=o.subarray(s,o.length-r),c=0;if(!t||a<8)for(var u=0;u<=a;u+=65535){var h=u+65535;h<a?c=iY(l,c,e.subarray(u,h)):(l[u]=n,c=iY(l,c,e.subarray(u,a)))}else{for(var d=iK[t-1],p=d>>>13,g=8191&d,_=(1<<i)-1,v=new iE(32768),f=new iE(_+1),m=Math.ceil(i/3),y=2*m,b=function(t){return(e[t]^e[t+1]<<m^e[t+2]<<y)&_},w=new iS(25e3),E=new iE(288),S=new iE(32),x=0,k=0,$=(u=0,0),I=0,F=0;u<a;++u){var C=b(u),P=32767&u,R=f[C];if(v[P]=R,f[C]=P,I<=u){var T=a-u;if((x>7e3||$>24576)&&T>423){c=iJ(e,l,0,w,E,S,k,$,F,u-F,c),$=x=k=0,F=u;for(var O=0;O<286;++O)E[O]=0;for(O=0;O<30;++O)S[O]=0}var A=2,M=0,D=g,L=P-R&32767;if(T>2&&C==b(u-L))for(var N=Math.min(p,T)-1,q=Math.min(32767,u),j=Math.min(258,T);L<=q&&--D&&P!=R;){if(e[u+A]==e[u+A-L]){for(var B=0;B<j&&e[u+B]==e[u+B-L];++B);if(B>A){if(A=B,M=L,B>N)break;var H=Math.min(L,B-2),z=0;for(O=0;O<H;++O){var U=u-L+O+32768&32767,G=U-v[U]+32768&32767;G>z&&(z=G,R=U)}}}L+=(P=R)-(R=v[P])+32768&32767}if(M){w[$++]=0x10000000|iP[A]<<18|iR[M];var V=31&iP[A],W=31&iR[M];k+=ix[V]+ik[W],++E[257+V],++S[W],I=u+A,++x}else w[$++]=e[u],++E[e[u]]}}c=iJ(e,l,n,w,E,S,k,$,F,u-F,c)}return iB(o,0,s+ij(c)+r)}(e,null==s.level?6:s.level,null==s.mem?Math.ceil(1.5*Math.max(8,Math.min(13,Math.log(e.length)))):12+s.mem,r,8,!0)),l=o.length;return i0(o,t),iQ(o,l-8,n.d()),iQ(o,l-4,a),o}function i2(e,t){var i=e.length;if("undefined"!=typeof TextEncoder)return(new TextEncoder).encode(e);for(var s=new iw(e.length+(e.length>>>1)),r=0,n=function(e){s[r++]=e},a=0;a<i;++a){if(r+5>s.length){var o=new iw(r+8+(i-a<<1));o.set(s),s=o}var l=e.charCodeAt(a);l<128||t?n(l):(l<2048?n(192|l>>>6):(l>55295&&l<57344?(n(240|(l=65536+(1047552&l)|1023&e.charCodeAt(++a))>>>18),n(128|l>>>12&63)):n(224|l>>>12),n(128|l>>>6&63)),n(128|63&l))}return iB(s,0,r)}var i3="disabled",i5="sampled",i6="active",i8="buffering",i4="paused",i7="trigger",i9=i7+"_activated",se=i7+"_pending",st=i7+"_"+i3;function si(e,t){return t.some(t=>"regex"===t.matching&&new RegExp(t.url).test(e))}class ss{constructor(e){this.kt=e}triggerStatus(e){var t=this.kt.map(t=>t.triggerStatus(e));return t.includes(i9)?i9:t.includes(se)?se:st}stop(){this.kt.forEach(e=>e.stop())}}class sr{constructor(e){this.kt=e}triggerStatus(e){var t=new Set;for(var i of this.kt)t.add(i.triggerStatus(e));switch(t.delete(st),t.size){case 0:return st;case 1:return Array.from(t)[0];default:return se}}stop(){this.kt.forEach(e=>e.stop())}}class sn{triggerStatus(){return se}stop(){}}class sa{constructor(e){this.Et=[],this.It=[],this.urlBlocked=!1,this._instance=e}onRemoteConfig(e){var t,i;this.Et=(null==(t=e.sessionRecording)?void 0:t.urlTriggers)||[],this.It=(null==(i=e.sessionRecording)?void 0:i.urlBlocklist)||[]}Pt(e){var t;return 0===this.Et.length?st:(null==(t=this._instance)?void 0:t.get_property(eI))===e?i9:se}triggerStatus(e){var t=this.Pt(e),i=t===i9?i9:t===se?se:st;return this._instance.register_for_session({$sdk_debug_replay_url_trigger_status:i}),i}checkUrlTriggerConditions(e,t,i){if(void 0!==s&&s.location.href){var r=s.location.href,n=this.urlBlocked,a=si(r,this.It);n&&a||(a&&!n?e():!a&&n&&t(),si(r,this.Et)&&i("url"))}}stop(){}}class so{constructor(e){this.linkedFlag=null,this.linkedFlagSeen=!1,this.Rt=()=>{},this._instance=e}triggerStatus(){var e=se;return D(this.linkedFlag)&&(e=st),this.linkedFlagSeen&&(e=i9),this._instance.register_for_session({$sdk_debug_replay_linked_flag_trigger_status:e}),e}onRemoteConfig(e,t){var i;if(this.linkedFlag=(null==(i=e.sessionRecording)?void 0:i.linkedFlag)||null,!D(this.linkedFlag)&&!this.linkedFlagSeen){var s=O(this.linkedFlag)?this.linkedFlag:this.linkedFlag.flag,r=O(this.linkedFlag)?null:this.linkedFlag.variant;this.Rt=this._instance.onFeatureFlags((e,i)=>{var n=!1;if(P(i)&&s in i){var a=i[s];n=N(a)?!0===a:r?a===r:!!a}this.linkedFlagSeen=n,n&&t(s,r)})}}stop(){this.Rt()}}class sl{constructor(e){this.Tt=[],this._instance=e}onRemoteConfig(e){var t;this.Tt=(null==(t=e.sessionRecording)?void 0:t.eventTriggers)||[]}Mt(e){var t;return 0===this.Tt.length?st:(null==(t=this._instance)?void 0:t.get_property(eF))===e?i9:se}triggerStatus(e){var t=this.Mt(e),i=t===i9?i9:t===se?se:st;return this._instance.register_for_session({$sdk_debug_replay_event_trigger_status:i}),i}stop(){}}function sc(e){return e.isRecordingEnabled?i8:i3}function su(e){if(!e.receivedFlags)return i8;if(!e.isRecordingEnabled)return i3;if(e.urlTriggerMatching.urlBlocked)return i4;var t=!0===e.isSampled,i=new ss([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId);return t?i5:i===i9?i6:i===se?i8:!1===e.isSampled?i3:i6}function sh(e){if(!e.receivedFlags)return i8;if(!e.isRecordingEnabled)return i3;if(e.urlTriggerMatching.urlBlocked)return i4;var t=new sr([e.eventTriggerMatching,e.urlTriggerMatching,e.linkedFlagMatching]).triggerStatus(e.sessionId),i=t!==st,s=N(e.isSampled);return i&&t===se?i8:i&&t===st||s&&!e.isSampled?i3:!0===e.isSampled?i5:i6}var sd="[SessionRecording]",sp=z(sd);function sg(){var e;return null==_||null==(e=_.__PosthogExtensions__)||null==(e=e.rrweb)?void 0:e.record}var s_=[ic.MouseMove,ic.MouseInteraction,ic.Scroll,ic.ViewportResize,ic.Input,ic.TouchMove,ic.MediaInteraction,ic.Drag],sv=e=>({rrwebMethod:e,enqueuedAt:Date.now(),attempt:1});function sf(e){return function(e,t){for(var i="",s=0;s<e.length;){var r=e[s++];r<128||t?i+=String.fromCharCode(r):r<224?i+=String.fromCharCode((31&r)<<6|63&e[s++]):r<240?i+=String.fromCharCode((15&r)<<12|(63&e[s++])<<6|63&e[s++]):i+=String.fromCharCode(55296|(r=((15&r)<<18|(63&e[s++])<<12|(63&e[s++])<<6|63&e[s++])-65536)>>10,56320|1023&r)}return i}(i1(i2(JSON.stringify(e))),!0)}function sm(e){return e.type===il.Custom&&"sessionIdle"===e.data.tag}class sy{get sessionId(){return this.Ct}get Ft(){return this._instance.config.session_recording.session_idle_threshold_ms||3e5}get started(){return this.Ot}get At(){if(!this._instance.sessionManager)throw Error(sd+" must be started with a valid sessionManager.");return this._instance.sessionManager}get Dt(){var e,t;return this.Lt.triggerStatus(this.sessionId)===se?6e4:null!=(e=null==(t=this._instance.config.session_recording)?void 0:t.full_snapshot_interval_millis)?e:3e5}get jt(){var e=this._instance.get_property(e$);return N(e)?e:null}get Nt(){var e,t,i=null==(e=this.C)?void 0:e.data[(null==(t=this.C)?void 0:t.data.length)-1],{sessionStartTimestamp:s}=this.At.checkAndGetSessionAndWindowId(!0);return i?i.timestamp-s:null}get zt(){var e=!!this._instance.get_property(ef),t=!this._instance.config.disable_session_recording;return s&&e&&t}get Ut(){var e=!!this._instance.get_property(em),t=this._instance.config.enable_recording_console_log;return null!=t?t:e}get qt(){var e,t,i,s,r,n,a=this._instance.config.session_recording.captureCanvas,o=this._instance.get_property(ew),l=null!=(e=null!=(t=null==a?void 0:a.recordCanvas)?t:null==o?void 0:o.enabled)&&e,c=null!=(i=null!=(s=null==a?void 0:a.canvasFps)?s:null==o?void 0:o.fps)?i:4,u=null!=(r=null!=(n=null==a?void 0:a.canvasQuality)?n:null==o?void 0:o.quality)?r:.4;if("string"==typeof u){var h=parseFloat(u);u=isNaN(h)?.4:h}return{enabled:l,fps:tH(c,0,12,"canvas recording fps",4),quality:tH(u,0,1,"canvas recording quality",.4)}}get Bt(){var e,t,i=this._instance.get_property(ey),s={recordHeaders:null==(e=this._instance.config.session_recording)?void 0:e.recordHeaders,recordBody:null==(t=this._instance.config.session_recording)?void 0:t.recordBody},r=(null==s?void 0:s.recordHeaders)||(null==i?void 0:i.recordHeaders),n=(null==s?void 0:s.recordBody)||(null==i?void 0:i.recordBody),a=P(this._instance.config.capture_performance)?this._instance.config.capture_performance.network_timing:this._instance.config.capture_performance,o=!!(N(a)?a:null==i?void 0:i.capturePerformance);return r||n||o?{recordHeaders:r,recordBody:n,recordPerformance:o}:void 0}get Ht(){var e,t,i,s,r,n,a=this._instance.get_property(eb),o={maskAllInputs:null==(e=this._instance.config.session_recording)?void 0:e.maskAllInputs,maskTextSelector:null==(t=this._instance.config.session_recording)?void 0:t.maskTextSelector,blockSelector:null==(i=this._instance.config.session_recording)?void 0:i.blockSelector},l=null!=(s=null==o?void 0:o.maskAllInputs)?s:null==a?void 0:a.maskAllInputs,c=null!=(r=null==o?void 0:o.maskTextSelector)?r:null==a?void 0:a.maskTextSelector,u=null!=(n=null==o?void 0:o.blockSelector)?n:null==a?void 0:a.blockSelector;return T(l)&&T(c)&&T(u)?void 0:{maskAllInputs:null==l||l,maskTextSelector:c,blockSelector:u}}get Wt(){var e=this._instance.get_property(eE);return L(e)?e:null}get Gt(){var e=this._instance.get_property(eS);return L(e)?e:null}get status(){return this.Jt?this.Vt({receivedFlags:this.Jt,isRecordingEnabled:this.zt,isSampled:this.jt,urlTriggerMatching:this.Kt,eventTriggerMatching:this.Yt,linkedFlagMatching:this.Xt,sessionId:this.sessionId}):i8}constructor(e){if(this.Vt=sc,this.Jt=!1,this.Qt=[],this.Zt="unknown",this.ti=Date.now(),this.Lt=new sn,this.ii=void 0,this.ei=void 0,this.ri=void 0,this.si=void 0,this.ni=void 0,this._forceAllowLocalhostNetworkCapture=!1,this.oi=()=>{this.ai()},this.li=()=>{this.ui("browser offline",{})},this.hi=()=>{this.ui("browser online",{})},this.di=()=>{if(null!=c&&c.visibilityState){var e="window "+c.visibilityState;this.ui(e,{})}},this._instance=e,this.Ot=!1,this.vi="/s/",this.ci=void 0,this.Jt=!1,!this._instance.sessionManager)throw sp.error("started without valid sessionManager"),Error(sd+" started without valid sessionManager. This is a bug.");if(this._instance.config.__preview_experimental_cookieless_mode)throw Error(sd+" cannot be used with __preview_experimental_cookieless_mode.");this.Xt=new so(this._instance),this.Kt=new sa(this._instance),this.Yt=new sl(this._instance);var{sessionId:t,windowId:i}=this.At.checkAndGetSessionAndWindowId();this.Ct=t,this.fi=i,this.C=this.pi(),this.Ft>=this.At.sessionTimeoutMs&&sp.warn("session_idle_threshold_ms ("+this.Ft+") is greater than the session timeout ("+this.At.sessionTimeoutMs+"). Session will never be detected as idle")}startIfEnabledOrStop(e){this.zt?(this.gi(e),en(s,"beforeunload",this.oi),en(s,"offline",this.li),en(s,"online",this.hi),en(s,"visibilitychange",this.di),this.mi(),this.bi(),D(this.ii)&&(this.ii=this._instance.on("eventCaptured",e=>{try{if("$pageview"===e.event){var t=null!=e&&e.properties.$current_url?this.yi(null==e?void 0:e.properties.$current_url):"";if(!t)return;this.ui("$pageview",{href:t})}}catch(e){sp.error("Could not add $pageview to rrweb session",e)}})),this.ei||(this.ei=this.At.onSessionId((e,t,i)=>{var s,r;i&&(this.ui("$session_id_change",{sessionId:e,windowId:t,changeReason:i}),null==(s=this._instance)||null==(s=s.persistence)||s.unregister(eF),null==(r=this._instance)||null==(r=r.persistence)||r.unregister(eI))}))):this.stopRecording()}stopRecording(){var e,t,i,r;this.Ot&&this.ci&&(this.ci(),this.ci=void 0,this.Ot=!1,null==s||s.removeEventListener("beforeunload",this.oi),null==s||s.removeEventListener("offline",this.li),null==s||s.removeEventListener("online",this.hi),null==s||s.removeEventListener("visibilitychange",this.di),this.pi(),clearInterval(this.wi),null==(e=this.ii)||e.call(this),this.ii=void 0,null==(t=this.ni)||t.call(this),this.ni=void 0,null==(i=this.ei)||i.call(this),this.ei=void 0,null==(r=this.si)||r.call(this),this.si=void 0,this.Yt.stop(),this.Kt.stop(),this.Xt.stop(),sp.info("stopped"))}Si(){var e;null==(e=this._instance.persistence)||e.unregister(e$)}$i(e){var t,i=this.Ct!==e,s=this.Wt;if(L(s)){var r=this.jt,n=i||!N(r),a=n?function(e){for(var t=0,i=0;i<e.length;i++)t=(t<<5)-t+e.charCodeAt(i)|0;return Math.abs(t)}(e)%100<tH(100*s,0,100):r;n&&(a?this.xi(i5):sp.warn("Sample rate ("+s+") has determined that this sessionId ("+e+") will not be sent to the server."),this.ui("samplingDecisionMade",{sampleRate:s,isSampled:a})),null==(t=this._instance.persistence)||t.register({[e$]:a})}else this.Si()}onRemoteConfig(e){var t,i,s,r;this.ui("$remote_config_received",e),this.ki(e),null!=(t=e.sessionRecording)&&t.endpoint&&(this.vi=null==(r=e.sessionRecording)?void 0:r.endpoint),this.mi(),"any"===(null==(i=e.sessionRecording)?void 0:i.triggerMatchType)?(this.Vt=su,this.Lt=new ss([this.Yt,this.Kt])):(this.Vt=sh,this.Lt=new sr([this.Yt,this.Kt])),this._instance.register_for_session({$sdk_debug_replay_remote_trigger_matching_config:null==(s=e.sessionRecording)?void 0:s.triggerMatchType}),this.Kt.onRemoteConfig(e),this.Yt.onRemoteConfig(e),this.Xt.onRemoteConfig(e,(e,t)=>{this.xi("linked_flag_matched",{flag:e,variant:t})}),this.Jt=!0,this.startIfEnabledOrStop()}mi(){L(this.Wt)&&D(this.si)&&(this.si=this.At.onSessionId(e=>{this.$i(e)}))}ki(e){if(this._instance.persistence){var t,i=this._instance.persistence,s=()=>{var t,s,r,n,a,o,l,c,u,h=null==(t=e.sessionRecording)?void 0:t.sampleRate,d=D(h)?null:parseFloat(h);D(d)&&this.Si();var p=null==(s=e.sessionRecording)?void 0:s.minimumDurationMilliseconds;i.register({[ef]:!!e.sessionRecording,[em]:null==(r=e.sessionRecording)?void 0:r.consoleLogRecordingEnabled,[ey]:V({capturePerformance:e.capturePerformance},null==(n=e.sessionRecording)?void 0:n.networkPayloadCapture),[eb]:null==(a=e.sessionRecording)?void 0:a.masking,[ew]:{enabled:null==(o=e.sessionRecording)?void 0:o.recordCanvas,fps:null==(l=e.sessionRecording)?void 0:l.canvasFps,quality:null==(c=e.sessionRecording)?void 0:c.canvasQuality},[eE]:d,[eS]:T(p)?null:p,[ex]:null==(u=e.sessionRecording)?void 0:u.scriptConfig})};s(),null==(t=this.ri)||t.call(this),this.ri=this.At.onSessionId(s)}}log(e,t){var i;void 0===t&&(t="log"),null==(i=this._instance.sessionRecording)||i.onRRwebEmit({type:6,data:{plugin:"rrweb/console@1",payload:{level:t,trace:[],payload:[JSON.stringify(e)]}},timestamp:Date.now()})}gi(e){if(!T(Object.assign)&&!T(Array.from)&&!(this.Ot||this._instance.config.disable_session_recording||this._instance.consent.isOptedOut())){var t;(this.Ot=!0,this.At.checkAndGetSessionAndWindowId(),sg())?this.Ei():null==(t=_.__PosthogExtensions__)||null==t.loadExternalDependency||t.loadExternalDependency(this._instance,this.Ii,e=>{if(e)return sp.error("could not load recorder",e);this.Ei()}),sp.info("starting"),this.status===i6&&this.xi(e||"recording_initialized")}}get Ii(){var e;return(null==(e=this._instance)||null==(e=e.persistence)||null==(e=e.get_property(ex))?void 0:e.script)||"recorder"}Pi(e){var t;return 3===e.type&&-1!==s_.indexOf(null==(t=e.data)?void 0:t.source)}Ri(e){var t=this.Pi(e);t||this.Zt||e.timestamp-this.ti>this.Ft&&(this.Zt=!0,clearInterval(this.wi),this.ui("sessionIdle",{eventTimestamp:e.timestamp,lastActivityTimestamp:this.ti,threshold:this.Ft,bufferLength:this.C.data.length,bufferSize:this.C.size}),this.ai());var i=!1;if(t&&(this.ti=e.timestamp,this.Zt)){var s="unknown"===this.Zt;this.Zt=!1,s||(this.ui("sessionNoLongerIdle",{reason:"user activity",type:e.type}),i=!0)}if(!this.Zt){var{windowId:r,sessionId:n}=this.At.checkAndGetSessionAndWindowId(!t,e.timestamp),a=this.Ct!==n,o=this.fi!==r;this.fi=r,this.Ct=n,a||o?(this.stopRecording(),this.startIfEnabledOrStop("session_id_changed")):i&&this.Ti()}}Mi(e){try{return e.rrwebMethod(),!0}catch(t){return this.Qt.length<10?this.Qt.push({enqueuedAt:e.enqueuedAt||Date.now(),attempt:e.attempt++,rrwebMethod:e.rrwebMethod}):sp.warn("could not emit queued rrweb event.",t,e),!1}}ui(e,t){return this.Mi(sv(()=>sg().addCustomEvent(e,t)))}Ci(){return this.Mi(sv(()=>sg().takeFullSnapshot()))}Ei(){var e,t,i,s,r={blockClass:"ph-no-capture",blockSelector:void 0,ignoreClass:"ph-ignore-input",maskTextClass:"ph-mask",maskTextSelector:void 0,maskTextFn:void 0,maskAllInputs:!0,maskInputOptions:{password:!0},maskInputFn:void 0,slimDOMOptions:{},collectFonts:!1,inlineStylesheet:!0,recordCrossOriginIframes:!1};for(var[n,a]of Object.entries(this._instance.config.session_recording||{}))n in r&&("maskInputOptions"===n?r.maskInputOptions=V({password:!0},a):r[n]=a);this.qt&&this.qt.enabled&&(r.recordCanvas=!0,r.sampling={canvas:this.qt.fps},r.dataURLOptions={type:"image/webp",quality:this.qt.quality}),this.Ht&&(r.maskAllInputs=null==(t=this.Ht.maskAllInputs)||t,r.maskTextSelector=null!=(i=this.Ht.maskTextSelector)?i:void 0,r.blockSelector=null!=(s=this.Ht.blockSelector)?s:void 0);var o=sg();if(o){this.Fi=null!=(e=this.Fi)?e:new ib(o,{refillRate:this._instance.config.session_recording.__mutationThrottlerRefillRate,bucketSize:this._instance.config.session_recording.__mutationThrottlerBucketSize,onBlockedNode:(e,t)=>{var i="Too many mutations on node '"+e+"'. Rate limiting. This could be due to SVG animations or something similar";sp.info(i,{node:t}),this.log(sd+" "+i,"warn")}});var l=this.Oi();this.ci=o(V({emit:e=>{this.onRRwebEmit(e)},plugins:l},r)),this.ti=Date.now(),this.Zt=N(this.Zt)?this.Zt:"unknown",this.ui("$session_options",{sessionRecordingOptions:r,activePlugins:l.map(e=>null==e?void 0:e.name)}),this.ui("$posthog_config",{config:this._instance.config})}else sp.error("onScriptLoaded was called but rrwebRecord is not available. This indicates something has gone wrong.")}Ti(){if(this.wi&&clearInterval(this.wi),!0!==this.Zt){var e=this.Dt;e&&(this.wi=setInterval(()=>{this.Ci()},e))}}Oi(){var e,t,i=[],s=null==(e=_.__PosthogExtensions__)||null==(e=e.rrwebPlugins)?void 0:e.getRecordConsolePlugin;s&&this.Ut&&i.push(s());var r=null==(t=_.__PosthogExtensions__)||null==(t=t.rrwebPlugins)?void 0:t.getRecordNetworkPlugin;return this.Bt&&C(r)&&(!tl.includes(location.hostname)||this._forceAllowLocalhostNetworkCapture?i.push(r(iy(this._instance.config,this.Bt))):sp.info("NetworkCapture not started because we are on localhost.")),i}onRRwebEmit(e){var t;if(this.Ai(),e&&P(e)){if(e.type===il.Meta){var i=this.yi(e.data.href);if(this.Di=i,!i)return;e.data.href=i}else this.Li();if(this.Kt.checkUrlTriggerConditions(()=>this.ji(),()=>this.Ni(),e=>this.zi(e)),!this.Kt.urlBlocked||e.type===il.Custom&&"recording paused"===e.data.tag){e.type===il.FullSnapshot&&this.Ti(),e.type===il.FullSnapshot&&this.Jt&&this.Lt.triggerStatus(this.sessionId)===se&&this.pi();var s=this.Fi?this.Fi.throttleMutations(e):e;if(s){var r=function(e){if(e&&P(e)&&6===e.type&&P(e.data)&&"rrweb/console@1"===e.data.plugin){e.data.payload.payload.length>10&&(e.data.payload.payload=e.data.payload.payload.slice(0,10),e.data.payload.payload.push("...[truncated]"));for(var t=[],i=0;i<e.data.payload.payload.length;i++)e.data.payload.payload[i]&&e.data.payload.payload[i].length>2e3?t.push(e.data.payload.payload[i].slice(0,2e3)+"...[truncated]"):t.push(e.data.payload.payload[i]);return e.data.payload.payload=t,e}return e}(s);if(this.Ri(r),!0!==this.Zt||sm(r)){if(sm(r)){var n=r.data.payload;n&&(r.timestamp=n.lastActivityTimestamp+n.threshold)}var a=null==(t=this._instance.config.session_recording.compress_events)||t?function(e){if(1024>io(e))return e;try{if(e.type===il.FullSnapshot)return V({},e,{data:sf(e.data),cv:"2024-10"});if(e.type===il.IncrementalSnapshot&&e.data.source===ic.Mutation)return V({},e,{cv:"2024-10",data:V({},e.data,{texts:sf(e.data.texts),attributes:sf(e.data.attributes),removes:sf(e.data.removes),adds:sf(e.data.adds)})});if(e.type===il.IncrementalSnapshot&&e.data.source===ic.StyleSheetRule)return V({},e,{cv:"2024-10",data:V({},e.data,{adds:e.data.adds?sf(e.data.adds):void 0,removes:e.data.removes?sf(e.data.removes):void 0})})}catch(e){sp.error("could not compress event - will use uncompressed event",e)}return e}(r):r,o={$snapshot_bytes:io(a),$snapshot_data:a,$session_id:this.Ct,$window_id:this.fi};this.status!==i3?this.Ui(o):this.pi()}}}}}Li(){if(!this._instance.config.capture_pageview&&s){var e=this.yi(s.location.href);this.Di!==e&&(this.ui("$url_changed",{href:e}),this.Di=e)}}Ai(){if(this.Qt.length){var e=[...this.Qt];this.Qt=[],e.forEach(e=>{Date.now()-e.enqueuedAt<=2e3&&this.Mi(e)})}}yi(e){var t=this._instance.config.session_recording;if(t.maskNetworkRequestFn){var i,s={url:e};return null==(i=s=t.maskNetworkRequestFn(s))?void 0:i.url}return e}pi(){return this.C={size:0,data:[],sessionId:this.Ct,windowId:this.fi},this.C}ai(){this.qi&&(clearTimeout(this.qi),this.qi=void 0);var e=this.Gt,t=this.Nt,i=L(t)&&t>=0,s=L(e)&&i&&t<e;return this.status===i8||this.status===i4||this.status===i3||s?(this.qi=setTimeout(()=>{this.ai()},2e3),this.C):(this.C.data.length>0&&(function e(t,i){if(void 0===i&&(i=6606028.8),t.size>=i&&t.data.length>1){var s=Math.floor(t.data.length/2),r=t.data.slice(0,s),n=t.data.slice(s);return[e({size:io(r),data:r,sessionId:t.sessionId,windowId:t.windowId}),e({size:io(n),data:n,sessionId:t.sessionId,windowId:t.windowId})].flatMap(e=>e)}return[t]})(this.C).forEach(e=>{this.Bi({$snapshot_bytes:e.size,$snapshot_data:e.data,$session_id:e.sessionId,$window_id:e.windowId,$lib:"web",$lib_version:v.LIB_VERSION})}),this.pi())}Ui(e){var t,i=2+((null==(t=this.C)?void 0:t.data.length)||0);!this.Zt&&(this.C.size+e.$snapshot_bytes+i>943718.4||this.C.sessionId!==this.Ct)&&(this.C=this.ai()),this.C.size+=e.$snapshot_bytes,this.C.data.push(e.$snapshot_data),this.qi||this.Zt||(this.qi=setTimeout(()=>{this.ai()},2e3))}Bi(e){this._instance.capture("$snapshot",e,{_url:this._instance.requestRouter.endpointFor("api",this.vi),_noTruncate:!0,_batchKey:"recordings",skip_client_rate_limiting:!0})}zi(e){var t;this.Lt.triggerStatus(this.sessionId)===se&&(null==(t=this._instance)||null==(t=t.persistence)||t.register({["url"===e?eI:eF]:this.Ct}),this.ai(),this.xi(e+"_trigger_matched"))}ji(){this.Kt.urlBlocked||(this.Kt.urlBlocked=!0,clearInterval(this.wi),sp.info("recording paused due to URL blocker"),this.ui("recording paused",{reason:"url blocker"}))}Ni(){this.Kt.urlBlocked&&(this.Kt.urlBlocked=!1,this.Ci(),this.Ti(),this.ui("recording resumed",{reason:"left blocked url"}),sp.info("recording resumed"))}bi(){0!==this.Yt.Tt.length&&D(this.ni)&&(this.ni=this._instance.on("eventCaptured",e=>{try{this.Yt.Tt.includes(e.event)&&this.zi("event")}catch(e){sp.error("Could not activate event trigger",e)}}))}overrideLinkedFlag(){this.Xt.linkedFlagSeen=!0,this.Ci(),this.xi("linked_flag_overridden")}overrideSampling(){var e;null==(e=this._instance.persistence)||e.register({[e$]:!0}),this.Ci(),this.xi("sampling_overridden")}overrideTrigger(e){this.zi(e)}xi(e,t){this._instance.register_for_session({$session_recording_start_reason:e}),sp.info(e.replace("_"," "),t),w(["recording_initialized","session_id_changed"],e)||this.ui(e,t)}get sdkDebugProperties(){var{sessionStartTimestamp:e}=this.At.checkAndGetSessionAndWindowId(!0);return{$recording_status:this.status,$sdk_debug_replay_internal_buffer_length:this.C.data.length,$sdk_debug_replay_internal_buffer_size:this.C.size,$sdk_debug_current_session_duration:this.Nt,$sdk_debug_session_start:e}}}var sb=z("[SegmentIntegration]"),sw="posthog-js";function sE(e,t){var{organization:i,projectId:s,prefix:r,severityAllowList:n=["error"]}=void 0===t?{}:t;return t=>{if(!("*"===n||n.includes(t.level))||!e.__loaded)return t;t.tags||(t.tags={});var a,o,l,c,u,h=e.requestRouter.endpointFor("ui","/project/"+e.config.token+"/person/"+e.get_distinct_id());t.tags["PostHog Person URL"]=h,e.sessionRecordingStarted()&&(t.tags["PostHog Recording URL"]=e.get_session_replay_url({withTimestamp:!0}));var d=(null==(a=t.exception)?void 0:a.values)||[],p=d.map(e=>V({},e,{stacktrace:e.stacktrace?V({},e.stacktrace,{type:"raw",frames:(e.stacktrace.frames||[]).map(e=>V({},e,{platform:"web:javascript"}))}):void 0})),g={$exception_message:(null==(o=d[0])?void 0:o.value)||t.message,$exception_type:null==(l=d[0])?void 0:l.type,$exception_personURL:h,$exception_level:t.level,$exception_list:p,$sentry_event_id:t.event_id,$sentry_exception:t.exception,$sentry_exception_message:(null==(c=d[0])?void 0:c.value)||t.message,$sentry_exception_type:null==(u=d[0])?void 0:u.type,$sentry_tags:t.tags};return i&&s&&(g.$sentry_url=(r||"https://sentry.io/organizations/")+i+"/issues/?project="+s+"&query="+t.event_id),e.exceptions.sendExceptionEvent(g),t}}class sS{constructor(e,t,i,s,r){this.name=sw,this.setupOnce=function(n){n(sE(e,{organization:t,projectId:i,prefix:s,severityAllowList:r}))}}}var sx=null!=s&&s.location?tp(s.location.hash,"__posthog")||tp(location.hash,"state"):null,sk="_postHogToolbarParams",s$=z("[Toolbar]"),sI=function(e){return e[e.UNINITIALIZED=0]="UNINITIALIZED",e[e.LOADING=1]="LOADING",e[e.LOADED=2]="LOADED",e}(sI||{});class sF{constructor(e){this.instance=e}Hi(e){_.ph_toolbar_state=e}Wi(){var e;return null!=(e=_.ph_toolbar_state)?e:sI.UNINITIALIZED}maybeLoadToolbar(e,t,i){if(void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===i&&(i=void 0),!s||!c)return!1;e=null!=e?e:s.location,i=null!=i?i:s.history;try{if(!t){try{s.localStorage.setItem("test","test"),s.localStorage.removeItem("test")}catch(e){return!1}t=null==s?void 0:s.localStorage}var r,n=sx||tp(e.hash,"__posthog")||tp(e.hash,"state"),a=n?ee(()=>JSON.parse(atob(decodeURIComponent(n))))||ee(()=>JSON.parse(decodeURIComponent(n))):null;return a&&"ph_authorize"===a.action?((r=a).source="url",r&&Object.keys(r).length>0&&(a.desiredHash?e.hash=a.desiredHash:i?i.replaceState(i.state,"",e.pathname+e.search):e.hash="")):((r=JSON.parse(t.getItem(sk)||"{}")).source="localstorage",delete r.userIntent),!(!r.token||this.instance.config.token!==r.token)&&(this.loadToolbar(r),!0)}catch(e){return!1}}Gi(e){var t=_.ph_load_toolbar||_.ph_load_editor;!D(t)&&C(t)?t(e,this.instance):s$.warn("No toolbar load function found")}loadToolbar(e){var t,i=!(null==c||!c.getElementById(eU));if(!s||i)return!1;var r="custom"===this.instance.requestRouter.region&&this.instance.config.advanced_disable_toolbar_metrics,n=V({token:this.instance.config.token},e,{apiURL:this.instance.requestRouter.endpointFor("ui")},r?{instrument:!1}:{});return(s.localStorage.setItem(sk,JSON.stringify(V({},n,{source:void 0}))),this.Wi()===sI.LOADED)?this.Gi(n):this.Wi()===sI.UNINITIALIZED&&(this.Hi(sI.LOADING),null==(t=_.__PosthogExtensions__)||null==t.loadExternalDependency||t.loadExternalDependency(this.instance,"toolbar",e=>{if(e)return s$.error("[Toolbar] Failed to load",e),void this.Hi(sI.UNINITIALIZED);this.Hi(sI.LOADED),this.Gi(n)}),en(s,"turbolinks:load",()=>{this.Hi(sI.UNINITIALIZED),this.loadToolbar(n)})),!0}Ji(e){return this.loadToolbar(e)}maybeLoadEditor(e,t,i){return void 0===e&&(e=void 0),void 0===t&&(t=void 0),void 0===i&&(i=void 0),this.maybeLoadToolbar(e,t,i)}}var sC=z("[TracingHeaders]");class sP{constructor(e){this.Vi=void 0,this.Ki=void 0,this.nt=()=>{var e,t;T(this.Vi)&&(null==(e=_.__PosthogExtensions__)||null==(e=e.tracingHeadersPatchFns)||e._patchXHR(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager)),T(this.Ki)&&(null==(t=_.__PosthogExtensions__)||null==(t=t.tracingHeadersPatchFns)||t._patchFetch(this._instance.config.__add_tracing_headers||[],this._instance.get_distinct_id(),this._instance.sessionManager))},this._instance=e}J(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.tracingHeadersPatchFns&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"tracing-headers",t=>{if(t)return sC.error("failed to load script",t);e()})}startIfEnabledOrStop(){var e,t;this._instance.config.__add_tracing_headers?this.J(this.nt):(null==(e=this.Vi)||e.call(this),null==(t=this.Ki)||t.call(this),this.Vi=void 0,this.Ki=void 0)}}var sR=z("[Web Vitals]");class sT{constructor(e){var t;this.Yi=!1,this.i=!1,this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0},this.Xi=()=>{clearTimeout(this.Qi),0!==this.C.metrics.length&&(this._instance.capture("$web_vitals",this.C.metrics.reduce((e,t)=>V({},e,{["$web_vitals_"+t.name+"_event"]:V({},t),["$web_vitals_"+t.name+"_value"]:t.value}),{})),this.C={url:void 0,metrics:[],firstMetricTimestamp:void 0})},this.Zi=e=>{var t,i=null==(t=this._instance.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0);if(T(i))sR.error("Could not read session ID. Dropping metrics!");else{this.C=this.C||{url:void 0,metrics:[],firstMetricTimestamp:void 0};var s=this.te();T(s)||(D(null==e?void 0:e.name)||D(null==e?void 0:e.value)?sR.error("Invalid metric received",e):this.ie&&e.value>=this.ie?sR.error("Ignoring metric with value >= "+this.ie,e):(this.C.url!==s&&(this.Xi(),this.Qi=setTimeout(this.Xi,this.flushToCaptureTimeoutMs)),T(this.C.url)&&(this.C.url=s),this.C.firstMetricTimestamp=T(this.C.firstMetricTimestamp)?Date.now():this.C.firstMetricTimestamp,e.attribution&&e.attribution.interactionTargetElement&&(e.attribution.interactionTargetElement=void 0),this.C.metrics.push(V({},e,{$current_url:s,$session_id:i.sessionId,$window_id:i.windowId,timestamp:Date.now()})),this.C.metrics.length===this.allowedMetrics.length&&this.Xi()))}},this.nt=()=>{var e,t,i,s,r=_.__PosthogExtensions__;T(r)||T(r.postHogWebVitalsCallbacks)||({onLCP:e,onCLS:t,onFCP:i,onINP:s}=r.postHogWebVitalsCallbacks),e&&t&&i&&s?(this.allowedMetrics.indexOf("LCP")>-1&&e(this.Zi.bind(this)),this.allowedMetrics.indexOf("CLS")>-1&&t(this.Zi.bind(this)),this.allowedMetrics.indexOf("FCP")>-1&&i(this.Zi.bind(this)),this.allowedMetrics.indexOf("INP")>-1&&s(this.Zi.bind(this)),this.i=!0):sR.error("web vitals callbacks not loaded - not starting")},this._instance=e,this.Yi=!(null==(t=this._instance.persistence)||!t.props[eg]),this.startIfEnabled()}get allowedMetrics(){var e,t,i=P(this._instance.config.capture_performance)?null==(e=this._instance.config.capture_performance)?void 0:e.web_vitals_allowed_metrics:void 0;return T(i)?(null==(t=this._instance.persistence)?void 0:t.props[ev])||["CLS","FCP","INP","LCP"]:i}get flushToCaptureTimeoutMs(){return(P(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals_delayed_flush_ms:void 0)||5e3}get ie(){var e=P(this._instance.config.capture_performance)&&L(this._instance.config.capture_performance.__web_vitals_max_value)?this._instance.config.capture_performance.__web_vitals_max_value:9e5;return 0<e&&e<=6e4?9e5:e}get isEnabled(){var e=null==u?void 0:u.protocol;if("http:"!==e&&"https:"!==e)return sR.info("Web Vitals are disabled on non-http/https protocols"),!1;var t=P(this._instance.config.capture_performance)?this._instance.config.capture_performance.web_vitals:N(this._instance.config.capture_performance)?this._instance.config.capture_performance:void 0;return N(t)?t:this.Yi}startIfEnabled(){this.isEnabled&&!this.i&&(sR.info("enabled, starting..."),this.J(this.nt))}onRemoteConfig(e){var t=P(e.capturePerformance)&&!!e.capturePerformance.web_vitals,i=P(e.capturePerformance)?e.capturePerformance.web_vitals_allowed_metrics:void 0;this._instance.persistence&&(this._instance.persistence.register({[eg]:t}),this._instance.persistence.register({[ev]:i})),this.Yi=t,this.startIfEnabled()}J(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.postHogWebVitalsCallbacks&&e(),null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"web-vitals",t=>{t?sR.error("failed to load script",t):e()})}te(){var e=s?s.location.href:void 0;return e||sR.error("Could not determine current URL"),e}}var sO=z("[Heatmaps]");function sA(e){return P(e)&&"clientX"in e&&"clientY"in e&&L(e.clientX)&&L(e.clientY)}class sM{constructor(e){var t;this.rageclicks=new to,this.Yi=!1,this.i=!1,this.ee=null,this.instance=e,this.Yi=!(null==(t=this.instance.persistence)||!t.props[eu])}get flushIntervalMilliseconds(){var e=5e3;return P(this.instance.config.capture_heatmaps)&&this.instance.config.capture_heatmaps.flush_interval_milliseconds&&(e=this.instance.config.capture_heatmaps.flush_interval_milliseconds),e}get isEnabled(){return T(this.instance.config.capture_heatmaps)?T(this.instance.config.enable_heatmaps)?this.Yi:this.instance.config.enable_heatmaps:!1!==this.instance.config.capture_heatmaps}startIfEnabled(){if(this.isEnabled)this.i||(sO.info("starting..."),this.re(),this.ee=setInterval(this.se.bind(this),this.flushIntervalMilliseconds));else{var e,t;clearInterval(null!=(e=this.ee)?e:void 0),null==(t=this.ne)||t.stop(),this.getAndClearBuffer()}}onRemoteConfig(e){var t=!!e.heatmaps;this.instance.persistence&&this.instance.persistence.register({[eu]:t}),this.Yi=t,this.startIfEnabled()}getAndClearBuffer(){var e=this.C;return this.C=void 0,e}oe(e){this.ae(e.originalEvent,"deadclick")}re(){s&&c&&(en(s,"beforeunload",this.se.bind(this)),en(c,"click",e=>this.ae(e||(null==s?void 0:s.event)),{capture:!0}),en(c,"mousemove",e=>this.le(e||(null==s?void 0:s.event)),{capture:!0}),this.ne=new tB(this.instance,tq,this.oe.bind(this)),this.ne.startIfEnabled(),this.i=!0)}ue(e,t){var i=this.instance.scrollManager.scrollY(),r=this.instance.scrollManager.scrollX(),n=this.instance.scrollManager.scrollElement(),a=function(e,t,i){for(var r=e;r&&eY(r)&&!eJ(r,"body")&&r!==i;){if(w(t,null==s?void 0:s.getComputedStyle(r).position))return!0;r=e6(r)}return!1}(e3(e),["fixed","sticky"],n);return{x:e.clientX+(a?0:r),y:e.clientY+(a?0:i),target_fixed:a,type:t}}ae(e,t){var i;if(void 0===t&&(t="click"),!eW(e.target)&&sA(e)){var s=this.ue(e,t);null!=(i=this.rageclicks)&&i.isRageClick(e.clientX,e.clientY,(new Date).getTime())&&this.he(V({},s,{type:"rageclick"})),this.he(s)}}le(e){!eW(e.target)&&sA(e)&&(clearTimeout(this.de),this.de=setTimeout(()=>{this.he(this.ue(e,"mousemove"))},500))}he(e){if(s){var t=s.location.href;this.C=this.C||{},this.C[t]||(this.C[t]=[]),this.C[t].push(e)}}se(){this.C&&!R(this.C)&&this.instance.capture("$$heatmap",{$heatmap_data:this.getAndClearBuffer()})}}class sD{constructor(e){this._instance=e}doPageView(e,t){var i,r=this.ve(e,t);return this.ce={pathname:null!=(i=null==s?void 0:s.location.pathname)?i:"",pageViewId:t,timestamp:e},this._instance.scrollManager.resetContext(),r}doPageLeave(e){var t;return this.ve(e,null==(t=this.ce)?void 0:t.pageViewId)}doEvent(){var e;return{$pageview_id:null==(e=this.ce)?void 0:e.pageViewId}}ve(e,t){var i=this.ce;if(!i)return{$pageview_id:t};var s={$pageview_id:t,$prev_pageview_id:i.pageViewId},r=this._instance.scrollManager.getContext();if(r&&!this._instance.config.disable_scroll_properties){var{maxScrollHeight:n,lastScrollY:a,maxScrollY:o,maxContentHeight:l,lastContentY:c,maxContentY:u}=r;if(!(T(n)||T(a)||T(o)||T(l)||T(c)||T(u))){n=Math.ceil(n),a=Math.ceil(a),o=Math.ceil(o),l=Math.ceil(l),c=Math.ceil(c),u=Math.ceil(u);var h=n<=1?1:tH(a/n,0,1),d=n<=1?1:tH(o/n,0,1),p=l<=1?1:tH(c/l,0,1),g=l<=1?1:tH(u/l,0,1);s=Z(s,{$prev_pageview_last_scroll:a,$prev_pageview_last_scroll_percentage:h,$prev_pageview_max_scroll:o,$prev_pageview_max_scroll_percentage:d,$prev_pageview_last_content:c,$prev_pageview_last_content_percentage:p,$prev_pageview_max_content:u,$prev_pageview_max_content_percentage:g})}}return i.pathname&&(s.$prev_pageview_pathname=i.pathname),i.timestamp&&(s.$prev_pageview_duration=(e.getTime()-i.timestamp.getTime())/1e3),s}}var sL=function(e){var t,i,s,r,n="";for(t=i=0,s=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<s;r++){var a=e.charCodeAt(r),o=null;a<128?i++:o=a>127&&a<2048?String.fromCharCode(a>>6|192,63&a|128):String.fromCharCode(a>>12|224,a>>6&63|128,63&a|128),M(o)||(i>t&&(n+=e.substring(t,i)),n+=o,t=i=r+1)}return i>t&&(n+=e.substring(t,e.length)),n},sN=!!d||!!h,sq="text/plain",sj=(e,t)=>{var[i,s]=e.split("?"),r=V({},t);null==s||s.split("&").forEach(e=>{var[t]=e.split("=");delete r[t]});var n=tu(r);return i+"?"+(n=n?(s?s+"&":"")+n:s)},sB=(e,t)=>JSON.stringify(e,(e,t)=>"bigint"==typeof t?t.toString():t,t),sH=e=>{var{data:t,compression:i}=e;if(t){if(i===y.GZipJS){var s=new Blob([i1(i2(sB(t)),{mtime:0})],{type:sq});return{contentType:sq,body:s,estimatedSize:s.size}}if(i===y.Base64){var r=(e=>"data="+encodeURIComponent("string"==typeof e?e:sB(e)))(function(e){var t,i,s,r,n,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",o=0,l=0,c="",u=[];if(!e)return e;e=sL(e);do t=(n=e.charCodeAt(o++)<<16|e.charCodeAt(o++)<<8|e.charCodeAt(o++))>>18&63,i=n>>12&63,s=n>>6&63,r=63&n,u[l++]=a.charAt(t)+a.charAt(i)+a.charAt(s)+a.charAt(r);while(o<e.length);switch(c=u.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c}(sB(t)));return{contentType:"application/x-www-form-urlencoded",body:r,estimatedSize:new Blob([r]).size}}var n=sB(t);return{contentType:"application/json",body:n,estimatedSize:new Blob([n]).size}}},sz=[];h&&sz.push({transport:"fetch",method:e=>{var t,i,{contentType:s,body:r,estimatedSize:n}=null!=(t=sH(e))?t:{},a=new Headers;K(e.headers,function(e,t){a.append(t,e)}),s&&a.append("Content-Type",s);var o=e.url,l=null;if(p){var c=new p;l={signal:c.signal,timeout:setTimeout(()=>c.abort(),e.timeout)}}h(o,V({method:(null==e?void 0:e.method)||"GET",headers:a,keepalive:"POST"===e.method&&52428.8>(n||0),body:r,signal:null==(i=l)?void 0:i.signal},e.fetchOptions)).then(t=>t.text().then(i=>{var s={statusCode:t.status,text:i};if(200===t.status)try{s.json=JSON.parse(i)}catch(e){H.error(e)}null==e.callback||e.callback(s)})).catch(t=>{H.error(t),null==e.callback||e.callback({statusCode:0,text:t})}).finally(()=>l?clearTimeout(l.timeout):null)}}),d&&sz.push({transport:"XHR",method:e=>{var t,i=new d;i.open(e.method||"GET",e.url,!0);var{contentType:s,body:r}=null!=(t=sH(e))?t:{};K(e.headers,function(e,t){i.setRequestHeader(t,e)}),s&&i.setRequestHeader("Content-Type",s),e.timeout&&(i.timeout=e.timeout),i.withCredentials=!0,i.onreadystatechange=()=>{if(4===i.readyState){var t={statusCode:i.status,text:i.responseText};if(200===i.status)try{t.json=JSON.parse(i.responseText)}catch(e){}null==e.callback||e.callback(t)}},i.send(r)}}),null!=l&&l.sendBeacon&&sz.push({transport:"sendBeacon",method:e=>{var t=sj(e.url,{beacon:"1"});try{var i,{contentType:s,body:r}=null!=(i=sH(e))?i:{},n="string"==typeof r?new Blob([r],{type:s}):r;l.sendBeacon(t,n)}catch(e){}}});var sU=function(e,t){if(!function(e){try{new RegExp(e)}catch(e){return!1}return!0}(t))return!1;try{return new RegExp(t).test(e)}catch(e){return!1}};function sG(e,t,i){return sB({distinct_id:e,userPropertiesToSet:t,userPropertiesToSetOnce:i})}var sV={exact:(e,t)=>t.some(t=>e.some(e=>t===e)),is_not:(e,t)=>t.every(t=>e.every(e=>t!==e)),regex:(e,t)=>t.some(t=>e.some(e=>sU(t,e))),not_regex:(e,t)=>t.every(t=>e.every(e=>!sU(t,e))),icontains:(e,t)=>t.map(sW).some(t=>e.map(sW).some(e=>t.includes(e))),not_icontains:(e,t)=>t.map(sW).every(t=>e.map(sW).every(e=>!t.includes(e)))},sW=e=>e.toLowerCase(),sY=z("[Error tracking]");class sJ{constructor(e){var t,i;this.fe=[],this._instance=e,this.fe=null!=(t=null==(i=this._instance.persistence)?void 0:i.get_property(ed))?t:[]}onRemoteConfig(e){var t,i,s,r=null!=(t=null==(i=e.errorTracking)?void 0:i.suppressionRules)?t:[],n=null==(s=e.errorTracking)?void 0:s.captureExtensionExceptions;this.fe=r,this._instance.persistence&&this._instance.persistence.register({[ed]:this.fe,[ep]:n})}get pe(){var e,t=!!this._instance.get_property(ep),i=this._instance.config.error_tracking.captureExtensionExceptions;return null!=(e=null!=i?i:t)&&e}sendExceptionEvent(e){if(this._e(e))sY.info("Skipping exception capture because a suppression rule matched");else{if(this.pe||!this.ge(e))return this._instance.capture("$exception",e,{_noTruncate:!0,_batchKey:"exceptionEvent"});sY.info("Skipping exception capture because it was thrown by an extension")}}_e(e){var t=e.$exception_list;if(!t||!F(t)||0===t.length)return!1;var i=t.reduce((e,t)=>{var{type:i,value:s}=t;return O(i)&&i.length>0&&e.$exception_types.push(i),O(s)&&s.length>0&&e.$exception_values.push(s),e},{$exception_types:[],$exception_values:[]});return this.fe.some(e=>{var t=e.values.map(e=>{var t,s=sV[e.operator],r=F(e.value)?e.value:[e.value],n=null!=(t=i[e.key])?t:[];return r.length>0&&s(r,n)});return"OR"===e.type?t.some(Boolean):t.every(Boolean)})}ge(e){var t=e.$exception_list;return!(!t||!F(t))&&t.flatMap(e=>{var t,i;return null!=(t=null==(i=e.stacktrace)?void 0:i.frames)?t:[]}).some(e=>e.filename&&e.filename.startsWith("chrome-extension://"))}}var sK="Mobile",sZ="Android",sX="Tablet",sQ=sZ+" "+sX,s0="iPad",s1="Apple",s2=s1+" Watch",s3="Safari",s5="BlackBerry",s6="Samsung",s8=s6+"Browser",s4=s6+" Internet",s7="Chrome",s9=s7+" OS",re=s7+" iOS",rt="Internet Explorer",ri=rt+" "+sK,rs="Opera",rr=rs+" Mini",rn="Edge",ra="Microsoft "+rn,ro="Firefox",rl=ro+" iOS",rc="Nintendo",ru="PlayStation",rh="Xbox",rd=sZ+" "+sK,rp=sK+" "+s3,rg="Windows",r_=rg+" Phone",rv="Nokia",rf="Ouya",rm="Generic",ry=rm+" "+sK.toLowerCase(),rb=rm+" "+sX.toLowerCase(),rw="Konqueror",rE="(\\d+(\\.\\d+)?)",rS=RegExp("Version/"+rE),rx=RegExp(rh,"i"),rk=RegExp(ru+" \\w+","i"),r$=RegExp(rc+" \\w+","i"),rI=RegExp(s5+"|PlayBook|BB10","i"),rF={"NT3.51":"NT 3.11","NT4.0":"NT 4.0","5.0":"2000",5.1:"XP",5.2:"XP","6.0":"Vista",6.1:"7",6.2:"8",6.3:"8.1",6.4:"10","10.0":"10"},rC=(e,t)=>t&&w(t,s1)||function(e){return w(e,s3)&&!w(e,s7)&&!w(e,sZ)}(e),rP=function(e,t){return t=t||"",w(e," OPR/")&&w(e,"Mini")?rr:w(e," OPR/")?rs:rI.test(e)?s5:w(e,"IE"+sK)||w(e,"WPDesktop")?ri:w(e,s8)?s4:w(e,rn)||w(e,"Edg/")?ra:w(e,"FBIOS")?"Facebook "+sK:w(e,"UCWEB")||w(e,"UCBrowser")?"UC Browser":w(e,"CriOS")?re:w(e,"CrMo")||w(e,s7)?s7:w(e,sZ)&&w(e,s3)?rd:w(e,"FxiOS")?rl:w(e.toLowerCase(),rw.toLowerCase())?rw:rC(e,t)?w(e,sK)?rp:s3:w(e,ro)?ro:w(e,"MSIE")||w(e,"Trident/")?rt:w(e,"Gecko")?ro:""},rR={[ri]:[RegExp("rv:"+rE)],[ra]:[RegExp(rn+"?\\/"+rE)],[s7]:[RegExp("("+s7+"|CrMo)\\/"+rE)],[re]:[RegExp("CriOS\\/"+rE)],"UC Browser":[RegExp("(UCBrowser|UCWEB)\\/"+rE)],[s3]:[rS],[rp]:[rS],[rs]:[RegExp("(Opera|OPR)\\/"+rE)],[ro]:[RegExp(ro+"\\/"+rE)],[rl]:[RegExp("FxiOS\\/"+rE)],[rw]:[RegExp("Konqueror[:/]?"+rE,"i")],[s5]:[RegExp(s5+" "+rE),rS],[rd]:[RegExp("android\\s"+rE,"i")],[s4]:[RegExp(s8+"\\/"+rE)],[rt]:[RegExp("(rv:|MSIE )"+rE)],Mozilla:[RegExp("rv:"+rE)]},rT=function(e,t){var i=rR[rP(e,t)];if(T(i))return null;for(var s=0;s<i.length;s++){var r=i[s],n=e.match(r);if(n)return parseFloat(n[n.length-2])}return null},rO=[[RegExp(rh+"; "+rh+" (.*?)[);]","i"),e=>[rh,e&&e[1]||""]],[RegExp(rc,"i"),[rc,""]],[RegExp(ru,"i"),[ru,""]],[rI,[s5,""]],[RegExp(rg,"i"),(e,t)=>{if(/Phone/.test(t)||/WPDesktop/.test(t))return[r_,""];if(new RegExp(sK).test(t)&&!/IEMobile\b/.test(t))return[rg+" "+sK,""];var i=/Windows NT ([0-9.]+)/i.exec(t);if(i&&i[1]){var s=rF[i[1]]||"";return/arm/i.test(t)&&(s="RT"),[rg,s]}return[rg,""]}],[/((iPhone|iPad|iPod).*?OS (\d+)_(\d+)_?(\d+)?|iPhone)/,e=>e&&e[3]?["iOS",[e[3],e[4],e[5]||"0"].join(".")]:["iOS",""]],[/(watch.*\/(\d+\.\d+\.\d+)|watch os,(\d+\.\d+),)/i,e=>{var t="";return e&&e.length>=3&&(t=T(e[2])?e[3]:e[2]),["watchOS",t]}],[RegExp("("+sZ+" (\\d+)\\.(\\d+)\\.?(\\d+)?|"+sZ+")","i"),e=>e&&e[2]?[sZ,[e[2],e[3],e[4]||"0"].join(".")]:[sZ,""]],[/Mac OS X (\d+)[_.](\d+)[_.]?(\d+)?/i,e=>{var t=["Mac OS X",""];if(e&&e[1]){var i=[e[1],e[2],e[3]||"0"];t[1]=i.join(".")}return t}],[/Mac/i,["Mac OS X",""]],[/CrOS/,[s9,""]],[/Linux|debian/i,["Linux",""]]],rA=function(e){return r$.test(e)?rc:rk.test(e)?ru:rx.test(e)?rh:RegExp(rf,"i").test(e)?rf:RegExp("("+r_+"|WPDesktop)","i").test(e)?r_:/iPad/.test(e)?s0:/iPod/.test(e)?"iPod Touch":/iPhone/.test(e)?"iPhone":/(watch)(?: ?os[,/]|\d,\d\/)[\d.]+/i.test(e)?s2:rI.test(e)?s5:/(kobo)\s(ereader|touch)/i.test(e)?"Kobo":RegExp(rv,"i").test(e)?rv:/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i.test(e)||/(kf[a-z]+)( bui|\)).+silk\//i.test(e)?"Kindle Fire":/(Android|ZTE)/i.test(e)?!new RegExp(sK).test(e)||/(9138B|TB782B|Nexus [97]|pixel c|HUAWEISHT|BTV|noble nook|smart ultra 6)/i.test(e)?/pixel[\daxl ]{1,6}/i.test(e)&&!/pixel c/i.test(e)||/(huaweimed-al00|tah-|APA|SM-G92|i980|zte|U304AA)/i.test(e)||/lmy47v/i.test(e)&&!/QTAQZ3/i.test(e)?sZ:sQ:sZ:RegExp("(pda|"+sK+")","i").test(e)?ry:RegExp(sX,"i").test(e)&&!RegExp(sX+" pc","i").test(e)?rb:""},rM="https?://(.*)",rD=["gclid","gclsrc","dclid","gbraid","wbraid","fbclid","msclkid","twclid","li_fat_id","igshid","ttclid","rdt_cid","epik","qclid","sccid","irclid","_kx"],rL=X(["utm_source","utm_medium","utm_campaign","utm_content","utm_term","gad_source","mc_cid"],rD),rN="<masked>",rq=["li_fat_id"];function rj(e,t,i){if(!c)return{};var s,r=t?X([],rD,i||[]):[],n=rB(td(c.URL,r,rN),e);return Z((s={},K(rq,function(e){var t=tI.D(e);s[e]=t||null}),s),n)}function rB(e,t){var i=rL.concat(t||[]),s={};return K(i,function(t){var i=th(e,t);s[t]=i||null}),s}function rH(e){var t=e?0===e.search(rM+"google.([^/?]*)")?"google":0===e.search(rM+"bing.com")?"bing":0===e.search(rM+"yahoo.com")?"yahoo":0===e.search(rM+"duckduckgo.com")?"duckduckgo":null:null,i={};if(!M(t)){i.$search_engine=t;var s=c?th(c.referrer,"yahoo"!=t?"q":"p"):"";s.length&&(i.ph_keyword=s)}return i}function rz(){return navigator.language||navigator.userLanguage}function rU(){return(null==c?void 0:c.referrer)||"$direct"}function rG(e,t){var i=e?X([],rD,t||[]):[],s=null==u?void 0:u.href.substring(0,1e3);return{r:rU().substring(0,1e3),u:s?td(s,i,rN):void 0}}function rV(e){var t,{r:i,u:s}=e,r={$referrer:i,$referring_domain:null==i?void 0:"$direct"==i?"$direct":null==(t=tc(i))?void 0:t.host};if(s){r.$current_url=s;var n=tc(s);r.$host=null==n?void 0:n.host,r.$pathname=null==n?void 0:n.pathname,Z(r,rB(s))}return i&&Z(r,rH(i)),r}function rW(){try{return Intl.DateTimeFormat().resolvedOptions().timeZone}catch(e){return}}var rY=z("[FeatureFlags]"),rJ="$active_feature_flags",rK="$override_feature_flags",rZ="$feature_flag_payloads",rX="$override_feature_flag_payloads",rQ="$feature_flag_request_id",r0=e=>{var t={};for(var[i,s]of Q(e||{}))s&&(t[i]=s);return t},r1=e=>{var t=e.flags;return t?(e.featureFlags=Object.fromEntries(Object.keys(t).map(e=>{var i;return[e,null!=(i=t[e].variant)?i:t[e].enabled]})),e.featureFlagPayloads=Object.fromEntries(Object.keys(t).filter(e=>t[e].enabled).filter(e=>{var i;return null==(i=t[e].metadata)?void 0:i.payload}).map(e=>{var i;return[e,null==(i=t[e].metadata)?void 0:i.payload]}))):rY.warn("Using an older version of the feature flags endpoint. Please upgrade your PostHog server to the latest version"),e},r2=function(e){return e.FeatureFlags="feature_flags",e.Recordings="recordings",e}({});class r3{constructor(e){this.me=!1,this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this._instance=e,this.featureFlagEventHandlers=[]}flags(){if(this._instance.config.__preview_remote_config)this.$e=!0;else{var e=!this.ke&&(this._instance.config.advanced_disable_feature_flags||this._instance.config.advanced_disable_feature_flags_on_first_load);this.Ee({disableFlags:e})}}get hasLoadedFlags(){return this.be}getFlags(){return Object.keys(this.getFlagVariants())}getFlagsWithDetails(){var e=this._instance.get_property(eR),t=this._instance.get_property(rK),i=this._instance.get_property(rX);if(!i&&!t)return e||{};var s=Z({},e||{});for(var r of[...new Set([...Object.keys(i||{}),...Object.keys(t||{})])]){var n,a,o=s[r],l=null==t?void 0:t[r],c=T(l)?null!=(n=null==o?void 0:o.enabled)&&n:!!l,u=T(l)?o.variant:"string"==typeof l?l:void 0,h=null==i?void 0:i[r],d=V({},o,{enabled:c,variant:c?null!=u?u:null==o?void 0:o.variant:void 0});c!==(null==o?void 0:o.enabled)&&(d.original_enabled=null==o?void 0:o.enabled),u!==(null==o?void 0:o.variant)&&(d.original_variant=null==o?void 0:o.variant),h&&(d.metadata=V({},null==o?void 0:o.metadata,{payload:h,original_payload:null==o||null==(a=o.metadata)?void 0:a.payload})),s[r]=d}return this.me||(rY.warn(" Overriding feature flag details!",{flagDetails:e,overriddenPayloads:i,finalDetails:s}),this.me=!0),s}getFlagVariants(){var e=this._instance.get_property(eC),t=this._instance.get_property(rK);if(!t)return e||{};for(var i=Z({},e),s=Object.keys(t),r=0;r<s.length;r++)i[s[r]]=t[s[r]];return this.me||(rY.warn(" Overriding feature flags!",{enabledFlags:e,overriddenFlags:t,finalFlags:i}),this.me=!0),i}getFlagPayloads(){var e=this._instance.get_property(rZ),t=this._instance.get_property(rX);if(!t)return e||{};for(var i=Z({},e||{}),s=Object.keys(t),r=0;r<s.length;r++)i[s[r]]=t[s[r]];return this.me||(rY.warn(" Overriding feature flag payloads!",{flagPayloads:e,overriddenPayloads:t,finalPayloads:i}),this.me=!0),i}reloadFeatureFlags(){this.we||this._instance.config.advanced_disable_feature_flags||this.ke||(this.ke=setTimeout(()=>{this.Ee()},5))}Ie(){clearTimeout(this.ke),this.ke=void 0}ensureFlagsLoaded(){this.be||this.ye||this.ke||this.reloadFeatureFlags()}setAnonymousDistinctId(e){this.$anon_distinct_id=e}setReloadingPaused(e){this.we=e}Ee(e){var t;if(this.Ie(),!this._instance.I())if(this.ye)this.Se=!0;else{var i={token:this._instance.config.token,distinct_id:this._instance.get_distinct_id(),groups:this._instance.getGroups(),$anon_distinct_id:this.$anon_distinct_id,person_properties:V({},(null==(t=this._instance.persistence)?void 0:t.get_initial_props())||{},this._instance.get_property(eT)||{}),group_properties:this._instance.get_property(eO)};(null!=e&&e.disableFlags||this._instance.config.advanced_disable_feature_flags)&&(i.disable_flags=!0);var s=this._instance.config.__preview_remote_config,r=this._instance.config.advanced_only_evaluate_survey_feature_flags?"&only_evaluate_survey_feature_flags=true":"",n=this._instance.requestRouter.endpointFor("api",(s?"/flags/?v=2":"/flags/?v=2&config=true")+r);s&&(i.timezone=rW()),this.ye=!0,this._instance.Pe({method:"POST",url:n,data:i,compression:this._instance.config.disable_compression?void 0:y.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:e=>{var t,s,r,n=!0;200===e.statusCode&&(this.Se||(this.$anon_distinct_id=void 0),n=!1),this.ye=!1,this.$e||(this.$e=!0,this._instance.Re(null!=(r=e.json)?r:{})),(!i.disable_flags||this.Se)&&((this.xe=!n,e.json&&null!=(s=e.json.quotaLimited)&&s.includes(r2.FeatureFlags))?rY.warn("You have hit your feature flags quota limit, and will not be able to load feature flags until the quota is reset.  Please visit https://posthog.com/docs/billing/limits-alerts to learn more."):(i.disable_flags||this.receivedFeatureFlags(null!=(t=e.json)?t:{},n),this.Se&&(this.Se=!1,this.Ee())))}})}}getFeatureFlag(e,t){if(void 0===t&&(t={}),this.be||this.getFlags()&&this.getFlags().length>0){var i=this.getFlagVariants()[e],s=""+i,r=this._instance.get_property(rQ)||void 0,n=this._instance.get_property(eD)||{};if((t.send_event||!("send_event"in t))&&(!(e in n)||!n[e].includes(s))){F(n[e])?n[e].push(s):n[e]=[s],null==(l=this._instance.persistence)||l.register({[eD]:n});var a=this.getFeatureFlagDetails(e),o={$feature_flag:e,$feature_flag_response:i,$feature_flag_payload:this.getFeatureFlagPayload(e)||null,$feature_flag_request_id:r,$feature_flag_bootstrapped_response:(null==(c=this._instance.config.bootstrap)||null==(c=c.featureFlags)?void 0:c[e])||null,$feature_flag_bootstrapped_payload:(null==(u=this._instance.config.bootstrap)||null==(u=u.featureFlagPayloads)?void 0:u[e])||null,$used_bootstrap_value:!this.xe};T(null==a||null==(h=a.metadata)?void 0:h.version)||(o.$feature_flag_version=a.metadata.version);var l,c,u,h,d,p,g,_,v,f,m=null!=(d=null==a||null==(p=a.reason)?void 0:p.description)?d:null==a||null==(g=a.reason)?void 0:g.code;m&&(o.$feature_flag_reason=m),null!=a&&null!=(_=a.metadata)&&_.id&&(o.$feature_flag_id=a.metadata.id),T(null==a?void 0:a.original_variant)&&T(null==a?void 0:a.original_enabled)||(o.$feature_flag_original_response=T(a.original_variant)?a.original_enabled:a.original_variant),null!=a&&null!=(v=a.metadata)&&v.original_payload&&(o.$feature_flag_original_payload=null==a||null==(f=a.metadata)?void 0:f.original_payload),this._instance.capture("$feature_flag_called",o)}return i}rY.warn('getFeatureFlag for key "'+e+"\" failed. Feature flags didn't load in time.")}getFeatureFlagDetails(e){return this.getFlagsWithDetails()[e]}getFeatureFlagPayload(e){return this.getFlagPayloads()[e]}getRemoteConfigPayload(e,t){var i=this._instance.config.token;this._instance.Pe({method:"POST",url:this._instance.requestRouter.endpointFor("api","/flags/?v=2&config=true"),data:{distinct_id:this._instance.get_distinct_id(),token:i},compression:this._instance.config.disable_compression?void 0:y.Base64,timeout:this._instance.config.feature_flag_request_timeout_ms,callback:i=>{var s,r=null==(s=i.json)?void 0:s.featureFlagPayloads;t((null==r?void 0:r[e])||void 0)}})}isFeatureEnabled(e,t){if(void 0===t&&(t={}),this.be||this.getFlags()&&this.getFlags().length>0)return!!this.getFeatureFlag(e,t);rY.warn('isFeatureEnabled for key "'+e+"\" failed. Feature flags didn't load in time.")}addFeatureFlagsHandler(e){this.featureFlagEventHandlers.push(e)}removeFeatureFlagsHandler(e){this.featureFlagEventHandlers=this.featureFlagEventHandlers.filter(t=>t!==e)}receivedFeatureFlags(e,t){if(this._instance.persistence){this.be=!0;var i=this.getFlagVariants(),s=this.getFlagPayloads(),r=this.getFlagsWithDetails();!function(e,t,i,s,r){void 0===i&&(i={}),void 0===s&&(s={}),void 0===r&&(r={});var n=r1(e),a=n.flags,o=n.featureFlags,l=n.featureFlagPayloads;if(o){var c=e.requestId;if(F(o)){rY.warn("v1 of the feature flags endpoint is deprecated. Please use the latest version.");var u={};if(o)for(var h=0;h<o.length;h++)u[o[h]]=!0;t&&t.register({[rJ]:o,[eC]:u})}else{var d=o,p=l,g=a;e.errorsWhileComputingFlags&&(d=V({},i,d),p=V({},s,p),g=V({},r,g)),t&&t.register(V({[rJ]:Object.keys(r0(d)),[eC]:d||{},[rZ]:p||{},[eR]:g||{}},c?{[rQ]:c}:{}))}}}(e,this._instance.persistence,i,s,r),this.Te(t)}}override(e,t){void 0===t&&(t=!1),rY.warn("override is deprecated. Please use overrideFeatureFlags instead."),this.overrideFeatureFlags({flags:e,suppressWarning:t})}overrideFeatureFlags(e){if(!this._instance.__loaded||!this._instance.persistence)return rY.uninitializedWarning("posthog.featureFlags.overrideFeatureFlags");if(!1===e)return this._instance.persistence.unregister(rK),this._instance.persistence.unregister(rX),void this.Te();if(e&&"object"==typeof e&&("flags"in e||"payloads"in e)){var t;if(this.me=!!(null!=(t=e.suppressWarning)&&t),"flags"in e){if(!1===e.flags)this._instance.persistence.unregister(rK);else if(e.flags)if(F(e.flags)){for(var i={},s=0;s<e.flags.length;s++)i[e.flags[s]]=!0;this._instance.persistence.register({[rK]:i})}else this._instance.persistence.register({[rK]:e.flags})}return"payloads"in e&&(!1===e.payloads?this._instance.persistence.unregister(rX):e.payloads&&this._instance.persistence.register({[rX]:e.payloads})),void this.Te()}this.Te()}onFeatureFlags(e){if(this.addFeatureFlagsHandler(e),this.be){var{flags:t,flagVariants:i}=this.Me();e(t,i)}return()=>this.removeFeatureFlagsHandler(e)}updateEarlyAccessFeatureEnrollment(e,t,i){var s,r=(this._instance.get_property(eP)||[]).find(t=>t.flagKey===e),n={["$feature_enrollment/"+e]:t},a={$feature_flag:e,$feature_enrollment:t,$set:n};r&&(a.$early_access_feature_name=r.name),i&&(a.$feature_enrollment_stage=i),this._instance.capture("$feature_enrollment_update",a),this.setPersonPropertiesForFlags(n,!1);var o=V({},this.getFlagVariants(),{[e]:t});null==(s=this._instance.persistence)||s.register({[rJ]:Object.keys(r0(o)),[eC]:o}),this.Te()}getEarlyAccessFeatures(e,t,i){void 0===t&&(t=!1);var s=this._instance.get_property(eP),r=i?"&"+i.map(e=>"stage="+e).join("&"):"";if(s&&!t)return e(s);this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/early_access_features/?token="+this._instance.config.token+r),method:"GET",callback:t=>{var i,s;if(t.json){var r=t.json.earlyAccessFeatures;return null==(i=this._instance.persistence)||i.unregister(eP),null==(s=this._instance.persistence)||s.register({[eP]:r}),e(r)}}})}Me(){var e=this.getFlags(),t=this.getFlagVariants();return{flags:e.filter(e=>t[e]),flagVariants:Object.keys(t).filter(e=>t[e]).reduce((e,i)=>(e[i]=t[i],e),{})}}Te(e){var{flags:t,flagVariants:i}=this.Me();this.featureFlagEventHandlers.forEach(s=>s(t,i,{errorsLoading:e}))}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0);var i=this._instance.get_property(eT)||{};this._instance.register({[eT]:V({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetPersonPropertiesForFlags(){this._instance.unregister(eT)}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0);var i=this._instance.get_property(eO)||{};0!==Object.keys(i).length&&Object.keys(i).forEach(t=>{i[t]=V({},i[t],e[t]),delete e[t]}),this._instance.register({[eO]:V({},i,e)}),t&&this._instance.reloadFeatureFlags()}resetGroupPropertiesForFlags(e){if(e){var t=this._instance.get_property(eO)||{};this._instance.register({[eO]:V({},t,{[e]:{}})})}else this._instance.unregister(eO)}reset(){this.be=!1,this.ye=!1,this.we=!1,this.Se=!1,this.$e=!1,this.xe=!1,this.$anon_distinct_id=void 0,this.Ie(),this.me=!1}}var r5=["cookie","localstorage","localstorage+cookie","sessionstorage","memory"];class r6{constructor(e,t){this.S=e,this.props={},this.Ce=!1,this.Fe=(e=>{var t="";return e.token&&(t=e.token.replace(/\+/g,"PL").replace(/\//g,"SL").replace(/=/g,"EQ")),e.persistence_name?"ph_"+e.persistence_name:"ph_"+t+"_posthog"})(e),this.B=this.Oe(e),this.load(),e.debug&&H.info("Persistence loaded",e.persistence,V({},this.props)),this.update_config(e,e,t),this.save()}isDisabled(){return!!this.Ae}Oe(e){-1===r5.indexOf(e.persistence.toLowerCase())&&(H.critical("Unknown persistence type "+e.persistence+"; falling back to localStorage+cookie"),e.persistence="localStorage+cookie");var t=e.persistence.toLowerCase();return"localstorage"===t&&tC.O()?tC:"localstorage+cookie"===t&&tR.O()?tR:"sessionstorage"===t&&tM.O()?tM:"memory"===t?tO:"cookie"===t?tI:tR.O()?tR:tI}properties(){var e={};return K(this.props,function(t,i){if(i===eC&&P(t))for(var s,r=Object.keys(t),n=0;n<r.length;n++)e["$feature/"+r[n]]=t[r[n]];else s=!1,(M(eV)?s:o&&eV.indexOf===o?-1!=eV.indexOf(i):(K(eV,function(e){if(s||(s=e===i))return Y}),s))||(e[i]=t)}),e}load(){if(!this.Ae){var e=this.B.L(this.Fe);e&&(this.props=Z({},e))}}save(){this.Ae||this.B.j(this.Fe,this.props,this.De,this.Le,this.je,this.S.debug)}remove(){this.B.N(this.Fe,!1),this.B.N(this.Fe,!0)}clear(){this.remove(),this.props={}}register_once(e,t,i){if(P(e)){T(t)&&(t="None"),this.De=T(i)?this.Ne:i;var s=!1;if(K(e,(e,i)=>{this.props.hasOwnProperty(i)&&this.props[i]!==t||(this.props[i]=e,s=!0)}),s)return this.save(),!0}return!1}register(e,t){if(P(e)){this.De=T(t)?this.Ne:t;var i=!1;if(K(e,(t,s)=>{e.hasOwnProperty(s)&&this.props[s]!==t&&(this.props[s]=t,i=!0)}),i)return this.save(),!0}return!1}unregister(e){e in this.props&&(delete this.props[e],this.save())}update_campaign_params(){if(!this.Ce){var e=rj(this.S.custom_campaign_params,this.S.mask_personal_data_properties,this.S.custom_personal_data_properties);R(ei(e))||this.register(e),this.Ce=!0}}update_search_keyword(){var e;this.register((e=null==c?void 0:c.referrer)?rH(e):{})}update_referrer_info(){var e;this.register_once({$referrer:rU(),$referring_domain:null!=c&&c.referrer&&(null==(e=tc(c.referrer))?void 0:e.host)||"$direct"},void 0)}set_initial_person_info(){this.props[ej]||this.props[eB]||this.register_once({[eH]:rG(this.S.mask_personal_data_properties,this.S.custom_personal_data_properties)},void 0)}get_initial_props(){var e={};K([eB,ej],t=>{var i=this.props[t];i&&K(i,function(t,i){e["$initial_"+S(i)]=t})});var t,i,s=this.props[eH];return s&&Z(e,(t=rV(s),i={},K(t,function(e,t){i["$initial_"+S(t)]=e}),i)),e}safe_merge(e){return K(this.props,function(t,i){i in e||(e[i]=t)}),e}update_config(e,t,i){if(this.Ne=this.De=e.cookie_expiration,this.set_disabled(e.disable_persistence||!!i),this.set_cross_subdomain(e.cross_subdomain_cookie),this.set_secure(e.secure_cookie),e.persistence!==t.persistence){var s=this.Oe(e),r=this.props;this.clear(),this.B=s,this.props=r,this.save()}}set_disabled(e){this.Ae=e,this.Ae?this.remove():this.save()}set_cross_subdomain(e){e!==this.Le&&(this.Le=e,this.remove(),this.save())}set_secure(e){e!==this.je&&(this.je=e,this.remove(),this.save())}set_event_timer(e,t){var i=this.props[el]||{};i[e]=t,this.props[el]=i,this.save()}remove_event_timer(e){var t=(this.props[el]||{})[e];return T(t)||(delete this.props[el][e],this.save()),t}get_property(e){return this.props[e]}set_property(e,t){this.props[e]=t,this.save()}}class r8{constructor(){this.ze={},this.ze={}}on(e,t){return this.ze[e]||(this.ze[e]=[]),this.ze[e].push(t),()=>{this.ze[e]=this.ze[e].filter(e=>e!==t)}}emit(e,t){for(var i of this.ze[e]||[])i(t);for(var s of this.ze["*"]||[])s(e,t)}}class r4{constructor(e){this.Ue=new r8,this.qe=(e,t)=>this.Be(e,t)&&this.He(e,t)&&this.We(e,t),this.Be=(e,t)=>null==t||!t.event||(null==e?void 0:e.event)===(null==t?void 0:t.event),this._instance=e,this.Ge=new Set,this.Je=new Set}init(){var e,t;T(null==(e=this._instance)?void 0:e.Ve)||null==(t=this._instance)||t.Ve((e,t)=>{this.on(e,t)})}register(e){var t,i;if(!T(null==(t=this._instance)?void 0:t.Ve)&&(e.forEach(e=>{var t,i;null==(t=this.Je)||t.add(e),null==(i=e.steps)||i.forEach(e=>{var t;null==(t=this.Ge)||t.add((null==e?void 0:e.event)||"")})}),null!=(i=this._instance)&&i.autocapture)){var s,r=new Set;e.forEach(e=>{var t;null==(t=e.steps)||t.forEach(e=>{null!=e&&e.selector&&r.add(null==e?void 0:e.selector)})}),null==(s=this._instance)||s.autocapture.setElementSelectors(r)}}on(e,t){var i;null!=t&&0!=e.length&&(this.Ge.has(e)||this.Ge.has(null==t?void 0:t.event))&&this.Je&&(null==(i=this.Je)?void 0:i.size)>0&&this.Je.forEach(e=>{this.Ke(t,e)&&this.Ue.emit("actionCaptured",e.name)})}Ye(e){this.onAction("actionCaptured",t=>e(t))}Ke(e,t){if(null==(null==t?void 0:t.steps))return!1;for(var i of t.steps)if(this.qe(e,i))return!0;return!1}onAction(e,t){return this.Ue.on(e,t)}He(e,t){if(null!=t&&t.url){var i,s=null==e||null==(i=e.properties)?void 0:i.$current_url;if(!s||"string"!=typeof s||!r4.Xe(s,null==t?void 0:t.url,(null==t?void 0:t.url_matching)||"contains"))return!1}return!0}static Xe(e,t,i){switch(i){case"regex":return!!s&&sU(e,t);case"exact":return t===e;case"contains":return sU(e,r4.Qe(t).replace(/_/g,".").replace(/%/g,".*"));default:return!1}}static Qe(e){return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}We(e,t){if((null!=t&&t.href||null!=t&&t.tag_name||null!=t&&t.text)&&!this.Ze(e).some(e=>!(null!=t&&t.href&&!r4.Xe(e.href||"",null==t?void 0:t.href,(null==t?void 0:t.href_matching)||"exact"))&&(null==t||!t.tag_name||e.tag_name===(null==t?void 0:t.tag_name))&&!(null!=t&&t.text&&!r4.Xe(e.text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact")&&!r4.Xe(e.$el_text||"",null==t?void 0:t.text,(null==t?void 0:t.text_matching)||"exact"))))return!1;if(null!=t&&t.selector){var i,s=null==e||null==(i=e.properties)?void 0:i.$element_selectors;if(!s||!s.includes(null==t?void 0:t.selector))return!1}return!0}Ze(e){return null==(null==e?void 0:e.properties.$elements)?[]:null==e?void 0:e.properties.$elements}}(function(e){e.Button="button",e.Tab="tab",e.Selector="selector"})({}),function(e){e.TopLeft="top_left",e.TopRight="top_right",e.TopCenter="top_center",e.MiddleLeft="middle_left",e.MiddleRight="middle_right",e.MiddleCenter="middle_center",e.Left="left",e.Center="center",e.Right="right",e.NextToTrigger="next_to_trigger"}({});var r7=function(e){return e.Popover="popover",e.API="api",e.Widget="widget",e.ExternalSurvey="external_survey",e}({}),r9=(function(e){e.Open="open",e.MultipleChoice="multiple_choice",e.SingleChoice="single_choice",e.Rating="rating",e.Link="link"}({}),function(e){e.NextQuestion="next_question",e.End="end",e.ResponseBased="response_based",e.SpecificQuestion="specific_question"}({}),function(e){e.Once="once",e.Recurring="recurring",e.Always="always"}({}),function(e){return e.SHOWN="survey shown",e.DISMISSED="survey dismissed",e.SENT="survey sent",e}({})),ne=function(e){return e.SURVEY_ID="$survey_id",e.SURVEY_NAME="$survey_name",e.SURVEY_RESPONSE="$survey_response",e.SURVEY_ITERATION="$survey_iteration",e.SURVEY_ITERATION_START_DATE="$survey_iteration_start_date",e.SURVEY_PARTIALLY_COMPLETED="$survey_partially_completed",e.SURVEY_SUBMISSION_ID="$survey_submission_id",e.SURVEY_QUESTIONS="$survey_questions",e.SURVEY_COMPLETED="$survey_completed",e}({}),nt=z("[Surveys]"),ni="seenSurvey_",ns=(e,t)=>{var i="$survey_"+t+"/"+e.id;return e.current_iteration&&e.current_iteration>0&&(i="$survey_"+t+"/"+e.id+"/"+e.current_iteration),i},nr=[r7.Popover,r7.Widget,r7.API];class nn{constructor(e){this._instance=e,this.tr=new Map,this.ir=new Map}register(e){var t;T(null==(t=this._instance)?void 0:t.Ve)||(this.er(e),this.rr(e))}rr(e){var t=e.filter(e=>{var t,i;return(null==(t=e.conditions)?void 0:t.actions)&&(null==(i=e.conditions)||null==(i=i.actions)||null==(i=i.values)?void 0:i.length)>0});0!==t.length&&(null==this.sr&&(this.sr=new r4(this._instance),this.sr.init(),this.sr.Ye(e=>{this.onAction(e)})),t.forEach(e=>{var t,i,s,r,n;e.conditions&&null!=(t=e.conditions)&&t.actions&&null!=(i=e.conditions)&&null!=(i=i.actions)&&i.values&&(null==(s=e.conditions)||null==(s=s.actions)||null==(s=s.values)?void 0:s.length)>0&&(null==(r=this.sr)||r.register(e.conditions.actions.values),null==(n=e.conditions)||null==(n=n.actions)||null==(n=n.values)||n.forEach(t=>{if(t&&t.name){var i=this.ir.get(t.name);i&&i.push(e.id),this.ir.set(t.name,i||[e.id])}}))}))}er(e){var t;0!==e.filter(e=>{var t,i;return(null==(t=e.conditions)?void 0:t.events)&&(null==(i=e.conditions)||null==(i=i.events)||null==(i=i.values)?void 0:i.length)>0}).length&&(null==(t=this._instance)||t.Ve((e,t)=>{this.onEvent(e,t)}),e.forEach(e=>{var t;null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||t.forEach(t=>{if(t&&t.name){var i=this.tr.get(t.name);i&&i.push(e.id),this.tr.set(t.name,i||[e.id])}})}))}onEvent(e,t){var i,s=(null==(i=this._instance)||null==(i=i.persistence)?void 0:i.props[eM])||[];if("survey shown"===e&&t&&s.length>0){nt.info("survey event matched, removing survey from activated surveys",{event:e,eventPayload:t,existingActivatedSurveys:s});var r,n=null==t||null==(r=t.properties)?void 0:r.$survey_id;if(n){var a=s.indexOf(n);a>=0&&(s.splice(a,1),this.nr(s))}}else this.tr.has(e)&&(nt.info("survey event matched, updating activated surveys",{event:e,surveys:this.tr.get(e)}),this.nr(s.concat(this.tr.get(e)||[])))}onAction(e){var t,i=(null==(t=this._instance)||null==(t=t.persistence)?void 0:t.props[eM])||[];this.ir.has(e)&&this.nr(i.concat(this.ir.get(e)||[]))}nr(e){var t;null==(t=this._instance)||null==(t=t.persistence)||t.register({[eM]:[...new Set(e)]})}getSurveys(){var e;return(null==(e=this._instance)||null==(e=e.persistence)?void 0:e.props[eM])||[]}getEventToSurveys(){return this.tr}ar(){return this.sr}}class na{constructor(e){this.lr=void 0,this._surveyManager=null,this.ur=!1,this.hr=!1,this.dr=[],this._instance=e,this._surveyEventReceiver=null}onRemoteConfig(e){var t=e.surveys;if(D(t))return nt.warn("Flags not loaded yet. Not loading surveys.");var i=F(t);this.lr=i?t.length>0:t,nt.info("flags response received, isSurveysEnabled: "+this.lr),this.loadIfEnabled()}reset(){localStorage.removeItem("lastSeenSurveyDate");for(var e=[],t=0;t<localStorage.length;t++){var i=localStorage.key(t);(null!=i&&i.startsWith(ni)||null!=i&&i.startsWith("inProgressSurvey_"))&&e.push(i)}e.forEach(e=>localStorage.removeItem(e))}loadIfEnabled(){if(!this._surveyManager)if(this.hr)nt.info("Already initializing surveys, skipping...");else if(this._instance.config.disable_surveys)nt.info("Disabled. Not loading surveys.");else{var e=null==_?void 0:_.__PosthogExtensions__;if(e){if(!T(this.lr)||this._instance.config.advanced_enable_surveys){var t=this.lr||this._instance.config.advanced_enable_surveys;this.hr=!0;try{var i=e.generateSurveys;if(i)return void this.vr(i,t);var s=e.loadExternalDependency;if(!s)return void this.cr("PostHog loadExternalDependency extension not found.");s(this._instance,"surveys",i=>{i||!e.generateSurveys?this.cr("Could not load surveys script",i):this.vr(e.generateSurveys,t)})}catch(e){throw this.cr("Error initializing surveys",e),e}finally{this.hr=!1}}}else nt.error("PostHog Extensions not found.")}}vr(e,t){this._surveyManager=e(this._instance,t),this._surveyEventReceiver=new nn(this._instance),nt.info("Surveys loaded successfully"),this.pr({isLoaded:!0})}cr(e,t){nt.error(e,t),this.pr({isLoaded:!1,error:e})}onSurveysLoaded(e){return this.dr.push(e),this._surveyManager&&this.pr({isLoaded:!0}),()=>{this.dr=this.dr.filter(t=>t!==e)}}getSurveys(e,t){if(void 0===t&&(t=!1),this._instance.config.disable_surveys)return nt.info("Disabled. Not loading surveys."),e([]);var i=this._instance.get_property(eA);if(i&&!t)return e(i,{isLoaded:!0});if(this.ur)return e([],{isLoaded:!1,error:"Surveys are already being loaded"});try{this.ur=!0,this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/surveys/?token="+this._instance.config.token),method:"GET",timeout:this._instance.config.surveys_request_timeout_ms,callback:t=>{this.ur=!1;var i=t.statusCode;if(200!==i||!t.json){var s="Surveys API could not be loaded, status: "+i;return nt.error(s),e([],{isLoaded:!1,error:s})}var r,n,a=t.json.surveys||[],o=a.filter(e=>{var t,i;return!(!e.start_date||e.end_date)&&(!(null==(t=e.conditions)||null==(t=t.events)||null==(t=t.values)||!t.length)||!(null==(i=e.conditions)||null==(i=i.actions)||null==(i=i.values)||!i.length))});return o.length>0&&(null==(n=this._surveyEventReceiver)||n.register(o)),null==(r=this._instance.persistence)||r.register({[eA]:a}),e(a,{isLoaded:!0})}})}catch(e){throw this.ur=!1,e}}pr(e){for(var t of this.dr)try{if(!e.isLoaded)return t([],e);this.getSurveys(t)}catch(e){nt.error("Error in survey callback",e)}}getActiveMatchingSurveys(e,t){if(void 0===t&&(t=!1),!D(this._surveyManager))return this._surveyManager.getActiveMatchingSurveys(e,t);nt.warn("init was not called")}_r(e){var t=null;return this.getSurveys(i=>{var s;t=null!=(s=i.find(t=>t.id===e))?s:null}),t}gr(e){if(D(this._surveyManager))return{eligible:!1,reason:"SDK is not enabled or survey functionality is not yet loaded"};var t="string"==typeof e?this._r(e):e;return t?this._surveyManager.checkSurveyEligibility(t):{eligible:!1,reason:"Survey not found"}}canRenderSurvey(e){if(D(this._surveyManager))return nt.warn("init was not called"),{visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"};var t=this.gr(e);return{visible:t.eligible,disabledReason:t.reason}}canRenderSurveyAsync(e,t){return D(this._surveyManager)?(nt.warn("init was not called"),Promise.resolve({visible:!1,disabledReason:"SDK is not enabled or survey functionality is not yet loaded"})):new Promise(i=>{this.getSurveys(t=>{var s,r=null!=(s=t.find(t=>t.id===e))?s:null;if(r){var n=this.gr(r);i({visible:n.eligible,disabledReason:n.reason})}else i({visible:!1,disabledReason:"Survey not found"})},t)})}renderSurvey(e,t){if(D(this._surveyManager))nt.warn("init was not called");else{var i=this._r(e);if(i)if(nr.includes(i.type)){var s=null==c?void 0:c.querySelector(t);s?this._surveyManager.renderSurvey(i,s):nt.warn("Survey element not found")}else nt.warn("Surveys of type "+i.type+" cannot be rendered in the app");else nt.warn("Survey not found")}}}var no=z("[RateLimiter]");class nl{constructor(e){var t,i;this.serverLimits={},this.lastEventRateLimited=!1,this.checkForLimiting=e=>{var t=e.text;if(t&&t.length)try{(JSON.parse(t).quota_limited||[]).forEach(e=>{no.info((e||"events")+" is quota limited."),this.serverLimits[e]=(new Date).getTime()+6e4})}catch(e){return void no.warn('could not rate limit - continuing. Error: "'+(null==e?void 0:e.message)+'"',{text:t})}},this.instance=e,this.captureEventsPerSecond=(null==(t=e.config.rate_limiting)?void 0:t.events_per_second)||10,this.captureEventsBurstLimit=Math.max((null==(i=e.config.rate_limiting)?void 0:i.events_burst_limit)||10*this.captureEventsPerSecond,this.captureEventsPerSecond),this.lastEventRateLimited=this.clientRateLimitContext(!0).isRateLimited}clientRateLimitContext(e){void 0===e&&(e=!1);var t,i,s,r=(new Date).getTime(),n=null!=(t=null==(i=this.instance.persistence)?void 0:i.get_property(eq))?t:{tokens:this.captureEventsBurstLimit,last:r};n.tokens+=(r-n.last)/1e3*this.captureEventsPerSecond,n.last=r,n.tokens>this.captureEventsBurstLimit&&(n.tokens=this.captureEventsBurstLimit);var a=n.tokens<1;return a||e||(n.tokens=Math.max(0,n.tokens-1)),!a||this.lastEventRateLimited||e||this.instance.capture("$$client_ingestion_warning",{$$client_ingestion_warning_message:"posthog-js client rate limited. Config is set to "+this.captureEventsPerSecond+" events per second and "+this.captureEventsBurstLimit+" events burst limit."},{skip_client_rate_limiting:!0}),this.lastEventRateLimited=a,null==(s=this.instance.persistence)||s.set_property(eq,n),{isRateLimited:a,remainingTokens:n.tokens}}isServerRateLimited(e){var t=this.serverLimits[e||"events"]||!1;return!1!==t&&(new Date).getTime()<t}}var nc=z("[RemoteConfig]");class nu{constructor(e){this._instance=e}get remoteConfig(){var e;return null==(e=_._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.config}mr(e){var t,i;null!=(t=_.__PosthogExtensions__)&&t.loadExternalDependency?null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,"remote-config",()=>e(this.remoteConfig)):(nc.error("PostHog Extensions not found. Cannot load remote config."),e())}br(e){this._instance.Pe({method:"GET",url:this._instance.requestRouter.endpointFor("assets","/array/"+this._instance.config.token+"/config"),callback:t=>{e(t.json)}})}load(){try{if(this.remoteConfig)return nc.info("Using preloaded remote config",this.remoteConfig),void this.Re(this.remoteConfig);if(this._instance.I())return void nc.warn("Remote config is disabled. Falling back to local config.");this.mr(e=>{if(!e)return nc.info("No config found after loading remote JS config. Falling back to JSON."),void this.br(e=>{this.Re(e)});this.Re(e)})}catch(e){nc.error("Error loading remote config",e)}}Re(e){e?this._instance.config.__preview_remote_config?(this._instance.Re(e),!1!==e.hasFeatureFlags&&this._instance.featureFlags.ensureFlagsLoaded()):nc.info("__preview_remote_config is disabled. Logging config instead",e):nc.error("Failed to fetch remote config from PostHog.")}}class nh{constructor(e,t){this.yr=!0,this.wr=[],this.Sr=tH((null==t?void 0:t.flush_interval_ms)||3e3,250,5e3,"flush interval",3e3),this.$r=e}enqueue(e){this.wr.push(e),this.kr||this.Er()}unload(){this.Ir();var e=Object.values(this.wr.length>0?this.Pr():{});[...e.filter(e=>0===e.url.indexOf("/e")),...e.filter(e=>0!==e.url.indexOf("/e"))].map(e=>{this.$r(V({},e,{transport:"sendBeacon"}))})}enable(){this.yr=!1,this.Er()}Er(){var e=this;this.yr||(this.kr=setTimeout(()=>{if(this.Ir(),this.wr.length>0){var t=this.Pr();for(var i in t)!function(){var s=t[i],r=(new Date).getTime();s.data&&F(s.data)&&K(s.data,e=>{e.offset=Math.abs(e.timestamp-r),delete e.timestamp}),e.$r(s)}()}},this.Sr))}Ir(){clearTimeout(this.kr),this.kr=void 0}Pr(){var e={};return K(this.wr,t=>{var i,s=(t?t.batchKey:null)||t.url;T(e[s])&&(e[s]=V({},t,{data:[]})),null==(i=e[s].data)||i.push(t.data)}),this.wr=[],e}}var nd=["retriesPerformedSoFar"];class np{constructor(e){this.Rr=!1,this.Tr=3e3,this.wr=[],this._instance=e,this.wr=[],this.Mr=!0,!T(s)&&"onLine"in s.navigator&&(this.Mr=s.navigator.onLine,en(s,"online",()=>{this.Mr=!0,this.se()}),en(s,"offline",()=>{this.Mr=!1}))}get length(){return this.wr.length}retriableRequest(e){var{retriesPerformedSoFar:t}=e,i=W(e,nd);L(t)&&t>0&&(i.url=sj(i.url,{retry_count:t})),this._instance.Pe(V({},i,{callback:e=>{200!==e.statusCode&&(e.statusCode<400||e.statusCode>=500)&&(null!=t?t:0)<10?this.Cr(V({retriesPerformedSoFar:t},i)):null==i.callback||i.callback(e)}}))}Cr(e){var t,i,s,r=e.retriesPerformedSoFar||0;e.retriesPerformedSoFar=r+1;var n=(s=(Math.random()-.5)*((i=Math.min(18e5,t=3e3*Math.pow(2,r)))-t/2),Math.ceil(i+s)),a=Date.now()+n;this.wr.push({retryAt:a,requestOptions:e});var o="Enqueued failed request for retry in "+n;navigator.onLine||(o+=" (Browser is offline)"),H.warn(o),this.Rr||(this.Rr=!0,this.Fr())}Fr(){this.Or&&clearTimeout(this.Or),this.Or=setTimeout(()=>{this.Mr&&this.wr.length>0&&this.se(),this.Fr()},this.Tr)}se(){var e=Date.now(),t=[],i=this.wr.filter(i=>i.retryAt<e||(t.push(i),!1));if(this.wr=t,i.length>0)for(var{requestOptions:s}of i)this.retriableRequest(s)}unload(){for(var{requestOptions:e}of(this.Or&&(clearTimeout(this.Or),this.Or=void 0),this.wr))try{this._instance.Pe(V({},e,{transport:"sendBeacon"}))}catch(e){H.error(e)}this.wr=[]}}class ng{constructor(e){this.Ar=()=>{this.Dr||(this.Dr={});var e,t,i,s,r=this.scrollElement(),n=this.scrollY(),a=r?Math.max(0,r.scrollHeight-r.clientHeight):0,o=n+((null==r?void 0:r.clientHeight)||0),l=(null==r?void 0:r.scrollHeight)||0;this.Dr.lastScrollY=Math.ceil(n),this.Dr.maxScrollY=Math.max(n,null!=(e=this.Dr.maxScrollY)?e:0),this.Dr.maxScrollHeight=Math.max(a,null!=(t=this.Dr.maxScrollHeight)?t:0),this.Dr.lastContentY=o,this.Dr.maxContentY=Math.max(o,null!=(i=this.Dr.maxContentY)?i:0),this.Dr.maxContentHeight=Math.max(l,null!=(s=this.Dr.maxContentHeight)?s:0)},this._instance=e}getContext(){return this.Dr}resetContext(){var e=this.Dr;return setTimeout(this.Ar,0),e}startMeasuringScrollPosition(){en(s,"scroll",this.Ar,{capture:!0}),en(s,"scrollend",this.Ar,{capture:!0}),en(s,"resize",this.Ar)}scrollElement(){if(!this._instance.config.scroll_root_selector)return null==s?void 0:s.document.documentElement;for(var e of F(this._instance.config.scroll_root_selector)?this._instance.config.scroll_root_selector:[this._instance.config.scroll_root_selector]){var t=null==s?void 0:s.document.querySelector(e);if(t)return t}}scrollY(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollTop||0}return s&&(s.scrollY||s.pageYOffset||s.document.documentElement.scrollTop)||0}scrollX(){if(this._instance.config.scroll_root_selector){var e=this.scrollElement();return e&&e.scrollLeft||0}return s&&(s.scrollX||s.pageXOffset||s.document.documentElement.scrollLeft)||0}}var n_=e=>rG(null==e?void 0:e.config.mask_personal_data_properties,null==e?void 0:e.config.custom_personal_data_properties);class nv{constructor(e,t,i,s){this.Lr=e=>{var t=this.jr();if(!t||t.sessionId!==e){var i={sessionId:e,props:this.Nr(this._instance)};this.zr.register({[eN]:i})}},this._instance=e,this.Ur=t,this.zr=i,this.Nr=s||n_,this.Ur.onSessionId(this.Lr)}jr(){return this.zr.props[eN]}getSetOnceProps(){var e,t=null==(e=this.jr())?void 0:e.props;return t?"r"in t?rV(t):{$referring_domain:t.referringDomain,$pathname:t.initialPathName,utm_source:t.utm_source,utm_campaign:t.utm_campaign,utm_medium:t.utm_medium,utm_content:t.utm_content,utm_term:t.utm_term}:{}}getSessionProps(){var e={};return K(ei(this.getSetOnceProps()),(t,i)=>{"$current_url"===i&&(i="url"),e["$session_entry_"+S(i)]=t}),e}}var nf=z("[SessionId]");class nm{constructor(e,t,i){if(this.qr=[],this.Br=(e,t)=>Math.abs(e-t)>this.sessionTimeoutMs,!e.persistence)throw Error("SessionIdManager requires a PostHogPersistence instance");if(e.config.__preview_experimental_cookieless_mode)throw Error("SessionIdManager cannot be used with __preview_experimental_cookieless_mode");this.S=e.config,this.zr=e.persistence,this.fi=void 0,this.Ct=void 0,this._sessionStartTimestamp=null,this._sessionActivityTimestamp=null,this.Hr=t||tS,this.Wr=i||tS;var s,r=this.S.persistence_name||this.S.token,n=this.S.session_idle_timeout_seconds||1800;if(this._sessionTimeoutMs=1e3*tH(n,60,36e3,"session_idle_timeout_seconds",1800),e.register({$configured_session_timeout_ms:this._sessionTimeoutMs}),this.Gr(),this.Jr="ph_"+r+"_window_id",this.Vr="ph_"+r+"_primary_window_exists",this.Kr()){var a=tM.L(this.Jr),o=tM.L(this.Vr);a&&!o?this.fi=a:tM.N(this.Jr),tM.j(this.Vr,!0)}if(null!=(s=this.S.bootstrap)&&s.sessionID)try{var l=(e=>{var t=e.replace(/-/g,"");if(32!==t.length)throw Error("Not a valid UUID");if("7"!==t[12])throw Error("Not a UUIDv7");return parseInt(t.substring(0,12),16)})(this.S.bootstrap.sessionID);this.Yr(this.S.bootstrap.sessionID,(new Date).getTime(),l)}catch(e){nf.error("Invalid sessionID in bootstrap",e)}this.Xr()}get sessionTimeoutMs(){return this._sessionTimeoutMs}onSessionId(e){return T(this.qr)&&(this.qr=[]),this.qr.push(e),this.Ct&&e(this.Ct,this.fi),()=>{this.qr=this.qr.filter(t=>t!==e)}}Kr(){return"memory"!==this.S.persistence&&!this.zr.Ae&&tM.O()}Qr(e){e!==this.fi&&(this.fi=e,this.Kr()&&tM.j(this.Jr,e))}Zr(){return this.fi?this.fi:this.Kr()?tM.L(this.Jr):null}Yr(e,t,i){e===this.Ct&&t===this._sessionActivityTimestamp&&i===this._sessionStartTimestamp||(this._sessionStartTimestamp=i,this._sessionActivityTimestamp=t,this.Ct=e,this.zr.register({[ek]:[t,e,i]}))}ts(){if(this.Ct&&this._sessionActivityTimestamp&&this._sessionStartTimestamp)return[this._sessionActivityTimestamp,this.Ct,this._sessionStartTimestamp];var e=this.zr.props[ek];return F(e)&&2===e.length&&e.push(e[0]),e||[0,null,0]}resetSessionId(){this.Yr(null,null,null)}Xr(){en(s,"beforeunload",()=>{this.Kr()&&tM.N(this.Vr)},{capture:!1})}checkAndGetSessionAndWindowId(e,t){if(void 0===e&&(e=!1),void 0===t&&(t=null),this.S.__preview_experimental_cookieless_mode)throw Error("checkAndGetSessionAndWindowId should not be called in __preview_experimental_cookieless_mode");var i=t||(new Date).getTime(),[s,r,n]=this.ts(),a=this.Zr(),o=L(n)&&n>0&&Math.abs(i-n)>864e5,l=!1,c=!r,u=!e&&this.Br(i,s);c||u||o?(r=this.Hr(),a=this.Wr(),nf.info("new session ID generated",{sessionId:r,windowId:a,changeReason:{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:o}}),n=i,l=!0):a||(a=this.Wr(),l=!0);var h=0===s||!e||o?i:s,d=0===n?(new Date).getTime():n;return this.Qr(a),this.Yr(r,h,d),e||this.Gr(),l&&this.qr.forEach(e=>e(r,a,l?{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:o}:void 0)),{sessionId:r,windowId:a,sessionStartTimestamp:d,changeReason:l?{noSessionId:c,activityTimeout:u,sessionPastMaximumLength:o}:void 0,lastActivityTimestamp:s}}Gr(){clearTimeout(this.es),this.es=setTimeout(()=>{var[e]=this.ts();this.Br((new Date).getTime(),e)&&this.resetSessionId()},1.1*this.sessionTimeoutMs)}}var ny=["$set_once","$set"],nb=z("[SiteApps]");class nw{constructor(e){this._instance=e,this.rs=[],this.apps={}}get isEnabled(){return!!this._instance.config.opt_in_site_apps}ss(e,t){if(t){var i=this.globalsForEvent(t);this.rs.push(i),this.rs.length>1e3&&(this.rs=this.rs.slice(10))}}get siteAppLoaders(){var e;return null==(e=_._POSTHOG_REMOTE_CONFIG)||null==(e=e[this._instance.config.token])?void 0:e.siteApps}init(){if(this.isEnabled){var e=this._instance.Ve(this.ss.bind(this));this.ns=()=>{e(),this.rs=[],this.ns=void 0}}}globalsForEvent(e){if(!e)throw Error("Event payload is required");var t,i,s,r,n,a,o,l={},c=this._instance.get_property("$groups")||[];for(var[u,h]of Object.entries(this._instance.get_property("$stored_group_properties")||{}))l[u]={id:c[u],type:u,properties:h};var{$set_once:d,$set:p}=e;return{event:V({},W(e,ny),{properties:V({},e.properties,p?{$set:V({},null!=(t=null==(i=e.properties)?void 0:i.$set)?t:{},p)}:{},d?{$set_once:V({},null!=(s=null==(r=e.properties)?void 0:r.$set_once)?s:{},d)}:{}),elements_chain:null!=(n=null==(a=e.properties)?void 0:a.$elements_chain)?n:"",distinct_id:null==(o=e.properties)?void 0:o.distinct_id}),person:{properties:this._instance.get_property("$stored_person_properties")},groups:l}}setupSiteApp(e){var t=this.apps[e.id],i=()=>{var i;!t.errored&&this.rs.length&&(nb.info("Processing "+this.rs.length+" events for site app with id "+e.id),this.rs.forEach(e=>null==t.processEvent?void 0:t.processEvent(e)),t.processedBuffer=!0),Object.values(this.apps).every(e=>e.processedBuffer||e.errored)&&(null==(i=this.ns)||i.call(this))},s=!1,r=r=>{t.errored=!r,t.loaded=!0,nb.info("Site app with id "+e.id+" "+(r?"loaded":"errored")),s&&i()};try{var{processEvent:n}=e.init({posthog:this._instance,callback:e=>{r(e)}});n&&(t.processEvent=n),s=!0}catch(t){nb.error("Error while initializing PostHog app with config id "+e.id,t),r(!1)}if(s&&t.loaded)try{i()}catch(i){nb.error("Error while processing buffered events PostHog app with config id "+e.id,i),t.errored=!0}}os(){var e=this.siteAppLoaders||[];for(var t of e)this.apps[t.id]={id:t.id,loaded:!1,errored:!1,processedBuffer:!1};for(var i of e)this.setupSiteApp(i)}ls(e){if(0!==Object.keys(this.apps).length){var t=this.globalsForEvent(e);for(var i of Object.values(this.apps))try{null==i.processEvent||i.processEvent(t)}catch(t){nb.error("Error while processing event "+e.event+" for site app "+i.id,t)}}}onRemoteConfig(e){var t,i,s,r=this;if(null!=(t=this.siteAppLoaders)&&t.length)return this.isEnabled?(this.os(),void this._instance.on("eventCaptured",e=>this.ls(e))):void nb.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.');if(null==(i=this.ns)||i.call(this),null!=(s=e.siteApps)&&s.length)if(this.isEnabled){var n=function(e){var t;_["__$$ph_site_app_"+e]=r._instance,null==(t=_.__PosthogExtensions__)||null==t.loadSiteApp||t.loadSiteApp(r._instance,o,t=>{if(t)return nb.error("Error while initializing PostHog app with config id "+e,t)})};for(var{id:a,url:o}of e.siteApps)n(a)}else nb.error('PostHog site apps are disabled. Enable the "opt_in_site_apps" config to proceed.')}}var nE=["amazonbot","amazonproductbot","app.hypefactors.com","applebot","archive.org_bot","awariobot","backlinksextendedbot","baiduspider","bingbot","bingpreview","chrome-lighthouse","dataforseobot","deepscan","duckduckbot","facebookexternal","facebookcatalog","http://yandex.com/bots","hubspot","ia_archiver","leikibot","linkedinbot","meta-externalagent","mj12bot","msnbot","nessus","petalbot","pinterest","prerender","rogerbot","screaming frog","sebot-wa","sitebulb","slackbot","slurp","trendictionbot","turnitin","twitterbot","vercel-screenshot","vercelbot","yahoo! slurp","yandexbot","zoombot","bot.htm","bot.php","(bot;","bot/","crawler","ahrefsbot","ahrefssiteaudit","semrushbot","siteauditbot","splitsignalbot","gptbot","oai-searchbot","chatgpt-user","perplexitybot","better uptime bot","sentryuptimebot","uptimerobot","headlesschrome","cypress","google-hoteladsverifier","adsbot-google","apis-google","duplexweb-google","feedfetcher-google","google favicon","google web preview","google-read-aloud","googlebot","googleother","google-cloudvertexbot","googleweblight","mediapartners-google","storebot-google","google-inspectiontool","bytespider"],nS=function(e,t){if(!e)return!1;var i=e.toLowerCase();return nE.concat(t||[]).some(e=>{var t=e.toLowerCase();return -1!==i.indexOf(t)})},nx=function(e,t){if(!e)return!1;var i=e.userAgent;if(i&&nS(i,t))return!0;try{var s=null==e?void 0:e.userAgentData;if(null!=s&&s.brands&&s.brands.some(e=>nS(null==e?void 0:e.brand,t)))return!0}catch(e){}return!!e.webdriver},nk=function(e){return e.US="us",e.EU="eu",e.CUSTOM="custom",e}({}),n$="i.posthog.com";class nI{constructor(e){this.us={},this.instance=e}get apiHost(){var e=this.instance.config.api_host.trim().replace(/\/$/,"");return"https://app.posthog.com"===e?"https://us.i.posthog.com":e}get uiHost(){var e,t=null==(e=this.instance.config.ui_host)?void 0:e.replace(/\/$/,"");return t||(t=this.apiHost.replace("."+n$,".posthog.com")),"https://app.posthog.com"===t?"https://us.posthog.com":t}get region(){return this.us[this.apiHost]||(/https:\/\/(app|us|us-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.us[this.apiHost]=nk.US:/https:\/\/(eu|eu-assets)(\.i)?\.posthog\.com/i.test(this.apiHost)?this.us[this.apiHost]=nk.EU:this.us[this.apiHost]=nk.CUSTOM),this.us[this.apiHost]}endpointFor(e,t){if(void 0===t&&(t=""),t&&(t="/"===t[0]?t:"/"+t),"ui"===e)return this.uiHost+t;if(this.region===nk.CUSTOM)return this.apiHost+t;var i=n$+t;switch(e){case"assets":return"https://"+this.region+"-assets."+i;case"api":return"https://"+this.region+"."+i}}}var nF={icontains:(e,t)=>!!s&&t.href.toLowerCase().indexOf(e.toLowerCase())>-1,not_icontains:(e,t)=>!!s&&-1===t.href.toLowerCase().indexOf(e.toLowerCase()),regex:(e,t)=>!!s&&sU(t.href,e),not_regex:(e,t)=>!!s&&!sU(t.href,e),exact:(e,t)=>t.href===e,is_not:(e,t)=>t.href!==e};class nC{constructor(e){var t=this;this.getWebExperimentsAndEvaluateDisplayLogic=function(e){void 0===e&&(e=!1),t.getWebExperiments(e=>{nC.hs("retrieved web experiments from the server"),t.ds=new Map,e.forEach(e=>{if(e.feature_flag_key){t.ds&&(nC.hs("setting flag key ",e.feature_flag_key," to web experiment ",e),null==(i=t.ds)||i.set(e.feature_flag_key,e));var i,s=t._instance.getFeatureFlag(e.feature_flag_key);O(s)&&e.variants[s]&&t.vs(e.name,s,e.variants[s].transforms)}else if(e.variants)for(var r in e.variants){var n=e.variants[r];nC.cs(n)&&t.vs(e.name,r,n.transforms)}})},e)},this._instance=e,this._instance.onFeatureFlags(e=>{this.onFeatureFlags(e)})}onFeatureFlags(e){if(this._is_bot())nC.hs("Refusing to render web experiment since the viewer is a likely bot");else if(!this._instance.config.disable_web_experiments){if(D(this.ds))return this.ds=new Map,this.loadIfEnabled(),void this.previewWebExperiment();nC.hs("applying feature flags",e),e.forEach(e=>{var t;if(this.ds&&null!=(t=this.ds)&&t.has(e)){var i,s=this._instance.getFeatureFlag(e),r=null==(i=this.ds)?void 0:i.get(e);s&&null!=r&&r.variants[s]&&this.vs(r.name,s,r.variants[s].transforms)}})}}previewWebExperiment(){var e=nC.getWindowLocation();if(null!=e&&e.search){var t=th(null==e?void 0:e.search,"__experiment_id"),i=th(null==e?void 0:e.search,"__experiment_variant");t&&i&&(nC.hs("previewing web experiments "+t+" && "+i),this.getWebExperiments(e=>{this.fs(parseInt(t),i,e)},!1,!0))}}loadIfEnabled(){this._instance.config.disable_web_experiments||this.getWebExperimentsAndEvaluateDisplayLogic()}getWebExperiments(e,t,i){if(this._instance.config.disable_web_experiments&&!i)return e([]);var s=this._instance.get_property("$web_experiments");if(s&&!t)return e(s);this._instance.Pe({url:this._instance.requestRouter.endpointFor("api","/api/web_experiments/?token="+this._instance.config.token),method:"GET",callback:t=>200===t.statusCode&&t.json?e(t.json.experiments||[]):e([])})}fs(e,t,i){var s=i.filter(t=>t.id===e);s&&s.length>0&&(nC.hs("Previewing web experiment ["+s[0].name+"] with variant ["+t+"]"),this.vs(s[0].name,t,s[0].variants[t].transforms))}static cs(e){return!D(e.conditions)&&nC.ps(e)&&nC._s(e)}static ps(e){if(D(e.conditions)||D(null==(t=e.conditions)?void 0:t.url))return!0;var t,i,s,r,n=nC.getWindowLocation();return!!n&&(null==(i=e.conditions)||!i.url||nF[null!=(s=null==(r=e.conditions)?void 0:r.urlMatchType)?s:"icontains"](e.conditions.url,n))}static getWindowLocation(){return null==s?void 0:s.location}static _s(e){if(D(e.conditions)||D(null==(i=e.conditions)?void 0:i.utm))return!0;var t=rj();if(t.utm_source){var i,s,r,n,a,o,l,c,u,h=null==(s=e.conditions)||null==(s=s.utm)||!s.utm_campaign||(null==(r=e.conditions)||null==(r=r.utm)?void 0:r.utm_campaign)==t.utm_campaign,d=null==(n=e.conditions)||null==(n=n.utm)||!n.utm_source||(null==(a=e.conditions)||null==(a=a.utm)?void 0:a.utm_source)==t.utm_source,p=null==(o=e.conditions)||null==(o=o.utm)||!o.utm_medium||(null==(l=e.conditions)||null==(l=l.utm)?void 0:l.utm_medium)==t.utm_medium,g=null==(c=e.conditions)||null==(c=c.utm)||!c.utm_term||(null==(u=e.conditions)||null==(u=u.utm)?void 0:u.utm_term)==t.utm_term;return h&&p&&g&&d}return!1}static hs(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];H.info("[WebExperiments] "+e,i)}vs(e,t,i){this._is_bot()?nC.hs("Refusing to render web experiment since the viewer is a likely bot"):"control"!==t?i.forEach(i=>{if(i.selector){nC.hs("applying transform of variant "+t+" for experiment "+e+" ",i);var s,r=null==(s=document)?void 0:s.querySelectorAll(i.selector);null==r||r.forEach(e=>{i.html&&(e.innerHTML=i.html),i.css&&e.setAttribute("style",i.css)})}}):nC.hs("Control variants leave the page unmodified.")}_is_bot(){return l&&this._instance?nx(l,this._instance.config.custom_blocked_useragents):void 0}}var nP=z("[PostHog ExternalIntegrations]"),nR={intercom:"intercom-integration",crispChat:"crisp-chat-integration"};class nT{constructor(e){this._instance=e}J(e,t){var i;null==(i=_.__PosthogExtensions__)||null==i.loadExternalDependency||i.loadExternalDependency(this._instance,e,e=>{if(e)return nP.error("failed to load script",e);t()})}startIfEnabledOrStop(){var e,t=this,i=function(e){var i,s,n;!r||null!=(i=_.__PosthogExtensions__)&&null!=(i=i.integrations)&&i[e]||t.J(nR[e],()=>{var i;null==(i=_.__PosthogExtensions__)||null==(i=i.integrations)||null==(i=i[e])||i.start(t._instance)}),!r&&null!=(s=_.__PosthogExtensions__)&&null!=(s=s.integrations)&&s[e]&&(null==(n=_.__PosthogExtensions__)||null==(n=n.integrations)||null==(n=n[e])||n.stop())};for(var[s,r]of Object.entries(null!=(e=this._instance.config.integrations)?e:{}))i(s)}}var nO={},nA=()=>{},nM="posthog",nD=!sN&&-1===(null==g?void 0:g.indexOf("MSIE"))&&-1===(null==g?void 0:g.indexOf("Mozilla")),nL=e=>{var t;return{api_host:"https://us.i.posthog.com",ui_host:null,token:"",autocapture:!0,rageclick:!0,cross_subdomain_cookie:function(e){var t=null==e?void 0:e.hostname;if(!O(t))return!1;var i=t.split(".").slice(-2).join(".");for(var s of es)if(i===s)return!1;return!0}(null==c?void 0:c.location),persistence:"localStorage+cookie",persistence_name:"",loaded:nA,save_campaign_params:!0,custom_campaign_params:[],custom_blocked_useragents:[],save_referrer:!0,capture_pageview:"2025-05-24"!==e||"history_change",capture_pageleave:"if_capture_pageview",defaults:null!=e?e:"unset",debug:u&&O(null==u?void 0:u.search)&&-1!==u.search.indexOf("__posthog_debug=true")||!1,cookie_expiration:365,upgrade:!1,disable_session_recording:!1,disable_persistence:!1,disable_web_experiments:!0,disable_surveys:!1,disable_surveys_automatic_display:!1,disable_external_dependency_loading:!1,enable_recording_console_log:void 0,secure_cookie:"https:"===(null==s||null==(t=s.location)?void 0:t.protocol),ip:!1,opt_out_capturing_by_default:!1,opt_out_persistence_by_default:!1,opt_out_useragent_filter:!1,opt_out_capturing_persistence_type:"localStorage",opt_out_capturing_cookie_prefix:null,opt_in_site_apps:!1,property_denylist:[],respect_dnt:!1,sanitize_properties:null,request_headers:{},request_batching:!0,properties_string_max_length:65535,session_recording:{},mask_all_element_attributes:!1,mask_all_text:!1,mask_personal_data_properties:!1,custom_personal_data_properties:[],advanced_disable_flags:!1,advanced_disable_decide:!1,advanced_disable_feature_flags:!1,advanced_disable_feature_flags_on_first_load:!1,advanced_only_evaluate_survey_feature_flags:!1,advanced_enable_surveys:!1,advanced_disable_toolbar_metrics:!1,feature_flag_request_timeout_ms:3e3,surveys_request_timeout_ms:1e4,on_request_error:e=>{var t="Bad HTTP status: "+e.statusCode+" "+e.text;H.error(t)},get_device_id:e=>e,capture_performance:void 0,name:"posthog",bootstrap:{},disable_compression:!1,session_idle_timeout_seconds:1800,person_profiles:"identified_only",before_send:void 0,request_queue_config:{flush_interval_ms:3e3},error_tracking:{},_onCapture:nA}},nN=e=>{var t={};T(e.process_person)||(t.person_profiles=e.process_person),T(e.xhr_headers)||(t.request_headers=e.xhr_headers),T(e.cookie_name)||(t.persistence_name=e.cookie_name),T(e.disable_cookie)||(t.disable_persistence=e.disable_cookie),T(e.store_google)||(t.save_campaign_params=e.store_google),T(e.verbose)||(t.debug=e.verbose);var i=Z({},t,e);return F(e.property_blacklist)&&(T(e.property_denylist)?i.property_denylist=e.property_blacklist:F(e.property_denylist)?i.property_denylist=[...e.property_blacklist,...e.property_denylist]:H.error("Invalid value for property_denylist config: "+e.property_denylist)),i};class nq{constructor(){this.__forceAllowLocalhost=!1}get gs(){return this.__forceAllowLocalhost}set gs(e){H.error("WebPerformanceObserver is deprecated and has no impact on network capture. Use `_forceAllowLocalhostNetworkCapture` on `posthog.sessionRecording`"),this.__forceAllowLocalhost=e}}class nj{get decideEndpointWasHit(){var e,t;return null!=(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&e}get flagsEndpointWasHit(){var e,t;return null!=(e=null==(t=this.featureFlags)?void 0:t.hasLoadedFlags)&&e}constructor(){this.webPerformance=new nq,this.bs=!1,this.version=v.LIB_VERSION,this.ys=new r8,this._calculate_event_properties=this.calculateEventProperties.bind(this),this.config=nL(),this.SentryIntegration=sS,this.sentryIntegration=e=>(function(e,t){var i=sE(e,t);return{name:sw,processEvent:e=>i(e)}})(this,e),this.__request_queue=[],this.__loaded=!1,this.analyticsDefaultEndpoint="/e/",this.ws=!1,this.Ss=null,this.$s=null,this.xs=null,this.featureFlags=new r3(this),this.toolbar=new sF(this),this.scrollManager=new ng(this),this.pageViewManager=new sD(this),this.surveys=new na(this),this.experiments=new nC(this),this.exceptions=new sJ(this),this.rateLimiter=new nl(this),this.requestRouter=new nI(this),this.consent=new tL(this),this.externalIntegrations=new nT(this),this.people={set:(e,t,i)=>{var s=O(e)?{[e]:t}:e;this.setPersonProperties(s),null==i||i({})},set_once:(e,t,i)=>{var s=O(e)?{[e]:t}:e;this.setPersonProperties(void 0,s),null==i||i({})}},this.on("eventCaptured",e=>H.info('send "'+(null==e?void 0:e.event)+'"',e))}init(e,t,i){if(i&&i!==nM){var s,r=null!=(s=nO[i])?s:new nj;return r._init(e,t,i),nO[i]=r,nO[nM][i]=r,r}return this._init(e,t,i)}_init(e,t,i){if(void 0===t&&(t={}),T(e)||A(e))return H.critical("PostHog was initialized without a token. This likely indicates a misconfiguration. Please check the first argument passed to posthog.init()"),this;if(this.__loaded)return H.warn("You have already initialized PostHog! Re-initializing is a no-op"),this;this.__loaded=!0,this.config={},this.ks=t,this.Es=[],t.person_profiles&&(this.$s=t.person_profiles),this.set_config(Z({},nL(t.defaults),nN(t),{name:i,token:e})),this.config.on_xhr_error&&H.error("on_xhr_error is deprecated. Use on_request_error instead"),this.compression=t.disable_compression?void 0:y.GZipJS;var r=this.Is();this.persistence=new r6(this.config,r),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new r6(V({},this.config,{persistence:"sessionStorage"}),r);var n=V({},this.persistence.props),a=V({},this.sessionPersistence.props);if(this.register({$initialization_time:(new Date).toISOString()}),this.Ps=new nh(e=>this.Rs(e),this.config.request_queue_config),this.Ts=new np(this),this.__request_queue=[],this.config.__preview_experimental_cookieless_mode||(this.sessionManager=new nm(this),this.sessionPropsManager=new nv(this,this.sessionManager,this.persistence)),new sP(this).startIfEnabledOrStop(),this.siteApps=new nw(this),null==(o=this.siteApps)||o.init(),this.config.__preview_experimental_cookieless_mode||(this.sessionRecording=new sy(this),this.sessionRecording.startIfEnabledOrStop()),this.config.disable_scroll_properties||this.scrollManager.startMeasuringScrollPosition(),this.autocapture=new tv(this),this.autocapture.startIfEnabled(),this.surveys.loadIfEnabled(),this.heatmaps=new sM(this),this.heatmaps.startIfEnabled(),this.webVitalsAutocapture=new sT(this),this.exceptionObserver=new tG(this),this.exceptionObserver.startIfEnabled(),this.deadClicksAutocapture=new tB(this,tj),this.deadClicksAutocapture.startIfEnabled(),this.historyAutocapture=new ia(this),this.historyAutocapture.startIfEnabled(),v.DEBUG=v.DEBUG||this.config.debug,v.DEBUG&&H.info("Starting in debug mode",{this:this,config:t,thisC:V({},this.config),p:n,s:a}),void 0!==(null==(l=t.bootstrap)?void 0:l.distinctID)){var o,l,c,u,h=this.config.get_device_id(tS()),d=null!=(c=t.bootstrap)&&c.isIdentifiedID?h:t.bootstrap.distinctID;this.persistence.set_property(eL,null!=(u=t.bootstrap)&&u.isIdentifiedID?"identified":"anonymous"),this.register({distinct_id:t.bootstrap.distinctID,$device_id:d})}if(this.Ms()){var p,g,_=Object.keys((null==(p=t.bootstrap)?void 0:p.featureFlags)||{}).filter(e=>{var i;return!(null==(i=t.bootstrap)||null==(i=i.featureFlags)||!i[e])}).reduce((e,i)=>{var s;return e[i]=(null==(s=t.bootstrap)||null==(s=s.featureFlags)?void 0:s[i])||!1,e},{}),f=Object.keys((null==(g=t.bootstrap)?void 0:g.featureFlagPayloads)||{}).filter(e=>_[e]).reduce((e,i)=>{var s,r;return null!=(s=t.bootstrap)&&null!=(s=s.featureFlagPayloads)&&s[i]&&(e[i]=null==(r=t.bootstrap)||null==(r=r.featureFlagPayloads)?void 0:r[i]),e},{});this.featureFlags.receivedFeatureFlags({featureFlags:_,featureFlagPayloads:f})}if(this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:eG,$device_id:null},"");else if(!this.get_distinct_id()){var m=this.config.get_device_id(tS());this.register_once({distinct_id:m,$device_id:m},""),this.persistence.set_property(eL,"anonymous")}return en(s,"onpagehide"in self?"pagehide":"unload",this._handle_unload.bind(this),{passive:!1}),this.toolbar.maybeLoadToolbar(),t.segment?function(e,t){var i=e.config.segment;if(!i)return t();!function(e,t){var i=e.config.segment;if(!i)return t();var s=i=>{var s=()=>i.anonymousId()||tS();e.config.get_device_id=s,i.id()&&(e.register({distinct_id:i.id(),$device_id:s()}),e.persistence.set_property(eL,"identified")),t()},r=i.user();"then"in r&&C(r.then)?r.then(e=>s(e)):s(r)}(e,()=>{var s;i.register((Promise&&Promise.resolve||sb.warn("This browser does not have Promise support, and can not use the segment integration"),s=(t,i)=>{if(!i)return t;t.event.userId||t.event.anonymousId===e.get_distinct_id()||(sb.info("No userId set, resetting PostHog"),e.reset()),t.event.userId&&t.event.userId!==e.get_distinct_id()&&(sb.info("UserId set, identifying with PostHog"),e.identify(t.event.userId));var s=e.calculateEventProperties(i,t.event.properties);return t.event.properties=Object.assign({},s,t.event.properties),t},{name:"PostHog JS",type:"enrichment",version:"1.0.0",isLoaded:()=>!0,load:()=>Promise.resolve(),track:e=>s(e,e.event.event),page:e=>s(e,"$pageview"),identify:e=>s(e,"$identify"),screen:e=>s(e,"$screen")})).then(()=>{t()})})}(this,()=>this.Cs()):this.Cs(),C(this.config._onCapture)&&this.config._onCapture!==nA&&(H.warn("onCapture is deprecated. Please use `before_send` instead"),this.on("eventCaptured",e=>this.config._onCapture(e.event,e))),this.config.ip&&H.warn('The `ip` config option has NO EFFECT AT ALL and has been deprecated. Use a custom transformation or "Discard IP data" project setting instead. See https://posthog.com/tutorials/web-redact-properties#hiding-customer-ip-address for more information.'),this}Re(e){var t,i,s,r,n,a,o,l;if(!c||!c.body)return H.info("document not ready yet, trying again in 500 milliseconds..."),void setTimeout(()=>{this.Re(e)},500);this.compression=void 0,e.supportedCompression&&!this.config.disable_compression&&(this.compression=w(e.supportedCompression,y.GZipJS)?y.GZipJS:w(e.supportedCompression,y.Base64)?y.Base64:void 0),null!=(t=e.analytics)&&t.endpoint&&(this.analyticsDefaultEndpoint=e.analytics.endpoint),this.set_config({person_profiles:this.$s?this.$s:"identified_only"}),null==(i=this.siteApps)||i.onRemoteConfig(e),null==(s=this.sessionRecording)||s.onRemoteConfig(e),null==(r=this.autocapture)||r.onRemoteConfig(e),null==(n=this.heatmaps)||n.onRemoteConfig(e),this.surveys.onRemoteConfig(e),null==(a=this.webVitalsAutocapture)||a.onRemoteConfig(e),null==(o=this.exceptionObserver)||o.onRemoteConfig(e),this.exceptions.onRemoteConfig(e),null==(l=this.deadClicksAutocapture)||l.onRemoteConfig(e)}Cs(){try{this.config.loaded(this)}catch(e){H.critical("`loaded` function failed",e)}this.Fs(),this.config.capture_pageview&&setTimeout(()=>{this.consent.isOptedIn()&&this.Os()},1),new nu(this).load(),this.featureFlags.flags()}Fs(){var e;this.has_opted_out_capturing()||this.config.request_batching&&(null==(e=this.Ps)||e.enable())}_dom_loaded(){this.has_opted_out_capturing()||J(this.__request_queue,e=>this.Rs(e)),this.__request_queue=[],this.Fs()}_handle_unload(){var e,t;this.config.request_batching?(this.As()&&this.capture("$pageleave"),null==(e=this.Ps)||e.unload(),null==(t=this.Ts)||t.unload()):this.As()&&this.capture("$pageleave",null,{transport:"sendBeacon"})}Pe(e){this.__loaded&&(nD?this.__request_queue.push(e):this.rateLimiter.isServerRateLimited(e.batchKey)||(e.transport=e.transport||this.config.api_transport,e.url=sj(e.url,{ip:+!!this.config.ip}),e.headers=V({},this.config.request_headers),e.compression="best-available"===e.compression?this.compression:e.compression,e.fetchOptions=e.fetchOptions||this.config.fetch_options,(e=>{var t,i,s,r=V({},e);r.timeout=r.timeout||6e4,r.url=sj(r.url,{_:(new Date).getTime().toString(),ver:v.LIB_VERSION,compression:r.compression});var n=null!=(t=r.transport)?t:"fetch",a=null!=(i=null==(s=er(sz,e=>e.transport===n))?void 0:s.method)?i:sz[0].method;if(!a)throw Error("No available transport method");a(r)})(V({},e,{callback:t=>{var i,s;this.rateLimiter.checkForLimiting(t),t.statusCode>=400&&(null==(i=(s=this.config).on_request_error)||i.call(s,t)),null==e.callback||e.callback(t)}}))))}Rs(e){this.Ts?this.Ts.retriableRequest(e):this.Pe(e)}_execute_array(e){var t,i=[],s=[],r=[];J(e,e=>{e&&(F(t=e[0])?r.push(e):C(e)?e.call(this):F(e)&&"alias"===t?i.push(e):F(e)&&-1!==t.indexOf("capture")&&C(this[t])?r.push(e):s.push(e))});var n=function(e,t){J(e,function(e){if(F(e[0])){var i=t;K(e,function(e){i=i[e[0]].apply(i,e.slice(1))})}else this[e[0]].apply(this,e.slice(1))},t)};n(i,this),n(s,this),n(r,this)}Ms(){var e,t;return(null==(e=this.config.bootstrap)?void 0:e.featureFlags)&&Object.keys(null==(t=this.config.bootstrap)?void 0:t.featureFlags).length>0||!1}push(e){this._execute_array([e])}capture(e,t,i){var s;if(this.__loaded&&this.persistence&&this.sessionPersistence&&this.Ps){if(!this.consent.isOptedOut())if(!T(e)&&O(e)){if(this.config.opt_out_useragent_filter||!this._is_bot()){var r=null!=i&&i.skip_client_rate_limiting?void 0:this.rateLimiter.clientRateLimitContext();if(null==r||!r.isRateLimited){null!=t&&t.$current_url&&!O(null==t?void 0:t.$current_url)&&(H.error("Invalid `$current_url` property provided to `posthog.capture`. Input must be a string. Ignoring provided value."),null==t||delete t.$current_url),this.sessionPersistence.update_search_keyword(),this.config.save_campaign_params&&this.sessionPersistence.update_campaign_params(),this.config.save_referrer&&this.sessionPersistence.update_referrer_info(),(this.config.save_campaign_params||this.config.save_referrer)&&this.persistence.set_initial_person_info();var n,a,o,l,c=new Date,u=(null==i?void 0:i.timestamp)||c,h=tS(),d={uuid:h,event:e,properties:this.calculateEventProperties(e,t||{},u,h)};r&&(d.properties.$lib_rate_limit_remaining_tokens=r.remainingTokens),(null==i?void 0:i.$set)&&(d.$set=null==i?void 0:i.$set);var p,g,_=this.Ds(null==i?void 0:i.$set_once);if(_&&(d.$set_once=_),(n=d,a=null!=i&&i._noTruncate?null:this.config.properties_string_max_length,o=e=>O(e)&&!M(a)?e.slice(0,a):e,l=new Set,d=function e(t,i){var s;return t!==Object(t)?o?o(t,i):t:l.has(t)?void 0:(l.add(t),F(t)?(s=[],J(t,t=>{s.push(e(t))})):(s={},K(t,(t,i)=>{l.has(t)||(s[i]=e(t,i))})),s)}(n)).timestamp=u,T(null==i?void 0:i.timestamp)||(d.properties.$event_time_override_provided=!0,d.properties.$event_time_override_system_time=c),e===r9.DISMISSED||e===r9.SENT){var v=null==t?void 0:t[ne.SURVEY_ID],f=null==t?void 0:t[ne.SURVEY_ITERATION];localStorage.setItem((g=""+ni+(p={id:v,current_iteration:f}).id,p.current_iteration&&p.current_iteration>0&&(g=""+ni+p.id+"_"+p.current_iteration),g),"true"),d.$set=V({},d.$set,{[ns({id:v,current_iteration:f},e===r9.SENT?"responded":"dismissed")]:!0})}var m=V({},d.properties.$set,d.$set);if(R(m)||this.setPersonPropertiesForFlags(m),!D(this.config.before_send)){var y=this.Ls(d);if(!y)return;d=y}this.ys.emit("eventCaptured",d);var b={method:"POST",url:null!=(s=null==i?void 0:i._url)?s:this.requestRouter.endpointFor("api",this.analyticsDefaultEndpoint),data:d,compression:"best-available",batchKey:null==i?void 0:i._batchKey};return!this.config.request_batching||i&&(null==i||!i._batchKey)||null!=i&&i.send_instantly?this.Rs(b):this.Ps.enqueue(b),d}H.critical("This capture call is ignored due to client rate limiting.")}}else H.error("No event name provided to posthog.capture")}else H.uninitializedWarning("posthog.capture")}Ve(e){return this.on("eventCaptured",t=>e(t.event,t))}calculateEventProperties(e,t,i,r,n){if(i=i||new Date,!this.persistence||!this.sessionPersistence)return t;var a,o=n?void 0:this.persistence.remove_event_timer(e),l=V({},t);if(l.token=this.config.token,l.$config_defaults=this.config.defaults,this.config.__preview_experimental_cookieless_mode&&(l.$cookieless_mode=!0),"$snapshot"===e){var h=V({},this.persistence.properties(),this.sessionPersistence.properties());return l.distinct_id=h.distinct_id,(!O(l.distinct_id)&&!L(l.distinct_id)||A(l.distinct_id))&&H.error("Invalid distinct_id for replay event. This indicates a bug in your implementation"),l}var d,p=function(e,t){if(!g)return{};var i,r,n=e?X([],rD,t||[]):[],[a,o]=function(e){for(var t=0;t<rO.length;t++){var[i,s]=rO[t],r=i.exec(e),n=r&&(C(s)?s(r,e):s);if(n)return n}return["",""]}(g);return Z(ei({$os:a,$os_version:o,$browser:rP(g,navigator.vendor),$device:rA(g),$device_type:(r=rA(g))===s0||r===sQ||"Kobo"===r||"Kindle Fire"===r||r===rb?sX:r===rc||r===rh||r===ru||r===rf?"Console":r===s2?"Wearable":r?sK:"Desktop",$timezone:rW(),$timezone_offset:function(){try{return(new Date).getTimezoneOffset()}catch(e){return}}()}),{$current_url:td(null==u?void 0:u.href,n,rN),$host:null==u?void 0:u.host,$pathname:null==u?void 0:u.pathname,$raw_user_agent:g.length>1e3?g.substring(0,997)+"...":g,$browser_version:rT(g,navigator.vendor),$browser_language:rz(),$browser_language_prefix:"string"==typeof(i=rz())?i.split("-")[0]:void 0,$screen_height:null==s?void 0:s.screen.height,$screen_width:null==s?void 0:s.screen.width,$viewport_height:null==s?void 0:s.innerHeight,$viewport_width:null==s?void 0:s.innerWidth,$lib:"web",$lib_version:v.LIB_VERSION,$insert_id:Math.random().toString(36).substring(2,10)+Math.random().toString(36).substring(2,10),$time:Date.now()/1e3})}(this.config.mask_personal_data_properties,this.config.custom_personal_data_properties);if(this.sessionManager){var{sessionId:_,windowId:f}=this.sessionManager.checkAndGetSessionAndWindowId(n,i.getTime());l.$session_id=_,l.$window_id=f}this.sessionPropsManager&&Z(l,this.sessionPropsManager.getSessionProps());try{this.sessionRecording&&Z(l,this.sessionRecording.sdkDebugProperties),l.$sdk_debug_retry_queue_size=null==(a=this.Ts)?void 0:a.length}catch(e){l.$sdk_debug_error_capturing_properties=String(e)}if(this.requestRouter.region===nk.CUSTOM&&(l.$lib_custom_api_host=this.config.api_host),d="$pageview"!==e||n?"$pageleave"!==e||n?this.pageViewManager.doEvent():this.pageViewManager.doPageLeave(i):this.pageViewManager.doPageView(i,r),l=Z(l,d),"$pageview"===e&&c&&(l.title=c.title),!T(o)){var m=i.getTime()-o;l.$duration=parseFloat((m/1e3).toFixed(3))}g&&this.config.opt_out_useragent_filter&&(l.$browser_type=this._is_bot()?"bot":"browser"),(l=Z({},p,this.persistence.properties(),this.sessionPersistence.properties(),l)).$is_identified=this._isIdentified(),F(this.config.property_denylist)?K(this.config.property_denylist,function(e){delete l[e]}):H.error("Invalid value for property_denylist config: "+this.config.property_denylist+" or property_blacklist config: "+this.config.property_blacklist);var y=this.config.sanitize_properties;y&&(H.error("sanitize_properties is deprecated. Use before_send instead"),l=y(l,e));var b=this.js();return l.$process_person_profile=b,b&&!n&&this.Ns("_calculate_event_properties"),l}Ds(e){if(!this.persistence||!this.js()||this.bs)return e;var t,i=Z({},this.persistence.get_initial_props(),(null==(t=this.sessionPropsManager)?void 0:t.getSetOnceProps())||{},e||{}),s=this.config.sanitize_properties;return s&&(H.error("sanitize_properties is deprecated. Use before_send instead"),i=s(i,"$set_once")),this.bs=!0,R(i)?void 0:i}register(e,t){var i;null==(i=this.persistence)||i.register(e,t)}register_once(e,t,i){var s;null==(s=this.persistence)||s.register_once(e,t,i)}register_for_session(e){var t;null==(t=this.sessionPersistence)||t.register(e)}unregister(e){var t;null==(t=this.persistence)||t.unregister(e)}unregister_for_session(e){var t;null==(t=this.sessionPersistence)||t.unregister(e)}zs(e,t){this.register({[e]:t})}getFeatureFlag(e,t){return this.featureFlags.getFeatureFlag(e,t)}getFeatureFlagPayload(e){var t=this.featureFlags.getFeatureFlagPayload(e);try{return JSON.parse(t)}catch(e){return t}}isFeatureEnabled(e,t){return this.featureFlags.isFeatureEnabled(e,t)}reloadFeatureFlags(){this.featureFlags.reloadFeatureFlags()}updateEarlyAccessFeatureEnrollment(e,t,i){this.featureFlags.updateEarlyAccessFeatureEnrollment(e,t,i)}getEarlyAccessFeatures(e,t,i){return void 0===t&&(t=!1),this.featureFlags.getEarlyAccessFeatures(e,t,i)}on(e,t){return this.ys.on(e,t)}onFeatureFlags(e){return this.featureFlags.onFeatureFlags(e)}onSurveysLoaded(e){return this.surveys.onSurveysLoaded(e)}onSessionId(e){var t,i;return null!=(t=null==(i=this.sessionManager)?void 0:i.onSessionId(e))?t:()=>{}}getSurveys(e,t){void 0===t&&(t=!1),this.surveys.getSurveys(e,t)}getActiveMatchingSurveys(e,t){void 0===t&&(t=!1),this.surveys.getActiveMatchingSurveys(e,t)}renderSurvey(e,t){this.surveys.renderSurvey(e,t)}canRenderSurvey(e){return this.surveys.canRenderSurvey(e)}canRenderSurveyAsync(e,t){return void 0===t&&(t=!1),this.surveys.canRenderSurveyAsync(e,t)}identify(e,t,i){if(!this.__loaded||!this.persistence)return H.uninitializedWarning("posthog.identify");if(L(e)&&(e=e.toString(),H.warn("The first argument to posthog.identify was a number, but it should be a string. It has been converted to a string.")),e)if(["distinct_id","distinctid"].includes(e.toLowerCase()))H.critical('The string "'+e+'" was set in posthog.identify which indicates an error. This ID should be unique to the user and not a hardcoded string.');else if(e!==eG){if(this.Ns("posthog.identify")){var s=this.get_distinct_id();this.register({$user_id:e}),this.get_property("$device_id")||this.register_once({$had_persisted_distinct_id:!0,$device_id:s},""),e!==s&&e!==this.get_property(eo)&&(this.unregister(eo),this.register({distinct_id:e}));var r="anonymous"===(this.persistence.get_property(eL)||"anonymous");e!==s&&r?(this.persistence.set_property(eL,"identified"),this.setPersonPropertiesForFlags(V({},i||{},t||{}),!1),this.capture("$identify",{distinct_id:e,$anon_distinct_id:s},{$set:t||{},$set_once:i||{}}),this.xs=sG(e,t,i),this.featureFlags.setAnonymousDistinctId(s)):(t||i)&&this.setPersonProperties(t,i),e!==s&&(this.reloadFeatureFlags(),this.unregister(eD))}}else H.critical('The string "'+eG+'" was set in posthog.identify which indicates an error. This ID is only used as a sentinel value.');else H.error("Unique user id has not been set in posthog.identify")}setPersonProperties(e,t){if((e||t)&&this.Ns("posthog.setPersonProperties")){var i=sG(this.get_distinct_id(),e,t);this.xs!==i?(this.setPersonPropertiesForFlags(V({},t||{},e||{})),this.capture("$set",{$set:e||{},$set_once:t||{}}),this.xs=i):H.info("A duplicate setPersonProperties call was made with the same properties. It has been ignored.")}}group(e,t,i){if(e&&t){if(this.Ns("posthog.group")){var s=this.getGroups();s[e]!==t&&this.resetGroupPropertiesForFlags(e),this.register({$groups:V({},s,{[e]:t})}),i&&(this.capture("$groupidentify",{$group_type:e,$group_key:t,$group_set:i}),this.setGroupPropertiesForFlags({[e]:i})),s[e]===t||i||this.reloadFeatureFlags()}}else H.error("posthog.group requires a group type and group key")}resetGroups(){this.register({$groups:{}}),this.resetGroupPropertiesForFlags(),this.reloadFeatureFlags()}setPersonPropertiesForFlags(e,t){void 0===t&&(t=!0),this.featureFlags.setPersonPropertiesForFlags(e,t)}resetPersonPropertiesForFlags(){this.featureFlags.resetPersonPropertiesForFlags()}setGroupPropertiesForFlags(e,t){void 0===t&&(t=!0),this.Ns("posthog.setGroupPropertiesForFlags")&&this.featureFlags.setGroupPropertiesForFlags(e,t)}resetGroupPropertiesForFlags(e){this.featureFlags.resetGroupPropertiesForFlags(e)}reset(e){if(H.info("reset"),!this.__loaded)return H.uninitializedWarning("posthog.reset");var t,i,s,r,n=this.get_property("$device_id");if(this.consent.reset(),null==(t=this.persistence)||t.clear(),null==(i=this.sessionPersistence)||i.clear(),this.surveys.reset(),this.featureFlags.reset(),null==(s=this.persistence)||s.set_property(eL,"anonymous"),null==(r=this.sessionManager)||r.resetSessionId(),this.xs=null,this.config.__preview_experimental_cookieless_mode)this.register_once({distinct_id:eG,$device_id:null},"");else{var a=this.config.get_device_id(tS());this.register_once({distinct_id:a,$device_id:e?a:n},"")}this.register({$last_posthog_reset:(new Date).toISOString()},1)}get_distinct_id(){return this.get_property("distinct_id")}getGroups(){return this.get_property("$groups")||{}}get_session_id(){var e,t;return null!=(e=null==(t=this.sessionManager)?void 0:t.checkAndGetSessionAndWindowId(!0).sessionId)?e:""}get_session_replay_url(e){if(!this.sessionManager)return"";var{sessionId:t,sessionStartTimestamp:i}=this.sessionManager.checkAndGetSessionAndWindowId(!0),s=this.requestRouter.endpointFor("ui","/project/"+this.config.token+"/replay/"+t);if(null!=e&&e.withTimestamp&&i){var r,n=null!=(r=e.timestampLookBack)?r:10;if(!i)return s;s+="?t="+Math.max(Math.floor(((new Date).getTime()-i)/1e3)-n,0)}return s}alias(e,t){return e===this.get_property(ea)?(H.critical("Attempting to create alias for existing People user - aborting."),-2):this.Ns("posthog.alias")?(T(t)&&(t=this.get_distinct_id()),e!==t?(this.zs(eo,e),this.capture("$create_alias",{alias:e,distinct_id:t})):(H.warn("alias matches current distinct_id - skipping api call."),this.identify(e),-1)):void 0}set_config(e){var t=V({},this.config);if(P(e)){Z(this.config,nN(e));var i,s,r,n,a,o=this.Is();null==(i=this.persistence)||i.update_config(this.config,t,o),this.sessionPersistence="sessionStorage"===this.config.persistence||"memory"===this.config.persistence?this.persistence:new r6(V({},this.config,{persistence:"sessionStorage"}),o),tC.O()&&"true"===tC.D("ph_debug")&&(this.config.debug=!0),this.config.debug&&(v.DEBUG=!0,H.info("set_config",{config:e,oldConfig:t,newConfig:V({},this.config)})),null==(s=this.sessionRecording)||s.startIfEnabledOrStop(),null==(r=this.autocapture)||r.startIfEnabled(),null==(n=this.heatmaps)||n.startIfEnabled(),this.surveys.loadIfEnabled(),this.Us(),null==(a=this.externalIntegrations)||a.startIfEnabledOrStop()}}startSessionRecording(e){var t,i,s,r,n,a=!0===e,o={sampling:a||!(null==e||!e.sampling),linked_flag:a||!(null==e||!e.linked_flag),url_trigger:a||!(null==e||!e.url_trigger),event_trigger:a||!(null==e||!e.event_trigger)};Object.values(o).some(Boolean)&&(null==(t=this.sessionManager)||t.checkAndGetSessionAndWindowId(),o.sampling&&(null==(i=this.sessionRecording)||i.overrideSampling()),o.linked_flag&&(null==(s=this.sessionRecording)||s.overrideLinkedFlag()),o.url_trigger&&(null==(r=this.sessionRecording)||r.overrideTrigger("url")),o.event_trigger&&(null==(n=this.sessionRecording)||n.overrideTrigger("event"))),this.set_config({disable_session_recording:!1})}stopSessionRecording(){this.set_config({disable_session_recording:!0})}sessionRecordingStarted(){var e;return!(null==(e=this.sessionRecording)||!e.started)}captureException(e,t){var i=Error("PostHog syntheticException");return this.exceptions.sendExceptionEvent(V({},function(e,t){var{error:i,event:s}=e,r={$exception_list:[]},n=i||s;if(tK(n)||tJ(n,"DOMException")){if("stack"in n)r=ii(n,t);else{var a=n.name||(tK(n)?"DOMError":"DOMException"),o=n.message?a+": "+n.message:a;r=is(o,V({},t,{overrideExceptionType:tK(n)?"DOMError":"DOMException",defaultExceptionMessage:o}))}return"code"in n&&(r.$exception_DOMException_code=""+n.code),r}if(tJ(n,"ErrorEvent")&&n.error)return ii(n.error,t);if(tY(n))return ii(n,t);if(tJ(n,"Object")||tV(n))return function(e,t){var i,s,r,n=null==(s=null==t?void 0:t.handled)||s,a=null==(r=null==t?void 0:t.synthetic)||r,o={type:null!=t&&t.overrideExceptionType?t.overrideExceptionType:tV(e)?e.constructor.name:"Error",value:"Non-Error 'exception' captured with keys: "+function(e,t){void 0===t&&(t=40);var i=Object.keys(e);if(i.sort(),!i.length)return"[object has no keys]";for(var s=i.length;s>0;s--){var r=i.slice(0,s).join(", ");if(!(r.length>t))return s===i.length||r.length<=t?r:r.slice(0,t)+"..."}return""}(e),mechanism:{handled:n,synthetic:a}};if(null!=t&&t.syntheticException){var l=ie(null==t?void 0:t.syntheticException,1);l.length&&(o.stacktrace={frames:l,type:"raw"})}return{$exception_list:[o],$exception_level:O(i=e.level)&&!A(i)&&b.indexOf(i)>=0?e.level:"error"}}(n,t);if(T(i)&&O(s)){var l="Error",c=s,u=s.match(t9);return u&&(l=u[1],c=u[2]),is(c,V({},t,{overrideExceptionType:l,defaultExceptionMessage:c}))}return is(n,t)}(e instanceof Error?{error:e,event:e.message}:{event:e},{syntheticException:i}),t))}loadToolbar(e){return this.toolbar.loadToolbar(e)}get_property(e){var t;return null==(t=this.persistence)?void 0:t.props[e]}getSessionProperty(e){var t;return null==(t=this.sessionPersistence)?void 0:t.props[e]}toString(){var e,t=null!=(e=this.config.name)?e:nM;return t!==nM&&(t=nM+"."+t),t}_isIdentified(){var e,t;return"identified"===(null==(e=this.persistence)?void 0:e.get_property(eL))||"identified"===(null==(t=this.sessionPersistence)?void 0:t.get_property(eL))}js(){var e,t;return!("never"===this.config.person_profiles||"identified_only"===this.config.person_profiles&&!this._isIdentified()&&R(this.getGroups())&&(null==(e=this.persistence)||null==(e=e.props)||!e[eo])&&(null==(t=this.persistence)||null==(t=t.props)||!t[ez]))}As(){return!0===this.config.capture_pageleave||"if_capture_pageview"===this.config.capture_pageleave&&(!0===this.config.capture_pageview||"history_change"===this.config.capture_pageview)}createPersonProfile(){this.js()||this.Ns("posthog.createPersonProfile")&&this.setPersonProperties({},{})}Ns(e){return"never"===this.config.person_profiles?(H.error(e+' was called, but process_person is set to "never". This call will be ignored.'),!1):(this.zs(ez,!0),!0)}Is(){var e=this.consent.isOptedOut(),t=this.config.opt_out_persistence_by_default;return this.config.disable_persistence||e&&!!t}Us(){var e,t,i,s,r=this.Is();return(null==(e=this.persistence)?void 0:e.Ae)!==r&&(null==(i=this.persistence)||i.set_disabled(r)),(null==(t=this.sessionPersistence)?void 0:t.Ae)!==r&&(null==(s=this.sessionPersistence)||s.set_disabled(r)),r}opt_in_capturing(e){var t;this.consent.optInOut(!0),this.Us(),(T(null==e?void 0:e.captureEventName)||null!=e&&e.captureEventName)&&this.capture(null!=(t=null==e?void 0:e.captureEventName)?t:"$opt_in",null==e?void 0:e.captureProperties,{send_instantly:!0}),this.config.capture_pageview&&this.Os()}opt_out_capturing(){this.consent.optInOut(!1),this.Us()}has_opted_in_capturing(){return this.consent.isOptedIn()}has_opted_out_capturing(){return this.consent.isOptedOut()}clear_opt_in_out_capturing(){this.consent.reset(),this.Us()}_is_bot(){return l?nx(l,this.config.custom_blocked_useragents):void 0}Os(){c&&("visible"===c.visibilityState?this.ws||(this.ws=!0,this.capture("$pageview",{title:c.title},{send_instantly:!0}),this.Ss&&(c.removeEventListener("visibilitychange",this.Ss),this.Ss=null)):this.Ss||(this.Ss=this.Os.bind(this),en(c,"visibilitychange",this.Ss)))}debug(e){!1===e?(null==s||s.console.log("You've disabled debug mode."),localStorage&&localStorage.removeItem("ph_debug"),this.set_config({debug:!1})):(null==s||s.console.log("You're now in debug mode. All calls to PostHog will be logged in your console.\nYou can disable this with `posthog.debug(false)`."),localStorage&&localStorage.setItem("ph_debug","true"),this.set_config({debug:!0}))}I(){var e,t,i,s,r=this.ks||{};return"advanced_disable_flags"in r?!!r.advanced_disable_flags:!1!==this.config.advanced_disable_flags?!!this.config.advanced_disable_flags:!0===this.config.advanced_disable_decide?(H.warn("Config field 'advanced_disable_decide' is deprecated. Please use 'advanced_disable_flags' instead. The old field will be removed in a future major version."),!0):(t="advanced_disable_decide",i=(e="advanced_disable_flags")in r&&!T(r[e]),s=t in r&&!T(r[t]),i?r[e]:!!s&&(H&&H.warn("Config field '"+t+"' is deprecated. Please use '"+e+"' instead. The old field will be removed in a future major version."),r[t]))}Ls(e){if(D(this.config.before_send))return e;var t=F(this.config.before_send)?this.config.before_send:[this.config.before_send],i=e;for(var s of t){if(D(i=s(i))){var r="Event '"+e.event+"' was rejected in beforeSend function";return j(e.event)?H.warn(r+". This can cause unexpected behavior."):H.info(r),null}i.properties&&!R(i.properties)||H.warn("Event '"+e.event+"' has no properties after beforeSend function, this is likely an error.")}return i}getPageViewId(){var e;return null==(e=this.pageViewManager.ce)?void 0:e.pageViewId}captureTraceFeedback(e,t){this.capture("$ai_feedback",{$ai_trace_id:String(e),$ai_feedback_text:t})}captureTraceMetric(e,t,i){this.capture("$ai_metric",{$ai_trace_id:String(e),$ai_metric_name:t,$ai_metric_value:String(i)})}}!function(e,t){for(var i=0;i<t.length;i++)e.prototype[t[i]]=et(e.prototype[t[i]])}(nj,["identify"]);var nB,nH=(nB=nO[nM]=new nj,function(){function e(){e.done||(e.done=!0,nD=!1,K(nO,function(e){e._dom_loaded()}))}null!=c&&c.addEventListener?"complete"===c.readyState?e():en(c,"DOMContentLoaded",e,{capture:!1}):s&&H.error("Browser doesn't support `document.addEventListener` so PostHog couldn't be initialized")}(),nB)}}]);