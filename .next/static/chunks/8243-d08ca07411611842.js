"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8243],{2564:(e,t,r)=>{r.d(t,{Qg:()=>i,bL:()=>s});var n=r(12115),a=r(63655),o=r(95155),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=n.forwardRef((e,t)=>(0,o.jsx)(a.sG.span,{...e,ref:t,style:{...i,...e.style}}));l.displayName="VisuallyHidden";var s=l},14186:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},17580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},22432:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},54011:(e,t,r)=>{r.d(t,{H4:()=>T,_V:()=>k,bL:()=>C});var n=r(12115),a=r(46081),o=r(39033),i=r(52712),l=r(63655),s=r(49033);function d(){return()=>{}}var u=r(95155),c="Avatar",[p,f]=(0,a.A)(c),[h,v]=p(c),y=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...a}=e,[o,i]=n.useState("idle");return(0,u.jsx)(h,{scope:r,imageLoadingStatus:o,onImageLoadingStatusChange:i,children:(0,u.jsx)(l.sG.span,{...a,ref:t})})});y.displayName=c;var g="AvatarImage",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:a,onLoadingStatusChange:c=()=>{},...p}=e,f=v(g,r),h=function(e,t){let{referrerPolicy:r,crossOrigin:a}=t,o=(0,s.useSyncExternalStore)(d,()=>!0,()=>!1),l=n.useRef(null),u=o?(l.current||(l.current=new window.Image),l.current):null,[c,p]=n.useState(()=>w(u,e));return(0,i.N)(()=>{p(w(u,e))},[u,e]),(0,i.N)(()=>{let e=e=>()=>{p(e)};if(!u)return;let t=e("loaded"),n=e("error");return u.addEventListener("load",t),u.addEventListener("error",n),r&&(u.referrerPolicy=r),"string"==typeof a&&(u.crossOrigin=a),()=>{u.removeEventListener("load",t),u.removeEventListener("error",n)}},[u,a,r]),c}(a,p),y=(0,o.c)(e=>{c(e),f.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==h&&y(h)},[h,y]),"loaded"===h?(0,u.jsx)(l.sG.img,{...p,ref:t,src:a}):null});x.displayName=g;var m="AvatarFallback",b=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:a,...o}=e,i=v(m,r),[s,d]=n.useState(void 0===a);return n.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>d(!0),a);return()=>window.clearTimeout(e)}},[a]),s&&"loaded"!==i.imageLoadingStatus?(0,u.jsx)(l.sG.span,{...o,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=m;var C=y,k=x,T=b},57434:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},72713:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},87489:(e,t,r)=>{r.d(t,{b:()=>d});var n=r(12115),a=r(63655),o=r(95155),i="horizontal",l=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=i,...d}=e,u=(r=s,l.includes(r))?s:i;return(0,o.jsx)(a.sG.div,{"data-orientation":u,...n?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...d,ref:t})});s.displayName="Separator";var d=s},88106:(e,t,r)=>{r.d(t,{Ke:()=>C,R6:()=>b,bL:()=>R});var n=r(12115),a=r(85185),o=r(46081),i=r(5845),l=r(52712),s=r(6101),d=r(63655),u=r(28905),c=r(61285),p=r(95155),f="Collapsible",[h,v]=(0,o.A)(f),[y,g]=h(f),x=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:a,defaultOpen:o,disabled:l,onOpenChange:s,...u}=e,[h,v]=(0,i.i)({prop:a,defaultProp:null!=o&&o,onChange:s,caller:f});return(0,p.jsx)(y,{scope:r,disabled:l,contentId:(0,c.B)(),open:h,onOpenToggle:n.useCallback(()=>v(e=>!e),[v]),children:(0,p.jsx)(d.sG.div,{"data-state":T(h),"data-disabled":l?"":void 0,...u,ref:t})})});x.displayName=f;var m="CollapsibleTrigger",b=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,o=g(m,r);return(0,p.jsx)(d.sG.button,{type:"button","aria-controls":o.contentId,"aria-expanded":o.open||!1,"data-state":T(o.open),"data-disabled":o.disabled?"":void 0,disabled:o.disabled,...n,ref:t,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})});b.displayName=m;var w="CollapsibleContent",C=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,a=g(w,e.__scopeCollapsible);return(0,p.jsx)(u.C,{present:r||a.open,children:e=>{let{present:r}=e;return(0,p.jsx)(k,{...n,ref:t,present:r})}})});C.displayName=w;var k=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:a,children:o,...i}=e,u=g(w,r),[c,f]=n.useState(a),h=n.useRef(null),v=(0,s.s)(t,h),y=n.useRef(0),x=y.current,m=n.useRef(0),b=m.current,C=u.open||c,k=n.useRef(C),R=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=h.current;if(e){R.current=R.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();y.current=t.height,m.current=t.width,k.current||(e.style.transitionDuration=R.current.transitionDuration,e.style.animationName=R.current.animationName),f(a)}},[u.open,a]),(0,p.jsx)(d.sG.div,{"data-state":T(u.open),"data-disabled":u.disabled?"":void 0,id:u.contentId,hidden:!C,...i,ref:v,style:{"--radix-collapsible-content-height":x?"".concat(x,"px"):void 0,"--radix-collapsible-content-width":b?"".concat(b,"px"):void 0,...e.style},children:C&&o})});function T(e){return e?"open":"closed"}var R=x},89613:(e,t,r)=>{r.d(t,{Kq:()=>V,UC:()=>U,ZL:()=>K,bL:()=>W,i3:()=>X,l9:()=>Z});var n=r(12115),a=r(85185),o=r(6101),i=r(46081),l=r(19178),s=r(61285),d=r(35152),u=r(34378),c=r(28905),p=r(63655),f=r(99708),h=r(5845),v=r(2564),y=r(95155),[g,x]=(0,i.A)("Tooltip",[d.Bk]),m=(0,d.Bk)(),b="TooltipProvider",w="tooltip.open",[C,k]=g(b),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:a=300,disableHoverableContent:o=!1,children:i}=e,l=n.useRef(!0),s=n.useRef(!1),d=n.useRef(0);return n.useEffect(()=>{let e=d.current;return()=>window.clearTimeout(e)},[]),(0,y.jsx)(C,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(d.current),l.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.current=!0,a)},[a]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:o,children:i})};T.displayName=b;var R="Tooltip",[E,j]=g(R),L=e=>{let{__scopeTooltip:t,children:r,open:a,defaultOpen:o,onOpenChange:i,disableHoverableContent:l,delayDuration:u}=e,c=k(R,e.__scopeTooltip),p=m(t),[f,v]=n.useState(null),g=(0,s.B)(),x=n.useRef(0),b=null!=l?l:c.disableHoverableContent,C=null!=u?u:c.delayDuration,T=n.useRef(!1),[j,L]=(0,h.i)({prop:a,defaultProp:null!=o&&o,onChange:e=>{e?(c.onOpen(),document.dispatchEvent(new CustomEvent(w))):c.onClose(),null==i||i(e)},caller:R}),M=n.useMemo(()=>j?T.current?"delayed-open":"instant-open":"closed",[j]),N=n.useCallback(()=>{window.clearTimeout(x.current),x.current=0,T.current=!1,L(!0)},[L]),A=n.useCallback(()=>{window.clearTimeout(x.current),x.current=0,L(!1)},[L]),_=n.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{T.current=!0,L(!0),x.current=0},C)},[C,L]);return n.useEffect(()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)},[]),(0,y.jsx)(d.bL,{...p,children:(0,y.jsx)(E,{scope:t,contentId:g,open:j,stateAttribute:M,trigger:f,onTriggerChange:v,onTriggerEnter:n.useCallback(()=>{c.isOpenDelayedRef.current?_():N()},[c.isOpenDelayedRef,_,N]),onTriggerLeave:n.useCallback(()=>{b?A():(window.clearTimeout(x.current),x.current=0)},[A,b]),onOpen:N,onClose:A,disableHoverableContent:b,children:r})})};L.displayName=R;var M="TooltipTrigger",N=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,l=j(M,r),s=k(M,r),u=m(r),c=n.useRef(null),f=(0,o.s)(t,c,l.onTriggerChange),h=n.useRef(!1),v=n.useRef(!1),g=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,y.jsx)(d.Mz,{asChild:!0,...u,children:(0,y.jsx)(p.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:f,onPointerMove:(0,a.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(v.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0))}),onPointerLeave:(0,a.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:(0,a.m)(e.onPointerDown,()=>{l.open&&l.onClose(),h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,a.m)(e.onFocus,()=>{h.current||l.onOpen()}),onBlur:(0,a.m)(e.onBlur,l.onClose),onClick:(0,a.m)(e.onClick,l.onClose)})})});N.displayName=M;var A="TooltipPortal",[_,D]=g(A,{forceMount:void 0}),I=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:a}=e,o=j(A,t);return(0,y.jsx)(_,{scope:t,forceMount:r,children:(0,y.jsx)(c.C,{present:r||o.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:a,children:n})})})};I.displayName=A;var P="TooltipContent",S=n.forwardRef((e,t)=>{let r=D(P,e.__scopeTooltip),{forceMount:n=r.forceMount,side:a="top",...o}=e,i=j(P,e.__scopeTooltip);return(0,y.jsx)(c.C,{present:n||i.open,children:i.disableHoverableContent?(0,y.jsx)(q,{side:a,...o,ref:t}):(0,y.jsx)(O,{side:a,...o,ref:t})})}),O=n.forwardRef((e,t)=>{let r=j(P,e.__scopeTooltip),a=k(P,e.__scopeTooltip),i=n.useRef(null),l=(0,o.s)(t,i),[s,d]=n.useState(null),{trigger:u,onClose:c}=r,p=i.current,{onPointerInTransitChange:f}=a,h=n.useCallback(()=>{d(null),f(!1)},[f]),v=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},a=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),a=Math.abs(t.right-e.x),o=Math.abs(t.left-e.x);switch(Math.min(r,n,a,o)){case o:return"left";case a:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,a),...function(e){let{top:t,right:r,bottom:n,left:a}=e;return[{x:a,y:t},{x:r,y:t},{x:r,y:n},{x:a,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(u&&p){let e=e=>v(e,p),t=e=>v(e,u);return u.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{u.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[u,p,v,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==u?void 0:u.contains(t))||(null==p?void 0:p.contains(t)),a=!function(e,t){let{x:r,y:n}=e,a=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let i=t[e],l=t[o],s=i.x,d=i.y,u=l.x,c=l.y;d>n!=c>n&&r<(u-s)*(n-d)/(c-d)+s&&(a=!a)}return a}(r,s);n?h():a&&(h(),c())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[u,p,s,c,h]),(0,y.jsx)(q,{...e,ref:l})}),[H,B]=g(R,{isInside:!1}),G=(0,f.Dc)("TooltipContent"),q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:a,"aria-label":o,onEscapeKeyDown:i,onPointerDownOutside:s,...u}=e,c=j(P,r),p=m(r),{onClose:f}=c;return n.useEffect(()=>(document.addEventListener(w,f),()=>document.removeEventListener(w,f)),[f]),n.useEffect(()=>{if(c.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(c.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[c.trigger,f]),(0,y.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,y.jsxs)(d.UC,{"data-state":c.stateAttribute,...p,...u,ref:t,style:{...u.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,y.jsx)(G,{children:a}),(0,y.jsx)(H,{scope:r,isInside:!0,children:(0,y.jsx)(v.bL,{id:c.contentId,role:"tooltip",children:o||a})})]})})});S.displayName=P;var z="TooltipArrow",F=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,a=m(r);return B(z,r).isInside?null:(0,y.jsx)(d.i3,{...a,...n,ref:t})});F.displayName=z;var V=T,W=L,Z=N,K=I,U=S,X=F}}]);