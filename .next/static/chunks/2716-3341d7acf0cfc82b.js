"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2716],{6653:(e,r,a)=>{a.d(r,{s:()=>l});var t=a(12115),s=a(83731);function l(){let e=(0,s.sf)(),r=(0,t.useCallback)((r,a)=>{null==e||e.capture("$pageview",{$current_url:r,page_title:a})},[e]),a=(0,t.useCallback)((r,a)=>{null==e||e.capture("button_clicked",{element_name:r,location:a})},[e]),l=(0,t.useCallback)(function(r){let a=!(arguments.length>1)||void 0===arguments[1]||arguments[1];null==e||e.capture("form_submitted",{form_name:r,success:a})},[e]),n=(0,t.useCallback)((r,a)=>{null==e||e.capture("search_performed",{search_term:r,results_count:a})},[e]),o=(0,t.useCallback)(r=>{null==e||e.capture("user_signed_up",{method:r}),null==e||e.identify()},[e]),i=(0,t.useCallback)(r=>{null==e||e.capture("user_logged_in",{method:r})},[e]),c=(0,t.useCallback)(r=>{null==e||e.capture("project_created",{project_type:r})},[e]),u=(0,t.useCallback)((r,a)=>{null==e||e.capture("feature_used",{feature_name:r,context:a})},[e]),d=(0,t.useCallback)(function(r,a){let t=arguments.length>2&&void 0!==arguments[2]&&arguments[2];null==e||e.capture("error_occurred",{error_message:r,context:a,fatal:t})},[e]),m=(0,t.useCallback)((r,a,t)=>{null==e||e.capture("timing_measured",{timing_name:r,timing_value:a,category:t||"performance"})},[e]),p=(0,t.useCallback)((r,a)=>{null==e||e.capture(r,a)},[e]);return{trackPageView:r,trackClick:a,trackFormSubmit:l,trackSearch:n,trackSignUp:o,trackLogin:i,trackProjectCreated:c,trackFeatureUsed:u,trackError:d,trackTiming:m,trackCustomEvent:p,identifyUser:(0,t.useCallback)((r,a)=>{null==e||e.identify(r,a)},[e]),setUserProperties:(0,t.useCallback)(r=>{null==e||e.setPersonProperties(r)},[e])}}},22716:(e,r,a)=>{a.d(r,{j:()=>x});var t=a(95155),s=a(26126),l=a(30285),n=a(6653),o=a(90786),i=a(14989),c=a(80733),u=a(53311),d=a(92138),m=a(71539),p=a(17580),g=a(16785),h=a(35695),b=a(12115);function x(e){let{variant:r,title:a,subtitle:x,showWelcomeBadge:v=!1,showQuickActions:f=!1,showFeatureBadges:j=!1}=e,{isSignedIn:_}=(0,o.P)(),{trackClick:k,trackCustomEvent:y}=(0,n.s)(),N=(0,h.useRouter)(),[w,C]=(0,b.useState)(0),[E,S]=(0,b.useState)(""),[A,P]=(0,b.useState)(!0),[T,z]=(0,b.useState)(""),[F,I]=(0,b.useState)(!1),{setIdeaText:W,startQuestionnaire:$,setCurrentProgress:U,setTotalSteps:q,incrementCompletedSteps:D,setCurrentStep:G,setCreatedProject:R}=(0,c.h)(),{addEventListener:B,removeEventListener:H}=(0,i.a8)(),J=["share your business idea & I'll help bring it into reality","Describe your next project idea","What do you want to build?","What's on your mind?"];(0,b.useEffect)(()=>{let e,r=J[w];return A?e=E.length<r.length?setTimeout(()=>{S(r.slice(0,E.length+1))},50):setTimeout(()=>{P(!1)},500):E.length>0?e=setTimeout(()=>{S(E.slice(0,-1))},25):(C(e=>(e+1)%J.length),P(!0)),()=>clearTimeout(e)},[E,A,w,J]),(0,b.useEffect)(()=>{if("dashboard"!==r)return;let e=e=>{let{type:r,data:a}=e.detail;switch(r){case"setup":q(a.totalSteps);break;case"progress":U(a);break;case"step_complete":D();break;case"complete":R({id:a.projectId,name:a.name,description:a.description}),G("completed"),I(!1);break;case"error":console.error("Project creation error:",a.error),I(!1)}};return B(e),()=>H(e)},[r,B,H,U,q,D,R,G]);let K=(e,a)=>{"landing"===r&&(k("quick-action-badge","hero-section"),y("badge_clicked",{badge_type:a,location:"hero-section"})),z(e)},L="dashboard"===r;return(0,t.jsxs)("div",{className:"w-full max-w-sm mx-auto",children:[(a||x||v)&&(0,t.jsxs)("div",{className:"mb-8 text-center",children:[v&&(0,t.jsx)("div",{className:"flex items-center justify-center gap-2 mb-4",children:(0,t.jsxs)(s.E,{variant:"secondary",className:"bg-[#166534]/10 text-[#166534] border-[#166534]/20",children:[(0,t.jsx)(u.A,{className:"w-3 h-3 mr-1"}),"Welcome back"]})}),a&&(0,t.jsx)("h1",{className:"text-xl md:text-2xl lg:text-4xl font-regular text-foreground mb-6 leading-tight",children:a}),x&&(0,t.jsx)("p",{className:"text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-2xl mx-auto",children:x})]}),(0,t.jsxs)("div",{className:"group relative ".concat(L?"bg-[#166534]/5 hover:bg-[#166534]/8":"bg-card/80"," backdrop-blur border rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300"),children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br ".concat(L?"from-[#166534]/8 via-transparent to-[#22c55e]/8":"from-[var(--primary)]/5 via-transparent to-[var(--primary-light)]/5"," pointer-events-none")}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("textarea",{value:T,onChange:e=>{z(e.target.value),"landing"===r&&1===e.target.value.length&&y("project_input_started",{location:"hero-section"})},onFocus:()=>{"landing"===r&&y("project_input_focused",{location:"hero-section"})},placeholder:E,className:"w-full px-4 py-3 pr-16 rounded-lg ".concat(L?"bg-[#166534]/5 hover:bg-[#166534]/10":"bg-background/50"," backdrop-blur text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 ").concat(L?"focus:ring-[#26F000]/40":"focus:ring-[var(--primary)]/20 focus:border-[var(--primary)]"," resize-none min-h-[80px] transition-all duration-300 hover:shadow-lg relative z-10"),rows:3,disabled:F}),(0,t.jsx)(l.$,{onClick:()=>{T.trim()&&("dashboard"===r?(I(!0),W(T),$(),N.push("/projects/create")):(k("generate-project-button","hero-section"),y("project_generation_attempted",{user_signed_in:_,project_input_length:T.length,has_project_input:T.trim().length>0,redirect_destination:_?"dashboard":"register"}),_&&T.trim()?(W(T),$(),N.push("/projects/create")):_?N.push("/user-dashboard"):N.push("/auth/register")))},disabled:!T.trim()||F,className:"absolute right-3 bottom-4 ".concat(L?"bg-[#CEFFC5] hover:bg-[#26F000] text-black hover:text-black":"bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]"," shadow-lg z-20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300"),size:"icon",children:F?(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,t.jsx)(d.A,{className:"w-4 h-4"})})]}),j&&(0,t.jsxs)("div",{className:"flex items-center justify-center gap-4 text-xs text-muted-foreground mt-4",children:[(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),"AI-powered insights"]}),(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),"Smart project planning"]}),(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-pulse"}),"Team collaboration"]})]}),f&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 justify-center mt-4",children:[(0,t.jsxs)(s.E,{variant:"outline",className:"text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer",onClick:()=>K("Create a project tracker that manages team tasks and deadlines","project_tracker"),children:[(0,t.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Project tracker"]}),(0,t.jsxs)(s.E,{variant:"outline",className:"text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer",onClick:()=>K("Create a team collaboration platform for better communication","team_collaboration"),children:[(0,t.jsx)(p.A,{className:"w-3 h-3 mr-1"}),"Team collaboration"]}),(0,t.jsxs)(s.E,{variant:"outline",className:"text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer",onClick:()=>K("Create a goal management system to track objectives and milestones","goal_management"),children:[(0,t.jsx)(g.A,{className:"w-3 h-3 mr-1"}),"Goal management"]})]})]})]})]})}}}]);