(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[550],{16:(e,t,r)=>{"use strict";r.d(t,{F:()=>i,h:()=>s});let n="DYNAMIC_SERVER_USAGE";class i extends Error{constructor(e){super("Dynamic server usage: "+e),this.description=e,this.digest=n}}function s(e){return"object"==typeof e&&null!==e&&"digest"in e&&"string"==typeof e.digest&&e.digest===n}},35:(e,t)=>{"use strict";var r={H:null,A:null};function n(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var r=2;r<arguments.length;r++)t+="&args[]="+encodeURIComponent(arguments[r])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=Array.isArray,s=Symbol.for("react.transitional.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),h=Symbol.for("react.memo"),p=Symbol.for("react.lazy"),f=Symbol.iterator,g=Object.prototype.hasOwnProperty,m=Object.assign;function y(e,t,r,n,i,a){return{$$typeof:s,type:e,key:t,ref:void 0!==(r=a.ref)?r:null,props:a}}function b(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var _=/\/+/g;function v(e,t){var r,n;return"object"==typeof e&&null!==e&&null!=e.key?(r=""+e.key,n={"=":"=0",":":"=2"},"$"+r.replace(/[=:]/g,function(e){return n[e]})):t.toString(36)}function w(){}function k(e,t,r){if(null==e)return e;var o=[],l=0;return!function e(t,r,o,l,c){var u,d,h,g=typeof t;("undefined"===g||"boolean"===g)&&(t=null);var m=!1;if(null===t)m=!0;else switch(g){case"bigint":case"string":case"number":m=!0;break;case"object":switch(t.$$typeof){case s:case a:m=!0;break;case p:return e((m=t._init)(t._payload),r,o,l,c)}}if(m)return c=c(t),m=""===l?"."+v(t,0):l,i(c)?(o="",null!=m&&(o=m.replace(_,"$&/")+"/"),e(c,r,o,"",function(e){return e})):null!=c&&(b(c)&&(u=c,d=o+(null==c.key||t&&t.key===c.key?"":(""+c.key).replace(_,"$&/")+"/")+m,c=y(u.type,d,void 0,void 0,void 0,u.props)),r.push(c)),1;m=0;var k=""===l?".":l+":";if(i(t))for(var S=0;S<t.length;S++)g=k+v(l=t[S],S),m+=e(l,r,o,g,c);else if("function"==typeof(S=null===(h=t)||"object"!=typeof h?null:"function"==typeof(h=f&&h[f]||h["@@iterator"])?h:null))for(t=S.call(t),S=0;!(l=t.next()).done;)g=k+v(l=l.value,S++),m+=e(l,r,o,g,c);else if("object"===g){if("function"==typeof t.then)return e(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(w,w):(e.status="pending",e.then(function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)},function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)})),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(t),r,o,l,c);throw Error(n(31,"[object Object]"===(r=String(t))?"object with keys {"+Object.keys(t).join(", ")+"}":r))}return m}(e,o,"","",function(e){return t.call(r,e,l++)}),o}function S(e){if(-1===e._status){var t=e._result;(t=t()).then(function(t){(0===e._status||-1===e._status)&&(e._status=1,e._result=t)},function(t){(0===e._status||-1===e._status)&&(e._status=2,e._result=t)}),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}function E(){return new WeakMap}function T(){return{s:0,v:void 0,o:null,p:null}}t.Children={map:k,forEach:function(e,t,r){k(e,function(){t.apply(this,arguments)},r)},count:function(e){var t=0;return k(e,function(){t++}),t},toArray:function(e){return k(e,function(e){return e})||[]},only:function(e){if(!b(e))throw Error(n(143));return e}},t.Fragment=o,t.Profiler=c,t.StrictMode=l,t.Suspense=d,t.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=r,t.cache=function(e){return function(){var t=r.A;if(!t)return e.apply(null,arguments);var n=t.getCacheForType(E);void 0===(t=n.get(e))&&(t=T(),n.set(e,t)),n=0;for(var i=arguments.length;n<i;n++){var s=arguments[n];if("function"==typeof s||"object"==typeof s&&null!==s){var a=t.o;null===a&&(t.o=a=new WeakMap),void 0===(t=a.get(s))&&(t=T(),a.set(s,t))}else null===(a=t.p)&&(t.p=a=new Map),void 0===(t=a.get(s))&&(t=T(),a.set(s,t))}if(1===t.s)return t.v;if(2===t.s)throw t.v;try{var o=e.apply(null,arguments);return(n=t).s=1,n.v=o}catch(e){throw(o=t).s=2,o.v=e,e}}},t.captureOwnerStack=function(){return null},t.cloneElement=function(e,t,r){if(null==e)throw Error(n(267,e));var i=m({},e.props),s=e.key,a=void 0;if(null!=t)for(o in void 0!==t.ref&&(a=void 0),void 0!==t.key&&(s=""+t.key),t)g.call(t,o)&&"key"!==o&&"__self"!==o&&"__source"!==o&&("ref"!==o||void 0!==t.ref)&&(i[o]=t[o]);var o=arguments.length-2;if(1===o)i.children=r;else if(1<o){for(var l=Array(o),c=0;c<o;c++)l[c]=arguments[c+2];i.children=l}return y(e.type,s,void 0,void 0,a,i)},t.createElement=function(e,t,r){var n,i={},s=null;if(null!=t)for(n in void 0!==t.key&&(s=""+t.key),t)g.call(t,n)&&"key"!==n&&"__self"!==n&&"__source"!==n&&(i[n]=t[n]);var a=arguments.length-2;if(1===a)i.children=r;else if(1<a){for(var o=Array(a),l=0;l<a;l++)o[l]=arguments[l+2];i.children=o}if(e&&e.defaultProps)for(n in a=e.defaultProps)void 0===i[n]&&(i[n]=a[n]);return y(e,s,void 0,void 0,null,i)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=b,t.lazy=function(e){return{$$typeof:p,_payload:{_status:-1,_result:e},_init:S}},t.memo=function(e,t){return{$$typeof:h,type:e,compare:void 0===t?null:t}},t.use=function(e){return r.H.use(e)},t.useCallback=function(e,t){return r.H.useCallback(e,t)},t.useDebugValue=function(){},t.useId=function(){return r.H.useId()},t.useMemo=function(e,t){return r.H.useMemo(e,t)},t.version="19.2.0-canary-3fbfb9ba-20250409"},58:(e,t,r)=>{"use strict";r.d(t,{xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}},115:(e,t,r)=>{"use strict";r.d(t,{XN:()=>i,FP:()=>n});let n=(0,r(58).xl)();function i(e){let t=n.getStore();switch(!t&&function(e){throw Object.defineProperty(Error(`\`${e}\` was called outside a request scope. Read more: https://nextjs.org/docs/messages/next-dynamic-api-wrong-context`),"__NEXT_ERROR_CODE",{value:"E251",enumerable:!1,configurable:!0})}(e),t.type){case"request":default:return t;case"prerender":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside a prerender. This is a bug in Next.js.`),"__NEXT_ERROR_CODE",{value:"E401",enumerable:!1,configurable:!0});case"cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside "use cache". Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E37",enumerable:!1,configurable:!0});case"unstable-cache":throw Object.defineProperty(Error(`\`${e}\` cannot be called inside unstable_cache. Call it outside and pass an argument instead. Read more: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E69",enumerable:!1,configurable:!0})}}},159:(e,t,r)=>{"use strict";r.d(t,{RM:()=>s,s8:()=>i});let n=new Set(Object.values({NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401})),i="NEXT_HTTP_ERROR_FALLBACK";function s(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===i&&n.has(Number(r))}},167:(e,t,r)=>{"use strict";r.d(t,{nJ:()=>i});var n=r(821);function i(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,i]=t,s=t.slice(2,-2).join(";"),a=Number(t.at(-2));return"NEXT_REDIRECT"===r&&("replace"===i||"push"===i)&&"string"==typeof s&&!isNaN(a)&&a in n.Q}},199:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var n=r(159),i=r(167);function s(e){return(0,i.nJ)(e)||(0,n.RM)(e)}},201:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return a},withRequest:function(){return s}});let n=new(r(521)).AsyncLocalStorage;function i(e,t){let r=t.header(e,"next-test-proxy-port");if(!r)return;let n=t.url(e);return{url:n,proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function s(e,t,r){let s=i(e,t);return s?n.run(s,r):r()}function a(e,t){let r=n.getStore();return r||(e&&t?i(e,t):void 0)}},221:(e,t,r)=>{"use strict";r.d(t,{headers:()=>_}),r(818),r(725);var n=r(535),i=r(115),s=r(557),a=r(602),o=r(801),l=r(815);let c={current:null},u="function"==typeof l.cache?l.cache:e=>e,d=console.warn;function h(e){return function(...t){d(e(...t))}}u(e=>{try{d(c.current)}finally{c.current=null}});var p=r(335);let f=new WeakMap,g=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})});function m(){return this.getAll().map(e=>[e.name,e]).values()}function y(e){for(let e of this.getAll())this.delete(e.name);return e}var b=r(381);function _(){let e=n.J.getStore(),t=i.FP.getStore();if(e){if(t&&"after"===t.phase&&!(0,p.iC)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return w(b.o.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new a.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,l=t;let n=v.get(l);if(n)return n;let i=(0,o.W)(l.renderSignal,"`headers()`");return v.set(l,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${k(arguments[0])}, ...)\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},delete:{value:function(){let e=`\`headers().delete(${k(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},get:{value:function(){let e=`\`headers().get(${k(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},has:{value:function(){let e=`\`headers().has(${k(arguments[0])})\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},set:{value:function(){let e=`\`headers().set(${k(arguments[0])}, ...)\``,t=E(r,e);(0,s.t3)(r,e,t,l)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=E(r,e);(0,s.t3)(r,e,t,l)}},keys:{value:function(){let e="`headers().keys()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},values:{value:function(){let e="`headers().values()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},entries:{value:function(){let e="`headers().entries()`",t=E(r,e);(0,s.t3)(r,e,t,l)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=E(r,e);(0,s.t3)(r,e,t,l)}}}),i}else"prerender-ppr"===t.type?(0,s.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,s.xI)("headers",e,t);(0,s.Pk)(e,t)}return w((0,i.XN)("headers").headers)}let v=new WeakMap;function w(e){let t=v.get(e);if(t)return t;let r=Promise.resolve(e);return v.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function k(e){return"string"==typeof e?`'${e}'`:"..."}let S=h(E);function E(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function T(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return x(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return x(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return C(null);default:return t}}function x(e,t){let r,n=O.get(T);return n||(r=C(e),O.set(e,r),r)}r(16);let O=new WeakMap;function C(e){let t=new R(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class R{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){I("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){I("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let P=h(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function I(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},280:(e,t,r)=>{var n;(()=>{var i={226:function(i,s){!function(a,o){"use strict";var l="function",c="undefined",u="object",d="string",h="major",p="model",f="name",g="type",m="vendor",y="version",b="architecture",_="console",v="mobile",w="tablet",k="smarttv",S="wearable",E="embedded",T="Amazon",x="Apple",O="ASUS",C="BlackBerry",R="Browser",P="Chrome",I="Firefox",A="Google",N="Huawei",U="Microsoft",M="Motorola",j="Opera",D="Samsung",L="Sharp",q="Sony",B="Xiaomi",H="Zebra",$="Facebook",z="Chromium OS",K="Mac OS",J=function(e,t){var r={};for(var n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},W=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},F=function(e,t){return typeof e===d&&-1!==V(t).indexOf(V(e))},V=function(e){return e.toLowerCase()},G=function(e,t){if(typeof e===d)return e=e.replace(/^\s\s*/,""),typeof t===c?e:e.substring(0,350)},X=function(e,t){for(var r,n,i,s,a,c,d=0;d<t.length&&!a;){var h=t[d],p=t[d+1];for(r=n=0;r<h.length&&!a&&h[r];)if(a=h[r++].exec(e))for(i=0;i<p.length;i++)c=a[++n],typeof(s=p[i])===u&&s.length>0?2===s.length?typeof s[1]==l?this[s[0]]=s[1].call(this,c):this[s[0]]=s[1]:3===s.length?typeof s[1]!==l||s[1].exec&&s[1].test?this[s[0]]=c?c.replace(s[1],s[2]):void 0:this[s[0]]=c?s[1].call(this,c,s[2]):void 0:4===s.length&&(this[s[0]]=c?s[3].call(this,c.replace(s[1],s[2])):o):this[s]=c||o;d+=2}},Q=function(e,t){for(var r in t)if(typeof t[r]===u&&t[r].length>0){for(var n=0;n<t[r].length;n++)if(F(t[r][n],e))return"?"===r?o:r}else if(F(t[r],e))return"?"===r?o:r;return e},Y={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},Z={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[f,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[f,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[f,y],[/opios[\/ ]+([\w\.]+)/i],[y,[f,j+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[f,j]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[f,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[f,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[f,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[f,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[f,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[f,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[f,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[f,/(.+)/,"$1 Secure "+R],y],[/\bfocus\/([\w\.]+)/i],[y,[f,I+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[f,j+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[f,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[f,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[f,j+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[f,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[y,[f,I]],[/\bqihu|(qi?ho?o?|360)browser/i],[[f,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[f,/(.+)/,"$1 "+R],y],[/(comodo_dragon)\/([\w\.]+)/i],[[f,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[f,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[f],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[f,$],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[f,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[f,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[f,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[f,P+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[f,P+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[f,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[f,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[f,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,f],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[f,[y,Q,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[f,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[f,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[f,I+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[f,y],[/(cobalt)\/([\w\.]+)/i],[f,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[b,"amd64"]],[/(ia32(?=;))/i],[[b,V]],[/((?:i[346]|x)86)[;\)]/i],[[b,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[b,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[b,"armhf"]],[/windows (ce|mobile); ppc;/i],[[b,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[b,/ower/,"",V]],[/(sun4\w)[;\)]/i],[[b,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[b,V]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[p,[m,D],[g,w]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[p,[m,D],[g,v]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[p,[m,x],[g,v]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[p,[m,x],[g,w]],[/(macintosh);/i],[p,[m,x]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[p,[m,L],[g,v]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[p,[m,N],[g,w]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[p,[m,N],[g,v]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[p,/_/g," "],[m,B],[g,v]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[p,/_/g," "],[m,B],[g,w]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[p,[m,"OPPO"],[g,v]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[p,[m,"Vivo"],[g,v]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[p,[m,"Realme"],[g,v]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[p,[m,M],[g,v]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[p,[m,M],[g,w]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[p,[m,"LG"],[g,w]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[p,[m,"LG"],[g,v]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[p,[m,"Lenovo"],[g,w]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[p,/_/g," "],[m,"Nokia"],[g,v]],[/(pixel c)\b/i],[p,[m,A],[g,w]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[p,[m,A],[g,v]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[p,[m,q],[g,v]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[p,"Xperia Tablet"],[m,q],[g,w]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[p,[m,"OnePlus"],[g,v]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[p,[m,T],[g,w]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[p,/(.+)/g,"Fire Phone $1"],[m,T],[g,v]],[/(playbook);[-\w\),; ]+(rim)/i],[p,m,[g,w]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[p,[m,C],[g,v]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[p,[m,O],[g,w]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[p,[m,O],[g,v]],[/(nexus 9)/i],[p,[m,"HTC"],[g,w]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[m,[p,/_/g," "],[g,v]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[p,[m,"Acer"],[g,w]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[p,[m,"Meizu"],[g,v]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[m,p,[g,v]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[m,p,[g,w]],[/(surface duo)/i],[p,[m,U],[g,w]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[p,[m,"Fairphone"],[g,v]],[/(u304aa)/i],[p,[m,"AT&T"],[g,v]],[/\bsie-(\w*)/i],[p,[m,"Siemens"],[g,v]],[/\b(rct\w+) b/i],[p,[m,"RCA"],[g,w]],[/\b(venue[\d ]{2,7}) b/i],[p,[m,"Dell"],[g,w]],[/\b(q(?:mv|ta)\w+) b/i],[p,[m,"Verizon"],[g,w]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[p,[m,"Barnes & Noble"],[g,w]],[/\b(tm\d{3}\w+) b/i],[p,[m,"NuVision"],[g,w]],[/\b(k88) b/i],[p,[m,"ZTE"],[g,w]],[/\b(nx\d{3}j) b/i],[p,[m,"ZTE"],[g,v]],[/\b(gen\d{3}) b.+49h/i],[p,[m,"Swiss"],[g,v]],[/\b(zur\d{3}) b/i],[p,[m,"Swiss"],[g,w]],[/\b((zeki)?tb.*\b) b/i],[p,[m,"Zeki"],[g,w]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[m,"Dragon Touch"],p,[g,w]],[/\b(ns-?\w{0,9}) b/i],[p,[m,"Insignia"],[g,w]],[/\b((nxa|next)-?\w{0,9}) b/i],[p,[m,"NextBook"],[g,w]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[m,"Voice"],p,[g,v]],[/\b(lvtel\-)?(v1[12]) b/i],[[m,"LvTel"],p,[g,v]],[/\b(ph-1) /i],[p,[m,"Essential"],[g,v]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[p,[m,"Envizen"],[g,w]],[/\b(trio[-\w\. ]+) b/i],[p,[m,"MachSpeed"],[g,w]],[/\btu_(1491) b/i],[p,[m,"Rotor"],[g,w]],[/(shield[\w ]+) b/i],[p,[m,"Nvidia"],[g,w]],[/(sprint) (\w+)/i],[m,p,[g,v]],[/(kin\.[onetw]{3})/i],[[p,/\./g," "],[m,U],[g,v]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[p,[m,H],[g,w]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[p,[m,H],[g,v]],[/smart-tv.+(samsung)/i],[m,[g,k]],[/hbbtv.+maple;(\d+)/i],[[p,/^/,"SmartTV"],[m,D],[g,k]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[m,"LG"],[g,k]],[/(apple) ?tv/i],[m,[p,x+" TV"],[g,k]],[/crkey/i],[[p,P+"cast"],[m,A],[g,k]],[/droid.+aft(\w)( bui|\))/i],[p,[m,T],[g,k]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[p,[m,L],[g,k]],[/(bravia[\w ]+)( bui|\))/i],[p,[m,q],[g,k]],[/(mitv-\w{5}) bui/i],[p,[m,B],[g,k]],[/Hbbtv.*(technisat) (.*);/i],[m,p,[g,k]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[m,G],[p,G],[g,k]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[g,k]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[m,p,[g,_]],[/droid.+; (shield) bui/i],[p,[m,"Nvidia"],[g,_]],[/(playstation [345portablevi]+)/i],[p,[m,q],[g,_]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[p,[m,U],[g,_]],[/((pebble))app/i],[m,p,[g,S]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[p,[m,x],[g,S]],[/droid.+; (glass) \d/i],[p,[m,A],[g,S]],[/droid.+; (wt63?0{2,3})\)/i],[p,[m,H],[g,S]],[/(quest( 2| pro)?)/i],[p,[m,$],[g,S]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[m,[g,E]],[/(aeobc)\b/i],[p,[m,T],[g,E]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[p,[g,v]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[p,[g,w]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[g,w]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[g,v]],[/(android[-\w\. ]{0,9});.+buil/i],[p,[m,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[f,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[f,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[f,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,f]],os:[[/microsoft (windows) (vista|xp)/i],[f,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[f,[y,Q,Y]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,"Windows"],[y,Q,Y]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[f,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[f,K],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,f],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[f,y],[/\(bb(10);/i],[y,[f,C]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[f,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[f,I+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[f,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[f,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[f,P+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[f,z],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[f,y],[/(sunos) ?([\w\.\d]*)/i],[[f,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[f,y]]},ee=function(e,t){if(typeof e===u&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof a!==c&&a.navigator?a.navigator:o,n=e||(r&&r.userAgent?r.userAgent:""),i=r&&r.userAgentData?r.userAgentData:o,s=t?J(Z,t):Z,_=r&&r.userAgent==n;return this.getBrowser=function(){var e,t={};return t[f]=o,t[y]=o,X.call(t,n,s.browser),t[h]=typeof(e=t[y])===d?e.replace(/[^\d\.]/g,"").split(".")[0]:o,_&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[f]="Brave"),t},this.getCPU=function(){var e={};return e[b]=o,X.call(e,n,s.cpu),e},this.getDevice=function(){var e={};return e[m]=o,e[p]=o,e[g]=o,X.call(e,n,s.device),_&&!e[g]&&i&&i.mobile&&(e[g]=v),_&&"Macintosh"==e[p]&&r&&typeof r.standalone!==c&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[p]="iPad",e[g]=w),e},this.getEngine=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,s.engine),e},this.getOS=function(){var e={};return e[f]=o,e[y]=o,X.call(e,n,s.os),_&&!e[f]&&i&&"Unknown"!=i.platform&&(e[f]=i.platform.replace(/chrome os/i,z).replace(/macos/i,K)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return n},this.setUA=function(e){return n=typeof e===d&&e.length>350?G(e,350):e,this},this.setUA(n),this};ee.VERSION="1.0.35",ee.BROWSER=W([f,y,h]),ee.CPU=W([b]),ee.DEVICE=W([p,m,g,_,v,k,w,S,E]),ee.ENGINE=ee.OS=W([f,y]),typeof s!==c?(i.exports&&(s=i.exports=ee),s.UAParser=ee):r.amdO?void 0===(n=(function(){return ee}).call(t,r,t,e))||(e.exports=n):typeof a!==c&&(a.UAParser=ee);var et=typeof a!==c&&(a.jQuery||a.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},s={};function a(e){var t=s[e];if(void 0!==t)return t.exports;var r=s[e]={exports:{}},n=!0;try{i[e].call(r.exports,r,r.exports,a),n=!1}finally{n&&delete s[e]}return r.exports}a.ab="//",e.exports=a(226)})()},335:(e,t,r)=>{"use strict";r.d(t,{iC:()=>i}),r(602);var n=r(427);function i(){let e=n.Z.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},356:e=>{"use strict";e.exports=require("node:buffer")},381:(e,t,r)=>{"use strict";r.d(t,{o:()=>s});var n=r(716);class i extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new i}}class s extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return n.l.get(t,r,i);let s=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===s);if(void 0!==a)return n.l.get(t,a,i)},set(t,r,i,s){if("symbol"==typeof r)return n.l.set(t,r,i,s);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return n.l.set(t,o??r,i,s)},has(t,r){if("symbol"==typeof r)return n.l.has(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==s&&n.l.has(t,s)},deleteProperty(t,r){if("symbol"==typeof r)return n.l.deleteProperty(t,r);let i=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===s||n.l.deleteProperty(t,s)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return i.callable;default:return n.l.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new s(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},427:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});let n=(0,r(620).xl)()},521:e=>{"use strict";e.exports=require("node:async_hooks")},535:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});let n=(0,r(58).xl)()},552:(e,t,r)=>{"use strict";var n=r(356).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return s}});let i=r(201),s={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function a(e,t){let{url:r,method:i,headers:s,body:a,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}=t;return{testData:e,api:"fetch",request:{url:r,method:i,headers:[...Array.from(s),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:a?n.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:c,mode:u,redirect:d,referrer:h,referrerPolicy:p}}}async function o(e,t){let r=(0,i.getTestReqInfo)(t,s);if(!r)return e(t);let{testData:o,proxyPort:l}=r,c=await a(o,t),u=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(c),next:{internal:!0}});if(!u.ok)throw Object.defineProperty(Error(`Proxy request failed: ${u.status}`),"__NEXT_ERROR_CODE",{value:"E146",enumerable:!1,configurable:!0});let d=await u.json(),{api:h}=d;switch(h){case"continue":return e(t);case"abort":case"unhandled":throw Object.defineProperty(Error(`Proxy request aborted [${t.method} ${t.url}]`),"__NEXT_ERROR_CODE",{value:"E145",enumerable:!1,configurable:!0})}let{status:p,headers:f,body:g}=d.response;return new Response(g?n.from(g,"base64"):null,{status:p,headers:new Headers(f)})}function l(e){return r.g.fetch=function(t,r){var n;return(null==r||null==(n=r.next)?void 0:n.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},554:(e,t)=>{"use strict";t.qg=function(e,t){let a=new r,o=e.length;if(o<2)return a;let l=t?.decode||s,c=0;do{let t=e.indexOf("=",c);if(-1===t)break;let r=e.indexOf(";",c),s=-1===r?o:r;if(t>s){c=e.lastIndexOf(";",t-1)+1;continue}let u=n(e,c,t),d=i(e,t,u),h=e.slice(u,d);if(void 0===a[h]){let r=n(e,t+1,s),o=i(e,s,r),c=l(e.slice(r,o));a[h]=c}c=s+1}while(c<o);return a},Object.prototype.toString;let r=(()=>{let e=function(){};return e.prototype=Object.create(null),e})();function n(e,t,r){do{let r=e.charCodeAt(t);if(32!==r&&9!==r)return t}while(++t<r);return r}function i(e,t,r){for(;t>r;){let r=e.charCodeAt(--t);if(32!==r&&9!==r)return t+1}return r}function s(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},557:(e,t,r)=>{"use strict";r.d(t,{t3:()=>l,I3:()=>d,Ui:()=>c,xI:()=>a,Pk:()=>o});var n=r(815),i=r(16);r(602),r(115),r(535),r(801);let s="function"==typeof n.unstable_postpone;function a(e,t,r){let n=Object.defineProperty(new i.F(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw r.revalidate=0,t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}function o(e,t){t&&"cache"!==t.type&&"unstable-cache"!==t.type&&("prerender"===t.type||"prerender-legacy"===t.type)&&(t.revalidate=0)}function l(e,t,r,n){if(!1===n.controller.signal.aborted){let i=n.dynamicTracking;i&&null===i.syncDynamicErrorWithStack&&(i.syncDynamicExpression=t,i.syncDynamicErrorWithStack=r,!0===n.validating&&(i.syncDynamicLogged=!0)),function(e,t,r){let n=p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`);r.controller.abort(n);let i=r.dynamicTracking;i&&i.dynamicAccesses.push({stack:i.isDebugDynamicAccesses?Error().stack:void 0,expression:t})}(e,t,n)}throw p(`Route ${e} needs to bail out of prerendering at this point because it used ${t}.`)}function c(e,t,r){(function(){if(!s)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),r&&r.dynamicAccesses.push({stack:r.isDebugDynamicAccesses?Error().stack:void 0,expression:t}),n.unstable_postpone(u(e,t))}function u(e,t){return`Route ${e} needs to bail out of prerendering at this point because it used ${t}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function d(e){return"object"==typeof e&&null!==e&&"string"==typeof e.message&&h(e.message)}function h(e){return e.includes("needs to bail out of prerendering at this point because it used")&&e.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===h(u("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});function p(e){let t=Object.defineProperty(Error(e),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return t.digest="NEXT_PRERENDER_INTERRUPTED",t}RegExp(`\\n\\s+at __next_metadata_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_viewport_boundary__[\\n\\s]`),RegExp(`\\n\\s+at __next_outlet_boundary__[\\n\\s]`)},602:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});class n extends Error{constructor(...e){super(...e),this.code="NEXT_STATIC_GEN_BAILOUT"}}},620:(e,t,r)=>{"use strict";r.d(t,{cg:()=>o,xl:()=>a});let n=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class i{disable(){throw n}getStore(){}run(){throw n}exit(){throw n}enterWith(){throw n}static bind(e){return e}}let s="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function a(){return s?new s:new i}function o(e){return s?s.bind(e):i.bind(e)}},716:(e,t,r)=>{"use strict";r.d(t,{l:()=>n});class n{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},724:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,n=Object.getOwnPropertyNames,i=Object.prototype.hasOwnProperty,s={};function a(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),n=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?n:`${n}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[n,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(n,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}function l(e){if(!e)return;let[[t,r],...n]=o(e),{domain:i,expires:s,httponly:a,maxage:l,path:d,samesite:h,secure:p,partitioned:f,priority:g}=Object.fromEntries(n.map(([e,t])=>[e.toLowerCase().replace(/-/g,""),t]));{var m,y,b={name:t,value:decodeURIComponent(r),domain:i,...s&&{expires:new Date(s)},...a&&{httpOnly:!0},..."string"==typeof l&&{maxAge:Number(l)},path:d,...h&&{sameSite:c.includes(m=(m=h).toLowerCase())?m:void 0},...p&&{secure:!0},...g&&{priority:u.includes(y=(y=g).toLowerCase())?y:void 0},...f&&{partitioned:!0}};let e={};for(let t in b)b[t]&&(e[t]=b[t]);return e}}((e,r)=>{for(var n in r)t(e,n,{get:r[n],enumerable:!0})})(s,{RequestCookies:()=>d,ResponseCookies:()=>h,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>a}),e.exports=((e,s,a,o)=>{if(s&&"object"==typeof s||"function"==typeof s)for(let l of n(s))i.call(e,l)||l===a||t(e,l,{get:()=>s[l],enumerable:!(o=r(s,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),s);var c=["strict","lax","none"],u=["low","medium","high"],d=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===n).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,n=this._parsed;return n.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(n).map(([e,t])=>a(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>a(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},h=class{constructor(e){var t,r,n;this._parsed=new Map,this._headers=e;let i=null!=(n=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?n:[];for(let e of Array.isArray(i)?i:function(e){if(!e)return[];var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}(i)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let n="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===n)}has(e){return this._parsed.has(e)}set(...e){let[t,r,n]=1===e.length?[e[0].name,e[0].value,e[0]]:e,i=this._parsed;return i.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...n})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=a(r);t.append("set-cookie",e)}}(i,this._headers),this}delete(...e){let[t,r]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0]];return this.set({...r,name:t,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(a).join("; ")}}},725:(e,t,r)=>{"use strict";r.d(t,{Ud:()=>n.stringifyCookie,VO:()=>n.ResponseCookies,tm:()=>n.RequestCookies});var n=r(724)},792:(e,t,r)=>{"use strict";r.d(t,{X:()=>function e(t){if((0,s.p)(t)||"object"==typeof t&&null!==t&&"digest"in t&&"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t.digest||(0,o.h)(t)||(0,a.I3)(t)||"object"==typeof t&&null!==t&&t.$$typeof===i||(0,n.T)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}});var n=r(801);let i=Symbol.for("react.postpone");var s=r(199),a=r(557),o=r(16)},801:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===i}r.d(t,{T:()=>n,W:()=>o});let i="HANGING_PROMISE_REJECTION";class s extends Error{constructor(e){super(`During prerendering, ${e} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${e} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context.`),this.expression=e,this.digest=i}}let a=new WeakMap;function o(e,t){if(e.aborted)return Promise.reject(new s(t));{let r=new Promise((r,n)=>{let i=n.bind(null,new s(t)),o=a.get(e);if(o)o.push(i);else{let t=[i];a.set(e,t),e.addEventListener("abort",()=>{for(let e=0;e<t.length;e++)t[e]()},{once:!0})}});return r.catch(l),r}}function l(){}},802:e=>{(()=>{"use strict";var t={993:e=>{var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function s(e,t,n,s,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var o=new i(n,s||e,a),l=r?r+t:t;return e._events[l]?e._events[l].fn?e._events[l]=[e._events[l],o]:e._events[l].push(o):(e._events[l]=o,e._eventsCount++),e}function a(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function o(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),(new n).__proto__||(r=!1)),o.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},o.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,s=n.length,a=Array(s);i<s;i++)a[i]=n[i].fn;return a},o.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},o.prototype.emit=function(e,t,n,i,s,a){var o=r?r+e:e;if(!this._events[o])return!1;var l,c,u=this._events[o],d=arguments.length;if(u.fn){switch(u.once&&this.removeListener(e,u.fn,void 0,!0),d){case 1:return u.fn.call(u.context),!0;case 2:return u.fn.call(u.context,t),!0;case 3:return u.fn.call(u.context,t,n),!0;case 4:return u.fn.call(u.context,t,n,i),!0;case 5:return u.fn.call(u.context,t,n,i,s),!0;case 6:return u.fn.call(u.context,t,n,i,s,a),!0}for(c=1,l=Array(d-1);c<d;c++)l[c-1]=arguments[c];u.fn.apply(u.context,l)}else{var h,p=u.length;for(c=0;c<p;c++)switch(u[c].once&&this.removeListener(e,u[c].fn,void 0,!0),d){case 1:u[c].fn.call(u[c].context);break;case 2:u[c].fn.call(u[c].context,t);break;case 3:u[c].fn.call(u[c].context,t,n);break;case 4:u[c].fn.call(u[c].context,t,n,i);break;default:if(!l)for(h=1,l=Array(d-1);h<d;h++)l[h-1]=arguments[h];u[c].fn.apply(u[c].context,l)}}return!0},o.prototype.on=function(e,t,r){return s(this,e,t,r,!1)},o.prototype.once=function(e,t,r){return s(this,e,t,r,!0)},o.prototype.removeListener=function(e,t,n,i){var s=r?r+e:e;if(!this._events[s])return this;if(!t)return a(this,s),this;var o=this._events[s];if(o.fn)o.fn!==t||i&&!o.once||n&&o.context!==n||a(this,s);else{for(var l=0,c=[],u=o.length;l<u;l++)(o[l].fn!==t||i&&!o[l].once||n&&o[l].context!==n)&&c.push(o[l]);c.length?this._events[s]=1===c.length?c[0]:c:a(this,s)}return this},o.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&a(this,t)):(this._events=new n,this._eventsCount=0),this},o.prototype.off=o.prototype.removeListener,o.prototype.addListener=o.prototype.on,o.prefixed=r,o.EventEmitter=o,e.exports=o},213:e=>{e.exports=(e,t)=>(t=t||(()=>{}),e.then(e=>new Promise(e=>{e(t())}).then(()=>e),e=>new Promise(e=>{e(t())}).then(()=>{throw e})))},574:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r){let n=0,i=e.length;for(;i>0;){let s=i/2|0,a=n+s;0>=r(e[a],t)?(n=++a,i-=s+1):i=s}return n}},821:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});let n=r(574);class i{constructor(){this._queue=[]}enqueue(e,t){let r={priority:(t=Object.assign({priority:0},t)).priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority)return void this._queue.push(r);let i=n.default(this._queue,r,(e,t)=>t.priority-e.priority);this._queue.splice(i,0,r)}dequeue(){let e=this._queue.shift();return null==e?void 0:e.run}filter(e){return this._queue.filter(t=>t.priority===e.priority).map(e=>e.run)}get size(){return this._queue.length}}t.default=i},816:(e,t,r)=>{let n=r(213);class i extends Error{constructor(e){super(e),this.name="TimeoutError"}}let s=(e,t,r)=>new Promise((s,a)=>{if("number"!=typeof t||t<0)throw TypeError("Expected `milliseconds` to be a positive number");if(t===1/0)return void s(e);let o=setTimeout(()=>{if("function"==typeof r){try{s(r())}catch(e){a(e)}return}let n="string"==typeof r?r:`Promise timed out after ${t} milliseconds`,o=r instanceof Error?r:new i(n);"function"==typeof e.cancel&&e.cancel(),a(o)},t);n(e.then(s,a),()=>{clearTimeout(o)})});e.exports=s,e.exports.default=s,e.exports.TimeoutError=i}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var s=r[e]={exports:{}},a=!0;try{t[e](s,s.exports,n),a=!1}finally{a&&delete r[e]}return s.exports}n.ab="//";var i={};(()=>{Object.defineProperty(i,"__esModule",{value:!0});let e=n(993),t=n(816),r=n(821),s=()=>{},a=new t.TimeoutError;class o extends e{constructor(e){var t,n,i,a;if(super(),this._intervalCount=0,this._intervalEnd=0,this._pendingCount=0,this._resolveEmpty=s,this._resolveIdle=s,!("number"==typeof(e=Object.assign({carryoverConcurrencyCount:!1,intervalCap:1/0,interval:0,concurrency:1/0,autoStart:!0,queueClass:r.default},e)).intervalCap&&e.intervalCap>=1))throw TypeError(`Expected \`intervalCap\` to be a number from 1 and up, got \`${null!=(n=null==(t=e.intervalCap)?void 0:t.toString())?n:""}\` (${typeof e.intervalCap})`);if(void 0===e.interval||!(Number.isFinite(e.interval)&&e.interval>=0))throw TypeError(`Expected \`interval\` to be a finite number >= 0, got \`${null!=(a=null==(i=e.interval)?void 0:i.toString())?a:""}\` (${typeof e.interval})`);this._carryoverConcurrencyCount=e.carryoverConcurrencyCount,this._isIntervalIgnored=e.intervalCap===1/0||0===e.interval,this._intervalCap=e.intervalCap,this._interval=e.interval,this._queue=new e.queueClass,this._queueClass=e.queueClass,this.concurrency=e.concurrency,this._timeout=e.timeout,this._throwOnTimeout=!0===e.throwOnTimeout,this._isPaused=!1===e.autoStart}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--,this._tryToStartAnother(),this.emit("next")}_resolvePromises(){this._resolveEmpty(),this._resolveEmpty=s,0===this._pendingCount&&(this._resolveIdle(),this._resolveIdle=s,this.emit("idle"))}_onResumeInterval(){this._onInterval(),this._initializeIntervalIfNeeded(),this._timeoutId=void 0}_isIntervalPaused(){let e=Date.now();if(void 0===this._intervalId){let t=this._intervalEnd-e;if(!(t<0))return void 0===this._timeoutId&&(this._timeoutId=setTimeout(()=>{this._onResumeInterval()},t)),!0;this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}return!1}_tryToStartAnother(){if(0===this._queue.size)return this._intervalId&&clearInterval(this._intervalId),this._intervalId=void 0,this._resolvePromises(),!1;if(!this._isPaused){let e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){let t=this._queue.dequeue();return!!t&&(this.emit("active"),t(),e&&this._initializeIntervalIfNeeded(),!0)}}return!1}_initializeIntervalIfNeeded(){this._isIntervalIgnored||void 0!==this._intervalId||(this._intervalId=setInterval(()=>{this._onInterval()},this._interval),this._intervalEnd=Date.now()+this._interval)}_onInterval(){0===this._intervalCount&&0===this._pendingCount&&this._intervalId&&(clearInterval(this._intervalId),this._intervalId=void 0),this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0,this._processQueue()}_processQueue(){for(;this._tryToStartAnother(););}get concurrency(){return this._concurrency}set concurrency(e){if(!("number"==typeof e&&e>=1))throw TypeError(`Expected \`concurrency\` to be a number from 1 and up, got \`${e}\` (${typeof e})`);this._concurrency=e,this._processQueue()}async add(e,r={}){return new Promise((n,i)=>{let s=async()=>{this._pendingCount++,this._intervalCount++;try{let s=void 0===this._timeout&&void 0===r.timeout?e():t.default(Promise.resolve(e()),void 0===r.timeout?this._timeout:r.timeout,()=>{(void 0===r.throwOnTimeout?this._throwOnTimeout:r.throwOnTimeout)&&i(a)});n(await s)}catch(e){i(e)}this._next()};this._queue.enqueue(s,r),this._tryToStartAnother(),this.emit("add")})}async addAll(e,t){return Promise.all(e.map(async e=>this.add(e,t)))}start(){return this._isPaused&&(this._isPaused=!1,this._processQueue()),this}pause(){this._isPaused=!0}clear(){this._queue=new this._queueClass}async onEmpty(){if(0!==this._queue.size)return new Promise(e=>{let t=this._resolveEmpty;this._resolveEmpty=()=>{t(),e()}})}async onIdle(){if(0!==this._pendingCount||0!==this._queue.size)return new Promise(e=>{let t=this._resolveIdle;this._resolveIdle=()=>{t(),e()}})}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}i.default=o})(),e.exports=i})()},815:(e,t,r)=>{"use strict";e.exports=r(35)},818:(e,t,r)=>{"use strict";r.d(t,{Ck:()=>l,K8:()=>u,hm:()=>d});var n=r(725),i=r(716),s=r(535),a=r(115);class o extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new o}}class l{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return o.callable;default:return i.l.get(e,t,r)}}})}}let c=Symbol.for("next.mutated.cookies");class u{static wrap(e,t){let r=new n.VO(new Headers);for(let t of e.getAll())r.set(t);let a=[],o=new Set,l=()=>{let e=s.J.getStore();if(e&&(e.pathWasRevalidated=!0),a=r.getAll().filter(e=>o.has(e.name)),t){let e=[];for(let t of a){let r=new n.VO(new Headers);r.set(t),e.push(r.toString())}t(e)}},u=new Proxy(r,{get(e,t,r){switch(t){case c:return a;case"delete":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),u}finally{l()}};case"set":return function(...t){o.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),u}finally{l()}};default:return i.l.get(e,t,r)}}});return u}}function d(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return h("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return h("cookies().set"),e.set(...r),t};default:return i.l.get(e,r,n)}}});return t}function h(e){if("action"!==(0,a.XN)(e).phase)throw new o}},821:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n});var n=function(e){return e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect",e}({})},830:(e,t,r)=>{"use strict";r.d(t,{s:()=>n});let n=(0,r(58).xl)()},833:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>o3});var i={};async function s(){return"_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&await _ENTRIES.middleware_instrumentation}r.r(i),r.d(i,{config:()=>o1,default:()=>o0});let a=null;async function o(){if("phase-production-build"===process.env.NEXT_PHASE)return;a||(a=s());let e=await a;if(null==e?void 0:e.register)try{await e.register()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}async function l(...e){let t=await s();try{var r;await (null==t||null==(r=t.onRequestError)?void 0:r.call(t,...e))}catch(e){console.error("Error in instrumentation.onRequestError:",e)}}let c=null;function u(){return c||(c=o()),c}function d(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},construct(){throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})},apply(r,n,i){if("function"==typeof i[0])return i[0](t);throw Object.defineProperty(Error(d(e)),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),u();class h extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class p extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class f extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}let g="_N_T_",m={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",apiNode:"api-node",apiEdge:"api-edge",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",pagesDirBrowser:"pages-dir-browser",pagesDirEdge:"pages-dir-edge",pagesDirNode:"pages-dir-node"};function y(e){var t,r,n,i,s,a=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,s=!1;l();)if(","===(r=e.charAt(o))){for(n=o,o+=1,l(),i=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(s=!0,o=i,a.push(e.substring(t,n)),t=o):o=n+1}else o+=1;(!s||o>=e.length)&&a.push(e.substring(t,e.length))}return a}function b(e){let t={},r=[];if(e)for(let[n,i]of e.entries())"set-cookie"===n.toLowerCase()?(r.push(...y(i)),t[n]=1===r.length?r[0]:r):t[n]=i;return t}function _(e){try{return String(new URL(String(e)))}catch(t){throw Object.defineProperty(Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t}),"__NEXT_ERROR_CODE",{value:"E61",enumerable:!1,configurable:!0})}}({...m,GROUP:{builtinReact:[m.reactServerComponents,m.actionBrowser],serverOnly:[m.reactServerComponents,m.actionBrowser,m.instrument,m.middleware],neutralTarget:[m.apiNode,m.apiEdge],clientOnly:[m.serverSideRendering,m.appPagesBrowser],bundled:[m.reactServerComponents,m.actionBrowser,m.serverSideRendering,m.appPagesBrowser,m.shared,m.instrument,m.middleware],appPages:[m.reactServerComponents,m.serverSideRendering,m.appPagesBrowser,m.actionBrowser]}});let v=Symbol("response"),w=Symbol("passThrough"),k=Symbol("waitUntil");class S{constructor(e,t){this[w]=!1,this[k]=t?{kind:"external",function:t}:{kind:"internal",promises:[]}}respondWith(e){this[v]||(this[v]=Promise.resolve(e))}passThroughOnException(){this[w]=!0}waitUntil(e){if("external"===this[k].kind)return(0,this[k].function)(e);this[k].promises.push(e)}}class E extends S{constructor(e){var t;super(e.request,null==(t=e.context)?void 0:t.waitUntil),this.sourcePage=e.page}get request(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}function T(e){return e.replace(/\/$/,"")||"/"}function x(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function O(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=x(e);return""+t+r+n+i}function C(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:n,hash:i}=x(e);return""+r+t+n+i}function R(e,t){if("string"!=typeof e)return!1;let{pathname:r}=x(e);return r===t||r.startsWith(t+"/")}let P=new WeakMap;function I(e,t){let r;if(!t)return{pathname:e};let n=P.get(t);n||(n=t.map(e=>e.toLowerCase()),P.set(t,n));let i=e.split("/",2);if(!i[1])return{pathname:e};let s=i[1].toLowerCase(),a=n.indexOf(s);return a<0?{pathname:e}:(r=t[a],{pathname:e=e.slice(r.length+1)||"/",detectedLocale:r})}let A=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function N(e,t){return new URL(String(e).replace(A,"localhost"),t&&String(t).replace(A,"localhost"))}let U=Symbol("NextURLInternal");class M{constructor(e,t,r){let n,i;"object"==typeof t&&"pathname"in t||"string"==typeof t?(n=t,i=r||{}):i=r||t||{},this[U]={url:N(e,n??i.base),options:i,basePath:""},this.analyze()}analyze(){var e,t,r,n,i;let s=function(e,t){var r,n;let{basePath:i,i18n:s,trailingSlash:a}=null!=(r=t.nextConfig)?r:{},o={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):a};i&&R(o.pathname,i)&&(o.pathname=function(e,t){if(!R(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(o.pathname,i),o.basePath=i);let l=o.pathname;if(o.pathname.startsWith("/_next/data/")&&o.pathname.endsWith(".json")){let e=o.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/");o.buildId=e[0],l="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(o.pathname=l)}if(s){let e=t.i18nProvider?t.i18nProvider.analyze(o.pathname):I(o.pathname,s.locales);o.locale=e.detectedLocale,o.pathname=null!=(n=e.pathname)?n:o.pathname,!e.detectedLocale&&o.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(l):I(l,s.locales)).detectedLocale&&(o.locale=e.detectedLocale)}return o}(this[U].url.pathname,{nextConfig:this[U].options.nextConfig,parseData:!0,i18nProvider:this[U].options.i18nProvider}),a=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[U].url,this[U].options.headers);this[U].domainLocale=this[U].options.i18nProvider?this[U].options.i18nProvider.detectDomainLocale(a):function(e,t,r){if(e)for(let s of(r&&(r=r.toLowerCase()),e)){var n,i;if(t===(null==(n=s.domain)?void 0:n.split(":",1)[0].toLowerCase())||r===s.defaultLocale.toLowerCase()||(null==(i=s.locales)?void 0:i.some(e=>e.toLowerCase()===r)))return s}}(null==(t=this[U].options.nextConfig)||null==(e=t.i18n)?void 0:e.domains,a);let o=(null==(r=this[U].domainLocale)?void 0:r.defaultLocale)||(null==(i=this[U].options.nextConfig)||null==(n=i.i18n)?void 0:n.defaultLocale);this[U].url.pathname=s.pathname,this[U].defaultLocale=o,this[U].basePath=s.basePath??"",this[U].buildId=s.buildId,this[U].locale=s.locale??o,this[U].trailingSlash=s.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,n){if(!t||t===r)return e;let i=e.toLowerCase();return!n&&(R(i,"/api")||R(i,"/"+t.toLowerCase()))?e:O(e,"/"+t)}((e={basePath:this[U].basePath,buildId:this[U].buildId,defaultLocale:this[U].options.forceLocale?void 0:this[U].defaultLocale,locale:this[U].locale,pathname:this[U].url.pathname,trailingSlash:this[U].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=T(t)),e.buildId&&(t=C(O(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=O(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:C(t,"/"):T(t)}formatSearch(){return this[U].url.search}get buildId(){return this[U].buildId}set buildId(e){this[U].buildId=e}get locale(){return this[U].locale??""}set locale(e){var t,r;if(!this[U].locale||!(null==(r=this[U].options.nextConfig)||null==(t=r.i18n)?void 0:t.locales.includes(e)))throw Object.defineProperty(TypeError(`The NextURL configuration includes no locale "${e}"`),"__NEXT_ERROR_CODE",{value:"E597",enumerable:!1,configurable:!0});this[U].locale=e}get defaultLocale(){return this[U].defaultLocale}get domainLocale(){return this[U].domainLocale}get searchParams(){return this[U].url.searchParams}get host(){return this[U].url.host}set host(e){this[U].url.host=e}get hostname(){return this[U].url.hostname}set hostname(e){this[U].url.hostname=e}get port(){return this[U].url.port}set port(e){this[U].url.port=e}get protocol(){return this[U].url.protocol}set protocol(e){this[U].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[U].url=N(e),this.analyze()}get origin(){return this[U].url.origin}get pathname(){return this[U].url.pathname}set pathname(e){this[U].url.pathname=e}get hash(){return this[U].url.hash}set hash(e){this[U].url.hash=e}get search(){return this[U].url.search}set search(e){this[U].url.search=e}get password(){return this[U].url.password}set password(e){this[U].url.password=e}get username(){return this[U].url.username}set username(e){this[U].url.username=e}get basePath(){return this[U].basePath}set basePath(e){this[U].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new M(String(this),this[U].options)}}var j=r(725);let D=Symbol("internal request");class L extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);_(r),e instanceof Request?super(e,t):super(r,t);let n=new M(r,{headers:b(this.headers),nextConfig:t.nextConfig});this[D]={cookies:new j.tm(this.headers),nextUrl:n,url:n.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[D].cookies}get nextUrl(){return this[D].nextUrl}get page(){throw new p}get ua(){throw new f}get url(){return this[D].url}}var q=r(716);let B=Symbol("internal response"),H=new Set([301,302,303,307,308]);function $(e,t){var r;if(null==e||null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Object.defineProperty(Error("request.headers must be an instance of Headers"),"__NEXT_ERROR_CODE",{value:"E119",enumerable:!1,configurable:!0});let r=[];for(let[n,i]of e.request.headers)t.set("x-middleware-request-"+n,i),r.push(n);t.set("x-middleware-override-headers",r.join(","))}}class z extends Response{constructor(e,t={}){super(e,t);let r=this.headers,n=new Proxy(new j.VO(r),{get(e,n,i){switch(n){case"delete":case"set":return(...i)=>{let s=Reflect.apply(e[n],e,i),a=new Headers(r);return s instanceof j.VO&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,j.Ud)(e)).join(",")),$(t,a),s};default:return q.l.get(e,n,i)}}});this[B]={cookies:n,url:t.url?new M(t.url,{headers:b(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[B].cookies}static json(e,t){let r=Response.json(e,t);return new z(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!H.has(r))throw Object.defineProperty(RangeError('Failed to execute "redirect" on "response": Invalid status code'),"__NEXT_ERROR_CODE",{value:"E529",enumerable:!1,configurable:!0});let n="object"==typeof t?t:{},i=new Headers(null==n?void 0:n.headers);return i.set("Location",_(e)),new z(null,{...n,headers:i,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",_(e)),$(t,r),new z(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),$(e,t),new z(null,{...e,headers:t})}}function K(e,t){let r="string"==typeof t?new URL(t):t,n=new URL(e,t),i=n.origin===r.origin;return{url:i?n.toString().slice(r.origin.length):n.toString(),isRelative:i}}let J="Next-Router-Prefetch",W=["RSC","Next-Router-State-Tree",J,"Next-HMR-Refresh","Next-Router-Segment-Prefetch"],F="_rsc";var V=r(381),G=r(818),X=function(e){return e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404",e}(X||{}),Q=function(e){return e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents",e}(Q||{}),Y=function(e){return e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer",e}(Y||{}),Z=function(e){return e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch",e}(Z||{}),ee=function(e){return e.startServer="startServer.startServer",e}(ee||{}),et=function(e){return e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult",e}(et||{}),er=function(e){return e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch",e}(er||{}),en=function(e){return e.executeRoute="Router.executeRoute",e}(en||{}),ei=function(e){return e.runHandler="Node.runHandler",e}(ei||{}),es=function(e){return e.runHandler="AppRouteRouteHandlers.runHandler",e}(es||{}),ea=function(e){return e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport",e}(ea||{}),eo=function(e){return e.execute="Middleware.execute",e}(eo||{});let el=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],ec=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"];function eu(e){return null!==e&&"object"==typeof e&&"then"in e&&"function"==typeof e.then}let{context:ed,propagation:eh,trace:ep,SpanStatusCode:ef,SpanKind:eg,ROOT_CONTEXT:em}=n=r(956);class ey extends Error{constructor(e,t){super(),this.bubble=e,this.result=t}}let eb=(e,t)=>{(function(e){return"object"==typeof e&&null!==e&&e instanceof ey})(t)&&t.bubble?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:ef.ERROR,message:null==t?void 0:t.message})),e.end()},e_=new Map,ev=n.createContextKey("next.rootSpanId"),ew=0,ek=()=>ew++,eS={set(e,t,r){e.push({key:t,value:r})}};class eE{getTracerInstance(){return ep.getTracer("next.js","0.0.1")}getContext(){return ed}getTracePropagationData(){let e=ed.active(),t=[];return eh.inject(e,t,eS),t}getActiveScopeSpan(){return ep.getSpan(null==ed?void 0:ed.active())}withPropagatedContext(e,t,r){let n=ed.active();if(ep.getSpanContext(n))return t();let i=eh.extract(n,e,r);return ed.with(i,t)}trace(...e){var t;let[r,n,i]=e,{fn:s,options:a}="function"==typeof n?{fn:n,options:{}}:{fn:i,options:{...n}},o=a.spanName??r;if(!el.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||a.hideSpan)return s();let l=this.getSpanContext((null==a?void 0:a.parentSpan)??this.getActiveScopeSpan()),c=!1;l?(null==(t=ep.getSpanContext(l))?void 0:t.isRemote)&&(c=!0):(l=(null==ed?void 0:ed.active())??em,c=!0);let u=ek();return a.attributes={"next.span_name":o,"next.span_type":r,...a.attributes},ed.with(l.setValue(ev,u),()=>this.getTracerInstance().startActiveSpan(o,a,e=>{let t="performance"in globalThis&&"measure"in performance?globalThis.performance.now():void 0,n=()=>{e_.delete(u),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&ec.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};c&&e_.set(u,new Map(Object.entries(a.attributes??{})));try{if(s.length>1)return s(e,t=>eb(e,t));let t=s(e);if(eu(t))return t.then(t=>(e.end(),t)).catch(t=>{throw eb(e,t),t}).finally(n);return e.end(),n(),t}catch(t){throw eb(e,t),n(),t}}))}wrap(...e){let t=this,[r,n,i]=3===e.length?e:[e[0],{},e[1]];return el.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=n;"function"==typeof e&&"function"==typeof i&&(e=e.apply(this,arguments));let s=arguments.length-1,a=arguments[s];if("function"!=typeof a)return t.trace(r,e,()=>i.apply(this,arguments));{let n=t.getContext().bind(ed.active(),a);return t.trace(r,e,(e,t)=>(arguments[s]=function(e){return null==t||t(e),n.apply(this,arguments)},i.apply(this,arguments)))}}:i}startSpan(...e){let[t,r]=e,n=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,n)}getSpanContext(e){return e?ep.setSpan(ed.active(),e):void 0}getRootSpanAttributes(){let e=ed.active().getValue(ev);return e_.get(e)}setRootSpanAttribute(e,t){let r=ed.active().getValue(ev),n=e_.get(r);n&&n.set(e,t)}}let eT=(()=>{let e=new eE;return()=>e})(),ex="__prerender_bypass";Symbol("__next_preview_data"),Symbol(ex);class eO{constructor(e,t,r,n){var i;let s=e&&function(e,t){let r=V.o.from(e.headers);return{isOnDemandRevalidate:r.get("x-prerender-revalidate")===t.previewModeId,revalidateOnlyGenerated:r.has("x-prerender-revalidate-if-generated")}}(t,e).isOnDemandRevalidate,a=null==(i=r.get(ex))?void 0:i.value;this._isEnabled=!!(!s&&a&&e&&a===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=n}get isEnabled(){return this._isEnabled}enable(){if(!this._previewModeId)throw Object.defineProperty(Error("Invariant: previewProps missing previewModeId this should never happen"),"__NEXT_ERROR_CODE",{value:"E93",enumerable:!1,configurable:!0});this._mutableCookies.set({name:ex,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"}),this._isEnabled=!0}disable(){this._mutableCookies.set({name:ex,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)}),this._isEnabled=!1}}function eC(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],n=new Headers;for(let e of y(r))n.append("set-cookie",e);for(let e of new j.VO(n).getAll())t.set(e)}}var eR=r(115),eP=r(802),eI=r.n(eP);class eA extends Error{constructor(e,t){super("Invariant: "+(e.endsWith(".")?e:e+".")+" This is a bug in Next.js.",t),this.name="InvariantError"}}var eN=r(535);class eU{constructor(e,t){this.cache=new Map,this.sizes=new Map,this.totalSize=0,this.maxSize=e,this.calculateSize=t||(()=>1)}set(e,t){if(!e||!t)return;let r=this.calculateSize(t);if(r>this.maxSize)return void console.warn("Single item size exceeds maxSize");this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0),this.cache.set(e,t),this.sizes.set(e,r),this.totalSize+=r,this.touch(e)}has(e){return!!e&&(this.touch(e),!!this.cache.get(e))}get(e){if(!e)return;let t=this.cache.get(e);if(void 0!==t)return this.touch(e),t}touch(e){let t=this.cache.get(e);void 0!==t&&(this.cache.delete(e),this.cache.set(e,t),this.evictIfNecessary())}evictIfNecessary(){for(;this.totalSize>this.maxSize&&this.cache.size>0;)this.evictLeastRecentlyUsed()}evictLeastRecentlyUsed(){let e=this.cache.keys().next().value;if(void 0!==e){let t=this.sizes.get(e)||0;this.totalSize-=t,this.cache.delete(e),this.sizes.delete(e)}}reset(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}keys(){return[...this.cache.keys()]}remove(e){this.cache.has(e)&&(this.totalSize-=this.sizes.get(e)||0,this.cache.delete(e),this.sizes.delete(e))}clear(){this.cache.clear(),this.sizes.clear(),this.totalSize=0}get size(){return this.cache.size}get currentSize(){return this.totalSize}}r(356).Buffer,new eU(0x3200000,e=>e.size),process.env.NEXT_PRIVATE_DEBUG_CACHE&&console.debug.bind(console,"DefaultCacheHandler:"),process.env.NEXT_PRIVATE_DEBUG_CACHE,Symbol.for("@next/cache-handlers");let eM=Symbol.for("@next/cache-handlers-map"),ej=Symbol.for("@next/cache-handlers-set"),eD=globalThis;function eL(){if(eD[eM])return eD[eM].entries()}async function eq(e,t){if(!e)return t();let r=eB(e);try{return await t()}finally{let t=function(e,t){let r=new Set(e.pendingRevalidatedTags),n=new Set(e.pendingRevalidateWrites);return{pendingRevalidatedTags:t.pendingRevalidatedTags.filter(e=>!r.has(e)),pendingRevalidates:Object.fromEntries(Object.entries(t.pendingRevalidates).filter(([t])=>!(t in e.pendingRevalidates))),pendingRevalidateWrites:t.pendingRevalidateWrites.filter(e=>!n.has(e))}}(r,eB(e));await e$(e,t)}}function eB(e){return{pendingRevalidatedTags:e.pendingRevalidatedTags?[...e.pendingRevalidatedTags]:[],pendingRevalidates:{...e.pendingRevalidates},pendingRevalidateWrites:e.pendingRevalidateWrites?[...e.pendingRevalidateWrites]:[]}}async function eH(e,t){if(0===e.length)return;let r=[];t&&r.push(t.revalidateTag(e));let n=function(){if(eD[ej])return eD[ej].values()}();if(n)for(let t of n)r.push(t.expireTags(...e));await Promise.all(r)}async function e$(e,t){let r=(null==t?void 0:t.pendingRevalidatedTags)??e.pendingRevalidatedTags??[],n=(null==t?void 0:t.pendingRevalidates)??e.pendingRevalidates??{},i=(null==t?void 0:t.pendingRevalidateWrites)??e.pendingRevalidateWrites??[];return Promise.all([eH(r,e.incrementalCache),...Object.values(n),...i])}var ez=r(620),eK=r(427);class eJ{constructor({waitUntil:e,onClose:t,onTaskError:r}){this.workUnitStores=new Set,this.waitUntil=e,this.onClose=t,this.onTaskError=r,this.callbackQueue=new(eI()),this.callbackQueue.pause()}after(e){if(eu(e))this.waitUntil||eW(),this.waitUntil(e.catch(e=>this.reportTaskError("promise",e)));else if("function"==typeof e)this.addCallback(e);else throw Object.defineProperty(Error("`after()`: Argument must be a promise or a function"),"__NEXT_ERROR_CODE",{value:"E50",enumerable:!1,configurable:!0})}addCallback(e){this.waitUntil||eW();let t=eR.FP.getStore();t&&this.workUnitStores.add(t);let r=eK.Z.getStore(),n=r?r.rootTaskSpawnPhase:null==t?void 0:t.phase;this.runCallbacksOnClosePromise||(this.runCallbacksOnClosePromise=this.runCallbacksOnClose(),this.waitUntil(this.runCallbacksOnClosePromise));let i=(0,ez.cg)(async()=>{try{await eK.Z.run({rootTaskSpawnPhase:n},()=>e())}catch(e){this.reportTaskError("function",e)}});this.callbackQueue.add(i)}async runCallbacksOnClose(){return await new Promise(e=>this.onClose(e)),this.runCallbacks()}async runCallbacks(){if(0===this.callbackQueue.size)return;for(let e of this.workUnitStores)e.phase="after";let e=eN.J.getStore();if(!e)throw Object.defineProperty(new eA("Missing workStore in AfterContext.runCallbacks"),"__NEXT_ERROR_CODE",{value:"E547",enumerable:!1,configurable:!0});return eq(e,()=>(this.callbackQueue.start(),this.callbackQueue.onIdle()))}reportTaskError(e,t){if(console.error("promise"===e?"A promise passed to `after()` rejected:":"An error occurred in a function passed to `after()`:",t),this.onTaskError)try{null==this.onTaskError||this.onTaskError.call(this,t)}catch(e){console.error(Object.defineProperty(new eA("`onTaskError` threw while handling an error thrown from an `after` task",{cause:e}),"__NEXT_ERROR_CODE",{value:"E569",enumerable:!1,configurable:!0}))}}}function eW(){throw Object.defineProperty(Error("`after()` will not work correctly, because `waitUntil` is not available in the current environment."),"__NEXT_ERROR_CODE",{value:"E91",enumerable:!1,configurable:!0})}function eF(e){let t,r={then:(n,i)=>(t||(t=e()),t.then(e=>{r.value=e}).catch(()=>{}),t.then(n,i))};return r}class eV{onClose(e){if(this.isClosed)throw Object.defineProperty(Error("Cannot subscribe to a closed CloseController"),"__NEXT_ERROR_CODE",{value:"E365",enumerable:!1,configurable:!0});this.target.addEventListener("close",e),this.listeners++}dispatchClose(){if(this.isClosed)throw Object.defineProperty(Error("Cannot close a CloseController multiple times"),"__NEXT_ERROR_CODE",{value:"E229",enumerable:!1,configurable:!0});this.listeners>0&&this.target.dispatchEvent(new Event("close")),this.isClosed=!0}constructor(){this.target=new EventTarget,this.listeners=0,this.isClosed=!1}}function eG(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}let eX=Symbol.for("@next/request-context"),eQ=e=>{let t=["/layout"];if(e.startsWith("/")){let r=e.split("/");for(let e=1;e<r.length+1;e++){let n=r.slice(0,e).join("/");n&&(n.endsWith("/page")||n.endsWith("/route")||(n=`${n}${!n.endsWith("/")?"/":""}layout`),t.push(n))}}return t};async function eY(e,t,r){let n=[],i=r&&r.size>0;for(let t of eQ(e))t=`${g}${t}`,n.push(t);if(t.pathname&&!i){let e=`${g}${t.pathname}`;n.push(e)}return{tags:n,expirationsByCacheKind:function(e){let t=new Map,r=eL();if(r)for(let[n,i]of r)"getExpiration"in i&&t.set(n,eF(async()=>i.getExpiration(...e)));return t}(n)}}class eZ extends L{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}respondWith(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}waitUntil(){throw Object.defineProperty(new h({page:this.sourcePage}),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}let e0={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},e1=(e,t)=>eT().withPropagatedContext(e.headers,t,e0),e2=!1;async function e5(e){var t;let n,i;if(!e2&&(e2=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(905);e(),e1=t(e1)}await u();let s=void 0!==globalThis.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new M(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e),r=function(e){for(let t of["nxtP","nxtI"])if(e!==t&&e.startsWith(t))return e.substring(t.length);return null}(e);if(r){for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)}}let o=a.buildId;a.buildId="";let l=function(e){let t=new Headers;for(let[r,n]of Object.entries(e))for(let e of Array.isArray(n)?n:[n])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}(e.request.headers),c=l.has("x-nextjs-data"),d="1"===l.get("RSC");c&&"/index"===a.pathname&&(a.pathname="/");let h=new Map;if(!s)for(let e of W){let t=e.toLowerCase(),r=l.get(t);null!==r&&(h.set(t,r),l.delete(t))}let p=new eZ({page:e.page,input:(function(e){let t="string"==typeof e,r=t?new URL(e):e;return r.searchParams.delete(F),t?r.toString():r})(a).toString(),init:{body:e.request.body,headers:l,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});c&&Object.defineProperty(p,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCache&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:eG()})}));let f=e.request.waitUntil??(null==(t=function(){let e=globalThis[eX];return null==e?void 0:e.get()}())?void 0:t.waitUntil),g=new E({request:p,page:e.page,context:f?{waitUntil:f}:void 0});if((n=await e1(p,()=>{if("/middleware"===e.page||"/src/middleware"===e.page){let t=g.waitUntil.bind(g),r=new eV;return eT().trace(eo.execute,{spanName:`middleware ${p.method} ${p.nextUrl.pathname}`,attributes:{"http.target":p.nextUrl.pathname,"http.method":p.method}},async()=>{try{var n,s,a,l,c,u;let d=eG(),h=await eY("/",p.nextUrl,null),f=(c=p.nextUrl,u=e=>{i=e},function(e,t,r,n,i,s,a,o,l,c,u){function d(e){r&&r.setHeader("Set-Cookie",e)}let h={};return{type:"request",phase:e,implicitTags:s,url:{pathname:n.pathname,search:n.search??""},rootParams:i,get headers(){return h.headers||(h.headers=function(e){let t=V.o.from(e);for(let e of W)t.delete(e.toLowerCase());return V.o.seal(t)}(t.headers)),h.headers},get cookies(){if(!h.cookies){let e=new j.tm(V.o.from(t.headers));eC(t,e),h.cookies=G.Ck.seal(e)}return h.cookies},set cookies(value){h.cookies=value},get mutableCookies(){if(!h.mutableCookies){let e=function(e,t){let r=new j.tm(V.o.from(e));return G.K8.wrap(r,t)}(t.headers,a||(r?d:void 0));eC(t,e),h.mutableCookies=e}return h.mutableCookies},get userspaceMutableCookies(){return h.userspaceMutableCookies||(h.userspaceMutableCookies=(0,G.hm)(this.mutableCookies)),h.userspaceMutableCookies},get draftMode(){return h.draftMode||(h.draftMode=new eO(l,t,this.cookies,this.mutableCookies)),h.draftMode},renderResumeDataCache:o??null,isHmrRefresh:c,serverComponentsHmrCache:u||globalThis.__serverComponentsHmrCache}}("action",p,void 0,c,{},h,u,void 0,d,!1,void 0)),m=function({page:e,fallbackRouteParams:t,renderOpts:r,requestEndedState:n,isPrefetchRequest:i,buildId:s,previouslyRevalidatedTags:a}){var o;let l={isStaticGeneration:!r.shouldWaitOnAllReady&&!r.supportsDynamicResponse&&!r.isDraftMode&&!r.isPossibleServerAction,page:e,fallbackRouteParams:t,route:(o=e.split("/").reduce((e,t,r,n)=>t?"("===t[0]&&t.endsWith(")")||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t:e,"")).startsWith("/")?o:"/"+o,incrementalCache:r.incrementalCache||globalThis.__incrementalCache,cacheLifeProfiles:r.cacheLifeProfiles,isRevalidate:r.isRevalidate,isPrerendering:r.nextExport,fetchCache:r.fetchCache,isOnDemandRevalidate:r.isOnDemandRevalidate,isDraftMode:r.isDraftMode,requestEndedState:n,isPrefetchRequest:i,buildId:s,reactLoadableManifest:(null==r?void 0:r.reactLoadableManifest)||{},assetPrefix:(null==r?void 0:r.assetPrefix)||"",afterContext:function(e){let{waitUntil:t,onClose:r,onAfterTaskError:n}=e;return new eJ({waitUntil:t,onClose:r,onTaskError:n})}(r),dynamicIOEnabled:r.experimental.dynamicIO,dev:r.dev??!1,previouslyRevalidatedTags:a,refreshTagsByCacheKind:function(){let e=new Map,t=eL();if(t)for(let[r,n]of t)"refreshTags"in n&&e.set(r,eF(async()=>n.refreshTags()));return e}()};return r.store=l,l}({page:"/",fallbackRouteParams:null,renderOpts:{cacheLifeProfiles:null==(s=e.request.nextConfig)||null==(n=s.experimental)?void 0:n.cacheLife,experimental:{isRoutePPREnabled:!1,dynamicIO:!1,authInterrupts:!!(null==(l=e.request.nextConfig)||null==(a=l.experimental)?void 0:a.authInterrupts)},supportsDynamicResponse:!0,waitUntil:t,onClose:r.onClose.bind(r),onAfterTaskError:void 0},requestEndedState:{ended:!1},isPrefetchRequest:p.headers.has(J),buildId:o??"",previouslyRevalidatedTags:[]});return await eN.J.run(m,()=>eR.FP.run(f,e.handler,p,g))}finally{setTimeout(()=>{r.dispatchClose()},0)}})}return e.handler(p,g)}))&&!(n instanceof Response))throw Object.defineProperty(TypeError("Expected an instance of Response to be returned"),"__NEXT_ERROR_CODE",{value:"E567",enumerable:!1,configurable:!0});n&&i&&n.headers.set("set-cookie",i);let m=null==n?void 0:n.headers.get("x-middleware-rewrite");if(n&&m&&(d||!s)){let t=new M(m,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});s||t.host!==p.nextUrl.host||(t.buildId=o||t.buildId,n.headers.set("x-middleware-rewrite",String(t)));let{url:r,isRelative:i}=K(t.toString(),a.toString());!s&&c&&n.headers.set("x-nextjs-rewrite",r),d&&i&&(a.pathname!==t.pathname&&n.headers.set("x-nextjs-rewritten-path",t.pathname),a.search!==t.search&&n.headers.set("x-nextjs-rewritten-query",t.search.slice(1)))}let y=null==n?void 0:n.headers.get("Location");if(n&&y&&!s){let t=new M(y,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});n=new Response(n.body,n),t.host===a.host&&(t.buildId=o||t.buildId,n.headers.set("Location",t.toString())),c&&(n.headers.delete("Location"),n.headers.set("x-nextjs-redirect",K(t.toString(),a.toString()).url))}let b=n||z.next(),_=b.headers.get("x-middleware-override-headers"),v=[];if(_){for(let[e,t]of h)b.headers.set(`x-middleware-request-${e}`,t),v.push(e);v.length>0&&b.headers.set("x-middleware-override-headers",_+","+v.join(","))}return{response:b,waitUntil:("internal"===g[k].kind?Promise.all(g[k].promises).then(()=>{}):void 0)??Promise.resolve(),fetchMetrics:p.fetchMetrics}}function e4(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function e3(e){return e&&e.sensitive?"":"i"}function e6(e,t,r){var n;return e instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,i=r.exec(e.source);i;)t.push({name:i[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),i=r.exec(e.source);return e}(e,t):Array.isArray(e)?(n=e.map(function(e){return e6(e,t,r).source}),new RegExp("(?:".concat(n.join("|"),")"),e3(r))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,a=r.end,o=r.encode,l=void 0===o?function(e){return e}:o,c=r.delimiter,u=r.endsWith,d="[".concat(e4(void 0===u?"":u),"]|$"),h="[".concat(e4(void 0===c?"/#?":c),"]"),p=void 0===s||s?"^":"",f=0;f<e.length;f++){var g=e[f];if("string"==typeof g)p+=e4(l(g));else{var m=e4(l(g.prefix)),y=e4(l(g.suffix));if(g.pattern)if(t&&t.push(g),m||y)if("+"===g.modifier||"*"===g.modifier){var b="*"===g.modifier?"?":"";p+="(?:".concat(m,"((?:").concat(g.pattern,")(?:").concat(y).concat(m,"(?:").concat(g.pattern,"))*)").concat(y,")").concat(b)}else p+="(?:".concat(m,"(").concat(g.pattern,")").concat(y,")").concat(g.modifier);else{if("+"===g.modifier||"*"===g.modifier)throw TypeError('Can not repeat "'.concat(g.name,'" without a prefix and suffix'));p+="(".concat(g.pattern,")").concat(g.modifier)}else p+="(?:".concat(m).concat(y,")").concat(g.modifier)}}if(void 0===a||a)i||(p+="".concat(h,"?")),p+=r.endsWith?"(?=".concat(d,")"):"$";else{var _=e[e.length-1],v="string"==typeof _?h.indexOf(_[_.length-1])>-1:void 0===_;i||(p+="(?:".concat(h,"(?=").concat(d,"))?")),v||(p+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(p,e3(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var i="",s=r+1;s<e.length;){var a=e.charCodeAt(s);if(a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122||95===a){i+=e[s++];continue}break}if(!i)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:i}),r=s;continue}if("("===n){var o=1,l="",s=r+1;if("?"===e[s])throw TypeError('Pattern cannot start with "?" at '.concat(s));for(;s<e.length;){if("\\"===e[s]){l+=e[s++]+e[s++];continue}if(")"===e[s]){if(0==--o){s++;break}}else if("("===e[s]&&(o++,"?"!==e[s+1]))throw TypeError("Capturing groups are not allowed at ".concat(s));l+=e[s++]}if(o)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=s;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,i=void 0===n?"./":n,s=t.delimiter,a=void 0===s?"/#?":s,o=[],l=0,c=0,u="",d=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},h=function(e){var t=d(e);if(void 0!==t)return t;var n=r[c],i=n.type,s=n.index;throw TypeError("Unexpected ".concat(i," at ").concat(s,", expected ").concat(e))},p=function(){for(var e,t="";e=d("CHAR")||d("ESCAPED_CHAR");)t+=e;return t},f=function(e){for(var t=0;t<a.length;t++){var r=a[t];if(e.indexOf(r)>-1)return!0}return!1},g=function(e){var t=o[o.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||f(r)?"[^".concat(e4(a),"]+?"):"(?:(?!".concat(e4(r),")[^").concat(e4(a),"])+?")};c<r.length;){var m=d("CHAR"),y=d("NAME"),b=d("PATTERN");if(y||b){var _=m||"";-1===i.indexOf(_)&&(u+=_,_=""),u&&(o.push(u),u=""),o.push({name:y||l++,prefix:_,suffix:"",pattern:b||g(_),modifier:d("MODIFIER")||""});continue}var v=m||d("ESCAPED_CHAR");if(v){u+=v;continue}if(u&&(o.push(u),u=""),d("OPEN")){var _=p(),w=d("NAME")||"",k=d("PATTERN")||"",S=p();h("CLOSE"),o.push({name:w||(k?l++:""),pattern:w&&!k?g(_):k,prefix:_,suffix:S,modifier:d("MODIFIER")||""});continue}h("END")}return o}(e,r),t,r)}var e8=e=>{try{return e6(e)}catch(t){throw Error(`Invalid path: ${e}.
Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${t.message}`)}},e9=e=>e.map(e=>e instanceof RegExp?e:e8(e)),e7=e=>{let t=e9([e||""].flat().filter(Boolean));return e=>t.some(t=>t.test(e))},te=Object.defineProperty,tt=Object.getOwnPropertyDescriptor,tr=Object.getOwnPropertyNames,tn=Object.prototype.hasOwnProperty,ti=e=>{throw TypeError(e)},ts=(e,t,r)=>t.has(e)||ti("Cannot "+r),ta=(e,t,r)=>(ts(e,t,"read from private field"),r?r.call(e):t.get(e)),to=(e,t,r)=>t.has(e)?ti("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),tl=(e,t,r,n)=>(ts(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),tc=(e,t,r)=>(ts(e,t,"access private method"),r),tu={initialDelay:125,maxDelayBetweenRetries:0,factor:2,shouldRetry:(e,t)=>t<5,retryImmediately:!1,jitter:!0},td=async e=>new Promise(t=>setTimeout(t,e)),th=(e,t)=>t?e*(1+Math.random()):e,tp=e=>{let t=0,r=()=>{let r=e.initialDelay*Math.pow(e.factor,t);return r=th(r,e.jitter),Math.min(e.maxDelayBetweenRetries||r,r)};return async()=>{await td(r()),t++}},tf=async(e,t={})=>{let r=0,{shouldRetry:n,initialDelay:i,maxDelayBetweenRetries:s,factor:a,retryImmediately:o,jitter:l}={...tu,...t},c=tp({initialDelay:i,maxDelayBetweenRetries:s,factor:a,jitter:l});for(;;)try{return await e()}catch(e){if(!n(e,++r))throw e;o&&1===r?await td(th(100,l)):await c()}},tg=e=>"undefined"!=typeof atob&&"function"==typeof atob?atob(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e,"base64").toString():e,tm=e=>"undefined"!=typeof btoa&&"function"==typeof btoa?btoa(e):"undefined"!=typeof global&&global.Buffer?new global.Buffer(e).toString("base64"):e,ty=[".lcl.dev",".lclstage.dev",".lclclerk.com"],tb=[".lcl.dev",".stg.dev",".lclstage.dev",".stgstage.dev",".dev.lclclerk.com",".stg.lclclerk.com",".accounts.lclclerk.com","accountsstage.dev","accounts.dev"],t_=[".lcl.dev","lclstage.dev",".lclclerk.com",".accounts.lclclerk.com"],tv=[".accountsstage.dev"],tw="https://api.clerk.com",tk="pk_live_";function tS(e){if(!e.endsWith("$"))return!1;let t=e.slice(0,-1);return!t.includes("$")&&t.includes(".")}function tE(e,t={}){let r;if(!(e=e||"")||!tT(e)){if(t.fatal&&!e)throw Error("Publishable key is missing. Ensure that your publishable key is correctly configured. Double-check your environment configuration for your keys, or access them here: https://dashboard.clerk.com/last-active?path=api-keys");if(t.fatal&&!tT(e))throw Error("Publishable key not valid.");return null}let n=e.startsWith(tk)?"production":"development";try{r=tg(e.split("_")[2])}catch{if(t.fatal)throw Error("Publishable key not valid: Failed to decode key.");return null}if(!tS(r)){if(t.fatal)throw Error("Publishable key not valid: Decoded key has invalid format.");return null}let i=r.slice(0,-1);return t.proxyUrl?i=t.proxyUrl:"development"!==n&&t.domain&&t.isSatellite&&(i=`clerk.${t.domain}`),{instanceType:n,frontendApi:i}}function tT(e=""){try{if(!(e.startsWith(tk)||e.startsWith("pk_test_")))return!1;let t=e.split("_");if(3!==t.length)return!1;let r=t[2];if(!r)return!1;let n=tg(r);return tS(n)}catch{return!1}}function tx(e){return e.startsWith("test_")||e.startsWith("sk_test_")}async function tO(e,t=globalThis.crypto.subtle){let r=new TextEncoder().encode(e);return tm(String.fromCharCode(...new Uint8Array(await t.digest("sha-1",r)))).replace(/\+/gi,"-").replace(/\//gi,"_").substring(0,8)}var tC=(e,t)=>`${e}_${t}`,tR=()=>!1,tP=()=>{try{return!0}catch{}return!1},tI=new Set,tA=(e,t,r)=>{let n=tR()||tP(),i=r??e;tI.has(i)||n||(tI.add(i),console.warn(`Clerk - DEPRECATION WARNING: "${e}" is deprecated and will be removed in the next major release.
${t}`))};function tN(e){return{code:e.code,message:e.message,longMessage:e.long_message,meta:{paramName:e?.meta?.param_name,sessionId:e?.meta?.session_id,emailAddresses:e?.meta?.email_addresses,identifiers:e?.meta?.identifiers,zxcvbn:e?.meta?.zxcvbn,plan:e?.meta?.plan,isPlanUpgradePossible:e?.meta?.is_plan_upgrade_possible}}}var tU=class e extends Error{constructor(t,{data:r,status:n,clerkTraceId:i,retryAfter:s}){super(t),this.toString=()=>{let e=`[${this.name}]
Message:${this.message}
Status:${this.status}
Serialized errors: ${this.errors.map(e=>JSON.stringify(e))}`;return this.clerkTraceId&&(e+=`
Clerk Trace ID: ${this.clerkTraceId}`),e},Object.setPrototypeOf(this,e.prototype),this.status=n,this.message=t,this.clerkTraceId=i,this.retryAfter=s,this.clerkError=!0,this.errors=function(e=[]){return e.length>0?e.map(tN):[]}(r)}},tM=Object.freeze({InvalidProxyUrlErrorMessage:"The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})",InvalidPublishableKeyErrorMessage:"The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})",MissingPublishableKeyErrorMessage:"Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingSecretKeyErrorMessage:"Missing secretKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.",MissingClerkProvider:"{{source}} can only be used within the <ClerkProvider /> component. Learn more: https://clerk.com/docs/components/clerk-provider"});function tj({packageName:e,customMessages:t}){let r=e;function n(e,t){if(!t)return`${r}: ${e}`;let n=e;for(let r of e.matchAll(/{{([a-zA-Z0-9-_]+)}}/g)){let e=(t[r[1]]||"").toString();n=n.replace(`{{${r[1]}}}`,e)}return`${r}: ${n}`}let i={...tM,...t};return{setPackageName({packageName:e}){return"string"==typeof e&&(r=e),this},setMessages({customMessages:e}){return Object.assign(i,e||{}),this},throwInvalidPublishableKeyError(e){throw Error(n(i.InvalidPublishableKeyErrorMessage,e))},throwInvalidProxyUrl(e){throw Error(n(i.InvalidProxyUrlErrorMessage,e))},throwMissingPublishableKeyError(){throw Error(n(i.MissingPublishableKeyErrorMessage))},throwMissingSecretKeyError(){throw Error(n(i.MissingSecretKeyErrorMessage))},throwMissingClerkProviderError(e){throw Error(n(i.MissingClerkProvider,e))},throw(e){throw Error(n(e))}}}var tD=tj({packageName:"@clerk/backend"}),{isDevOrStagingUrl:tL}=function(){let e=new Map;return{isDevOrStagingUrl:t=>{if(!t)return!1;let r="string"==typeof t?t:t.hostname,n=e.get(r);return void 0===n&&(n=tb.some(e=>r.endsWith(e)),e.set(r,n)),n}}}(),tq={InvalidSecretKey:"clerk_key_invalid"},tB={TokenExpired:"token-expired",TokenInvalid:"token-invalid",TokenInvalidAlgorithm:"token-invalid-algorithm",TokenInvalidAuthorizedParties:"token-invalid-authorized-parties",TokenInvalidSignature:"token-invalid-signature",TokenNotActiveYet:"token-not-active-yet",TokenIatInTheFuture:"token-iat-in-the-future",TokenVerificationFailed:"token-verification-failed",InvalidSecretKey:"secret-key-invalid",LocalJWKMissing:"jwk-local-missing",RemoteJWKFailedToLoad:"jwk-remote-failed-to-load",JWKFailedToResolve:"jwk-failed-to-resolve",JWKKidMismatch:"jwk-kid-mismatch"},tH={ContactSupport:"Contact <EMAIL>",EnsureClerkJWT:"Make sure that this is a valid Clerk generate JWT.",SetClerkJWTKey:"Set the CLERK_JWT_KEY environment variable.",SetClerkSecretKey:"Set the CLERK_SECRET_KEY environment variable."},t$=class e extends Error{constructor({action:t,message:r,reason:n}){super(r),Object.setPrototypeOf(this,e.prototype),this.reason=n,this.message=r,this.action=t}getFullMessage(){return`${[this.message,this.action].filter(e=>e).join(" ")} (reason=${this.reason}, token-carrier=${this.tokenCarrier})`}},tz={TokenInvalid:"token-invalid",InvalidSecretKey:"secret-key-invalid",UnexpectedError:"unexpected-error"},tK=class e extends Error{constructor({message:t,code:r,status:n}){super(t),Object.setPrototypeOf(this,e.prototype),this.code=r,this.status=n}getFullMessage(){return`${this.message} (code=${this.code}, status=${this.status})`}};let tJ=crypto;var tW=fetch.bind(globalThis),tF={crypto:tJ,get fetch(){return tW},AbortController:globalThis.AbortController,Blob:globalThis.Blob,FormData:globalThis.FormData,Headers:globalThis.Headers,Request:globalThis.Request,Response:globalThis.Response},tV={parse:(e,t)=>(function(e,t,r={}){if(!t.codes){t.codes={};for(let e=0;e<t.chars.length;++e)t.codes[t.chars[e]]=e}if(!r.loose&&e.length*t.bits&7)throw SyntaxError("Invalid padding");let n=e.length;for(;"="===e[n-1];)if(--n,!r.loose&&!((e.length-n)*t.bits&7))throw SyntaxError("Invalid padding");let i=new(r.out??Uint8Array)(n*t.bits/8|0),s=0,a=0,o=0;for(let r=0;r<n;++r){let n=t.codes[e[r]];if(void 0===n)throw SyntaxError("Invalid character "+e[r]);a=a<<t.bits|n,(s+=t.bits)>=8&&(s-=8,i[o++]=255&a>>s)}if(s>=t.bits||255&a<<8-s)throw SyntaxError("Unexpected end of data");return i})(e,tG,t)},tG={chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_",bits:6},tX={RS256:"SHA-256",RS384:"SHA-384",RS512:"SHA-512"},tQ="RSASSA-PKCS1-v1_5",tY={RS256:tQ,RS384:tQ,RS512:tQ},tZ=Object.keys(tX),t0=e=>Array.isArray(e)&&e.length>0&&e.every(e=>"string"==typeof e),t1=(e,t)=>{let r=[t].flat().filter(e=>!!e),n=[e].flat().filter(e=>!!e);if(r.length>0&&n.length>0){if("string"==typeof e){if(!r.includes(e))throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenVerificationFailed,message:`Invalid JWT audience claim (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}else if(t0(e)&&!e.some(e=>r.includes(e)))throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenVerificationFailed,message:`Invalid JWT audience claim array (aud) ${JSON.stringify(e)}. Is not included in "${JSON.stringify(r)}".`})}},t2=e=>{if(void 0!==e&&"JWT"!==e)throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenInvalid,message:`Invalid JWT type ${JSON.stringify(e)}. Expected "JWT".`})},t5=e=>{if(!tZ.includes(e))throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenInvalidAlgorithm,message:`Invalid JWT algorithm ${JSON.stringify(e)}. Supported: ${tZ}.`})},t4=e=>{if("string"!=typeof e)throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenVerificationFailed,message:`Subject claim (sub) is required and must be a string. Received ${JSON.stringify(e)}.`})},t3=(e,t)=>{if(e&&t&&0!==t.length&&!t.includes(e))throw new t$({reason:tB.TokenInvalidAuthorizedParties,message:`Invalid JWT Authorized party claim (azp) ${JSON.stringify(e)}. Expected "${t}".`})},t6=(e,t)=>{if("number"!=typeof e)throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenVerificationFailed,message:`Invalid JWT expiry date claim (exp) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()<=r.getTime()-t)throw new t$({reason:tB.TokenExpired,message:`JWT is expired. Expiry date: ${n.toUTCString()}, Current date: ${r.toUTCString()}.`})},t8=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenVerificationFailed,message:`Invalid JWT not before date claim (nbf) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new t$({reason:tB.TokenNotActiveYet,message:`JWT cannot be used prior to not before date claim (nbf). Not before date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})},t9=(e,t)=>{if(void 0===e)return;if("number"!=typeof e)throw new t$({action:tH.EnsureClerkJWT,reason:tB.TokenVerificationFailed,message:`Invalid JWT issued at date claim (iat) ${JSON.stringify(e)}. Expected number.`});let r=new Date(Date.now()),n=new Date(0);if(n.setUTCSeconds(e),n.getTime()>r.getTime()+t)throw new t$({reason:tB.TokenIatInTheFuture,message:`JWT issued at date claim (iat) is in the future. Issued at date: ${n.toUTCString()}; Current date: ${r.toUTCString()};`})};async function t7(e,t){let{header:r,signature:n,raw:i}=e,s=new TextEncoder().encode([i.header,i.payload].join(".")),a=function(e){let t=tX[e],r=tY[e];if(!t||!r)throw Error(`Unsupported algorithm ${e}, expected one of ${tZ.join(",")}.`);return{hash:{name:tX[e]},name:tY[e]}}(r.alg);try{let e=await function(e,t,r){if("object"==typeof e)return tF.crypto.subtle.importKey("jwk",e,t,!1,[r]);let n=function(e){let t=tg(e.replace(/-----BEGIN.*?-----/g,"").replace(/-----END.*?-----/g,"").replace(/\s/g,"")),r=new Uint8Array(new ArrayBuffer(t.length));for(let e=0,n=t.length;e<n;e++)r[e]=t.charCodeAt(e);return r}(e),i="sign"===r?"pkcs8":"spki";return tF.crypto.subtle.importKey(i,n,t,!1,[r])}(t,a,"verify");return{data:await tF.crypto.subtle.verify(a.name,e,n,s)}}catch(e){return{errors:[new t$({reason:tB.TokenInvalidSignature,message:e?.message})]}}}function re(e){let t=(e||"").toString().split(".");if(3!==t.length)return{errors:[new t$({reason:tB.TokenInvalid,message:"Invalid JWT form. A JWT consists of three parts separated by dots."})]};let[r,n,i]=t,s=new TextDecoder,a=JSON.parse(s.decode(tV.parse(r,{loose:!0}))),o=JSON.parse(s.decode(tV.parse(n,{loose:!0})));return{data:{header:a,payload:o,signature:tV.parse(i,{loose:!0}),raw:{header:r,payload:n,signature:i,text:e}}}}async function rt(e,t){let{audience:r,authorizedParties:n,clockSkewInMs:i,key:s}=t,a=i||5e3,{data:o,errors:l}=re(e);if(l)return{errors:l};let{header:c,payload:u}=o;try{let{typ:e,alg:t}=c;t2(e),t5(t);let{azp:i,sub:s,aud:o,iat:l,exp:d,nbf:h}=u;t4(s),t1([o],[r]),t3(i,n),t6(d,a),t8(h,a),t9(l,a)}catch(e){return{errors:[e]}}let{data:d,errors:h}=await t7(o,s);return h?{errors:[new t$({action:tH.EnsureClerkJWT,reason:tB.TokenVerificationFailed,message:`Error verifying JWT signature. ${h[0]}`})]}:d?{data:u}:{errors:[new t$({reason:tB.TokenInvalidSignature,message:"JWT signature is invalid."})]}}var rr={strict_mfa:{afterMinutes:10,level:"multi_factor"},strict:{afterMinutes:10,level:"second_factor"},moderate:{afterMinutes:60,level:"second_factor"},lax:{afterMinutes:1440,level:"second_factor"}},rn=new Set(["first_factor","second_factor","multi_factor"]),ri=new Set(["strict_mfa","strict","moderate","lax"]),rs=e=>"number"==typeof e&&e>0,ra=e=>rn.has(e),ro=e=>ri.has(e),rl=e=>e.replace(/^(org:)*/,"org:"),rc=(e,t)=>{let{orgId:r,orgRole:n,orgPermissions:i}=t;return(e.role||e.permission)&&r&&n&&i?e.permission?i.includes(rl(e.permission)):e.role?rl(n)===rl(e.role):null:null},ru=(e,t)=>{let{org:r,user:n}=rh(e),[i,s]=t.split(":"),a=s||i;return"org"===i?r.includes(a):"user"===i?n.includes(a):[...r,...n].includes(a)},rd=(e,t)=>{let{features:r,plans:n}=t;return e.feature&&r?ru(r,e.feature):e.plan&&n?ru(n,e.plan):null},rh=e=>{let t=e?e.split(",").map(e=>e.trim()):[];return{org:t.filter(e=>e.split(":")[0].includes("o")).map(e=>e.split(":")[1]),user:t.filter(e=>e.split(":")[0].includes("u")).map(e=>e.split(":")[1])}},rp=e=>{if(!e)return!1;let t="string"==typeof e&&ro(e),r="object"==typeof e&&ra(e.level)&&rs(e.afterMinutes);return(!!t||!!r)&&(e=>"string"==typeof e?rr[e]:e).bind(null,e)},rf=(e,{factorVerificationAge:t})=>{if(!e.reverification||!t)return null;let r=rp(e.reverification);if(!r)return null;let{level:n,afterMinutes:i}=r(),[s,a]=t,o=-1!==s?i>s:null,l=-1!==a?i>a:null;switch(n){case"first_factor":return o;case"second_factor":return -1!==a?l:o;case"multi_factor":return -1===a?o:o&&l}},rg=e=>t=>{if(!e.userId)return!1;let r=rd(t,e),n=rc(t,e),i=rf(t,e);return[r||n,i].some(e=>null===e)?[r||n,i].some(e=>!0===e):[r||n,i].every(e=>!0===e)},rm=({per:e,fpm:t})=>{if(!e||!t)return{permissions:[],featurePermissionMap:[]};let r=e.split(",").map(e=>e.trim()),n=t.split(",").map(e=>Number.parseInt(e.trim(),10)).map(e=>e.toString(2).padStart(r.length,"0").split("").map(e=>Number.parseInt(e,10)).reverse()).filter(Boolean);return{permissions:r,featurePermissionMap:n}},ry=e=>{let t,r,n,i,s=e.fva??null,a=e.sts??null;if(2===e.v){if(e.o){t=e.o?.id,n=e.o?.slg,e.o?.rol&&(r=`org:${e.o?.rol}`);let{org:s}=rh(e.fea),{permissions:a,featurePermissionMap:o}=rm({per:e.o?.per,fpm:e.o?.fpm});i=function({features:e,permissions:t,featurePermissionMap:r}){if(!e||!t||!r)return[];let n=[];for(let i=0;i<e.length;i++){let s=e[i];if(i>=r.length)continue;let a=r[i];if(a)for(let e=0;e<a.length;e++)1===a[e]&&n.push(`org:${s}:${t[e]}`)}return n}({features:s,featurePermissionMap:o,permissions:a})}}else t=e.org_id,r=e.org_role,n=e.org_slug,i=e.org_permissions;return{sessionClaims:e,sessionId:e.sid,sessionStatus:a,actor:e.act,userId:e.sub,orgId:t,orgRole:r,orgSlug:n,orgPermissions:i,factorVerificationAge:s}},rb=r(554),r_="https://api.clerk.com",rv="@clerk/backend@2.6.3",rw="2025-04-10",rk={Session:"__session",Refresh:"__refresh",ClientUat:"__client_uat",Handshake:"__clerk_handshake",DevBrowser:"__clerk_db_jwt",RedirectCount:"__clerk_redirect_count",HandshakeNonce:"__clerk_handshake_nonce"},rS={ClerkSynced:"__clerk_synced",SuffixedCookies:"suffixed_cookies",ClerkRedirectUrl:"__clerk_redirect_url",DevBrowser:rk.DevBrowser,Handshake:rk.Handshake,HandshakeHelp:"__clerk_help",LegacyDevBrowser:"__dev_session",HandshakeReason:"__clerk_hs_reason",HandshakeNonce:rk.HandshakeNonce,HandshakeFormat:"format"},rE={Cookies:rk,Headers:{Accept:"accept",AuthMessage:"x-clerk-auth-message",Authorization:"authorization",AuthReason:"x-clerk-auth-reason",AuthSignature:"x-clerk-auth-signature",AuthStatus:"x-clerk-auth-status",AuthToken:"x-clerk-auth-token",CacheControl:"cache-control",ClerkRedirectTo:"x-clerk-redirect-to",ClerkRequestData:"x-clerk-request-data",ClerkUrl:"x-clerk-clerk-url",CloudFrontForwardedProto:"cloudfront-forwarded-proto",ContentType:"content-type",ContentSecurityPolicy:"content-security-policy",ContentSecurityPolicyReportOnly:"content-security-policy-report-only",EnableDebug:"x-clerk-debug",ForwardedHost:"x-forwarded-host",ForwardedPort:"x-forwarded-port",ForwardedProto:"x-forwarded-proto",Host:"host",Location:"location",Nonce:"x-nonce",Origin:"origin",Referrer:"referer",SecFetchDest:"sec-fetch-dest",SecFetchSite:"sec-fetch-site",UserAgent:"user-agent",ReportingEndpoints:"reporting-endpoints"},ContentTypes:{Json:"application/json"},QueryParameters:rS},rT=(e,t,r,n)=>{if(""===e)return rx(t.toString(),r?.toString());let i=new URL(e),s=r?new URL(r,i):void 0,a=new URL(t,i),o=`${i.hostname}:${i.port}`!=`${a.hostname}:${a.port}`;return s&&(o&&s.searchParams.delete(rE.QueryParameters.ClerkSynced),a.searchParams.set("redirect_url",s.toString())),o&&n&&a.searchParams.set(rE.QueryParameters.DevBrowser,n),a.toString()},rx=(e,t)=>{let r;if(e.startsWith("http"))r=new URL(e);else{if(!t||!t.startsWith("http"))throw Error("destination url or return back url should be an absolute path url!");let n=new URL(t);r=new URL(e,n.origin)}return t&&r.searchParams.set("redirect_url",t),r.toString()},rO=e=>{let{publishableKey:t,redirectAdapter:r,signInUrl:n,signUpUrl:i,baseUrl:s,sessionStatus:a}=e,o=tE(t),l=o?.frontendApi,c=o?.instanceType==="development",u=function(e){if(!e)return"";let t=e.replace(/clerk\.accountsstage\./,"accountsstage.").replace(/clerk\.accounts\.|clerk\./,"accounts.");return`https://${t}`}(l),d="pending"===a,h=(t,{returnBackUrl:n})=>r(rT(s,`${t}/tasks`,n,c?e.devBrowserToken:null));return{redirectToSignUp:({returnBackUrl:t}={})=>{i||u||tD.throwMissingPublishableKeyError();let a=`${u}/sign-up`,o=i||function(e){if(!e)return;let t=new URL(e,s);return t.pathname=`${t.pathname}/create`,t.toString()}(n)||a;return d?h(o,{returnBackUrl:t}):r(rT(s,o,t,c?e.devBrowserToken:null))},redirectToSignIn:({returnBackUrl:t}={})=>{n||u||tD.throwMissingPublishableKeyError();let i=`${u}/sign-in`,a=n||i;return d?h(a,{returnBackUrl:t}):r(rT(s,a,t,c?e.devBrowserToken:null))}}};function rC(e,t){return Object.keys(e).reduce((e,r)=>({...e,[r]:t[r]||e[r]}),{...e})}function rR(e){if(!e||"string"!=typeof e)throw Error("Missing Clerk Secret Key. Go to https://dashboard.clerk.com and get your key for your instance.")}var rP=class{constructor(e,t,r){this.cookieSuffix=e,this.clerkRequest=t,this.originalFrontendApi="",this.initPublishableKeyValues(r),this.initHeaderValues(),this.initCookieValues(),this.initHandshakeValues(),Object.assign(this,r),this.clerkUrl=this.clerkRequest.clerkUrl}get sessionToken(){return this.sessionTokenInCookie||this.tokenInHeader}usesSuffixedCookies(){let e=this.getSuffixedCookie(rE.Cookies.ClientUat),t=this.getCookie(rE.Cookies.ClientUat),r=this.getSuffixedCookie(rE.Cookies.Session)||"",n=this.getCookie(rE.Cookies.Session)||"";if(n&&!this.tokenHasIssuer(n))return!1;if(n&&!this.tokenBelongsToInstance(n))return!0;if(!e&&!r)return!1;let{data:i}=re(n),s=i?.payload.iat||0,{data:a}=re(r),o=a?.payload.iat||0;if("0"!==e&&"0"!==t&&s>o||"0"===e&&"0"!==t)return!1;if("production"!==this.instanceType){let r=this.sessionExpired(a);if("0"!==e&&"0"===t&&r)return!1}return!!e||!r}isCrossOriginReferrer(){if(!this.referrer||!this.clerkUrl.origin)return!1;try{if("cross-site"===this.getHeader(rE.Headers.SecFetchSite))return!0;return new URL(this.referrer).origin!==this.clerkUrl.origin}catch{return!1}}initPublishableKeyValues(e){tE(e.publishableKey,{fatal:!0}),this.publishableKey=e.publishableKey;let t=tE(this.publishableKey,{fatal:!0,domain:e.domain,isSatellite:e.isSatellite});this.originalFrontendApi=t.frontendApi;let r=tE(this.publishableKey,{fatal:!0,proxyUrl:e.proxyUrl,domain:e.domain,isSatellite:e.isSatellite});this.instanceType=r.instanceType,this.frontendApi=r.frontendApi}initHeaderValues(){this.tokenInHeader=this.parseAuthorizationHeader(this.getHeader(rE.Headers.Authorization)),this.origin=this.getHeader(rE.Headers.Origin),this.host=this.getHeader(rE.Headers.Host),this.forwardedHost=this.getHeader(rE.Headers.ForwardedHost),this.forwardedProto=this.getHeader(rE.Headers.CloudFrontForwardedProto)||this.getHeader(rE.Headers.ForwardedProto),this.referrer=this.getHeader(rE.Headers.Referrer),this.userAgent=this.getHeader(rE.Headers.UserAgent),this.secFetchDest=this.getHeader(rE.Headers.SecFetchDest),this.accept=this.getHeader(rE.Headers.Accept)}initCookieValues(){this.sessionTokenInCookie=this.getSuffixedOrUnSuffixedCookie(rE.Cookies.Session),this.refreshTokenInCookie=this.getSuffixedCookie(rE.Cookies.Refresh),this.clientUat=Number.parseInt(this.getSuffixedOrUnSuffixedCookie(rE.Cookies.ClientUat)||"")||0}initHandshakeValues(){this.devBrowserToken=this.getQueryParam(rE.QueryParameters.DevBrowser)||this.getSuffixedOrUnSuffixedCookie(rE.Cookies.DevBrowser),this.handshakeToken=this.getQueryParam(rE.QueryParameters.Handshake)||this.getCookie(rE.Cookies.Handshake),this.handshakeRedirectLoopCounter=Number(this.getCookie(rE.Cookies.RedirectCount))||0,this.handshakeNonce=this.getQueryParam(rE.QueryParameters.HandshakeNonce)||this.getCookie(rE.Cookies.HandshakeNonce)}getQueryParam(e){return this.clerkRequest.clerkUrl.searchParams.get(e)}getHeader(e){return this.clerkRequest.headers.get(e)||void 0}getCookie(e){return this.clerkRequest.cookies.get(e)||void 0}getSuffixedCookie(e){return this.getCookie(tC(e,this.cookieSuffix))||void 0}getSuffixedOrUnSuffixedCookie(e){return this.usesSuffixedCookies()?this.getSuffixedCookie(e):this.getCookie(e)}parseAuthorizationHeader(e){if(!e)return;let[t,r]=e.split(" ",2);return r?"Bearer"===t?r:void 0:t}tokenHasIssuer(e){let{data:t,errors:r}=re(e);return!r&&!!t.payload.iss}tokenBelongsToInstance(e){if(!e)return!1;let{data:t,errors:r}=re(e);if(r)return!1;let n=t.payload.iss.replace(/https?:\/\//gi,"");return this.originalFrontendApi===n}sessionExpired(e){return!!e&&e?.payload.exp<=(Date.now()/1e3|0)}},rI=async(e,t)=>new rP(t.publishableKey?await tO(t.publishableKey,tF.crypto.subtle):"",e,t),rA=RegExp("(?<!:)/{1,}","g");function rN(...e){return e.filter(e=>e).join("/").replace(rA,"/")}var rU=class{constructor(e){this.request=e}requireId(e){if(!e)throw Error("A valid resource ID is required.")}},rM="/actor_tokens",rj=class extends rU{async create(e){return this.request({method:"POST",path:rM,bodyParams:e})}async revoke(e){return this.requireId(e),this.request({method:"POST",path:rN(rM,e,"revoke")})}},rD="/accountless_applications",rL=class extends rU{async createAccountlessApplication(){return this.request({method:"POST",path:rD})}async completeAccountlessApplicationOnboarding(){return this.request({method:"POST",path:rN(rD,"complete")})}},rq="/allowlist_identifiers",rB=class extends rU{async getAllowlistIdentifierList(e={}){return this.request({method:"GET",path:rq,queryParams:{...e,paginated:!0}})}async createAllowlistIdentifier(e){return this.request({method:"POST",path:rq,bodyParams:e})}async deleteAllowlistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:rN(rq,e)})}},rH="/api_keys",r$=class extends rU{async create(e){return this.request({method:"POST",path:rH,bodyParams:e})}async revoke(e){let{apiKeyId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rN(rH,t,"revoke"),bodyParams:r})}async getSecret(e){return this.requireId(e),this.request({method:"GET",path:rN(rH,e,"secret")})}async verifySecret(e){return this.request({method:"POST",path:rN(rH,"verify"),bodyParams:{secret:e}})}},rz=class extends rU{async changeDomain(e){return this.request({method:"POST",path:rN("/beta_features","change_domain"),bodyParams:e})}},rK="/blocklist_identifiers",rJ=class extends rU{async getBlocklistIdentifierList(e={}){return this.request({method:"GET",path:rK,queryParams:e})}async createBlocklistIdentifier(e){return this.request({method:"POST",path:rK,bodyParams:e})}async deleteBlocklistIdentifier(e){return this.requireId(e),this.request({method:"DELETE",path:rN(rK,e)})}},rW="/clients",rF=class extends rU{async getClientList(e={}){return this.request({method:"GET",path:rW,queryParams:{...e,paginated:!0}})}async getClient(e){return this.requireId(e),this.request({method:"GET",path:rN(rW,e)})}verifyClient(e){return this.request({method:"POST",path:rN(rW,"verify"),bodyParams:{token:e}})}async getHandshakePayload(e){return this.request({method:"GET",path:rN(rW,"handshake_payload"),queryParams:e})}},rV="/domains",rG=class extends rU{async list(){return this.request({method:"GET",path:rV})}async add(e){return this.request({method:"POST",path:rV,bodyParams:e})}async update(e){let{domainId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rN(rV,t),bodyParams:r})}async delete(e){return this.deleteDomain(e)}async deleteDomain(e){return this.requireId(e),this.request({method:"DELETE",path:rN(rV,e)})}},rX="/email_addresses",rQ=class extends rU{async getEmailAddress(e){return this.requireId(e),this.request({method:"GET",path:rN(rX,e)})}async createEmailAddress(e){return this.request({method:"POST",path:rX,bodyParams:e})}async updateEmailAddress(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rN(rX,e),bodyParams:t})}async deleteEmailAddress(e){return this.requireId(e),this.request({method:"DELETE",path:rN(rX,e)})}},rY=class extends rU{async verifyAccessToken(e){return this.request({method:"POST",path:rN("/oauth_applications/access_tokens","verify"),bodyParams:{access_token:e}})}},rZ="/instance",r0=class extends rU{async get(){return this.request({method:"GET",path:rZ})}async update(e){return this.request({method:"PATCH",path:rZ,bodyParams:e})}async updateRestrictions(e){return this.request({method:"PATCH",path:rN(rZ,"restrictions"),bodyParams:e})}async updateOrganizationSettings(e){return this.request({method:"PATCH",path:rN(rZ,"organization_settings"),bodyParams:e})}},r1="/invitations",r2=class extends rU{async getInvitationList(e={}){return this.request({method:"GET",path:r1,queryParams:{...e,paginated:!0}})}async createInvitation(e){return this.request({method:"POST",path:r1,bodyParams:e})}async revokeInvitation(e){return this.requireId(e),this.request({method:"POST",path:rN(r1,e,"revoke")})}},r5="/machines",r4=class extends rU{async get(e){return this.requireId(e),this.request({method:"GET",path:rN(r5,e)})}async list(e={}){return this.request({method:"GET",path:r5,queryParams:e})}async create(e){return this.request({method:"POST",path:r5,bodyParams:e})}async update(e){let{machineId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rN(r5,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:rN(r5,e)})}async getSecretKey(e){return this.requireId(e),this.request({method:"GET",path:rN(r5,e,"secret_key")})}async createScope(e,t){return this.requireId(e),this.request({method:"POST",path:rN(r5,e,"scopes"),bodyParams:{toMachineId:t}})}async deleteScope(e,t){return this.requireId(e),this.request({method:"DELETE",path:rN(r5,e,"scopes",t)})}},r3=class extends rU{async verifySecret(e){return this.request({method:"POST",path:rN("/m2m_tokens","verify"),bodyParams:{secret:e}})}},r6=class extends rU{async getJwks(){return this.request({method:"GET",path:"/jwks"})}},r8="/jwt_templates",r9=class extends rU{async list(e={}){return this.request({method:"GET",path:r8,queryParams:{...e,paginated:!0}})}async get(e){return this.requireId(e),this.request({method:"GET",path:rN(r8,e)})}async create(e){return this.request({method:"POST",path:r8,bodyParams:e})}async update(e){let{templateId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rN(r8,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:rN(r8,e)})}},r7="/organizations",ne=class extends rU{async getOrganizationList(e){return this.request({method:"GET",path:r7,queryParams:e})}async createOrganization(e){return this.request({method:"POST",path:r7,bodyParams:e})}async getOrganization(e){let{includeMembersCount:t}=e,r="organizationId"in e?e.organizationId:e.slug;return this.requireId(r),this.request({method:"GET",path:rN(r7,r),queryParams:{includeMembersCount:t}})}async updateOrganization(e,t){return this.requireId(e),this.request({method:"PATCH",path:rN(r7,e),bodyParams:t})}async updateOrganizationLogo(e,t){this.requireId(e);let r=new tF.FormData;return r.append("file",t?.file),t?.uploaderUserId&&r.append("uploader_user_id",t?.uploaderUserId),this.request({method:"PUT",path:rN(r7,e,"logo"),formData:r})}async deleteOrganizationLogo(e){return this.requireId(e),this.request({method:"DELETE",path:rN(r7,e,"logo")})}async updateOrganizationMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:rN(r7,e,"metadata"),bodyParams:t})}async deleteOrganization(e){return this.request({method:"DELETE",path:rN(r7,e)})}async getOrganizationMembershipList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rN(r7,t,"memberships"),queryParams:r})}async getInstanceOrganizationMembershipList(e){return this.request({method:"GET",path:"/organization_memberships",queryParams:e})}async createOrganizationMembership(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rN(r7,t,"memberships"),bodyParams:r})}async updateOrganizationMembership(e){let{organizationId:t,userId:r,...n}=e;return this.requireId(t),this.request({method:"PATCH",path:rN(r7,t,"memberships",r),bodyParams:n})}async updateOrganizationMembershipMetadata(e){let{organizationId:t,userId:r,...n}=e;return this.request({method:"PATCH",path:rN(r7,t,"memberships",r,"metadata"),bodyParams:n})}async deleteOrganizationMembership(e){let{organizationId:t,userId:r}=e;return this.requireId(t),this.request({method:"DELETE",path:rN(r7,t,"memberships",r)})}async getOrganizationInvitationList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rN(r7,t,"invitations"),queryParams:r})}async createOrganizationInvitation(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rN(r7,t,"invitations"),bodyParams:r})}async createOrganizationInvitationBulk(e,t){return this.requireId(e),this.request({method:"POST",path:rN(r7,e,"invitations","bulk"),bodyParams:t})}async getOrganizationInvitation(e){let{organizationId:t,invitationId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"GET",path:rN(r7,t,"invitations",r)})}async revokeOrganizationInvitation(e){let{organizationId:t,invitationId:r,...n}=e;return this.requireId(t),this.request({method:"POST",path:rN(r7,t,"invitations",r,"revoke"),bodyParams:n})}async getOrganizationDomainList(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rN(r7,t,"domains"),queryParams:r})}async createOrganizationDomain(e){let{organizationId:t,...r}=e;return this.requireId(t),this.request({method:"POST",path:rN(r7,t,"domains"),bodyParams:{...r,verified:r.verified??!0}})}async updateOrganizationDomain(e){let{organizationId:t,domainId:r,...n}=e;return this.requireId(t),this.requireId(r),this.request({method:"PATCH",path:rN(r7,t,"domains",r),bodyParams:n})}async deleteOrganizationDomain(e){let{organizationId:t,domainId:r}=e;return this.requireId(t),this.requireId(r),this.request({method:"DELETE",path:rN(r7,t,"domains",r)})}},nt="/oauth_applications",nr=class extends rU{async list(e={}){return this.request({method:"GET",path:nt,queryParams:e})}async get(e){return this.requireId(e),this.request({method:"GET",path:rN(nt,e)})}async create(e){return this.request({method:"POST",path:nt,bodyParams:e})}async update(e){let{oauthApplicationId:t,...r}=e;return this.requireId(t),this.request({method:"PATCH",path:rN(nt,t),bodyParams:r})}async delete(e){return this.requireId(e),this.request({method:"DELETE",path:rN(nt,e)})}async rotateSecret(e){return this.requireId(e),this.request({method:"POST",path:rN(nt,e,"rotate_secret")})}},nn="/phone_numbers",ni=class extends rU{async getPhoneNumber(e){return this.requireId(e),this.request({method:"GET",path:rN(nn,e)})}async createPhoneNumber(e){return this.request({method:"POST",path:nn,bodyParams:e})}async updatePhoneNumber(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rN(nn,e),bodyParams:t})}async deletePhoneNumber(e){return this.requireId(e),this.request({method:"DELETE",path:rN(nn,e)})}},ns=class extends rU{async verify(e){return this.request({method:"POST",path:"/proxy_checks",bodyParams:e})}},na="/redirect_urls",no=class extends rU{async getRedirectUrlList(){return this.request({method:"GET",path:na,queryParams:{paginated:!0}})}async getRedirectUrl(e){return this.requireId(e),this.request({method:"GET",path:rN(na,e)})}async createRedirectUrl(e){return this.request({method:"POST",path:na,bodyParams:e})}async deleteRedirectUrl(e){return this.requireId(e),this.request({method:"DELETE",path:rN(na,e)})}},nl="/saml_connections",nc=class extends rU{async getSamlConnectionList(e={}){return this.request({method:"GET",path:nl,queryParams:e})}async createSamlConnection(e){return this.request({method:"POST",path:nl,bodyParams:e,options:{deepSnakecaseBodyParamKeys:!0}})}async getSamlConnection(e){return this.requireId(e),this.request({method:"GET",path:rN(nl,e)})}async updateSamlConnection(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rN(nl,e),bodyParams:t,options:{deepSnakecaseBodyParamKeys:!0}})}async deleteSamlConnection(e){return this.requireId(e),this.request({method:"DELETE",path:rN(nl,e)})}},nu="/sessions",nd=class extends rU{async getSessionList(e={}){return this.request({method:"GET",path:nu,queryParams:{...e,paginated:!0}})}async getSession(e){return this.requireId(e),this.request({method:"GET",path:rN(nu,e)})}async createSession(e){return this.request({method:"POST",path:nu,bodyParams:e})}async revokeSession(e){return this.requireId(e),this.request({method:"POST",path:rN(nu,e,"revoke")})}async verifySession(e,t){return this.requireId(e),this.request({method:"POST",path:rN(nu,e,"verify"),bodyParams:{token:t}})}async getToken(e,t,r){this.requireId(e);let n={method:"POST",path:t?rN(nu,e,"tokens",t):rN(nu,e,"tokens")};return void 0!==r&&(n.bodyParams={expires_in_seconds:r}),this.request(n)}async refreshSession(e,t){this.requireId(e);let{suffixed_cookies:r,...n}=t;return this.request({method:"POST",path:rN(nu,e,"refresh"),bodyParams:n,queryParams:{suffixed_cookies:r}})}},nh="/sign_in_tokens",np=class extends rU{async createSignInToken(e){return this.request({method:"POST",path:nh,bodyParams:e})}async revokeSignInToken(e){return this.requireId(e),this.request({method:"POST",path:rN(nh,e,"revoke")})}},nf="/sign_ups",ng=class extends rU{async get(e){return this.requireId(e),this.request({method:"GET",path:rN(nf,e)})}async update(e){let{signUpAttemptId:t,...r}=e;return this.request({method:"PATCH",path:rN(nf,t),bodyParams:r})}},nm=class extends rU{async createTestingToken(){return this.request({method:"POST",path:"/testing_tokens"})}},ny="/users",nb=class extends rU{async getUserList(e={}){let{limit:t,offset:r,orderBy:n,...i}=e,[s,a]=await Promise.all([this.request({method:"GET",path:ny,queryParams:e}),this.getCount(i)]);return{data:s,totalCount:a}}async getUser(e){return this.requireId(e),this.request({method:"GET",path:rN(ny,e)})}async createUser(e){return this.request({method:"POST",path:ny,bodyParams:e})}async updateUser(e,t={}){return this.requireId(e),this.request({method:"PATCH",path:rN(ny,e),bodyParams:t})}async updateUserProfileImage(e,t){this.requireId(e);let r=new tF.FormData;return r.append("file",t?.file),this.request({method:"POST",path:rN(ny,e,"profile_image"),formData:r})}async updateUserMetadata(e,t){return this.requireId(e),this.request({method:"PATCH",path:rN(ny,e,"metadata"),bodyParams:t})}async deleteUser(e){return this.requireId(e),this.request({method:"DELETE",path:rN(ny,e)})}async getCount(e={}){return this.request({method:"GET",path:rN(ny,"count"),queryParams:e})}async getUserOauthAccessToken(e,t){this.requireId(e);let r=t.startsWith("oauth_"),n=r?t:`oauth_${t}`;return r&&tA("getUserOauthAccessToken(userId, provider)","Remove the `oauth_` prefix from the `provider` argument."),this.request({method:"GET",path:rN(ny,e,"oauth_access_tokens",n),queryParams:{paginated:!0}})}async disableUserMFA(e){return this.requireId(e),this.request({method:"DELETE",path:rN(ny,e,"mfa")})}async getOrganizationMembershipList(e){let{userId:t,limit:r,offset:n}=e;return this.requireId(t),this.request({method:"GET",path:rN(ny,t,"organization_memberships"),queryParams:{limit:r,offset:n}})}async getOrganizationInvitationList(e){let{userId:t,...r}=e;return this.requireId(t),this.request({method:"GET",path:rN(ny,t,"organization_invitations"),queryParams:r})}async verifyPassword(e){let{userId:t,password:r}=e;return this.requireId(t),this.request({method:"POST",path:rN(ny,t,"verify_password"),bodyParams:{password:r}})}async verifyTOTP(e){let{userId:t,code:r}=e;return this.requireId(t),this.request({method:"POST",path:rN(ny,t,"verify_totp"),bodyParams:{code:r}})}async banUser(e){return this.requireId(e),this.request({method:"POST",path:rN(ny,e,"ban")})}async unbanUser(e){return this.requireId(e),this.request({method:"POST",path:rN(ny,e,"unban")})}async lockUser(e){return this.requireId(e),this.request({method:"POST",path:rN(ny,e,"lock")})}async unlockUser(e){return this.requireId(e),this.request({method:"POST",path:rN(ny,e,"unlock")})}async deleteUserProfileImage(e){return this.requireId(e),this.request({method:"DELETE",path:rN(ny,e,"profile_image")})}async deleteUserPasskey(e){return this.requireId(e.userId),this.requireId(e.passkeyIdentificationId),this.request({method:"DELETE",path:rN(ny,e.userId,"passkeys",e.passkeyIdentificationId)})}async deleteUserWeb3Wallet(e){return this.requireId(e.userId),this.requireId(e.web3WalletIdentificationId),this.request({method:"DELETE",path:rN(ny,e.userId,"web3_wallets",e.web3WalletIdentificationId)})}async deleteUserExternalAccount(e){return this.requireId(e.userId),this.requireId(e.externalAccountId),this.request({method:"DELETE",path:rN(ny,e.userId,"external_accounts",e.externalAccountId)})}async deleteUserBackupCodes(e){return this.requireId(e),this.request({method:"DELETE",path:rN(ny,e,"backup_code")})}async deleteUserTOTP(e){return this.requireId(e),this.request({method:"DELETE",path:rN(ny,e,"totp")})}},n_="/waitlist_entries",nv=class extends rU{async list(e={}){return this.request({method:"GET",path:n_,queryParams:e})}async create(e){return this.request({method:"POST",path:n_,bodyParams:e})}},nw="/webhooks",nk=class extends rU{async createSvixApp(){return this.request({method:"POST",path:rN(nw,"svix")})}async generateSvixAuthURL(){return this.request({method:"POST",path:rN(nw,"svix_url")})}async deleteSvixApp(){return this.request({method:"DELETE",path:rN(nw,"svix")})}},nS=e=>"object"==typeof e&&null!==e,nE=e=>nS(e)&&!(e instanceof RegExp)&&!(e instanceof Error)&&!(e instanceof Date)&&!(globalThis.Blob&&e instanceof globalThis.Blob),nT=Symbol("mapObjectSkip"),nx=(e,t,r,n=new WeakMap)=>{if(r={deep:!1,target:{},...r},n.has(e))return n.get(e);n.set(e,r.target);let{target:i}=r;delete r.target;let s=e=>e.map(e=>nE(e)?nx(e,t,r,n):e);if(Array.isArray(e))return s(e);for(let[a,o]of Object.entries(e)){let l=t(a,o,e);if(l===nT)continue;let[c,u,{shouldRecurse:d=!0}={}]=l;"__proto__"!==c&&(r.deep&&d&&nE(u)&&(u=Array.isArray(u)?s(u):nx(u,t,r,n)),i[c]=u)}return i};function nO(e,t,r){if(!nS(e))throw TypeError(`Expected an object, got \`${e}\` (${typeof e})`);if(Array.isArray(e))throw TypeError("Expected an object, got an array");return nx(e,t,r)}var nC=/([\p{Ll}\d])(\p{Lu})/gu,nR=/(\p{Lu})([\p{Lu}][\p{Ll}])/gu,nP=/(\d)\p{Ll}|(\p{L})\d/u,nI=/[^\p{L}\d]+/giu,nA="$1\0$2";function nN(e){let t=e.trim();t=(t=t.replace(nC,nA).replace(nR,nA)).replace(nI,"\0");let r=0,n=t.length;for(;"\0"===t.charAt(r);)r++;if(r===n)return[];for(;"\0"===t.charAt(n-1);)n--;return t.slice(r,n).split(/\0/g)}function nU(e){let t=nN(e);for(let e=0;e<t.length;e++){let r=t[e],n=nP.exec(r);if(n){let i=n.index+(n[1]??n[2]).length;t.splice(e,1,r.slice(0,i),r.slice(i))}}return t}function nM(e,t){var r,n={delimiter:"_",...t};let[i,s,a]=function(e,t={}){let r=t.split??(t.separateNumbers?nU:nN),n=t.prefixCharacters??"",i=t.suffixCharacters??"",s=0,a=e.length;for(;s<e.length;){let t=e.charAt(s);if(!n.includes(t))break;s++}for(;a>s;){let t=a-1,r=e.charAt(t);if(!i.includes(r))break;a=t}return[e.slice(0,s),r(e.slice(s,a)),e.slice(a)]}(e,n);return i+s.map(!1===(r=n?.locale)?e=>e.toLowerCase():e=>e.toLocaleLowerCase(r)).join(n?.delimiter??" ")+a}var nj={}.constructor;function nD(e,t){return e.some(e=>"string"==typeof e?e===t:e.test(t))}function nL(e,t,r){return r.shouldRecurse?{shouldRecurse:r.shouldRecurse(e,t)}:void 0}var nq=function(e,t){if(Array.isArray(e)){if(e.some(e=>e.constructor!==nj))throw Error("obj must be array of plain objects");let r=(t={deep:!0,exclude:[],parsingOptions:{},...t}).snakeCase||(e=>nM(e,t.parsingOptions));return e.map(e=>nO(e,(e,n)=>[nD(t.exclude,e)?e:r(e),n,nL(e,n,t)],t))}if(e.constructor!==nj)throw Error("obj must be an plain object");let r=(t={deep:!0,exclude:[],parsingOptions:{},...t}).snakeCase||(e=>nM(e,t.parsingOptions));return nO(e,(e,n)=>[nD(t.exclude,e)?e:r(e),n,nL(e,n,t)],t)},nB=class e{constructor(e,t,r,n){this.publishableKey=e,this.secretKey=t,this.claimUrl=r,this.apiKeysUrl=n}static fromJSON(t){return new e(t.publishable_key,t.secret_key,t.claim_url,t.api_keys_url)}},nH=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.status=t,this.userId=r,this.actor=n,this.token=i,this.url=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.status,t.user_id,t.actor,t.token,t.url,t.created_at,t.updated_at)}},n$=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=i,this.instanceId=s,this.invitationId=a}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id,t.invitation_id)}},nz=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g){this.id=e,this.type=t,this.name=r,this.subject=n,this.scopes=i,this.claims=s,this.revoked=a,this.revocationReason=o,this.expired=l,this.expiration=c,this.createdBy=u,this.description=d,this.lastUsedAt=h,this.createdAt=p,this.updatedAt=f,this.secret=g}static fromJSON(t){return new e(t.id,t.type,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.description,t.last_used_at,t.created_at,t.updated_at,t.secret)}},nK=class e{constructor(e,t,r,n,i,s){this.id=e,this.identifier=t,this.identifierType=r,this.createdAt=n,this.updatedAt=i,this.instanceId=s}static fromJSON(t){return new e(t.id,t.identifier,t.identifier_type,t.created_at,t.updated_at,t.instance_id)}},nJ=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.isMobile=t,this.ipAddress=r,this.city=n,this.country=i,this.browserVersion=s,this.browserName=a,this.deviceType=o}static fromJSON(t){return new e(t.id,t.is_mobile,t.ip_address,t.city,t.country,t.browser_version,t.browser_name,t.device_type)}},nW=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d=null){this.id=e,this.clientId=t,this.userId=r,this.status=n,this.lastActiveAt=i,this.expireAt=s,this.abandonAt=a,this.createdAt=o,this.updatedAt=l,this.lastActiveOrganizationId=c,this.latestActivity=u,this.actor=d}static fromJSON(t){return new e(t.id,t.client_id,t.user_id,t.status,t.last_active_at,t.expire_at,t.abandon_at,t.created_at,t.updated_at,t.last_active_organization_id,t.latest_activity&&nJ.fromJSON(t.latest_activity),t.actor)}},nF=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.sessionIds=t,this.sessions=r,this.signInId=n,this.signUpId=i,this.lastActiveSessionId=s,this.createdAt=a,this.updatedAt=o}static fromJSON(t){return new e(t.id,t.session_ids,t.sessions.map(e=>nW.fromJSON(e)),t.sign_in_id,t.sign_up_id,t.last_active_session_id,t.created_at,t.updated_at)}},nV=class e{constructor(e,t,r){this.host=e,this.value=t,this.required=r}static fromJSON(t){return new e(t.host,t.value,t.required)}},nG=class e{constructor(e){this.cookies=e}static fromJSON(t){return new e(t.cookies)}},nX=class e{constructor(e,t,r,n){this.object=e,this.id=t,this.slug=r,this.deleted=n}static fromJSON(t){return new e(t.object,t.id||null,t.slug||null,t.deleted)}},nQ=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.name=t,this.isSatellite=r,this.frontendApiUrl=n,this.developmentOrigin=i,this.cnameTargets=s,this.accountsPortalUrl=a,this.proxyUrl=o}static fromJSON(t){return new e(t.id,t.name,t.is_satellite,t.frontend_api_url,t.development_origin,t.cname_targets&&t.cname_targets.map(e=>nV.fromJSON(e)),t.accounts_portal_url,t.proxy_url)}},nY=class e{constructor(e,t,r,n,i,s,a,o,l,c,u){this.id=e,this.fromEmailName=t,this.emailAddressId=r,this.toEmailAddress=n,this.subject=i,this.body=s,this.bodyPlain=a,this.status=o,this.slug=l,this.data=c,this.deliveredByClerk=u}static fromJSON(t){return new e(t.id,t.from_email_name,t.email_address_id,t.to_email_address,t.subject,t.body,t.body_plain,t.status,t.slug,t.data,t.delivered_by_clerk)}},nZ=class e{constructor(e,t){this.id=e,this.type=t}static fromJSON(t){return new e(t.id,t.type)}},n0=class e{constructor(e,t,r=null,n=null,i=null,s=null,a=null){this.status=e,this.strategy=t,this.externalVerificationRedirectURL=r,this.attempts=n,this.expireAt=i,this.nonce=s,this.message=a}static fromJSON(t){return new e(t.status,t.strategy,t.external_verification_redirect_url?new URL(t.external_verification_redirect_url):null,t.attempts,t.expire_at,t.nonce)}},n1=class e{constructor(e,t,r,n){this.id=e,this.emailAddress=t,this.verification=r,this.linkedTo=n}static fromJSON(t){return new e(t.id,t.email_address,t.verification&&n0.fromJSON(t.verification),t.linked_to.map(e=>nZ.fromJSON(e)))}},n2=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d={},h,p){this.id=e,this.provider=t,this.identificationId=r,this.externalId=n,this.approvedScopes=i,this.emailAddress=s,this.firstName=a,this.lastName=o,this.imageUrl=l,this.username=c,this.phoneNumber=u,this.publicMetadata=d,this.label=h,this.verification=p}static fromJSON(t){return new e(t.id,t.provider,t.identification_id,t.provider_user_id,t.approved_scopes,t.email_address,t.first_name,t.last_name,t.image_url||"",t.username,t.phone_number,t.public_metadata,t.label,t.verification&&n0.fromJSON(t.verification))}},n5=class e{constructor(e,t,r,n,i,s,a,o,l,c,u){this.id=e,this.clientId=t,this.type=r,this.subject=n,this.scopes=i,this.revoked=s,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdAt=c,this.updatedAt=u}static fromJSON(t){return new e(t.id,t.client_id,t.type,t.subject,t.scopes,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_at,t.updated_at)}},n4=class e{constructor(e,t,r){this.id=e,this.environmentType=t,this.allowedOrigins=r}static fromJSON(t){return new e(t.id,t.environment_type,t.allowed_origins)}},n3=class e{constructor(e,t,r,n,i){this.allowlist=e,this.blocklist=t,this.blockEmailSubaddresses=r,this.blockDisposableEmailDomains=n,this.ignoreDotsForGmailAddresses=i}static fromJSON(t){return new e(t.allowlist,t.blocklist,t.block_email_subaddresses,t.block_disposable_email_domains,t.ignore_dots_for_gmail_addresses)}},n6=class e{constructor(e,t,r,n,i){this.id=e,this.restrictedToAllowlist=t,this.fromEmailAddress=r,this.progressiveSignUp=n,this.enhancedEmailDeliverability=i}static fromJSON(t){return new e(t.id,t.restricted_to_allowlist,t.from_email_address,t.progressive_sign_up,t.enhanced_email_deliverability)}},n8=class e{constructor(e,t,r,n,i,s,a,o){this.id=e,this.emailAddress=t,this.publicMetadata=r,this.createdAt=n,this.updatedAt=i,this.status=s,this.url=a,this.revoked=o,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.public_metadata,t.created_at,t.updated_at,t.status,t.url,t.revoked);return r._raw=t,r}},n9={AccountlessApplication:"accountless_application",ActorToken:"actor_token",AllowlistIdentifier:"allowlist_identifier",ApiKey:"api_key",BlocklistIdentifier:"blocklist_identifier",Client:"client",Cookies:"cookies",Domain:"domain",Email:"email",EmailAddress:"email_address",Instance:"instance",InstanceRestrictions:"instance_restrictions",InstanceSettings:"instance_settings",Invitation:"invitation",Machine:"machine",MachineScope:"machine_scope",MachineSecretKey:"machine_secret_key",MachineToken:"machine_to_machine_token",JwtTemplate:"jwt_template",OauthAccessToken:"oauth_access_token",IdpOAuthAccessToken:"clerk_idp_oauth_access_token",OAuthApplication:"oauth_application",Organization:"organization",OrganizationInvitation:"organization_invitation",OrganizationMembership:"organization_membership",OrganizationSettings:"organization_settings",PhoneNumber:"phone_number",ProxyCheck:"proxy_check",RedirectUrl:"redirect_url",SamlConnection:"saml_connection",Session:"session",SignInToken:"sign_in_token",SignUpAttempt:"sign_up_attempt",SmsMessage:"sms_message",User:"user",WaitlistEntry:"waitlist_entry",Token:"token",TotalCount:"total_count"},n7=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.name=t,this.instanceId=r,this.createdAt=n,this.updatedAt=i,this.scopedMachines=s,this.defaultTokenTtl=a}static fromJSON(t){return new e(t.id,t.name,t.instance_id,t.created_at,t.updated_at,t.scoped_machines.map(t=>new e(t.id,t.name,t.instance_id,t.created_at,t.updated_at,[],t.default_token_ttl)),t.default_token_ttl)}},ie=class e{constructor(e,t,r,n){this.fromMachineId=e,this.toMachineId=t,this.createdAt=r,this.deleted=n}static fromJSON(t){return new e(t.from_machine_id,t.to_machine_id,t.created_at,t.deleted)}},it=class e{constructor(e){this.secret=e}static fromJSON(t){return new e(t.secret)}},ir=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h){this.id=e,this.name=t,this.subject=r,this.scopes=n,this.claims=i,this.revoked=s,this.revocationReason=a,this.expired=o,this.expiration=l,this.createdBy=c,this.creationReason=u,this.createdAt=d,this.updatedAt=h}static fromJSON(t){return new e(t.id,t.name,t.subject,t.scopes,t.claims,t.revoked,t.revocation_reason,t.expired,t.expiration,t.created_by,t.creation_reason,t.created_at,t.updated_at)}},ii=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.name=t,this.claims=r,this.lifetime=n,this.allowedClockSkew=i,this.customSigningKey=s,this.signingAlgorithm=a,this.createdAt=o,this.updatedAt=l}static fromJSON(t){return new e(t.id,t.name,t.claims,t.lifetime,t.allowed_clock_skew,t.custom_signing_key,t.signing_algorithm,t.created_at,t.updated_at)}},is=class e{constructor(e,t,r,n={},i,s,a,o){this.externalAccountId=e,this.provider=t,this.token=r,this.publicMetadata=n,this.label=i,this.scopes=s,this.tokenSecret=a,this.expiresAt=o}static fromJSON(t){return new e(t.external_account_id,t.provider,t.token,t.public_metadata,t.label||"",t.scopes,t.token_secret,t.expires_at)}},ia=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f){this.id=e,this.instanceId=t,this.name=r,this.clientId=n,this.isPublic=i,this.scopes=s,this.redirectUris=a,this.authorizeUrl=o,this.tokenFetchUrl=l,this.userInfoUrl=c,this.discoveryUrl=u,this.tokenIntrospectionUrl=d,this.createdAt=h,this.updatedAt=p,this.clientSecret=f}static fromJSON(t){return new e(t.id,t.instance_id,t.name,t.client_id,t.public,t.scopes,t.redirect_uris,t.authorize_url,t.token_fetch_url,t.user_info_url,t.discovery_url,t.token_introspection_url,t.created_at,t.updated_at,t.client_secret)}},io=class e{constructor(e,t,r,n,i,s,a,o={},l={},c,u,d,h){this.id=e,this.name=t,this.slug=r,this.imageUrl=n,this.hasImage=i,this.createdAt=s,this.updatedAt=a,this.publicMetadata=o,this.privateMetadata=l,this.maxAllowedMemberships=c,this.adminDeleteEnabled=u,this.membersCount=d,this.createdBy=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.name,t.slug,t.image_url||"",t.has_image,t.created_at,t.updated_at,t.public_metadata,t.private_metadata,t.max_allowed_memberships,t.admin_delete_enabled,t.members_count,t.created_by);return r._raw=t,r}},il=class e{constructor(e,t,r,n,i,s,a,o,l,c,u={},d={},h){this.id=e,this.emailAddress=t,this.role=r,this.roleName=n,this.organizationId=i,this.createdAt=s,this.updatedAt=a,this.expiresAt=o,this.url=l,this.status=c,this.publicMetadata=u,this.privateMetadata=d,this.publicOrganizationData=h,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.email_address,t.role,t.role_name,t.organization_id,t.created_at,t.updated_at,t.expires_at,t.url,t.status,t.public_metadata,t.private_metadata,t.public_organization_data);return r._raw=t,r}},ic=class e{constructor(e,t,r,n={},i={},s,a,o,l){this.id=e,this.role=t,this.permissions=r,this.publicMetadata=n,this.privateMetadata=i,this.createdAt=s,this.updatedAt=a,this.organization=o,this.publicUserData=l,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.role,t.permissions,t.public_metadata,t.private_metadata,t.created_at,t.updated_at,io.fromJSON(t.organization),iu.fromJSON(t.public_user_data));return r._raw=t,r}},iu=class e{constructor(e,t,r,n,i,s){this.identifier=e,this.firstName=t,this.lastName=r,this.imageUrl=n,this.hasImage=i,this.userId=s}static fromJSON(t){return new e(t.identifier,t.first_name,t.last_name,t.image_url,t.has_image,t.user_id)}},id=class e{constructor(e,t,r,n,i,s,a,o,l){this.enabled=e,this.maxAllowedMemberships=t,this.maxAllowedRoles=r,this.maxAllowedPermissions=n,this.creatorRole=i,this.adminDeleteEnabled=s,this.domainsEnabled=a,this.domainsEnrollmentModes=o,this.domainsDefaultRole=l}static fromJSON(t){return new e(t.enabled,t.max_allowed_memberships,t.max_allowed_roles,t.max_allowed_permissions,t.creator_role,t.admin_delete_enabled,t.domains_enabled,t.domains_enrollment_modes,t.domains_default_role)}},ih=class e{constructor(e,t,r,n,i,s){this.id=e,this.phoneNumber=t,this.reservedForSecondFactor=r,this.defaultSecondFactor=n,this.verification=i,this.linkedTo=s}static fromJSON(t){return new e(t.id,t.phone_number,t.reserved_for_second_factor,t.default_second_factor,t.verification&&n0.fromJSON(t.verification),t.linked_to.map(e=>nZ.fromJSON(e)))}},ip=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.domainId=t,this.lastRunAt=r,this.proxyUrl=n,this.successful=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.domain_id,t.last_run_at,t.proxy_url,t.successful,t.created_at,t.updated_at)}},ig=class e{constructor(e,t,r,n){this.id=e,this.url=t,this.createdAt=r,this.updatedAt=n}static fromJSON(t){return new e(t.id,t.url,t.created_at,t.updated_at)}},im=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g,m,y,b,_,v){this.id=e,this.name=t,this.domain=r,this.organizationId=n,this.idpEntityId=i,this.idpSsoUrl=s,this.idpCertificate=a,this.idpMetadataUrl=o,this.idpMetadata=l,this.acsUrl=c,this.spEntityId=u,this.spMetadataUrl=d,this.active=h,this.provider=p,this.userCount=f,this.syncUserAttributes=g,this.allowSubdomains=m,this.allowIdpInitiated=y,this.createdAt=b,this.updatedAt=_,this.attributeMapping=v}static fromJSON(t){return new e(t.id,t.name,t.domain,t.organization_id,t.idp_entity_id,t.idp_sso_url,t.idp_certificate,t.idp_metadata_url,t.idp_metadata,t.acs_url,t.sp_entity_id,t.sp_metadata_url,t.active,t.provider,t.user_count,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at,t.attribute_mapping&&ib.fromJSON(t.attribute_mapping))}},iy=class e{constructor(e,t,r,n,i,s,a,o,l,c){this.id=e,this.name=t,this.domain=r,this.active=n,this.provider=i,this.syncUserAttributes=s,this.allowSubdomains=a,this.allowIdpInitiated=o,this.createdAt=l,this.updatedAt=c}static fromJSON(t){return new e(t.id,t.name,t.domain,t.active,t.provider,t.sync_user_attributes,t.allow_subdomains,t.allow_idp_initiated,t.created_at,t.updated_at)}},ib=class e{constructor(e,t,r,n){this.userId=e,this.emailAddress=t,this.firstName=r,this.lastName=n}static fromJSON(t){return new e(t.user_id,t.email_address,t.first_name,t.last_name)}},i_=class e{constructor(e,t,r,n,i,s,a,o,l){this.id=e,this.provider=t,this.providerUserId=r,this.active=n,this.emailAddress=i,this.firstName=s,this.lastName=a,this.verification=o,this.samlConnection=l}static fromJSON(t){return new e(t.id,t.provider,t.provider_user_id,t.active,t.email_address,t.first_name,t.last_name,t.verification&&n0.fromJSON(t.verification),t.saml_connection&&iy.fromJSON(t.saml_connection))}},iv=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.userId=t,this.token=r,this.status=n,this.url=i,this.createdAt=s,this.updatedAt=a}static fromJSON(t){return new e(t.id,t.user_id,t.token,t.status,t.url,t.created_at,t.updated_at)}},iw=class e{constructor(e,t){this.nextAction=e,this.supportedStrategies=t}static fromJSON(t){return new e(t.next_action,t.supported_strategies)}},ik=class e{constructor(e,t,r,n){this.emailAddress=e,this.phoneNumber=t,this.web3Wallet=r,this.externalAccount=n}static fromJSON(t){return new e(t.email_address&&iw.fromJSON(t.email_address),t.phone_number&&iw.fromJSON(t.phone_number),t.web3_wallet&&iw.fromJSON(t.web3_wallet),t.external_account)}},iS=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g,m,y,b,_,v,w){this.id=e,this.status=t,this.requiredFields=r,this.optionalFields=n,this.missingFields=i,this.unverifiedFields=s,this.verifications=a,this.username=o,this.emailAddress=l,this.phoneNumber=c,this.web3Wallet=u,this.passwordEnabled=d,this.firstName=h,this.lastName=p,this.customAction=f,this.externalId=g,this.createdSessionId=m,this.createdUserId=y,this.abandonAt=b,this.legalAcceptedAt=_,this.publicMetadata=v,this.unsafeMetadata=w}static fromJSON(t){return new e(t.id,t.status,t.required_fields,t.optional_fields,t.missing_fields,t.unverified_fields,t.verifications?ik.fromJSON(t.verifications):null,t.username,t.email_address,t.phone_number,t.web3_wallet,t.password_enabled,t.first_name,t.last_name,t.custom_action,t.external_id,t.created_session_id,t.created_user_id,t.abandon_at,t.legal_accepted_at,t.public_metadata,t.unsafe_metadata)}},iE=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.fromPhoneNumber=t,this.toPhoneNumber=r,this.message=n,this.status=i,this.phoneNumberId=s,this.data=a}static fromJSON(t){return new e(t.id,t.from_phone_number,t.to_phone_number,t.message,t.status,t.phone_number_id,t.data)}},iT=class e{constructor(e){this.jwt=e}static fromJSON(t){return new e(t.jwt)}},ix=class e{constructor(e,t,r){this.id=e,this.web3Wallet=t,this.verification=r}static fromJSON(t){return new e(t.id,t.web3_wallet,t.verification&&n0.fromJSON(t.verification))}},iO=class e{constructor(e,t,r,n,i,s,a,o,l,c,u,d,h,p,f,g,m,y,b,_={},v={},w={},k=[],S=[],E=[],T=[],x=[],O,C,R=null,P,I){this.id=e,this.passwordEnabled=t,this.totpEnabled=r,this.backupCodeEnabled=n,this.twoFactorEnabled=i,this.banned=s,this.locked=a,this.createdAt=o,this.updatedAt=l,this.imageUrl=c,this.hasImage=u,this.primaryEmailAddressId=d,this.primaryPhoneNumberId=h,this.primaryWeb3WalletId=p,this.lastSignInAt=f,this.externalId=g,this.username=m,this.firstName=y,this.lastName=b,this.publicMetadata=_,this.privateMetadata=v,this.unsafeMetadata=w,this.emailAddresses=k,this.phoneNumbers=S,this.web3Wallets=E,this.externalAccounts=T,this.samlAccounts=x,this.lastActiveAt=O,this.createOrganizationEnabled=C,this.createOrganizationsLimit=R,this.deleteSelfEnabled=P,this.legalAcceptedAt=I,this._raw=null}get raw(){return this._raw}static fromJSON(t){let r=new e(t.id,t.password_enabled,t.totp_enabled,t.backup_code_enabled,t.two_factor_enabled,t.banned,t.locked,t.created_at,t.updated_at,t.image_url,t.has_image,t.primary_email_address_id,t.primary_phone_number_id,t.primary_web3_wallet_id,t.last_sign_in_at,t.external_id,t.username,t.first_name,t.last_name,t.public_metadata,t.private_metadata,t.unsafe_metadata,(t.email_addresses||[]).map(e=>n1.fromJSON(e)),(t.phone_numbers||[]).map(e=>ih.fromJSON(e)),(t.web3_wallets||[]).map(e=>ix.fromJSON(e)),(t.external_accounts||[]).map(e=>n2.fromJSON(e)),(t.saml_accounts||[]).map(e=>i_.fromJSON(e)),t.last_active_at,t.create_organization_enabled,t.create_organizations_limit,t.delete_self_enabled,t.legal_accepted_at);return r._raw=t,r}get primaryEmailAddress(){return this.emailAddresses.find(({id:e})=>e===this.primaryEmailAddressId)??null}get primaryPhoneNumber(){return this.phoneNumbers.find(({id:e})=>e===this.primaryPhoneNumberId)??null}get primaryWeb3Wallet(){return this.web3Wallets.find(({id:e})=>e===this.primaryWeb3WalletId)??null}get fullName(){return[this.firstName,this.lastName].join(" ").trim()||null}},iC=class e{constructor(e,t,r,n,i,s,a){this.id=e,this.emailAddress=t,this.status=r,this.invitation=n,this.createdAt=i,this.updatedAt=s,this.isLocked=a}static fromJSON(t){return new e(t.id,t.email_address,t.status,t.invitation&&n8.fromJSON(t.invitation),t.created_at,t.updated_at,t.is_locked)}};function iR(e){if("string"!=typeof e&&"object"in e&&"deleted"in e)return nX.fromJSON(e);switch(e.object){case n9.AccountlessApplication:return nB.fromJSON(e);case n9.ActorToken:return nH.fromJSON(e);case n9.AllowlistIdentifier:return n$.fromJSON(e);case n9.ApiKey:return nz.fromJSON(e);case n9.BlocklistIdentifier:return nK.fromJSON(e);case n9.Client:return nF.fromJSON(e);case n9.Cookies:return nG.fromJSON(e);case n9.Domain:return nQ.fromJSON(e);case n9.EmailAddress:return n1.fromJSON(e);case n9.Email:return nY.fromJSON(e);case n9.IdpOAuthAccessToken:return n5.fromJSON(e);case n9.Instance:return n4.fromJSON(e);case n9.InstanceRestrictions:return n3.fromJSON(e);case n9.InstanceSettings:return n6.fromJSON(e);case n9.Invitation:return n8.fromJSON(e);case n9.JwtTemplate:return ii.fromJSON(e);case n9.Machine:return n7.fromJSON(e);case n9.MachineScope:return ie.fromJSON(e);case n9.MachineSecretKey:return it.fromJSON(e);case n9.MachineToken:return ir.fromJSON(e);case n9.OauthAccessToken:return is.fromJSON(e);case n9.OAuthApplication:return ia.fromJSON(e);case n9.Organization:return io.fromJSON(e);case n9.OrganizationInvitation:return il.fromJSON(e);case n9.OrganizationMembership:return ic.fromJSON(e);case n9.OrganizationSettings:return id.fromJSON(e);case n9.PhoneNumber:return ih.fromJSON(e);case n9.ProxyCheck:return ip.fromJSON(e);case n9.RedirectUrl:return ig.fromJSON(e);case n9.SamlConnection:return im.fromJSON(e);case n9.SignInToken:return iv.fromJSON(e);case n9.SignUpAttempt:return iS.fromJSON(e);case n9.Session:return nW.fromJSON(e);case n9.SmsMessage:return iE.fromJSON(e);case n9.Token:return iT.fromJSON(e);case n9.TotalCount:return e.total_count;case n9.User:return iO.fromJSON(e);case n9.WaitlistEntry:return iC.fromJSON(e);default:return e}}function iP(e){var t;return t=async t=>{let r,{secretKey:n,requireSecretKey:i=!0,apiUrl:s=r_,apiVersion:a="v1",userAgent:o=rv,skipApiVersionInUrl:l=!1}=e,{path:c,method:u,queryParams:d,headerParams:h,bodyParams:p,formData:f,options:g}=t,{deepSnakecaseBodyParamKeys:m=!1}=g||{};i&&rR(n);let y=new URL(l?rN(s,c):rN(s,a,c));if(d)for(let[e,t]of Object.entries(nq({...d})))t&&[t].flat().forEach(t=>y.searchParams.append(e,t));let b=new Headers({"Clerk-API-Version":rw,"User-Agent":o,...h});n&&b.set("Authorization",`Bearer ${n}`);try{var _;f?r=await tF.fetch(y.href,{method:u,headers:b,body:f}):(b.set("Content-Type","application/json"),r=await tF.fetch(y.href,{method:u,headers:b,...(()=>{if(!("GET"!==u&&p&&Object.keys(p).length>0))return null;let e=e=>nq(e,{deep:m});return{body:JSON.stringify(Array.isArray(p)?p.map(e):e(p))}})()}));let e=r?.headers&&r.headers?.get(rE.Headers.ContentType)===rE.ContentTypes.Json,t=await (e?r.json():r.text());if(!r.ok)return{data:null,errors:iN(t),status:r?.status,statusText:r?.statusText,clerkTraceId:iI(t,r?.headers),retryAfter:iA(r?.headers)};return{...Array.isArray(t)?{data:t.map(e=>iR(e))}:(_=t)&&"object"==typeof _&&"data"in _&&Array.isArray(_.data)&&void 0!==_.data?{data:t.data.map(e=>iR(e)),totalCount:t.total_count}:{data:iR(t)},errors:null}}catch(e){if(e instanceof Error)return{data:null,errors:[{code:"unexpected_error",message:e.message||"Unexpected error"}],clerkTraceId:iI(e,r?.headers)};return{data:null,errors:iN(e),status:r?.status,statusText:r?.statusText,clerkTraceId:iI(e,r?.headers),retryAfter:iA(r?.headers)}}},async(...e)=>{let{data:r,errors:n,totalCount:i,status:s,statusText:a,clerkTraceId:o,retryAfter:l}=await t(...e);if(n){let e=new tU(a||"",{data:[],status:s,clerkTraceId:o,retryAfter:l});throw e.errors=n,e}return void 0!==i?{data:r,totalCount:i}:r}}function iI(e,t){return e&&"object"==typeof e&&"clerk_trace_id"in e&&"string"==typeof e.clerk_trace_id?e.clerk_trace_id:t?.get("cf-ray")||""}function iA(e){let t=e?.get("Retry-After");if(!t)return;let r=parseInt(t,10);if(!isNaN(r))return r}function iN(e){if(e&&"object"==typeof e&&"errors"in e){let t=e.errors;return t.length>0?t.map(tN):[]}return[]}function iU(e){let t=iP(e);return{__experimental_accountlessApplications:new rL(iP({...e,requireSecretKey:!1})),actorTokens:new rj(t),allowlistIdentifiers:new rB(t),apiKeys:new r$(iP({...e,skipApiVersionInUrl:!0})),betaFeatures:new rz(t),blocklistIdentifiers:new rJ(t),clients:new rF(t),domains:new rG(t),emailAddresses:new rQ(t),idPOAuthAccessToken:new rY(iP({...e,skipApiVersionInUrl:!0})),instance:new r0(t),invitations:new r2(t),jwks:new r6(t),jwtTemplates:new r9(t),machines:new r4(t),machineTokens:new r3(iP({...e,skipApiVersionInUrl:!0})),oauthApplications:new nr(t),organizations:new ne(t),phoneNumbers:new ni(t),proxyChecks:new ns(t),redirectUrls:new no(t),samlConnections:new nc(t),sessions:new nd(t),signInTokens:new np(t),signUps:new ng(t),testingTokens:new nm(t),users:new nb(t),waitlistEntries:new nv(t),webhooks:new nk(t)}}var iM={SessionToken:"session_token",ApiKey:"api_key",MachineToken:"machine_token",OAuthToken:"oauth_token"},ij="oat_",iD=["mt_",ij,"ak_"];function iL(e){return iD.some(t=>e.startsWith(t))}function iq(e){if(e.startsWith("mt_"))return iM.MachineToken;if(e.startsWith(ij))return iM.OAuthToken;if(e.startsWith("ak_"))return iM.ApiKey;throw Error("Unknown machine token type")}var iB=(e,t)=>!!e&&("any"===t||(Array.isArray(t)?t:[t]).includes(e)),iH=e=>()=>{let t={...e};return t.secretKey=(t.secretKey||"").substring(0,7),t.jwtKey=(t.jwtKey||"").substring(0,7),{...t}};function i$(e,t){return{tokenType:iM.SessionToken,sessionClaims:null,sessionId:null,sessionStatus:t??null,userId:null,actor:null,orgId:null,orgRole:null,orgSlug:null,orgPermissions:null,factorVerificationAge:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:iH(e),isAuthenticated:!1}}function iz(e,t){let r={id:null,subject:null,scopes:null,has:()=>!1,getToken:()=>Promise.resolve(null),debug:iH(t),isAuthenticated:!1};switch(e){case iM.ApiKey:return{...r,tokenType:e,name:null,claims:null,scopes:null,userId:null,orgId:null};case iM.MachineToken:return{...r,tokenType:e,name:null,claims:null,scopes:null,machineId:null};case iM.OAuthToken:return{...r,tokenType:e,scopes:null,userId:null,clientId:null};default:throw Error(`Invalid token type: ${e}`)}}function iK(){return{isAuthenticated:!1,tokenType:null,getToken:()=>Promise.resolve(null),has:()=>!1,debug:()=>({})}}var iJ=e=>{let{debug:t,getToken:r,has:n,...i}=e;return i},iW=e=>{let{fetcher:t,sessionToken:r,sessionId:n}=e||{};return async(e={})=>n?e.template||void 0!==e.expiresInSeconds?t(n,e.template,e.expiresInSeconds):r:null},iF=({authObject:e,acceptsToken:t=iM.SessionToken})=>"any"===t?e:Array.isArray(t)?iB(e.tokenType,t)?e:iK():iB(e.tokenType,t)?e:!function(e){return e===iM.ApiKey||e===iM.MachineToken||e===iM.OAuthToken}(t)?i$(e.debug):iz(t,e.debug),iV={SignedIn:"signed-in",SignedOut:"signed-out",Handshake:"handshake"},iG={ClientUATWithoutSessionToken:"client-uat-but-no-session-token",DevBrowserMissing:"dev-browser-missing",DevBrowserSync:"dev-browser-sync",PrimaryRespondsToSyncing:"primary-responds-to-syncing",PrimaryDomainCrossOriginSync:"primary-domain-cross-origin-sync",SatelliteCookieNeedsSyncing:"satellite-needs-syncing",SessionTokenAndUATMissing:"session-token-and-uat-missing",SessionTokenMissing:"session-token-missing",SessionTokenExpired:"session-token-expired",SessionTokenIATBeforeClientUAT:"session-token-iat-before-client-uat",SessionTokenNBF:"session-token-nbf",SessionTokenIatInTheFuture:"session-token-iat-in-the-future",SessionTokenWithoutClientUAT:"session-token-but-no-client-uat",ActiveOrganizationMismatch:"active-organization-mismatch",TokenTypeMismatch:"token-type-mismatch",UnexpectedError:"unexpected-error"};function iX(e){let{authenticateContext:t,headers:r=new Headers,token:n}=e;return{status:iV.SignedIn,reason:null,message:null,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!0,isAuthenticated:!0,tokenType:e.tokenType,toAuth:({treatPendingAsSignedOut:r=!0}={})=>{if(e.tokenType===iM.SessionToken){let{sessionClaims:i}=e,s=function(e,t,r){let{actor:n,sessionId:i,sessionStatus:s,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d}=ry(r),h=iU(e),p=iW({sessionId:i,sessionToken:t,fetcher:async(e,t,r)=>(await h.sessions.getToken(e,t||"",r)).jwt});return{tokenType:iM.SessionToken,actor:n,sessionClaims:r,sessionId:i,sessionStatus:s,userId:a,orgId:o,orgRole:l,orgSlug:c,orgPermissions:u,factorVerificationAge:d,getToken:p,has:rg({orgId:o,orgRole:l,orgPermissions:u,userId:a,factorVerificationAge:d,features:r.fea||"",plans:r.pla||""}),debug:iH({...e,sessionToken:t}),isAuthenticated:!0}}(t,n,i);return r&&"pending"===s.sessionStatus?i$(void 0,s.sessionStatus):s}let{machineData:i}=e;var s=e.tokenType;let a={id:i.id,subject:i.subject,getToken:()=>Promise.resolve(n),has:()=>!1,debug:iH(t),isAuthenticated:!0};switch(s){case iM.ApiKey:return{...a,tokenType:s,name:i.name,claims:i.claims,scopes:i.scopes,userId:i.subject.startsWith("user_")?i.subject:null,orgId:i.subject.startsWith("org_")?i.subject:null};case iM.MachineToken:return{...a,tokenType:s,name:i.name,claims:i.claims,scopes:i.scopes,machineId:i.subject};case iM.OAuthToken:return{...a,tokenType:s,scopes:i.scopes,userId:i.subject,clientId:i.clientId};default:throw Error(`Invalid token type: ${s}`)}},headers:r,token:n}}function iQ(e){let{authenticateContext:t,headers:r=new Headers,reason:n,message:i="",tokenType:s}=e;return iY({status:iV.SignedOut,reason:n,message:i,proxyUrl:t.proxyUrl||"",publishableKey:t.publishableKey||"",isSatellite:t.isSatellite||!1,domain:t.domain||"",signInUrl:t.signInUrl||"",signUpUrl:t.signUpUrl||"",afterSignInUrl:t.afterSignInUrl||"",afterSignUpUrl:t.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:s,toAuth:()=>s===iM.SessionToken?i$({...t,status:iV.SignedOut,reason:n,message:i}):iz(s,{reason:n,message:i,headers:r}),headers:r,token:null})}var iY=e=>{let t=new Headers(e.headers||{});if(e.message)try{t.set(rE.Headers.AuthMessage,e.message)}catch{}if(e.reason)try{t.set(rE.Headers.AuthReason,e.reason)}catch{}if(e.status)try{t.set(rE.Headers.AuthStatus,e.status)}catch{}return e.headers=t,e},iZ=class extends URL{isCrossOrigin(e){return this.origin!==new URL(e.toString()).origin}},i0=(...e)=>new iZ(...e),i1=class extends Request{constructor(e,t){super("string"!=typeof e&&"url"in e?e.url:String(e),t||"string"==typeof e?void 0:e),this.clerkUrl=this.deriveUrlFromHeaders(this),this.cookies=this.parseCookies(this)}toJSON(){return{url:this.clerkUrl.href,method:this.method,headers:JSON.stringify(Object.fromEntries(this.headers)),clerkUrl:this.clerkUrl.toString(),cookies:JSON.stringify(Object.fromEntries(this.cookies))}}deriveUrlFromHeaders(e){let t=new URL(e.url),r=e.headers.get(rE.Headers.ForwardedProto),n=e.headers.get(rE.Headers.ForwardedHost),i=e.headers.get(rE.Headers.Host),s=t.protocol,a=this.getFirstValueFromHeader(n)??i,o=this.getFirstValueFromHeader(r)??s?.replace(/[:/]/,""),l=a&&o?`${o}://${a}`:t.origin;return l===t.origin?i0(t):i0(t.pathname+t.search,l)}getFirstValueFromHeader(e){return e?.split(",")[0]}parseCookies(e){return new Map(Object.entries((0,rb.qg)(this.decodeCookieValue(e.headers.get("cookie")||""))))}decodeCookieValue(e){return e?e.replace(/(%[0-9A-Z]{2})+/g,decodeURIComponent):e}},i2=(...e)=>e[0]instanceof i1?e[0]:new i1(...e),i5=e=>e.split(";")[0]?.split("=")[0],i4=e=>e.split(";")[0]?.split("=")[1],i3={},i6=0;function i8(e,t=!0){i3[e.kid]=e,i6=t?Date.now():-1}var i9="local";function i7(e){if(!i3[i9]){if(!e)throw new t$({action:tH.SetClerkJWTKey,message:"Missing local JWK.",reason:tB.LocalJWKMissing});i8({kid:"local",kty:"RSA",alg:"RS256",n:e.replace(/\r\n|\n|\r/g,"").replace("-----BEGIN PUBLIC KEY-----","").replace("-----END PUBLIC KEY-----","").replace("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA","").replace("IDAQAB","").replace(/\+/g,"-").replace(/\//g,"_"),e:"AQAB"},!1)}return i3[i9]}async function se({secretKey:e,apiUrl:t=r_,apiVersion:r="v1",kid:n,skipJwksCache:i}){if(i||function(){if(-1===i6)return!1;let e=Date.now()-i6>=3e5;return e&&(i3={}),e}()||!i3[n]){if(!e)throw new t$({action:tH.ContactSupport,message:"Failed to load JWKS from Clerk Backend or Frontend API.",reason:tB.RemoteJWKFailedToLoad});let{keys:n}=await tf(()=>st(t,e,r));if(!n||!n.length)throw new t$({action:tH.ContactSupport,message:"The JWKS endpoint did not contain any signing keys. Contact <EMAIL>.",reason:tB.RemoteJWKFailedToLoad});n.forEach(e=>i8(e))}let s=i3[n];if(!s){let e=Object.values(i3).map(e=>e.kid).sort().join(", ");throw new t$({action:`Go to your Dashboard and validate your secret and public keys are correct. ${tH.ContactSupport} if the issue persists.`,message:`Unable to find a signing key in JWKS that matches the kid='${n}' of the provided session token. Please make sure that the __session cookie or the HTTP authorization header contain a Clerk-generated session JWT. The following kid is available: ${e}`,reason:tB.JWKKidMismatch})}return s}async function st(e,t,r){if(!t)throw new t$({action:tH.SetClerkSecretKey,message:"Missing Clerk Secret Key or API Key. Go to https://dashboard.clerk.com and get your key for your instance.",reason:tB.RemoteJWKFailedToLoad});let n=new URL(e);n.pathname=rN(n.pathname,r,"/jwks");let i=await tF.fetch(n.href,{headers:{Authorization:`Bearer ${t}`,"Clerk-API-Version":rw,"Content-Type":"application/json","User-Agent":rv}});if(!i.ok){let e=await i.json(),t=sr(e?.errors,tq.InvalidSecretKey);if(t){let e=tB.InvalidSecretKey;throw new t$({action:tH.ContactSupport,message:t.message,reason:e})}throw new t$({action:tH.ContactSupport,message:`Error loading Clerk JWKS from ${n.href} with code=${i.status}`,reason:tB.RemoteJWKFailedToLoad})}return i.json()}var sr=(e,t)=>e?e.find(e=>e.code===t):null;async function sn(e,t){let{data:r,errors:n}=re(e);if(n)return{errors:n};let{header:i}=r,{kid:s}=i;try{let r;if(t.jwtKey)r=i7(t.jwtKey);else{if(!t.secretKey)return{errors:[new t$({action:tH.SetClerkJWTKey,message:"Failed to resolve JWK during verification.",reason:tB.JWKFailedToResolve})]};r=await se({...t,kid:s})}return await rt(e,{...t,key:r})}catch(e){return{errors:[e]}}}function si(e,t,r){if("clerkError"in t){let n,i;switch(t.status){case 401:n=tz.InvalidSecretKey,i=t.errors[0]?.message||"Invalid secret key";break;case 404:n=tz.TokenInvalid,i=r;break;default:n=tz.UnexpectedError,i="Unexpected error"}return{data:void 0,tokenType:e,errors:[new tK({message:i,code:n,status:t.status})]}}return{data:void 0,tokenType:e,errors:[new tK({message:"Unexpected error",code:tz.UnexpectedError,status:t.status})]}}async function ss(e,t){try{let r=iU(t);return{data:await r.machineTokens.verifySecret(e),tokenType:iM.MachineToken,errors:void 0}}catch(e){return si(iM.MachineToken,e,"Machine token not found")}}async function sa(e,t){try{let r=iU(t);return{data:await r.idPOAuthAccessToken.verifyAccessToken(e),tokenType:iM.OAuthToken,errors:void 0}}catch(e){return si(iM.OAuthToken,e,"OAuth token not found")}}async function so(e,t){try{let r=iU(t);return{data:await r.apiKeys.verifySecret(e),tokenType:iM.ApiKey,errors:void 0}}catch(e){return si(iM.ApiKey,e,"API key not found")}}async function sl(e,t){if(e.startsWith("mt_"))return ss(e,t);if(e.startsWith(ij))return sa(e,t);if(e.startsWith("ak_"))return so(e,t);throw Error("Unknown machine token type")}async function sc(e,{key:t}){let{data:r,errors:n}=re(e);if(n)throw n[0];let{header:i,payload:s}=r,{typ:a,alg:o}=i;t2(a),t5(o);let{data:l,errors:c}=await t7(r,t);if(c)throw new t$({reason:tB.TokenVerificationFailed,message:`Error verifying handshake token. ${c[0]}`});if(!l)throw new t$({reason:tB.TokenInvalidSignature,message:"Handshake signature is invalid."});return s}async function su(e,t){let r,{secretKey:n,apiUrl:i,apiVersion:s,jwksCacheTtlInMs:a,jwtKey:o,skipJwksCache:l}=t,{data:c,errors:u}=re(e);if(u)throw u[0];let{kid:d}=c.header;if(o)r=i7(o);else if(n)r=await se({secretKey:n,apiUrl:i,apiVersion:s,kid:d,jwksCacheTtlInMs:a,skipJwksCache:l});else throw new t$({action:tH.SetClerkJWTKey,message:"Failed to resolve JWK during handshake verification.",reason:tB.JWKFailedToResolve});return await sc(e,{key:r})}var sd=class{constructor(e,t,r){this.authenticateContext=e,this.options=t,this.organizationMatcher=r}isRequestEligibleForHandshake(){let{accept:e,secFetchDest:t}=this.authenticateContext;return!!("document"===t||"iframe"===t||!t&&e?.startsWith("text/html"))}buildRedirectToHandshake(e){if(!this.authenticateContext?.clerkUrl)throw Error("Missing clerkUrl in authenticateContext");let t=this.removeDevBrowserFromURL(this.authenticateContext.clerkUrl),r=this.authenticateContext.frontendApi.startsWith("http")?this.authenticateContext.frontendApi:`https://${this.authenticateContext.frontendApi}`,n=new URL("v1/client/handshake",r=r.replace(/\/+$/,"")+"/");n.searchParams.append("redirect_url",t?.href||""),n.searchParams.append("__clerk_api_version",rw),n.searchParams.append(rE.QueryParameters.SuffixedCookies,this.authenticateContext.usesSuffixedCookies().toString()),n.searchParams.append(rE.QueryParameters.HandshakeReason,e),n.searchParams.append(rE.QueryParameters.HandshakeFormat,"nonce"),"development"===this.authenticateContext.instanceType&&this.authenticateContext.devBrowserToken&&n.searchParams.append(rE.QueryParameters.DevBrowser,this.authenticateContext.devBrowserToken);let i=this.getOrganizationSyncTarget(this.authenticateContext.clerkUrl,this.organizationMatcher);return i&&this.getOrganizationSyncQueryParams(i).forEach((e,t)=>{n.searchParams.append(t,e)}),new Headers({[rE.Headers.Location]:n.href})}async getCookiesFromHandshake(){let e=[];if(this.authenticateContext.handshakeNonce)try{let t=await this.authenticateContext.apiClient?.clients.getHandshakePayload({nonce:this.authenticateContext.handshakeNonce});t&&e.push(...t.directives)}catch(e){console.error("Clerk: HandshakeService: error getting handshake payload:",e)}else if(this.authenticateContext.handshakeToken){let t=await su(this.authenticateContext.handshakeToken,this.authenticateContext);t&&Array.isArray(t.handshake)&&e.push(...t.handshake)}return e}async resolveHandshake(){let e=new Headers({"Access-Control-Allow-Origin":"null","Access-Control-Allow-Credentials":"true"}),t=await this.getCookiesFromHandshake(),r="";if(t.forEach(t=>{e.append("Set-Cookie",t),i5(t).startsWith(rE.Cookies.Session)&&(r=i4(t))}),"development"===this.authenticateContext.instanceType){let t=new URL(this.authenticateContext.clerkUrl);t.searchParams.delete(rE.QueryParameters.Handshake),t.searchParams.delete(rE.QueryParameters.HandshakeHelp),e.append(rE.Headers.Location,t.toString()),e.set(rE.Headers.CacheControl,"no-store")}if(""===r)return iQ({tokenType:iM.SessionToken,authenticateContext:this.authenticateContext,reason:iG.SessionTokenMissing,message:"",headers:e});let{data:n,errors:[i]=[]}=await sn(r,this.authenticateContext);if(n)return iX({tokenType:iM.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:n,headers:e,token:r});if("development"===this.authenticateContext.instanceType&&(i?.reason===tB.TokenExpired||i?.reason===tB.TokenNotActiveYet||i?.reason===tB.TokenIatInTheFuture)){let t=new t$({action:i.action,message:i.message,reason:i.reason});t.tokenCarrier="cookie",console.error(`Clerk: Clock skew detected. This usually means that your system clock is inaccurate. Clerk will attempt to account for the clock skew in development.

To resolve this issue, make sure your system's clock is set to the correct time (e.g. turn off and on automatic time synchronization).

---

${t.getFullMessage()}`);let{data:n,errors:[s]=[]}=await sn(r,{...this.authenticateContext,clockSkewInMs:864e5});if(n)return iX({tokenType:iM.SessionToken,authenticateContext:this.authenticateContext,sessionClaims:n,headers:e,token:r});throw Error(s?.message||"Clerk: Handshake retry failed.")}throw Error(i?.message||"Clerk: Handshake failed.")}handleTokenVerificationErrorInDevelopment(e){if(e.reason===tB.TokenInvalidSignature)throw Error("Clerk: Handshake token verification failed due to an invalid signature. If you have switched Clerk keys locally, clear your cookies and try again.");throw Error(`Clerk: Handshake token verification failed: ${e.getFullMessage()}.`)}checkAndTrackRedirectLoop(e){if(3===this.authenticateContext.handshakeRedirectLoopCounter)return!0;let t=this.authenticateContext.handshakeRedirectLoopCounter+1,r=rE.Cookies.RedirectCount;return e.append("Set-Cookie",`${r}=${t}; SameSite=Lax; HttpOnly; Max-Age=3`),!1}removeDevBrowserFromURL(e){let t=new URL(e);return t.searchParams.delete(rE.QueryParameters.DevBrowser),t.searchParams.delete(rE.QueryParameters.LegacyDevBrowser),t}getOrganizationSyncTarget(e,t){return t.findTarget(e)}getOrganizationSyncQueryParams(e){let t=new Map;return"personalAccount"===e.type&&t.set("organization_id",""),"organization"===e.type&&(e.organizationId&&t.set("organization_id",e.organizationId),e.organizationSlug&&t.set("organization_id",e.organizationSlug)),t}},sh=class{constructor(e){this.organizationPattern=this.createMatcher(e?.organizationPatterns),this.personalAccountPattern=this.createMatcher(e?.personalAccountPatterns)}createMatcher(e){if(!e)return null;try{return function(e,t){try{var r,n,i,s,a,o,l;return r=void 0,n=[],i=e6(e,n,r),s=n,a=r,void 0===a&&(a={}),o=a.decode,l=void 0===o?function(e){return e}:o,function(e){var t=i.exec(e);if(!t)return!1;for(var r=t[0],n=t.index,a=Object.create(null),o=1;o<t.length;o++)!function(e){if(void 0!==t[e]){var r=s[e-1];"*"===r.modifier||"+"===r.modifier?a[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return l(e,r)}):a[r.name]=l(t[e],r)}}(o);return{path:r,index:n,params:a}}}catch(e){throw Error(`Invalid path and options: Consult the documentation of path-to-regexp here: https://github.com/pillarjs/path-to-regexp/tree/6.x
${e.message}`)}}(e)}catch(t){throw Error(`Invalid pattern "${e}": ${t}`)}}findTarget(e){let t=this.findOrganizationTarget(e);return t||this.findPersonalAccountTarget(e)}findOrganizationTarget(e){if(!this.organizationPattern)return null;try{let t=this.organizationPattern(e.pathname);if(!t||!("params"in t))return null;let r=t.params;if(r.id)return{type:"organization",organizationId:r.id};if(r.slug)return{type:"organization",organizationSlug:r.slug};return null}catch(e){return console.error("Failed to match organization pattern:",e),null}}findPersonalAccountTarget(e){if(!this.personalAccountPattern)return null;try{return this.personalAccountPattern(e.pathname)?{type:"personalAccount"}:null}catch(e){return console.error("Failed to match personal account pattern:",e),null}}},sp={NonEligibleNoCookie:"non-eligible-no-refresh-cookie",NonEligibleNonGet:"non-eligible-non-get",InvalidSessionToken:"invalid-session-token",MissingApiClient:"missing-api-client",MissingSessionToken:"missing-session-token",MissingRefreshToken:"missing-refresh-token",ExpiredSessionTokenDecodeFailed:"expired-session-token-decode-failed",ExpiredSessionTokenMissingSidClaim:"expired-session-token-missing-sid-claim",FetchError:"fetch-error",UnexpectedSDKError:"unexpected-sdk-error",UnexpectedBAPIError:"unexpected-bapi-error"};function sf(e,t,r){return iB(e,t)?null:iQ({tokenType:e,authenticateContext:r,reason:iG.TokenTypeMismatch})}var sg=async(e,t)=>{let r=await rI(i2(e),t);rR(r.secretKey);let n=t.acceptsToken??iM.SessionToken;if(r.isSatellite){var i=r.signInUrl,s=r.secretKey;if(!i&&tx(s))throw Error("Missing signInUrl. Pass a signInUrl for dev instances if an app is satellite");if(r.signInUrl&&r.origin&&function(e,t){let r;try{r=new URL(e)}catch{throw Error("The signInUrl needs to have a absolute url format.")}if(r.origin===t)throw Error("The signInUrl needs to be on a different origin than your satellite application.")}(r.signInUrl,r.origin),!(r.proxyUrl||r.domain))throw Error("Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl")}let a=new sh(t.organizationSyncOptions),o=new sd(r,{organizationSyncOptions:t.organizationSyncOptions},a);async function l(r){if(!t.apiClient)return{data:null,error:{message:"An apiClient is needed to perform token refresh.",cause:{reason:sp.MissingApiClient}}};let{sessionToken:n,refreshTokenInCookie:i}=r;if(!n)return{data:null,error:{message:"Session token must be provided.",cause:{reason:sp.MissingSessionToken}}};if(!i)return{data:null,error:{message:"Refresh token must be provided.",cause:{reason:sp.MissingRefreshToken}}};let{data:s,errors:a}=re(n);if(!s||a)return{data:null,error:{message:"Unable to decode the expired session token.",cause:{reason:sp.ExpiredSessionTokenDecodeFailed,errors:a}}};if(!s?.payload?.sid)return{data:null,error:{message:"Expired session token is missing the `sid` claim.",cause:{reason:sp.ExpiredSessionTokenMissingSidClaim}}};try{return{data:(await t.apiClient.sessions.refreshSession(s.payload.sid,{format:"cookie",suffixed_cookies:r.usesSuffixedCookies(),expired_token:n||"",refresh_token:i||"",request_origin:r.clerkUrl.origin,request_headers:Object.fromEntries(Array.from(e.headers.entries()).map(([e,t])=>[e,[t]]))})).cookies,error:null}}catch(e){if(!e?.errors?.length)return{data:null,error:{message:"Unexpected Server/BAPI error",cause:{reason:sp.UnexpectedBAPIError,errors:[e]}}};if("unexpected_error"===e.errors[0].code)return{data:null,error:{message:"Fetch unexpected error",cause:{reason:sp.FetchError,errors:e.errors}}};return{data:null,error:{message:e.errors[0].code,cause:{reason:e.errors[0].code,errors:e.errors}}}}}async function c(e){let{data:t,error:r}=await l(e);if(!t||0===t.length)return{data:null,error:r};let n=new Headers,i="";t.forEach(e=>{n.append("Set-Cookie",e),i5(e).startsWith(rE.Cookies.Session)&&(i=i4(e))});let{data:s,errors:a}=await sn(i,e);return a?{data:null,error:{message:"Clerk: unable to verify refreshed session token.",cause:{reason:sp.InvalidSessionToken,errors:a}}}:{data:{jwtPayload:s,sessionToken:i,headers:n},error:null}}function u(e,t,r,n){if(!o.isRequestEligibleForHandshake())return iQ({tokenType:iM.SessionToken,authenticateContext:e,reason:t,message:r});let i=n??o.buildRedirectToHandshake(t);return(i.get(rE.Headers.Location)&&i.set(rE.Headers.CacheControl,"no-store"),o.checkAndTrackRedirectLoop(i))?(console.log("Clerk: Refreshing the session token resulted in an infinite redirect loop. This usually means that your Clerk instance keys do not match - make sure to copy the correct publishable and secret keys from the Clerk dashboard."),iQ({tokenType:iM.SessionToken,authenticateContext:e,reason:t,message:r})):function(e,t,r="",n){return iY({status:iV.Handshake,reason:t,message:r,publishableKey:e.publishableKey||"",isSatellite:e.isSatellite||!1,domain:e.domain||"",proxyUrl:e.proxyUrl||"",signInUrl:e.signInUrl||"",signUpUrl:e.signUpUrl||"",afterSignInUrl:e.afterSignInUrl||"",afterSignUpUrl:e.afterSignUpUrl||"",isSignedIn:!1,isAuthenticated:!1,tokenType:iM.SessionToken,toAuth:()=>null,headers:n,token:null})}(e,t,r,i)}async function d(){let{tokenInHeader:e}=r;try{let{data:t,errors:n}=await sn(e,r);if(n)throw n[0];return iX({tokenType:iM.SessionToken,authenticateContext:r,sessionClaims:t,headers:new Headers,token:e})}catch(e){return p(e,"header")}}async function h(){let e=r.clientUat,t=!!r.sessionTokenInCookie,n=!!r.devBrowserToken;if(r.handshakeNonce||r.handshakeToken)try{return await o.resolveHandshake()}catch(e){e instanceof t$&&"development"===r.instanceType?o.handleTokenVerificationErrorInDevelopment(e):console.error("Clerk: unable to resolve handshake:",e)}if("development"===r.instanceType&&r.clerkUrl.searchParams.has(rE.QueryParameters.DevBrowser))return u(r,iG.DevBrowserSync,"");let i=r.isSatellite&&"document"===r.secFetchDest;if("production"===r.instanceType&&i)return u(r,iG.SatelliteCookieNeedsSyncing,"");if("development"===r.instanceType&&i&&!r.clerkUrl.searchParams.has(rE.QueryParameters.ClerkSynced)){let e=new URL(r.signInUrl);e.searchParams.append(rE.QueryParameters.ClerkRedirectUrl,r.clerkUrl.toString());let t=new Headers({[rE.Headers.Location]:e.toString()});return u(r,iG.SatelliteCookieNeedsSyncing,"",t)}let s=new URL(r.clerkUrl).searchParams.get(rE.QueryParameters.ClerkRedirectUrl);if("development"===r.instanceType&&!r.isSatellite&&s){let e=new URL(s);r.devBrowserToken&&e.searchParams.append(rE.QueryParameters.DevBrowser,r.devBrowserToken),e.searchParams.append(rE.QueryParameters.ClerkSynced,"true");let t=new Headers({[rE.Headers.Location]:e.toString()});return u(r,iG.PrimaryRespondsToSyncing,"",t)}if("development"===r.instanceType&&!n)return u(r,iG.DevBrowserMissing,"");if(!e&&!t)return iQ({tokenType:iM.SessionToken,authenticateContext:r,reason:iG.SessionTokenAndUATMissing});if(!e&&t)return u(r,iG.SessionTokenWithoutClientUAT,"");if(e&&!t)return u(r,iG.ClientUATWithoutSessionToken,"");let{data:l,errors:c}=re(r.sessionTokenInCookie);if(c)return p(c[0],"cookie");if(l.payload.iat<r.clientUat)return u(r,iG.SessionTokenIATBeforeClientUAT,"");try{let{data:e,errors:t}=await sn(r.sessionTokenInCookie,r);if(t)throw t[0];let n=iX({tokenType:iM.SessionToken,authenticateContext:r,sessionClaims:e,headers:new Headers,token:r.sessionTokenInCookie});if(!r.isSatellite&&"document"===r.secFetchDest&&r.isCrossOriginReferrer())return u(r,iG.PrimaryDomainCrossOriginSync,"Cross-origin request from satellite domain requires handshake");let i=n.toAuth();if(i.userId){let e=function(e,t){let r=a.findTarget(e.clerkUrl);if(!r)return null;let n=!1;if("organization"===r.type&&(r.organizationSlug&&r.organizationSlug!==t.orgSlug&&(n=!0),r.organizationId&&r.organizationId!==t.orgId&&(n=!0)),"personalAccount"===r.type&&t.orgId&&(n=!0),!n)return null;if(e.handshakeRedirectLoopCounter>0)return console.warn("Clerk: Organization activation handshake loop detected. This is likely due to an invalid organization ID or slug. Skipping organization activation."),null;let i=u(e,iG.ActiveOrganizationMismatch,"");return"handshake"!==i.status?null:i}(r,i);if(e)return e}return n}catch(e){return p(e,"cookie")}}async function p(t,n){let i;if(!(t instanceof t$))return iQ({tokenType:iM.SessionToken,authenticateContext:r,reason:iG.UnexpectedError});if(t.reason===tB.TokenExpired&&r.refreshTokenInCookie&&"GET"===e.method){let{data:e,error:t}=await c(r);if(e)return iX({tokenType:iM.SessionToken,authenticateContext:r,sessionClaims:e.jwtPayload,headers:e.headers,token:e.sessionToken});i=t?.cause?.reason?t.cause.reason:sp.UnexpectedSDKError}else i="GET"!==e.method?sp.NonEligibleNonGet:r.refreshTokenInCookie?null:sp.NonEligibleNoCookie;return(t.tokenCarrier=n,[tB.TokenExpired,tB.TokenNotActiveYet,tB.TokenIatInTheFuture].includes(t.reason))?u(r,sy({tokenError:t.reason,refreshError:i}),t.getFullMessage()):iQ({tokenType:iM.SessionToken,authenticateContext:r,reason:t.reason,message:t.getFullMessage()})}function f(e,t){return t instanceof tK?iQ({tokenType:e,authenticateContext:r,reason:t.code,message:t.getFullMessage()}):iQ({tokenType:e,authenticateContext:r,reason:iG.UnexpectedError})}async function g(){let{tokenInHeader:e}=r;if(!e)return p(Error("Missing token in header"),"header");if(!iL(e))return iQ({tokenType:n,authenticateContext:r,reason:iG.TokenTypeMismatch,message:""});let t=sf(iq(e),n,r);if(t)return t;let{data:i,tokenType:s,errors:a}=await sl(e,r);return a?f(s,a[0]):iX({tokenType:s,authenticateContext:r,machineData:i,token:e})}async function m(){let{tokenInHeader:e}=r;if(!e)return p(Error("Missing token in header"),"header");if(iL(e)){let t=sf(iq(e),n,r);if(t)return t;let{data:i,tokenType:s,errors:a}=await sl(e,r);return a?f(s,a[0]):iX({tokenType:s,authenticateContext:r,machineData:i,token:e})}let{data:t,errors:i}=await sn(e,r);return i?p(i[0],"header"):iX({tokenType:iM.SessionToken,authenticateContext:r,sessionClaims:t,token:e})}return Array.isArray(n)&&!function(e,t){let r=null,{tokenInHeader:n}=t;return n&&(r=iL(n)?iq(n):iM.SessionToken),iB(r??iM.SessionToken,e)}(n,r)?function(){let e=iK();return iY({status:iV.SignedOut,reason:iG.TokenTypeMismatch,message:"",proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",signInUrl:"",signUpUrl:"",afterSignInUrl:"",afterSignUpUrl:"",isSignedIn:!1,isAuthenticated:!1,tokenType:null,toAuth:()=>e,headers:new Headers,token:null})}():r.tokenInHeader?"any"===n?m():n===iM.SessionToken?d():g():n===iM.OAuthToken||n===iM.ApiKey||n===iM.MachineToken?iQ({tokenType:n,authenticateContext:r,reason:"No token in header"}):h()},sm=e=>{let{isSignedIn:t,isAuthenticated:r,proxyUrl:n,reason:i,message:s,publishableKey:a,isSatellite:o,domain:l}=e;return{isSignedIn:t,isAuthenticated:r,proxyUrl:n,reason:i,message:s,publishableKey:a,isSatellite:o,domain:l}},sy=({tokenError:e,refreshError:t})=>{switch(e){case tB.TokenExpired:return`${iG.SessionTokenExpired}-refresh-${t}`;case tB.TokenNotActiveYet:return iG.SessionTokenNBF;case tB.TokenIatInTheFuture:return iG.SessionTokenIatInTheFuture;default:return iG.UnexpectedError}},sb={secretKey:"",jwtKey:"",apiUrl:void 0,apiVersion:void 0,proxyUrl:"",publishableKey:"",isSatellite:!1,domain:"",audience:""};r(821),r(167),r(830).s;var s_=r(159);let sv=""+s_.s8+";404";s_.s8,s_.s8,r(792).X,r(280),"undefined"==typeof URLPattern||URLPattern,r(557),r(602),r(801),r(335),new WeakMap;let sw={Headers:{NextRewrite:"x-middleware-rewrite",NextResume:"x-middleware-next",NextRedirect:"Location",NextUrl:"next-url",NextAction:"next-action",NextjsData:"x-nextjs-data"}},sk=e=>e.headers.get(sw.Headers.NextRedirect),sS=(e,t,r)=>(e.headers.set(t,r),e);var sE="__clerk_db_jwt",sT=e=>{let t=new URL(e);return t.searchParams.delete(sE),t},sx=e=>{let t=new URL(e);return t.searchParams.delete("__dev_session"),t.hash=decodeURI(t.hash).replace(/__clerk_db_jwt\[(.*)\]/,""),t.href.endsWith("#")&&(t.hash=""),t};let sO=(e,t,r)=>{let n=t.headers.get("location");if("true"===t.headers.get(rE.Headers.ClerkRedirectTo)&&n&&tx(r.secretKey)&&e.clerkUrl.isCrossOrigin(n)){let r=e.cookies.get(sE)||"",i=function(e,t){let r=new URL(e),n=r.searchParams.get(sE);r.searchParams.delete(sE);let i=n||t;return i&&r.searchParams.set(sE,i),r}(new URL(n),r);return z.redirect(i.href,t)}return t},sC={rE:"15.3.5"},sR=e=>{if(!e||"string"!=typeof e)return e;try{return(e||"").replace(/^(sk_(live|test)_)(.+?)(.{3})$/,"$1*********$4")}catch{return""}},sP=e=>(Array.isArray(e)?e:[e]).map(e=>"string"==typeof e?sR(e):JSON.stringify(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,sR(t)])),null,2)).join(", "),sI=(e,t)=>()=>{let r=[],n=!1;return{enable:()=>{n=!0},debug:(...e)=>{n&&r.push(e.map(e=>"function"==typeof e?e():e))},commit:()=>{if(n){var i,s;for(let n of(console.log((i=e,`[clerk debug start: ${i}]`)),r)){let e=t(n);e=e.split("\n").map(e=>`  ${e}`).join("\n"),process.env.VERCEL&&(e=function(e,t){let r=new TextEncoder,n=new TextDecoder("utf-8"),i=r.encode(e).slice(0,4096);return n.decode(i).replace(/\uFFFD/g,"")}(e,4096)),console.log(e)}console.log((s=e,`[clerk debug end: ${s}] (@clerk/nextjs=6.29.0,next=${sC.rE},timestamp=${Math.round(new Date().getTime()/1e3)})`))}}}},sA=(e,t)=>(...r)=>{let n=("string"==typeof e?sI(e,sP):e)(),i=t(n);try{let e=i(...r);if("object"==typeof e&&"then"in e&&"function"==typeof e.then)return e.then(e=>(n.commit(),e)).catch(e=>{throw n.commit(),e});return n.commit(),e}catch(e){throw n.commit(),e}};function sN(e,t,r){return"function"==typeof e?e(t):void 0!==e?e:void 0!==r?r:void 0}var sU=e=>{let t=r=>{if(!r)return r;if(Array.isArray(r))return r.map(e=>"object"==typeof e||Array.isArray(e)?t(e):e);let n={...r};for(let r of Object.keys(n)){let i=e(r.toString());i!==r&&(n[i]=n[r],delete n[r]),"object"==typeof n[i]&&(n[i]=t(n[i]))}return n};return t};function sM(e){if("boolean"==typeof e)return e;if(null==e)return!1;if("string"==typeof e){if("true"===e.toLowerCase())return!0;if("false"===e.toLowerCase())return!1}let t=parseInt(e,10);return!isNaN(t)&&t>0}sU(function(e){return e?e.replace(/[A-Z]/g,e=>`_${e.toLowerCase()}`):""}),sU(function(e){return e?e.replace(/([-_][a-z])/g,e=>e.toUpperCase().replace(/-|_/,"")):""}),process.env.NEXT_PUBLIC_CLERK_JS_VERSION,process.env.NEXT_PUBLIC_CLERK_JS_URL;let sj=process.env.CLERK_API_VERSION||"v1",sD=process.env.CLERK_SECRET_KEY||"",sL="pk_test_Zmlyc3QtbW9uaXRvci03NC5jbGVyay5hY2NvdW50cy5kZXYk",sq=process.env.CLERK_ENCRYPTION_KEY||"",sB=process.env.CLERK_API_URL||(e=>{let t=tE(e)?.frontendApi;return t?.startsWith("clerk.")&&ty.some(e=>t?.endsWith(e))?tw:t_.some(e=>t?.endsWith(e))?"https://api.lclclerk.com":tv.some(e=>t?.endsWith(e))?"https://api.clerkstage.dev":tw})(sL),sH=process.env.NEXT_PUBLIC_CLERK_DOMAIN||"",s$=process.env.NEXT_PUBLIC_CLERK_PROXY_URL||"",sz=sM(process.env.NEXT_PUBLIC_CLERK_IS_SATELLITE)||!1,sK="/auth/login",sJ=sM(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DISABLED),sW=sM(process.env.NEXT_PUBLIC_CLERK_TELEMETRY_DEBUG),sF=sM(process.env.NEXT_PUBLIC_CLERK_KEYLESS_DISABLED)||!1,sV=!(sC.rE.startsWith("13.")||sC.rE.startsWith("14.0"))&&!1,sG=e=>{if(!(e instanceof Error)||!("message"in e))return!1;let{message:t}=e,r=t.toLowerCase(),n=r.includes("dynamic server usage"),i=r.includes("this page needs to bail out of prerendering");return/Route .*? needs to bail out of prerendering at this point because it used .*?./.test(t)||n||i};async function sX(){try{let{headers:e}=await Promise.resolve().then(r.bind(r,221)),t=await e();return new L("https://placeholder.com",{headers:t})}catch(e){if(e&&sG(e))throw e;throw Error(`Clerk: auth(), currentUser() and clerkClient(), are only supported in App Router (/app directory).
If you're using /pages, try getAuth() instead.
Original error: ${e}`)}}var sQ=class{constructor(){to(this,at),to(this,s7,"clerk_telemetry_throttler"),to(this,ae,864e5)}isEventThrottled(e){if(!ta(this,at,ai))return!1;let t=Date.now(),r=tc(this,at,ar).call(this,e),n=ta(this,at,an)?.[r];if(!n){let e={...ta(this,at,an),[r]:t};localStorage.setItem(ta(this,s7),JSON.stringify(e))}if(n&&t-n>ta(this,ae)){let e=ta(this,at,an);delete e[r],localStorage.setItem(ta(this,s7),JSON.stringify(e))}return!!n}};s7=new WeakMap,ae=new WeakMap,at=new WeakSet,ar=function(e){let{sk:t,pk:r,payload:n,...i}=e,s={...n,...i};return JSON.stringify(Object.keys({...n,...i}).sort().map(e=>s[e]))},an=function(){let e=localStorage.getItem(ta(this,s7));return e?JSON.parse(e):{}},ai=function(){if("undefined"==typeof window)return!1;let e=window.localStorage;if(!e)return!1;try{let t="test";return e.setItem(t,t),e.removeItem(t),!0}catch(t){return t instanceof DOMException&&("QuotaExceededError"===t.name||"NS_ERROR_DOM_QUOTA_REACHED"===t.name)&&e.length>0&&e.removeItem(ta(this,s7)),!1}};var sY={samplingRate:1,maxBufferSize:5,endpoint:"https://clerk-telemetry.com"},sZ=class{constructor(e){to(this,au),to(this,as),to(this,aa),to(this,ao,{}),to(this,al,[]),to(this,ac),tl(this,as,{maxBufferSize:e.maxBufferSize??sY.maxBufferSize,samplingRate:e.samplingRate??sY.samplingRate,disabled:e.disabled??!1,debug:e.debug??!1,endpoint:sY.endpoint}),e.clerkVersion||"undefined"!=typeof window?ta(this,ao).clerkVersion=e.clerkVersion??"":ta(this,ao).clerkVersion="",ta(this,ao).sdk=e.sdk,ta(this,ao).sdkVersion=e.sdkVersion,ta(this,ao).publishableKey=e.publishableKey??"";let t=tE(e.publishableKey);t&&(ta(this,ao).instanceType=t.instanceType),e.secretKey&&(ta(this,ao).secretKey=e.secretKey.substring(0,16)),tl(this,aa,new sQ)}get isEnabled(){return!("development"!==ta(this,ao).instanceType||ta(this,as).disabled||"undefined"!=typeof process&&process.env&&sM(process.env.CLERK_TELEMETRY_DISABLED)||"undefined"!=typeof window&&window?.navigator?.webdriver)}get isDebug(){return ta(this,as).debug||"undefined"!=typeof process&&process.env&&sM(process.env.CLERK_TELEMETRY_DEBUG)}record(e){let t=tc(this,au,ay).call(this,e.event,e.payload);tc(this,au,ag).call(this,t.event,t),tc(this,au,ad).call(this,t,e.eventSamplingRate)&&(ta(this,al).push(t),tc(this,au,ap).call(this))}};as=new WeakMap,aa=new WeakMap,ao=new WeakMap,al=new WeakMap,ac=new WeakMap,au=new WeakSet,ad=function(e,t){return this.isEnabled&&!this.isDebug&&tc(this,au,ah).call(this,e,t)},ah=function(e,t){let r=Math.random();return!!(r<=ta(this,as).samplingRate&&(void 0===t||r<=t))&&!ta(this,aa).isEventThrottled(e)},ap=function(){if("undefined"==typeof window)return void tc(this,au,af).call(this);if(ta(this,al).length>=ta(this,as).maxBufferSize){ta(this,ac)&&("undefined"!=typeof cancelIdleCallback?cancelIdleCallback:clearTimeout)(ta(this,ac)),tc(this,au,af).call(this);return}ta(this,ac)||("requestIdleCallback"in window?tl(this,ac,requestIdleCallback(()=>{tc(this,au,af).call(this)})):tl(this,ac,setTimeout(()=>{tc(this,au,af).call(this)},0)))},af=function(){fetch(new URL("/v1/event",ta(this,as).endpoint),{method:"POST",body:JSON.stringify({events:ta(this,al)}),headers:{"Content-Type":"application/json"}}).catch(()=>void 0).then(()=>{tl(this,al,[])}).catch(()=>void 0)},ag=function(e,t){this.isDebug&&(void 0!==console.groupCollapsed?(console.groupCollapsed("[clerk/telemetry]",e),console.log(t),console.groupEnd()):console.log("[clerk/telemetry]",e,t))},am=function(){let e={name:ta(this,ao).sdk,version:ta(this,ao).sdkVersion};if("undefined"!=typeof window){let t=window;if(t.Clerk){let r=t.Clerk;if("object"==typeof r&&null!==r&&"constructor"in r&&"function"==typeof r.constructor&&r.constructor.sdkMetadata){let{name:t,version:n}=r.constructor.sdkMetadata;void 0!==t&&(e.name=t),void 0!==n&&(e.version=n)}}}return e},ay=function(e,t){let r=tc(this,au,am).call(this);return{event:e,cv:ta(this,ao).clerkVersion??"",it:ta(this,ao).instanceType??"",sdk:r.name,sdkv:r.version,...ta(this,ao).publishableKey?{pk:ta(this,ao).publishableKey}:{},...ta(this,ao).secretKey?{sk:ta(this,ao).secretKey}:{},payload:t}};let s0={secretKey:sD,publishableKey:sL,apiUrl:sB,apiVersion:sj,userAgent:"@clerk/nextjs@6.29.0",proxyUrl:s$,domain:sH,isSatellite:sz,sdkMetadata:{name:"@clerk/nextjs",version:"6.29.0",environment:"production"},telemetry:{disabled:sJ,debug:sW}},s1=e=>(function(e){let t={...e},r=iU(t),n=function(e){let t=rC(sb,e.options),r=e.apiClient;return{authenticateRequest:(e,n={})=>{let{apiUrl:i,apiVersion:s}=t,a=rC(t,n);return sg(e,{...n,...a,apiUrl:i,apiVersion:s,apiClient:r})},debugRequestState:sm}}({options:t,apiClient:r}),i=new sZ({...e.telemetry,publishableKey:t.publishableKey,secretKey:t.secretKey,samplingRate:.1,...t.sdkMetadata?{sdk:t.sdkMetadata.name,sdkVersion:t.sdkMetadata.version}:{}});return{...r,...n,telemetry:i}})({...s0,...e});function s2(e,t){var r,n;return function(e){try{let{headers:t,nextUrl:r,cookies:n}=e||{};return"function"==typeof(null==t?void 0:t.get)&&"function"==typeof(null==r?void 0:r.searchParams.get)&&"function"==typeof(null==n?void 0:n.get)}catch{return!1}}(e)||function(e){try{let{headers:t}=e||{};return"function"==typeof(null==t?void 0:t.get)}catch{return!1}}(e)?e.headers.get(t):e.headers[t]||e.headers[t.toLowerCase()]||(null==(n=null==(r=e.socket)?void 0:r._httpMessage)?void 0:n.getHeader(t))}var s5=r(521);let s4=new Map,s3=new s5.AsyncLocalStorage;var s6=new Set,s8={warnOnce:e=>{s6.has(e)||(s6.add(e),console.warn(e))}};function s9(e){return/^http(s)?:\/\//.test(e||"")}var s7,ae,at,ar,an,ai,as,aa,ao,al,ac,au,ad,ah,ap,af,ag,am,ay,ab,a_,av,aw,ak,aS,aE,aT=Object.defineProperty,ax=(e,t,r)=>t in e?aT(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,aO=(null==(ab="undefined"!=typeof globalThis?globalThis:void 0)?void 0:ab.crypto)||(null==(a_=void 0!==r.g?r.g:void 0)?void 0:a_.crypto)||(null==(av="undefined"!=typeof window?window:void 0)?void 0:av.crypto)||(null==(aw="undefined"!=typeof self?self:void 0)?void 0:aw.crypto)||(null==(aS=null==(ak="undefined"!=typeof frames?frames:void 0)?void 0:ak[0])?void 0:aS.crypto);aE=aO?e=>{let t=[];for(let r=0;r<e;r+=4)t.push(aO.getRandomValues(new Uint32Array(1))[0]);return new aR(t,e)}:e=>{let t=[],r=e=>{let t=e,r=0x3ade68b1;return()=>{let e=((r=36969*(65535&r)+(r>>16)|0)<<16)+(t=18e3*(65535&t)+(t>>16)|0)|0;return e/=0x100000000,(e+=.5)*(Math.random()>.5?1:-1)}};for(let n=0,i;n<e;n+=4){let e=r(0x100000000*(i||Math.random()));i=0x3ade67b7*e(),t.push(0x100000000*e()|0)}return new aR(t,e)};var aC=class{static create(...e){return new this(...e)}mixIn(e){return Object.assign(this,e)}clone(){let e=new this.constructor;return Object.assign(e,this),e}},aR=class extends aC{constructor(e=[],t=4*e.length){super();let r=e;if(r instanceof ArrayBuffer&&(r=new Uint8Array(r)),(r instanceof Int8Array||r instanceof Uint8ClampedArray||r instanceof Int16Array||r instanceof Uint16Array||r instanceof Int32Array||r instanceof Uint32Array||r instanceof Float32Array||r instanceof Float64Array)&&(r=new Uint8Array(r.buffer,r.byteOffset,r.byteLength)),r instanceof Uint8Array){let e=r.byteLength,t=[];for(let n=0;n<e;n+=1)t[n>>>2]|=r[n]<<24-n%4*8;this.words=t,this.sigBytes=e}else this.words=e,this.sigBytes=t}toString(e=aP){return e.stringify(this)}concat(e){let t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(let e=0;e<i;e+=1){let i=r[e>>>2]>>>24-e%4*8&255;t[n+e>>>2]|=i<<24-(n+e)%4*8}else for(let e=0;e<i;e+=4)t[n+e>>>2]=r[e>>>2];return this.sigBytes+=i,this}clamp(){let{words:e,sigBytes:t}=this;e[t>>>2]&=0xffffffff<<32-t%4*8,e.length=Math.ceil(t/4)}clone(){let e=super.clone.call(this);return e.words=this.words.slice(0),e}};((e,t,r)=>ax(e,"symbol"!=typeof t?t+"":t,r))(aR,"random",aE);var aP={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push((r>>>4).toString(16)),n.push((15&r).toString(16))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new aR(r,t/2)}},aI={stringify(e){let{words:t,sigBytes:r}=e,n=[];for(let e=0;e<r;e+=1){let r=t[e>>>2]>>>24-e%4*8&255;n.push(String.fromCharCode(r))}return n.join("")},parse(e){let t=e.length,r=[];for(let n=0;n<t;n+=1)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new aR(r,t)}},aA={stringify(e){try{return decodeURIComponent(escape(aI.stringify(e)))}catch{throw Error("Malformed UTF-8 data")}},parse:e=>aI.parse(unescape(encodeURIComponent(e)))},aN=class extends aC{constructor(){super(),this._minBufferSize=0}reset(){this._data=new aR,this._nDataBytes=0}_append(e){let t=e;"string"==typeof t&&(t=aA.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes}_process(e){let t,{_data:r,blockSize:n}=this,i=r.words,s=r.sigBytes,a=s/(4*n),o=(a=e?Math.ceil(a):Math.max((0|a)-this._minBufferSize,0))*n,l=Math.min(4*o,s);if(o){for(let e=0;e<o;e+=n)this._doProcessBlock(i,e);t=i.splice(0,o),r.sigBytes-=l}return new aR(t,l)}clone(){let e=super.clone.call(this);return e._data=this._data.clone(),e}},aU=class extends aN{constructor(e){super(),this.blockSize=16,this.cfg=Object.assign(new aC,e),this.reset()}static _createHelper(e){return(t,r)=>new e(r).finalize(t)}static _createHmacHelper(e){return(t,r)=>new aM(e,r).finalize(t)}reset(){super.reset.call(this),this._doReset()}update(e){return this._append(e),this._process(),this}finalize(e){return e&&this._append(e),this._doFinalize()}},aM=class extends aC{constructor(e,t){super();let r=new e;this._hasher=r;let n=t;"string"==typeof n&&(n=aA.parse(n));let i=r.blockSize,s=4*i;n.sigBytes>s&&(n=r.finalize(t)),n.clamp();let a=n.clone();this._oKey=a;let o=n.clone();this._iKey=o;let l=a.words,c=o.words;for(let e=0;e<i;e+=1)l[e]^=0x5c5c5c5c,c[e]^=0x36363636;a.sigBytes=s,o.sigBytes=s,this.reset()}reset(){let e=this._hasher;e.reset(),e.update(this._iKey)}update(e){return this._hasher.update(e),this}finalize(e){let t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}},aj=(e,t,r)=>{let n=[],i=0;for(let s=0;s<t;s+=1)if(s%4){let t=r[e.charCodeAt(s-1)]<<s%4*2|r[e.charCodeAt(s)]>>>6-s%4*2;n[i>>>2]|=t<<24-i%4*8,i+=1}return aR.create(n,i)},aD={stringify(e){let{words:t,sigBytes:r}=e,n=this._map;e.clamp();let i=[];for(let e=0;e<r;e+=3){let s=(t[e>>>2]>>>24-e%4*8&255)<<16|(t[e+1>>>2]>>>24-(e+1)%4*8&255)<<8|t[e+2>>>2]>>>24-(e+2)%4*8&255;for(let t=0;t<4&&e+.75*t<r;t+=1)i.push(n.charAt(s>>>6*(3-t)&63))}let s=n.charAt(64);if(s)for(;i.length%4;)i.push(s);return i.join("")},parse(e){let t=e.length,r=this._map,n=this._reverseMap;if(!n){this._reverseMap=[],n=this._reverseMap;for(let e=0;e<r.length;e+=1)n[r.charCodeAt(e)]=e}let i=r.charAt(64);if(i){let r=e.indexOf(i);-1!==r&&(t=r)}return aj(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},aL=[];for(let e=0;e<64;e+=1)aL[e]=0x100000000*Math.abs(Math.sin(e+1))|0;var aq=(e,t,r,n,i,s,a)=>{let o=e+(t&r|~t&n)+i+a;return(o<<s|o>>>32-s)+t},aB=(e,t,r,n,i,s,a)=>{let o=e+(t&n|r&~n)+i+a;return(o<<s|o>>>32-s)+t},aH=(e,t,r,n,i,s,a)=>{let o=e+(t^r^n)+i+a;return(o<<s|o>>>32-s)+t},a$=(e,t,r,n,i,s,a)=>{let o=e+(r^(t|~n))+i+a;return(o<<s|o>>>32-s)+t},az=class extends aU{_doReset(){this._hash=new aR([0x67452301,0xefcdab89,0x98badcfe,0x10325476])}_doProcessBlock(e,t){for(let r=0;r<16;r+=1){let n=t+r,i=e[n];e[n]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00}let r=this._hash.words,n=e[t+0],i=e[t+1],s=e[t+2],a=e[t+3],o=e[t+4],l=e[t+5],c=e[t+6],u=e[t+7],d=e[t+8],h=e[t+9],p=e[t+10],f=e[t+11],g=e[t+12],m=e[t+13],y=e[t+14],b=e[t+15],_=r[0],v=r[1],w=r[2],k=r[3];_=aq(_,v,w,k,n,7,aL[0]),k=aq(k,_,v,w,i,12,aL[1]),w=aq(w,k,_,v,s,17,aL[2]),v=aq(v,w,k,_,a,22,aL[3]),_=aq(_,v,w,k,o,7,aL[4]),k=aq(k,_,v,w,l,12,aL[5]),w=aq(w,k,_,v,c,17,aL[6]),v=aq(v,w,k,_,u,22,aL[7]),_=aq(_,v,w,k,d,7,aL[8]),k=aq(k,_,v,w,h,12,aL[9]),w=aq(w,k,_,v,p,17,aL[10]),v=aq(v,w,k,_,f,22,aL[11]),_=aq(_,v,w,k,g,7,aL[12]),k=aq(k,_,v,w,m,12,aL[13]),w=aq(w,k,_,v,y,17,aL[14]),v=aq(v,w,k,_,b,22,aL[15]),_=aB(_,v,w,k,i,5,aL[16]),k=aB(k,_,v,w,c,9,aL[17]),w=aB(w,k,_,v,f,14,aL[18]),v=aB(v,w,k,_,n,20,aL[19]),_=aB(_,v,w,k,l,5,aL[20]),k=aB(k,_,v,w,p,9,aL[21]),w=aB(w,k,_,v,b,14,aL[22]),v=aB(v,w,k,_,o,20,aL[23]),_=aB(_,v,w,k,h,5,aL[24]),k=aB(k,_,v,w,y,9,aL[25]),w=aB(w,k,_,v,a,14,aL[26]),v=aB(v,w,k,_,d,20,aL[27]),_=aB(_,v,w,k,m,5,aL[28]),k=aB(k,_,v,w,s,9,aL[29]),w=aB(w,k,_,v,u,14,aL[30]),v=aB(v,w,k,_,g,20,aL[31]),_=aH(_,v,w,k,l,4,aL[32]),k=aH(k,_,v,w,d,11,aL[33]),w=aH(w,k,_,v,f,16,aL[34]),v=aH(v,w,k,_,y,23,aL[35]),_=aH(_,v,w,k,i,4,aL[36]),k=aH(k,_,v,w,o,11,aL[37]),w=aH(w,k,_,v,u,16,aL[38]),v=aH(v,w,k,_,p,23,aL[39]),_=aH(_,v,w,k,m,4,aL[40]),k=aH(k,_,v,w,n,11,aL[41]),w=aH(w,k,_,v,a,16,aL[42]),v=aH(v,w,k,_,c,23,aL[43]),_=aH(_,v,w,k,h,4,aL[44]),k=aH(k,_,v,w,g,11,aL[45]),w=aH(w,k,_,v,b,16,aL[46]),v=aH(v,w,k,_,s,23,aL[47]),_=a$(_,v,w,k,n,6,aL[48]),k=a$(k,_,v,w,u,10,aL[49]),w=a$(w,k,_,v,y,15,aL[50]),v=a$(v,w,k,_,l,21,aL[51]),_=a$(_,v,w,k,g,6,aL[52]),k=a$(k,_,v,w,a,10,aL[53]),w=a$(w,k,_,v,p,15,aL[54]),v=a$(v,w,k,_,i,21,aL[55]),_=a$(_,v,w,k,d,6,aL[56]),k=a$(k,_,v,w,b,10,aL[57]),w=a$(w,k,_,v,c,15,aL[58]),v=a$(v,w,k,_,m,21,aL[59]),_=a$(_,v,w,k,o,6,aL[60]),k=a$(k,_,v,w,f,10,aL[61]),w=a$(w,k,_,v,s,15,aL[62]),v=a$(v,w,k,_,h,21,aL[63]),r[0]=r[0]+_|0,r[1]=r[1]+v|0,r[2]=r[2]+w|0,r[3]=r[3]+k|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32;let i=Math.floor(r/0x100000000);t[(n+64>>>9<<4)+15]=(i<<8|i>>>24)&0xff00ff|(i<<24|i>>>8)&0xff00ff00,t[(n+64>>>9<<4)+14]=(r<<8|r>>>24)&0xff00ff|(r<<24|r>>>8)&0xff00ff00,e.sigBytes=(t.length+1)*4,this._process();let s=this._hash,a=s.words;for(let e=0;e<4;e+=1){let t=a[e];a[e]=(t<<8|t>>>24)&0xff00ff|(t<<24|t>>>8)&0xff00ff00}return s}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}};aU._createHelper(az),aU._createHmacHelper(az);var aK=class extends aC{constructor(e){super(),this.cfg=Object.assign(new aC,{keySize:4,hasher:az,iterations:1},e)}compute(e,t){let r,{cfg:n}=this,i=n.hasher.create(),s=aR.create(),a=s.words,{keySize:o,iterations:l}=n;for(;a.length<o;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(let e=1;e<l;e+=1)r=i.finalize(r),i.reset();s.concat(r)}return s.sigBytes=4*o,s}},aJ=class extends aN{constructor(e,t,r){super(),this.cfg=Object.assign(new aC,r),this._xformMode=e,this._key=t,this.reset()}static createEncryptor(e,t){return this.create(this._ENC_XFORM_MODE,e,t)}static createDecryptor(e,t){return this.create(this._DEC_XFORM_MODE,e,t)}static _createHelper(e){let t=e=>"string"==typeof e?aZ:aY;return{encrypt:(r,n,i)=>t(n).encrypt(e,r,n,i),decrypt:(r,n,i)=>t(n).decrypt(e,r,n,i)}}reset(){super.reset.call(this),this._doReset()}process(e){return this._append(e),this._process()}finalize(e){return e&&this._append(e),this._doFinalize()}};aJ._ENC_XFORM_MODE=1,aJ._DEC_XFORM_MODE=2,aJ.keySize=4,aJ.ivSize=4;var aW=class extends aC{constructor(e,t){super(),this._cipher=e,this._iv=t}static createEncryptor(e,t){return this.Encryptor.create(e,t)}static createDecryptor(e,t){return this.Decryptor.create(e,t)}};function aF(e,t,r){let n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(let i=0;i<r;i+=1)e[t+i]^=n[i]}var aV=class extends aW{};aV.Encryptor=class extends aV{processBlock(e,t){let r=this._cipher,{blockSize:n}=r;aF.call(this,e,t,n),r.encryptBlock(e,t),this._prevBlock=e.slice(t,t+n)}},aV.Decryptor=class extends aV{processBlock(e,t){let r=this._cipher,{blockSize:n}=r,i=e.slice(t,t+n);r.decryptBlock(e,t),aF.call(this,e,t,n),this._prevBlock=i}};var aG={pad(e,t){let r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,s=[];for(let e=0;e<n;e+=4)s.push(i);let a=aR.create(s,n);e.concat(a)},unpad(e){let t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},aX=class extends aJ{constructor(e,t,r){super(e,t,Object.assign({mode:aV,padding:aG},r)),this.blockSize=4}reset(){let e;super.reset.call(this);let{cfg:t}=this,{iv:r,mode:n}=t;this._xformMode===this.constructor._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e}_doProcessBlock(e,t){this._mode.processBlock(e,t)}_doFinalize(){let e,{padding:t}=this.cfg;return this._xformMode===this.constructor._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e}},aQ=class extends aC{constructor(e){super(),this.mixIn(e)}toString(e){return(e||this.formatter).stringify(this)}},aY=class extends aC{static encrypt(e,t,r,n){let i=Object.assign(new aC,this.cfg,n),s=e.createEncryptor(r,i),a=s.finalize(t),o=s.cfg;return aQ.create({ciphertext:a,key:r,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:s.blockSize,formatter:i.format})}static decrypt(e,t,r,n){let i=t,s=Object.assign(new aC,this.cfg,n);return i=this._parse(i,s.format),e.createDecryptor(r,s).finalize(i.ciphertext)}static _parse(e,t){return"string"==typeof e?t.parse(e,this):e}};aY.cfg=Object.assign(new aC,{format:{stringify(e){let t,{ciphertext:r,salt:n}=e;return(n?aR.create([0x53616c74,0x65645f5f]).concat(n).concat(r):r).toString(aD)},parse(e){let t,r=aD.parse(e),n=r.words;return 0x53616c74===n[0]&&0x65645f5f===n[1]&&(t=aR.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),aQ.create({ciphertext:r,salt:t})}}});var aZ=class extends aY{static encrypt(e,t,r,n){let i=Object.assign(new aC,this.cfg,n),s=i.kdf.execute(r,e.keySize,e.ivSize,i.salt,i.hasher);i.iv=s.iv;let a=aY.encrypt.call(this,e,t,s.key,i);return a.mixIn(s),a}static decrypt(e,t,r,n){let i=t,s=Object.assign(new aC,this.cfg,n);i=this._parse(i,s.format);let a=s.kdf.execute(r,e.keySize,e.ivSize,i.salt,s.hasher);return s.iv=a.iv,aY.decrypt.call(this,e,i,a.key,s)}};aZ.cfg=Object.assign(aY.cfg,{kdf:{execute(e,t,r,n,i){let s,a=n;a||(a=aR.random(8)),s=i?aK.create({keySize:t+r,hasher:i}).compute(e,a):aK.create({keySize:t+r}).compute(e,a);let o=aR.create(s.words.slice(t),4*r);return s.sigBytes=4*t,aQ.create({key:s,iv:o,salt:a})}}});var a0=[],a1=[],a2=[],a5=[],a4=[],a3=[],a6=[],a8=[],a9=[],a7=[],oe=[];for(let e=0;e<256;e+=1)e<128?oe[e]=e<<1:oe[e]=e<<1^283;var ot=0,or=0;for(let e=0;e<256;e+=1){let e=or^or<<1^or<<2^or<<3^or<<4;e=e>>>8^255&e^99,a0[ot]=e,a1[e]=ot;let t=oe[ot],r=oe[t],n=oe[r],i=257*oe[e]^0x1010100*e;a2[ot]=i<<24|i>>>8,a5[ot]=i<<16|i>>>16,a4[ot]=i<<8|i>>>24,a3[ot]=i,i=0x1010101*n^65537*r^257*t^0x1010100*ot,a6[e]=i<<24|i>>>8,a8[e]=i<<16|i>>>16,a9[e]=i<<8|i>>>24,a7[e]=i,ot?(ot=t^oe[oe[oe[n^t]]],or^=oe[oe[or]]):ot=or=1}var on=[0,1,2,4,8,16,32,64,128,27,54],oi=class extends aX{_doReset(){let e;if(this._nRounds&&this._keyPriorReset===this._key)return;this._keyPriorReset=this._key;let t=this._keyPriorReset,r=t.words,n=t.sigBytes/4;this._nRounds=n+6;let i=(this._nRounds+1)*4;this._keySchedule=[];let s=this._keySchedule;for(let t=0;t<i;t+=1)t<n?s[t]=r[t]:(e=s[t-1],t%n?n>6&&t%n==4&&(e=a0[e>>>24]<<24|a0[e>>>16&255]<<16|a0[e>>>8&255]<<8|a0[255&e]):e=(a0[(e=e<<8|e>>>24)>>>24]<<24|a0[e>>>16&255]<<16|a0[e>>>8&255]<<8|a0[255&e])^on[t/n|0]<<24,s[t]=s[t-n]^e);this._invKeySchedule=[];let a=this._invKeySchedule;for(let t=0;t<i;t+=1){let r=i-t;e=t%4?s[r]:s[r-4],t<4||r<=4?a[t]=e:a[t]=a6[a0[e>>>24]]^a8[a0[e>>>16&255]]^a9[a0[e>>>8&255]]^a7[a0[255&e]]}}encryptBlock(e,t){this._doCryptBlock(e,t,this._keySchedule,a2,a5,a4,a3,a0)}decryptBlock(e,t){let r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,a6,a8,a9,a7,a1),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r}_doCryptBlock(e,t,r,n,i,s,a,o){let l=this._nRounds,c=e[t]^r[0],u=e[t+1]^r[1],d=e[t+2]^r[2],h=e[t+3]^r[3],p=4;for(let e=1;e<l;e+=1){let e=n[c>>>24]^i[u>>>16&255]^s[d>>>8&255]^a[255&h]^r[p];p+=1;let t=n[u>>>24]^i[d>>>16&255]^s[h>>>8&255]^a[255&c]^r[p];p+=1;let o=n[d>>>24]^i[h>>>16&255]^s[c>>>8&255]^a[255&u]^r[p];p+=1;let l=n[h>>>24]^i[c>>>16&255]^s[u>>>8&255]^a[255&d]^r[p];p+=1,c=e,u=t,d=o,h=l}let f=(o[c>>>24]<<24|o[u>>>16&255]<<16|o[d>>>8&255]<<8|o[255&h])^r[p];p+=1;let g=(o[u>>>24]<<24|o[d>>>16&255]<<16|o[h>>>8&255]<<8|o[255&c])^r[p];p+=1;let m=(o[d>>>24]<<24|o[h>>>16&255]<<16|o[c>>>8&255]<<8|o[255&u])^r[p];p+=1;let y=(o[h>>>24]<<24|o[c>>>16&255]<<16|o[u>>>8&255]<<8|o[255&d])^r[p];p+=1,e[t]=f,e[t+1]=g,e[t+2]=m,e[t+3]=y}};oi.keySize=8;var os=aX._createHelper(oi),oa=[],oo=class extends aU{_doReset(){this._hash=new aR([0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0])}_doProcessBlock(e,t){let r=this._hash.words,n=r[0],i=r[1],s=r[2],a=r[3],o=r[4];for(let r=0;r<80;r+=1){if(r<16)oa[r]=0|e[t+r];else{let e=oa[r-3]^oa[r-8]^oa[r-14]^oa[r-16];oa[r]=e<<1|e>>>31}let l=(n<<5|n>>>27)+o+oa[r];r<20?l+=(i&s|~i&a)+0x5a827999:r<40?l+=(i^s^a)+0x6ed9eba1:r<60?l+=(i&s|i&a|s&a)-0x70e44324:l+=(i^s^a)-0x359d3e2a,o=a,a=s,s=i<<30|i>>>2,i=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+s|0,r[3]=r[3]+a|0,r[4]=r[4]+o|0}_doFinalize(){let e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[(n+64>>>9<<4)+14]=Math.floor(r/0x100000000),t[(n+64>>>9<<4)+15]=r,e.sigBytes=4*t.length,this._process(),this._hash}clone(){let e=super.clone.call(this);return e._hash=this._hash.clone(),e}},ol=(aU._createHelper(oo),aU._createHmacHelper(oo));let oc=`
Missing domain and proxyUrl. A satellite application needs to specify a domain or a proxyUrl.

1) With middleware
   e.g. export default clerkMiddleware({domain:'YOUR_DOMAIN',isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_DOMAIN='YOUR_DOMAIN'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'
   `,ou=`
Invalid signInUrl. A satellite application requires a signInUrl for development instances.
Check if signInUrl is missing from your configuration or if it is not an absolute URL

1) With middleware
   e.g. export default clerkMiddleware({signInUrl:'SOME_URL', isSatellite:true});
2) With environment variables e.g.
   NEXT_PUBLIC_CLERK_SIGN_IN_URL='SOME_URL'
   NEXT_PUBLIC_CLERK_IS_SATELLITE='true'`,od=`Clerk: Unable to decrypt request data.

Refresh the page if your .env file was just updated. If the issue persists, ensure the encryption key is valid and properly set.

For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)`,oh=tj({packageName:"@clerk/nextjs"}),op="x-middleware-override-headers",of="x-middleware-request",og=(e,t,r)=>{e.headers.get(op)||(e.headers.set(op,[...t.headers.keys()]),t.headers.forEach((t,r)=>{e.headers.set(`${of}-${r}`,t)})),Object.entries(r).forEach(([t,r])=>{e.headers.set(op,`${e.headers.get(op)},${t}`),e.headers.set(`${of}-${t}`,r)})},om=(e,t)=>{let r,n=sN(null==t?void 0:t.proxyUrl,e.clerkUrl,s$);r=n&&!s9(n)?new URL(n,e.clerkUrl).toString():n;let i=sN(t.isSatellite,new URL(e.url),sz),s=sN(t.domain,new URL(e.url),sH),a=(null==t?void 0:t.signInUrl)||sK;if(i&&!r&&!s)throw Error(oc);if(i&&!s9(a)&&tx(t.secretKey||sD))throw Error(ou);return{proxyUrl:r,isSatellite:i,domain:s,signInUrl:a}},oy=e=>z.redirect(e,{headers:{[rE.Headers.ClerkRedirectTo]:"true"}}),ob="clerk_keyless_dummy_key";function o_(){if(tP())throw Error("Clerk: Unable to decrypt request data, this usually means the encryption key is invalid. Ensure the encryption key is properly set. For more information, see: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys. (code=encryption_key_invalid)");throw Error(od)}function ov(e,t){return JSON.parse(os.decrypt(e,t).toString(aA))}let ow=async()=>{var e,t;let r;try{let e=await sX(),t=s2(e,rE.Headers.ClerkRequestData);r=function(e){if(!e)return{};let t=tP()?sq||sD:sq||sD||ob;try{return ov(e,t)}catch{if(sV)try{return ov(e,ob)}catch{o_()}o_()}}(t)}catch(e){if(e&&sG(e))throw e}let n=null!=(t=null==(e=s3.getStore())?void 0:e.get("requestData"))?t:r;return(null==n?void 0:n.secretKey)||(null==n?void 0:n.publishableKey)?s1(n):s1({})};class ok{static createDefaultDirectives(){return Object.entries(this.DEFAULT_DIRECTIVES).reduce((e,[t,r])=>(e[t]=new Set(r),e),{})}static isKeyword(e){return this.KEYWORDS.has(e.replace(/^'|'$/g,""))}static formatValue(e){let t=e.replace(/^'|'$/g,"");return this.isKeyword(t)?`'${t}'`:e}static handleDirectiveValues(e){let t=new Set;return e.includes("'none'")||e.includes("none")?t.add("'none'"):e.forEach(e=>t.add(this.formatValue(e))),t}}ok.KEYWORDS=new Set(["none","self","strict-dynamic","unsafe-eval","unsafe-hashes","unsafe-inline"]),ok.DEFAULT_DIRECTIVES={"connect-src":["self","https://clerk-telemetry.com","https://*.clerk-telemetry.com","https://api.stripe.com","https://maps.googleapis.com"],"default-src":["self"],"form-action":["self"],"frame-src":["self","https://challenges.cloudflare.com","https://*.js.stripe.com","https://js.stripe.com","https://hooks.stripe.com"],"img-src":["self","https://img.clerk.com"],"script-src":["self","unsafe-inline","https:","http:","https://*.js.stripe.com","https://js.stripe.com","https://maps.googleapis.com"],"style-src":["self","unsafe-inline"],"worker-src":["self","blob:"]};let oS="__clerk_keys_";async function oE(e){let t=new TextEncoder().encode(e);return Array.from(new Uint8Array(await crypto.subtle.digest("SHA-256",t))).map(e=>e.toString(16).padStart(2,"0")).join("").slice(0,16)}async function oT(){let e=process.env.PWD;if(!e)return`${oS}0`;let t=e.split("/").filter(Boolean).slice(-3).reverse().join("/"),r=await oE(t);return`${oS}${r}`}async function ox(e){let t;if(!sV)return;let r=await oT();try{r&&(t=JSON.parse(e(r)||"{}"))}catch{t=void 0}return t}let oO={REDIRECT_TO_URL:"CLERK_PROTECT_REDIRECT_TO_URL",REDIRECT_TO_SIGN_IN:"CLERK_PROTECT_REDIRECT_TO_SIGN_IN",REDIRECT_TO_SIGN_UP:"CLERK_PROTECT_REDIRECT_TO_SIGN_UP"},oC={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},oR=new Set(Object.values(oC)),oP="NEXT_HTTP_ERROR_FALLBACK";function oI(e){if(!function(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r]=e.digest.split(";");return t===oP&&oR.has(Number(r))}(e))return;let[,t]=e.digest.split(";");return Number(t)}let oA="NEXT_REDIRECT";function oN(e,t,r="replace",n=307){let i=Error(oA);throw i.digest=`${oA};${r};${e};${n};`,i.clerk_digest=oO.REDIRECT_TO_URL,Object.assign(i,t),i}function oU(e,t){return null===t?"":t||e}function oM(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";"),[r,n]=t,i=t.slice(2,-2).join(";"),s=Number(t.at(-2));return r===oA&&("replace"===n||"push"===n)&&"string"==typeof i&&!isNaN(s)&&307===s}function oj(){let e=Error(oP);throw e.digest=`${oP};${oC.UNAUTHORIZED}`,e}let oD=e=>{if(e&&!e.unauthenticatedUrl&&!e.unauthorizedUrl&&!e.token&&(1!==Object.keys(e).length||!("token"in e)))return e},oL=e=>{var t,r;return!!e.headers.get(sw.Headers.NextUrl)&&((null==(t=e.headers.get(rE.Headers.Accept))?void 0:t.includes("text/x-component"))||(null==(r=e.headers.get(rE.Headers.ContentType))?void 0:r.includes("multipart/form-data"))||!!e.headers.get(sw.Headers.NextAction))},oq=e=>{var t;return"document"===e.headers.get(rE.Headers.SecFetchDest)||"iframe"===e.headers.get(rE.Headers.SecFetchDest)||(null==(t=e.headers.get(rE.Headers.Accept))?void 0:t.includes("text/html"))||oB(e)||o$(e)},oB=e=>!!e.headers.get(sw.Headers.NextUrl)&&!oL(e)||oH(),oH=()=>{let e=globalThis.fetch;if(!function(e){return"__nextPatched"in e&&!0===e.__nextPatched}(e))return!1;let{page:t,pagePath:r}=e.__nextGetStaticStore().getStore()||{};return!!(r||t)},o$=e=>!!e.headers.get(sw.Headers.NextjsData),oz=e=>[e[0]instanceof Request?e[0]:void 0,e[0]instanceof Request?e[1]:void 0],oK=e=>["function"==typeof e[0]?e[0]:void 0,(2===e.length?e[1]:"function"==typeof e[0]?{}:e[0])||{}],oJ=e=>"/clerk-sync-keyless"===e.nextUrl.pathname,oW=e=>{let t=e.nextUrl.searchParams.get("returnUrl"),r=new URL(e.url);return r.pathname="",z.redirect(t||r.toString())},oF=(e,t)=>({...t,...om(e,t),acceptsToken:"any"}),oV=e=>(t={})=>{!function(e,t){oN(e,{clerk_digest:oO.REDIRECT_TO_SIGN_IN,returnBackUrl:oU(e,t)})}(e.clerkUrl.toString(),t.returnBackUrl)},oG=e=>(t={})=>{!function(e,t){oN(e,{clerk_digest:oO.REDIRECT_TO_SIGN_UP,returnBackUrl:oU(e,t)})}(e.clerkUrl.toString(),t.returnBackUrl)},oX=(e,t,r)=>async(n,i)=>(function(e){let{redirectToSignIn:t,authObject:r,redirect:n,notFound:i,request:s,unauthorized:a}=e;return async(...e)=>{var o,l,c,u,d,h;let p=oD(e[0]),f=(null==(o=e[0])?void 0:o.unauthenticatedUrl)||(null==(l=e[1])?void 0:l.unauthenticatedUrl),g=(null==(c=e[0])?void 0:c.unauthorizedUrl)||(null==(u=e[1])?void 0:u.unauthorizedUrl),m=(null==(d=e[0])?void 0:d.token)||(null==(h=e[1])?void 0:h.token)||iM.SessionToken,y=()=>r.tokenType!==iM.SessionToken?a():g?n(g):i();return iB(r.tokenType,m)?r.tokenType!==iM.SessionToken?r.isAuthenticated?r:y():"pending"!==r.sessionStatus&&r.userId?p?"function"==typeof p?p(r.has)?r:y():r.has(p)?r:y():r:f?n(f):oq(s)?t():i():y()}})({request:e,redirect:e=>oN(e,{redirectUrl:e}),notFound:()=>(function(){let e=Object.defineProperty(Error(sv),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=sv,e})(),unauthorized:oj,authObject:iF({authObject:t,acceptsToken:(null==n?void 0:n.token)||(null==i?void 0:i.token)||iM.SessionToken}),redirectToSignIn:r})(n,i),oQ=(e,t,r)=>async n=>{var i;let s=e.toAuth({treatPendingAsSignedOut:null==n?void 0:n.treatPendingAsSignedOut}),a=null!=(i=null==n?void 0:n.acceptsToken)?i:iM.SessionToken,o=iF({authObject:s,acceptsToken:a});return o.tokenType===iM.SessionToken&&iB(iM.SessionToken,a)?Object.assign(o,{redirectToSignIn:t,redirectToSignUp:r}):o},oY=(e,t,r,n)=>{var i;if(oI(e)===oC.UNAUTHORIZED){let e=new z(null,{status:401}),t=n.toAuth();if(t&&t.tokenType===iM.OAuthToken){let t=tE(n.publishableKey);return sS(e,"WWW-Authenticate",`Bearer resource_metadata="https://${null==t?void 0:t.frontendApi}/.well-known/oauth-protected-resource"`)}return e}if(function(e){return"object"==typeof e&&null!==e&&"digest"in e&&"NEXT_NOT_FOUND"===e.digest||oI(e)===oC.NOT_FOUND}(e))return sS(z.rewrite(new URL(`/clerk_${Date.now()}`,r.url)),rE.Headers.AuthReason,"protect-rewrite");let s=function(e){return!!oM(e)&&"clerk_digest"in e&&e.clerk_digest===oO.REDIRECT_TO_SIGN_IN}(e),a=function(e){return!!oM(e)&&"clerk_digest"in e&&e.clerk_digest===oO.REDIRECT_TO_SIGN_UP}(e);if(s||a){let r=rO({redirectAdapter:oy,baseUrl:t.clerkUrl,signInUrl:n.signInUrl,signUpUrl:n.signUpUrl,publishableKey:n.publishableKey,sessionStatus:null==(i=n.toAuth())?void 0:i.sessionStatus}),{returnBackUrl:a}=e;return r[s?"redirectToSignIn":"redirectToSignUp"]({returnBackUrl:a})}if(oM(e))return oy(e.redirectUrl);throw e},oZ=(e=>{if("function"==typeof e)return t=>e(t);let t=e7(e);return e=>t(e.nextUrl.pathname)})(["/user-dashboard(.*)","/admin(.*)","/profile(.*)","/projects(.*)","/settings(.*)"]),o0=((...e)=>{let[t,r]=oz(e),[n,i]=oK(e);return s3.run(s4,()=>{let e=sA("clerkMiddleware",e=>async(t,r)=>{var s,a;let o="function"==typeof i?await i(t):i,l=await ox(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value}),c=function(e,t){return e||t(),e}(o.publishableKey||sL||(null==l?void 0:l.publishableKey),()=>oh.throwMissingPublishableKeyError()),u=function(e,t){return e||t(),e}(o.secretKey||sD||(null==l?void 0:l.secretKey),()=>oh.throwMissingSecretKeyError()),d={publishableKey:c,secretKey:u,signInUrl:o.signInUrl||sK,signUpUrl:o.signUpUrl||"/auth/register",...o};s4.set("requestData",d);let h=await ow();d.debug&&e.enable();let p=i2(t);e.debug("options",d),e.debug("url",()=>p.toJSON());let f=t.headers.get(rE.Headers.Authorization);f&&f.startsWith("Basic ")&&e.debug("Basic Auth detected");let g=t.headers.get(rE.Headers.ContentSecurityPolicy);g&&e.debug("Content-Security-Policy detected",()=>({value:g}));let m=await h.authenticateRequest(p,oF(p,d));e.debug("requestState",()=>({status:m.status,headers:JSON.stringify(Object.fromEntries(m.headers)),reason:m.reason}));let y=m.headers.get(rE.Headers.Location);if(y){let e=z.redirect(y);return m.headers.forEach((t,r)=>{r!==rE.Headers.Location&&e.headers.append(r,t)}),e}if(m.status===iV.Handshake)throw Error("Clerk: handshake status without redirect");let b=m.toAuth();e.debug("auth",()=>({auth:b,debug:b.debug()}));let _=oV(p),v=oG(p),w=await oX(p,b,_),k=oQ(m,_,v);k.protect=w;let S=z.next();try{S=await s3.run(s4,async()=>null==n?void 0:n(k,t,r))||S}catch(e){S=oY(e,p,t,m)}if(d.contentSecurityPolicy){let{headers:t}=function(e,t){var r;let n=[],i=t.strict?function(){let e=new Uint8Array(16);return crypto.getRandomValues(e),btoa(Array.from(e,e=>String.fromCharCode(e)).join(""))}():void 0,s=function(e,t,r,n){let i=Object.entries(ok.DEFAULT_DIRECTIVES).reduce((e,[t,r])=>(e[t]=new Set(r),e),{});if(i["connect-src"].add(t),e&&(i["script-src"].delete("http:"),i["script-src"].delete("https:"),i["script-src"].add("'strict-dynamic'"),n&&i["script-src"].add(`'nonce-${n}'`)),r){let e=new Map;Object.entries(r).forEach(([t,r])=>{let n=Array.isArray(r)?r:[r];ok.DEFAULT_DIRECTIVES[t]?function(e,t,r){if(r.includes("'none'")||r.includes("none")){e[t]=new Set(["'none'"]);return}let n=new Set;e[t].forEach(e=>{n.add(ok.formatValue(e))}),r.forEach(e=>{n.add(ok.formatValue(e))}),e[t]=n}(i,t,n):function(e,t,r){if(r.includes("'none'")||r.includes("none"))return e.set(t,new Set(["'none'"]));let n=new Set;r.forEach(e=>{let t=ok.formatValue(e);n.add(t)}),e.set(t,n)}(e,t,n)}),e.forEach((e,t)=>{i[t]=e})}return Object.entries(i).sort(([e],[t])=>e.localeCompare(t)).map(([e,t])=>{let r=Array.from(t).map(e=>({raw:e,formatted:ok.formatValue(e)}));return`${e} ${r.map(e=>e.formatted).join(" ")}`}).join("; ")}(null!=(r=t.strict)&&r,e,t.directives,i);return t.reportTo&&(s+="; report-to csp-endpoint",n.push([rE.Headers.ReportingEndpoints,`csp-endpoint="${t.reportTo}"`])),t.reportOnly?n.push([rE.Headers.ContentSecurityPolicyReportOnly,s]):n.push([rE.Headers.ContentSecurityPolicy,s]),i&&n.push([rE.Headers.Nonce,i]),{headers:n}}((null!=(a=null==(s=tE(c))?void 0:s.frontendApi)?a:"").replace("$",""),d.contentSecurityPolicy);t.forEach(([e,t])=>{sS(S,e,t)}),e.debug("Clerk generated CSP",()=>({headers:t}))}if(m.headers&&m.headers.forEach((t,r)=>{r===rE.Headers.ContentSecurityPolicy&&e.debug("Content-Security-Policy detected",()=>({value:t})),S.headers.append(r,t)}),sk(S))return e.debug("handlerResult is redirect"),sO(p,S,d);d.debug&&og(S,p,{[rE.Headers.EnableDebug]:"true"});let E=u===(null==l?void 0:l.secretKey)?{publishableKey:null==l?void 0:l.publishableKey,secretKey:null==l?void 0:l.secretKey}:{};return!function(e,t,r,n,i,s){let a,{reason:o,message:l,status:c,token:u}=r;if(t||(t=z.next()),t.headers.get(sw.Headers.NextRedirect))return;"1"===t.headers.get(sw.Headers.NextResume)&&(t.headers.delete(sw.Headers.NextResume),a=new URL(e.url));let d=t.headers.get(sw.Headers.NextRewrite);if(d){let t=new URL(e.url);if((a=new URL(d)).origin!==t.origin)return}if(a){let r=function(e,t,r){var n;let i=e=>!e||!Object.values(e).some(e=>void 0!==e);if(i(e)&&i(t)&&!r)return;if(e.secretKey&&!sq)return void s8.warnOnce("Clerk: Missing `CLERK_ENCRYPTION_KEY`. Required for propagating `secretKey` middleware option. See docs: https://clerk.com/docs/references/nextjs/clerk-middleware#dynamic-keys");let s=tP()?sq||(n=()=>oh.throwMissingSecretKeyError(),sD||n(),sD):sq||sD||ob;return os.encrypt(JSON.stringify({...t,...e,machineAuthObject:null!=r?r:void 0}),s).toString()}(n,i,s);og(t,e,{[rE.Headers.AuthStatus]:c,[rE.Headers.AuthToken]:u||"",[rE.Headers.AuthSignature]:u?ol(u,(null==n?void 0:n.secretKey)||sD||i.secretKey||"").toString():"",[rE.Headers.AuthMessage]:l||"",[rE.Headers.AuthReason]:o||"",[rE.Headers.ClerkUrl]:e.clerkUrl.toString(),...r?{[rE.Headers.ClerkRequestData]:r}:{}}),t.headers.set(sw.Headers.NextRewrite,a.href)}}(p,S,m,o,E,"session_token"===b.tokenType?null:iJ(b)),S}),s=async(t,r)=>{var n,s;if(oJ(t))return oW(t);let a="function"==typeof i?await i(t):i,o=await ox(e=>{var r;return null==(r=t.cookies.get(e))?void 0:r.value}),l=!(a.publishableKey||sL||(null==o?void 0:o.publishableKey)),c=null!=(s=null==(n=s2(t,rE.Headers.Authorization))?void 0:n.replace("Bearer ",""))?s:"";if(l&&!iL(c)){let e=z.next();return og(e,t,{[rE.Headers.AuthStatus]:"signed-out"}),e}return e(t,r)},a=async(t,r)=>sV?s(t,r):e(t,r);return t&&r?a(t,r):a})})(async(e,t)=>{oZ(t)&&await e.protect()}),o1={matcher:["/((?!.*\\..*|_next).*)","/","/(api|trpc)(.*)"]};r(199);let o2={...i},o5=o2.middleware||o2.default,o4="/src/middleware";if("function"!=typeof o5)throw Object.defineProperty(Error(`The Middleware "${o4}" must export a \`middleware\` or a \`default\` function`),"__NEXT_ERROR_CODE",{value:"E120",enumerable:!1,configurable:!0});function o3(e){return e5({...e,page:o4,handler:async(...e)=>{try{return await o5(...e)}catch(i){let t=e[0],r=new URL(t.url),n=r.pathname+r.search;throw await l(i,{path:n,method:t.method,headers:Object.fromEntries(t.headers.entries())},{routerKind:"Pages Router",routePath:"/middleware",routeType:"middleware",revalidateReason:void 0}),i}}})}},890:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var i={},s=t.split(n),a=(r||{}).decode||e,o=0;o<s.length;o++){var l=s[o],c=l.indexOf("=");if(!(c<0)){var u=l.substr(0,c).trim(),d=l.substr(++c,l.length).trim();'"'==d[0]&&(d=d.slice(1,-1)),void 0==i[u]&&(i[u]=function(e,t){try{return t(e)}catch(t){return e}}(d,a))}}return i},t.serialize=function(e,t,n){var s=n||{},a=s.encode||r;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!i.test(e))throw TypeError("argument name is invalid");var o=a(t);if(o&&!i.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=s.maxAge){var c=s.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(s.domain){if(!i.test(s.domain))throw TypeError("option domain is invalid");l+="; Domain="+s.domain}if(s.path){if(!i.test(s.path))throw TypeError("option path is invalid");l+="; Path="+s.path}if(s.expires){if("function"!=typeof s.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+s.expires.toUTCString()}if(s.httpOnly&&(l+="; HttpOnly"),s.secure&&(l+="; Secure"),s.sameSite)switch("string"==typeof s.sameSite?s.sameSite.toLowerCase():s.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,i=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},905:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return s},wrapRequestHandler:function(){return a}});let n=r(201),i=r(552);function s(){return(0,i.interceptFetch)(r.g.fetch)}function a(e){return(t,r)=>(0,n.withRequest)(t,i.reader,()=>e(t,r))}},956:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let n=r(223),i=r(172),s=r(930),a="context",o=new n.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,i.registerGlobal)(a,e,s.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...n){return this._getContextManager().with(e,t,r,...n)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,i.getGlobal)(a)||o}disable(){this._getContextManager().disable(),(0,i.unregisterGlobal)(a,s.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let n=r(56),i=r(912),s=r(957),a=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,a.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:s.DiagLogLevel.INFO})=>{var n,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!=(n=e.stack)?n:e.message),!1}"number"==typeof r&&(r={logLevel:r});let c=(0,a.getGlobal)("diag"),u=(0,i.createLogLevelDiagLogger)(null!=(o=r.logLevel)?o:s.DiagLogLevel.INFO,e);if(c&&!r.suppressOverrideMessage){let e=null!=(l=Error().stack)?l:"<failed to generate stacktrace>";c.warn(`Current logger will be overwritten from ${e}`),u.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,a.registerGlobal)("diag",u,t,!0)},t.disable=()=>{(0,a.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new n.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let n=r(660),i=r(172),s=r(930),a="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,i.registerGlobal)(a,e,s.DiagAPI.instance())}getMeterProvider(){return(0,i.getGlobal)(a)||n.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,i.unregisterGlobal)(a,s.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let n=r(172),i=r(874),s=r(194),a=r(277),o=r(369),l=r(930),c="propagation",u=new i.NoopTextMapPropagator;class d{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=a.getBaggage,this.getActiveBaggage=a.getActiveBaggage,this.setBaggage=a.setBaggage,this.deleteBaggage=a.deleteBaggage}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalPropagator(e){return(0,n.registerGlobal)(c,e,l.DiagAPI.instance())}inject(e,t,r=s.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=s.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,n.unregisterGlobal)(c,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,n.getGlobal)(c)||u}}t.PropagationAPI=d},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let n=r(172),i=r(846),s=r(139),a=r(607),o=r(930),l="trace";class c{constructor(){this._proxyTracerProvider=new i.ProxyTracerProvider,this.wrapSpanContext=s.wrapSpanContext,this.isSpanContextValid=s.isSpanContextValid,this.deleteSpan=a.deleteSpan,this.getSpan=a.getSpan,this.getActiveSpan=a.getActiveSpan,this.getSpanContext=a.getSpanContext,this.setSpan=a.setSpan,this.setSpanContext=a.setSpanContext}static getInstance(){return this._instance||(this._instance=new c),this._instance}setGlobalTracerProvider(e){let t=(0,n.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,n.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,n.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new i.ProxyTracerProvider}}t.TraceAPI=c},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let n=r(491),i=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function s(e){return e.getValue(i)||void 0}t.getBaggage=s,t.getActiveBaggage=function(){return s(n.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(i,t)},t.deleteBaggage=function(e){return e.deleteValue(i)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let n=new r(this._entries);return n._entries.set(e,t),n}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let n=r(930),i=r(993),s=r(830),a=n.DiagAPI.instance();t.createBaggage=function(e={}){return new i.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(a.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:s.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0,t.context=r(491).ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let n=r(780);class i{active(){return n.ROOT_CONTEXT}with(e,t,r,...n){return t.call(r,...n)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=i},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,n)=>{let i=new r(t._currentContext);return i._currentContext.set(e,n),i},t.deleteValue=e=>{let n=new r(t._currentContext);return n._currentContext.delete(e),n}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0,t.diag=r(930).DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let n=r(172);class i{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return s("debug",this._namespace,e)}error(...e){return s("error",this._namespace,e)}info(...e){return s("info",this._namespace,e)}warn(...e){return s("warn",this._namespace,e)}verbose(...e){return s("verbose",this._namespace,e)}}function s(e,t,r){let i=(0,n.getGlobal)("diag");if(i)return r.unshift(t),i[e](...r)}t.DiagComponentLogger=i},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class n{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=n},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let n=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,n){let i=t[r];return"function"==typeof i&&e>=n?i.bind(t):function(){}}return e<n.DiagLogLevel.NONE?e=n.DiagLogLevel.NONE:e>n.DiagLogLevel.ALL&&(e=n.DiagLogLevel.ALL),t=t||{},{error:r("error",n.DiagLogLevel.ERROR),warn:r("warn",n.DiagLogLevel.WARN),info:r("info",n.DiagLogLevel.INFO),debug:r("debug",n.DiagLogLevel.DEBUG),verbose:r("verbose",n.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let n=r(200),i=r(521),s=r(130),a=i.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${a}`),l=n._globalThis;t.registerGlobal=function(e,t,r,n=!1){var s;let a=l[o]=null!=(s=l[o])?s:{version:i.VERSION};if(!n&&a[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(a.version!==i.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${a.version} for ${e} does not match previously registered API v${i.VERSION}`);return r.error(t.stack||t.message),!1}return a[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${i.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let n=null==(t=l[o])?void 0:t.version;if(n&&(0,s.isCompatible)(n))return null==(r=l[o])?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${i.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let n=r(521),i=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function s(e){let t=new Set([e]),r=new Set,n=e.match(i);if(!n)return()=>!1;let s={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=s.prerelease)return function(t){return t===e};function a(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let n=e.match(i);if(!n)return a(e);let o={major:+n[1],minor:+n[2],patch:+n[3],prerelease:n[4]};if(null!=o.prerelease||s.major!==o.major)return a(e);if(0===s.major)return s.minor===o.minor&&s.patch<=o.patch?(t.add(e),!0):a(e);return s.minor<=o.minor?(t.add(e),!0):a(e)}}t._makeCompatibilityCheck=s,t.isCompatible=s(n.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0,t.metrics=r(653).MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class n{}t.NoopMetric=n;class i extends n{add(e,t){}}t.NoopCounterMetric=i;class s extends n{add(e,t){}}t.NoopUpDownCounterMetric=s;class a extends n{record(e,t){}}t.NoopHistogramMetric=a;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class c extends o{}t.NoopObservableGaugeMetric=c;class u extends o{}t.NoopObservableUpDownCounterMetric=u,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new i,t.NOOP_HISTOGRAM_METRIC=new a,t.NOOP_UP_DOWN_COUNTER_METRIC=new s,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new c,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new u,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let n=r(102);class i{getMeter(e,t,r){return n.NOOP_METER}}t.NoopMeterProvider=i,t.NOOP_METER_PROVIDER=new i},200:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0,t.propagation=r(181).PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0,t.trace=r(997).TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let n=r(476);class i{constructor(e=n.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=i},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let n=r(491),i=r(607),s=r(403),a=r(139),o=n.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){var n;if(null==t?void 0:t.root)return new s.NonRecordingSpan;let l=r&&(0,i.getSpanContext)(r);return"object"==typeof(n=l)&&"string"==typeof n.spanId&&"string"==typeof n.traceId&&"number"==typeof n.traceFlags&&(0,a.isSpanContextValid)(l)?new s.NonRecordingSpan(l):new s.NonRecordingSpan}startActiveSpan(e,t,r,n){let s,a,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(s=t,l=r):(s=t,a=r,l=n);let c=null!=a?a:o.active(),u=this.startSpan(e,s,c),d=(0,i.setSpan)(c,u);return o.with(d,l,void 0,u)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let n=r(614);class i{getTracer(e,t,r){return new n.NoopTracer}}t.NoopTracerProvider=i},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let n=new(r(614)).NoopTracer;class i{constructor(e,t,r,n){this._provider=e,this.name=t,this.version=r,this.options=n}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,n){let i=this._getTracer();return Reflect.apply(i.startActiveSpan,i,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):n}}t.ProxyTracer=i},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let n=r(125),i=new(r(124)).NoopTracerProvider;class s{getTracer(e,t,r){var i;return null!=(i=this.getDelegateTracer(e,t,r))?i:new n.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!=(e=this._delegate)?e:i}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var n;return null==(n=this._delegate)?void 0:n.getTracer(e,t,r)}}t.ProxyTracerProvider=s},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let n=r(780),i=r(403),s=r(491),a=(0,n.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(a)||void 0}function l(e,t){return e.setValue(a,t)}t.getSpan=o,t.getActiveSpan=function(){return o(s.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(a)},t.setSpanContext=function(e,t){return l(e,new i.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null==(t=o(e))?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let n=r(564);class i{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),i=r.indexOf("=");if(-1!==i){let s=r.slice(0,i),a=r.slice(i+1,t.length);(0,n.validateKey)(s)&&(0,n.validateValue)(a)&&e.set(s,a)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new i;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=i},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",n=`[a-z]${r}{0,255}`,i=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,s=RegExp(`^(?:${n}|${i})$`),a=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return s.test(e)},t.validateValue=function(e){return a.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let n=r(325);t.createTraceState=function(e){return new n.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let n=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:n.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let n=r(476),i=r(403),s=/^([0-9a-f]{32})$/i,a=/^[0-9a-f]{16}$/i;function o(e){return s.test(e)&&e!==n.INVALID_TRACEID}function l(e){return a.test(e)&&e!==n.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new i.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},n={};function i(e){var r=n[e];if(void 0!==r)return r.exports;var s=n[e]={exports:{}},a=!0;try{t[e].call(s.exports,s,s.exports,i),a=!1}finally{a&&delete n[e]}return s.exports}i.ab="//";var s={};(()=>{Object.defineProperty(s,"__esModule",{value:!0}),s.trace=s.propagation=s.metrics=s.diag=s.context=s.INVALID_SPAN_CONTEXT=s.INVALID_TRACEID=s.INVALID_SPANID=s.isValidSpanId=s.isValidTraceId=s.isSpanContextValid=s.createTraceState=s.TraceFlags=s.SpanStatusCode=s.SpanKind=s.SamplingDecision=s.ProxyTracerProvider=s.ProxyTracer=s.defaultTextMapSetter=s.defaultTextMapGetter=s.ValueType=s.createNoopMeter=s.DiagLogLevel=s.DiagConsoleLogger=s.ROOT_CONTEXT=s.createContextKey=s.baggageEntryMetadataFromString=void 0;var e=i(369);Object.defineProperty(s,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=i(780);Object.defineProperty(s,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(s,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=i(972);Object.defineProperty(s,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var n=i(957);Object.defineProperty(s,"DiagLogLevel",{enumerable:!0,get:function(){return n.DiagLogLevel}});var a=i(102);Object.defineProperty(s,"createNoopMeter",{enumerable:!0,get:function(){return a.createNoopMeter}});var o=i(901);Object.defineProperty(s,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=i(194);Object.defineProperty(s,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(s,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var c=i(125);Object.defineProperty(s,"ProxyTracer",{enumerable:!0,get:function(){return c.ProxyTracer}});var u=i(846);Object.defineProperty(s,"ProxyTracerProvider",{enumerable:!0,get:function(){return u.ProxyTracerProvider}});var d=i(996);Object.defineProperty(s,"SamplingDecision",{enumerable:!0,get:function(){return d.SamplingDecision}});var h=i(357);Object.defineProperty(s,"SpanKind",{enumerable:!0,get:function(){return h.SpanKind}});var p=i(847);Object.defineProperty(s,"SpanStatusCode",{enumerable:!0,get:function(){return p.SpanStatusCode}});var f=i(475);Object.defineProperty(s,"TraceFlags",{enumerable:!0,get:function(){return f.TraceFlags}});var g=i(98);Object.defineProperty(s,"createTraceState",{enumerable:!0,get:function(){return g.createTraceState}});var m=i(139);Object.defineProperty(s,"isSpanContextValid",{enumerable:!0,get:function(){return m.isSpanContextValid}}),Object.defineProperty(s,"isValidTraceId",{enumerable:!0,get:function(){return m.isValidTraceId}}),Object.defineProperty(s,"isValidSpanId",{enumerable:!0,get:function(){return m.isValidSpanId}});var y=i(476);Object.defineProperty(s,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(s,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(s,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let b=i(67);Object.defineProperty(s,"context",{enumerable:!0,get:function(){return b.context}});let _=i(506);Object.defineProperty(s,"diag",{enumerable:!0,get:function(){return _.diag}});let v=i(886);Object.defineProperty(s,"metrics",{enumerable:!0,get:function(){return v.metrics}});let w=i(939);Object.defineProperty(s,"propagation",{enumerable:!0,get:function(){return w.propagation}});let k=i(845);Object.defineProperty(s,"trace",{enumerable:!0,get:function(){return k.trace}}),s.default={context:b.context,diag:_.diag,metrics:v.metrics,propagation:w.propagation,trace:k.trace}})(),e.exports=s})()}},e=>{var t=e(e.s=833);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_src/middleware"]=t}]);
//# sourceMappingURL=middleware.js.map