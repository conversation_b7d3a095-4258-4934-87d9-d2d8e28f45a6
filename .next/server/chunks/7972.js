"use strict";exports.id=7972,exports.ids=[7972],exports.modules={61855:(e,t,a)=>{a.d(t,{Q:()=>o});var r=a(43210),n=a(49605),l=a(54024),i=["axis"],o=(0,r.forwardRef)((e,t)=>r.createElement(l.P,{chartName:"AreaChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:i,tooltipPayloadSearcher:n.uN,categoricalChartProps:e,ref:t}))},77814:(e,t,a)=>{a.d(t,{Gk:()=>es,Vf:()=>eo});var r=a(43210),n=a(49384),l=a(81888),i=a(95530),o=a(98986),s=a(98845),c=a(20237),u=a(22989),p=a(64279),d=a(54186),y=a(81730),m=a(37625),h=a(46993),f=a(84648),v=a(85621),x=a(51426),b=a(57282),E=a(6548),g=(e,t,a,r)=>(0,v.Gx)(e,"xAxis",t,r),A=(e,t,a,r)=>(0,v.CR)(e,"xAxis",t,r),P=(e,t,a,r)=>(0,v.Gx)(e,"yAxis",a,r),O=(e,t,a,r)=>(0,v.CR)(e,"yAxis",a,r),I=(0,f.Mz)([x.fz,g,P,A,O],(e,t,a,r,n)=>(0,p._L)(e,"xAxis")?(0,p.Hj)(t,r,!1):(0,p.Hj)(a,n,!1)),M=(0,f.Mz)([v.ld,(e,t,a,r,n)=>n],(e,t)=>e.filter(e=>"area"===e.type).find(e=>e.id===t)),k=(0,f.Mz)([x.fz,g,P,A,O,(e,t,a,r,n)=>{var l,i,o=M(e,t,a,r,n);if(null!=o){var s=(0,x.fz)(e);if(null!=(i=(0,p._L)(s,"xAxis")?(0,v.TC)(e,"yAxis",a,r):(0,v.TC)(e,"xAxis",t,r))){var{stackId:c}=o,u=(0,E.x)(o);if(null!=c&&null!=u){var d=null==(l=i[c])?void 0:l.stackedData;return null==d?void 0:d.find(e=>e.key===u)}}}},b.HS,I,M],(e,t,a,r,n,l,i,o,s)=>{var c,{chartData:u,dataStartIndex:p,dataEndIndex:d}=i;if(null!=s&&("horizontal"===e||"vertical"===e)&&null!=t&&null!=a&&null!=r&&null!=n&&0!==r.length&&0!==n.length&&null!=o){var{data:y}=s;if(null!=(c=y&&y.length>0?y:null==u?void 0:u.slice(p,d+1)))return eo({layout:e,xAxis:t,yAxis:a,xAxisTicks:r,yAxisTicks:n,dataStartIndex:p,areaSettings:s,stackedData:l,displayedData:c,chartBaseValue:void 0,bandSize:o})}}),C=a(83409),j=a(21426),w=a(14956),W=a(43209),N=a(36304),D=a(73865),L=a(12128),S=a(27934),z=a(3785),T=a(75787),G=a(99857),R=a(23758),H=["id"],K=["activeDot","animationBegin","animationDuration","animationEasing","connectNulls","dot","fill","fillOpacity","hide","isAnimationActive","legendType","stroke","xAxisId","yAxisId"];function B(e,t){if(null==e)return{};var a,r,n=function(e,t){if(null==e)return{};var a={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;a[r]=e[r]}return a}(e,t);if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(r=0;r<l.length;r++)a=l[r],-1===t.indexOf(a)&&({}).propertyIsEnumerable.call(e,a)&&(n[a]=e[a])}return n}function V(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,r)}return a}function $(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{};t%2?V(Object(a),!0).forEach(function(t){var r,n,l;r=e,n=t,l=a[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var a=e[Symbol.toPrimitive];if(void 0!==a){var r=a.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in r?Object.defineProperty(r,n,{value:l,enumerable:!0,configurable:!0,writable:!0}):r[n]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(a)):V(Object(a)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(a,t))})}return e}function F(){return(F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var r in a)({}).hasOwnProperty.call(a,r)&&(e[r]=a[r])}return e}).apply(null,arguments)}function J(e,t){return e&&"none"!==e?e:t}var Q=e=>{var{dataKey:t,name:a,stroke:r,fill:n,legendType:l,hide:i}=e;return[{inactive:i,dataKey:t,type:l,color:J(r,n),value:(0,p.uM)(a,t),payload:e}]};function _(e){var{dataKey:t,data:a,stroke:r,strokeWidth:n,fill:l,name:i,hide:o,unit:s}=e;return{dataDefinedOnItem:a,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:l,dataKey:t,nameKey:void 0,name:(0,p.uM)(i,t),hide:o,type:e.tooltipType,color:J(r,l),unit:s}}}var X=(e,t)=>{var a;if(r.isValidElement(e))a=r.cloneElement(e,t);else if("function"==typeof e)a=e(t);else{var l=(0,n.$)("recharts-area-dot","boolean"!=typeof e?e.className:"");a=r.createElement(i.c,F({},t,{className:l}))}return a};function Z(e){var{clipPathId:t,points:a,props:n}=e,{needClip:l,dot:i,dataKey:s}=n;if(null==a||!i&&1!==a.length)return null;var c=(0,d.y$)(i),u=(0,G.u)(n),p=(0,d.J9)(i,!0),y=a.map((e,t)=>X(i,$($($({key:"dot-".concat(t),r:3},u),p),{},{index:t,cx:e.x,cy:e.y,dataKey:s,value:e.value,payload:e.payload,points:a}))),m={clipPath:l?"url(#clipPath-".concat(c?"":"dots-").concat(t,")"):void 0};return r.createElement(o.W,F({className:"recharts-area-dots"},m),y)}function q(e){var{points:t,baseLine:a,needClip:n,clipPathId:i,props:c,showLabels:u}=e,{layout:p,type:d,stroke:y,connectNulls:m,isRange:h}=c,{id:f}=c,v=B(c,H),x=(0,G.u)(v);return r.createElement(r.Fragment,null,(null==t?void 0:t.length)>1&&r.createElement(o.W,{clipPath:n?"url(#clipPath-".concat(i,")"):void 0},r.createElement(l.I,F({},x,{id:f,points:t,connectNulls:m,type:d,baseLine:a,layout:p,stroke:"none",className:"recharts-area-area"})),"none"!==y&&r.createElement(l.I,F({},x,{className:"recharts-area-curve",layout:p,type:d,connectNulls:m,fill:"none",points:t})),"none"!==y&&h&&r.createElement(l.I,F({},x,{className:"recharts-area-curve",layout:p,type:d,connectNulls:m,fill:"none",points:a}))),r.createElement(Z,{points:t,props:v,clipPathId:i}),u&&s.Z.renderCallByParent(v,t))}function U(e){var{alpha:t,baseLine:a,points:n,strokeWidth:l}=e,i=n[0].y,o=n[n.length-1].y;if(!(0,L.H)(i)||!(0,L.H)(o))return null;var s=t*Math.abs(i-o),c=Math.max(...n.map(e=>e.x||0));return((0,u.Et)(a)?c=Math.max(a,c):a&&Array.isArray(a)&&a.length&&(c=Math.max(...a.map(e=>e.x||0),c)),(0,u.Et)(c))?r.createElement("rect",{x:0,y:i<o?i:i-s,width:c+(l?parseInt("".concat(l),10):1),height:Math.floor(s)}):null}function Y(e){var{alpha:t,baseLine:a,points:n,strokeWidth:l}=e,i=n[0].x,o=n[n.length-1].x;if(!(0,L.H)(i)||!(0,L.H)(o))return null;var s=t*Math.abs(i-o),c=Math.max(...n.map(e=>e.y||0));return((0,u.Et)(a)?c=Math.max(a,c):a&&Array.isArray(a)&&a.length&&(c=Math.max(...a.map(e=>e.y||0),c)),(0,u.Et)(c))?r.createElement("rect",{x:i<o?i:i-s,y:0,width:s,height:Math.floor(c+(l?parseInt("".concat(l),10):1))}):null}function ee(e){var{alpha:t,layout:a,points:n,baseLine:l,strokeWidth:i}=e;return"vertical"===a?r.createElement(U,{alpha:t,points:n,baseLine:l,strokeWidth:i}):r.createElement(Y,{alpha:t,points:n,baseLine:l,strokeWidth:i})}function et(e){var{needClip:t,clipPathId:a,props:n,previousPointsRef:l,previousBaselineRef:i}=e,{points:s,baseLine:c,isAnimationActive:p,animationBegin:d,animationDuration:y,animationEasing:m,onAnimationStart:h,onAnimationEnd:f}=n,v=(0,N.n)(n,"recharts-area-"),[x,b]=(0,r.useState)(!0),E=(0,r.useCallback)(()=>{"function"==typeof f&&f(),b(!1)},[f]),g=(0,r.useCallback)(()=>{"function"==typeof h&&h(),b(!0)},[h]),A=l.current,P=i.current;return r.createElement(R.J,{begin:d,duration:y,isActive:p,easing:m,onAnimationEnd:E,onAnimationStart:g,key:v},e=>{if(A){var p,d=A.length/s.length,y=1===e?s:s.map((t,a)=>{var r=Math.floor(a*d);if(A[r]){var n=A[r];return $($({},t),{},{x:(0,u.GW)(n.x,t.x,e),y:(0,u.GW)(n.y,t.y,e)})}return t});return p=(0,u.Et)(c)?(0,u.GW)(P,c,e):(0,u.uy)(c)||(0,u.M8)(c)?(0,u.GW)(P,0,e):c.map((t,a)=>{var r=Math.floor(a*d);if(Array.isArray(P)&&P[r]){var n=P[r];return $($({},t),{},{x:(0,u.GW)(n.x,t.x,e),y:(0,u.GW)(n.y,t.y,e)})}return t}),e>0&&(l.current=y,i.current=p),r.createElement(q,{points:y,baseLine:p,needClip:t,clipPathId:a,props:n,showLabels:!x})}return e>0&&(l.current=s,i.current=c),r.createElement(o.W,null,r.createElement("defs",null,r.createElement("clipPath",{id:"animationClipPath-".concat(a)},r.createElement(ee,{alpha:e,points:s,baseLine:c,layout:n.layout,strokeWidth:n.strokeWidth}))),r.createElement(o.W,{clipPath:"url(#animationClipPath-".concat(a,")")},r.createElement(q,{points:s,baseLine:c,needClip:t,clipPathId:a,props:n,showLabels:!0})))})}function ea(e){var{needClip:t,clipPathId:a,props:n}=e,{points:l,baseLine:i,isAnimationActive:o}=n,s=(0,r.useRef)(null),c=(0,r.useRef)(),u=s.current,p=c.current;return o&&l&&l.length&&(u!==l||p!==i)?r.createElement(et,{needClip:t,clipPathId:a,props:n,previousPointsRef:s,previousBaselineRef:c}):r.createElement(q,{points:l,baseLine:i,needClip:t,clipPathId:a,props:n,showLabels:!0})}class er extends r.PureComponent{render(){var e,{hide:t,dot:a,points:l,className:i,top:s,left:c,needClip:u,xAxisId:p,yAxisId:m,width:f,height:v,id:x,baseLine:b}=this.props;if(t)return null;var E=(0,n.$)("recharts-area",i),{r:g=3,strokeWidth:A=2}=null!=(e=(0,d.J9)(a,!1))?e:{r:3,strokeWidth:2},P=(0,d.y$)(a),O=2*g+A;return r.createElement(r.Fragment,null,r.createElement(o.W,{className:E},u&&r.createElement("defs",null,r.createElement(h.Q,{clipPathId:x,xAxisId:p,yAxisId:m}),!P&&r.createElement("clipPath",{id:"clipPath-dots-".concat(x)},r.createElement("rect",{x:c-O/2,y:s-O/2,width:f+O,height:v+O}))),r.createElement(ea,{needClip:u,clipPathId:x,props:this.props})),r.createElement(y.W,{points:l,mainColor:J(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}),this.props.isRange&&Array.isArray(b)&&r.createElement(y.W,{points:b,mainColor:J(this.props.stroke,this.props.fill),itemDataKey:this.props.dataKey,activeDot:this.props.activeDot}))}}var en={activeDot:!0,animationBegin:0,animationDuration:1500,animationEasing:"ease",connectNulls:!1,dot:!1,fill:"#3182bd",fillOpacity:.6,hide:!1,isAnimationActive:!c.m.isSsr,legendType:"line",stroke:"#3182bd",xAxisId:0,yAxisId:0};function el(e){var t,a=(0,D.e)(e,en),{activeDot:n,animationBegin:l,animationDuration:i,animationEasing:o,connectNulls:s,dot:c,fill:u,fillOpacity:p,hide:d,isAnimationActive:y,legendType:m,stroke:f,xAxisId:v,yAxisId:b}=a,E=B(a,K),g=(0,x.WX)(),A=(0,j.fW)(),{needClip:P}=(0,h.l)(v,b),O=(0,C.r)(),{points:I,isRange:M,baseLine:w}=null!=(t=(0,W.G)(t=>k(t,v,b,O,e.id)))?t:{},N=(0,S.oM)();if("horizontal"!==g&&"vertical"!==g||null==N||"AreaChart"!==A&&"ComposedChart"!==A)return null;var{height:L,width:z,x:T,y:G}=N;return I&&I.length?r.createElement(er,F({},E,{activeDot:n,animationBegin:l,animationDuration:i,animationEasing:o,baseLine:w,connectNulls:s,dot:c,fill:u,fillOpacity:p,height:L,hide:d,layout:g,isAnimationActive:y,isRange:M,legendType:m,needClip:P,points:I,stroke:f,width:z,left:T,top:G,xAxisId:v,yAxisId:b})):null}var ei=(e,t,a,r,n)=>{var l=null!=a?a:t;if((0,u.Et)(l))return l;var i="horizontal"===e?n:r,o=i.scale.domain();if("number"===i.type){var s=Math.max(o[0],o[1]),c=Math.min(o[0],o[1]);return"dataMin"===l?c:"dataMax"===l||s<0?s:Math.max(Math.min(o[0],o[1]),0)}return"dataMin"===l?o[0]:"dataMax"===l?o[1]:o[0]};function eo(e){var t,{areaSettings:{connectNulls:a,baseValue:r,dataKey:n},stackedData:l,layout:i,chartBaseValue:o,xAxis:s,yAxis:c,displayedData:u,dataStartIndex:d,xAxisTicks:y,yAxisTicks:m,bandSize:h}=e,f=l&&l.length,v=ei(i,o,r,s,c),x="horizontal"===i,b=!1,E=u.map((e,t)=>{f?r=l[d+t]:Array.isArray(r=(0,p.kr)(e,n))?b=!0:r=[v,r];var r,i=null==r[1]||f&&!a&&null==(0,p.kr)(e,n);return x?{x:(0,p.nb)({axis:s,ticks:y,bandSize:h,entry:e,index:t}),y:i?null:c.scale(r[1]),value:r,payload:e}:{x:i?null:s.scale(r[1]),y:(0,p.nb)({axis:c,ticks:m,bandSize:h,entry:e,index:t}),value:r,payload:e}});return t=f||b?E.map(e=>{var t=Array.isArray(e.value)?e.value[0]:null;return x?{x:e.x,y:null!=t&&null!=e.y?c.scale(t):null,payload:e.payload}:{x:null!=t?s.scale(t):null,y:e.y,payload:e.payload}}):x?c.scale(v):s.scale(v),{points:E,baseLine:t,isRange:b}}function es(e){var t=(0,D.e)(e,en),a=(0,C.r)();return r.createElement(z.x,{id:t.id,type:"area"},e=>r.createElement(r.Fragment,null,r.createElement(w.A,{legendPayload:Q(t)}),r.createElement(m.r,{fn:_,args:t}),r.createElement(T.p,{type:"area",id:e,data:t.data,dataKey:t.dataKey,xAxisId:t.xAxisId,yAxisId:t.yAxisId,zAxisId:0,stackId:(0,p.$8)(t.stackId),hide:t.hide,barSize:void 0,baseValue:t.baseValue,isPanorama:a,connectNulls:t.connectNulls}),r.createElement(el,F({},t,{id:e}))))}es.displayName="Area"}};