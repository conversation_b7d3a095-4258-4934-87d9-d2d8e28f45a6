"use strict";exports.id=674,exports.ids=[674],exports.modules={11437:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},14952:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17458:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("file-chart-column-increasing",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 18v-2",key:"qcmpov"}],["path",{d:"M12 18v-4",key:"q1q25u"}],["path",{d:"M16 18v-6",key:"15y0np"}]])},17971:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},18179:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},19959:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},24413:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},34318:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},40083:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40228:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},56085:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},58559:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},64021:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70742:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},75024:(e,s,t)=>{t.d(s,{t:()=>b});var a=t(60687),i=t(44493),r=t(96834),d=t(29523),l=t(32584),n=t(58869),c=t(84027),m=t(97051),o=t(18179),x=t(96474),h=t(41312),p=t(40228);let u=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);var g=t(88233),y=t(48730),j=t(58559);let v={name:"Admin User",email:"<EMAIL>",role:"Administrator",joinDate:"January 2024",lastLogin:"2 hours ago",avatar:"",stats:{totalLogins:245,projectsManaged:12,usersManaged:1250,systemUptime:"99.9%"}},f=[{id:1,title:"System Update Available",message:"A new system update is ready to be installed",type:"info",time:"5 minutes ago",read:!1},{id:2,title:"New User Registration",message:"John Doe has registered for an account",type:"success",time:"1 hour ago",read:!1},{id:3,title:"Server Maintenance",message:"Scheduled maintenance completed successfully",type:"success",time:"3 hours ago",read:!0},{id:4,title:"Security Alert",message:"Multiple failed login attempts detected",type:"warning",time:"1 day ago",read:!0}],N=[{id:1,name:"Siift Analytics Platform",description:"Main analytics dashboard and reporting system",status:"active",members:8,progress:85,lastUpdated:"2 hours ago",priority:"high"},{id:2,name:"User Management System",description:"Complete user authentication and management",status:"active",members:5,progress:92,lastUpdated:"1 day ago",priority:"medium"},{id:3,name:"API Documentation",description:"Comprehensive API documentation and guides",status:"completed",members:3,progress:100,lastUpdated:"3 days ago",priority:"low"},{id:4,name:"Mobile App Integration",description:"Mobile application backend integration",status:"planning",members:0,progress:15,lastUpdated:"1 week ago",priority:"high"}],A=[{id:1,action:"User Login",user:"<EMAIL>",timestamp:"2 minutes ago",type:"user"},{id:2,action:"Project Updated",user:"<EMAIL>",details:"Siift Analytics Platform",timestamp:"15 minutes ago",type:"project"},{id:3,action:"New User Registration",user:"<EMAIL>",timestamp:"1 hour ago",type:"user"},{id:4,action:"System Backup",user:"system",timestamp:"2 hours ago",type:"system"},{id:5,action:"Settings Changed",user:"<EMAIL>",details:"Security settings updated",timestamp:"3 hours ago",type:"settings"}],k={general:{siteName:"Siift Admin Panel",timezone:"UTC-8 (Pacific)",language:"English",theme:"System"},security:{twoFactorAuth:!0,sessionTimeout:"30 minutes",passwordPolicy:"Strong",loginAttempts:5}};function b({activeTab:e}){return(0,a.jsx)("div",{className:"space-y-6",children:(()=>{switch(e){case"profile":return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-5 w-5"}),"Profile Information"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"flex items-start gap-6",children:[(0,a.jsxs)(l.eu,{className:"h-20 w-20",children:[(0,a.jsx)(l.BK,{src:v.avatar}),(0,a.jsx)(l.q5,{className:"text-lg",children:v.name.split(" ").map(e=>e[0]).join("")})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold",children:v.name}),(0,a.jsx)("p",{className:"text-muted-foreground",children:v.email}),(0,a.jsx)(r.E,{variant:"secondary",children:v.role}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Joined"}),(0,a.jsx)("p",{className:"font-medium",children:v.joinDate})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Last Login"}),(0,a.jsx)("p",{className:"font-medium",children:v.lastLogin})]})]})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Statistics"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-blue-600",children:v.stats.totalLogins}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total Logins"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-green-600",children:v.stats.projectsManaged}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Projects Managed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-purple-600",children:v.stats.usersManaged}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Users Managed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-2xl font-bold text-indigo-600",children:v.stats.systemUptime}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"System Uptime"})]})]})})]})]});case"settings":return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5"}),"General Settings"]})}),(0,a.jsx)(i.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Site Name"}),(0,a.jsx)("p",{className:"font-medium",children:k.general.siteName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Timezone"}),(0,a.jsx)("p",{className:"font-medium",children:k.general.timezone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Language"}),(0,a.jsx)("p",{className:"font-medium",children:k.general.language})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Theme"}),(0,a.jsx)("p",{className:"font-medium",children:k.general.theme})]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Security Settings"})}),(0,a.jsx)(i.Wu,{className:"space-y-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Two-Factor Authentication"}),(0,a.jsx)(r.E,{variant:k.security.twoFactorAuth?"default":"secondary",children:k.security.twoFactorAuth?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Session Timeout"}),(0,a.jsx)("p",{className:"font-medium",children:k.security.sessionTimeout})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Password Policy"}),(0,a.jsx)("p",{className:"font-medium",children:k.security.passwordPolicy})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Max Login Attempts"}),(0,a.jsx)("p",{className:"font-medium",children:k.security.loginAttempts})]})]})})]})]});case"notifications":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(m.A,{className:"h-5 w-5"}),"Recent Notifications"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:f.map(e=>(0,a.jsx)("div",{className:`p-4 rounded-lg border ${e.read?"bg-muted/50":"bg-background"}`,children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),!e.read&&(0,a.jsx)(r.E,{variant:"default",className:"h-2 w-2 p-0 rounded-full"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.time})]}),(0,a.jsx)(r.E,{variant:"success"===e.type?"default":"warning"===e.type?"destructive":"secondary",children:e.type})]})},e.id))})})]})});case"projects":return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(i.Zp,{className:"flex-1 mr-4",children:(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),"Projects Overview"]})})}),(0,a.jsxs)(d.$,{className:"flex items-center gap-2",children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"Create New Project"]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:N.map(e=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"font-semibold",children:e.name}),(0,a.jsx)(r.E,{variant:"active"===e.status?"default":"completed"===e.status?"secondary":"outline",children:e.status}),(0,a.jsxs)(r.E,{variant:"high"===e.priority?"destructive":"medium"===e.priority?"default":"secondary",children:[e.priority," priority"]})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),e.members," members"]}),(0,a.jsxs)("span",{className:"flex items-center gap-1",children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"Updated ",e.lastUpdated]})]})]}),(0,a.jsxs)("div",{className:"text-right space-y-2",children:[(0,a.jsxs)("p",{className:"text-2xl font-bold",children:[e.progress,"%"]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(d.$,{variant:"outline",size:"sm",children:(0,a.jsx)(u,{className:"h-4 w-4"})}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",children:(0,a.jsx)(g.A,{className:"h-4 w-4"})})]})]})]})})},e.id))})]});case"recent":return(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsxs)(i.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),"Recent Activity"]})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:A.map(e=>(0,a.jsxs)("div",{className:"flex items-center gap-4 p-3 rounded-lg border",children:[(0,a.jsx)("div",{className:`p-2 rounded-full ${"user"===e.type?"bg-blue-100 text-blue-600":"project"===e.type?"bg-green-100 text-green-600":"system"===e.type?"bg-purple-100 text-purple-600":"bg-indigo-100 text-indigo-600"}`,children:"user"===e.type?(0,a.jsx)(h.A,{className:"h-4 w-4"}):"project"===e.type?(0,a.jsx)(o.A,{className:"h-4 w-4"}):"system"===e.type?(0,a.jsx)(j.A,{className:"h-4 w-4"}):(0,a.jsx)(c.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"font-medium",children:e.action}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[e.user,e.details&&` - ${e.details}`]})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.timestamp})]},e.id))})})]})});default:return(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"Select a tab to view content"})})})}})()})}},78200:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},85778:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},88233:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96834:(e,s,t)=>{t.d(s,{E:()=>n});var a=t(60687);t(43210);var i=t(8730),r=t(24224),d=t(4780);let l=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,asChild:t=!1,...r}){let n=t?i.DX:"span";return(0,a.jsx)(n,{"data-slot":"badge",className:(0,d.cn)(l({variant:s}),e),...r})}},97051:(e,s,t)=>{t.d(s,{A:()=>a});let a=(0,t(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};