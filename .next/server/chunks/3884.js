"use strict";exports.id=3884,exports.ids=[3884],exports.modules={6102:(e,t,r)=>{r.d(t,{Z:()=>s});var n=r(43210);function s(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},13964:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},14952:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},40211:(e,t,r)=>{r.d(t,{C1:()=>w,bL:()=>E});var n=r(43210),s=r(98599),o=r(11273),i=r(70569),l=r(65551),u=r(6102),a=r(18853),c=r(46059),d=r(14163),f=r(60687),p="Checkbox",[h,m]=(0,o.A)(p),[x,v]=h(p);function g(e){let{__scopeCheckbox:t,checked:r,children:s,defaultChecked:o,disabled:i,form:u,name:a,onCheckedChange:c,required:d,value:h="on",internal_do_not_use_render:m}=e,[v,g]=(0,l.i)({prop:r,defaultProp:o??!1,onChange:c,caller:p}),[k,y]=n.useState(null),[E,b]=n.useState(null),w=n.useRef(!1),C=!k||!!u||!!k.closest("form"),R={checked:v,disabled:i,setChecked:g,control:k,setControl:y,name:a,form:u,value:h,hasConsumerStoppedPropagationRef:w,required:d,defaultChecked:!j(o)&&o,isFormControl:C,bubbleInput:E,setBubbleInput:b};return(0,f.jsx)(x,{scope:t,...R,children:"function"==typeof m?m(R):s})}var k="CheckboxTrigger",y=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...o},l)=>{let{control:u,value:a,disabled:c,checked:p,required:h,setControl:m,setChecked:x,hasConsumerStoppedPropagationRef:g,isFormControl:y,bubbleInput:E}=v(k,e),b=(0,s.s)(l,m),w=n.useRef(p);return n.useEffect(()=>{let e=u?.form;if(e){let t=()=>x(w.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[u,x]),(0,f.jsx)(d.sG.button,{type:"button",role:"checkbox","aria-checked":j(p)?"mixed":p,"aria-required":h,"data-state":P(p),"data-disabled":c?"":void 0,disabled:c,value:a,...o,ref:b,onKeyDown:(0,i.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(r,e=>{x(e=>!!j(e)||!e),E&&y&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});y.displayName=k;var E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:s,defaultChecked:o,required:i,disabled:l,value:u,onCheckedChange:a,form:c,...d}=e;return(0,f.jsx)(g,{__scopeCheckbox:r,checked:s,defaultChecked:o,disabled:l,required:i,onCheckedChange:a,name:n,form:c,value:u,internal_do_not_use_render:({isFormControl:e})=>(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(y,{...d,ref:t,__scopeCheckbox:r}),e&&(0,f.jsx)(R,{__scopeCheckbox:r})]})})});E.displayName=p;var b="CheckboxIndicator",w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...s}=e,o=v(b,r);return(0,f.jsx)(c.C,{present:n||j(o.checked)||!0===o.checked,children:(0,f.jsx)(d.sG.span,{"data-state":P(o.checked),"data-disabled":o.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});w.displayName=b;var C="CheckboxBubbleInput",R=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:o,hasConsumerStoppedPropagationRef:i,checked:l,defaultChecked:c,required:p,disabled:h,name:m,value:x,form:g,bubbleInput:k,setBubbleInput:y}=v(C,e),E=(0,s.s)(r,y),b=(0,u.Z)(l),w=(0,a.X)(o);n.useEffect(()=>{if(!k)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(b!==l&&e){let r=new Event("click",{bubbles:t});k.indeterminate=j(l),e.call(k,!j(l)&&l),k.dispatchEvent(r)}},[k,b,l,i]);let R=n.useRef(!j(l)&&l);return(0,f.jsx)(d.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??R.current,required:p,disabled:h,name:m,value:x,form:g,...t,tabIndex:-1,ref:E,style:{...t.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function j(e){return"indeterminate"===e}function P(e){return j(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=C},47033:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},88920:(e,t,r)=>{r.d(t,{N:()=>g});var n=r(60687),s=r(43210),o=r(12157),i=r(72789),l=r(15124),u=r(21279),a=r(18171),c=r(32582);class d extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,a.s)(e)&&e.offsetWidth||0,n=this.props.sizeRef.current;n.height=t.offsetHeight||0,n.width=t.offsetWidth||0,n.top=t.offsetTop,n.left=t.offsetLeft,n.right=r-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function f({children:e,isPresent:t,anchorX:r,root:o}){let i=(0,s.useId)(),l=(0,s.useRef)(null),u=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:a}=(0,s.useContext)(c.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:n,top:s,left:c,right:d}=u.current;if(t||!l.current||!e||!n)return;let f="left"===r?`left: ${c}`:`right: ${d}`;l.current.dataset.motionPopId=i;let p=document.createElement("style");a&&(p.nonce=a);let h=o??document.head;return h.appendChild(p),p.sheet&&p.sheet.insertRule(`
          [data-motion-pop-id="${i}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            ${f}px !important;
            top: ${s}px !important;
          }
        `),()=>{h.contains(p)&&h.removeChild(p)}},[t]),(0,n.jsx)(d,{isPresent:t,childRef:l,sizeRef:u,children:s.cloneElement(e,{ref:l})})}let p=({children:e,initial:t,isPresent:r,onExitComplete:o,custom:l,presenceAffectsLayout:a,mode:c,anchorX:d,root:p})=>{let m=(0,i.M)(h),x=(0,s.useId)(),v=!0,g=(0,s.useMemo)(()=>(v=!1,{id:x,initial:t,isPresent:r,custom:l,onExitComplete:e=>{for(let t of(m.set(e,!0),m.values()))if(!t)return;o&&o()},register:e=>(m.set(e,!1),()=>m.delete(e))}),[r,m,o]);return a&&v&&(g={...g}),(0,s.useMemo)(()=>{m.forEach((e,t)=>m.set(t,!1))},[r]),s.useEffect(()=>{r||m.size||!o||o()},[r]),"popLayout"===c&&(e=(0,n.jsx)(f,{isPresent:r,anchorX:d,root:p,children:e})),(0,n.jsx)(u.t.Provider,{value:g,children:e})};function h(){return new Map}var m=r(86044);let x=e=>e.key||"";function v(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}let g=({children:e,custom:t,initial:r=!0,onExitComplete:u,presenceAffectsLayout:a=!0,mode:c="sync",propagate:d=!1,anchorX:f="left",root:h})=>{let[g,k]=(0,m.xQ)(d),y=(0,s.useMemo)(()=>v(e),[e]),E=d&&!g?[]:y.map(x),b=(0,s.useRef)(!0),w=(0,s.useRef)(y),C=(0,i.M)(()=>new Map),[R,j]=(0,s.useState)(y),[P,M]=(0,s.useState)(y);(0,l.E)(()=>{b.current=!1,w.current=y;for(let e=0;e<P.length;e++){let t=x(P[e]);E.includes(t)?C.delete(t):!0!==C.get(t)&&C.set(t,!1)}},[P,E.length,E.join("-")]);let A=[];if(y!==R){let e=[...y];for(let t=0;t<P.length;t++){let r=P[t],n=x(r);E.includes(n)||(e.splice(t,0,r),A.push(r))}return"wait"===c&&A.length&&(e=A),M(v(e)),j(y),null}let{forceRender:I}=(0,s.useContext)(o.L);return(0,n.jsx)(n.Fragment,{children:P.map(e=>{let s=x(e),o=(!d||!!g)&&(y===P||E.includes(s));return(0,n.jsx)(p,{isPresent:o,initial:(!b.current||!!r)&&void 0,custom:t,presenceAffectsLayout:a,mode:c,root:h,onExitComplete:o?void 0:()=>{if(!C.has(s))return;C.set(s,!0);let e=!0;C.forEach(t=>{t||(e=!1)}),e&&(I?.(),M(w.current),d&&k?.(),u&&u())},anchorX:f,children:e},s)})})}}};