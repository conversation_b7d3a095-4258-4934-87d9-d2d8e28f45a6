exports.id=1838,exports.ids=[1838],exports.modules={471:(e,t,r)=>{Promise.resolve().then(r.bind(r,80793)),Promise.resolve().then(r.bind(r,85850)),Promise.resolve().then(r.bind(r,93821)),Promise.resolve().then(r.bind(r,58758)),Promise.resolve().then(r.bind(r,41330)),Promise.resolve().then(r.bind(r,21313)),Promise.resolve().then(r.t.bind(r,79167,23)),Promise.resolve().then(r.bind(r,22296)),Promise.resolve().then(r.bind(r,15255)),Promise.resolve().then(r.bind(r,24122)),Promise.resolve().then(r.bind(r,95881)),Promise.resolve().then(r.bind(r,96871)),Promise.resolve().then(r.bind(r,64616)),Promise.resolve().then(r.bind(r,87730))},4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(49384),i=r(82348);function a(...e){return(0,i.QP)((0,s.$)(e))}},14329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687);r(43210);var i=r(85814),a=r.n(i),o=r(43649),n=r(78122),c=r(32192),l=r(23166),d=r(70042);function m({error:e,reset:t}){return(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.02]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='27' cy='7' r='1'/%3E%3Ccircle cx='47' cy='7' r='1'/%3E%3Ccircle cx='7' cy='27' r='1'/%3E%3Ccircle cx='27' cy='27' r='1'/%3E%3Ccircle cx='47' cy='27' r='1'/%3E%3Ccircle cx='7' cy='47' r='1'/%3E%3Ccircle cx='27' cy='47' r='1'/%3E%3Ccircle cx='47' cy='47' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-background via-transparent to-background"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-transparent to-background"}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.025] mix-blend-overlay",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)("div",{className:"relative z-10 flex min-h-screen items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"text-center space-y-8 max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.g,{size:64,animated:!1}),(0,s.jsx)("span",{className:"text-3xl font-bold text-foreground",children:"Siift"})]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(o.A,{className:"h-16 w-16 text-destructive"})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Something went wrong"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"An unexpected error occurred"})]}),(0,s.jsxs)("div",{className:"flex flex-col gap-3",children:[(0,s.jsxs)(d.V,{onClick:t,size:"lg",className:"px-6",children:[(0,s.jsx)(n.A,{className:"h-5 w-5 mr-2"}),"Try Again"]}),(0,s.jsx)(a(),{href:"/",children:(0,s.jsxs)(d.V,{variant:"outline",size:"lg",className:"px-6",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 mr-2"}),"Back to Main"]})})]})]})})]})}},15255:(e,t,r)=>{"use strict";r.d(t,{ClerkSessionProvider:()=>l});var s=r(60687),i=r(14792),a=r(41330);r(43210);var o=r(78941),n=r(16189),c=r(38553);function l({children:e}){let{user:t,isLoaded:r}=(0,i.Jd)(),{isSignedIn:l,getToken:d}=(0,a.d)(),{actions:m,isAuthenticated:u}=(0,o.B)(),{identifyUser:p,setUserProperties:g}=(0,c.s)();return(0,n.useRouter)(),(0,s.jsx)(s.Fragment,{children:e})}},18579:(e,t,r)=>{"use strict";r.d(t,{YQ:()=>i});let s={title:"Siift - Modern Project Management",description:"A modern project management platform built with Next.js and NestJS. Streamline your workflow with powerful tools and intuitive design.",keywords:["project management","productivity","collaboration","workflow","task management"],image:"/images/og-image.png",url:"https://siift.app",type:"website",siteName:"Siift",locale:"en_US",noIndex:!1};function i(e={}){let{title:t=s.title,description:r=s.description,keywords:a=s.keywords,image:o=s.image,url:n=s.url,type:c=s.type,publishedTime:l,modifiedTime:d,author:m,siteName:u=s.siteName,locale:p=s.locale,noIndex:g=s.noIndex,canonical:h}=e,f=t===s.title?t:`${t} | ${s.siteName}`,y=n.startsWith("http")?n:`${s.url}${n}`,x=o.startsWith("http")?o:`${s.url}${o}`;return{title:f,description:r,keywords:a.join(", "),authors:m?[{name:m}]:void 0,creator:u,publisher:u,robots:g?"noindex,nofollow":"index,follow",alternates:{canonical:h||y},openGraph:{title:f,description:r,url:y,siteName:u,images:[{url:x,width:1200,height:630,alt:t}],locale:p,type:c,publishedTime:l,modifiedTime:d},twitter:{card:"summary_large_image",title:f,description:r,images:[x],creator:"@siiftapp"},other:{"theme-color":"#10b981","msapplication-TileColor":"#10b981","apple-mobile-web-app-capable":"yes","apple-mobile-web-app-status-bar-style":"default","format-detection":"telephone=no"}}}},19808:(e,t,r)=>{"use strict";r.d(t,{BackgroundProvider:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call BackgroundProvider() from the server but BackgroundProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx","BackgroundProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useBackground() from the server but useBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx","useBackground")},22296:(e,t,r)=>{"use strict";r.d(t,{PHProvider:()=>l});var s=r(60687),i=r(41867),a=r(2931),o=r(43210),n=r(16189);function c(){return(0,n.usePathname)(),(0,n.useSearchParams)(),null}function l({children:e}){return(0,s.jsxs)(a.so,{client:i.Ay,children:[(0,s.jsx)(o.Suspense,{fallback:null,children:(0,s.jsx)(c,{})}),e]})}},22807:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687),i=r(23166);function a(){return(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.02]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='27' cy='7' r='1'/%3E%3Ccircle cx='47' cy='7' r='1'/%3E%3Ccircle cx='7' cy='27' r='1'/%3E%3Ccircle cx='27' cy='27' r='1'/%3E%3Ccircle cx='47' cy='27' r='1'/%3E%3Ccircle cx='7' cy='47' r='1'/%3E%3Ccircle cx='27' cy='47' r='1'/%3E%3Ccircle cx='47' cy='47' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-background via-transparent to-background"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-transparent to-background"}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.025] mix-blend-overlay",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)("div",{className:"relative z-10 flex min-h-screen items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"text-center space-y-6",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)("div",{className:"flex items-center gap-3",children:(0,s.jsx)(i.g,{size:64,animated:!1,showText:!0,textSize:48})})}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground animate-pulse",children:"Loading..."})]})})]})}},22888:(e,t,r)=>{"use strict";r.d(t,{QueryProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx","QueryProvider")},23166:(e,t,r)=>{"use strict";r.d(t,{g:()=>c});var s=r(60687),i=r(76180),a=r.n(i);r(43210);var o=r(85814),n=r.n(o);let c=({size:e=48,textSize:t=24,className:r="",animated:i=!1,showText:o=!1,href:c,animationSpeed:l=5})=>{let d=e/48,m=32*d,u=20*d,p=+d,g=1.25*d,h=(0,s.jsxs)("div",{style:{position:"relative",width:e,height:e,display:"inline-block",animation:i?`logoFloat ${3/l}s ease-in-out infinite`:"none"},children:[(0,s.jsx)("div",{style:{position:"absolute",width:e,height:e,borderRadius:"50%",background:"#b4fd98",border:`${p}px solid #73ed47`,left:0,top:0,zIndex:1,transform:"rotate(70deg)",animation:i?`logoRotate ${8/l}s linear infinite`:"none"},children:(0,s.jsx)("div",{style:{position:"absolute",width:m,height:m,borderRadius:"50%",background:"#0A4000",border:`${p}px solid #73ed47`,left:e-m-p-g,top:p+g,zIndex:2,transform:"rotate(280deg)",animation:i?`logoPulse ${2/l}s ease-in-out infinite`:"none"},children:(0,s.jsx)("div",{style:{position:"absolute",width:u,height:u,borderRadius:"50%",background:"#fff",border:`${p}px solid #73ed47`,left:m-u-p-g,top:p+g,zIndex:3,animation:i?`logoGlow ${4/l}s ease-in-out infinite`:"none"}})})}),i&&(0,s.jsx)(a(),{id:"5428b1dfccf92ce4",children:"@-webkit-keyframes logoRotate{0%{-webkit-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);transform:rotate(430deg)}}@-moz-keyframes logoRotate{0%{-moz-transform:rotate(70deg);transform:rotate(70deg)}100%{-moz-transform:rotate(430deg);transform:rotate(430deg)}}@-o-keyframes logoRotate{0%{-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-o-transform:rotate(430deg);transform:rotate(430deg)}}@keyframes logoRotate{0%{-webkit-transform:rotate(70deg);-moz-transform:rotate(70deg);-o-transform:rotate(70deg);transform:rotate(70deg)}100%{-webkit-transform:rotate(430deg);-moz-transform:rotate(430deg);-o-transform:rotate(430deg);transform:rotate(430deg)}}"})]}),f=(0,s.jsxs)("div",{className:`flex items-center gap-3 ${r}`,children:[h,(0,s.jsx)("span",{className:"font-bold text-foreground",style:{fontSize:`${t}px`,position:"relative",animation:i?`textGlow ${3/l}s ease-in-out infinite`:"none"},children:"siift.ai"})]}),y=o?f:h;return c?(0,s.jsx)(n(),{href:c,className:"hover:opacity-80 transition-opacity",children:y}):y}},23440:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call SessionProvider() from the server but SessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx","SessionProvider")},24122:(e,t,r)=>{"use strict";r.d(t,{QueryProvider:()=>o});var s=r(60687),i=r(8693),a=r(50582);function o({children:e}){return(0,s.jsxs)(i.Ht,{client:a.qQ,children:[e,!1]})}},26785:(e,t,r)=>{"use strict";r.d(t,{M:()=>o,Y:()=>a});var s=r(37413),i=r(36162);function a({data:e}){return(0,s.jsx)(i.default,{id:"structured-data",type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(e)}})}let o={organization:e=>({"@context":"https://schema.org","@type":"Organization",name:e.name,url:e.url,logo:{"@type":"ImageObject",url:e.logo},description:e.description,sameAs:e.socialMedia||[],contactPoint:e.contactPoint?{"@type":"ContactPoint",telephone:e.contactPoint.telephone,contactType:e.contactPoint.contactType,email:e.contactPoint.email}:void 0}),website:e=>({"@context":"https://schema.org","@type":"WebSite",name:e.name,url:e.url,description:e.description,potentialAction:e.searchUrl?{"@type":"SearchAction",target:e.searchUrl,"query-input":"required name=search_term_string"}:void 0}),softwareApplication:e=>({"@context":"https://schema.org","@type":"SoftwareApplication",name:e.name,description:e.description,url:e.url,applicationCategory:e.category,operatingSystem:e.operatingSystem||"Web Browser",offers:e.price?{"@type":"Offer",price:e.price,priceCurrency:e.priceCurrency||"USD"}:void 0}),article:e=>({"@context":"https://schema.org","@type":"Article",headline:e.title,description:e.description,author:{"@type":"Person",name:e.author},datePublished:e.publishedTime,dateModified:e.modifiedTime||e.publishedTime,image:{"@type":"ImageObject",url:e.image},url:e.url,publisher:{"@type":"Organization",name:e.publisher.name,logo:{"@type":"ImageObject",url:e.publisher.logo}}}),blog:e=>({"@context":"https://schema.org","@type":"Blog",name:e.name,description:e.description,url:e.url,blogPost:e.posts.map(e=>({"@type":"BlogPosting",headline:e.title,description:e.description,url:e.url,datePublished:e.publishedTime,author:{"@type":"Person",name:e.author},image:{"@type":"ImageObject",url:e.image}}))}),faq:e=>({"@context":"https://schema.org","@type":"FAQPage",mainEntity:e.map(e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}}))}),breadcrumb:e=>({"@context":"https://schema.org","@type":"BreadcrumbList",itemListElement:e.map((e,t)=>({"@type":"ListItem",position:t+1,name:e.name,item:e.url}))}),product:e=>({"@context":"https://schema.org","@type":"Product",name:e.name,description:e.description,image:e.image,brand:{"@type":"Brand",name:e.brand},offers:{"@type":"Offer",price:e.price,priceCurrency:e.priceCurrency,availability:`https://schema.org/${e.availability}`},aggregateRating:e.rating?{"@type":"AggregateRating",ratingValue:e.rating.value,reviewCount:e.rating.count}:void 0})}},28421:(e,t,r)=>{"use strict";r.d(t,{ClerkSessionProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ClerkSessionProvider() from the server but ClerkSessionProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx","ClerkSessionProvider")},35852:(e,t,r)=>{Promise.resolve().then(r.bind(r,22807))},36659:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f294d927378fcfaafe4b72d3c48c966515621b8b4":()=>s.y});var s=r(38301)},38553:(e,t,r)=>{"use strict";r.d(t,{s:()=>a});var s=r(43210),i=r(2931);function a(){let e=(0,i.sf)(),t=(0,s.useCallback)((t,r)=>{e?.capture("$pageview",{$current_url:t,page_title:r})},[e]),r=(0,s.useCallback)((t,r)=>{e?.capture("button_clicked",{element_name:t,location:r})},[e]),a=(0,s.useCallback)((t,r=!0)=>{e?.capture("form_submitted",{form_name:t,success:r})},[e]),o=(0,s.useCallback)((t,r)=>{e?.capture("search_performed",{search_term:t,results_count:r})},[e]),n=(0,s.useCallback)(t=>{e?.capture("user_signed_up",{method:t}),e?.identify()},[e]),c=(0,s.useCallback)(t=>{e?.capture("user_logged_in",{method:t})},[e]),l=(0,s.useCallback)(t=>{e?.capture("project_created",{project_type:t})},[e]),d=(0,s.useCallback)((t,r)=>{e?.capture("feature_used",{feature_name:t,context:r})},[e]),m=(0,s.useCallback)((t,r,s=!1)=>{e?.capture("error_occurred",{error_message:t,context:r,fatal:s})},[e]),u=(0,s.useCallback)((t,r,s)=>{e?.capture("timing_measured",{timing_name:t,timing_value:r,category:s||"performance"})},[e]),p=(0,s.useCallback)((t,r)=>{e?.capture(t,r)},[e]);return{trackPageView:t,trackClick:r,trackFormSubmit:a,trackSearch:o,trackSignUp:n,trackLogin:c,trackProjectCreated:l,trackFeatureUsed:d,trackError:m,trackTiming:u,trackCustomEvent:p,identifyUser:(0,s.useCallback)((t,r)=>{e?.identify(t,r)},[e]),setUserProperties:(0,s.useCallback)(t=>{e?.setPersonProperties(t)},[e])}}},39446:(e,t,r)=>{Promise.resolve().then(r.bind(r,14329))},40223:(e,t,r)=>{Promise.resolve().then(r.bind(r,63441)),Promise.resolve().then(r.bind(r,62808)),Promise.resolve().then(r.bind(r,7791)),Promise.resolve().then(r.bind(r,12918)),Promise.resolve().then(r.bind(r,13920)),Promise.resolve().then(r.bind(r,62278)),Promise.resolve().then(r.t.bind(r,47429,23)),Promise.resolve().then(r.bind(r,80554)),Promise.resolve().then(r.bind(r,28421)),Promise.resolve().then(r.bind(r,22888)),Promise.resolve().then(r.bind(r,23440)),Promise.resolve().then(r.bind(r,83701)),Promise.resolve().then(r.bind(r,48482)),Promise.resolve().then(r.bind(r,19808))},48482:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx","Toaster")},49004:(e,t,r)=>{Promise.resolve().then(r.bind(r,67393))},50582:(e,t,r)=>{"use strict";r.d(t,{WG:()=>a,lH:()=>i,qQ:()=>s});let s=new(r(39091)).E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>!(t?.status>=400&&t?.status<500)&&e<3,refetchOnWindowFocus:!1,refetchOnReconnect:!0,refetchOnMount:!0},mutations:{retry:1,onError:e=>{console.error("Mutation error:",e)}}}}),i={user:e=>["user",e],userProfile:e=>["user","profile",e],projects:()=>["projects"],project:e=>["projects",e],projectTasks:e=>["projects",e,"tasks"],admin:{analytics:()=>["admin","analytics"],summary:()=>["admin","analytics","summary"],users:e=>["admin","users",e],feedback:e=>["admin","feedback",e]},businessSections:e=>["business-sections",e]},a={user:e=>{s.invalidateQueries({queryKey:i.user(e)}),s.invalidateQueries({queryKey:i.userProfile(e)})},projects:()=>{s.invalidateQueries({queryKey:i.projects()})},project:e=>{s.invalidateQueries({queryKey:i.project(e)}),s.invalidateQueries({queryKey:i.projectTasks(e)})},admin:()=>{s.invalidateQueries({queryKey:i.admin.analytics()}),s.invalidateQueries({queryKey:i.admin.summary()}),s.invalidateQueries({queryKey:i.admin.users()}),s.invalidateQueries({queryKey:i.admin.feedback()})}}},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx","default")},54431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx","default")},57347:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(60687),i=r(85814),a=r.n(i),o=r(32192),n=r(23166),c=r(70042);function l(){return(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.02]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Ccircle cx='7' cy='7' r='1'/%3E%3Ccircle cx='27' cy='7' r='1'/%3E%3Ccircle cx='47' cy='7' r='1'/%3E%3Ccircle cx='7' cy='27' r='1'/%3E%3Ccircle cx='27' cy='27' r='1'/%3E%3Ccircle cx='47' cy='27' r='1'/%3E%3Ccircle cx='7' cy='47' r='1'/%3E%3Ccircle cx='27' cy='47' r='1'/%3E%3Ccircle cx='47' cy='47' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")"}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-background via-transparent to-background"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-background via-transparent to-background"}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.015] dark:opacity-[0.025] mix-blend-overlay",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.9' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)("div",{className:"relative z-10 flex min-h-screen items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"text-center space-y-8",children:[(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(n.g,{size:64,animated:!1}),(0,s.jsx)("span",{className:"text-3xl font-bold text-foreground",children:"Siift"})]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h1",{className:"text-6xl font-bold text-foreground",children:"404"}),(0,s.jsx)("p",{className:"text-xl text-muted-foreground",children:"Page Not Found"})]}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(a(),{href:"/",children:(0,s.jsxs)(c.V,{size:"lg",className:"px-6",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Back to Main"]})})})]})})]})}},61135:()=>{},61570:(e,t,r)=>{"use strict";r.d(t,{N:()=>m});let s={codeLength:6,codeExpirationMinutes:15,maxAttempts:3,rateLimitMinutes:1,maxEmailsPerHour:10};class i{static{this.codes=new Map}static{this.messages=[]}static{this.rateLimits=new Map}static savecode(e,t){this.codes.set(e,t)}static getCode(e){return this.codes.get(e)}static deleteCode(e){this.codes.delete(e)}static saveMessage(e){this.messages.push(e),this.messages.length>100&&this.messages.shift()}static getMessages(e){return e?this.messages.filter(t=>t.to===e):[...this.messages]}static setRateLimit(e,t){this.rateLimits.set(e,t)}static getRateLimit(e){return this.rateLimits.get(e)}static clearExpiredCodes(){let e=Date.now();for(let[t,r]of this.codes.entries())r.expiresAt<e&&this.codes.delete(t)}static clearExpiredRateLimits(){let e=Date.now();for(let[t,r]of this.rateLimits.entries())r.resetAt<e&&this.rateLimits.delete(t)}static getAllCodes(){return this.codes}static clearMessages(){this.messages.splice(0)}static clearRateLimits(){this.rateLimits.clear()}}let a={"email-verification":{subject:"Verify your email address",body:`
      <h2>Welcome to Siift!</h2>
      <p>Please verify your email address by entering this code:</p>
      <div style="font-size: 24px; font-weight: bold; color: #2563eb; padding: 20px; background: #f3f4f6; border-radius: 8px; text-align: center; margin: 20px 0;">
        {{CODE}}
      </div>
      <p>This code will expire in 15 minutes.</p>
      <p>If you didn't create an account, please ignore this email.</p>
    `,type:"verification"},"password-reset":{subject:"Reset your password",body:`
      <h2>Password Reset Request</h2>
      <p>You requested to reset your password. Use this code to continue:</p>
      <div style="font-size: 24px; font-weight: bold; color: #dc2626; padding: 20px; background: #fef2f2; border-radius: 8px; text-align: center; margin: 20px 0;">
        {{CODE}}
      </div>
      <p>This code will expire in 15 minutes.</p>
      <p>If you didn't request this, please ignore this email.</p>
    `,type:"password-reset"},welcome:{subject:"Welcome to Siift!",body:`
      <h2>Welcome to Siift!</h2>
      <p>Your email has been verified successfully.</p>
      <p>You can now access all features of your account.</p>
      <p>Thank you for joining us!</p>
    `,type:"welcome"}};class o{static{this.config=s}static generateCode(){let e=this.config.codeLength,t="";for(let r=0;r<e;r++)t+=Math.floor(10*Math.random()).toString();return t}static createCodeKey(e,t){return`${e}:${t}`}static checkRateLimit(e){i.clearExpiredRateLimits();let t=i.getRateLimit(e),r=Date.now();if(t&&t.resetAt>r){if(t.count>=this.config.maxEmailsPerHour){let e=Error("Rate limit exceeded. Please try again later.");throw e.code="RATE_LIMITED",e.details={resetAt:t.resetAt},e}i.setRateLimit(e,{...t,count:t.count+1})}else i.setRateLimit(e,{email:e,count:1,resetAt:r+36e5})}static async sendEmailVerification(e){await new Promise(e=>setTimeout(e,800));try{this.checkRateLimit(e.email);let t=this.generateCode(),r=Date.now(),s=r+60*this.config.codeExpirationMinutes*1e3,o={code:t,email:e.email,type:"email-verification",expiresAt:s,attempts:0,maxAttempts:this.config.maxAttempts,createdAt:r},n=this.createCodeKey(e.email,"email-verification");i.savecode(n,o);let c=a["email-verification"],l={id:`msg_${r}_${Math.random().toString(36).substr(2,9)}`,to:e.email,subject:c.subject,body:c.body.replace("{{CODE}}",t),type:c.type,sentAt:r,verificationCode:t};return i.saveMessage(l),i.clearExpiredCodes(),{success:!0,messageId:l.id,message:"Verification email sent successfully"}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send verification email")}}static async sendPasswordReset(e){await new Promise(e=>setTimeout(e,800));try{this.checkRateLimit(e.email);let t=this.generateCode(),r=Date.now(),s=r+60*this.config.codeExpirationMinutes*1e3,o={code:t,email:e.email,type:"password-reset",expiresAt:s,attempts:0,maxAttempts:this.config.maxAttempts,createdAt:r},n=this.createCodeKey(e.email,"password-reset");i.savecode(n,o);let c=a["password-reset"],l={id:`msg_${r}_${Math.random().toString(36).substr(2,9)}`,to:e.email,subject:c.subject,body:c.body.replace("{{CODE}}",t),type:c.type,sentAt:r,verificationCode:t};return i.saveMessage(l),i.clearExpiredCodes(),{success:!0,messageId:l.id,message:"Password reset email sent successfully"}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send password reset email")}}static async verifyCode(e){await new Promise(e=>setTimeout(e,500)),i.clearExpiredCodes();let t=this.createCodeKey(e.email,e.type),r=i.getCode(t);if(!r){let e=Error("Verification code not found or expired");throw e.code="CODE_EXPIRED",e}if(r.expiresAt<Date.now()){i.deleteCode(t);let e=Error("Verification code has expired");throw e.code="CODE_EXPIRED",e}if(r.attempts>=r.maxAttempts){i.deleteCode(t);let e=Error("Maximum verification attempts exceeded");throw e.code="MAX_ATTEMPTS_EXCEEDED",e}if(r.attempts++,i.savecode(t,r),r.code!==e.code){let e=Error("Invalid verification code");throw e.code="CODE_INVALID",e.details={attemptsRemaining:r.maxAttempts-r.attempts},e}return i.deleteCode(t),!0}static getMessages(e){return i.getMessages(e)}static getLatestCode(e,t){let r=this.createCodeKey(e,t),s=i.getCode(r);return s?.code||null}static getAllActiveCodes(){i.clearExpiredCodes();let e=[];for(let[t,r]of i.getAllCodes().entries())r.expiresAt>Date.now()&&e.push(r);return e}static clearAll(){i.getAllCodes().clear(),i.clearMessages(),i.clearRateLimits()}}let n=[{id:"admin-1",email:"<EMAIL>",name:"Admin User",role:"admin",isEmailVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"user-1",email:"<EMAIL>",name:"Test User",role:"user",isEmailVerified:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],c={},l=e=>new Promise(t=>setTimeout(t,e)),d=e=>{let t={userId:e.id,email:e.email,role:e.role,exp:Math.floor(Date.now()/1e3)+604800};return`mock.jwt.${btoa(JSON.stringify(t))}`};class m{static async login(e,t){await l(1e3);let r=n.find(t=>t.email===e);if(!r)throw Error("User not found");if(({"<EMAIL>":"123456789@","<EMAIL>":"password123"})[e]!==t)throw Error("Invalid password");let s=d(r),i=d(r);return{user:r,tokens:{accessToken:s,refreshToken:i,expiresAt:Date.now()+6048e5}}}static async register(e,t,r,s,i){if(await l(1200),n.find(t=>t.email===e))throw Error("User already exists with this email");if(i){if("000000"===i)throw Error("Invalid referral code. Please check and try again.");console.log(`Mock: Referral code ${i} accepted`)}let a={id:`user-${Date.now()}`,email:e,name:`${r} ${s}`,role:"user",isEmailVerified:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n.push(a);try{await o.sendEmailVerification({email:a.email,name:a.name,type:"registration"})}catch(e){console.warn("Failed to send verification email during registration:",e)}let c=d(a),m=d(a);return{user:a,tokens:{accessToken:c,refreshToken:m,expiresAt:Date.now()+6048e5}}}static async forgotPassword(e){await l(800);let t=n.find(t=>t.email===e);if(!t)return console.log(`Mock: Email ${e} not found, but returning success for security`),{message:"If an account with this email exists, a reset code has been sent."};try{let e=await o.sendPasswordReset({email:t.email,name:t.name});return{message:e.message,code:e.code}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send password reset email")}}static async resetPassword(e,t,r){if(await l(1e3),!n.find(t=>t.email===e))throw Error("User not found");try{return await o.verifyCode({email:e,code:t,type:"password-reset"}),console.log(`Mock: Password reset successful for ${e}`),console.log(`Mock: New password would be: ${r}`),{message:"Password reset successfully"}}catch(e){if(e instanceof Error)throw e;throw Error("Password reset failed")}}static async refreshToken(e){await l(500);try{let t=JSON.parse(atob(e.split(".")[2])),r=n.find(e=>e.id===t.userId);if(!r)throw Error("Invalid refresh token");let s=d(r),i=d(r),a=Date.now()+6048e5;return{tokens:{accessToken:s,refreshToken:i,expiresAt:a}}}catch(e){throw Error("Invalid refresh token")}}static async verifyToken(e){await l(300);try{return JSON.parse(atob(e.split(".")[2])).exp>Math.floor(Date.now()/1e3)}catch(e){return!1}}static getVerificationCodes(){return{...c}}static clearExpiredCodes(){let e=Date.now();Object.keys(c).forEach(t=>{c[t].expires<e&&delete c[t]})}static async sendEmailVerification(e,t){await l(800);try{if(!n.find(t=>t.email===e))throw Error("User not found");let r=await o.sendEmailVerification({email:e,name:t,type:"registration"});return{success:r.success,message:r.message,code:r.code}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to send verification email")}}static async verifyEmail(e,t){await l(600);try{await o.verifyCode({email:e,code:t,type:"email-verification"});let r=n.find(t=>t.email===e);return r&&(r.isEmailVerified=!0,r.updatedAt=new Date().toISOString()),{success:!0,message:"Email verified successfully"}}catch(e){if(e instanceof Error)throw e;throw Error("Email verification failed")}}static async resendEmailVerification(e){await l(800);try{let t=n.find(t=>t.email===e);if(!t)throw Error("User not found");if(t.isEmailVerified)throw Error("Email is already verified");let r=await o.sendEmailVerification({email:e,name:t.name,type:"registration"});return{success:r.success,message:r.message,code:r.code}}catch(e){if(e instanceof Error&&"code"in e)throw e;throw Error("Failed to resend verification email")}}}},61773:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f224e4721f5ca5775123a32a61c3ed9d39d024dc3":()=>s.at,"7f8c5c51cbacb0c8ebb58a84bbada3dbf7311b5599":()=>s.ot,"7fb4440f3f5270c51037d546ce3fc826912f39a08a":()=>s.ai});var s=r(98749)},64616:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>o});var s=r(60687),i=r(10218),a=r(52581);let o=({...e})=>{let{theme:t="system"}=(0,i.D)();return(0,s.jsx)(a.l$,{theme:t,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",error:"!group-[.toaster]:bg-red-50 !group-[.toaster]:text-red-900 !group-[.toaster]:border-red-200 dark:!group-[.toaster]:bg-red-950 dark:!group-[.toaster]:text-red-100 dark:!group-[.toaster]:border-red-800",success:"!group-[.toaster]:bg-green-50 !group-[.toaster]:text-green-900 !group-[.toaster]:border-green-200 dark:!group-[.toaster]:bg-green-950 dark:!group-[.toaster]:text-green-100 dark:!group-[.toaster]:border-green-800",warning:"!group-[.toaster]:bg-yellow-50 !group-[.toaster]:text-yellow-900 !group-[.toaster]:border-yellow-200 dark:!group-[.toaster]:bg-yellow-950 dark:!group-[.toaster]:text-yellow-100 dark:!group-[.toaster]:border-yellow-800",info:"!group-[.toaster]:bg-blue-50 !group-[.toaster]:text-blue-900 !group-[.toaster]:border-blue-200 dark:!group-[.toaster]:bg-blue-950 dark:!group-[.toaster]:text-blue-100 dark:!group-[.toaster]:border-blue-800"}},...e})}},66420:(e,t,r)=>{"use strict";r.d(t,{hS:()=>s});let s={sm:"h-4 w-4",md:"h-6 w-6",lg:"h-7 w-7",xl:"h-8 w-8",badge:"h-4 w-4",navigation:"h-6 w-6",sidebar:"h-6 w-6",header:"h-6 w-6",button:"h-6 w-6",projectIcon:"h-6 w-6",actionIcon:"h-6 w-6",chatIcon:"h-6 w-6",profileIcon:"h-6 w-6",notificationIcon:"h-6 w-6"}},67393:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx","default")},70042:(e,t,r)=>{"use strict";r.d(t,{V:()=>o});var s=r(60687),i=r(66420),a=r(4780);let o=r(43210).forwardRef(({className:e,icon:t,iconClassName:r,text:o,subText:n,textClassName:c,subTextClassName:l,badge:d,badgeClassName:m,trailing:u,variant:p="default",size:g="md",showBorder:h=!1,borderClassName:f,layout:y="horizontal",hoverColor:x="green",hoverScale:b=!0,children:v,...w},k)=>{let E=h?`border ${f||"border-border/30"}`:"",C={sm:i.hS.sm,md:i.hS.md,lg:i.hS.lg};return(0,s.jsxs)("button",{ref:k,className:(0,a.cn)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer",{default:"bg-background hover:bg-sidebar-accent",ghost:"bg-transparent hover:bg-sidebar-accent",outline:"bg-background border border-input hover:bg-gray-100",accent:"bg-sidebar-accent hover:bg-sidebar-accent/80",secondary:"bg-gray-900 text-white hover:bg-gray-800"}[p],{sm:"icon-only"===y?"h-6 w-6 p-1":"h-8 px-2 py-1 text-xs",md:"icon-only"===y?"h-8 w-8 p-1.5":"h-10 px-3 py-2 text-sm",lg:"icon-only"===y?"h-10 w-10 p-2":"h-12 px-4 py-3 text-base"}[g],{green:"hover:text-[var(--primary-dark)]",grey:"hover:text-muted-foreground hover:bg-muted/50 hover:border-border",accent:"hover:text-accent-foreground"}[x],{horizontal:"flex-row gap-2",vertical:"flex-col gap-1","icon-only":"flex-row"}[y],E,"rounded-lg","horizontal"===y&&o?"justify-start":"","icon-only"===y?"justify-center":"",e),...w,children:[t&&(0,s.jsx)(t,{className:(0,a.cn)(r||C[g],b?"transition-transform duration-200 hover:scale-110":"")}),(o||n)&&"icon-only"!==y&&(0,s.jsxs)("div",{className:(0,a.cn)("flex flex-col","horizontal"===y?"flex-1 text-left":"text-center","vertical"===y?"items-center":"items-start"),children:[o&&(0,s.jsx)("span",{className:(0,a.cn)("font-medium truncate","sm"===g?"text-xs":"md"===g?"text-sm":"text-base",c),children:o}),n&&(0,s.jsx)("span",{className:(0,a.cn)("text-muted-foreground truncate","text-xs",l),children:n})]}),d&&(0,s.jsx)("span",{className:(0,a.cn)("bg-primary text-primary-foreground rounded-md flex items-center justify-center absolute -top-1 -right-1 z-10","sm"===g||"md"===g?"text-xs h-4 min-w-4":"text-sm h-5 min-w-5",m),children:d}),u&&"icon-only"!==y&&(0,s.jsx)("div",{className:"ml-auto",children:u}),v]})});o.displayName="SidebarButton"},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74935:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},76398:(e,t,r)=>{Promise.resolve().then(r.bind(r,54431))},77753:(e,t,r)=>{"use strict";r.d(t,{y:()=>i});var s=r(6475);let i=(0,s.createServerReference)("7f294d927378fcfaafe4b72d3c48c966515621b8b4",s.callServer,void 0,s.findSourceMapURL,"invalidateCacheAction")},78941:(e,t,r)=>{"use strict";r.d(t,{B:()=>p});var s=r(26787),i=r(59350),a=r(98647),o=r(61570);let n=process.env.NEXT_PUBLIC_TOKEN_ENCRYPTION_KEY||"fallback-key-change-in-production";class c{static{this.key=n}static initialize(){this.key=n}static encrypt(e){return e}static decrypt(e){return e}}c.initialize();class l{static setTokens(e){}static getTokens(){return null}static clearTokens(){}static hasValidTokens(){let e=this.getTokens();return null!==e&&e.expiresAt>Date.now()}static getAccessToken(){let e=this.getTokens();return e?.accessToken||null}static getRefreshToken(){let e=this.getTokens();return e?.refreshToken||null}static setUser(e){}static getUser(){return null}static clearUser(){}static clearAll(){this.clearTokens(),this.clearUser()}}let d={user:null,tokens:null,isAuthenticated:!1,isInitialized:!1,pendingEmailVerification:void 0,emailVerificationSent:!1},m=null,u=e=>{m&&clearTimeout(m);let t=Math.max(e-Date.now()-3e5,6e4);t>0&&(m=setTimeout(()=>{p.getState().actions.refreshToken()},t))},p=(0,s.v)()((0,i.lt)((0,i.eh)((0,i.Zr)((e,t)=>({...d,isLoading:!1,error:null,actions:{login:async t=>{e({isLoading:!0,error:null});try{let r=await o.N.login(t.email,t.password);if(l.setTokens(r.tokens),l.setUser(r.user),r.tokens.accessToken)try{(0,a.R)(r.tokens.accessToken)}catch(e){console.warn("Failed to initialize admin API:",e)}e({user:r.user,tokens:r.tokens,isAuthenticated:!0,isLoading:!1,error:null}),u(r.tokens.expiresAt)}catch(t){throw e({error:{code:"LOGIN_FAILED",message:t instanceof Error?t.message:"Login failed"},isLoading:!1}),t}},signup:async t=>{e({isLoading:!0,error:null});try{let r=await o.N.register(t.email,t.password,t.name.split(" ")[0]||"User",t.name.split(" ").slice(1).join(" ")||"");l.setTokens(r.tokens),l.setUser(r.user),e({user:r.user,tokens:r.tokens,isAuthenticated:!0,isLoading:!1,error:null}),u(r.tokens.expiresAt)}catch(t){throw e({error:{code:"SIGNUP_FAILED",message:t instanceof Error?t.message:"Signup failed"},isLoading:!1}),t}},logout:()=>{m&&(clearTimeout(m),m=null),l.clearAll(),e({...d,isInitialized:!0})},refreshToken:async()=>{try{let t=l.getTokens();if(!t?.refreshToken)throw Error("No refresh token available");let r=await o.N.refreshToken(t.refreshToken);l.setTokens(r.tokens),e({tokens:r.tokens}),u(r.tokens.expiresAt)}catch(e){throw t().actions.logout(),e}},initializeSession:async()=>{try{e({isLoading:!0});let t=l.getTokens(),r=l.getUser();if(t&&r)if(t.expiresAt>Date.now()){if(t.accessToken)try{(0,a.R)(t.accessToken)}catch(e){console.warn("Failed to initialize admin API:",e)}e({user:r,tokens:t,isAuthenticated:!0,isInitialized:!0,isLoading:!1}),u(t.expiresAt);return}else l.clearAll();e({...d,isInitialized:!0,isLoading:!1})}catch(t){console.error("Session initialization failed:",t),e({...d,isInitialized:!0,isLoading:!1})}},clearSession:()=>{m&&(clearTimeout(m),m=null),l.clearAll(),e(d)},updateUser:r=>{let s=t().user;if(s){let t={...s,...r};l.setUser(t),e({user:t})}},setTokens:t=>{l.setTokens(t),e({tokens:t}),u(t.expiresAt)},clearTokens:()=>{m&&(clearTimeout(m),m=null),l.clearTokens(),e({tokens:null,isAuthenticated:!1})},sendEmailVerification:async(t,r)=>{e({isLoading:!0,error:null});try{await o.N.sendEmailVerification(t,r),e({isLoading:!1,pendingEmailVerification:t,emailVerificationSent:!0})}catch(t){throw e({error:{code:"EMAIL_VERIFICATION_FAILED",message:t instanceof Error?t.message:"Failed to send verification email"},isLoading:!1}),t}},verifyEmail:async(r,s)=>{e({isLoading:!0,error:null});try{await o.N.verifyEmail(r,s);let i=t().user;if(i&&i.email===r){let t={...i,isEmailVerified:!0};l.setUser(t),e({user:t,pendingEmailVerification:void 0,emailVerificationSent:!1,isLoading:!1})}else e({pendingEmailVerification:void 0,emailVerificationSent:!1,isLoading:!1})}catch(t){throw e({error:{code:"EMAIL_VERIFICATION_FAILED",message:t instanceof Error?t.message:"Email verification failed"},isLoading:!1}),t}},resendEmailVerification:async t=>{e({isLoading:!0,error:null});try{await o.N.resendEmailVerification(t),e({isLoading:!1,emailVerificationSent:!0})}catch(t){throw e({error:{code:"EMAIL_VERIFICATION_FAILED",message:t instanceof Error?t.message:"Failed to resend verification email"},isLoading:!1}),t}},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})}}}),{name:"session-store",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated,isInitialized:e.isInitialized})})),{name:"session-store"}))},80554:(e,t,r)=>{"use strict";r.d(t,{PHProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call PHProvider() from the server but PHProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx","PHProvider")},83701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx","ThemeProvider")},87730:(e,t,r)=>{"use strict";r.d(t,{BackgroundProvider:()=>n});var s=r(60687),i=r(43210);let a={enabled:!0},o=(0,i.createContext)(void 0);function n({children:e}){let[t,r]=(0,i.useState)(a);return(0,s.jsx)(o.Provider,{value:{settings:t,updateSettings:e=>{r(t=>({...t,...e}))},resetSettings:()=>{r(a)}},children:e})}},89700:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},89848:(e,t,r)=>{Promise.resolve().then(r.bind(r,54413))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y,metadata:()=>f});var s=r(37413),i=r(83701);r(61135);var a=r(61606),o=r(23440),n=r(28421),c=r(22888),l=r(48482),d=r(19808),m=r(51022),u=r.n(m),p=r(80554),g=r(18579),h=r(26785);let f=(0,g.YQ)({title:"Siift - Modern Project Management",description:"A modern project management platform built with Next.js and NestJS. Streamline your workflow with powerful tools and intuitive design.",keywords:["project management","productivity","collaboration","workflow","task management","team collaboration"],url:"/"});function y({children:e}){let t=h.M.organization({name:"Siift",url:"https://siift.app",logo:"https://siift.app/images/logo.png",description:"A modern project management platform built with Next.js and NestJS",socialMedia:["https://twitter.com/siiftapp","https://linkedin.com/company/siift"]}),r=h.M.website({name:"Siift",url:"https://siift.app",description:"A modern project management platform built with Next.js and NestJS",searchUrl:"https://siift.app/search?q={search_term_string}"});return(0,s.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,s.jsx)("head",{children:(0,s.jsx)(h.Y,{data:[t,r]})}),(0,s.jsx)("body",{className:`${u().variable} antialiased font-outfit`,suppressHydrationWarning:!0,children:(0,s.jsx)(a.lJ,{children:(0,s.jsx)(c.QueryProvider,{children:(0,s.jsx)(p.PHProvider,{children:(0,s.jsx)(i.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:(0,s.jsx)(d.BackgroundProvider,{children:(0,s.jsx)(o.SessionProvider,{children:(0,s.jsxs)(n.ClerkSessionProvider,{children:[e,(0,s.jsx)(l.Toaster,{})]})})})})})})})})]})}},95881:(e,t,r)=>{"use strict";r.d(t,{SessionProvider:()=>c});var s=r(60687);r(43210);let i=({message:e="Loading...",size:t="md",fullScreen:r=!0})=>{let i={sm:{circle:"w-12 h-12",text:"text-sm",spacing:"space-y-3"},md:{circle:"w-16 h-16",text:"text-base",spacing:"space-y-4"},lg:{circle:"w-16 h-16",text:"text-lg",spacing:"space-y-6"}}[t];return(0,s.jsx)("div",{className:r?"fixed inset-0 z-50 flex items-center justify-center":"flex items-center justify-center w-full h-full",children:(0,s.jsxs)("div",{className:`text-center ${i.spacing}`,children:[(0,s.jsx)("div",{className:"relative flex items-center justify-center",children:(0,s.jsx)("div",{className:`${i.circle} border-4 border-gray-200 border-t-green-500 rounded-full animate-spin`})}),e&&(0,s.jsx)("p",{className:`text-gray-600 dark:text-gray-400 font-medium ${i.text}`,children:e})]})})},a=({message:e})=>(0,s.jsx)(i,{message:e,size:"md",fullScreen:!0});var o=r(78941),n=r(16189);function c({children:e}){let{actions:t,isInitialized:r,isLoading:i}=(0,o.B)(),c=(0,n.usePathname)(),l=c?.startsWith("/auth/");return r&&!i||l?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)(a,{message:"Initializing..."})}},96871:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var s=r(60687),i=r(10218);function a({children:e,...t}){return(0,s.jsx)(i.N,{...t,children:e})}r(43210)},98647:(e,t,r)=>{"use strict";r.d(t,{R:()=>o,i:()=>a});let s="http://localhost:3000";console.log("\uD83D\uDD27 Admin API Base URL:",s),console.log("\uD83D\uDD27 Environment variable NEXT_PUBLIC_ADMIN_API_URL:","http://localhost:3000");class i{setToken(e){this.token=e,console.log("\uD83D\uDD11 Admin API token set:",e?`${e.substring(0,20)}...`:"null")}getToken(){return this.token}async request(e,t={}){let r=`${s}${e}`,i={headers:{"Content-Type":"application/json",...this.token&&{Authorization:`Bearer ${this.token}`},...t.headers},...t};console.log(`🚀 Admin API Request: ${t.method||"GET"} ${r}`),console.log("� Token available:",!!this.token),console.log("�\uD83D\uDCE4 Request config:",{headers:i.headers,method:t.method||"GET",body:t.body});try{let t=Date.now(),s=await fetch(r,i),a=Date.now()-t;if(console.log(`📥 Admin API Response: ${e}`),console.log(`⏱️  Duration: ${a}ms`),console.log(`📊 Status: ${s.status} ${s.statusText}`),!s.ok){if(401===s.status)throw console.error("❌ Unauthorized - Please check your admin credentials"),Error("Unauthorized - Please check your admin credentials");if(403===s.status)throw console.error("❌ Forbidden - Admin access required"),Error("Forbidden - Admin access required");let e=await s.json().catch(()=>({}));throw console.error("❌ Error response:",e),Error(e.message||`HTTP error! status: ${s.status}`)}let o=await s.json();return console.log("✅ Response data:",o),console.log("---"),o}catch(t){throw console.error(`❌ Admin API request failed: ${e}`,t),console.log("---"),t}}async getAnalyticsSummary(){return this.request("/admin/analytics/summary")}async getUserAnalytics(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/analytics/users${r?`?${r}`:""}`;return this.request(s)}async getActivityMetrics(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/analytics/activity-metrics${r?`?${r}`:""}`;return this.request(s)}async getActivityTrends(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/analytics/activity-trends${r?`?${r}`:""}`;return this.request(s)}async getHealthCheck(){return this.request("/admin/health")}async getAgentCalls(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/agent-analytics/calls${r?`?${r}`:""}`;return this.request(s)}async getAgentUsageStats(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/agent-analytics/usage-stats${r?`?${r}`:""}`;return this.request(s)}async getTokenTrends(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/agent-analytics/token-trends${r?`?${r}`:""}`;return this.request(s)}async getUserCosts(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/agent-analytics/user-costs${r?`?${r}`:""}`;return this.request(s)}async getModelPerformance(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/agent-analytics/model-performance${r?`?${r}`:""}`;return this.request(s)}async getAgentEfficiency(e={}){let t=new URLSearchParams;Object.entries(e).forEach(([e,r])=>{null!=r&&t.append(e,r.toString())});let r=t.toString(),s=`/admin/agent-analytics/agent-efficiency${r?`?${r}`:""}`;return this.request(s)}async runMigrations(){return this.request("/admin/migrations/run",{method:"POST"})}async cleanupTenantSchemas(){return this.request("/admin/cleanup/schemas",{method:"POST"})}async getFeedback(e){let t=new URLSearchParams;e?.page&&t.append("page",e.page.toString()),e?.limit&&t.append("limit",e.limit.toString()),e?.search&&t.append("search",e.search),e?.isHighPriority!==void 0&&t.append("isHighPriority",e.isHighPriority.toString()),e?.minSubmissionCount&&t.append("minSubmissionCount",e.minSubmissionCount.toString()),e?.startDate&&t.append("startDate",e.startDate),e?.endDate&&t.append("endDate",e.endDate),e?.sortBy&&t.append("sortBy",e.sortBy),e?.sortOrder&&t.append("sortOrder",e.sortOrder);let r=t.toString(),s=r?`/admin/feedback?${r}`:"/admin/feedback";return this.request(s)}constructor(){this.token=null}}let a=new i,o=e=>{a.setToken(e)}},99576:(e,t,r)=>{Promise.resolve().then(r.bind(r,57347))}};