"use strict";exports.id=4336,exports.ids=[4336],exports.modules={21342:(e,t,a)=>{a.d(t,{I:()=>c,SQ:()=>d,V0:()=>f,_2:()=>l,lp:()=>u,mB:()=>g,rI:()=>n,ty:()=>i});var r=a(60687);a(43210);var o=a(26312),s=a(4780);function n({...e}){return(0,r.jsx)(o.bL,{"data-slot":"dropdown-menu",...e})}function i({...e}){return(0,r.jsx)(o.l9,{"data-slot":"dropdown-menu-trigger",...e})}function d({className:e,sideOffset:t=4,...a}){return(0,r.jsx)(o.ZL,{children:(0,r.jsx)(o.<PERSON>,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,s.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...a})})}function c({...e}){return(0,r.jsx)(o.YJ,{"data-slot":"dropdown-menu-group",...e})}function l({className:e,inset:t,variant:a="default",...n}){return(0,r.jsx)(o.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":a,className:(0,s.cn)("focus:bg-gray-100 border border-transparent focus:text-gray-900 data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n})}function u({className:e,inset:t,...a}){return(0,r.jsx)(o.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,s.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...a})}function g({className:e,...t}){return(0,r.jsx)(o.wv,{"data-slot":"dropdown-menu-separator",className:(0,s.cn)("bg-border -mx-1 my-1 h-px",e),...t})}function f({className:e,...t}){return(0,r.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,s.cn)("text-muted-foreground ml-auto text-xs tracking-widest",e),...t})}},29523:(e,t,a)=>{a.d(t,{$:()=>d});var r=a(60687),o=a(8730),s=a(24224);a(43210);var n=a(4780);let i=(0,s.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:a,asChild:s=!1,...d}){let c=s?o.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:a,className:e})),...d})}},67074:(e,t,a)=>{a.d(t,{GF:()=>o,Ni:()=>s});var r=a(52581);let o=(e,t)=>{r.oR.success(e,{description:t?.description,duration:t?.duration||4e3})},s=(e,t)=>{r.oR.error(e,{description:t?.description,duration:t?.duration||5e3})}},67146:(e,t,a)=>{a.d(t,{CG:()=>d,Fm:()=>g,Qs:()=>v,cj:()=>i,h:()=>u,qp:()=>f});var r=a(60687);a(43210);var o=a(26134),s=a(11860),n=a(4780);function i({...e}){return(0,r.jsx)(o.bL,{"data-slot":"sheet",...e})}function d({...e}){return(0,r.jsx)(o.l9,{"data-slot":"sheet-trigger",...e})}function c({...e}){return(0,r.jsx)(o.ZL,{"data-slot":"sheet-portal",...e})}function l({className:e,...t}){return(0,r.jsx)(o.hJ,{"data-slot":"sheet-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,side:a="right",...i}){return(0,r.jsxs)(c,{children:[(0,r.jsx)(l,{}),(0,r.jsxs)(o.UC,{"data-slot":"sheet-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===a&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===a&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===a&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===a&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...i,children:[t,(0,r.jsxs)(o.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,r.jsx)(s.A,{className:"size-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"sheet-header",className:(0,n.cn)("flex flex-col gap-1.5 p-4",e),...t})}function f({className:e,...t}){return(0,r.jsx)(o.hE,{"data-slot":"sheet-title",className:(0,n.cn)("text-foreground font-semibold",e),...t})}function v({className:e,...t}){return(0,r.jsx)(o.VY,{"data-slot":"sheet-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}},87979:(e,t,a)=>{a.d(t,{A:()=>i});var r=a(78941),o=a(16189),s=a(43210),n=a(67074);function i(){let{user:e,isAuthenticated:t,error:a,pendingEmailVerification:i,emailVerificationSent:d,actions:c}=(0,r.B)(),l=(0,o.useRouter)(),[u,g]=(0,s.useState)(!1),f=(0,s.useCallback)(async e=>{g(!0);try{await c.login(e);let{user:t}=r.B.getState();(0,n.GF)("Welcome back!",{description:`Logged in as ${t?.email}`}),t?.role==="admin"?l.replace("/admin"):l.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Login failed";throw(0,n.Ni)("Login Failed",{description:e}),t}finally{g(!1)}},[c,l,g]),v=(0,s.useCallback)(async e=>{g(!0);try{await c.signup(e);let{user:t}=r.B.getState();(0,n.GF)("Account created successfully!",{description:`Welcome ${t?.name||t?.email}!`}),t?.role==="admin"?l.replace("/admin"):l.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Signup failed";throw(0,n.Ni)("Signup Failed",{description:e}),t}finally{g(!1)}},[c,l,g]),b=(0,s.useCallback)(()=>{c.logout(),l.push("/auth/login")},[c,l]),h=(0,s.useCallback)(()=>{c.setError(null)},[c]),m=(0,s.useCallback)(async(e,t)=>{try{await c.sendEmailVerification(e,t)}catch(e){throw e}},[c]);return{user:e,isAuthenticated:t,isLoading:u,error:a,pendingEmailVerification:i,emailVerificationSent:d,login:f,signup:v,logout:b,clearError:h,sendEmailVerification:m,verifyEmail:(0,s.useCallback)(async(e,t)=>{try{await c.verifyEmail(e,t)}catch(e){throw e}},[c]),resendEmailVerification:(0,s.useCallback)(async e=>{try{await c.resendEmailVerification(e)}catch(e){throw e}},[c]),updateUser:c.updateUser,refreshToken:c.refreshToken}}},95450:(e,t,a)=>{a.d(t,{P:()=>i});var r=a(41330),o=a(14792),s=a(16189),n=a(52581);function i(){let{isSignedIn:e,signOut:t,getToken:a}=(0,r.d)(),{user:i,isLoaded:d}=(0,o.Jd)(),{signIn:c,isLoaded:l}=(0,o.go)(),{signUp:u,isLoaded:g}=(0,o.yC)(),f=(0,s.useRouter)();return{isSignedIn:e,isLoaded:d,user:i,signOut:async()=>{try{await t(),n.oR.success("Signed out successfully"),f.push("/")}catch(e){n.oR.error("Failed to sign out"),console.error("Sign out error:",e)}},getToken:async()=>{try{return await a()}catch(e){return console.error("Error getting Clerk token:",e),null}},signIn:c,signUp:u,signInLoaded:l,signUpLoaded:g,userId:i?.id,email:i?.emailAddresses[0]?.emailAddress,firstName:i?.firstName,lastName:i?.lastName,fullName:i?.fullName,imageUrl:i?.imageUrl,isEmailVerified:i?.emailAddresses[0]?.verification?.status==="verified"}}}};