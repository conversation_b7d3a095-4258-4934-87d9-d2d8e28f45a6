"use strict";exports.id=4553,exports.ids=[4553],exports.modules={14553:(e,r,t)=>{t.r(r),t.d(r,{hasSrcAppDir:()=>o,suggestMiddlewareLocation:()=>i});var s=t(17561);function o(){let{existsSync:e}=(0,s.p8)(),r=(0,s.dc)(),t=(0,s.y4)();return!!e(r.join(t(),"src","app"))}function i(){let e=["ts","js"],r=(e,r,t)=>`Clerk: clerkMiddleware() was not run, your middleware file might be misplaced. Move your middleware file to ./${r}middleware.${e}. Currently located at ./${t}middleware.${e}`,{existsSync:t}=(0,s.p8)(),o=(0,s.dc)(),i=(0,s.y4)(),d=o.join(i(),"src","app"),p=o.join(i(),"app"),a=(s,i,d)=>{for(let p of e)if(t(o.join(s,`middleware.${p}`)))return r(p,i,d)};return t(d)?a(d,"src/","src/app/")||a(i(),"src/",""):t(p)?a(p,"","app/"):void 0}},17561:(e,r,t)=>{t.d(r,{y4:()=>a,p8:()=>d,dc:()=>p});var s=Object.getOwnPropertyNames;let o=((e,r)=>function(){return r||(0,e[s(e)[0]])((r={exports:{}}).exports,r),r.exports})({"src/runtime/node/safe-node-apis.js"(e,r){let{existsSync:s,writeFileSync:o,readFileSync:i,appendFileSync:d,mkdirSync:p,rmSync:a}=t(73024);r.exports={fs:{existsSync:s,writeFileSync:o,readFileSync:i,appendFileSync:d,mkdirSync:p,rmSync:a},path:t(76760),cwd:()=>process.cwd()}}})(),i=e=>{throw Error(`Clerk: ${e} is missing. This is an internal error. Please contact Clerk's support.`)},d=()=>(o.fs||i("fs"),o.fs),p=()=>(o.path||i("path"),o.path),a=()=>(o.cwd||i("cwd"),o.cwd)}};