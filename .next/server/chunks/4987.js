"use strict";exports.id=4987,exports.ids=[4987],exports.modules={8759:(e,a,t)=>{t.d(a,{Ke:()=>n,Nt:()=>s,R6:()=>d});var r=t(60687),i=t(95682);function s({...e}){return(0,r.jsx)(i.bL,{"data-slot":"collapsible",...e})}function d({...e}){return(0,r.jsx)(i.R6,{"data-slot":"collapsible-trigger",...e})}function n({...e}){return(0,r.jsx)(i.Ke,{"data-slot":"collapsible-content",...e})}},25070:(e,a,t)=>{t.d(a,{Bx:()=>g,Yv:()=>j,CG:()=>w,Cn:()=>N,jj:()=>y,Gh:()=>h,sF:()=>v,wZ:()=>z,Uj:()=>C,FX:()=>k,q9:()=>L,Cp:()=>B,Fg:()=>S,GB:()=>p,jM:()=>m,x2:()=>x,cL:()=>f});var r=t(60687),i=t(43210),s=t(8730),d=t(24224),n=t(51214),o=t(4780),l=t(29523);t(89667),t(35950);var c=t(67146),u=t(76242);let b=i.createContext(null);function f(){let e=i.useContext(b);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function p({defaultOpen:e=!0,open:a,onOpenChange:t,className:s,style:d,children:n,...l}){let c=function(){let[e,a]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),t=()=>{a(window.innerWidth<768)};return e.addEventListener("change",t),a(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[f,p]=i.useState(!1),[g,x]=i.useState(e),m=a??g,v=i.useCallback(e=>{let a="function"==typeof e?e(m):e;t?t(a):x(a),document.cookie=`sidebar_state=${a}; path=/; max-age=604800`},[t,m]),h=i.useCallback(()=>c?p(e=>!e):v(e=>!e),[c,v,p]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),h())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[h]);let w=m?"expanded":"collapsed",j=i.useMemo(()=>({state:w,open:m,setOpen:v,isMobile:c,openMobile:f,setOpenMobile:p,toggleSidebar:h}),[w,m,v,c,f,p,h]);return(0,r.jsx)(b.Provider,{value:j,children:(0,r.jsx)(u.Bc,{delayDuration:0,children:(0,r.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...d},className:(0,o.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",s),...l,children:n})})})}function g({side:e="left",variant:a="sidebar",collapsible:t="offcanvas",className:i,children:s,...d}){let{isMobile:n,state:l,openMobile:u,setOpenMobile:b}=f();return"none"===t?(0,r.jsx)("div",{"data-slot":"sidebar",className:(0,o.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",i),...d,children:s}):n?(0,r.jsx)(c.cj,{open:u,onOpenChange:b,...d,children:(0,r.jsxs)(c.h,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:e,children:[(0,r.jsxs)(c.Fm,{className:"sr-only",children:[(0,r.jsx)(c.qp,{children:"Sidebar"}),(0,r.jsx)(c.Qs,{children:"Displays the mobile sidebar."})]}),(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:s})]})}):(0,r.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?t:"","data-variant":a,"data-side":e,"data-slot":"sidebar",children:[(0,r.jsx)("div",{"data-slot":"sidebar-gap",className:(0,o.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,r.jsx)("div",{"data-slot":"sidebar-container",className:(0,o.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===e?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",i),...d,children:(0,r.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:s})})]})}function x({className:e,onClick:a,...t}){let{toggleSidebar:i}=f();return(0,r.jsxs)(l.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,o.cn)("size-7",e),onClick:e=>{a?.(e),i()},...t,children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function m({className:e,...a}){let{toggleSidebar:t}=f();return(0,r.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:t,title:"Toggle Sidebar",className:(0,o.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",e),...a})}function v({className:e,...a}){return(0,r.jsx)("main",{"data-slot":"sidebar-inset",className:(0,o.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",e),...a})}function h({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...a})}function w({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,o.cn)("flex flex-col gap-2 p-2",e),...a})}function j({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,o.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...a})}function N({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,o.cn)("relative flex w-full min-w-0 flex-col p-2",e),...a})}function y({className:e,asChild:a=!1,...t}){let i=a?s.DX:"div";return(0,r.jsx)(i,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,o.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...t})}function z({className:e,...a}){return(0,r.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,o.cn)("flex w-full min-w-0 flex-col gap-1",e),...a})}function k({className:e,...a}){return(0,r.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,o.cn)("group/menu-item relative",e),...a})}let _=(0,d.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function C({asChild:e=!1,isActive:a=!1,variant:t="default",size:i="default",tooltip:d,className:n,...l}){let c=e?s.DX:"button",{isMobile:b,state:p}=f(),g=(0,r.jsx)(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":i,"data-active":a,className:(0,o.cn)(_({variant:t,size:i}),n),...l});return d?("string"==typeof d&&(d={children:d}),(0,r.jsxs)(u.m_,{children:[(0,r.jsx)(u.k$,{asChild:!0,children:g}),(0,r.jsx)(u.ZI,{side:"right",align:"center",hidden:"collapsed"!==p||b,...d})]})):g}function L({className:e,...a}){return(0,r.jsx)("ul",{"data-slot":"sidebar-menu-sub","data-sidebar":"menu-sub",className:(0,o.cn)("border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",e),...a})}function S({className:e,...a}){return(0,r.jsx)("li",{"data-slot":"sidebar-menu-sub-item","data-sidebar":"menu-sub-item",className:(0,o.cn)("group/menu-sub-item relative",e),...a})}function B({asChild:e=!1,size:a="md",isActive:t=!1,className:i,...d}){let n=e?s.DX:"a";return(0,r.jsx)(n,{"data-slot":"sidebar-menu-sub-button","data-sidebar":"menu-sub-button","data-size":a,"data-active":t,className:(0,o.cn)("text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===a&&"text-xs","md"===a&&"text-sm","group-data-[collapsible=icon]:hidden",i),...d})}},32584:(e,a,t)=>{t.d(a,{BK:()=>n,eu:()=>d,q5:()=>o});var r=t(60687);t(43210);var i=t(11096),s=t(4780);function d({className:e,...a}){return(0,r.jsx)(i.bL,{"data-slot":"avatar",className:(0,s.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...a})}function n({className:e,...a}){return(0,r.jsx)(i._V,{"data-slot":"avatar-image",className:(0,s.cn)("aspect-square size-full",e),...a})}function o({className:e,...a}){return(0,r.jsx)(i.H4,{"data-slot":"avatar-fallback",className:(0,s.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...a})}},35950:(e,a,t)=>{t.d(a,{w:()=>d});var r=t(60687);t(43210);var i=t(62369),s=t(4780);function d({className:e,orientation:a="horizontal",decorative:t=!0,...d}){return(0,r.jsx)(i.b,{"data-slot":"separator",decorative:t,orientation:a,className:(0,s.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...d})}},44493:(e,a,t)=>{t.d(a,{BT:()=>o,Wu:()=>l,ZB:()=>n,Zp:()=>s,aR:()=>d,wL:()=>c});var r=t(60687);t(43210);var i=t(4780);function s({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...a})}function d({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...a})}function n({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...a})}function o({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...a})}function l({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...a})}function c({className:e,...a}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,i.cn)("flex px-6 [.border-t]:pt-6",e),...a})}},76242:(e,a,t)=>{t.d(a,{Bc:()=>d,ZI:()=>l,k$:()=>o,m_:()=>n});var r=t(60687);t(43210);var i=t(9989),s=t(4780);function d({delayDuration:e=0,...a}){return(0,r.jsx)(i.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...a})}function n({...e}){return(0,r.jsx)(d,{children:(0,r.jsx)(i.bL,{"data-slot":"tooltip",...e})})}function o({...e}){return(0,r.jsx)(i.l9,{"data-slot":"tooltip-trigger",...e})}function l({className:e,sideOffset:a=0,children:t,...d}){return(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,s.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",e),...d,children:[t,(0,r.jsx)(i.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},89667:(e,a,t)=>{t.d(a,{p:()=>s});var r=t(60687);t(43210);var i=t(4780);function s({className:e,type:a,...t}){return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}}};