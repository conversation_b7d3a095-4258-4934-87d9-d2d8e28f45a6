exports.id=2140,exports.ids=[2140],exports.modules={7126:(e,t,s)=>{"use strict";s.d(t,{U:()=>ee});var r=s(60687),a=s(99891),i=s(53411),n=s(58869),l=s(84027),c=s(97051),d=s(18179),o=s(48730),m=s(41312),u=s(78200),x=s(10022),h=s(34318),g=s(24413),j=s(17458),f=s(64021),p=s(19959),b=s(11437),y=s(16189),v=s(43210),N=s.n(v),A=s(14952),k=s(85814),w=s.n(k),C=s(8759),P=s(25070);function U({items:e}){let t=(0,y.usePathname)();return(0,r.jsxs)(P.Cn,{children:[(0,r.jsx)(P.jj,{children:"Platform"}),(0,r.jsx)(P.wZ,{children:e.map(e=>{let s=t===e.url||t.startsWith(e.url+"/");return e.items?(0,r.jsx)(C.Nt,{asChild:!0,defaultOpen:s,className:"group/collapsible",children:(0,r.jsxs)(P.FX,{children:[(0,r.jsx)(C.R6,{asChild:!0,children:(0,r.jsxs)(P.Uj,{tooltip:e.title,className:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors duration-200",children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title}),(0,r.jsx)(A.A,{className:"ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90"})]})}),(0,r.jsx)(C.Ke,{children:(0,r.jsx)(P.q9,{className:"ml-4 border-l border-sidebar-border pl-2",children:e.items?.map(e=>{let s=t===e.url;return(0,r.jsx)(P.Fg,{children:(0,r.jsx)(P.Cp,{asChild:!0,isActive:s,className:"hover:bg-sidebar-accent/50 hover:text-sidebar-accent-foreground transition-colors duration-200 text-sidebar-foreground/80",children:(0,r.jsx)(w(),{href:e.url,children:e.title})})},e.title)})})})]})},e.title):(0,r.jsx)(P.FX,{children:(0,r.jsx)(P.Uj,{asChild:!0,tooltip:e.title,isActive:s,className:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground transition-colors duration-200",children:(0,r.jsxs)(w(),{href:e.url,children:[e.icon&&(0,r.jsx)(e.icon,{}),(0,r.jsx)("span",{children:e.title})]})})},e.title)})})]})}var R=s(17971),S=s(56085),z=s(70742),B=s(85778),F=s(40083),_=s(32584),I=s(21342),M=s(95450);function q({user:e}){let{isMobile:t}=(0,P.cL)(),{signOut:s}=(0,M.P)(),a=(0,y.useRouter)(),i=e=>e.split(" ").map(e=>e[0]).join("").toUpperCase().slice(0,2);return(0,r.jsx)(P.wZ,{children:(0,r.jsx)(P.FX,{children:(0,r.jsxs)(I.rI,{children:[(0,r.jsx)(I.ty,{asChild:!0,children:(0,r.jsxs)(P.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsxs)(_.eu,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(_.BK,{src:e.avatar,alt:e.name}),(0,r.jsx)(_.q5,{className:"rounded-lg",children:i(e.name)})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:e.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:e.email})]}),(0,r.jsx)(R.A,{className:"ml-auto size-4"})]})}),(0,r.jsxs)(I.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",side:t?"bottom":"right",align:"end",sideOffset:4,children:[(0,r.jsx)(I.lp,{className:"p-0 font-normal",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:[(0,r.jsxs)(_.eu,{className:"h-8 w-8 rounded-lg",children:[(0,r.jsx)(_.BK,{src:e.avatar,alt:e.name}),(0,r.jsx)(_.q5,{className:"rounded-lg",children:i(e.name)})]}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:e.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:e.email})]})]})}),(0,r.jsx)(I.mB,{}),(0,r.jsx)(I.I,{children:(0,r.jsxs)(I._2,{className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(S.A,{}),"Upgrade to Pro"]})}),(0,r.jsx)(I.mB,{}),(0,r.jsxs)(I.I,{children:[(0,r.jsxs)(I._2,{onClick:()=>{a.push("/user-dashboard?tab=profile")},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(z.A,{}),"Account"]}),(0,r.jsxs)(I._2,{onClick:()=>{a.push("/user-dashboard?tab=billing")},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(B.A,{}),"Billing"]}),(0,r.jsxs)(I._2,{onClick:()=>{a.push("/user-dashboard?tab=notifications")},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(c.A,{}),"Notifications"]})]}),(0,r.jsx)(I.mB,{}),(0,r.jsxs)(I._2,{onClick:()=>{s()},className:"hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)(F.A,{}),"Log out"]})]})]})})})}var D=s(96474);function G({teams:e}){let{isMobile:t}=(0,P.cL)(),[s,a]=v.useState(e[0]);return s?(0,r.jsx)(P.wZ,{children:(0,r.jsx)(P.FX,{children:(0,r.jsxs)(I.rI,{children:[(0,r.jsx)(I.ty,{asChild:!0,children:(0,r.jsxs)(P.Uj,{size:"lg",className:"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground",children:[(0,r.jsx)("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg",children:(0,r.jsx)(s.logo,{className:"size-4"})}),(0,r.jsxs)("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[(0,r.jsx)("span",{className:"truncate font-medium",children:s.name}),(0,r.jsx)("span",{className:"truncate text-xs",children:s.plan})]}),(0,r.jsx)(R.A,{className:"ml-auto"})]})}),(0,r.jsxs)(I.SQ,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"start",side:t?"bottom":"right",sideOffset:4,children:[(0,r.jsx)(I.lp,{className:"text-muted-foreground text-xs",children:"Teams"}),e.map((e,t)=>(0,r.jsxs)(I._2,{onClick:()=>a(e),className:"gap-2 p-2 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border",children:(0,r.jsx)(e.logo,{className:"size-3.5 shrink-0"})}),e.name,(0,r.jsxs)(I.V0,{children:["⌘",t+1]})]},e.name)),(0,r.jsx)(I.mB,{}),(0,r.jsxs)(I._2,{className:"gap-2 p-2 hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,r.jsx)("div",{className:"flex size-6 items-center justify-center rounded-md border bg-transparent",children:(0,r.jsx)(D.A,{className:"size-4"})}),(0,r.jsx)("div",{className:"text-muted-foreground font-medium",children:"Add team"})]})]})]})})}):null}var L=s(87979);let X=e=>({user:{name:e?.name||"Admin",email:e?.email||"",avatar:e?.avatar||""},teams:[{name:"Siift Admin",logo:a.A,plan:"Admin Panel"}],navMain:[{title:"Dashboard",url:"/admin/analytics",icon:i.A,isActive:!0,items:[{title:"Overview",url:"/admin/analytics/overview"},{title:"User Analytics",url:"/admin/analytics/users"},{title:"Activity Metrics",url:"/admin/analytics/activity-metrics"},{title:"Revenue Reports",url:"/admin/analytics/revenue"},{title:"Performance",url:"/admin/analytics/performance"},{title:"Feedbacks",url:"/admin/analytics/feedbacks"}]},{title:"Profile",url:"/admin/profile",icon:n.A},{title:"Settings",url:"/admin/settings-tab",icon:l.A},{title:"Notifications",url:"/admin/notifications",icon:c.A},{title:"Projects",url:"/admin/projects",icon:d.A,items:[{title:"All Projects",url:"/admin/projects/all"},{title:"Create New",url:"/admin/projects/create"}]},{title:"Recent",url:"/admin/recent",icon:o.A},{title:"User Management",url:"/admin/users",icon:m.A,items:[{title:"All Users",url:"/admin/users/all"},{title:"Active Users",url:"/admin/users/active"},{title:"Pending Approval",url:"/admin/users/pending"},{title:"User Roles",url:"/admin/users/roles"},{title:"Permissions",url:"/admin/users/permissions"}]},{title:"Agent Analytics",url:"/admin/agent",icon:u.A,items:[{title:"Agent Calls",url:"/admin/agent/calls"},{title:"Usage Statistics",url:"/admin/agent/usage-stats"},{title:"Token Trends",url:"/admin/agent/token-trends"},{title:"Performance",url:"/admin/agent/performance"}]},{title:"Reports",url:"/admin/reports",icon:x.A,items:[{title:"System Reports",url:"/admin/reports/system"},{title:"User Reports",url:"/admin/reports/users"},{title:"Activity Reports",url:"/admin/reports/activity"},{title:"Export Data",url:"/admin/reports/export"}]},{title:"System",url:"/admin/system",icon:h.A,items:[{title:"Health Check",url:"/admin/system/health",icon:a.A},{title:"Server Status",url:"/admin/system/status",icon:g.A},{title:"Logs",url:"/admin/system/logs",icon:j.A},{title:"Maintenance",url:"/admin/system/maintenance",icon:l.A}]},{title:"Settings",url:"/admin/settings",icon:l.A,items:[{title:"General",url:"/admin/settings/general",icon:l.A},{title:"Security",url:"/admin/settings/security",icon:f.A},{title:"Notifications",url:"/admin/settings/notifications",icon:c.A},{title:"API Keys",url:"/admin/settings/api-keys",icon:p.A},{title:"Integrations",url:"/admin/settings/integrations",icon:b.A}]}]});function K({...e}){let{user:t,logout:s}=(0,L.A)();(0,y.useRouter)();let a=X(t);return(0,r.jsxs)(P.Bx,{collapsible:"icon",...e,children:[(0,r.jsx)(P.Gh,{children:(0,r.jsx)(G,{teams:a.teams})}),(0,r.jsx)(P.Yv,{children:(0,r.jsx)(U,{items:a.navMain})}),(0,r.jsx)(P.CG,{children:(0,r.jsx)(q,{user:a.user})}),(0,r.jsx)(P.jM,{})]})}var O=s(67602),T=s(8730),Z=s(4780);function Q({...e}){return(0,r.jsx)("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function Y({className:e,...t}){return(0,r.jsx)("ol",{"data-slot":"breadcrumb-list",className:(0,Z.cn)("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function E({className:e,...t}){return(0,r.jsx)("li",{"data-slot":"breadcrumb-item",className:(0,Z.cn)("inline-flex items-center gap-1.5",e),...t})}function H({asChild:e,className:t,...s}){let a=e?T.DX:"a";return(0,r.jsx)(a,{"data-slot":"breadcrumb-link",className:(0,Z.cn)("hover:text-foreground transition-colors",t),...s})}function V({className:e,...t}){return(0,r.jsx)("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:(0,Z.cn)("text-foreground font-normal",e),...t})}function W({children:e,className:t,...s}){return(0,r.jsx)("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:(0,Z.cn)("[&>svg]:size-3.5",t),...s,children:e??(0,r.jsx)(A.A,{})})}var J=s(35950);let $=e=>{let t=e.split("/").filter(Boolean),s=[];if(t.length>1){if(s.push({title:"Admin",href:"/admin"}),"analytics"===t[1]){if(s.push({title:"Analytics",href:"/admin/analytics"}),t[2]){let r=t[2].split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");s.push({title:r,href:e,isCurrentPage:!0})}}else if("agent"===t[1]&&(s.push({title:"Agent Analytics",href:"/admin/agent"}),t[2])){let r=t[2].split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");s.push({title:r,href:e,isCurrentPage:!0})}}return s};function ee({children:e}){let t=$((0,y.usePathname)());return(0,r.jsx)(O.h,{children:(0,r.jsxs)(P.GB,{children:[(0,r.jsx)(K,{}),(0,r.jsxs)(P.sF,{children:[(0,r.jsx)("header",{className:"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 px-4",children:[(0,r.jsx)(P.x2,{className:"-ml-1"}),(0,r.jsx)(J.w,{orientation:"vertical",className:"mr-2 data-[orientation=vertical]:h-4"}),(0,r.jsx)(Q,{children:(0,r.jsx)(Y,{children:t.map((e,s)=>(0,r.jsxs)(N().Fragment,{children:[(0,r.jsx)(E,{className:0===s?"hidden md:block":"",children:e.isCurrentPage?(0,r.jsx)(V,{children:e.title}):(0,r.jsx)(H,{href:e.href,children:e.title})}),s<t.length-1&&(0,r.jsx)(W,{className:"hidden md:block"})]},e.href))})})]})}),(0,r.jsx)("div",{className:"flex flex-1 flex-col gap-4 p-4 pt-0",children:e})]})]})})}},67602:(e,t,s)=>{"use strict";s.d(t,{h:()=>l});var r=s(60687),a=s(87979),i=s(99891),n=s(16189);function l({children:e}){let{user:t,isAuthenticated:s,isLoading:l}=(0,a.A)(),c=(0,n.useRouter)();return l?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):s?t?.email?.includes("siift.ai")?(0,r.jsx)(r.Fragment,{children:e}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-4 text-destructive"}),(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"You need a siift.ai email address to access the admin panel."}),(0,r.jsx)("button",{onClick:()=>c.push("/user-dashboard"),className:"mt-4 text-primary hover:underline",children:"Go to Dashboard"})]})}):(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,r.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Authentication Required"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Please log in to access this page."})]})})}s(43210)},78335:()=>{},96487:()=>{},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,dynamic:()=>a,revalidate:()=>i});var r=s(37413);let a="force-dynamic",i=0;function n({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}}};