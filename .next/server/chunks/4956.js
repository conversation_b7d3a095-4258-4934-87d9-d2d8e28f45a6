"use strict";exports.id=4956,exports.ids=[4956],exports.modules={17561:(e,t,r)=>{r.d(t,{y4:()=>o,p8:()=>i,dc:()=>l});var n=Object.getOwnPropertyNames;let c=((e,t)=>function(){return t||(0,e[n(e)[0]])((t={exports:{}}).exports,t),t.exports})({"src/runtime/node/safe-node-apis.js"(e,t){let{existsSync:n,writeFileSync:c,readFileSync:s,appendFileSync:i,mkdirSync:l,rmSync:o}=r(73024);t.exports={fs:{existsSync:n,writeFileSync:c,readFileSync:s,appendFileSync:i,mkdirSync:l,rmSync:o},path:r(76760),cwd:()=>process.cwd()}}})(),s=e=>{throw Error(`Clerk: ${e} is missing. This is an internal error. Please contact Clerk's support.`)},i=()=>(c.fs||s("fs"),c.fs),l=()=>(c.path||s("path"),c.path),o=()=>(c.cwd||s("cwd"),c.cwd)},34956:(e,t,r)=>{r.r(t),r.d(t,{createOrReadKeyless:()=>g,removeKeyless:()=>m,safeParseClerkFile:()=>p});var n=r(15243),c=r(17561);let s=".clerk",i="clerk.lock",l=(...e)=>{let t=(0,c.dc)(),r=(0,c.y4)();return t.join(r(),s,...e)},o=".tmp",a=()=>l(o,"keyless.json"),u=()=>l(o,"README.md"),d=!1;function p(){let{readFileSync:e}=(0,c.p8)();try{let t,r=a();try{t=e(r,{encoding:"utf-8"})||"{}"}catch{t="{}"}return JSON.parse(t)}catch{return}}let f=()=>{let{writeFileSync:e}=(0,c.p8)();d=!0,e(i,"This file can be deleted. Please delete this file and refresh your application",{encoding:"utf8",mode:"0777",flag:"w"})},y=()=>{let{rmSync:e}=(0,c.p8)();try{e(i,{force:!0,recursive:!0})}catch{}d=!1},h=()=>{let{existsSync:e}=(0,c.p8)();return d||e(i)};async function g(){let{writeFileSync:e,mkdirSync:t}=(0,c.p8)();if(h())return null;f();let r=a(),i=u();t(l(o),{recursive:!0}),function(){let{existsSync:e,writeFileSync:t,readFileSync:r,appendFileSync:n}=(0,c.p8)(),i=(0,c.dc)(),l=(0,c.y4)(),o=i.join(l(),".gitignore");e(o)||t(o,""),r(o,"utf-8").includes(s+"/")||n(o,`
# clerk configuration (can include secrets)
/${s}/
`)}();let d=p();if((null==d?void 0:d.publishableKey)&&(null==d?void 0:d.secretKey))return y(),d;let g=(0,n.n)({}),m=await g.__experimental_accountlessApplications.createAccountlessApplication().catch(()=>null);return m&&(e(r,JSON.stringify(m),{encoding:"utf8",mode:"0777",flag:"w"}),e(i,`
## DO NOT COMMIT
This directory is auto-generated from \`@clerk/nextjs\` because you are running in Keyless mode. Avoid committing the \`.clerk/\` directory as it includes the secret key of the unclaimed instance.
  `,{encoding:"utf8",mode:"0777",flag:"w"})),y(),m}function m(){let{rmSync:e}=(0,c.p8)();if(!h()){f();try{e(l(),{force:!0,recursive:!0})}catch{}y()}}}};