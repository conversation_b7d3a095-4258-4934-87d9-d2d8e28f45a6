"use strict";exports.id=4181,exports.ids=[4181],exports.modules={363:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},11860:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12941:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},21134:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},26134:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>X});var n=r(43210),o=r(70569),a=r(98599),l=r(11273),i=r(96963),s=r(65551),d=r(31355),c=r(32547),u=r(25028),p=r(46059),f=r(14163),h=r(1359),g=r(42247),y=r(63376),m=r(8730),v=r(60687),k="Dialog",[x,j]=(0,l.A)(k),[D,b]=x(k),A=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:a??!1,onChange:l,caller:k});return(0,v.jsx)(D,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};A.displayName=k;var C="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=b(C,r),i=(0,a.s)(t,l.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":S(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})});R.displayName=C;var w="DialogPortal",[M,I]=x(w,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=b(w,t);return(0,v.jsx)(M,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(p.C,{present:r||l.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=w;var N="DialogOverlay",E=n.forwardRef((e,t)=>{let r=I(N,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=b(N,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:n||a.open,children:(0,v.jsx)(_,{...o,ref:t})}):null});E.displayName=N;var F=(0,m.TL)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=b(N,r);return(0,v.jsx)(g.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":S(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",q=n.forwardRef((e,t)=>{let r=I(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=b(P,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||a.open,children:a.modal?(0,v.jsx)(T,{...o,ref:t}):(0,v.jsx)(z,{...o,ref:t})})});q.displayName=P;var T=n.forwardRef((e,t)=>{let r=b(P,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,y.Eq)(e)},[]),(0,v.jsx)(B,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),z=n.forwardRef((e,t)=>{let r=b(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,u=b(P,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,h.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":S(u.open),...s,ref:f,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(K,{titleId:u.titleId}),(0,v.jsx)(Y,{contentRef:p,descriptionId:u.descriptionId})]})]})}),G="DialogTitle",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=b(G,r);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});$.displayName=G;var H="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=b(H,r);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=H;var Z="DialogClose",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=b(Z,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function S(e){return e?"open":"closed"}L.displayName=Z;var V="DialogTitleWarning",[U,J]=(0,l.q)(V,{contentName:P,titleName:G,docsSlug:"dialog"}),K=({titleId:e})=>{let t=J(V),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Y=({contentRef:e,descriptionId:t})=>{let r=J("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Q=A,X=R,ee=O,et=E,er=q,en=$,eo=W,ea=L},40083:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},58869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62157:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},72575:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},98876:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])}};