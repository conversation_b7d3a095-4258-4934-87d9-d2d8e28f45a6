"use strict";exports.id=6912,exports.ids=[6912],exports.modules={34729:(e,t,a)=>{a.d(t,{T:()=>n});var s=a(60687),r=a(43210),i=a(4780);let n=r.forwardRef(({className:e,...t},a)=>(0,s.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));n.displayName="Textarea"},36594:(e,t,a)=>{a.d(t,{T:()=>i});var s=a(60687),r=a(43210);function i({fadeEdge:e=80,dotSizes:t=[2],spacing:a=20,dotsPerRow:i=8,opacity:n=.3,darkOpacity:l=.4,lightColors:o=["888888"],darkColors:d=["AAAAAA"],className:c=""}){let u=e=>{let t=1e4*Math.sin(e);return t-Math.floor(t)},{dots:x,patternSize:m}=(0,r.useMemo)(()=>{let e=[],s=i*a;for(let s=0;s<i;s++)for(let r=0;r<i;r++){let i=r*a+a/2,n=s*a+a/2,l=1e3*s+r,c=Math.floor(u(l)*t.length),x=Math.floor(u(l+1)*o.length),m=Math.floor(u(l+2)*d.length),p=t[c],g=o[x],f=d[m];e.push({x:i,y:n,size:p,lightColor:g,darkColor:f})}return{dots:e,patternSize:s}},[i,a,t,o,d]),p=(e=!1)=>{let t=x.map((t,a)=>{let s=e?t.darkColor:t.lightColor;return`<circle cx='${t.x}' cy='${t.y}' r='${t.size}' fill='%23${s}'/>`}).join("");return`url("data:image/svg+xml,%3Csvg width='${m}' height='${m}' viewBox='0 0 ${m} ${m}' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E${t}%3C/g%3E%3C/svg%3E")`};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:`absolute inset-0 opacity-[${n}] dark:opacity-0 ${c}`,style:{backgroundImage:p(!1),maskImage:`
            linear-gradient(to right, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%)
          `,maskComposite:"intersect",WebkitMaskImage:`
            linear-gradient(to right, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%)
          `,WebkitMaskComposite:"source-in"}}),(0,s.jsx)("div",{className:`absolute inset-0 opacity-0 dark:opacity-[${l}] ${c}`,style:{backgroundImage:p(!0),maskImage:`
            linear-gradient(to right, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%)
          `,maskComposite:"intersect",WebkitMaskImage:`
            linear-gradient(to right, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black ${e}%, black ${100-e}%, transparent 100%)
          `,WebkitMaskComposite:"source-in"}})]})}},44493:(e,t,a)=>{a.d(t,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>c});var s=a(60687);a(43210);var r=a(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},56896:(e,t,a)=>{a.d(t,{S:()=>l});var s=a(60687);a(43210);var r=a(40211),i=a(13964),n=a(4780);function l({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[2px] border-2 transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(i.A,{className:"size-3.5 stroke-[3]"})})})}},89667:(e,t,a)=>{a.d(t,{p:()=>i});var s=a(60687);a(43210);var r=a(4780);function i({className:e,type:t,...a}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...a})}},96912:(e,t,a)=>{a.d(t,{C:()=>b});var s=a(60687),r=a(29523),i=a(44493),n=a(56896),l=a(89667),o=a(23166),d=a(34729),c=a(88920),u=a(92576),x=a(13964),m=a(47033),p=a(14952),g=a(43210),f=a(36594);function b({config:e,className:t=""}){let[a,b]=(0,g.useState)(0),[h,v]=(0,g.useState)({}),[k,j]=(0,g.useState)(!1),y=e.questions[a],N=a===e.questions.length-1,w=0===a,C=(e,t)=>{v(a=>({...a,[e]:t}))},$=e=>{C(y.id,e)},E=(e,t)=>{let a=h[y.id]||[];t?C(y.id,[...a,e]):C(y.id,a.filter(t=>t!==e))},S=e=>{C(y.id,e)};return y?(0,s.jsxs)("div",{className:`min-h-screen bg-background relative overflow-hidden ${t}`,children:[(0,s.jsx)(f.T,{fadeEdge:90,dotSizes:[1,1.5,2],spacing:25,dotsPerRow:6,opacity:.2,darkOpacity:.3,lightColors:["CCCCCC","BBBBBB","DDDDDD"],darkColors:["666666","777777","555555"]}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)("div",{className:"relative z-10 flex flex-col min-h-screen p-4",children:(0,s.jsx)("div",{className:"flex-1 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)(c.N,{mode:"wait",children:(0,s.jsx)(u.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},exit:{opacity:0,x:-20},transition:{duration:.3},children:(0,s.jsxs)(i.Zp,{className:"border",children:[(0,s.jsxs)(i.aR,{className:"text-center pb-3",children:[(0,s.jsx)(i.ZB,{className:"text-lg font-semibold",children:y.title}),y.subtitle&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:y.subtitle})]}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:["single"===y.type&&(0,s.jsx)("div",{className:"space-y-2",children:y.options?.map(e=>(0,s.jsx)(r.$,{variant:h[y.id]===e.value?"default":"outline",className:"w-full justify-start h-auto py-3 px-4 text-left",onClick:()=>$(e.value),children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsx)("span",{children:e.label}),h[y.id]===e.value&&(0,s.jsx)(x.A,{className:"w-4 h-4"})]})},e.value.toString()))}),"multiple"===y.type&&(0,s.jsx)("div",{className:"space-y-2",children:y.options?.map(e=>{let t=(h[y.id]||[]).includes(e.value);return(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer",onClick:()=>E(e.value,!t),children:[(0,s.jsx)(n.S,{checked:t,onCheckedChange:t=>E(e.value,t)}),(0,s.jsx)("span",{className:"flex-1",children:e.label})]},e.value.toString())})}),"text"===y.type&&(0,s.jsx)(l.p,{placeholder:y.placeholder||"Enter your answer...",value:h[y.id]||"",onChange:e=>S(e.target.value),className:"w-full",autoFocus:!0}),"textarea"===y.type&&(0,s.jsx)(d.T,{placeholder:y.placeholder||"Enter your answer...",value:h[y.id]||"",onChange:e=>S(e.target.value),className:"w-full min-h-[100px]",autoFocus:!0}),("single"!==y.type||N)&&(0,s.jsxs)("div",{className:"flex justify-between pt-4",children:[!N&&(0,s.jsxs)(r.$,{variant:"outline",onClick:()=>{if(!w){let t=a-1;b(t),e.onStepChange?.(t,h)}},disabled:w,className:"flex items-center gap-2",size:"sm",children:[(0,s.jsx)(m.A,{className:"w-4 h-4"}),"Previous"]}),(0,s.jsxs)(r.$,{onClick:()=>{if(N)e.onComplete(h);else{let t=a+1;b(t),e.onStepChange?.(t,h)}},disabled:!k,className:`flex items-center gap-2 bg-[#166534] hover:bg-[#166534]/90 ${N?"w-full":"ml-auto"}`,size:"sm",children:[N?"Complete":"Next",!N&&(0,s.jsx)(p.A,{className:"w-4 h-4"})]})]})]})]})},a)}),(0,s.jsxs)("div",{className:"mt-8 space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Step ",a+1," of ",e.questions.length]}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:[Math.round((a+1)/e.questions.length*100),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-muted rounded-full h-1",children:(0,s.jsx)(u.P.div,{className:"bg-[#166534] h-1 rounded-full",initial:{width:0},animate:{width:`${(a+1)/e.questions.length*100}%`},transition:{duration:.3}})})]}),(0,s.jsx)("div",{className:"flex justify-center mt-6",children:(0,s.jsx)(o.g,{size:24,textSize:14,animated:!0,showText:!0,className:"opacity-60"})})]})})})]}):null}}};