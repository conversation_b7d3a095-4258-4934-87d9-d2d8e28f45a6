"use strict";exports.id=7359,exports.ids=[7359],exports.modules={9989:(e,t,r)=>{r.d(t,{Kq:()=>V,UC:()=>W,ZL:()=>U,bL:()=>Z,i3:()=>X,l9:()=>K});var n=r(43210),o=r(70569),a=r(98599),i=r(11273),l=r(31355),s=r(96963),d=r(55509),c=r(25028),u=r(46059),p=r(14163),h=r(8730),f=r(65551),y=r(69024),x=r(60687),[v,g]=(0,i.A)("Tooltip",[d.Bk]),m=(0,d.Bk)(),b="TooltipProvider",w="tooltip.open",[C,k]=v(b),T=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:i}=e,l=n.useRef(!0),s=n.useRef(!1),d=n.useRef(0);return n.useEffect(()=>{let e=d.current;return()=>window.clearTimeout(e)},[]),(0,x.jsx)(C,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(d.current),l.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>l.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:a,children:i})};T.displayName=b;var R="Tooltip",[j,E]=v(R),M=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:a,onOpenChange:i,disableHoverableContent:l,delayDuration:c}=e,u=k(R,e.__scopeTooltip),p=m(t),[h,y]=n.useState(null),v=(0,s.B)(),g=n.useRef(0),b=l??u.disableHoverableContent,C=c??u.delayDuration,T=n.useRef(!1),[E,M]=(0,f.i)({prop:o,defaultProp:a??!1,onChange:e=>{e?(u.onOpen(),document.dispatchEvent(new CustomEvent(w))):u.onClose(),i?.(e)},caller:R}),L=n.useMemo(()=>E?T.current?"delayed-open":"instant-open":"closed",[E]),A=n.useCallback(()=>{window.clearTimeout(g.current),g.current=0,T.current=!1,M(!0)},[M]),N=n.useCallback(()=>{window.clearTimeout(g.current),g.current=0,M(!1)},[M]),D=n.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{T.current=!0,M(!0),g.current=0},C)},[C,M]);return n.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),(0,x.jsx)(d.bL,{...p,children:(0,x.jsx)(j,{scope:t,contentId:v,open:E,stateAttribute:L,trigger:h,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{u.isOpenDelayedRef.current?D():A()},[u.isOpenDelayedRef,D,A]),onTriggerLeave:n.useCallback(()=>{b?N():(window.clearTimeout(g.current),g.current=0)},[N,b]),onOpen:A,onClose:N,disableHoverableContent:b,children:r})})};M.displayName=R;var L="TooltipTrigger",A=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,l=E(L,r),s=k(L,r),c=m(r),u=n.useRef(null),h=(0,a.s)(t,u,l.onTriggerChange),f=n.useRef(!1),y=n.useRef(!1),v=n.useCallback(()=>f.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",v),[v]),(0,x.jsx)(d.Mz,{asChild:!0,...c,children:(0,x.jsx)(p.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...i,ref:h,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{l.open&&l.onClose(),f.current=!0,document.addEventListener("pointerup",v,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{f.current||l.onOpen()}),onBlur:(0,o.m)(e.onBlur,l.onClose),onClick:(0,o.m)(e.onClick,l.onClose)})})});A.displayName=L;var N="TooltipPortal",[D,P]=v(N,{forceMount:void 0}),_=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,a=E(N,t);return(0,x.jsx)(D,{scope:t,forceMount:r,children:(0,x.jsx)(u.C,{present:r||a.open,children:(0,x.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};_.displayName=N;var O="TooltipContent",I=n.forwardRef((e,t)=>{let r=P(O,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,i=E(O,e.__scopeTooltip);return(0,x.jsx)(u.C,{present:n||i.open,children:i.disableHoverableContent?(0,x.jsx)(F,{side:o,...a,ref:t}):(0,x.jsx)(B,{side:o,...a,ref:t})})}),B=n.forwardRef((e,t)=>{let r=E(O,e.__scopeTooltip),o=k(O,e.__scopeTooltip),i=n.useRef(null),l=(0,a.s)(t,i),[s,d]=n.useState(null),{trigger:c,onClose:u}=r,p=i.current,{onPointerInTransitChange:h}=o,f=n.useCallback(()=>{d(null),h(!1)},[h]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t,r=5){let n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),h(!0)},[h]);return n.useEffect(()=>()=>f(),[f]),n.useEffect(()=>{if(c&&p){let e=e=>y(e,p),t=e=>y(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,y,f]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=c?.contains(t)||p?.contains(t),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let i=t[e],l=t[a],s=i.x,d=i.y,c=l.x,u=l.y;d>n!=u>n&&r<(c-s)*(n-d)/(u-d)+s&&(o=!o)}return o}(r,s);n?f():o&&(f(),u())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,u,f]),(0,x.jsx)(F,{...e,ref:l})}),[H,q]=v(R,{isInside:!1}),z=(0,h.Dc)("TooltipContent"),F=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,u=E(O,r),p=m(r),{onClose:h}=u;return n.useEffect(()=>(document.addEventListener(w,h),()=>document.removeEventListener(w,h)),[h]),n.useEffect(()=>{if(u.trigger){let e=e=>{let t=e.target;t?.contains(u.trigger)&&h()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[u.trigger,h]),(0,x.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:h,children:(0,x.jsxs)(d.UC,{"data-state":u.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,x.jsx)(z,{children:o}),(0,x.jsx)(H,{scope:r,isInside:!0,children:(0,x.jsx)(y.bL,{id:u.contentId,role:"tooltip",children:a||o})})]})})});I.displayName=O;var G="TooltipArrow",S=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=m(r);return q(G,r).isInside?null:(0,x.jsx)(d.i3,{...o,...n,ref:t})});S.displayName=G;var V=T,Z=M,K=A,U=_,W=I,X=S},10022:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},41312:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},48730:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},51214:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},53411:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},69024:(e,t,r)=>{r.d(t,{Qg:()=>i,bL:()=>s});var n=r(43210),o=r(14163),a=r(60687),i=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),l=n.forwardRef((e,t)=>(0,a.jsx)(o.sG.span,{...e,ref:t,style:{...i,...e.style}}));l.displayName="VisuallyHidden";var s=l},95682:(e,t,r)=>{r.d(t,{Ke:()=>C,R6:()=>b,bL:()=>R});var n=r(43210),o=r(70569),a=r(11273),i=r(65551),l=r(66156),s=r(98599),d=r(14163),c=r(46059),u=r(96963),p=r(60687),h="Collapsible",[f,y]=(0,a.A)(h),[x,v]=f(h),g=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,open:o,defaultOpen:a,disabled:l,onOpenChange:s,...c}=e,[f,y]=(0,i.i)({prop:o,defaultProp:a??!1,onChange:s,caller:h});return(0,p.jsx)(x,{scope:r,disabled:l,contentId:(0,u.B)(),open:f,onOpenToggle:n.useCallback(()=>y(e=>!e),[y]),children:(0,p.jsx)(d.sG.div,{"data-state":T(f),"data-disabled":l?"":void 0,...c,ref:t})})});g.displayName=h;var m="CollapsibleTrigger",b=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,...n}=e,a=v(m,r);return(0,p.jsx)(d.sG.button,{type:"button","aria-controls":a.contentId,"aria-expanded":a.open||!1,"data-state":T(a.open),"data-disabled":a.disabled?"":void 0,disabled:a.disabled,...n,ref:t,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});b.displayName=m;var w="CollapsibleContent",C=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=v(w,e.__scopeCollapsible);return(0,p.jsx)(c.C,{present:r||o.open,children:({present:e})=>(0,p.jsx)(k,{...n,ref:t,present:e})})});C.displayName=w;var k=n.forwardRef((e,t)=>{let{__scopeCollapsible:r,present:o,children:a,...i}=e,c=v(w,r),[u,h]=n.useState(o),f=n.useRef(null),y=(0,s.s)(t,f),x=n.useRef(0),g=x.current,m=n.useRef(0),b=m.current,C=c.open||u,k=n.useRef(C),R=n.useRef(void 0);return n.useEffect(()=>{let e=requestAnimationFrame(()=>k.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,l.N)(()=>{let e=f.current;if(e){R.current=R.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();x.current=t.height,m.current=t.width,k.current||(e.style.transitionDuration=R.current.transitionDuration,e.style.animationName=R.current.animationName),h(o)}},[c.open,o]),(0,p.jsx)(d.sG.div,{"data-state":T(c.open),"data-disabled":c.disabled?"":void 0,id:c.contentId,hidden:!C,...i,ref:y,style:{"--radix-collapsible-content-height":g?`${g}px`:void 0,"--radix-collapsible-content-width":b?`${b}px`:void 0,...e.style},children:C&&a})});function T(e){return e?"open":"closed"}var R=g},96474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};