exports.id=6852,exports.ids=[6852],exports.modules={4536:(e,t,r)=>{let{createProxy:o}=r(39844);e.exports=o("/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/app-dir/link.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12941:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},26134:(e,t,r)=>{"use strict";r.d(t,{UC:()=>er,VY:()=>en,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>eo,hJ:()=>et,l9:()=>X});var o=r(43210),n=r(70569),a=r(98599),s=r(11273),i=r(96963),l=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),h=r(63376),y=r(8730),v=r(60687),x="Dialog",[w,D]=(0,s.A)(x),[j,C]=w(x),b=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:s,modal:d=!0}=e,u=o.useRef(null),c=o.useRef(null),[p,f]=(0,l.i)({prop:n,defaultProp:a??!1,onChange:s,caller:x});return(0,v.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};b.displayName=x;var k="DialogTrigger",R=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,s=C(k,r),i=(0,a.s)(t,s.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":S(s.open),...o,ref:i,onClick:(0,n.m)(e.onClick,s.onOpenToggle)})});R.displayName=k;var A="DialogPortal",[N,I]=w(A,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,s=C(A,t);return(0,v.jsx)(N,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,v.jsx)(p.C,{present:r||s.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=A;var E="DialogOverlay",_=o.forwardRef((e,t)=>{let r=I(E,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=C(E,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:o||a.open,children:(0,v.jsx)(M,{...n,ref:t})}):null});_.displayName=E;var F=(0,y.TL)("DialogOverlay.RemoveScroll"),M=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=C(E,r);return(0,v.jsx)(m.A,{as:F,allowPinchZoom:!0,shards:[n.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":S(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),P="DialogContent",$=o.forwardRef((e,t)=>{let r=I(P,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=C(P,e.__scopeDialog);return(0,v.jsx)(p.C,{present:o||a.open,children:a.modal?(0,v.jsx)(L,{...n,ref:t}):(0,v.jsx)(W,{...n,ref:t})})});$.displayName=P;var L=o.forwardRef((e,t)=>{let r=C(P,e.__scopeDialog),s=o.useRef(null),i=(0,a.s)(t,r.contentRef,s);return o.useEffect(()=>{let e=s.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(B,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),W=o.forwardRef((e,t)=>{let r=C(P,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,v.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:s,onCloseAutoFocus:i,...l}=e,c=C(P,r),p=o.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:s,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":S(c.open),...l,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(K,{titleId:c.titleId}),(0,v.jsx)(Y,{contentRef:p,descriptionId:c.descriptionId})]})]})}),T="DialogTitle",G=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=C(T,r);return(0,v.jsx)(f.sG.h2,{id:n.titleId,...o,ref:t})});G.displayName=T;var U="DialogDescription",Z=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=C(U,r);return(0,v.jsx)(f.sG.p,{id:n.descriptionId,...o,ref:t})});Z.displayName=U;var q="DialogClose",H=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,a=C(q,r);return(0,v.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})});function S(e){return e?"open":"closed"}H.displayName=q;var V="DialogTitleWarning",[z,J]=(0,s.q)(V,{contentName:P,titleName:T,docsSlug:"dialog"}),K=({titleId:e})=>{let t=J(V),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Y=({contentRef:e,descriptionId:t})=>{let r=J("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(n))},[n,e,t]),null},Q=b,X=R,ee=O,et=_,er=$,eo=G,en=Z,ea=H},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(61120);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),s=e=>{let t=a(e);return t.charAt(0).toUpperCase()+t.slice(1)},i=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,o.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:n,className:a="",children:s,iconNode:u,...c},p)=>(0,o.createElement)("svg",{ref:p,...d,width:t,height:t,stroke:e,strokeWidth:n?24*Number(r)/Number(t):r,className:i("lucide",a),...!s&&!l(c)&&{"aria-hidden":"true"},...c},[...u.map(([e,t])=>(0,o.createElement)(e,t)),...Array.isArray(s)?s:[s]])),c=(e,t)=>{let r=(0,o.forwardRef)(({className:r,...a},l)=>(0,o.createElement)(u,{ref:l,iconNode:t,className:i(`lucide-${n(s(e))}`,`lucide-${e}`,r),...a}));return r.displayName=s(e),r}},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])}};