"use strict";exports.id=8562,exports.ids=[8562],exports.modules={11096:(e,r,t)=>{t.d(r,{H4:()=>b,_V:()=>R,bL:()=>j});var n=t(43210),o=t(11273),a=t(13495),i=t(66156),l=t(14163),s=t(57379);function d(){return()=>{}}var u=t(60687),c="Avatar",[f,p]=(0,o.A)(c),[g,m]=f(c),v=n.forwardRef((e,r)=>{let{__scopeAvatar:t,...o}=e,[a,i]=n.useState("idle");return(0,u.jsx)(g,{scope:t,imageLoadingStatus:a,onImageLoadingStatusChange:i,children:(0,u.jsx)(l.sG.span,{...o,ref:r})})});v.displayName=c;var y="AvatarImage",h=n.forwardRef((e,r)=>{let{__scopeAvatar:t,src:o,onLoadingStatusChange:c=()=>{},...f}=e,p=m(y,t),g=function(e,{referrerPolicy:r,crossOrigin:t}){let o=(0,s.useSyncExternalStore)(d,()=>!0,()=>!1),a=n.useRef(null),l=o?(a.current||(a.current=new window.Image),a.current):null,[u,c]=n.useState(()=>D(l,e));return(0,i.N)(()=>{c(D(l,e))},[l,e]),(0,i.N)(()=>{let e=e=>()=>{c(e)};if(!l)return;let n=e("loaded"),o=e("error");return l.addEventListener("load",n),l.addEventListener("error",o),r&&(l.referrerPolicy=r),"string"==typeof t&&(l.crossOrigin=t),()=>{l.removeEventListener("load",n),l.removeEventListener("error",o)}},[l,t,r]),u}(o,f),v=(0,a.c)(e=>{c(e),p.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==g&&v(g)},[g,v]),"loaded"===g?(0,u.jsx)(l.sG.img,{...f,ref:r,src:o}):null});h.displayName=y;var x="AvatarFallback",w=n.forwardRef((e,r)=>{let{__scopeAvatar:t,delayMs:o,...a}=e,i=m(x,t),[s,d]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>d(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==i.imageLoadingStatus?(0,u.jsx)(l.sG.span,{...a,ref:r}):null});function D(e,r){return e?r?(e.src!==r&&(e.src=r),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=x;var j=v,R=h,b=w},11860:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},26134:(e,r,t)=>{t.d(r,{UC:()=>et,VY:()=>eo,ZL:()=>ee,bL:()=>Q,bm:()=>ea,hE:()=>en,hJ:()=>er,l9:()=>X});var n=t(43210),o=t(70569),a=t(98599),i=t(11273),l=t(96963),s=t(65551),d=t(31355),u=t(32547),c=t(25028),f=t(46059),p=t(14163),g=t(1359),m=t(42247),v=t(63376),y=t(8730),h=t(60687),x="Dialog",[w,D]=(0,i.A)(x),[j,R]=w(x),b=e=>{let{__scopeDialog:r,children:t,open:o,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:a??!1,onChange:i,caller:x});return(0,h.jsx)(j,{scope:r,triggerRef:u,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:t})};b.displayName=x;var C="DialogTrigger",I=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,i=R(C,t),l=(0,a.s)(r,i.triggerRef);return(0,h.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Z(i.open),...n,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});I.displayName=C;var N="DialogPortal",[E,A]=w(N,{forceMount:void 0}),O=e=>{let{__scopeDialog:r,forceMount:t,children:o,container:a}=e,i=R(N,r);return(0,h.jsx)(E,{scope:r,forceMount:t,children:n.Children.map(o,e=>(0,h.jsx)(f.C,{present:t||i.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=N;var F="DialogOverlay",_=n.forwardRef((e,r)=>{let t=A(F,e.__scopeDialog),{forceMount:n=t.forceMount,...o}=e,a=R(F,e.__scopeDialog);return a.modal?(0,h.jsx)(f.C,{present:n||a.open,children:(0,h.jsx)(S,{...o,ref:r})}):null});_.displayName=F;var k=(0,y.TL)("DialogOverlay.RemoveScroll"),S=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,o=R(F,t);return(0,h.jsx)(m.A,{as:k,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(p.sG.div,{"data-state":Z(o.open),...n,ref:r,style:{pointerEvents:"auto",...n.style}})})}),L="DialogContent",P=n.forwardRef((e,r)=>{let t=A(L,e.__scopeDialog),{forceMount:n=t.forceMount,...o}=e,a=R(L,e.__scopeDialog);return(0,h.jsx)(f.C,{present:n||a.open,children:a.modal?(0,h.jsx)(G,{...o,ref:r}):(0,h.jsx)(T,{...o,ref:r})})});P.displayName=L;var G=n.forwardRef((e,r)=>{let t=R(L,e.__scopeDialog),i=n.useRef(null),l=(0,a.s)(r,t.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,h.jsx)(M,{...e,ref:l,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;(2===r.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,r)=>{let t=R(L,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,h.jsx)(M,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{e.onCloseAutoFocus?.(r),r.defaultPrevented||(o.current||t.triggerRef.current?.focus(),r.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:r=>{e.onInteractOutside?.(r),r.defaultPrevented||(o.current=!0,"pointerdown"===r.detail.originalEvent.type&&(a.current=!0));let n=r.target;t.triggerRef.current?.contains(n)&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&a.current&&r.preventDefault()}})}),M=n.forwardRef((e,r)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,c=R(L,t),f=n.useRef(null),p=(0,a.s)(r,f);return(0,g.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,h.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(K,{titleId:c.titleId}),(0,h.jsx)(Y,{contentRef:f,descriptionId:c.descriptionId})]})]})}),B="DialogTitle",W=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,o=R(B,t);return(0,h.jsx)(p.sG.h2,{id:o.titleId,...n,ref:r})});W.displayName=B;var $="DialogDescription",q=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,o=R($,t);return(0,h.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:r})});q.displayName=$;var H="DialogClose",V=n.forwardRef((e,r)=>{let{__scopeDialog:t,...n}=e,a=R(H,t);return(0,h.jsx)(p.sG.button,{type:"button",...n,ref:r,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}V.displayName=H;var z="DialogTitleWarning",[U,J]=(0,i.q)(z,{contentName:L,titleName:B,docsSlug:"dialog"}),K=({titleId:e})=>{let r=J(z),t=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(t))},[t,e]),null},Y=({contentRef:e,descriptionId:r})=>{let t=J("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return n.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");r&&t&&(document.getElementById(r)||console.warn(o))},[o,e,r]),null},Q=b,X=I,ee=O,er=_,et=P,en=W,eo=q,ea=V},58869:(e,r,t)=>{t.d(r,{A:()=>n});let n=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62369:(e,r,t)=>{t.d(r,{b:()=>d});var n=t(43210),o=t(14163),a=t(60687),i="horizontal",l=["horizontal","vertical"],s=n.forwardRef((e,r)=>{var t;let{decorative:n,orientation:s=i,...d}=e,u=(t=s,l.includes(t))?s:i;return(0,a.jsx)(o.sG.div,{"data-orientation":u,...n?{role:"none"}:{"aria-orientation":"vertical"===u?u:void 0,role:"separator"},...d,ref:r})});s.displayName="Separator";var d=s}};