"use strict";exports.id=3966,exports.ids=[3966],exports.modules={71494:(e,l,s)=>{s.d(l,{a:()=>a});var t=s(6475);let a=(0,t.createServerReference)("7fb4440f3f5270c51037d546ce3fc826912f39a08a",t.callServer,void 0,t.findSourceMapURL,"createOrReadKeylessAction")},93966:(e,l,s)=>{s.r(l),s.d(l,{KeylessCreatorOrReader:()=>n});var t=s(16189),a=s(43210),r=s.n(a),i=s(71494);let n=e=>{var l;let{children:s}=e,n=(null==(l=(0,t.useSelectedLayoutSegments)()[0])?void 0:l.startsWith("/_not-found"))||!1,[o,c]=r().useActionState(i.a,null);return((0,a.useEffect)(()=>{n||r().startTransition(()=>{c()})},[n]),r().isValidElement(s))?r().cloneElement(s,{key:null==o?void 0:o.publishableKey,publishableKey:null==o?void 0:o.publishableKey,__internal_keyless_claimKeylessApplicationUrl:null==o?void 0:o.claimUrl,__internal_keyless_copyInstanceKeysUrl:null==o?void 0:o.apiKeysUrl,__internal_bypassMissingPublishableKey:!0}):s}}};