exports.id=1529,exports.ids=[1529],exports.modules={175:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},1640:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},1706:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},3785:(e,t,r)=>{"use strict";r.d(t,{x:()=>c});var n,i=r(43210),a=r(22989),o=null!=(n=i["useId".toString()])?n:()=>{var[e]=i.useState(()=>(0,a.NF)("uid-"));return e},l=(0,i.createContext)(void 0),c=e=>{var{id:t,type:r,children:n}=e,a=function(e,t){var r=o();return t||(e?"".concat(e,"-").concat(r):r)}("recharts-".concat(r),t);return i.createElement(l.Provider,{value:a},n(a))}},4057:(e,t,r)=>{"use strict";r.d(t,{VU:()=>o,XC:()=>u,_U:()=>l});var n=r(43210),i=r(77400),a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var a={};return Object.keys(r).forEach(e=>{(0,i.q)(e)&&(a[e]=t||(t=>r[e](r,t)))}),a},c=(e,t,r)=>n=>(e(t,r,n),null),u=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(a=>{var o=e[a];(0,i.q)(a)&&"function"==typeof o&&(n||(n={}),n[a]=c(o,t,r))}),n}},5338:(e,t,r)=>{"use strict";r.d(t,{CA:()=>y,MC:()=>u,QG:()=>p,Vi:()=>c,cU:()=>s,fR:()=>f});var n=r(76067),i=r(71392);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:c,removeXAxis:u,addYAxis:s,removeYAxis:f,addZAxis:h,removeZAxis:d,updateYAxisWidth:p}=l.actions,y=l.reducer},5664:(e,t,r)=>{e.exports=r(87509).get},6548:(e,t,r)=>{"use strict";function n(e){return null==e?void 0:e.id}r.d(t,{x:()=>n})},8920:(e,t,r)=>{"use strict";r.d(t,{Be:()=>v,Cv:()=>O,D0:()=>M,Gl:()=>g,Dc:()=>j});var n=r(84648),i=r(86445),a=r(35034),o=r(19335),l=r(22989),c={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},u={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(53416),f=r(51426),h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:c.reversed,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:c.type,unit:void 0},d={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:u.type,unit:void 0},p={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:u.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:u.tickCount,ticks:void 0,type:"category",unit:void 0},v=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?p:h,g=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:d,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.HZ],o.lY),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([v,O],s.I);var j=(0,n.Mz)([b,x,w],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([g,j],s.I);var M=(0,n.Mz)([f.fz,m,x,w,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:c,startAngle:u,endAngle:s}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(c,a,a/2),innerRadius:r,outerRadius:n,startAngle:u,endAngle:s,clockWise:!1}}})},9474:(e,t,r)=>{e.exports=r(33731).last},10521:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},10687:(e,t,r)=>{e.exports=r(75446).sortBy},10907:(e,t,r)=>{"use strict";var n=r(43210),i=r(57379),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=i.useSyncExternalStore,l=n.useRef,c=n.useEffect,u=n.useMemo,s=n.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,r,n,i){var f=l(null);if(null===f.current){var h={hasValue:!1,value:null};f.current=h}else h=f.current;var d=o(e,(f=u(function(){function e(e){if(!c){if(c=!0,o=e,e=n(e),void 0!==i&&h.hasValue){var t=h.value;if(i(t,e))return l=t}return l=e}if(t=l,a(o,e))return t;var r=n(e);return void 0!==i&&i(t,r)?(o=e,t):(o=e,l=r)}var o,l,c=!1,u=void 0===r?null:r;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]},[t,r,n,i]))[0],f[1]);return c(function(){h.hasValue=!0,h.value=d},[d]),s(d),d}},11117:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),c=r?r+t:t;return e._events[c]?e._events[c].fn?e._events[c]=[e._events[c],l]:e._events[c].push(l):(e._events[c]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var c,u,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(u=1,c=Array(f-1);u<f;u++)c[u-1]=arguments[u];s.fn.apply(s.context,c)}else{var h,d=s.length;for(u=0;u<d;u++)switch(s[u].once&&this.removeListener(e,s[u].fn,void 0,!0),f){case 1:s[u].fn.call(s[u].context);break;case 2:s[u].fn.call(s[u].context,t);break;case 3:s[u].fn.call(s[u].context,t,n);break;case 4:s[u].fn.call(s[u].context,t,n,i);break;default:if(!c)for(h=1,c=Array(f-1);h<f;h++)c[h-1]=arguments[h];s[u].fn.apply(s[u].context,c)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var c=0,u=[],s=l.length;c<s;c++)(l[c].fn!==t||i&&!l[c].once||n&&l[c].context!==n)&&u.push(l[c]);u.length?this._events[a]=1===u.length?u[0]:u:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l},11281:(e,t,r)=>{"use strict";r.d(t,{$7:()=>f,Ru:()=>s,uZ:()=>u});var n=r(76067),i=r(17118),a=r(69009),o=r(21426),l=r(85621),c=r(97371),u=(0,n.VP)("keyDown"),s=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:u,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,u=e.payload;if("ArrowRight"===u||"ArrowLeft"===u||"Enter"===u){var s=Number((0,c.P)(n,(0,a.n4)(r))),f=(0,a.R4)(r);if("Enter"===u){var h=(0,o.pg)(r,"axis","hover",String(n.index));t.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:h}));return}var d=s+("ArrowRight"===u?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(d>=f.length)&&!(d<0)){var p=(0,o.pg)(r,"axis","hover",String(d));t.dispatch((0,i.o4)({active:!0,activeIndex:d.toString(),activeDataKey:void 0,activeCoordinate:p}))}}}}}),f.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},12128:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},12728:(e,t,r)=>{e.exports=r(92292).isEqual},13420:(e,t,r)=>{"use strict";r.d(t,{TK:()=>l});var n=r(43210),i=r(64267),a=r(43209),o=r(83409),l=e=>{var{chartData:t}=e,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}},14454:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(1640),a=r(23457),o=r(1706);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return c(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return u(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function c(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let c=0;c<e.length;c++){if(i.has(c))continue;let u=e[c],s=!1;if(r(u,o,a,e,t,n)&&(s=!0),s){i.add(c),l=!0;break}}if(!l)return!1}return!0}function u(e,t,r,n){return 0===t.size||e instanceof Set&&c([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,c){let u=r(t,n,i,a,o,c);return void 0!==u?!!u:l(t,n,e,c)},new Map)},t.isSetMatch=u},14952:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},14956:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,_:()=>l}),r(43210);var n=r(83409),i=r(51426),a=r(43209);function o(e){var{legendPayload:t}=e;return(0,a.j)(),(0,n.r)(),null}function l(e){var{legendPayload:t}=e;return(0,a.j)(),(0,a.G)(i.fz),null}r(53044)},15606:(e,t,r)=>{"use strict";r.d(t,{i:()=>c});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e*=1,t*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,c=r-e,u=i-t,s=o-e,f=l-t,h=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(h>1e-6)if(Math.abs(f*c-u*s)>1e-6&&a){let d=r-o,p=i-l,y=c*c+u*u,v=Math.sqrt(y),g=Math.sqrt(h),m=a*Math.tan((n-Math.acos((y+h-(d*d+p*p))/(2*v*g)))/2),b=m/g,x=m/v;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*d>s*p)},${this._x1=e+x*c},${this._y1=t+x*u}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,o,l,c){if(e*=1,t*=1,r*=1,c=!!c,r<0)throw Error(`negative radius: ${r}`);let u=r*Math.cos(o),s=r*Math.sin(o),f=e+u,h=t+s,d=1^c,p=c?o-l:l-o;null===this._x1?this._append`M${f},${h}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-h)>1e-6)&&this._append`L${f},${h}`,r&&(p<0&&(p=p%i+i),p>a?this._append`A${r},${r},0,1,${d},${e-u},${t-s}A${r},${r},0,1,${d},${this._x1=f},${this._y1=h}`:p>1e-6&&this._append`A${r},${r},0,${+(p>=n)},${d},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function c(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},15708:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(95819);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},16950:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var n=r(51426),i=e=>{var t=(0,n.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"}},17118:(e,t,r)=>{"use strict";r.d(t,{E1:()=>v,En:()=>m,Ix:()=>l,ML:()=>d,Nt:()=>p,RD:()=>s,UF:()=>u,XB:()=>c,jF:()=>y,k_:()=>a,o4:()=>g,oP:()=>f,xS:()=>h});var n=r(76067),i=r(71392),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:c,setTooltipSettingsState:u,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:h,setActiveClickItemIndex:d,setMouseOverAxisIndex:p,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},17458:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("file-chart-column-increasing",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 18v-2",key:"qcmpov"}],["path",{d:"M12 18v-4",key:"q1q25u"}],["path",{d:"M16 18v-6",key:"15y0np"}]])},17617:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(92681),i=r(40144),a=r(74838),o=r(30415);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},17874:(e,t,r)=>{"use strict";r.d(t,{f:()=>p});var n=r(22989),i=r(45093),a=r(20237);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class c{static create(e){return new c(e)}constructor(e){this.scale=e}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}}l(c,"EPS",1e-4);var u=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function s(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function f(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p(e,t,r){var o,{tick:l,ticks:c,viewBox:h,minTickGap:p,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=e;if(!c||!c.length||!l)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(o=s(c,((0,n.Et)(v)?v:0)+1))?o:[];var x=[],w="top"===y||"bottom"===y?"width":"height",O=m&&"width"===w?(0,i.Pu)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},j=(e,n)=>{var a,o="function"==typeof g?g(e.value,n):e.value;return"width"===w?(a=(0,i.Pu)(o,{fontSize:t,letterSpacing:r}),u({width:a.width+O.width,height:a.height+O.height},b)):(0,i.Pu)(o,{fontSize:t,letterSpacing:r})[w]},M=c.length>=2?(0,n.sA)(c[1].coordinate-c[0].coordinate):1,P=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(h,M,w);return"equidistantPreserveStart"===v?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:c}=t,u=0,h=1,d=l;h<=o.length;)if(a=function(){var t,a=null==n?void 0:n[u];if(void 0===a)return{v:s(n,h)};var o=u,p=()=>(void 0===t&&(t=r(a,o)),t),y=a.coordinate,v=0===u||f(e,y,p,d,c);v||(u=0,d=l,h+=1),v&&(d=y+e*(p()/2+i),u+=h)}())return a.v;return[]}(M,P,j,c,p):("preserveStart"===v||"preserveStartEnd"===v?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:c,end:u}=t;if(a){var s=n[l-1],h=r(s,l-1),p=e*(s.coordinate+e*h/2-u);o[l-1]=s=d(d({},s),{},{tickCoord:p>0?s.coordinate-p*e:s.coordinate}),f(e,s.tickCoord,()=>h,c,u)&&(u=s.tickCoord-e*(h/2+i),o[l-1]=d(d({},s),{},{isShow:!0}))}for(var y=a?l-1:l,v=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var s=e*(a.coordinate-e*l()/2-c);o[t]=a=d(d({},a),{},{tickCoord:s<0?a.coordinate-s*e:a.coordinate})}else o[t]=a=d(d({},a),{},{tickCoord:a.coordinate});f(e,a.tickCoord,l,c,u)&&(c=a.tickCoord+e*(l()/2+i),o[t]=d(d({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(M,P,j,c,p,"preserveStartEnd"===v):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:c}=t,u=function(t){var n,u=a[t],s=()=>(void 0===n&&(n=r(u,t)),n);if(t===o-1){var h=e*(u.coordinate+e*s()/2-c);a[t]=u=d(d({},u),{},{tickCoord:h>0?u.coordinate-h*e:u.coordinate})}else a[t]=u=d(d({},u),{},{tickCoord:u.coordinate});f(e,u.tickCoord,s,l,c)&&(c=u.tickCoord-e*(s()/2+i),a[t]=d(d({},u),{},{isShow:!0}))},s=o-1;s>=0;s--)u(s);return a}(M,P,j,c,p)).filter(e=>e.isShow)}},17971:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},18179:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},19335:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>l,Kg:()=>a,lY:()=>c,yy:()=>d}),r(43210);var a=Math.PI/180,o=e=>180*e/Math.PI,l=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),c=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},u=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},s=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,l=u({x:r,y:n},{x:i,y:a});if(l<=0)return{radius:l,angle:0};var c=Math.acos((r-i)/l);return n>a&&(c=2*Math.PI-c),{radius:l,angle:o(c),angleInRadian:c}},f=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},h=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},d=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:l}=s({x:n,y:a},t),{innerRadius:c,outerRadius:u}=t;if(o<c||o>u||0===o)return null;var{startAngle:d,endAngle:p}=f(t),y=l;if(d<=p){for(;y>p;)y-=360;for(;y<d;)y+=360;r=y>=d&&y<=p}else{for(;y>d;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=d}return r?i(i({},t),{},{radius:o,angle:h(y,t)}):null}},19598:(e,t,r)=>{"use strict";r.d(t,{h:()=>x});var n=r(43210),i=r(49384),a=r(71579),o=r(5338),l=r(43209),c=r(85621),u=r(35034),s=r(83409),f=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},h=r(97633),d=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e){return(0,l.j)(),null}var g=e=>{var t,{yAxisId:r,className:p,width:v,label:g}=e,m=(0,n.useRef)(null),b=(0,n.useRef)(null),x=(0,l.G)(u.c2),w=(0,s.r)(),O=(0,l.j)(),j="yAxis",M=(0,l.G)(e=>(0,c.iV)(e,j,r,w)),P=(0,l.G)(e=>(0,c.wP)(e,r)),A=(0,l.G)(e=>(0,c.KR)(e,r)),S=(0,l.G)(e=>(0,c.Zi)(e,j,r,w));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==v||!P||(0,h.Z)(g)||(0,n.isValidElement)(g))){var e,t=m.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:l}=t.props,c=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(P.width)!==Math.round(c)&&O((0,o.QG)({id:r,width:c}))}},[m,null==m||null==(t=m.current)||null==(t=t.tickRefs)?void 0:t.current,null==P?void 0:P.width,P,O,g,r,v]),null==P||null==A)return null;var{dangerouslySetInnerHTML:E,ticks:k}=e,_=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,d);return n.createElement(a.u,y({},_,{ref:m,labelRef:b,scale:M,x:A.x,y:A.y,width:P.width,height:P.height,className:(0,i.$)("recharts-".concat(j," ").concat(j),p),viewBox:x,ticks:S}))},m=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(v,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(g,e))},b={allowDataOverflow:c.cd.allowDataOverflow,allowDecimals:c.cd.allowDecimals,allowDuplicatedCategory:c.cd.allowDuplicatedCategory,hide:!1,mirror:c.cd.mirror,orientation:c.cd.orientation,padding:c.cd.padding,reversed:c.cd.reversed,scale:c.cd.scale,tickCount:c.cd.tickCount,type:c.cd.type,width:c.cd.width,yAxisId:0};class x extends n.Component{render(){return n.createElement(m,this.props)}}p(x,"displayName","YAxis"),p(x,"defaultProps",b)},19959:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},20202:(e,t,r)=>{"use strict";function n(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}r.d(t,{v:()=>n})},20237:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!0}},20911:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(55100);t.debounce=function(e,t=0,r={}){let i;"object"!=typeof r&&(r={});let{leading:a=!1,trailing:o=!0,maxWait:l}=r,c=[,,];a&&(c[0]="leading"),o&&(c[1]="trailing");let u=null,s=n.debounce(function(...t){i=e.apply(this,t),u=null},t,{edges:c}),f=function(...t){return null!=l&&(null===u&&(u=Date.now()),Date.now()-u>=l)?(i=e.apply(this,t),u=Date.now(),s.cancel(),s.schedule(),i):(s.apply(this,t),i)};return f.cancel=s.cancel,f.flush=()=>(s.flush(),i),f}},21251:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},21424:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},21426:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>N,aX:()=>L,dS:()=>D,dp:()=>_,fW:()=>j,pg:()=>C,r1:()=>S,u9:()=>z,yn:()=>I});var n=r(84648),i=r(10687),a=r.n(i),o=r(43209),l=r(64279),c=r(57282),u=r(69009),s=r(97350),f=r(51426),h=r(35034),d=r(86445),p=r(28550),y=r(32520),v=r(97371),g=r(49396),m=r(77100),b=r(72198),x=r(78242),w=r(32751),O=r(54526),j=()=>(0,o.G)(s.iO),M=(e,t)=>t,P=(e,t,r)=>r,A=(e,t,r,n)=>n,S=(0,n.Mz)(u.R4,e=>a()(e,e=>e.coordinate)),E=(0,n.Mz)([x.J,M,P,A],y.i),k=(0,n.Mz)([E,u.n4],v.P),_=(e,t,r)=>{if(null!=t){var n=(0,x.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},T=(0,n.Mz)([x.J,M,P,A],m.q),C=(0,n.Mz)([d.Lp,d.A$,f.fz,h.HZ,u.R4,A,T,b.x],g.o),D=(0,n.Mz)([E,C],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),N=(0,n.Mz)(u.R4,k,p.E),z=(0,n.Mz)([T,k,c.LF,O.D,N,b.x,M],w.N),I=(0,n.Mz)([E],e=>({isActive:e.active,activeIndex:e.index})),L=(e,t,r,n,i,a,o,c)=>{if(e&&t&&n&&i&&a){var u=(0,l.r4)(e.chartX,e.chartY,t,r,c);if(u){var s=(0,l.SW)(u,t),f=(0,l.gH)(s,o,a,n,i),h=(0,l.bk)(t,a,f,u);return{activeIndex:String(f),activeCoordinate:h}}}}},22344:(e,t,r)=>{"use strict";r.d(t,{y:()=>eO,L:()=>ew});var n=r(43210),i=r(49384),a=r(98986),o=r(25679),l=r(98845),c=r(22989),u=r(54186),s=r(20237),f=r(64279),h=r(4057),d=r(89653),p=r(47371),y=["x","y"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),c=parseInt("".concat(t.width||i.width),10);return m(m(m(m(m({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:c,name:t.name,radius:t.radius})}function x(e){return n.createElement(p.y,v({shapeType:"rectangle",propTransformer:b,activeClassName:"recharts-active-bar"},e))}var w=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,c.Et)(e))return e;var i=(0,c.Et)(r)||(0,c.uy)(r);return i?e(r,n):(i||(0,d.A)(!1),t)}},O=r(61545),j=r(37625),M=r(66861),P=r(46993),A=r(51426),S=r(84648),E=r(85621),k=r(57282),_=r(35034),T=r(97350),C=r(12128),D=r(6548),N=r(64574);function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L=(0,S.Mz)([E.ld,(e,t,r,n,i)=>i],(e,t)=>e.filter(e=>"bar"===e.type).find(e=>e.id===t)),R=(0,S.Mz)([L],e=>null==e?void 0:e.maxBarSize),$=(e,t,r)=>{var n=null!=r?r:e;if(!(0,c.uy)(n))return(0,c.F4)(n,t,0)},U=(0,S.Mz)([A.fz,E.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type)),F=(0,S.Mz)([U,T.x3,(e,t,r)=>"horizontal"===(0,A.fz)(e)?(0,E.BQ)(e,"xAxis",t):(0,E.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(N.g),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:$(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:$(t,r,e.barSize)}))]}),B=(e,t,r,n)=>{var i,a;return"horizontal"===(0,A.fz)(e)?(i=(0,E.Gx)(e,"xAxis",t,n),a=(0,E.CR)(e,"xAxis",t,n)):(i=(0,E.Gx)(e,"yAxis",r,n),a=(0,E.CR)(e,"yAxis",r,n)),(0,f.Hj)(i,a)},H=(0,S.Mz)([F,T.JN,T._5,T.gY,(e,t,r,n,i)=>{var a,o,l,u,s=L(e,t,r,n,i);if(null!=s){var h=(0,A.fz)(e),d=(0,T.JN)(e),{maxBarSize:p}=s,y=(0,c.uy)(p)?d:p;return"horizontal"===h?(l=(0,E.Gx)(e,"xAxis",t,n),u=(0,E.CR)(e,"xAxis",t,n)):(l=(0,E.Gx)(e,"yAxis",r,n),u=(0,E.CR)(e,"yAxis",r,n)),null!=(a=null!=(o=(0,f.Hj)(l,u,!0))?o:y)?a:0}},B,R],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=(0,c.F4)(e,r,0,!0),u=[];if((0,C.H)(n[0].barSize)){var s=!1,f=r/o,h=n.reduce((e,t)=>e+(t.barSize||0),0);(h+=(o-1)*l)>=r&&(h-=(o-1)*l,l=0),h>=r&&f>0&&(s=!0,f*=.9,h=o*f);var d={offset:((r-h)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:d.offset+d.size+l,size:s?f:null!=(r=t.barSize)?r:0}}];return d=n[n.length-1].position,n},u)}else{var p=(0,c.F4)(t,r,0,!0);r-2*p-(o-1)*l<=0&&(l=0);var y=(r-2*p-(o-1)*l)/o;y>1&&(y>>=0);var v=(0,C.H)(i)?Math.min(y,i):y;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p+(y+l)*r+(y-v)/2,size:v}}],u)}return a}}(r,n,i!==a?i:a,e,(0,c.uy)(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>I(I({},e),{},{position:I(I({},e.position),{},{offset:e.position.offset-i/2})}))),l}),K=(0,S.Mz)([H,L],(e,t)=>{if(null!=e&&null!=t){var r=e.find(e=>e.stackId===t.stackId&&null!=t.dataKey&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),G=(0,S.Mz)([(e,t,r,n)=>"horizontal"===(0,A.fz)(e)?(0,E.TC)(e,"yAxis",r,n):(0,E.TC)(e,"xAxis",t,n),L],(e,t)=>{var r=(0,D.x)(t);if(!e||null==r||null==t)return;var{stackId:n}=t;if(null!=n){var i=e[n];if(i){var{stackedData:a}=i;if(a)return a.find(e=>e.key===r)}}}),Z=(0,S.Mz)([_.HZ,(e,t,r,n)=>(0,E.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,E.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,E.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,E.CR)(e,"yAxis",r,n),K,A.fz,k.HS,B,G,L,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,c,u,s,f)=>{var h,{chartData:d,dataStartIndex:p,dataEndIndex:y}=l;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=c){var{data:v}=s;if(null!=(h=null!=v&&v.length>0?v:null==d?void 0:d.slice(p,y+1)))return ew({layout:o,barSettings:s,pos:a,bandSize:c,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:u,displayedData:h,offset:e,cells:f})}}),q=r(43209),W=r(83409),V=r(69009),Y=r(14956),X=r(36304),J=r(73865),Q=r(3785),ee=r(75787),et=r(99857),er=r(23758),en=["onMouseEnter","onMouseLeave","onClick"],ei=["value","background","tooltipPosition"],ea=["id"],eo=["onMouseEnter","onClick","onMouseLeave"];function el(){return(el=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function ec(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ec(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ec(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function es(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var ef=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,f.uM)(r,t),payload:e}]};function eh(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,f.uM)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function ed(e){var t=(0,q.G)(V.A2),{data:r,dataKey:i,background:a,allOtherBarProps:o}=e,{onMouseEnter:l,onMouseLeave:c,onClick:s}=o,f=es(o,en),d=(0,O.Cj)(l,i),p=(0,O.Pg)(c),y=(0,O.Ub)(s,i);if(!a||null==r)return null;var v=(0,u.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:c}=e,u=es(e,ei);if(!l)return null;var s=d(e,r),g=p(e,r),m=y(e,r),b=eu(eu(eu(eu(eu({option:a,isActive:String(r)===t},u),{},{fill:"#eee"},l),v),(0,h.XC)(f,e,r)),{},{onMouseEnter:s,onMouseLeave:g,onClick:m,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(x,el({key:"background-bar-".concat(r)},b))}))}function ep(e){var{data:t,props:r,showLabels:i}=e,o=(0,et.u)(r),{id:c}=o,u=es(o,ea),{shape:s,dataKey:f,activeBar:d}=r,p=(0,q.G)(V.A2),y=(0,q.G)(V.Xb),{onMouseEnter:v,onClick:g,onMouseLeave:m}=r,b=es(r,eo),w=(0,O.Cj)(v,f),j=(0,O.Pg)(m),M=(0,O.Ub)(g,f);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=d&&String(t)===p&&(null==y||f===y),i=eu(eu(eu({},u),e),{},{isActive:r,option:r?d:s,index:t,dataKey:f});return n.createElement(a.W,el({className:"recharts-bar-rectangle"},(0,h.XC)(b,e,t),{onMouseEnter:w(e,t),onMouseLeave:j(e,t),onClick:M(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(x,i))}),i&&l.Z.renderCallByParent(r,t)):null}function ey(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:l,animationBegin:u,animationDuration:s,animationEasing:f,onAnimationEnd:h,onAnimationStart:d}=t,p=r.current,y=(0,X.n)(t,"recharts-bar-"),[v,g]=(0,n.useState)(!1),m=(0,n.useCallback)(()=>{"function"==typeof h&&h(),g(!1)},[h]),b=(0,n.useCallback)(()=>{"function"==typeof d&&d(),g(!0)},[d]);return n.createElement(er.J,{begin:u,duration:s,isActive:l,easing:f,onAnimationEnd:m,onAnimationStart:b,key:y},e=>{var l=1===e?i:null==i?void 0:i.map((t,r)=>{var n=p&&p[r];if(n)return eu(eu({},t),{},{x:(0,c.GW)(n.x,t.x,e),y:(0,c.GW)(n.y,t.y,e),width:(0,c.GW)(n.width,t.width,e),height:(0,c.GW)(n.height,t.height,e)});if("horizontal"===o){var i=(0,c.GW)(0,t.height,e);return eu(eu({},t),{},{y:t.y+t.height-i,height:i})}var a=(0,c.GW)(0,t.width,e);return eu(eu({},t),{},{width:a})});return(e>0&&(r.current=null!=l?l:null),null==l)?null:n.createElement(a.W,null,n.createElement(ep,{props:t,data:l,showLabels:!v}))})}function ev(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(ey,{previousRectanglesRef:i,props:e}):n.createElement(ep,{props:e,data:t,showLabels:!0})}var eg=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,f.kr)(e,t)}};class em extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:o,xAxisId:l,yAxisId:c,needClip:u,background:s,id:f}=this.props;if(e)return null;var h=(0,i.$)("recharts-bar",o);return n.createElement(a.W,{className:h,id:f},u&&n.createElement("defs",null,n.createElement(P.Q,{clipPathId:f,xAxisId:l,yAxisId:c})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:u?"url(#clipPath-".concat(f,")"):void 0},n.createElement(ed,{data:t,dataKey:r,background:s,allOtherBarProps:this.props}),n.createElement(ev,this.props)),this.props.children)}}var eb={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!s.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function ex(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:d,isAnimationActive:p}=e,{needClip:y}=(0,P.l)(r,i),v=(0,A.WX)(),g=(0,W.r)(),m=(0,u.aS)(e.children,o.f),b=(0,q.G)(t=>Z(t,r,i,g,e.id,m));if("vertical"!==v&&"horizontal"!==v)return null;var x=null==b?void 0:b[0];return t=null==x||null==x.height||null==x.width?0:"vertical"===v?x.height/2:x.width/2,n.createElement(M.zk,{xAxisId:r,yAxisId:i,data:b,dataPointFormatter:eg,errorBarOffset:t},n.createElement(em,el({},e,{layout:v,needClip:y,data:b,xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:d,isAnimationActive:p})))}function ew(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:u,yAxisTicks:s,stackedData:h,displayedData:d,offset:p,cells:y}=e,v="horizontal"===t?l:o,g=h?v.scale.domain():null,m=(0,f.DW)({numericAxis:v});return d.map((e,d)=>{h?b=(0,f._f)(h[d],g):Array.isArray(b=(0,f.kr)(e,r))||(b=[m,b]);var v=w(n,0)(b[1],d);if("horizontal"===t){var b,x,O,j,M,P,A,[S,E]=[l.scale(b[0]),l.scale(b[1])];x=(0,f.y2)({axis:o,ticks:u,bandSize:a,offset:i.offset,entry:e,index:d}),O=null!=(A=null!=E?E:S)?A:void 0,j=i.size;var k=S-E;if(M=(0,c.M8)(k)?0:k,P={x,y:p.top,width:j,height:p.height},Math.abs(v)>0&&Math.abs(M)<Math.abs(v)){var _=(0,c.sA)(M||v)*(Math.abs(v)-Math.abs(M));O-=_,M+=_}}else{var[T,C]=[o.scale(b[0]),o.scale(b[1])];if(x=T,O=(0,f.y2)({axis:l,ticks:s,bandSize:a,offset:i.offset,entry:e,index:d}),j=C-T,M=i.size,P={x:p.left,y:O,width:p.width,height:M},Math.abs(v)>0&&Math.abs(j)<Math.abs(v)){var D=(0,c.sA)(j||v)*(Math.abs(v)-Math.abs(j));j+=D}}return null==x||null==O||null==j||null==M?null:eu(eu({},e),{},{x,y:O,width:j,height:M,value:h?b:b[1],payload:e,background:P,tooltipPosition:{x:x+j/2,y:O+M/2}},y&&y[d]&&y[d].props)}).filter(Boolean)}function eO(e){var t=(0,J.e)(e,eb),r=(0,W.r)();return n.createElement(Q.x,{id:t.id,type:"bar"},e=>n.createElement(n.Fragment,null,n.createElement(Y.A,{legendPayload:ef(t)}),n.createElement(j.r,{fn:eh,args:t}),n.createElement(ee.p,{type:"bar",id:e,data:void 0,xAxisId:t.xAxisId,yAxisId:t.yAxisId,zAxisId:0,dataKey:t.dataKey,stackId:(0,f.$8)(t.stackId),hide:t.hide,barSize:t.barSize,minPointSize:t.minPointSize,maxBarSize:t.maxBarSize,isPanorama:r}),n.createElement(ex,el({},t,{id:e}))))}eO.displayName="Bar"},22786:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},22989:(e,t,r)=>{"use strict";r.d(t,{CG:()=>d,Dj:()=>p,Et:()=>c,F4:()=>h,GW:()=>y,M8:()=>o,NF:()=>f,Zb:()=>m,_3:()=>l,eP:()=>v,sA:()=>a,uy:()=>g,vh:()=>u});var n=r(5664),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,c=e=>("number"==typeof e||e instanceof Number)&&!o(e),u=e=>c(e)||"string"==typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},h=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!c(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},d=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},p=(e,t)=>c(e)&&c(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){return c(e)&&c(t)?e+r*(t-e):t}function v(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var g=e=>null==e,m=e=>g(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},23457:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},23561:(e,t,r)=>{"use strict";r.d(t,{E:()=>k});var n=r(43210),i=r(49384),a=r(22989),o=r(20237),l=r(54186),c=r(45093),u=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,d={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},p=Object.keys(d);class y{static parse(e){var t,[,r,n]=null!=(t=h.exec(e))?t:[];return new y(parseFloat(r),null!=n?n:"")}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),p.includes(t)&&(this.num=e*d[t],this.unit="px")}add(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}}function v(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=u.exec(t))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),c="*"===i?o.multiply(l):o.divide(l);if(c.isNaN())return"NaN";t=t.replace(u,c.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,h,d,p]=null!=(f=s.exec(t))?f:[],v=y.parse(null!=h?h:""),g=y.parse(null!=p?p:""),m="+"===d?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";t=t.replace(s,m.toString())}return t}var g=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=g.exec(r));){var[,n]=t;r=r.replace(g,v(n))}return r}(t),t=v(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],x=["dx","dy","angle","className","breakAll"];function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,M=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(j));var o=i.map(e=>({word:e,width:(0,c.Pu)(e,n).width})),l=r?0:(0,c.Pu)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(e){return null}},P=(e,t,r,n,i)=>{var o,{maxLines:l,children:c,style:u,breakAll:s}=e,f=(0,a.Et)(l),h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},d=h(t),p=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(d.length>l||p(d).width>Number(n)))return d;for(var y=e=>{var t=h(M({breakAll:s,style:u,children:c.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||p(t).width>Number(n),t]},v=0,g=c.length-1,m=0;v<=g&&m<=c.length-1;){var b=Math.floor((v+g)/2),[x,w]=y(b-1),[O]=y(b);if(x||O||(v=b+1),x&&O&&(g=b-1),!x&&O){o=w;break}m++}return o||d},A=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(j)}],S=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:l}=e;if((t||r)&&!o.m.isSsr){var c=M({breakAll:a,children:n,style:i});if(!c)return A(n);var{wordsWithComputedWidth:u,spaceWidth:s}=c;return P({breakAll:a,children:n,maxLines:l,style:i},u,s,t,r)}return A(n)},E="#808080",k=(0,n.forwardRef)((e,t)=>{var r,{x:o=0,y:c=0,lineHeight:u="1em",capHeight:s="0.71em",scaleToFit:f=!1,textAnchor:h="start",verticalAnchor:d="end",fill:p=E}=e,y=O(e,b),v=(0,n.useMemo)(()=>S({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:f,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,f,y.style,y.width]),{dx:g,dy:j,angle:M,className:P,breakAll:A}=y,k=O(y,x);if(!(0,a.vh)(o)||!(0,a.vh)(c))return null;var _=o+((0,a.Et)(g)?g:0),T=c+((0,a.Et)(j)?j:0);switch(d){case"start":r=m("calc(".concat(s,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(u," + (").concat(s," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(u,")"))}var C=[];if(f){var D=v[0].width,{width:N}=y;C.push("scale(".concat((0,a.Et)(N)?N/D:1,")"))}return M&&C.push("rotate(".concat(M,", ").concat(_,", ").concat(T,")")),C.length&&(k.transform=C.join(" ")),n.createElement("text",w({},(0,l.J9)(k,!0),{ref:t,x:_,y:T,className:(0,i.$)("recharts-text",P),textAnchor:h,fill:p.includes("url")?E:p}),v.map((e,t)=>{var i=e.words.join(A?"":" ");return n.createElement("tspan",{x:_,dy:0===t?r:u,key:"".concat(i,"-").concat(t)},i)}))});k.displayName="Text"},23758:(e,t,r)=>{"use strict";r.d(t,{J:()=>u});var n=r(43210),i=r(73865);r(74875),r(74173);var a=r(31068),o={begin:0,duration:1e3,easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}},l={t:0},c={t:1};function u(e){var t=(0,i.e)(e,o),{isActive:r,canBegin:u,duration:s,easing:f,begin:h,onAnimationEnd:d,onAnimationStart:p,children:y}=t;(0,a.L)("JavascriptAnimate",t.animationManager);var[v,g]=(0,n.useState)(r?l:c);return(0,n.useRef)(null),y(v.t)}},23814:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(84648),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},23854:(e,t,r)=>{e.exports=r(45263).uniqBy},24028:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(43209),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},24413:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},25679:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},25893:(e,t,r)=>{"use strict";r.d(t,{p:()=>i}),r(43210),r(32181);var n=r(43209);function i(e){return(0,n.j)(),null}},26101:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(49384),i=r(43210),a=r(67766),o=r.n(a),l=r(22989),c=r(10521);function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:u="100%",height:f="100%",minWidth:h=0,minHeight:d,maxHeight:p,children:y,debounce:v=0,id:g,className:m,onResize:b,style:x={}}=e,w=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>w.current);var[j,M]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),P=(0,i.useCallback)((e,t)=>{M(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;P(r,n),null==(t=O.current)||t.call(O,r,n)};v>0&&(e=o()(e,v,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=w.current.getBoundingClientRect();return P(r,n),t.observe(w.current),()=>{t.disconnect()}},[P,v]);var A=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=j;if(e<0||t<0)return null;(0,c.R)((0,l._3)(u)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",u,f),(0,c.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(u)?e:u,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),p&&a>p&&(a=p)),(0,c.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,u,f,h,d,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({width:n,height:a},e.props.style)}))},[r,y,f,p,d,h,j,u]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},x),{},{width:u,height:f,minWidth:h,minHeight:d,maxHeight:p}),ref:w},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},A))})},26349:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(48130);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},26495:(e,t,r)=>{"use strict";r.d(t,{M:()=>n});var n=e=>e.tooltip.settings.axisId},26652:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(43210).createContext)(null)},27469:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},27747:(e,t,r)=>{"use strict";r.d(t,{W:()=>m});var n=r(43210),i=r(49384),a=r(71579),o=r(43209);r(5338);var l=r(85621),c=r(35034),u=r(83409),s=["children"],f=["dangerouslySetInnerHTML","ticks"];function h(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function y(e){(0,o.j)();var t=(0,n.useMemo)(()=>{var{children:t}=e;return p(e,s)},[e]),r=(0,o.G)(e=>(0,l.Rl)(e,t.id));return t===r?e.children:null}var v=e=>{var{xAxisId:t,className:r}=e,s=(0,o.G)(c.c2),h=(0,u.r)(),y="xAxis",v=(0,o.G)(e=>(0,l.iV)(e,y,t,h)),g=(0,o.G)(e=>(0,l.Zi)(e,y,t,h)),m=(0,o.G)(e=>(0,l.Lw)(e,t)),b=(0,o.G)(e=>(0,l.L$)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:x,ticks:w}=e,O=p(e,f);return n.createElement(a.u,d({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(y," ").concat(y),r),viewBox:s,ticks:g}))},g=e=>{var t,r,i,a,o;return n.createElement(y,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},n.createElement(v,e))};class m extends n.Component{render(){return n.createElement(g,this.props)}}h(m,"displayName","XAxis"),h(m,"defaultProps",{allowDataOverflow:l.PU.allowDataOverflow,allowDecimals:l.PU.allowDecimals,allowDuplicatedCategory:l.PU.allowDuplicatedCategory,height:l.PU.height,hide:!1,mirror:l.PU.mirror,orientation:l.PU.orientation,padding:l.PU.padding,reversed:l.PU.reversed,scale:l.PU.scale,tickCount:l.PU.tickCount,type:l.PU.type,xAxisId:0})},27934:(e,t,r)=>{"use strict";r.d(t,{EI:()=>f,oM:()=>s});var n=r(43209),i=r(69009),a=r(84648),o=r(35034),l=(0,a.Mz)([o.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),c=r(86445),u=(0,a.Mz)([l,c.Lp,c.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),s=()=>(0,n.G)(u),f=()=>(0,n.G)(i.JG)},27977:(e,t,r)=>{"use strict";r.d(t,{g:()=>s});var n=r(84648),i=r(51426),a=r(69009),o=r(35034),l=r(21426),c=r(8920),u=r(16950),s=(0,n.Mz)([(e,t)=>t,i.fz,c.D0,u.R,a.gL,a.R4,l.r1,o.HZ],l.aX)},28382:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},28550:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(22989),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},29243:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(91428);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},29632:(e,t,r)=>{"use strict";e.exports=r(97668)},29862:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(92923),i=r(27469);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let c=t?.(r,a,o,l);if(null!=c)return c;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},30415:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(30657),a=r(59138),o=r(87509),l=r(57841);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},30657:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},30802:(e,t,r)=>{"use strict";r.d(t,{As:()=>u,TK:()=>s,Vi:()=>c,ZF:()=>l,g5:()=>o,iZ:()=>f});var n=r(76067),i=r(71392),a=(0,n.Z0)({name:"graphicalItems",initialState:{cartesianItems:[],polarItems:[]},reducers:{addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,a=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));a>-1&&(e.cartesianItems[a]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addCartesianGraphicalItem:o,replaceCartesianGraphicalItem:l,removeCartesianGraphicalItem:c,addPolarGraphicalItem:u,removePolarGraphicalItem:s}=a.actions,f=a.reducer},30921:(e,t,r)=>{e.exports=r(71337).range},31068:(e,t,r)=>{"use strict";r.d(t,{L:()=>o});var n=r(43210);class i{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var a=(0,n.createContext)(function(){var e,t,r,n,a,o;return e=new i,r=()=>null,n=!1,a=null,o=t=>{if(!n){if(Array.isArray(t)){if(!t.length)return;var[i,...l]=t;if("number"==typeof i){a=e.setTimeout(o.bind(null,l),i);return}o(i),a=e.setTimeout(o.bind(null,l));return}"string"==typeof t&&r(t),"object"==typeof t&&r(t),"function"==typeof t&&t()}},{stop:()=>{n=!0},start:e=>{n=!1,a&&(a(),a=null),o(e)},subscribe:e=>(r=e,()=>{r=()=>null}),getTimeoutController:()=>e}});function o(e,t){var r=(0,n.useContext)(a);return(0,n.useMemo)(()=>null!=t?t:r(e),[e,t,r])}},32181:(e,t,r)=>{"use strict";r.d(t,{mZ:()=>l,vE:()=>o});var n=r(76067),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=a.reducer,{updateOptions:l}=a.actions},32520:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(17118);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},32751:(e,t,r)=>{"use strict";r.d(t,{N:()=>c});var n=r(22989),i=r(64279),a=r(20202);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var c=(e,t,r,o,c,u,s)=>{if(null!=t&&null!=u){var{chartData:f,computedData:h,dataStartIndex:d,dataEndIndex:p}=r;return e.reduce((e,r)=>{var y,v,g,m,b,{dataDefinedOnItem:x,settings:w}=r,O=(y=x,v=f,null!=y?y:v),j=Array.isArray(O)?(0,a.v)(O,d,p):O,M=null!=(g=null==w?void 0:w.dataKey)?g:null==o?void 0:o.dataKey,P=null==w?void 0:w.nameKey;return Array.isArray(m=null!=o&&o.dataKey&&Array.isArray(j)&&!Array.isArray(j[0])&&"axis"===s?(0,n.eP)(j,o.dataKey,c):u(j,t,h,P))?m.forEach(t=>{var r=l(l({},w),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:w,dataKey:M,payload:m,value:(0,i.kr)(m,M),name:null!=(b=(0,i.kr)(m,P))?b:null==w?void 0:w.name})),e},[])}}},33731:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(97766),i=r(21424),a=r(26349);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},34258:(e,t,r)=>{"use strict";r.d(t,{e:()=>p,k:()=>y});var n=r(76067),i=r(17118),a=r(27977),o=r(77357),l=r(43075),c=r(75601),u=r(84648),s=r(72198),f=r(78242),h=(0,u.Mz)([f.J],e=>e.tooltipItemPayloads),d=(0,u.Mz)([h,s.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),p=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:p,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===u){var s=(0,a.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===u){var f,h=r.touches[0],p=document.elementFromPoint(h.clientX,h.clientY);if(!p||!p.getAttribute)return;var y=p.getAttribute(c.F0),v=null!=(f=p.getAttribute(c.um))?f:void 0,g=d(t.getState(),y,v);t.dispatch((0,i.RD)({activeDataKey:v,activeIndex:y,activeCoordinate:g}))}}})},34318:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},34955:(e,t,r)=>{"use strict";r.d(t,{h:()=>y});var n=r(43210),i=r(49384),a=r(54186),o=r(19335),l=r(22989),c=r(73865);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999),f=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:c,cornerIsExternal:u}=e,s=c*(l?1:-1)+n,f=Math.asin(c/s)/o.Kg,h=u?i:i+a*f,d=(0,o.IZ)(t,r,s,h);return{center:d,circleTangency:(0,o.IZ)(t,r,n,h),lineTangency:(0,o.IZ)(t,r,s*Math.cos(f*o.Kg),u?i-a*f:i),theta:f}},h=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:l}=e,c=s(a,l),u=a+c,f=(0,o.IZ)(t,r,i,a),h=(0,o.IZ)(t,r,i,u),d="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(a>u),",\n    ").concat(h.x,",").concat(h.y,"\n  ");if(n>0){var p=(0,o.IZ)(t,r,n,a),y=(0,o.IZ)(t,r,n,u);d+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(a<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else d+="L ".concat(t,",").concat(r," Z");return d},d=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:c,startAngle:u,endAngle:s}=e,d=(0,l.sA)(s-u),{circleTangency:p,lineTangency:y,theta:v}=f({cx:t,cy:r,radius:i,angle:u,sign:d,cornerRadius:a,cornerIsExternal:c}),{circleTangency:g,lineTangency:m,theta:b}=f({cx:t,cy:r,radius:i,angle:s,sign:-d,cornerRadius:a,cornerIsExternal:c}),x=c?Math.abs(u-s):Math.abs(u-s)-v-b;if(x<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):h({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:s});var w="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(x>180),",").concat(+(d<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:j,theta:M}=f({cx:t,cy:r,radius:n,angle:u,sign:d,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),{circleTangency:P,lineTangency:A,theta:S}=f({cx:t,cy:r,radius:n,angle:s,sign:-d,isExternal:!0,cornerRadius:a,cornerIsExternal:c}),E=c?Math.abs(u-s):Math.abs(u-s)-M-S;if(E<0&&0===a)return"".concat(w,"L").concat(t,",").concat(r,"Z");w+="L".concat(A.x,",").concat(A.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(E>180),",").concat(+(d>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(d<0),",").concat(j.x,",").concat(j.y,"Z")}else w+="L".concat(t,",").concat(r,"Z");return w},p={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y=e=>{var t,r=(0,c.e)(e,p),{cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x,className:w}=r;if(y<f||b===x)return null;var O=(0,i.$)("recharts-sector",w),j=y-f,M=(0,l.F4)(v,j,0,!0);return t=M>0&&360>Math.abs(b-x)?d({cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:Math.min(M,j/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:x}):h({cx:o,cy:s,innerRadius:f,outerRadius:y,startAngle:b,endAngle:x}),n.createElement("path",u({},(0,a.J9)(r,!0),{className:O,d:t}))}},35034:(e,t,r)=>{"use strict";r.d(t,{c2:()=>g,HZ:()=>y,Ds:()=>v});var n=r(84648),i=r(5664),a=r.n(i),o=r(10687),l=r.n(o),c=e=>e.legend.settings;(0,n.Mz)([e=>e.legend.payload,c],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?l()(n,r):n});var u=r(64279),s=r(86445),f=r(23814),h=r(75601);function d(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?d(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):d(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(0,n.Mz)([s.Lp,s.A$,s.HK,e=>e.brush.height,f.h,f.W,c,e=>e.legend.size],(e,t,r,n,i,o,l,c)=>{var s=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:h.tQ;return p(p({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),f=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:p(p({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),d=p(p({},f),s),y=d.bottom;d.bottom+=n;var v=e-(d=(0,u.s0)(d,l,c)).left-d.right,g=t-d.top-d.bottom;return p(p({brushBottom:y},d),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.Mz)(y,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),g=(0,n.Mz)(s.Lp,s.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},35314:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},36023:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},36166:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},36304:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(43210),i=r(22989);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(t)),a=(0,n.useRef)(e);return a.current!==e&&(r.current=(0,i.NF)(t),a.current=e),r.current}},37586:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(36023),i=r(76021),a=r(43574);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},c=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,u=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:u.map(t=>c(t,e))})).slice().sort((e,t)=>{for(let i=0;i<u.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},37625:(e,t,r)=>{"use strict";r.d(t,{r:()=>a}),r(43210);var n=r(43209);r(17118);var i=r(83409);function a(e){var{fn:t,args:r}=e;return(0,n.j)(),(0,i.r)(),null}},39733:(e,t,r)=>{"use strict";e.exports=r(10907)},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40144:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(87509);t.property=function(e){return function(t){return n.get(t,e)}}},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42066:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(14454);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},42750:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(15708);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},43075:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>c,au:()=>l,xH:()=>i});var n=r(43209),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return o(t,i(e),a(e))}function c(e){return(0,n.G)(t=>l(t,e))}},43084:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(20911);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},43209:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(39733),i=r(43210),a=r(26652),o=e=>e,l=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},c=()=>{},u=()=>c,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:u,t?t.store.getState:c,t?t.store.getState:c,t?e:c,s)}},43574:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},44919:(e,t,r)=>{"use strict";r.d(t,{x:()=>o,y:()=>a});var n=r(76067),i=r(69009),a=(0,n.VP)("externalEvent"),o=(0,n.Nc)();o.startListening({actionCreator:a,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},45093:(e,t,r)=>{"use strict";r.d(t,{Pu:()=>f});var n=r(20237);class i{constructor(e){!function(e,t,r){var n;(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"cache",new Map),this.maxSize=e}get(e){var t=this.cache.get(e);return void 0!==t&&(this.cache.delete(e),this.cache.set(e,t)),t}set(e,t){if(this.cache.has(e))this.cache.delete(e);else if(this.cache.size>=this.maxSize){var r=this.cache.keys().next().value;this.cache.delete(r)}this.cache.set(e,t)}clear(){this.cache.clear()}size(){return this.cache.size}}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var o=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},{cacheSize:2e3,enableCache:!0}),l=new i(o.cacheSize),c={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},u="recharts_measurement_span",s=(e,t)=>{try{var r=document.getElementById(u);r||((r=document.createElement("span")).setAttribute("id",u),r.setAttribute("aria-hidden","true"),document.body.appendChild(r)),Object.assign(r.style,c,t),r.textContent="".concat(e);var n=r.getBoundingClientRect();return{width:n.width,height:n.height}}catch(e){return{width:0,height:0}}},f=function(e){var t,r,i,a,c,u,f=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};if(!o.enableCache)return s(e,f);var h=(t=f.fontSize||"",r=f.fontFamily||"",i=f.fontWeight||"",a=f.fontStyle||"",c=f.letterSpacing||"",u=f.textTransform||"","".concat(e,"|").concat(t,"|").concat(r,"|").concat(i,"|").concat(a,"|").concat(c,"|").concat(u)),d=l.get(h);if(d)return d;var p=s(e,f);return l.set(h,p),p}},45263:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(59618),i=r(92681),a=r(90830),o=r(17617);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},46993:(e,t,r)=>{"use strict";r.d(t,{Q:()=>c,l:()=>l});var n=r(43210),i=r(43209),a=r(85621),o=r(27934);function l(e,t){var r,n,o=(0,i.G)(t=>(0,a.Rl)(t,e)),l=(0,i.G)(e=>(0,a.sf)(e,t)),c=null!=(r=null==o?void 0:o.allowDataOverflow)?r:a.PU.allowDataOverflow,u=null!=(n=null==l?void 0:l.allowDataOverflow)?n:a.cd.allowDataOverflow;return{needClip:c||u,needClipX:c,needClipY:u}}function c(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,o.oM)(),{needClipX:c,needClipY:u,needClip:s}=l(t,r);if(!s)return null;var{x:f,y:h,width:d,height:p}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:c?f:f-d/2,y:u?h:h-p/2,width:c?d:2*d,height:u?p:2*p}))}},47371:(e,t,r)=>{"use strict";r.d(t,{y:()=>V});var n=r(43210),i=r(92867),a=r.n(i),o=r(71524),l=r(49384),c=r(54186),u=r(73865),s=r(52457);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var h=(e,t,r,n,i)=>{var a,o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},d={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},p=e=>{var t=(0,u.e)(e,d),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:o,y:p,upperWidth:y,lowerWidth:v,height:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isUpdateAnimationActive:O}=t;if(o!==+o||p!==+p||y!==+y||v!==+v||g!==+g||0===y&&0===v||0===g)return null;var j=(0,l.$)("recharts-trapezoid",m);return O?n.createElement(s.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:o,y:p},to:{upperWidth:y,lowerWidth:v,height:g,x:o,y:p},duration:x,animationEasing:b,isActive:O},e=>{var{upperWidth:a,lowerWidth:o,height:l,x:u,y:d}=e;return n.createElement(s.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,easing:b},n.createElement("path",f({},(0,c.J9)(t,!0),{className:j,d:h(u,d,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,c.J9)(t,!0),{className:j,d:h(o,p,y,v,g)})))},y=r(34955),v=r(98986);let g=Math.cos,m=Math.sin,b=Math.sqrt,x=Math.PI,w=2*x,O={draw(e,t){let r=b(t/x);e.moveTo(r,0),e.arc(0,0,r,0,w)}},j=b(1/3),M=2*j,P=m(x/10)/m(7*x/10),A=m(w/10)*P,S=-g(w/10)*P,E=b(3),k=b(3)/2,_=1/b(12),T=(_/2+1)*3;var C=r(22786),D=r(15606);b(3),b(3);var N=r(22989),z=["type","size","sizeType"];function I(){return(I=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var $={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/M),n=r*j;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=A*r,i=S*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=w*t/5,o=g(a),l=m(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*E));e.moveTo(0,2*r),e.lineTo(-E*r,-r),e.lineTo(E*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/T),n=r/2,i=r*_,a=r*_+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-k*i,k*n+-.5*i),e.lineTo(-.5*n-k*a,k*n+-.5*a),e.lineTo(-.5*o-k*a,k*o+-.5*a),e.lineTo(-.5*n+k*i,-.5*i-k*n),e.lineTo(-.5*n+k*a,-.5*a-k*n),e.lineTo(-.5*o+k*a,-.5*a-k*o),e.closePath()}}},U=Math.PI/180,F=e=>$["symbol".concat((0,N.Zb)(e))]||O,B=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*U;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},H=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=R(R({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,z)),{},{type:t,size:r,sizeType:i}),{className:o,cx:u,cy:s}=a,f=(0,c.J9)(a,!0);return u===+u&&s===+s&&r===+r?n.createElement("path",I({},f,{className:(0,l.$)("recharts-symbols",o),transform:"translate(".concat(u,", ").concat(s,")"),d:(()=>{var e=F(t);return(function(e,t){let r=null,n=(0,D.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,C.A)(e||O),t="function"==typeof t?t:(0,C.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,C.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,C.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(B(r,i,t))()})()})):null};H.registerSymbol=(e,t)=>{$["symbol".concat((0,N.Zb)(e))]=t};var K=["option","shapeType","propTransformer","activeClassName","isActive"];function G(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?G(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):G(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function q(e,t){return Z(Z({},t),e)}function W(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(p,r);case"sector":return n.createElement(y.h,r);case"symbols":if("symbols"===t)return n.createElement(H,r);break;default:return null}}function V(e){var t,{option:r,shapeType:i,propTransformer:o=q,activeClassName:l="recharts-active-shape",isActive:c}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,K);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,Z(Z({},u),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(u);else if(a()(r)&&"boolean"!=typeof r){var s=o(r,u);t=n.createElement(W,{shapeType:i,elementProps:s})}else t=n.createElement(W,{shapeType:i,elementProps:u});return c?n.createElement(v.W,{className:l},t):t}},48130:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},48657:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},48806:(e,t,r)=>{"use strict";r.d(t,{J:()=>W});var n=r(43210),i=r(54864),a=r(11208),o=r(76067),l=r(49605),c=r(17118),u=r(64267),s=r(52693),f=r(85407);function h(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var d=r(5338),p=r(30802),y=r(71392),v=(0,o.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,y.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,y.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,y.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:g,removeDot:m,addArea:b,removeArea:x,addLine:w,removeLine:O}=v.actions,j=v.reducer,M={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},P=(0,o.Z0)({name:"brush",initialState:M,reducers:{setBrushSettings:(e,t)=>null==t.payload?M:t.payload}}),{setBrushSettings:A}=P.actions,S=P.reducer,E=r(53044),k=r(32181),_=(0,o.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,y.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,y.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:T,removeRadiusAxis:C,addAngleAxis:D,removeAngleAxis:N}=_.actions,z=_.reducer,I=r(61645),L=r(11281),R=r(44919),$=r(34258),U=(0,o.Z0)({name:"errorBars",initialState:{},reducers:{addErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]||(e[r]=[]),e[r].push(n)},removeErrorBar:(e,t)=>{var{itemId:r,errorBar:n}=t.payload;e[r]&&(e[r]=e[r].filter(e=>e.dataKey!==n.dataKey||e.direction!==n.direction))}}}),{addErrorBar:F,removeErrorBar:B}=U.actions,H=U.reducer,K=(0,a.HY)({brush:S,cartesianAxis:d.CA,chartData:u.LV,errorBars:H,graphicalItems:p.iZ,layout:s.Vp,legend:E.CU,options:l.lJ,polarAxis:z,polarOptions:I.J,referenceElements:j,rootProps:k.vE,tooltip:c.En}),G=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,o.U1)({reducer:K,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([f.YF.middleware,f.fP.middleware,L.$7.middleware,R.x.middleware,$.k.middleware]),devTools:{serialize:{replacer:h},name:"recharts-".concat(t)}})},Z=r(83409),q=r(26652);function W(e){var{preloadedState:t,children:r,reduxStoreName:a}=e,o=(0,Z.r)(),l=(0,n.useRef)(null);if(o)return r;null==l.current&&(l.current=G(t,a));var c=q.E;return n.createElement(i.Kq,{context:c,store:l.current},r)}},49396:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var c=o[0],u=null==c?void 0:l(c.positions,a);if(null!=u)return u;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}}},49605:(e,t,r)=>{"use strict";r.d(t,{dl:()=>c,lJ:()=>l,uN:()=>a});var n=r(76067),i=r(22989);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:c}=o.actions},49899:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},50380:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var n=r(6548),i=r(64279);function a(e,t,r){var{chartData:a=[]}=t,o=null==r?void 0:r.dataKey,l=new Map;return e.forEach(e=>{var t,r=null!=(t=e.data)?t:a;if(null!=r&&0!==r.length){var c=(0,n.x)(e);r.forEach((t,r)=>{var n,a=null==o?r:String((0,i.kr)(t,o,null)),u=(0,i.kr)(t,e.dataKey,0);Object.assign(n=l.has(a)?l.get(a):{},{[c]:u}),l.set(a,n)})}}),Array.from(l.values())}},51426:(e,t,r)=>{"use strict";r.d(t,{W7:()=>s,WX:()=>p,fz:()=>d,rY:()=>h,sk:()=>c,yi:()=>f}),r(43210);var n=r(43209),i=r(35034),a=r(86445),o=r(83409),l=r(94728),c=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),c=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&c?{width:a.width-c.left-c.right,height:a.height-c.top-c.bottom,x:c.left,y:c.top}:r},u={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:u},f=()=>(0,n.G)(a.Lp),h=()=>(0,n.G)(a.A$),d=e=>e.layout.layoutType,p=()=>(0,n.G)(d)},52371:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(92923);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},52457:(e,t,r)=>{"use strict";r.d(t,{i:()=>v});var n=r(43210),i=r(12728),a=r.n(i),o=r(74173),l=r(74875),c=r(55716),u=r(31068),s=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){p(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class y extends n.PureComponent{constructor(e,t){super(e,t),p(this,"mounted",!1),p(this,"manager",void 0),p(this,"stopJSAnimation",null),p(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:c}=this.props;if(this.manager=c,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:c}=this.state;if(r){if(!t){this.state&&c&&(n&&c[n]!==o||!n&&c!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var u=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=u||i?l:e.to;this.state&&c&&(n&&c[n]!==s||!n&&c!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(d(d({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:c,onAnimationStart:u}=e,s=(0,l.A)(t,r,(0,o.yl)(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([u,a,()=>{this.stopJSAnimation=s()},n,c])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof u||"spring"===a)return void this.runJSAnimation(e);var s=n?{[n]:i}:i,f=(0,c.dl)(Object.keys(s),r,a);this.manager.start([o,t,d(d({},s),{},{transition:f}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:c,to:u,canBegin:f,onAnimationEnd:h,shouldReAnimate:p,onAnimationReStart:y,animationManager:v}=e,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,s),m=n.Children.count(t),b=this.state.style;if("function"==typeof t)return t(b);if(!l||0===m||i<=0)return t;var x=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,d(d({},g),{},{style:d(d({},t),b),className:r}))};return 1===m?x(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>x(e)))}}function v(e){var t,r=(0,u.L)(null!=(t=e.attributeName)?t:Object.keys(e.to).join(","),e.animationManager);return n.createElement(y,f({},e,{animationManager:r}))}p(y,"displayName","Animate"),p(y,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}})},52693:(e,t,r)=>{"use strict";r.d(t,{B_:()=>i,JK:()=>a,Vp:()=>c,gX:()=>o,hF:()=>l});var n=(0,r(76067).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:l}=n.actions,c=n.reducer},53044:(e,t,r)=>{"use strict";r.d(t,{CU:()=>s,Lx:()=>c,u3:()=>u});var n=r(76067),i=r(71392),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:c,removeLegendPayload:u}=a.actions,s=a.reducer},53416:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},54024:(e,t,r)=>{"use strict";r.d(t,{P:()=>p});var n=r(43210),i=r(48806),a=r(13420),o=r(71680),l=r(25893),c=r(84071),u=r(73865),s=r(12128),f=["width","height"];function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p=(0,n.forwardRef)(function(e,t){var r,p=(0,u.e)(e.categoricalChartProps,d),{width:y,height:v}=p,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(p,f);if(!(0,s.F)(y)||!(0,s.F)(v))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,categoricalChartProps:O}=e;return n.createElement(i.J,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:x,tooltipPayloadSearcher:w,eventEmitter:void 0}},reduxStoreName:null!=(r=O.id)?r:m},n.createElement(a.TK,{chartData:O.data}),n.createElement(o.s,{width:y,height:v,layout:p.layout,margin:p.margin}),n.createElement(l.p,{accessibilityLayer:p.accessibilityLayer,barCategoryGap:p.barCategoryGap,maxBarSize:p.maxBarSize,stackOffset:p.stackOffset,barGap:p.barGap,barSize:p.barSize,syncId:p.syncId,syncMethod:p.syncMethod,className:p.className}),n.createElement(c.L,h({},g,{width:y,height:v,ref:t})))})},54186:(e,t,r)=>{"use strict";r.d(t,{J9:()=>m,aS:()=>y,y$:()=>v});var n=r(5664),i=r.n(n),a=r(43210),o=r(29632),l=r(22989),c=r(4057),u=r(77400),s=r(99857),f=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",h=null,d=null,p=e=>{if(e===h&&Array.isArray(d))return d;var t=[];return a.Children.forEach(e,e=>{(0,l.uy)(e)||((0,o.isFragment)(e)?t=t.concat(p(e.props.children)):t.push(e))}),d=t,h=e,t};function y(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>f(e)):[f(t)],p(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var v=e=>!e||"object"!=typeof e||!("clipDot"in e)||!!e.clipDot,g=(e,t,r,n)=>{if("symbol"==typeof t||"number"==typeof t)return!0;var i,a=null!=(i=n&&(null===c.VU||void 0===c.VU?void 0:c.VU[n]))?i:[],o=t.startsWith("data-"),l="function"!=typeof e&&(!!n&&a.includes(t)||(0,s.R)(t)),f=!!r&&(0,u.q)(t);return o||l||f},m=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;g(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},54526:(e,t,r)=>{"use strict";r.d(t,{D:()=>o});var n=r(85621),i=r(16950),a=r(26495),o=e=>{var t=(0,i.R)(e),r=(0,a.M)(e);return(0,n.Hd)(e,t,r)}},55100:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t,{signal:r,edges:n}={}){let i,a=null,o=null!=n&&n.includes("leading"),l=null==n||n.includes("trailing"),c=()=>{null!==a&&(e.apply(i,a),i=void 0,a=null)},u=()=>{l&&c(),d()},s=null,f=()=>{null!=s&&clearTimeout(s),s=setTimeout(()=>{s=null,u()},t)},h=()=>{null!==s&&(clearTimeout(s),s=null)},d=()=>{h(),i=void 0,a=null},p=function(...e){if(r?.aborted)return;i=this,a=e;let t=null==s;f(),o&&t&&c()};return p.schedule=f,p.cancel=d,p.flush=()=>{c()},r?.addEventListener("abort",d,{once:!0}),p}},55716:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{dl:()=>o,mP:()=>l,s8:()=>c});var a=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),o=(e,t,r)=>e.map(e=>"".concat(a(e)," ").concat(t,"ms ").concat(r)).join(","),l=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),c=(e,t)=>Object.keys(t).reduce((r,n)=>i(i({},r),{},{[n]:e(n,t[n])}),{})},56085:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},57282:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(84648),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},57841:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(76431),i=r(98150),a=r(29243),o=r(43574);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},59138:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(29862);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},59618:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},60324:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},60559:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},61545:(e,t,r)=>{"use strict";r.d(t,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(43209),i=r(17118),a=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.j)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},61645:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,U:()=>i});var n=(0,r(76067).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},64021:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},64267:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(76067).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},64279:(e,t,r)=>{"use strict";r.d(t,{qx:()=>I,IH:()=>z,s0:()=>x,gH:()=>b,SW:()=>B,YB:()=>M,bk:()=>F,Hj:()=>L,DW:()=>T,y2:()=>_,nb:()=>k,PW:()=>O,Mk:()=>N,$8:()=>E,yy:()=>S,Rh:()=>j,GF:()=>R,uM:()=>$,kr:()=>m,r4:()=>U,_L:()=>w,_f:()=>P});var n=r(10687),i=r.n(n),a=r(5664),o=r.n(a);function l(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var c=r(48657),u=r(22786);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function h(e){let t=[];return t.key=e,t}var d=r(22989),p=r(19335),y=r(20202);function v(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function g(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?v(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function m(e,t,r){return(0,d.uy)(e)||(0,d.uy)(t)?r:(0,d.vh)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var b=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var c=0;c<l;c++){var u=c>0?r[c-1].coordinate:r[l-1].coordinate,s=r[c].coordinate,f=c>=l-1?r[0].coordinate:r[c+1].coordinate,h=void 0;if((0,d.sA)(s-u)!==(0,d.sA)(f-s)){var p=[];if((0,d.sA)(f-s)===(0,d.sA)(i[1]-i[0])){h=f;var y=s+i[1]-i[0];p[0]=Math.min(y,(y+u)/2),p[1]=Math.max(y,(y+u)/2)}else{h=u;var v=f+i[1]-i[0];p[0]=Math.min(s,(v+s)/2),p[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(e>g[0]&&e<=g[1]||e>=p[0]&&e<=p[1]){({index:o}=r[c]);break}}else{var m=Math.min(u,f),b=Math.max(u,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[c]);break}}}else if(t){for(var x=0;x<l;x++)if(0===x&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x>0&&x<l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2&&e<=(t[x].coordinate+t[x+1].coordinate)/2||x===l-1&&e>(t[x].coordinate+t[x-1].coordinate)/2){({index:o}=t[x]);break}}return o},x=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,d.Et)(e[a]))return g(g({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,d.Et)(e[o]))return g(g({},e),{},{[o]:e[o]+(i||0)})}return e},w=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,O=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},j=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:c,categoricalDomain:u,tickCount:s,ticks:f,niceTicks:h,axisType:p}=e;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,v=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(v="angleAxis"===p&&a&&a.length>=2?2*(0,d.sA)(a[0]-a[1])*v:v,t&&(f||h))?(f||h||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+v,value:e,offset:v,index:t})).filter(e=>!(0,d.M8)(e.coordinate)):c&&u?u.map((e,t)=>({coordinate:o(e)+v,value:e,index:t,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+v,value:e,offset:v,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+v,value:n?n[e]:e,index:t,offset:v}))},M=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},P=(e,t)=>{if(!t||2!==t.length||!(0,d.Et)(t[0])||!(0,d.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,d.Et)(e[0])||e[0]<r)&&(i[0]=r),(!(0,d.Et)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},A={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=(0,d.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,c=0;o<r;++o)c+=e[o][n][1]||0;i[n][1]+=i[n][0]=-c/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var c=0,u=0,s=0;c<i;++c){for(var f=e[t[c]],h=f[o][1]||0,d=(h-(f[o-1][1]||0))/2,p=0;p<c;++p){var y=e[t[p]];d+=(y[o][1]||0)-(y[o-1][1]||0)}u+=h,s+=d*h}r[o-1][1]+=r[o-1][0]=a,u&&(a-=s/u)}r[o-1][1]+=r[o-1][0]=a,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,d.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},S=(e,t,r)=>{var n=A[r];return(function(){var e=(0,u.A)([]),t=s,r=l,n=f;function i(i){var a,o,l=Array.from(e.apply(this,arguments),h),u=l.length,s=-1;for(let e of i)for(a=0,++s;a<u;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=(0,c.A)(t(l));a<u;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,u.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,u.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,u.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+m(e,t,0)).order(s).offset(n)(e)};function E(e){return null==e?void 0:String(e)}function k(e){var{axis:t,ticks:r,bandSize:n,entry:i,index:a,dataKey:o}=e;if("category"===t.type){if(!t.allowDuplicatedCategory&&t.dataKey&&!(0,d.uy)(i[t.dataKey])){var l=(0,d.eP)(r,"value",i[t.dataKey]);if(l)return l.coordinate+n/2}return r[a]?r[a].coordinate+n/2:null}var c=m(i,(0,d.uy)(o)?t.dataKey:o);return(0,d.uy)(c)?null:t.scale(c)}var _=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=m(a,t.dataKey,t.scale.domain()[o]);return(0,d.uy)(l)?null:t.scale(l)-i/2+n},T=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},C=e=>{var t=e.flat(2).filter(d.Et);return[Math.min(...t),Math.max(...t)]},D=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],N=(e,t,r)=>{if(null!=e)return D(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=C((0,y.v)(n,t,r));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},z=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,L=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,l=1,c=a.length;l<c;l++){var u=a[l],s=a[l-1];o=Math.min((u.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function R(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return g(g({},t),{},{dataKey:r,payload:n,value:i,name:a})}function $(e,t){return e?String(e):"string"==typeof t?t:void 0}function U(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,p.yy)({x:e,y:t},n):null}var F=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return g(g(g({},n),(0,p.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:c}=n;return g(g(g({},n),(0,p.IZ)(n.cx,n.cy,l,c)),{},{angle:c,radius:l})}return{x:0,y:0}},B=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},64574:(e,t,r)=>{"use strict";function n(e){return null!=e.stackId&&null!=e.dataKey}r.d(t,{g:()=>n})},64635:(e,t,r)=>{"use strict";r.d(t,{m:()=>eo});var n=r(43210),i=r(51215),a=r(10687),o=r.n(a),l=r(49384),c=r(22989);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e){return Array.isArray(e)&&(0,c.vh)(e[0])&&(0,c.vh)(e[1])?e.join(" ~ "):e}var d=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:d,itemSorter:p,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,x=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),w=f({margin:0},a),O=!(0,c.uy)(g),j=O?g:"",M=(0,l.$)("recharts-default-tooltip",y),P=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(j=m(g,s)),n.createElement("div",u({className:M,style:x},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:P,style:w},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var e=(p?o()(s,p):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||d||h,{value:o,name:l}=e,u=o,p=l;if(a){var y=a(o,l,e,r,s);if(Array.isArray(y))[u,p]=y;else{if(null==y)return null;u=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-name"},p):null,(0,c.vh)(p)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},u),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},p="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:u,viewBoxDimension:s}=e;if(a&&(0,c.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),h=r[n]+i;if(t[n])return o[n]?f:h;var d=u[n];return null==d?0:o[n]?f<d?Math.max(h,d):Math.max(f,d):null==s?0:h+l>d+s?Math.max(f,d):Math.max(h,d)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class x extends n.PureComponent{constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:u,isAnimationActive:s,offset:f,position:h,reverseDirection:d,useTranslate3d:g,viewBox:b,wrapperStyle:x,lastBoundingBox:w,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:M,cssProperties:P}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:u,reverseDirection:s,tooltipBox:f,useTranslate3d:h,viewBox:d}=e;return{cssProperties:t=f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.width,viewBox:d,viewBoxDimension:d.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:u,reverseDirection:s,tooltipDimension:f.height,viewBox:d,viewBoxDimension:d.height}),useTranslate3d:h}):y,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(p,{["".concat(p,"-right")]:(0,c.Et)(r)&&t&&(0,c.Et)(t.x)&&r>=t.x,["".concat(p,"-left")]:(0,c.Et)(r)&&t&&(0,c.Et)(t.x)&&r<t.x,["".concat(p,"-bottom")]:(0,c.Et)(n)&&t&&(0,c.Et)(t.y)&&n>=t.y,["".concat(p,"-top")]:(0,c.Et)(n)&&t&&(0,c.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:h,reverseDirection:d,tooltipBox:{height:w.height,width:w.width},useTranslate3d:g,viewBox:b}),A=j?{}:m(m({transition:s&&e?"transform ".concat(r,"ms ").concat(i):void 0},P),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&u?"visible":"hidden",position:"absolute",top:0,left:0}),S=m(m({},A),{},{visibility:!this.state.dismissed&&e&&u?"visible":"hidden"},x);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:M,style:S,ref:O},a)}}var w=r(20237),O=r(23854),j=r.n(O),M=r(51426),P=r(24028),A=r(81888),S=r(54186),E=["x","y","top","left","width","height","className"];function k(){return(k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),C=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:u=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:u},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,E));return(0,c.Et)(t)&&(0,c.Et)(r)&&(0,c.Et)(o)&&(0,c.Et)(u)&&(0,c.Et)(i)&&(0,c.Et)(a)?n.createElement("path",k({},(0,S.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:T(t,r,o,u,i,a)})):null},D=r(71524),N=r(19335);function z(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,N.IZ)(t,r,n,i),(0,N.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var I=r(34955),L=r(43209),R=r(64279),$=r(69009),U=r(54526);function F(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?F(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):F(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var H=()=>(0,L.G)(U.D),K=()=>{var e=H(),t=(0,L.G)($.R4),r=(0,L.G)($.fl);return(0,R.Hj)(B(B({},e),{},{scale:r}),t)},G=r(21426);function Z(){return(Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function V(e){var t,r,i,{coordinate:a,payload:o,index:c,offset:u,tooltipAxisBandSize:s,layout:f,cursor:h,tooltipEventType:d,chartName:p}=e;if(!h||!a||"ScatterChart"!==p&&"axis"!==d)return null;if("ScatterChart"===p)r=a,i=C;else if("BarChart"===p)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:u.left+.5,y:"horizontal"===f?u.top+.5:a.y-t,width:"horizontal"===f?s:u.width-1,height:"horizontal"===f?u.height-1:s},i=D.M;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=z(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=I.h}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return z(t);else{var{cx:l,cy:c,innerRadius:u,outerRadius:s,angle:f}=t,h=(0,N.IZ)(l,c,u,f),d=(0,N.IZ)(l,c,s,f);n=h.x,i=h.y,a=d.x,o=d.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,u)},i=A.I;var x="object"==typeof h&&"className"in h?h.className:void 0,w=W(W(W(W({stroke:"#ccc",pointerEvents:"none"},u),r),(0,S.J9)(h,!1)),{},{payload:o,payloadIndex:c,className:(0,l.$)("recharts-tooltip-cursor",x)});return(0,n.isValidElement)(h)?(0,n.cloneElement)(h,w):(0,n.createElement)(i,w)}function Y(e){var t=K(),r=(0,M.W7)(),i=(0,M.WX)(),a=(0,G.fW)();return n.createElement(V,Z({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var X=r(97711);r(17118);var J=r(98009),Q=r(43075),ee=r(73865);function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function er(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?et(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function en(e){return e.dataKey}var ei=[],ea={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!w.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function eo(e){var t,r,a=(0,ee.e)(e,ea),{active:o,allowEscapeViewBox:l,animationDuration:c,animationEasing:u,content:s,filterNull:f,isAnimationActive:h,offset:p,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:w,shared:O,trigger:A,defaultIndex:S,portal:E,axisId:k}=a;(0,L.j)();var _="number"==typeof S?String(S):S,T=(0,M.sk)(),C=(0,P.$)(),D=(0,Q.Td)(O),{activeIndex:N,isActive:z}=(0,L.G)(e=>(0,G.yn)(e,D,A,_)),I=(0,L.G)(e=>(0,G.u9)(e,D,A,_)),R=(0,L.G)(e=>(0,G.BZ)(e,D,A,_)),$=(0,L.G)(e=>(0,G.dS)(e,D,A,_)),U=(0,X.X)(),F=null!=o?o:z,[B,H]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([I,F]),K="axis"===D?R:void 0;(0,J.m7)(D,A,$,K,N,F);var Z=null!=E?E:U;if(null==Z)return null;var q=null!=I?I:ei;F||(q=ei),f&&q.length&&(t=I.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),q=!0===y?j()(t,en):"function"==typeof y?j()(t,y):t);var W=q.length>0,V=n.createElement(x,{allowEscapeViewBox:l,animationDuration:c,animationEasing:u,isAnimationActive:h,active:F,coordinate:$,hasPayload:W,offset:p,position:v,reverseDirection:g,useTranslate3d:m,viewBox:T,wrapperStyle:b,lastBoundingBox:B,innerRef:H,hasPortalFromProps:!!E},(r=er(er({},a),{},{payload:q,label:K,active:F,coordinate:$,accessibilityLayer:C}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(d,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(V,Z),F&&n.createElement(Y,{cursor:w,tooltipEventType:D,coordinate:$,payload:I,index:N}))}},66777:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98150),i=r(26349),a=r(1640),o=r(1706);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},66861:(e,t,r)=>{"use strict";r.d(t,{zk:()=>o});var n=r(43210),i=["children"],a=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function o(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,i);return n.createElement(a.Provider,{value:r},t)}},67766:(e,t,r)=>{e.exports=r(43084).throttle},69009:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>es,eE:()=>ep,Xb:()=>ef,JG:()=>eg,A2:()=>eu,yn:()=>ey,gL:()=>ee,fl:()=>et,R4:()=>ei,n4:()=>D});var n=r(84648),i=r(85621),a=r(51426),o=r(64279),l=r(57282),c=r(97350),u=r(22989),s=r(53416),f=r(43075),h=r(28550),d=r(32520),p=r(97371),y=r(49396),v=r(86445),g=r(35034),m=r(77100),b=r(72198),x=r(78242),w=r(32751),O=r(26495),j=r(16950),M=r(54526),P=r(50380),A=r(64574),S=(0,n.Mz)([M.D,a.fz,i.um,c.iO,j.R],i.sr),E=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),k=(0,n.Mz)([j.R,O.M],i.eo),_=(0,n.Mz)([E,M.D,k],i.ec),T=(0,n.Mz)([_],e=>e.filter(A.g)),C=(0,n.Mz)([_],i.rj),D=(0,n.Mz)([C,l.LF],i.Nk),N=(0,n.Mz)([T,l.LF,M.D],P.A),z=(0,n.Mz)([D,M.D,_],i.fb),I=(0,n.Mz)([M.D],i.S5),L=(0,n.Mz)([_],e=>e.filter(A.g)),R=(0,n.Mz)([N,L,c.eC],i.MK),$=(0,n.Mz)([R,l.LF,j.R],i.pM),U=(0,n.Mz)([_],i.IO),F=(0,n.Mz)([D,M.D,U,i.CH,j.R],i.kz),B=(0,n.Mz)([i.Kr,j.R,O.M],i.P9),H=(0,n.Mz)([B,j.R],i.Oz),K=(0,n.Mz)([i.gT,j.R,O.M],i.P9),G=(0,n.Mz)([K,j.R],i.q),Z=(0,n.Mz)([i.$X,j.R,O.M],i.P9),q=(0,n.Mz)([Z,j.R],i.bb),W=(0,n.Mz)([H,q,G],i.yi),V=(0,n.Mz)([M.D,I,$,F,W,a.fz,j.R],i.wL),Y=(0,n.Mz)([M.D,a.fz,D,z,c.eC,j.R,V],i.tP),X=(0,n.Mz)([Y,M.D,S],i.xp),J=(0,n.Mz)([M.D,Y,X,j.R],i.g1),Q=e=>{var t=(0,j.R)(e),r=(0,O.M)(e);return(0,i.D5)(e,t,r,!1)},ee=(0,n.Mz)([M.D,Q],s.I),et=(0,n.Mz)([M.D,S,J,ee],i.Qn),er=(0,n.Mz)([a.fz,z,M.D,j.R],i.tF),en=(0,n.Mz)([a.fz,z,M.D,j.R],i.iv),ei=(0,n.Mz)([a.fz,M.D,S,et,Q,er,en,j.R],(e,t,r,n,i,a,l,c)=>{if(t){var{type:s}=t,f=(0,o._L)(e,c);if(n){var h="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,d="category"===s&&n.bandwidth?n.bandwidth()/h:0;return(d="angleAxis"===c&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,u.sA)(i[0]-i[1])*d:d,f&&l)?l.map((e,t)=>({coordinate:n(e)+d,value:e,index:t,offset:d})):n.domain().map((e,t)=>({coordinate:n(e)+d,value:a?a[e]:e,index:t,offset:d}))}}}),ea=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),eo=e=>e.tooltip.settings.trigger,el=e=>e.tooltip.settings.defaultIndex,ec=(0,n.Mz)([x.J,ea,eo,el],d.i),eu=(0,n.Mz)([ec,D],p.P),es=(0,n.Mz)([ei,eu],h.E),ef=(0,n.Mz)([ec],e=>{if(e)return e.dataKey}),eh=(0,n.Mz)([x.J,ea,eo,el],m.q),ed=(0,n.Mz)([v.Lp,v.A$,a.fz,g.HZ,ei,el,eh,b.x],y.o),ep=(0,n.Mz)([ec,ed],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),ey=(0,n.Mz)([ec],e=>e.active),ev=(0,n.Mz)([eh,eu,l.LF,M.D,es,b.x,ea],w.N),eg=(0,n.Mz)([ev],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},69404:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(175),i=r(91653),a=r(91428),o=r(27469),l=r(1706);t.isEqualWith=function(e,t,r){return function e(t,r,c,u,s,f,h){let d=h(t,r,c,u,s,f);if(void 0!==d)return d;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,u,s){if(Object.is(r,c))return!0;let f=a.getTag(r),h=a.getTag(c);if(f===o.argumentsTag&&(f=o.objectTag),h===o.argumentsTag&&(h=o.objectTag),f!==h)return!1;switch(f){case o.stringTag:return r.toString()===c.toString();case o.numberTag:{let e=r.valueOf(),t=c.valueOf();return l.eq(e,t)}case o.booleanTag:case o.dateTag:case o.symbolTag:return Object.is(r.valueOf(),c.valueOf());case o.regexpTag:return r.source===c.source&&r.flags===c.flags;case o.functionTag:return r===c}let d=(u=u??new Map).get(r),p=u.get(c);if(null!=d&&null!=p)return d===c;u.set(r,c),u.set(c,r);try{switch(f){case o.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,u,s))return!1;return!0;case o.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,u,s));if(-1===o)return!1;n.splice(o,1)}return!0}case o.arrayTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:case o.bigUint64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.bigInt64ArrayTag:case o.float32ArrayTag:case o.float64ArrayTag:if("undefined"!=typeof Buffer&&Buffer.isBuffer(r)!==Buffer.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,u,s))return!1;return!0;case o.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),u,s);case o.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),u,s);case o.errorTag:return r.name===c.name&&r.message===c.message;case o.objectTag:{if(!(t(r.constructor,c.constructor,u,s)||n.isPlainObject(r)&&n.isPlainObject(c)))return!1;let a=[...Object.keys(r),...i.getSymbols(r)],o=[...Object.keys(c),...i.getSymbols(c)];if(a.length!==o.length)return!1;for(let t=0;t<a.length;t++){let n=a[t],i=r[n];if(!Object.hasOwn(c,n))return!1;let o=c[n];if(!e(i,o,n,r,c,u,s))return!1}return!0}default:return!1}}finally{u.delete(r),u.delete(c)}}(t,r,f,h)}(e,t,void 0,void 0,void 0,void 0,r)}},70742:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71337:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(66777),i=r(42750);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},71392:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>u,a6:()=>s,h4:()=>G,jM:()=>K,ss:()=>B});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function u(e){return!!e&&!!e[o]}function s(e){return!!e&&(h(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||g(e)||m(e))}var f=Object.prototype.constructor.toString();function h(e){if(!e||"object"!=typeof e)return!1;let t=c(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function d(e,t){0===p(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function p(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:g(e)?2:3*!!m(e)}function y(e,t){return 2===p(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t,r){let n=p(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function g(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function x(e,t){if(g(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=h(e);if(!0!==t&&("class_only"!==t||r)){let t=c(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(c(e),t)}}function w(e,t=!1){return j(e)||u(e)||!s(e)||(p(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>w(t,!0))),e}function O(){l(2)}function j(e){return Object.isFrozen(e)}var M={};function P(e){let t=M[e];return t||l(0,e),t}function A(e,t){t&&(P("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function S(e){E(e),e.drafts_.forEach(_),e.drafts_=null}function E(e){e===n&&(n=e.parent_)}function k(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function _(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(S(t),l(4)),s(e)&&(e=C(t,e),t.parent_||N(t,e)),t.patches_&&P("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),S(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(j(t))return t;let n=t[o];if(!n)return d(t,(i,a)=>D(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return N(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),d(i,(i,o)=>D(e,n,t,i,o,r,a)),N(e,t,!1),r&&e.patches_&&P("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function D(e,t,r,n,i,a,o){if(u(i)){let o=C(e,i,a&&t&&3!==t.type_&&!y(t.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!u(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!j(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(e,i)}}function N(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&w(t,r)}var z={get(e,t){if(t===o)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,a=t;let o=R(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!s(l)?l:l===L(e.base_,t)?(U(e),e.copy_[t]=F(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=R(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=L(b(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;U(e),$(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==L(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,U(e),$(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>c(e.base_),setPrototypeOf(){l(12)}},I={};function L(e,t){let r=e[o];return(r?b(r):e)[t]}function R(e,t){if(!(t in e))return;let r=c(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=c(r)}}function $(e){!e.modified_&&(e.modified_=!0,e.parent_&&$(e.parent_))}function U(e){e.copy_||(e.copy_=x(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function F(e,t){let r=g(e)?P("MapSet").proxyMap_(e,t):m(e)?P("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=z;r&&(a=[i],o=I);let{revoke:l,proxy:c}=Proxy.revocable(a,o);return i.draft_=c,i.revoke_=l,c}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function B(e){return u(e)||l(10,e),function e(t){let r;if(!s(t)||j(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=x(t,n.scope_.immer_.useStrictShallowCopy_)}else r=x(t,!0);return d(r,(t,n)=>{v(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}d(z,(e,t)=>{I[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),I.deleteProperty=function(e,t){return I.set.call(this,e,t,void 0)},I.set=function(e,t,r){return z.set.call(this,e[0],t,r,e[0])};var H=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(e)){let i=k(this),a=F(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?S(i):E(i)}return A(i,r),T(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&w(n,!0),r){let t=[],i=[];P("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){s(e)||l(8),u(e)&&(e=B(e));let t=k(this),r=F(e,void 0);return r[o].isManual_=!0,E(t),r}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||l(9);let{scope_:n}=r;return A(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=P("Patches").applyPatches_;return u(e)?n(e,t):this.produce(e,e=>n(e,t))}},K=H.produce;function G(e){return e}H.produceWithPatches.bind(H),H.setAutoFreeze.bind(H),H.setUseStrictShallowCopy.bind(H),H.applyPatches.bind(H),H.createDraft.bind(H),H.finishDraft.bind(H)},71524:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(43210),i=r(49384),a=r(54186),o=r(73865),l=r(52457);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,c=r>=0?1:-1,u=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(u,",").concat(e+c*s[0],",").concat(t)),a+="L ".concat(e+r-c*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(u,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(u,",\n        ").concat(e+r-c*s[2],",").concat(t+n)),a+="L ".concat(e+c*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(u,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var h=Math.min(o,i);a="M ".concat(e,",").concat(t+l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(e+c*h,",").concat(t,"\n            L ").concat(e+r-c*h,",").concat(t,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(e+r,",").concat(t+l*h,"\n            L ").concat(e+r,",").concat(t+n-l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(e+r-c*h,",").concat(t+n,"\n            L ").concat(e+c*h,",").concat(t+n,"\n            A ").concat(h,",").concat(h,",0,0,").concat(u,",").concat(e,",").concat(t+n-l*h," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.e)(e,s),r=(0,n.useRef)(null),[f,h]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&h(e)}catch(e){}},[]);var{x:d,y:p,width:y,height:v,radius:g,className:m}=t,{animationEasing:b,animationDuration:x,animationBegin:w,isAnimationActive:O,isUpdateAnimationActive:j}=t;if(d!==+d||p!==+p||y!==+y||v!==+v||0===y||0===v)return null;var M=(0,i.$)("recharts-rectangle",m);return j?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:v,x:d,y:p},to:{width:y,height:v,x:d,y:p},duration:x,animationEasing:b,isActive:j},e=>{var{width:i,height:o,x:s,y:h}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:w,duration:x,isActive:O,easing:b},n.createElement("path",c({},(0,a.J9)(t,!0),{className:M,d:u(s,h,i,o,g),ref:r})))}):n.createElement("path",c({},(0,a.J9)(t,!0),{className:M,d:u(d,p,y,v,g)}))}},71579:(e,t,r)=>{"use strict";r.d(t,{u:()=>j});var n=r(43210),i=r(5664),a=r.n(i),o=r(49384);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var c=r(98986),u=r(23561),s=r(97633),f=r(22989),h=r(4057),d=r(54186),p=r(17874),y=r(99857),v=["viewBox"],g=["viewBox"];function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){O(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function O(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class j extends n.Component{constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}shouldComponentUpdate(e,t){var{viewBox:r}=e,n=w(e,v),i=this.props,{viewBox:a}=i,o=w(i,g);return!l(r,a)||!l(n,o)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:c,width:u,height:s,orientation:h,tickSize:d,mirror:p,tickMargin:y}=this.props,v=p?-1:1,g=e.tickSize||d,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(h){case"top":t=r=e.coordinate,o=(n=(i=c+!p*s)-v*g)-v*y,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=l+!p*u)-v*g)-v*y,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=l+p*u)+v*g)+v*y,o=m;break;default:t=r=e.coordinate,o=(n=(i=c+p*s)+v*g)+v*y,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:c,axisLine:u}=this.props,s=x(x(x({},(0,d.J9)(this.props,!1)),(0,d.J9)(u,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!c||"bottom"===l&&c);s=x(x({},s),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var h=+("left"===l&&!c||"right"===l&&c);s=x(x({},s),{},{x1:e+h*r,y1:t,x2:e+h*r,y2:t+i})}return n.createElement("line",m({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(u,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,x(x({},t),{},{className:a}));else if("function"==typeof e)i=e(x(x({},t),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),i=n.createElement(u.E,m({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:u,tickFormatter:s,unit:f,padding:v}=this.props,g=(0,p.f)(x(x({},this.props),{},{ticks:r}),e,t),b=this.getTickTextAnchor(),w=this.getTickVerticalAnchor(),O=(0,y.u)(this.props),M=(0,d.J9)(u,!1),P=x(x({},O),{},{fill:"none"},(0,d.J9)(i,!1)),A=g.map((e,t)=>{var{line:r,tick:d}=this.getTickLineCoord(e),p=x(x(x(x({textAnchor:b,verticalAnchor:w},O),{},{stroke:"none",fill:l},M),d),{},{index:t,payload:e,visibleTicksCount:g.length,tickFormatter:s,padding:v});return n.createElement(c.W,m({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,h.XC)(this.props,e,t)),i&&n.createElement("line",m({},P,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),u&&j.renderTickItem(u,p,"".concat("function"==typeof s?s(e.value,t):e.value).concat(f||"")))});return A.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},A):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(c.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s.J.renderCallByParent(this.props))}}O(j,"displayName","CartesianAxis"),O(j,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},71680:(e,t,r)=>{"use strict";r.d(t,{s:()=>a}),r(43210);var n=r(83409);r(52693);var i=r(43209);function a(e){var{layout:t,width:r,height:a,margin:o}=e;return(0,i.j)(),(0,n.r)(),null}},72198:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},73865:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},74173:(e,t,r)=>{"use strict";r.d(t,{yl:()=>u});var n=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],i=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),a=(e,t)=>r=>i(n(e,t),r),o=(e,t)=>r=>i([...n(e,t).map((e,t)=>e*t).slice(1),0],r),l=function(){for(var e,t,r,n,i=arguments.length,l=Array(i),c=0;c<i;c++)l[c]=arguments[c];if(1===l.length)switch(l[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var u=l[0].split("(");"cubic-bezier"===u[0]&&4===u[1].split(")")[0].split(",").length&&([e,r,t,n]=u[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===l.length&&([e,r,t,n]=l);var s=a(e,t),f=a(r,n),h=o(e,t),d=e=>e>1?1:e<0?0:e,p=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=d(r-i/a)}return f(r)};return p.isStepper=!1,p},c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},u=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return l(e);case"spring":return c();default:if("cubic-bezier"===e.split("(")[0])return l(e)}return"function"==typeof e?e:null}},74838:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(42066),i=r(52371);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},74875:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var n=r(55716);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r)=>e+(t-e)*r,l=e=>{var{from:t,to:r}=e;return t!==r},c=(e,t,r)=>{var i=(0,n.s8)((t,r)=>{if(l(r)){var[n,i]=e(r.from,r.to,r.velocity);return a(a({},r),{},{from:n,velocity:i})}return r},t);return r<1?(0,n.s8)((e,t)=>l(t)?a(a({},t),{},{velocity:o(t.velocity,i[e].velocity,r),from:o(t.from,i[e].from,r)}):t,t):c(e,i,r-1)};let u=(e,t,r,i,u,s)=>{var f=(0,n.mP)(e,t);return!0===r.isStepper?function(e,t,r,i,o,u){var s,f=i.reduce((r,n)=>a(a({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),h=()=>(0,n.s8)((e,t)=>t.from,f),d=()=>!Object.values(f).filter(l).length,p=null,y=n=>{s||(s=n);var i=(n-s)/r.dt;f=c(r,f,i),o(a(a(a({},e),t),h())),s=n,d()||(p=u.setTimeout(y))};return()=>(p=u.setTimeout(y),()=>{p()})}(e,t,r,f,u,s):function(e,t,r,i,l,c,u){var s,f=null,h=l.reduce((r,n)=>a(a({},r),{},{[n]:[e[n],t[n]]}),{}),d=l=>{s||(s=l);var p=(l-s)/i,y=(0,n.s8)((e,t)=>o(...t,r(p)),h);if(c(a(a(a({},e),t),y)),p<1)f=u.setTimeout(d);else{var v=(0,n.s8)((e,t)=>o(...t,r(1)),h);c(a(a(a({},e),t),v))}};return()=>(f=u.setTimeout(d),()=>{f()})}(e,t,r,i,f,u,s)}},75446:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(37586),i=r(28382),a=r(66777);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},75601:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},75787:(e,t,r)=>{"use strict";r.d(t,{p:()=>a,v:()=>o});var n=r(43210),i=r(43209);function a(e){return(0,i.j)(),(0,n.useRef)(null),null}function o(e){return(0,i.j)(),null}r(30802)},76021:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(95819),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},76067:(e,t,r)=>{"use strict";r.d(t,{U1:()=>m,VP:()=>u,Nc:()=>ey,Z0:()=>T});var n=r(11208);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(71392),l="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)};"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var c=e=>e&&"function"==typeof e.match;function u(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(ew(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}function s(e){return["type","payload","error","meta"].indexOf(e)>-1}var f=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function h(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function d(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var p=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new f;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},y=e=>t=>{setTimeout(t,e)},v=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,c="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===e.type?e.queueNotification:y(e.timeout),u=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,c(u)),n.dispatch(e)}finally{i=!0}}})},g=e=>function(t){let{autoBatch:r=!0}=t??{},n=new f(e);return r&&n.push(v("object"==typeof r?r:void 0)),n};function m(e){let t,r,i=p(),{reducer:a,middleware:o,devTools:c=!0,duplicateMiddlewareCheck:u=!0,preloadedState:s,enhancers:f}=e||{};if("function"==typeof a)t=a;else if((0,n.Qd)(a))t=(0,n.HY)(a);else throw Error(ew(1));r="function"==typeof o?o(i):i();let h=n.Zz;c&&(h=l({trace:!1,..."object"==typeof c&&c}));let d=g((0,n.Tw)(...r)),y=h(..."function"==typeof f?f(d):d());return(0,n.y$)(t,s,y)}function b(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(ew(28));if(n in r)throw Error(ew(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var x=(e,t)=>c(e)?e.match(t):e(t);function w(...e){return t=>e.some(e=>x(e,t))}var O=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},j=["name","message","stack","code"],M=class{constructor(e,t){this.payload=e,this.meta=t}_type},P=class{constructor(e,t){this.payload=e,this.meta=t}_type},A=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of j)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},S="External signal was aborted";function E(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var k=Symbol.for("rtk-slice-createasyncthunk"),_=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(_||{}),T=function({creators:e}={}){let t=e?.asyncThunk?.[k];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(ew(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),c={},s={},f={},p=[],y={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(ew(12));if(r in s)throw Error(ew(13));return s[r]=t,y},addMatcher:(e,t)=>(p.push({matcher:e,reducer:t}),y),exposeAction:(e,t)=>(f[e]=t,y),exposeCaseReducer:(e,t)=>(c[e]=t,y)};function v(){let[t={},r=[],n]="function"==typeof e.extraReducers?b(e.extraReducers):[e.extraReducers],i={...t,...s};return function(e,t){let r,[n,i,a]=b(t);if("function"==typeof e)r=()=>h(e());else{let t=h(e);r=()=>t}function l(e=r(),t){let c=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===c.filter(e=>!!e).length&&(c=[a]),c.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of p)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(ew(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:c,settled:u,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),c&&n.addCase(f.rejected,c),u&&n.addMatcher(f.settled,u),n.exposeCaseReducer(t,{fulfilled:o||C,pending:l||C,rejected:c||C,settled:u||C})}(o,i,y,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(ew(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?u(e,o):u(e))}(o,i,y)});let g=e=>e,m=new Map,x=new WeakMap;function w(e,t){return r||(r=v()),r(e,t)}function O(){return r||(r=v()),r.getInitialState()}function j(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=d(x,n,O)),i}function i(t=g){let n=d(m,r,()=>new WeakMap);return d(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>d(x,t,O),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let M={name:n,reducer:w,actions:f,caseReducers:c,getInitialState:O,...j(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:w},r),{...M,...j(n,!0)}}};return M}}();function C(){}function D(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(s)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function N(e,t){return t(e)}function z(e){return Array.isArray(e)||(e=Object.values(e)),e}var I="listener",L="completed",R="cancelled",$=`task-${R}`,U=`task-${L}`,F=`${I}-${R}`,B=`${I}-${L}`,H=class{constructor(e){this.code=e,this.message=`task ${R} (reason: ${e})`}name="TaskAbortError";message},K=(e,t)=>{if("function"!=typeof e)throw TypeError(ew(32))},G=()=>{},Z=(e,t=G)=>(e.catch(t),e),q=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),W=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},V=e=>{if(e.aborted){let{reason:t}=e;throw new H(t)}};function Y(e,t){let r=G;return new Promise((n,i)=>{let a=()=>i(new H(e.reason));if(e.aborted)return void a();r=q(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=G})}var X=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof H?"cancelled":"rejected",error:e}}finally{t?.()}},J=e=>t=>Z(Y(e,t).then(t=>(V(e),t))),Q=e=>{let t=J(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:ee}=Object,et={},er="listenerMiddleware",en=(e,t)=>{let r=t=>q(e,()=>W(t,e.reason));return(n,i)=>{K(n,"taskExecutor");let a=new AbortController;r(a);let o=X(async()=>{V(e),V(a.signal);let t=await n({pause:J(a.signal),delay:Q(a.signal),signal:a.signal});return V(a.signal),t},()=>W(a,U));return i?.autoJoin&&t.push(o.catch(G)),{result:J(e)(o),cancel(){W(a,$)}}}},ei=(e,t)=>{let r=async(r,n)=>{V(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await Y(t,Promise.race(a));return V(t),e}finally{i()}};return(e,t)=>Z(r(e,t))},ea=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=u(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(ew(21));return K(a,"options.listener"),{predicate:i,type:t,effect:a}},eo=ee(e=>{let{type:t,predicate:r,effect:n}=ea(e);return{id:O(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(ew(22))}}},{withTypes:()=>eo}),el=(e,t)=>{let{type:r,effect:n,predicate:i}=ea(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},ec=e=>{e.pending.forEach(e=>{W(e,F)})},eu=e=>()=>{e.forEach(ec),e.clear()},es=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},ef=ee(u(`${er}/add`),{withTypes:()=>ef}),eh=u(`${er}/removeAll`),ed=ee(u(`${er}/remove`),{withTypes:()=>ed}),ep=(...e)=>{console.error(`${er}/error`,...e)},ey=(e={})=>{let t=new Map,{extra:r,onError:i=ep}=e;K(i,"onError");let a=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&ec(e)}),o=e=>a(el(t,e)??eo(e));ee(o,{withTypes:()=>o});let l=e=>{let r=el(t,e);return r&&(r.unsubscribe(),e.cancelActive&&ec(r)),!!r};ee(l,{withTypes:()=>l});let c=async(e,n,a,l)=>{let c=new AbortController,u=ei(o,c.signal),s=[];try{e.pending.add(c),await Promise.resolve(e.effect(n,ee({},a,{getOriginalState:l,condition:(e,t)=>u(e,t).then(Boolean),take:u,delay:Q(c.signal),pause:J(c.signal),extra:r,signal:c.signal,fork:en(c.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==c&&(W(e,F),r.delete(e))})},cancel:()=>{W(c,F),e.pending.delete(c)},throwIfCancelled:()=>{V(c.signal)}})))}catch(e){e instanceof H||es(i,e,{raisedBy:"effect"})}finally{await Promise.all(s),W(c,B),e.pending.delete(c)}},u=eu(t);return{middleware:e=>r=>a=>{let s;if(!(0,n.ve)(a))return r(a);if(ef.match(a))return o(a.payload);if(eh.match(a))return void u();if(ed.match(a))return l(a.payload);let f=e.getState(),h=()=>{if(f===et)throw Error(ew(23));return f};try{if(s=r(a),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(a,r,f)}catch(e){t=!1,es(i,e,{raisedBy:"predicate"})}t&&c(n,a,e,h)}}}finally{f=et}return s},startListening:o,stopListening:l,clearListeners:u}},ev=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,eg=Symbol.for("rtk-state-proxy-original"),em=e=>!!e&&!!e[eg],eb=new WeakMap,ex={};function ew(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},76431:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},77100:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},77357:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},77400:(e,t,r)=>{"use strict";r.d(t,{q:()=>i});var n=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"];function i(e){return"string"==typeof e&&n.includes(e)}},78200:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},78242:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},81888:(e,t,r)=>{"use strict";r.d(t,{I:()=>H});var n=r(43210);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function l(e){this._context=e}function c(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},c.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class u{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function h(e){return new f(e)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function d(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function p(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function v(e){this._context=e}function g(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function x(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function w(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,p(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,p(this,r=d(this,e,t)),r);break;default:y(this,this._t0,r=d(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(e,t){v.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=x(e),i=x(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},w.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(48657),j=r(22786),M=r(15606);function P(e){return e[0]}function A(e){return e[1]}function S(e,t){var r=(0,j.A)(!0),n=null,i=h,a=null,o=(0,M.i)(l);function l(l){var c,u,s,f=(l=(0,O.A)(l)).length,h=!1;for(null==n&&(a=i(s=o())),c=0;c<=f;++c)!(c<f&&r(u=l[c],c,l))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(u,c,l),+t(u,c,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?P:(0,j.A)(e),t="function"==typeof t?t:void 0===t?A:(0,j.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,j.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function E(e,t,r){var n=null,i=(0,j.A)(!0),a=null,o=h,l=null,c=(0,M.i)(u);function u(u){var s,f,h,d,p,y=(u=(0,O.A)(u)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(p=c())),s=0;s<=y;++s){if(!(s<y&&i(d=u[s],s,u))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),h=s-1;h>=f;--h)l.point(g[h],m[h]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(d,s,u),m[s]=+t(d,s,u),l.point(n?+n(d,s,u):g[s],r?+r(d,s,u):m[s]))}if(p)return l=null,p+""||null}function s(){return S().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?P:(0,j.A)(+e),t="function"==typeof t?t:void 0===t?(0,j.A)(0):(0,j.A)(+t),r="function"==typeof r?r:void 0===r?A:(0,j.A)(+r),u.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),n=null,u):e},u.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),u):e},u.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,j.A)(+e),u):n},u.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),r=null,u):t},u.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),u):t},u.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,j.A)(+e),u):r},u.lineX0=u.lineY0=function(){return s().x(e).y(t)},u.lineY1=function(){return s().x(e).y(r)},u.lineX1=function(){return s().x(n).y(t)},u.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,j.A)(!!e),u):i},u.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),u):o},u.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),u):a},u}var k=r(49384),_=r(4057),T=r(22989),C=r(12128),D=r(99857);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function I(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new c(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new u(e,!0)},curveBumpY:function(e){return new u(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:h,curveMonotoneX:function(e){return new v(e)},curveMonotoneY:function(e){return new g(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new w(e,.5)},curveStepAfter:function(e){return new w(e,1)},curveStepBefore:function(e){return new w(e,0)}},R=e=>(0,C.H)(e.x)&&(0,C.H)(e.y),$=e=>e.x,U=e=>e.y,F=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,T.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?L["".concat(r).concat("vertical"===t?"Y":"X")]:L[r]||h},B=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=F(r,a),c=o?n.filter(R):n;if(Array.isArray(i)){var u=o?i.filter(e=>R(e)):i,s=c.map((e,t)=>I(I({},e),{},{base:u[t]}));return(t="vertical"===a?E().y(U).x1($).x0(e=>e.base.x):E().x($).y1(U).y0(e=>e.base.y)).defined(R).curve(l),t(s)}return(t="vertical"===a&&(0,T.Et)(i)?E().y(U).x1($).x0(i):(0,T.Et)(i)?E().x($).y1(U).y0(i):S().x($).y(U)).defined(R).curve(l),t(c)},H=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?B(e):i;return n.createElement("path",N({},(0,D.u)(e),(0,_._U)(e),{className:(0,k.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},83409:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(43210),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},84071:(e,t,r)=>{"use strict";r.d(t,{L:()=>R});var n=r(43210),i=r(51426),a=r(24028),o=r(83409),l=r(49384),c=r(54186),u=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:i,height:a,viewBox:o,className:f,style:h,title:d,desc:p}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,u),v=o||{width:i,height:a,x:0,y:0},g=(0,l.$)("recharts-surface",f);return n.createElement("svg",s({},(0,c.J9)(y,!0,"svg"),{className:g,width:i,height:a,style:h,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:t}),n.createElement("title",null,d),n.createElement("desc",null,p),r)}),h=r(43209),d=r(94728),p=r(12128),y=["children"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={width:"100%",height:"100%"},m=(0,n.forwardRef)((e,t)=>{var r,o,l=(0,i.yi)(),c=(0,i.rY)(),u=(0,a.$)();if(!(0,p.F)(l)||!(0,p.F)(c))return null;var{children:s,otherAttributes:h,title:d,desc:y}=e;return r="number"==typeof h.tabIndex?h.tabIndex:u?0:void 0,o="string"==typeof h.role?h.role:u?"application":void 0,n.createElement(f,v({},h,{title:d,desc:y,role:o,tabIndex:r,width:l,height:c,style:g,ref:t}),s)}),b=e=>{var{children:t}=e,r=(0,h.G)(d.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(f,{width:i,height:a,x:l,y:o},t)},x=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y);return(0,o.r)()?n.createElement(b,null,r):n.createElement(m,v({ref:t},i),r)}),w=r(17118),O=r(85407),j=r(98009),M=r(11281),P=r(86445);r(52693);var A=r(44919),S=r(34258),E=r(97711),k=(0,n.createContext)(null);function _(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:c,onDoubleClick:u,onMouseDown:s,onMouseEnter:f,onMouseLeave:d,onMouseMove:p,onMouseUp:y,onTouchEnd:v,onTouchMove:g,onTouchStart:m,style:b,width:x}=e,T=(0,h.j)(),[C,D]=(0,n.useState)(null),[N,z]=(0,n.useState)(null);(0,j.l3)();var I=function(){(0,h.j)();var[e,t]=(0,n.useState)(null);return(0,h.G)(P.et),t}(),L=(0,n.useCallback)(e=>{I(e),"function"==typeof t&&t(e),D(e),z(e)},[I,t,D,z]),R=(0,n.useCallback)(e=>{T((0,O.ky)(e)),T((0,A.y)({handler:o,reactEvent:e}))},[T,o]),$=(0,n.useCallback)(e=>{T((0,O.dj)(e)),T((0,A.y)({handler:f,reactEvent:e}))},[T,f]),U=(0,n.useCallback)(e=>{T((0,w.xS)()),T((0,A.y)({handler:d,reactEvent:e}))},[T,d]),F=(0,n.useCallback)(e=>{T((0,O.dj)(e)),T((0,A.y)({handler:p,reactEvent:e}))},[T,p]),B=(0,n.useCallback)(()=>{T((0,M.Ru)())},[T]),H=(0,n.useCallback)(e=>{T((0,M.uZ)(e.key))},[T]),K=(0,n.useCallback)(e=>{T((0,A.y)({handler:c,reactEvent:e}))},[T,c]),G=(0,n.useCallback)(e=>{T((0,A.y)({handler:u,reactEvent:e}))},[T,u]),Z=(0,n.useCallback)(e=>{T((0,A.y)({handler:s,reactEvent:e}))},[T,s]),q=(0,n.useCallback)(e=>{T((0,A.y)({handler:y,reactEvent:e}))},[T,y]),W=(0,n.useCallback)(e=>{T((0,A.y)({handler:m,reactEvent:e}))},[T,m]),V=(0,n.useCallback)(e=>{T((0,S.e)(e)),T((0,A.y)({handler:g,reactEvent:e}))},[T,g]),Y=(0,n.useCallback)(e=>{T((0,A.y)({handler:v,reactEvent:e}))},[T,v]);return n.createElement(E.$.Provider,{value:C},n.createElement(k.Provider,{value:N},n.createElement("div",{className:(0,l.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?_(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):_(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:x,height:a},b),onClick:R,onContextMenu:K,onDoubleClick:G,onFocus:B,onKeyDown:H,onMouseDown:Z,onMouseEnter:$,onMouseLeave:U,onMouseMove:F,onMouseUp:q,onTouchEnd:Y,onTouchMove:V,onTouchStart:W,ref:L},r)))}),C=r(22989),D=r(27934),N=(0,n.createContext)(void 0),z=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,C.NF)("recharts"),"-clip")),i=(0,D.oM)();if(null==i)return null;var{x:a,y:o,width:l,height:c}=i;return n.createElement(N.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:c,width:l}))),t)},I=r(99857),L=["children","className","width","height","style","compact","title","desc"],R=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,width:a,height:o,style:l,compact:c,title:u,desc:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,L),h=(0,I.u)(f);return c?n.createElement(x,{otherAttributes:h,title:u,desc:s},r):n.createElement(T,{className:i,style:l,width:a,height:o,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(x,{otherAttributes:h,title:u,desc:s,ref:t},n.createElement(z,null,r)))})},84648:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>w});var n=e=>Array.isArray(e)?e:[e],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=l;constructor(e,t=l){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function l(e,t){return e===t}function c(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var u=(e,t)=>!1;function s(){return function(e,t=l){return new o(null,t)}(0,u)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=s()),c(t)};Symbol();var h=0,d=Object.getPrototypeOf({}),p=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,y);tag=s();tags={};children={};collectionTag=null;id=h++},y={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in d)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new v(e):new p(e)}(n)),r.tag&&c(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=s()).value=n),c(r),n}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},v=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],g);tag=s();tags={};children={};collectionTag=null;id=h++},g={get:([e],t)=>("length"===t&&f(e),y.get(e,t)),ownKeys:([e])=>y.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>y.getOwnPropertyDescriptor(e,t),has:([e],t)=>y.has(e,t)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function x(e,t={}){let r,n=b(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}}let c=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new m(t):t}return c.s=1,c.v=t,t}return o.clearCache=()=>{n=b(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var w=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},l=e.pop();"object"==typeof l&&(o=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:c,memoizeOptions:u=[],argsMemoize:s=x,argsMemoizeOptions:f=[],devModeChecks:h={}}={...r,...o},d=n(u),p=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),v=c(function(){return i++,l.apply(null,arguments)},...d);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e)},...p),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:c,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(x),O=Object.assign((e,t=w)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>O})},85168:(e,t,r)=>{"use strict";r.d(t,{d:()=>C});var n=r(43210),i=r(10521),a=r(22989),o=r(64279),l=r(17874),c=r(71579),u=r(51426),s=r(85621),f=r(43209),h=r(83409),d=r(73865),p=r(99857),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function x(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:c}=e;return n.createElement("rect",{x:i,y:a,ry:c,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function M(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:o,y2:l,key:c}=t,u=O(t,y),s=(0,p.u)(u),{offset:f}=s,h=O(s,v);r=n.createElement("line",w({},h,{x1:i,y1:a,x2:o,y2:l,fill:"none",key:c}))}return r}function P(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=O(e,g),u=a.map((e,n)=>M(i,x(x({},c),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},u)}function A(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,c=O(e,m),u=a.map((e,n)=>M(i,x(x({},c),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},u)}function S(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:c,horizontal:u=!0}=e;if(!u||!t||!t.length)return null;var s=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,c)=>{var u=s[c+1]?s[c+1]-e:a+l-e;if(u<=0)return null;var f=c%t.length;return n.createElement("rect",{key:"react-".concat(c),y:e,x:i,height:u,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function E(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:c,verticalPoints:u}=e;if(!t||!r||!r.length)return null;var s=u.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var u=s[t+1]?s[t+1]-e:a+l-e;if(u<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:u,height:c,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var k=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,o.PW)((0,l.f)(x(x(x({},c.u.defaultProps),r),{},{ticks:(0,o.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},_=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,o.PW)((0,l.f)(x(x(x({},c.u.defaultProps),r),{},{ticks:(0,o.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function C(e){var t=(0,u.yi)(),r=(0,u.rY)(),o=(0,u.W7)(),l=x(x({},(0,d.e)(e,T)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:c,yAxisId:p,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:M}=l,C=(0,h.r)(),D=(0,f.G)(e=>(0,s.ZB)(e,"xAxis",c,C)),N=(0,f.G)(e=>(0,s.ZB)(e,"yAxis",p,C));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var z=l.verticalCoordinatesGenerator||k,I=l.horizontalCoordinatesGenerator||_,{horizontalPoints:L,verticalPoints:R}=l;if((!L||!L.length)&&"function"==typeof I){var $=O&&O.length,U=I({yAxis:N?x(x({},N),{},{ticks:$?O:N.ticks}):void 0,width:t,height:r,offset:o},!!$||b);(0,i.R)(Array.isArray(U),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof U,"]")),Array.isArray(U)&&(L=U)}if((!R||!R.length)&&"function"==typeof z){var F=M&&M.length,B=z({xAxis:D?x(x({},D),{},{ticks:F?M:D.ticks}):void 0,width:t,height:r,offset:o},!!F||b);(0,i.R)(Array.isArray(B),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof B,"]")),Array.isArray(B)&&(R=B)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(S,w({},l,{horizontalPoints:L})),n.createElement(E,w({},l,{verticalPoints:R})),n.createElement(P,w({},l,{offset:o,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(A,w({},l,{offset:o,verticalPoints:R,xAxis:D,yAxis:N})))}C.displayName="CartesianGrid"},85407:(e,t,r)=>{"use strict";r.d(t,{YF:()=>u,dj:()=>s,fP:()=>f,ky:()=>c});var n=r(76067),i=r(17118),a=r(27977),o=r(43075),l=r(77357),c=(0,n.VP)("mouseClick"),u=(0,n.Nc)();u.startListening({actionCreator:c,effect:(e,t)=>{var r=e.payload,n=(0,a.g)(t.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=t.getState(),c=(0,o.au)(n,n.tooltip.settings.shared),u=(0,a.g)(n,(0,l.w)(r));"axis"===c&&((null==u?void 0:u.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:u.activeIndex,activeDataKey:void 0,activeCoordinate:u.activeCoordinate})):t.dispatch((0,i.xS)()))}})},85621:(e,t,r)=>{"use strict";r.d(t,{kz:()=>iL,fb:()=>iE,q:()=>i1,tP:()=>ae,g1:()=>al,iv:()=>aI,Nk:()=>iA,pM:()=>iz,Oz:()=>iQ,tF:()=>aN,rj:()=>iM,ec:()=>ib,bb:()=>i4,xp:()=>aa,wL:()=>i8,sr:()=>ar,Qn:()=>ai,MK:()=>iD,IO:()=>iO,P9:()=>iq,S5:()=>iK,PU:()=>io,cd:()=>ic,eo:()=>iv,yi:()=>iG,CH:()=>iR,ZB:()=>aR,D5:()=>ay,iV:()=>ag,Hd:()=>ip,Gx:()=>aF,DP:()=>id,BQ:()=>aD,_y:()=>aH,AV:()=>i6,um:()=>iy,xM:()=>an,gT:()=>iV,Kr:()=>iZ,$X:()=>iX,TC:()=>iN,Zi:()=>a$,CR:()=>aU,ld:()=>ig,L$:()=>a_,Rl:()=>il,Lw:()=>aP,KR:()=>aT,sf:()=>iu,wP:()=>aC});var n={};r.r(n),r.d(n,{scaleBand:()=>p,scaleDiverging:()=>function e(){var t=eR(rQ()(ex));return t.copy=function(){return rY(t,e())},c.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=eZ(rQ()).domain([.1,1,10]);return t.copy=function(){return rY(t,e()).base(t.base())},c.apply(t,arguments)},scaleDivergingPow:()=>r0,scaleDivergingSqrt:()=>r1,scaleDivergingSymlog:()=>function e(){var t=eV(rQ());return t.copy=function(){return rY(t,e()).constant(t.constant())},c.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,em),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,em):[0,1],eR(n)},scaleImplicit:()=>h,scaleLinear:()=>function e(){var t=eA();return t.copy=function(){return eM(t,e())},l.apply(t,arguments),eR(t)},scaleLog:()=>function e(){let t=eZ(eP()).domain([1,10]);return t.copy=()=>eM(t,e()).base(t.base()),l.apply(t,arguments),t},scaleOrdinal:()=>d,scalePoint:()=>y,scalePow:()=>e0,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=S){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[k(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(j),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},l.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function c(e){return null!=e&&e<=e?o[k(a,e,0,i)]:t}function u(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return c}return c.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,u()):[r,n]},c.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,u()):o.slice()},c.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},c.unknown=function(e){return arguments.length&&(t=e),c},c.thresholds=function(){return a.slice()},c.copy=function(){return e().domain([r,n]).range(o).unknown(t)},l.apply(eR(c),arguments)},scaleRadial:()=>function e(){var t,r=eA(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(e2(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,em)).map(e2)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},l.apply(a,arguments),eR(a)},scaleSequential:()=>function e(){var t=eR(rV()(ex));return t.copy=function(){return rY(t,e())},c.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=eZ(rV()).domain([1,10]);return t.copy=function(){return rY(t,e()).base(t.base())},c.apply(t,arguments)},scaleSequentialPow:()=>rX,scaleSequentialQuantile:()=>function e(){var t=[],r=ex;function n(e){if(null!=e&&!isNaN(e*=1))return r((k(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(j),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return e5(e);if(t>=1)return e4(e);var n,i=(n-1)*t,a=Math.floor(i),o=e4((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?e3:function(e=j){if(e===j)return e3;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,c=Math.log(o),u=.5*Math.exp(2*c/3),s=.5*Math.sqrt(c*u*(o-u)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*u/o+s)),h=Math.min(i,Math.floor(r+(o-l)*u/o+s));e(t,r,f,h,a)}let o=t[r],l=n,c=i;for(e6(t,n,r),a(t[i],o)>0&&e6(t,n,i);l<c;){for(e6(t,l,c),++l,--c;0>a(t[l],o);)++l;for(;a(t[c],o)>0;)--c}0===a(t[n],o)?e6(t,n,c):e6(t,++c,i),c<=r&&(n=c+1),r<=c&&(i=c-1)}return t})(e,a).subarray(0,a+1));return o+(e5(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},c.apply(n,arguments)},scaleSequentialSqrt:()=>rJ,scaleSequentialSymlog:()=>function e(){var t=eV(rV());return t.copy=function(){return rY(t,e()).constant(t.constant())},c.apply(t,arguments)},scaleSqrt:()=>e1,scaleSymlog:()=>function e(){var t=eV(eP());return t.copy=function(){return eM(t,e()).constant(t.constant())},l.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[k(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},l.apply(a,arguments)},scaleTime:()=>rq,scaleUtc:()=>rW,tickFormat:()=>eL});var i=r(84648),a=r(30921),o=r.n(a);function l(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function c(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class u extends Map{constructor(e,t=f){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(s(this,e))}has(e){return super.has(s(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function s({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function f(e){return null!==e&&"object"==typeof e?e.valueOf():e}let h=Symbol("implicit");function d(){var e=new u,t=[],r=[],n=h;function i(i){let a=e.get(i);if(void 0===a){if(n!==h)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new u,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return d(t,r).unknown(n)},l.apply(i,arguments),i}function p(){var e,t,r=d().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,c=!1,u=0,s=0,f=.5;function h(){var r=n().length,l=o<a,h=l?o:a,d=l?a:o;e=(d-h)/Math.max(1,r-u+2*s),c&&(e=Math.floor(e)),h+=(d-h-e*(r-u))*f,t=e*(1-u),c&&(h=Math.round(h),t=Math.round(t));var p=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return h+e*t});return i(l?p.reverse():p)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),h()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,h()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,c=!0,h()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(c=!!e,h()):c},r.padding=function(e){return arguments.length?(u=Math.min(1,s=+e),h()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),h()):u},r.paddingOuter=function(e){return arguments.length?(s=+e,h()):s},r.align=function(e){return arguments.length?(f=Math.max(0,Math.min(1,e)),h()):f},r.copy=function(){return p(n(),[a,o]).round(c).paddingInner(u).paddingOuter(s).align(f)},l.apply(h(),arguments)}function y(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(p.apply(null,arguments).paddingInner(1))}let v=Math.sqrt(50),g=Math.sqrt(10),m=Math.sqrt(2);function b(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),c=o/Math.pow(10,l),u=c>=v?10:c>=g?5:c>=m?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/u)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*u)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?b(e,t,2*r):[n,i,a]}function x(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?b(t,e,r):b(e,t,r);if(!(a>=i))return[];let l=a-i+1,c=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)c[e]=-((a-e)/o);else for(let e=0;e<l;++e)c[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)c[e]=-((i+e)/o);else for(let e=0;e<l;++e)c[e]=(i+e)*o;return c}function w(e,t,r){return b(e*=1,t*=1,r*=1)[2]}function O(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?w(t,e,r):w(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function j(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function M(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function P(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=j,r=(t,r)=>j(e(t),r),n=(t,r)=>e(t)-r):(t=e===j||e===M?e:A,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function A(){return 0}function S(e){return null===e?NaN:+e}let E=P(j),k=E.right;function _(e,t,r){e.prototype=t.prototype=r,r.constructor=e}function T(e,t){var r=Object.create(e.prototype);for(var n in t)r[n]=t[n];return r}function C(){}E.left,P(S).center;var D="\\s*([+-]?\\d+)\\s*",N="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",z="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",I=/^#([0-9a-f]{3,8})$/,L=RegExp(`^rgb\\(${D},${D},${D}\\)$`),R=RegExp(`^rgb\\(${z},${z},${z}\\)$`),$=RegExp(`^rgba\\(${D},${D},${D},${N}\\)$`),U=RegExp(`^rgba\\(${z},${z},${z},${N}\\)$`),F=RegExp(`^hsl\\(${N},${z},${z}\\)$`),B=RegExp(`^hsla\\(${N},${z},${z},${N}\\)$`),H={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function K(){return this.rgb().formatHex()}function G(){return this.rgb().formatRgb()}function Z(e){var t,r;return e=(e+"").trim().toLowerCase(),(t=I.exec(e))?(r=t[1].length,t=parseInt(t[1],16),6===r?q(t):3===r?new Y(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===r?W(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===r?W(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=L.exec(e))?new Y(t[1],t[2],t[3],1):(t=R.exec(e))?new Y(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=$.exec(e))?W(t[1],t[2],t[3],t[4]):(t=U.exec(e))?W(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=F.exec(e))?er(t[1],t[2]/100,t[3]/100,1):(t=B.exec(e))?er(t[1],t[2]/100,t[3]/100,t[4]):H.hasOwnProperty(e)?q(H[e]):"transparent"===e?new Y(NaN,NaN,NaN,0):null}function q(e){return new Y(e>>16&255,e>>8&255,255&e,1)}function W(e,t,r,n){return n<=0&&(e=t=r=NaN),new Y(e,t,r,n)}function V(e,t,r,n){var i;return 1==arguments.length?((i=e)instanceof C||(i=Z(i)),i)?new Y((i=i.rgb()).r,i.g,i.b,i.opacity):new Y:new Y(e,t,r,null==n?1:n)}function Y(e,t,r,n){this.r=+e,this.g=+t,this.b=+r,this.opacity=+n}function X(){return`#${et(this.r)}${et(this.g)}${et(this.b)}`}function J(){let e=Q(this.opacity);return`${1===e?"rgb(":"rgba("}${ee(this.r)}, ${ee(this.g)}, ${ee(this.b)}${1===e?")":`, ${e})`}`}function Q(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function ee(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function et(e){return((e=ee(e))<16?"0":"")+e.toString(16)}function er(e,t,r,n){return n<=0?e=t=r=NaN:r<=0||r>=1?e=t=NaN:t<=0&&(e=NaN),new ei(e,t,r,n)}function en(e){if(e instanceof ei)return new ei(e.h,e.s,e.l,e.opacity);if(e instanceof C||(e=Z(e)),!e)return new ei;if(e instanceof ei)return e;var t=(e=e.rgb()).r/255,r=e.g/255,n=e.b/255,i=Math.min(t,r,n),a=Math.max(t,r,n),o=NaN,l=a-i,c=(a+i)/2;return l?(o=t===a?(r-n)/l+(r<n)*6:r===a?(n-t)/l+2:(t-r)/l+4,l/=c<.5?a+i:2-a-i,o*=60):l=c>0&&c<1?0:o,new ei(o,l,c,e.opacity)}function ei(e,t,r,n){this.h=+e,this.s=+t,this.l=+r,this.opacity=+n}function ea(e){return(e=(e||0)%360)<0?e+360:e}function eo(e){return Math.max(0,Math.min(1,e||0))}function el(e,t,r){return(e<60?t+(r-t)*e/60:e<180?r:e<240?t+(r-t)*(240-e)/60:t)*255}function ec(e,t,r,n,i){var a=e*e,o=a*e;return((1-3*e+3*a-o)*t+(4-6*a+3*o)*r+(1+3*e+3*a-3*o)*n+o*i)/6}_(C,Z,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:K,formatHex:K,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return en(this).formatHsl()},formatRgb:G,toString:G}),_(Y,V,T(C,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new Y(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new Y(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Y(ee(this.r),ee(this.g),ee(this.b),Q(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:X,formatHex:X,formatHex8:function(){return`#${et(this.r)}${et(this.g)}${et(this.b)}${et((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:J,toString:J})),_(ei,function(e,t,r,n){return 1==arguments.length?en(e):new ei(e,t,r,null==n?1:n)},T(C,{brighter(e){return e=null==e?1.4285714285714286:Math.pow(1.4285714285714286,e),new ei(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?.7:Math.pow(.7,e),new ei(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+(this.h<0)*360,t=isNaN(e)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*t,i=2*r-n;return new Y(el(e>=240?e-240:e+120,i,n),el(e,i,n),el(e<120?e+240:e-120,i,n),this.opacity)},clamp(){return new ei(ea(this.h),eo(this.s),eo(this.l),Q(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let e=Q(this.opacity);return`${1===e?"hsl(":"hsla("}${ea(this.h)}, ${100*eo(this.s)}%, ${100*eo(this.l)}%${1===e?")":`, ${e})`}`}}));let eu=e=>()=>e;function es(e,t){var r,n,i=t-e;return i?(r=e,n=i,function(e){return r+e*n}):eu(isNaN(e)?t:e)}let ef=function e(t){var r,n=1==(r=+t)?es:function(e,t){var n,i,a;return t-e?(n=e,i=t,n=Math.pow(n,a=r),i=Math.pow(i,a)-n,a=1/a,function(e){return Math.pow(n+e*i,a)}):eu(isNaN(e)?t:e)};function i(e,t){var r=n((e=V(e)).r,(t=V(t)).r),i=n(e.g,t.g),a=n(e.b,t.b),o=es(e.opacity,t.opacity);return function(t){return e.r=r(t),e.g=i(t),e.b=a(t),e.opacity=o(t),e+""}}return i.gamma=e,i}(1);function eh(e){return function(t){var r,n,i=t.length,a=Array(i),o=Array(i),l=Array(i);for(r=0;r<i;++r)n=V(t[r]),a[r]=n.r||0,o[r]=n.g||0,l[r]=n.b||0;return a=e(a),o=e(o),l=e(l),n.opacity=1,function(e){return n.r=a(e),n.g=o(e),n.b=l(e),n+""}}}eh(function(e){var t=e.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,t-1):Math.floor(r*t),i=e[n],a=e[n+1],o=n>0?e[n-1]:2*i-a,l=n<t-1?e[n+2]:2*a-i;return ec((r-n/t)*t,o,i,a,l)}}),eh(function(e){var t=e.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*t),i=e[(n+t-1)%t],a=e[n%t],o=e[(n+1)%t],l=e[(n+2)%t];return ec((r-n/t)*t,i,a,o,l)}});function ed(e,t){return e*=1,t*=1,function(r){return e*(1-r)+t*r}}var ep=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ey=RegExp(ep.source,"g");function ev(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?eu(t):("number"===i?ed:"string"===i?(n=Z(t))?(t=n,ef):function(e,t){var r,n,i,a,o,l=ep.lastIndex=ey.lastIndex=0,c=-1,u=[],s=[];for(e+="",t+="";(i=ep.exec(e))&&(a=ey.exec(t));)(o=a.index)>l&&(o=t.slice(l,o),u[c]?u[c]+=o:u[++c]=o),(i=i[0])===(a=a[0])?u[c]?u[c]+=a:u[++c]=a:(u[++c]=null,s.push({i:c,x:ed(i,a)})),l=ey.lastIndex;return l<t.length&&(o=t.slice(l),u[c]?u[c]+=o:u[++c]=o),u.length<2?s[0]?(r=s[0].x,function(e){return r(e)+""}):(n=t,function(){return n}):(t=s.length,function(e){for(var r,n=0;n<t;++n)u[(r=s[n]).i]=r.x(e);return u.join("")})}:t instanceof Z?ef:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=ev(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=ev(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:ed:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function eg(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function em(e){return+e}var eb=[0,1];function ex(e){return e}function ew(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function eO(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=ew(i,n),a=r(o,a)):(n=ew(n,i),a=r(a,o)),function(e){return a(n(e))}}function ej(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=ew(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=k(e,t,1,n)-1;return a[r](i[r](t))}}function eM(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function eP(){var e,t,r,n,i,a,o=eb,l=eb,c=ev,u=ex;function s(){var e,t,r,c=Math.min(o.length,l.length);return u!==ex&&(e=o[0],t=o[c-1],e>t&&(r=e,e=t,t=r),u=function(r){return Math.max(e,Math.min(t,r))}),n=c>2?ej:eO,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,c)))(e(u(t)))}return f.invert=function(r){return u(t((a||(a=n(l,o.map(e),ed)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,em),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),c=eg,s()},f.clamp=function(e){return arguments.length?(u=!!e||ex,s()):u!==ex},f.interpolate=function(e){return arguments.length?(c=e,s()):c},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function eA(){return eP()(ex,ex)}var eS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function eE(e){var t;if(!(t=eS.exec(e)))throw Error("invalid format: "+e);return new ek({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function ek(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function e_(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function eT(e){return(e=e_(Math.abs(e)))?e[1]:NaN}function eC(e,t){var r=e_(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}eE.prototype=ek.prototype,ek.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let eD={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>eC(100*e,t),r:eC,s:function(e,t){var r=e_(e,t);if(!r)return e+"";var n=r[0],i=r[1],a=i-(r9=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,o=n.length;return a===o?n:a>o?n+Array(a-o+1).join("0"):a>0?n.slice(0,a)+"."+n.slice(a):"0."+Array(1-a).join("0")+e_(e,Math.max(0,t+a-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eN(e){return e}var ez=Array.prototype.map,eI=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function eL(e,t,r,n){var i,a,o,l=O(e,t,r);switch((n=eE(null==n?",f":n)).type){case"s":var c=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(o=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(eT(c)/3)))-eT(Math.abs(l))))||(n.precision=o),nr(n,c);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(o=Math.max(0,eT(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=l)))-eT(i))+1)||(n.precision=o-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(o=Math.max(0,-eT(Math.abs(l))))||(n.precision=o-("%"===n.type)*2)}return nt(n)}function eR(e){var t=e.domain;return e.ticks=function(e){var r=t();return x(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return eL(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,c=a[o],u=a[l],s=10;for(u<c&&(i=c,c=u,u=i,i=o,o=l,l=i);s-- >0;){if((i=w(c,u,r))===n)return a[o]=c,a[l]=u,t(a);if(i>0)c=Math.floor(c/i)*i,u=Math.ceil(u/i)*i;else if(i<0)c=Math.ceil(c*i)/i,u=Math.floor(u*i)/i;else break;n=i}return e},e}function e$(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eU(e){return Math.log(e)}function eF(e){return Math.exp(e)}function eB(e){return-Math.log(-e)}function eH(e){return-Math.exp(-e)}function eK(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eG(e){return(t,r)=>-e(-t,r)}function eZ(e){let t,r,n=e(eU,eF),i=n.domain,a=10;function o(){var o,l;return t=(o=a)===Math.E?Math.log:10===o&&Math.log10||2===o&&Math.log2||(o=Math.log(o),e=>Math.log(e)/o),r=10===(l=a)?eK:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eG(t),r=eG(r),e(eB,eH)):e(eU,eF),n}return n.base=function(e){return arguments.length?(a=+e,o()):a},n.domain=function(e){return arguments.length?(i(e),o()):i()},n.ticks=e=>{let n,o,l=i(),c=l[0],u=l[l.length-1],s=u<c;s&&([c,u]=[u,c]);let f=t(c),h=t(u),d=null==e?10:+e,p=[];if(!(a%1)&&h-f<d){if(f=Math.floor(f),h=Math.ceil(h),c>0){for(;f<=h;++f)for(n=1;n<a;++n)if(!((o=f<0?n/r(-f):n*r(f))<c)){if(o>u)break;p.push(o)}}else for(;f<=h;++f)for(n=a-1;n>=1;--n)if(!((o=f>0?n/r(-f):n*r(f))<c)){if(o>u)break;p.push(o)}2*p.length<d&&(p=x(c,u,d))}else p=x(f,h,Math.min(h-f,d)).map(r);return s?p.reverse():p},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===a?"s":","),"function"!=typeof i&&(a%1||null!=(i=eE(i)).precision||(i.trim=!0),i=nt(i)),e===1/0)return i;let o=Math.max(1,a*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*a<a-.5&&(n*=a),n<=o?i(e):""}},n.nice=()=>i(e$(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function eq(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function eW(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function eV(e){var t=1,r=e(eq(1),eW(t));return r.constant=function(r){return arguments.length?e(eq(t=+r),eW(t)):t},eR(r)}function eY(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function eX(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function eJ(e){return e<0?-e*e:e*e}function eQ(e){var t=e(ex,ex),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(ex,ex):.5===r?e(eX,eJ):e(eY(r),eY(1/r)):r},eR(t)}function e0(){var e=eQ(eP());return e.copy=function(){return eM(e,e0()).exponent(e.exponent())},l.apply(e,arguments),e}function e1(){return e0.apply(null,arguments).exponent(.5)}function e2(e){return Math.sign(e)*e*e}function e4(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function e5(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}nt=(ne=function(e){var t,r,n,i=void 0===e.grouping||void 0===e.thousands?eN:(t=ez.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],c=0;i>0&&l>0&&(c+l+1>n&&(l=Math.max(1,n-c)),a.push(e.substring(i-=l,i+l)),!((c+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),a=void 0===e.currency?"":e.currency[0]+"",o=void 0===e.currency?"":e.currency[1]+"",l=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?eN:(n=ez.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return n[+e]})}),u=void 0===e.percent?"%":e.percent+"",s=void 0===e.minus?"−":e.minus+"",f=void 0===e.nan?"NaN":e.nan+"";function h(e){var t=(e=eE(e)).fill,r=e.align,n=e.sign,h=e.symbol,d=e.zero,p=e.width,y=e.comma,v=e.precision,g=e.trim,m=e.type;"n"===m?(y=!0,m="g"):eD[m]||(void 0===v&&(v=12),g=!0,m="g"),(d||"0"===t&&"="===r)&&(d=!0,t="0",r="=");var b="$"===h?a:"#"===h&&/[boxX]/.test(m)?"0"+m.toLowerCase():"",x="$"===h?o:/[%p]/.test(m)?u:"",w=eD[m],O=/[defgprs%]/.test(m);function j(e){var a,o,u,h=b,j=x;if("c"===m)j=w(e)+j,e="";else{var M=(e*=1)<0||1/e<0;if(e=isNaN(e)?f:w(Math.abs(e),v),g&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),M&&0==+e&&"+"!==n&&(M=!1),h=(M?"("===n?n:s:"-"===n||"("===n?"":n)+h,j=("s"===m?eI[8+r9/3]:"")+j+(M&&"("===n?")":""),O){for(a=-1,o=e.length;++a<o;)if(48>(u=e.charCodeAt(a))||u>57){j=(46===u?l+e.slice(a+1):e.slice(a))+j,e=e.slice(0,a);break}}}y&&!d&&(e=i(e,1/0));var P=h.length+e.length+j.length,A=P<p?Array(p-P+1).join(t):"";switch(y&&d&&(e=i(A+e,A.length?p-j.length:1/0),A=""),r){case"<":e=h+e+j+A;break;case"=":e=h+A+e+j;break;case"^":e=A.slice(0,P=A.length>>1)+h+e+j+A.slice(P);break;default:e=A+h+e+j}return c(e)}return v=void 0===v?6:/[gprs]/.test(m)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return e+""},j}return{format:h,formatPrefix:function(e,t){var r=h(((e=eE(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(eT(t)/3))),i=Math.pow(10,-n),a=eI[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,nr=ne.formatPrefix;function e3(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function e6(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let e8=new Date,e7=new Date;function e9(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>e9(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(e8.setTime(+t),e7.setTime(+n),e(e8),e(e7),Math.floor(r(e8,e7))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let te=e9(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);te.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?e9(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):te:null,te.range;let tt=e9(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());tt.range;let tr=e9(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());tr.range;let tn=e9(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());tn.range;let ti=e9(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());ti.range;let ta=e9(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());ta.range;let to=e9(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);to.range;let tl=e9(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);tl.range;let tc=e9(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function tu(e){return e9(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}tc.range;let ts=tu(0),tf=tu(1),th=tu(2),td=tu(3),tp=tu(4),ty=tu(5),tv=tu(6);function tg(e){return e9(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}ts.range,tf.range,th.range,td.range,tp.range,ty.range,tv.range;let tm=tg(0),tb=tg(1),tx=tg(2),tw=tg(3),tO=tg(4),tj=tg(5),tM=tg(6);tm.range,tb.range,tx.range,tw.range,tO.range,tj.range,tM.range;let tP=e9(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());tP.range;let tA=e9(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());tA.range;let tS=e9(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());tS.every=e=>isFinite(e=Math.floor(e))&&e>0?e9(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,tS.range;let tE=e9(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tk(e,t,r,n,i,a){let o=[[tt,1,1e3],[tt,5,5e3],[tt,15,15e3],[tt,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=P(([,,e])=>e).right(o,i);if(a===o.length)return e.every(O(t/31536e6,r/31536e6,n));if(0===a)return te.every(Math.max(O(t,r,n),1));let[l,c]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(c)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}tE.every=e=>isFinite(e=Math.floor(e))&&e>0?e9(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,tE.range;let[t_,tT]=tk(tE,tA,tm,tc,ta,tn),[tC,tD]=tk(tS,tP,ts,to,ti,tr);function tN(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tz(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tI(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tL={"-":"",_:" ",0:"0"},tR=/^\s*\d+/,t$=/^%/,tU=/[\\^$*+?|[\]().{}]/g;function tF(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function tB(e){return e.replace(tU,"\\$&")}function tH(e){return RegExp("^(?:"+e.map(tB).join("|")+")","i")}function tK(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tG(e,t,r){var n=tR.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tZ(e,t,r){var n=tR.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function tq(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function tW(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function tV(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function tY(e,t,r){var n=tR.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function tX(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function tJ(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function tQ(e,t,r){var n=tR.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function t0(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function t1(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function t2(e,t,r){var n=tR.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function t4(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function t5(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function t3(e,t,r){var n=tR.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function t6(e,t,r){var n=tR.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function t8(e,t,r){var n=tR.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function t7(e,t,r){var n=t$.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function t9(e,t,r){var n=tR.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function re(e,t,r){var n=tR.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function rt(e,t){return tF(e.getDate(),t,2)}function rr(e,t){return tF(e.getHours(),t,2)}function rn(e,t){return tF(e.getHours()%12||12,t,2)}function ri(e,t){return tF(1+to.count(tS(e),e),t,3)}function ra(e,t){return tF(e.getMilliseconds(),t,3)}function ro(e,t){return ra(e,t)+"000"}function rl(e,t){return tF(e.getMonth()+1,t,2)}function rc(e,t){return tF(e.getMinutes(),t,2)}function ru(e,t){return tF(e.getSeconds(),t,2)}function rs(e){var t=e.getDay();return 0===t?7:t}function rf(e,t){return tF(ts.count(tS(e)-1,e),t,2)}function rh(e){var t=e.getDay();return t>=4||0===t?tp(e):tp.ceil(e)}function rd(e,t){return e=rh(e),tF(tp.count(tS(e),e)+(4===tS(e).getDay()),t,2)}function rp(e){return e.getDay()}function ry(e,t){return tF(tf.count(tS(e)-1,e),t,2)}function rv(e,t){return tF(e.getFullYear()%100,t,2)}function rg(e,t){return tF((e=rh(e)).getFullYear()%100,t,2)}function rm(e,t){return tF(e.getFullYear()%1e4,t,4)}function rb(e,t){var r=e.getDay();return tF((e=r>=4||0===r?tp(e):tp.ceil(e)).getFullYear()%1e4,t,4)}function rx(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tF(t/60|0,"0",2)+tF(t%60,"0",2)}function rw(e,t){return tF(e.getUTCDate(),t,2)}function rO(e,t){return tF(e.getUTCHours(),t,2)}function rj(e,t){return tF(e.getUTCHours()%12||12,t,2)}function rM(e,t){return tF(1+tl.count(tE(e),e),t,3)}function rP(e,t){return tF(e.getUTCMilliseconds(),t,3)}function rA(e,t){return rP(e,t)+"000"}function rS(e,t){return tF(e.getUTCMonth()+1,t,2)}function rE(e,t){return tF(e.getUTCMinutes(),t,2)}function rk(e,t){return tF(e.getUTCSeconds(),t,2)}function r_(e){var t=e.getUTCDay();return 0===t?7:t}function rT(e,t){return tF(tm.count(tE(e)-1,e),t,2)}function rC(e){var t=e.getUTCDay();return t>=4||0===t?tO(e):tO.ceil(e)}function rD(e,t){return e=rC(e),tF(tO.count(tE(e),e)+(4===tE(e).getUTCDay()),t,2)}function rN(e){return e.getUTCDay()}function rz(e,t){return tF(tb.count(tE(e)-1,e),t,2)}function rI(e,t){return tF(e.getUTCFullYear()%100,t,2)}function rL(e,t){return tF((e=rC(e)).getUTCFullYear()%100,t,2)}function rR(e,t){return tF(e.getUTCFullYear()%1e4,t,4)}function r$(e,t){var r=e.getUTCDay();return tF((e=r>=4||0===r?tO(e):tO.ceil(e)).getUTCFullYear()%1e4,t,4)}function rU(){return"+0000"}function rF(){return"%"}function rB(e){return+e}function rH(e){return Math.floor(e/1e3)}function rK(e){return new Date(e)}function rG(e){return e instanceof Date?+e:+new Date(+e)}function rZ(e,t,r,n,i,a,o,l,c,u){var s=eA(),f=s.invert,h=s.domain,d=u(".%L"),p=u(":%S"),y=u("%I:%M"),v=u("%I %p"),g=u("%a %d"),m=u("%b %d"),b=u("%B"),x=u("%Y");function w(e){return(c(e)<e?d:l(e)<e?p:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:x)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?h(Array.from(e,rG)):h().map(rK)},s.ticks=function(t){var r=h();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?w:u(t)},s.nice=function(e){var r=h();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?h(e$(r,e)):s},s.copy=function(){return eM(s,rZ(e,t,r,n,i,a,o,l,c,u))},s}function rq(){return l.apply(rZ(tC,tD,tS,tP,ts,to,ti,tr,tt,ni).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rW(){return l.apply(rZ(t_,tT,tE,tA,tm,tl,ta,tn,tt,na).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rV(){var e,t,r,n,i,a=0,o=1,l=ex,c=!1;function u(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,c?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),u):[l(0),l(1)]}}return u.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),u):[a,o]},u.clamp=function(e){return arguments.length?(c=!!e,u):c},u.interpolator=function(e){return arguments.length?(l=e,u):l},u.range=s(ev),u.rangeRound=s(eg),u.unknown=function(e){return arguments.length?(i=e,u):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),u}}function rY(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function rX(){var e=eQ(rV());return e.copy=function(){return rY(e,rX()).exponent(e.exponent())},c.apply(e,arguments)}function rJ(){return rX.apply(null,arguments).exponent(.5)}function rQ(){var e,t,r,n,i,a,o,l=0,c=.5,u=1,s=1,f=ex,h=!1;function d(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(h?Math.max(0,Math.min(1,e)):e))}function p(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=ev);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),d):[f(0),f(.5),f(1)]}}return d.domain=function(o){return arguments.length?([l,c,u]=o,e=a(l*=1),t=a(c*=1),r=a(u*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d):[l,c,u]},d.clamp=function(e){return arguments.length?(h=!!e,d):h},d.interpolator=function(e){return arguments.length?(f=e,d):f},d.range=p(ev),d.rangeRound=p(eg),d.unknown=function(e){return arguments.length?(o=e,d):o},function(o){return a=o,e=o(l),t=o(c),r=o(u),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,d}}function r0(){var e=eQ(rQ());return e.copy=function(){return rY(e,r0()).exponent(e.exponent())},c.apply(e,arguments)}function r1(){return r0.apply(null,arguments).exponent(.5)}ni=(nn=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,c=e.shortMonths,u=tH(i),s=tK(i),f=tH(a),h=tK(a),d=tH(o),p=tK(o),y=tH(l),v=tK(l),g=tH(c),m=tK(c),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return c[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:rt,e:rt,f:ro,g:rg,G:rb,H:rr,I:rn,j:ri,L:ra,m:rl,M:rc,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rB,s:rH,S:ru,u:rs,U:rf,V:rd,w:rp,W:ry,x:null,X:null,y:rv,Y:rm,Z:rx,"%":rF},x={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return c[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:rw,e:rw,f:rA,g:rL,G:r$,H:rO,I:rj,j:rM,L:rP,m:rS,M:rE,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rB,s:rH,S:rk,u:r_,U:rT,V:rD,w:rN,W:rz,x:null,X:null,y:rI,Y:rR,Z:rU,"%":rF},w={a:function(e,t,r){var n=d.exec(t.slice(r));return n?(e.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return M(e,t,r,n)},d:t1,e:t1,f:t8,g:tX,G:tY,H:t4,I:t4,j:t2,L:t6,m:t0,M:t5,p:function(e,t,r){var n=u.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:tQ,Q:t9,s:re,S:t3,u:tZ,U:tq,V:tW,w:tG,W:tV,x:function(e,t,n){return M(e,r,t,n)},X:function(e,t,r){return M(e,n,t,r)},y:tX,Y:tY,Z:tJ,"%":t7};function O(e,t){return function(r){var n,i,a,o=[],l=-1,c=0,u=e.length;for(r instanceof Date||(r=new Date(+r));++l<u;)37===e.charCodeAt(l)&&(o.push(e.slice(c,l)),null!=(i=tL[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),c=l+1);return o.push(e.slice(c,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=tI(1900,void 0,1);if(M(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tz(tI(a.y,0,1))).getUTCDay())>4||0===i?tb.ceil(n):tb(n),n=tl.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=tN(tI(a.y,0,1))).getDay())>4||0===i?tf.ceil(n):tf(n),n=to.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?tz(tI(a.y,0,1)).getUTCDay():tN(tI(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tz(a)):tN(a)}}function M(e,t,r,n){for(var i,a,o=0,l=t.length,c=r.length;o<l;){if(n>=c)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=w[(i=t.charAt(o++))in tL?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),x.x=O(r,x),x.X=O(n,x),x.c=O(t,x),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",x);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,nn.parse,na=nn.utcFormat,nn.utcParse;var r2=r(51426),r4=r(64279),r5=r(57282),r3=r(22989),r6=r(12128);function r8(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,r6.H)(t)&&(0,r6.H)(r))return!0}return!1}function r7(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var r9,ne,nt,nr,nn,ni,na,no,nl,nc=!0,nu="[DecimalError] ",ns=nu+"Invalid argument: ",nf=nu+"Exponent out of range: ",nh=Math.floor,nd=Math.pow,np=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ny=nh(1286742750677284.5),nv={};function ng(e,t){var r,n,i,a,o,l,c,u,s=e.constructor,f=s.precision;if(!e.s||!t.s)return t.s||(t=new s(e)),nc?nS(t,f):t;if(c=e.d,u=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,l=u.length):(n=u,i=o,l=c.length),a>(l=(o=Math.ceil(f/7))>l?o+1:l+1)&&(a=l,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((l=c.length)-(a=u.length)<0&&(a=l,n=u,u=c,c=n),r=0;a;)r=(c[--a]=c[a]+u[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),l=c.length;0==c[--l];)c.pop();return t.d=c,t.e=i,nc?nS(t,f):t}function nm(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ns+e)}function nb(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=nM(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=nM(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}nv.absoluteValue=nv.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},nv.comparedTo=nv.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},nv.decimalPlaces=nv.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},nv.dividedBy=nv.div=function(e){return nx(this,new this.constructor(e))},nv.dividedToIntegerBy=nv.idiv=function(e){var t=this.constructor;return nS(nx(this,new t(e),0,1),t.precision)},nv.equals=nv.eq=function(e){return!this.cmp(e)},nv.exponent=function(){return nO(this)},nv.greaterThan=nv.gt=function(e){return this.cmp(e)>0},nv.greaterThanOrEqualTo=nv.gte=function(e){return this.cmp(e)>=0},nv.isInteger=nv.isint=function(){return this.e>this.d.length-2},nv.isNegative=nv.isneg=function(){return this.s<0},nv.isPositive=nv.ispos=function(){return this.s>0},nv.isZero=function(){return 0===this.s},nv.lessThan=nv.lt=function(e){return 0>this.cmp(e)},nv.lessThanOrEqualTo=nv.lte=function(e){return 1>this.cmp(e)},nv.logarithm=nv.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(nl))throw Error(nu+"NaN");if(this.s<1)throw Error(nu+(this.s?"NaN":"-Infinity"));return this.eq(nl)?new r(0):(nc=!1,t=nx(nP(this,i),nP(e,i),i),nc=!0,nS(t,n))},nv.minus=nv.sub=function(e){return e=new this.constructor(e),this.s==e.s?nE(this,e):ng(this,(e.s=-e.s,e))},nv.modulo=nv.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(nu+"NaN");return this.s?(nc=!1,t=nx(this,e,0,1).times(e),nc=!0,this.minus(t)):nS(new r(this),n)},nv.naturalExponential=nv.exp=function(){return nw(this)},nv.naturalLogarithm=nv.ln=function(){return nP(this)},nv.negated=nv.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},nv.plus=nv.add=function(e){return e=new this.constructor(e),this.s==e.s?ng(this,e):nE(this,(e.s=-e.s,e))},nv.precision=nv.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(ns+e);if(t=nO(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},nv.squareRoot=nv.sqrt=function(){var e,t,r,n,i,a,o,l=this.constructor;if(this.s<1){if(!this.s)return new l(0);throw Error(nu+"NaN")}for(e=nO(this),nc=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=nb(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=nh((e+1)/2)-(e<0||e%2),n=new l(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new l(i.toString()),i=o=(r=l.precision)+3;;)if(n=(a=n).plus(nx(this,a,o+2)).times(.5),nb(a.d).slice(0,o)===(t=nb(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(nS(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return nc=!0,nS(n,r)},nv.times=nv.mul=function(e){var t,r,n,i,a,o,l,c,u,s=this.constructor,f=this.d,h=(e=new s(e)).d;if(!this.s||!e.s)return new s(0);for(e.s*=this.s,r=this.e+e.e,(c=f.length)<(u=h.length)&&(a=f,f=h,h=a,o=c,c=u,u=o),a=[],n=o=c+u;n--;)a.push(0);for(n=u;--n>=0;){for(t=0,i=c+n;i>n;)l=a[i]+h[n]*f[i-n-1]+t,a[i--]=l%1e7|0,t=l/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,nc?nS(e,s.precision):e},nv.toDecimalPlaces=nv.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(nm(e,0,1e9),void 0===t?t=n.rounding:nm(t,0,8),nS(r,e+nO(r)+1,t))},nv.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=nk(n,!0):(nm(e,0,1e9),void 0===t?t=i.rounding:nm(t,0,8),r=nk(n=nS(new i(n),e+1,t),!0,e+1)),r},nv.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?nk(this):(nm(e,0,1e9),void 0===t?t=i.rounding:nm(t,0,8),r=nk((n=nS(new i(this),e+nO(this)+1,t)).abs(),!1,e+nO(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},nv.toInteger=nv.toint=function(){var e=this.constructor;return nS(new e(this),nO(this)+1,e.rounding)},nv.toNumber=function(){return+this},nv.toPower=nv.pow=function(e){var t,r,n,i,a,o,l=this,c=l.constructor,u=+(e=new c(e));if(!e.s)return new c(nl);if(!(l=new c(l)).s){if(e.s<1)throw Error(nu+"Infinity");return l}if(l.eq(nl))return l;if(n=c.precision,e.eq(nl))return nS(l,n);if(o=(t=e.e)>=(r=e.d.length-1),a=l.s,o){if((r=u<0?-u:u)<=0x1fffffffffffff){for(i=new c(nl),t=Math.ceil(n/7+4),nc=!1;r%2&&n_((i=i.times(l)).d,t),0!==(r=nh(r/2));)n_((l=l.times(l)).d,t);return nc=!0,e.s<0?new c(nl).div(i):nS(i,n)}}else if(a<0)throw Error(nu+"NaN");return a=a<0&&1&e.d[Math.max(t,r)]?-1:1,l.s=1,nc=!1,i=e.times(nP(l,n+12)),nc=!0,(i=nw(i)).s=a,i},nv.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=nO(i),n=nk(i,r<=a.toExpNeg||r>=a.toExpPos)):(nm(e,1,1e9),void 0===t?t=a.rounding:nm(t,0,8),r=nO(i=nS(new a(i),e,t)),n=nk(i,e<=r||r<=a.toExpNeg,e)),n},nv.toSignificantDigits=nv.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(nm(e,1,1e9),void 0===t?t=r.rounding:nm(t,0,8)),nS(new r(this),e,t)},nv.toString=nv.valueOf=nv.val=nv.toJSON=nv[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=nO(this),t=this.constructor;return nk(this,e<=t.toExpNeg||e>=t.toExpPos)};var nx=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,c,u,s,f,h,d,p,y,v,g,m,b,x,w,O,j,M,P=n.constructor,A=n.s==i.s?1:-1,S=n.d,E=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(nu+"Division by zero");for(u=0,c=n.e-i.e,j=E.length,w=S.length,p=(d=new P(A)).d=[];E[u]==(S[u]||0);)++u;if(E[u]>(S[u]||0)&&--c,(m=null==a?a=P.precision:o?a+(nO(n)-nO(i))+1:a)<0)return new P(0);if(m=m/7+2|0,u=0,1==j)for(s=0,E=E[0],m++;(u<w||s)&&m--;u++)b=1e7*s+(S[u]||0),p[u]=b/E|0,s=b%E|0;else{for((s=1e7/(E[0]+1)|0)>1&&(E=e(E,s),S=e(S,s),j=E.length,w=S.length),x=j,v=(y=S.slice(0,j)).length;v<j;)y[v++]=0;(M=E.slice()).unshift(0),O=E[0],E[1]>=1e7/2&&++O;do s=0,(l=t(E,y,j,v))<0?(g=y[0],j!=v&&(g=1e7*g+(y[1]||0)),(s=g/O|0)>1?(s>=1e7&&(s=1e7-1),h=(f=e(E,s)).length,v=y.length,1==(l=t(f,y,h,v))&&(s--,r(f,j<h?M:E,h))):(0==s&&(l=s=1),f=E.slice()),(h=f.length)<v&&f.unshift(0),r(y,f,v),-1==l&&(v=y.length,(l=t(E,y,j,v))<1&&(s++,r(y,j<v?M:E,v))),v=y.length):0===l&&(s++,y=[0]),p[u++]=s,l&&y[0]?y[v++]=S[x]||0:(y=[S[x]],v=1);while((x++<w||void 0!==y[0])&&m--)}return p[0]||p.shift(),d.e=c,nS(d,o?a+nO(d)+1:a)}}();function nw(e,t){var r,n,i,a,o,l=0,c=0,u=e.constructor,s=u.precision;if(nO(e)>16)throw Error(nf+nO(e));if(!e.s)return new u(nl);for(null==t?(nc=!1,o=s):o=t,a=new u(.03125);e.abs().gte(.1);)e=e.times(a),c+=5;for(o+=Math.log(nd(2,c))/Math.LN10*2+5|0,r=n=i=new u(nl),u.precision=o;;){if(n=nS(n.times(e),o),r=r.times(++l),nb((a=i.plus(nx(n,r,o))).d).slice(0,o)===nb(i.d).slice(0,o)){for(;c--;)i=nS(i.times(i),o);return u.precision=s,null==t?(nc=!0,nS(i,s)):i}i=a}}function nO(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function nj(e,t,r){if(t>e.LN10.sd())throw nc=!0,r&&(e.precision=r),Error(nu+"LN10 precision limit exceeded");return nS(new e(e.LN10),t)}function nM(e){for(var t="";e--;)t+="0";return t}function nP(e,t){var r,n,i,a,o,l,c,u,s,f=1,h=e,d=h.d,p=h.constructor,y=p.precision;if(h.s<1)throw Error(nu+(h.s?"NaN":"-Infinity"));if(h.eq(nl))return new p(0);if(null==t?(nc=!1,u=y):u=t,h.eq(10))return null==t&&(nc=!0),nj(p,u);if(p.precision=u+=10,n=(r=nb(d)).charAt(0),!(15e14>Math.abs(a=nO(h))))return c=nj(p,u+2,y).times(a+""),h=nP(new p(n+"."+r.slice(1)),u-10).plus(c),p.precision=y,null==t?(nc=!0,nS(h,y)):h;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=nb((h=h.times(e)).d)).charAt(0),f++;for(a=nO(h),n>1?(h=new p("0."+r),a++):h=new p(n+"."+r.slice(1)),l=o=h=nx(h.minus(nl),h.plus(nl),u),s=nS(h.times(h),u),i=3;;){if(o=nS(o.times(s),u),nb((c=l.plus(nx(o,new p(i),u))).d).slice(0,u)===nb(l.d).slice(0,u))return l=l.times(2),0!==a&&(l=l.plus(nj(p,u+2,y).times(a+""))),l=nx(l,new p(f),u),p.precision=y,null==t?(nc=!0,nS(l,y)):l;l=c,i+=2}}function nA(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=nh((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),nc&&(e.e>ny||e.e<-ny))throw Error(nf+r)}else e.s=0,e.e=0,e.d=[0];return e}function nS(e,t,r){var n,i,a,o,l,c,u,s,f=e.d;for(o=1,a=f[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,u=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(a=f.length))return e;for(o=1,u=a=f[s];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(l=u/(a=nd(10,o-i-1))%10|0,c=t<0||void 0!==f[s+1]||u%a,c=r<4?(l||c)&&(0==r||r==(e.s<0?3:2)):l>5||5==l&&(4==r||c||6==r&&(n>0?i>0?u/nd(10,o-i):0:f[s-1])%10&1||r==(e.s<0?8:7))),t<1||!f[0])return c?(a=nO(e),f.length=1,t=t-a-1,f[0]=nd(10,(7-t%7)%7),e.e=nh(-t/7)||0):(f.length=1,f[0]=e.e=e.s=0),e;if(0==n?(f.length=s,a=1,s--):(f.length=s+1,a=nd(10,7-n),f[s]=i>0?(u/nd(10,o-i)%nd(10,i)|0)*a:0),c)for(;;)if(0==s){1e7==(f[0]+=a)&&(f[0]=1,++e.e);break}else{if(f[s]+=a,1e7!=f[s])break;f[s--]=0,a=1}for(n=f.length;0===f[--n];)f.pop();if(nc&&(e.e>ny||e.e<-ny))throw Error(nf+nO(e));return e}function nE(e,t){var r,n,i,a,o,l,c,u,s,f,h=e.constructor,d=h.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new h(e),nc?nS(t,d):t;if(c=e.d,f=t.d,n=t.e,u=e.e,c=c.slice(),o=u-n){for((s=o<0)?(r=c,o=-o,l=f.length):(r=f,n=u,l=c.length),o>(i=Math.max(Math.ceil(d/7),l)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((s=(i=c.length)<(l=f.length))&&(l=i),i=0;i<l;i++)if(c[i]!=f[i]){s=c[i]<f[i];break}o=0}for(s&&(r=c,c=f,f=r,t.s=-t.s),l=c.length,i=f.length-l;i>0;--i)c[l++]=0;for(i=f.length;i>o;){if(c[--i]<f[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=f[i]}for(;0===c[--l];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,nc?nS(t,d):t):new h(0)}function nk(e,t,r){var n,i=nO(e),a=nb(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+nM(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+nM(-i-1)+a,r&&(n=r-o)>0&&(a+=nM(n))):i>=o?(a+=nM(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+nM(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=nM(n))),e.s<0?"-"+a:a}function n_(e,t){if(e.length>t)return e.length=t,!0}function nT(e){if(!e||"object"!=typeof e)throw Error(nu+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(nh(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(ns+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(ns+r+": "+n);return this}var no=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(ns+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return nA(this,e.toString())}if("string"!=typeof e)throw Error(ns+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,np.test(e))nA(this,e);else throw Error(ns+e)}if(a.prototype=nv,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=nT,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});nl=new no(1);let nC=no;var nD=e=>e,nN={},nz=e=>e===nN,nI=e=>function t(){return 0==arguments.length||1==arguments.length&&nz(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},nL=(e,t)=>1===e?t:nI(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==nN).length;return a>=e?t(...n):nL(e-a,nI(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>nz(e)?r.shift():e),...r)}))}),nR=e=>nL(e.length,e),n$=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},nU=nR((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),nF=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return nD;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},nB=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),nH=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function nK(e){var t;return 0===e?1:Math.floor(new nC(e).abs().log(10).toNumber())+1}function nG(e,t,r){for(var n=new nC(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}nR((e,t,r)=>{var n=+e;return n+r*(t-n)}),nR((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),nR((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var nZ=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},nq=(e,t,r)=>{if(e.lte(0))return new nC(0);var n=nK(e.toNumber()),i=new nC(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new nC(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new nC(t?l.toNumber():Math.ceil(l.toNumber()))},nW=(e,t,r)=>{var n=new nC(1),i=new nC(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new nC(10).pow(nK(e)-1),i=new nC(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new nC(Math.floor(e)))}else 0===e?i=new nC(Math.floor((t-1)/2)):r||(i=new nC(Math.floor(e)));var o=Math.floor((t-1)/2);return nF(nU(e=>i.add(new nC(e-o).mul(n)).toNumber()),n$)(0,t)},nV=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new nC(0),tickMin:new nC(0),tickMax:new nC(0)};var o=nq(new nC(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new nC(0):(i=new nC(e).add(t).div(2)).sub(new nC(i).mod(o))).sub(e).div(o).toNumber()),c=Math.ceil(new nC(t).sub(i).div(o).toNumber()),u=l+c+1;return u>r?nV(e,t,r,n,a+1):(u<r&&(c=t>0?c+(r-u):c,l=t>0?l:l+(r-u)),{step:o,tickMin:i.sub(new nC(l).mul(o)),tickMax:i.add(new nC(c).mul(o))})},nY=nH(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=nZ([t,r]);if(o===-1/0||l===1/0){var c=l===1/0?[o,...n$(0,n-1).map(()=>1/0)]:[...n$(0,n-1).map(()=>-1/0),l];return t>r?nB(c):c}if(o===l)return nW(o,n,i);var{step:u,tickMin:s,tickMax:f}=nV(o,l,a,i,0),h=nG(s,f.add(new nC(.1).mul(u)),u);return t>r?nB(h):h}),nX=nH(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=nZ([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),c=nq(new nC(o).sub(a).div(l-1),i,0),u=[...nG(new nC(a),new nC(o),c),o];return!1===i&&(u=u.map(e=>Math.round(e))),r>n?nB(u):u}),nJ=r(86445),nQ=r(23814),n0=r(35034),n1=r(94728),n2=r(97350),n4=r(8920),n5=r(36166),n3=r(60559),n6=r(53416),n8=r(75601),n7=r(6548),n9=r(54526),ie=r(50380),it=r(64574);function ir(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ii(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ir(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ir(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ia=[0,"auto"],io={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},il=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?io:r},ic={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ia,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:n8.tQ},iu=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?ic:r},is={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ih=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?is:r},id=(e,t,r)=>{switch(t){case"xAxis":return il(e,r);case"yAxis":return iu(e,r);case"zAxis":return ih(e,r);case"angleAxis":return(0,n4.Be)(e,r);case"radiusAxis":return(0,n4.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},ip=(e,t,r)=>{switch(t){case"xAxis":return il(e,r);case"yAxis":return iu(e,r);case"angleAxis":return(0,n4.Be)(e,r);case"radiusAxis":return(0,n4.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},iy=e=>e.graphicalItems.cartesianItems.some(e=>"bar"===e.type)||e.graphicalItems.polarItems.some(e=>"radialBar"===e.type);function iv(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var ig=e=>e.graphicalItems.cartesianItems,im=(0,i.Mz)([n5.N,n3.E],iv),ib=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),ix=(0,i.Mz)([ig,id,im],ib),iw=(0,i.Mz)([ix],e=>e.filter(e=>"area"===e.type||"bar"===e.type).filter(it.g)),iO=e=>e.filter(e=>!("stackId"in e)||void 0===e.stackId),ij=(0,i.Mz)([ix],iO),iM=e=>e.map(e=>e.data).filter(Boolean).flat(1),iP=(0,i.Mz)([ix],iM),iA=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},iS=(0,i.Mz)([iP,r5.HS],iA),iE=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,r4.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,r4.kr)(e,t)}))):e.map(e=>({value:e})),ik=(0,i.Mz)([iS,id,ix],iE);function i_(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function iT(e){return e.filter(e=>(0,r3.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,r3.M8)(e))}var iC=(0,i.Mz)([iw,r5.HS,n9.D],ie.A),iD=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(n7.x);return[n,{stackedData:(0,r4.yy)(e,a,r),graphicalItems:i}]})),iN=(0,i.Mz)([iC,iw,n2.eC],iD),iz=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,r4.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},iI=(0,i.Mz)([iN,r5.LF,n5.N],iz),iL=(e,t,r,n,i)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var a,o,l=null==(a=n[r.id])?void 0:a.filter(e=>i_(i,e)),c=(0,r4.kr)(e,null!=(o=t.dataKey)?o:r.dataKey);return{value:c,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,r3.M8)(t)||!r.length?[]:iT(r.flatMap(r=>{var n,i,a=(0,r4.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,r6.H)(n)&&(0,r6.H)(i))return[t-n,t+i]}))}(e,c,l)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,r4.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),iR=e=>e.errorBars,i$=(e,t,r)=>e.flatMap(e=>t[e.id]).filter(Boolean).filter(e=>i_(r,e));(0,i.Mz)([ij,iR,n5.N],i$);var iU=(0,i.Mz)([iS,id,ij,iR,n5.N],iL);function iF(e){var{value:t}=e;if((0,r3.vh)(t)||t instanceof Date)return t}var iB=e=>{var t=iT(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},iH=(e,t,r)=>{var n=e.map(iF).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,r3.CG)(n))?o()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},iK=e=>{var t;if(null==e||!("domain"in e))return ia;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=iT(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:ia},iG=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},iZ=e=>e.referenceElements.dots,iq=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),iW=(0,i.Mz)([iZ,n5.N,n3.E],iq),iV=e=>e.referenceElements.areas,iY=(0,i.Mz)([iV,n5.N,n3.E],iq),iX=e=>e.referenceElements.lines,iJ=(0,i.Mz)([iX,n5.N,n3.E],iq),iQ=(e,t)=>{var r=iT(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},i0=(0,i.Mz)(iW,n5.N,iQ),i1=(e,t)=>{var r=iT(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},i2=(0,i.Mz)([iY,n5.N],i1),i4=(e,t)=>{var r=iT(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},i5=(0,i.Mz)(iJ,n5.N,i4),i3=(0,i.Mz)(i0,i5,i2,(e,t,r)=>iG(e,r,t)),i6=(0,i.Mz)([id],iK),i8=(e,t,r,n,i,a,o)=>{var l=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,r6.H)(i))r=i;else if("function"==typeof i)return;if((0,r6.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(r8(o))return o}}(t,e.allowDataOverflow);return null!=l?l:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(r8(n))return r7(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,r3.Et)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&r4.IH.test(o)){var c=r4.IH.exec(o);if(null==c||null==t)i=void 0;else{var u=+c[1];i=t[0]-u}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if((0,r3.Et)(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&r4.qx.test(l)){var s=r4.qx.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var h=[i,a];if(r8(h))return null==t?h:r7(h,t,r)}}}(t,"vertical"===a&&"xAxis"===o||"horizontal"===a&&"yAxis"===o?iG(r,i,iB(n)):iG(i,iB(n)),e.allowDataOverflow)},i7=(0,i.Mz)([id,i6,iI,iU,i3,r2.fz,n5.N],i8),i9=[0,1],ae=(e,t,r,n,i,a,l)=>{if(null!=e&&null!=r&&0!==r.length||void 0!==l){var{dataKey:c,type:u}=e,s=(0,r4._L)(t,a);return s&&null==c?o()(0,r.length):"category"===u?iH(n,e,s):"expand"===i?i9:l}},at=(0,i.Mz)([id,r2.fz,iS,ik,n2.eC,n5.N,i7],ae),ar=(e,t,r,i,a)=>{if(null!=e){var{scale:o,type:l}=e;if("auto"===o)return"radial"===t&&"radiusAxis"===a?"band":"radial"===t&&"angleAxis"===a?"linear":"category"===l&&i&&(i.indexOf("LineChart")>=0||i.indexOf("AreaChart")>=0||i.indexOf("ComposedChart")>=0&&!r)?"point":"category"===l?"band":"linear";if("string"==typeof o){var c="scale".concat((0,r3.Zb)(o));return c in n?c:"point"}}},an=(0,i.Mz)([id,r2.fz,iy,n2.iO,n5.N],ar);function ai(e,t,r,i){if(null!=r&&null!=i){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(i);var a=function(e){if(null!=e){if(e in n)return n[e]();var t="scale".concat((0,r3.Zb)(e));if(t in n)return n[t]()}}(t);if(null!=a){var o=a.domain(r).range(i);return(0,r4.YB)(o),o}}}var aa=(e,t,r)=>{var n=iK(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&r8(e))return nY(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&r8(e))return nX(e,t.tickCount,t.allowDecimals)}},ao=(0,i.Mz)([at,ip,an],aa),al=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&r8(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,ac=(0,i.Mz)([id,at,ao,n5.N],al),au=(0,i.Mz)(ik,id,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(iT(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),as=(0,i.Mz)(au,r2.fz,n2.gY,n0.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,r6.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,r3.F4)(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),af=(0,i.Mz)(il,(e,t)=>{var r=il(e,t);return null==r||"string"!=typeof r.padding?0:as(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),ah=(0,i.Mz)(iu,(e,t)=>{var r=iu(e,t);return null==r||"string"!=typeof r.padding?0:as(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),ad=(0,i.Mz)([n0.HZ,af,n1.U,n1.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),ap=(0,i.Mz)([n0.HZ,r2.fz,ah,n1.U,n1.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),ay=(e,t,r,n)=>{var i;switch(t){case"xAxis":return ad(e,r,n);case"yAxis":return ap(e,r,n);case"zAxis":return null==(i=ih(e,r))?void 0:i.range;case"angleAxis":return(0,n4.Cv)(e);case"radiusAxis":return(0,n4.Dc)(e,r);default:return}},av=(0,i.Mz)([id,ay],n6.I),ag=(0,i.Mz)([id,an,ac,av],ai);function am(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,i.Mz)([ix,iR,n5.N],i$);var ab=(e,t)=>t,ax=(e,t,r)=>r,aw=(0,i.Mz)(nQ.h,ab,ax,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(am)),aO=(0,i.Mz)(nQ.W,ab,ax,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(am)),aj=(e,t)=>({width:e.width,height:t.height}),aM=(e,t)=>({width:"number"==typeof t.width?t.width:n8.tQ,height:e.height}),aP=(0,i.Mz)(n0.HZ,il,aj),aA=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},aS=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},aE=(0,i.Mz)(nJ.A$,n0.HZ,aw,ab,ax,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=aj(t,r);null==a&&(a=aA(t,n,e));var c="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(c)*l.height,a+=(c?-1:1)*l.height}),o}),ak=(0,i.Mz)(nJ.Lp,n0.HZ,aO,ab,ax,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=aM(t,r);null==a&&(a=aS(t,n,e));var c="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(c)*l.width,a+=(c?-1:1)*l.width}),o}),a_=(e,t)=>{var r=(0,n0.HZ)(e),n=il(e,t);if(null!=n){var i=aE(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},aT=(e,t)=>{var r=(0,n0.HZ)(e),n=iu(e,t);if(null!=n){var i=ak(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},aC=(0,i.Mz)(n0.HZ,iu,(e,t)=>({width:"number"==typeof t.width?t.width:n8.tQ,height:e.height})),aD=(e,t,r)=>{switch(t){case"xAxis":return aP(e,r).width;case"yAxis":return aC(e,r).height;default:return}},aN=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,r4._L)(e,n),c=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&(0,r3.CG)(c))return c}},az=(0,i.Mz)([r2.fz,ik,id,n5.N],aN),aI=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,r4._L)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},aL=(0,i.Mz)([r2.fz,ik,ip,n5.N],aI),aR=(0,i.Mz)([r2.fz,(e,t,r)=>{switch(t){case"xAxis":return il(e,r);case"yAxis":return iu(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},an,ag,az,aL,ay,ao,n5.N],(e,t,r,n,i,a,o,l,c)=>{if(null==t)return null;var u=(0,r4._L)(e,c);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:c,categoricalDomain:a,duplicateDomain:i,isCategorical:u,niceTicks:l,range:o,realScaleType:r,scale:n}}),a$=(0,i.Mz)([r2.fz,ip,an,ag,ao,ay,az,aL,n5.N],(e,t,r,n,i,a,o,l,c)=>{if(null!=t&&null!=n){var u=(0,r4._L)(e,c),{type:s,ticks:f,tickCount:h}=t,d="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/d:0;p="angleAxis"===c&&null!=a&&a.length>=2?2*(0,r3.sA)(a[0]-a[1])*p:p;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+p,value:e,offset:p})).filter(e=>!(0,r3.M8)(e.coordinate)):u&&l?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.ticks?n.ticks(h).map(e=>({coordinate:n(e)+p,value:e,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:o?o[e]:e,index:t,offset:p}))}}),aU=(0,i.Mz)([r2.fz,ip,ag,ay,az,aL,n5.N],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,r4._L)(e,o),{tickCount:c}=t,u=0;return(u="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,r3.sA)(n[0]-n[1])*u:u,l&&a)?a.map((e,t)=>({coordinate:r(e)+u,value:e,index:t,offset:u})):r.ticks?r.ticks(c).map(e=>({coordinate:r(e)+u,value:e,offset:u})):r.domain().map((e,t)=>({coordinate:r(e)+u,value:i?i[e]:e,index:t,offset:u}))}}),aF=(0,i.Mz)(id,ag,(e,t)=>{if(null!=e&&null!=t)return ii(ii({},e),{},{scale:t})}),aB=(0,i.Mz)([id,an,at,av],ai);(0,i.Mz)((e,t,r)=>ih(e,r),aB,(e,t)=>{if(null!=e&&null!=t)return ii(ii({},e),{},{scale:t})});var aH=(0,i.Mz)([r2.fz,nQ.h,nQ.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},85778:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},86445:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},87509:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(35314),i=r(76431),a=r(30657),o=r(43574);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var c=t,u=r,s=l;if(0===u.length)return s;let e=c;for(let t=0;t<u.length;t++){if(null==e||n.isUnsafeProperty(u[t]))return s;e=e[u[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},90015:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},90830:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(26349),i=r(49899);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},91428:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},91653:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},92292:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(69404),i=r(90015);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},92681:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},92867:(e,t,r)=>{e.exports=r(60324).isPlainObject},92923:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(91653),i=r(91428),a=r(27469),o=r(23457),l=r(21251);function c(e,t,r,n=new Map,s){let f=s?.(e,t,r,n);if(null!=f)return f;if(o.isPrimitive(e))return e;if(n.has(e))return n.get(e);if(Array.isArray(e)){let t=Array(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,s);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[i,a]of(n.set(e,t),e))t.set(i,c(a,i,r,n,s));return t}if(e instanceof Set){let t=new Set;for(let i of(n.set(e,t),e))t.add(c(i,void 0,r,n,s));return t}if("undefined"!=typeof Buffer&&Buffer.isBuffer(e))return e.subarray();if(l.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);n.set(e,t);for(let i=0;i<e.length;i++)t[i]=c(e[i],i,r,n,s);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return n.set(e,t),u(t,e,r,n,s),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return n.set(e,t),u(t,e,r,n,s),t}if(e instanceof Error){let t=new e.constructor;return n.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,u(t,e,r,n,s),t}if("object"==typeof e&&function(e){switch(i.getTag(e)){case a.argumentsTag:case a.arrayTag:case a.arrayBufferTag:case a.dataViewTag:case a.booleanTag:case a.dateTag:case a.float32ArrayTag:case a.float64ArrayTag:case a.int8ArrayTag:case a.int16ArrayTag:case a.int32ArrayTag:case a.mapTag:case a.numberTag:case a.objectTag:case a.regexpTag:case a.setTag:case a.stringTag:case a.symbolTag:case a.uint8ArrayTag:case a.uint8ClampedArrayTag:case a.uint16ArrayTag:case a.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return n.set(e,t),u(t,e,r,n,s),t}return e}function u(e,t,r=e,i,a){let o=[...Object.keys(t),...n.getSymbols(t)];for(let n=0;n<o.length;n++){let l=o[n],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,i,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=u},94728:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>c});var n=r(84648),i=r(35034),a=r(86445),o=r(22989),l=e=>e.brush,c=(0,n.Mz)([l,i.HZ,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},95819:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},97051:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},97350:(e,t,r)=>{"use strict";r.d(t,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>a,hX:()=>s,iO:()=>c,lZ:()=>u,pH:()=>f,x3:()=>o});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,a=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,c=e=>e.options.chartName,u=e=>e.rootProps.syncId,s=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},97371:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(12128),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},97633:(e,t,r)=>{"use strict";r.d(t,{J:()=>P,Z:()=>b});var n=r(43210),i=r(49384),a=r(23561),o=r(54186),l=r(22989),c=r(19335),u=r(51426),s=r(43209),f=r(8920),h=["offset"],d=["labelRef"];function p(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var m=e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n},b=e=>null!=e&&"function"==typeof e,x=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),360),w=(e,t,r,a)=>{var o,u,{position:s,offset:f,className:h}=e,{cx:d,cy:p,innerRadius:y,outerRadius:v,startAngle:m,endAngle:b,clockWise:w}=a,O=(y+v)/2,j=x(m,b),M=j>=0?1:-1;"insideStart"===s?(o=m+M*f,u=w):"insideEnd"===s?(o=b-M*f,u=!w):"end"===s&&(o=b+M*f,u=w),u=j<=0?u:!u;var P=(0,c.IZ)(d,p,O,o),A=(0,c.IZ)(d,p,O,o+(u?1:-1)*359),S="M".concat(P.x,",").concat(P.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!u,",\n    ").concat(A.x,",").concat(A.y),E=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",g({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",h)}),n.createElement("defs",null,n.createElement("path",{id:E,d:S})),n.createElement("textPath",{xlinkHref:"#".concat(E)},t))},O=(e,t,r)=>{var{cx:n,cy:i,innerRadius:a,outerRadius:o,startAngle:l,endAngle:u}=e,s=(l+u)/2;if("outside"===r){var{x:f,y:h}=(0,c.IZ)(n,i,o+t,s);return{x:f,y:h,textAnchor:f>=n?"start":"end",verticalAnchor:"middle"}}if("center"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===r)return{x:n,y:i,textAnchor:"middle",verticalAnchor:"end"};var{x:d,y:p}=(0,c.IZ)(n,i,(a+o)/2,s);return{x:d,y:p,textAnchor:"middle",verticalAnchor:"middle"}},j=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:c,height:u}=t,s=u>=0?1:-1,f=s*n,h=s>0?"end":"start",d=s>0?"start":"end",p=c>=0?1:-1,y=p*n,g=p>0?"end":"start",m=p>0?"start":"end";if("top"===i)return v(v({},{x:a+c/2,y:o-s*n,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(o-r.y,0),width:c}:{});if("bottom"===i)return v(v({},{x:a+c/2,y:o+u+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(o+u),0),width:c}:{});if("left"===i){var b={x:a-y,y:o+u/2,textAnchor:g,verticalAnchor:"middle"};return v(v({},b),r?{width:Math.max(b.x-r.x,0),height:u}:{})}if("right"===i){var x={x:a+c+y,y:o+u/2,textAnchor:m,verticalAnchor:"middle"};return v(v({},x),r?{width:Math.max(r.x+r.width-x.x,0),height:u}:{})}var w=r?{width:c,height:u}:{};return"insideLeft"===i?v({x:a+y,y:o+u/2,textAnchor:m,verticalAnchor:"middle"},w):"insideRight"===i?v({x:a+c-y,y:o+u/2,textAnchor:g,verticalAnchor:"middle"},w):"insideTop"===i?v({x:a+c/2,y:o+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===i?v({x:a+c/2,y:o+u-f,textAnchor:"middle",verticalAnchor:h},w):"insideTopLeft"===i?v({x:a+y,y:o+f,textAnchor:m,verticalAnchor:d},w):"insideTopRight"===i?v({x:a+c-y,y:o+f,textAnchor:g,verticalAnchor:d},w):"insideBottomLeft"===i?v({x:a+y,y:o+u-f,textAnchor:m,verticalAnchor:h},w):"insideBottomRight"===i?v({x:a+c-y,y:o+u-f,textAnchor:g,verticalAnchor:h},w):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?v({x:a+(0,l.F4)(i.x,c),y:o+(0,l.F4)(i.y,u),textAnchor:"end",verticalAnchor:"end"},w):v({x:a+c/2,y:o+u/2,textAnchor:"middle",verticalAnchor:"middle"},w)},M=e=>"cx"in e&&(0,l.Et)(e.cx);function P(e){var t,{offset:r=5}=e,c=v({offset:r},p(e,h)),{viewBox:y,position:b,value:x,children:P,content:A,className:S="",textBreakAll:E,labelRef:k}=c,_=(0,s.G)(f.D0),T=(0,u.sk)(),C=y||("center"===b?T:null!=_?_:T);if(!C||(0,l.uy)(x)&&(0,l.uy)(P)&&!(0,n.isValidElement)(A)&&"function"!=typeof A)return null;var D=v(v({},c),{},{viewBox:C});if((0,n.isValidElement)(A)){var{labelRef:N}=D,z=p(D,d);return(0,n.cloneElement)(A,z)}if("function"==typeof A){if(t=(0,n.createElement)(A,D),(0,n.isValidElement)(t))return t}else t=m(c);var I=M(C),L=(0,o.J9)(c,!0);if(I&&("insideStart"===b||"insideEnd"===b||"end"===b))return w(c,t,L,C);var R=I?O(C,c.offset,c.position):j(c,C);return n.createElement(a.E,g({ref:k,className:(0,i.$)("recharts-label",S)},L,R,{breakAll:E}),t)}P.displayName="Label";var A=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:c,innerRadius:u,outerRadius:s,x:f,y:h,top:d,left:p,width:y,height:v,clockWise:g,labelViewBox:m}=e;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(v)){if((0,l.Et)(f)&&(0,l.Et)(h))return{x:f,y:h,width:y,height:v};if((0,l.Et)(d)&&(0,l.Et)(p))return{x:d,y:p,width:y,height:v}}return(0,l.Et)(f)&&(0,l.Et)(h)?{x:f,y:h,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:u||0,outerRadius:s||c||o||0,clockWise:g}:e.viewBox?e.viewBox:void 0},S=(e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(P,g({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(P,g({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===P?(0,n.cloneElement)(e,v({key:"label-implicit"},i)):n.createElement(P,g({key:"label-implicit",content:e},i)):b(e)?n.createElement(P,g({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(P,g({},e,{key:"label-implicit"},i)):null};P.parseViewBox=A,P.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,l=A(e),c=(0,o.aS)(i,P).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)}));return r?[S(e.label,t||l,a),...c]:c}},97668:(e,t)=>{"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,i=r?Symbol.for("react.portal"):60106,a=r?Symbol.for("react.fragment"):60107,o=r?Symbol.for("react.strict_mode"):60108,l=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,u=r?Symbol.for("react.context"):60110,s=r?Symbol.for("react.async_mode"):60111,f=r?Symbol.for("react.concurrent_mode"):60111,h=r?Symbol.for("react.forward_ref"):60112,d=r?Symbol.for("react.suspense"):60113,p=(r&&Symbol.for("react.suspense_list"),r?Symbol.for("react.memo"):60115),y=r?Symbol.for("react.lazy"):60116;function v(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case s:case f:case a:case l:case o:case d:return e;default:switch(e=e&&e.$$typeof){case u:case h:case y:case p:case c:return e;default:return t}}case i:return t}}}r&&Symbol.for("react.block"),r&&Symbol.for("react.fundamental"),r&&Symbol.for("react.responder"),r&&Symbol.for("react.scope");t.isFragment=function(e){return v(e)===a}},97711:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(43210),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},97766:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},98009:(e,t,r)=>{"use strict";r.d(t,{l3:()=>u,m7:()=>s}),r(43210);var n=r(43209),i=r(97350);new(r(11117)),r(49605),r(17118);var a=r(21426),o=r(69009);function l(e){return e.tooltip.syncInteraction}var c=r(51426);function u(){(0,n.j)(),(0,n.G)(i.lZ),(0,n.G)(i.pH),(0,n.j)(),(0,n.G)(i.hX),(0,n.G)(o.R4),(0,c.WX)(),(0,c.sk)(),(0,n.G)(e=>e.rootProps.className),(0,n.G)(i.lZ),(0,n.G)(i.pH),(0,n.j)()}function s(e,t,r,o,c,u){(0,n.G)(r=>(0,a.dp)(r,e,t)),(0,n.G)(i.pH),(0,n.G)(i.lZ),(0,n.G)(i.hX);var s=(0,n.G)(l);null==s||s.active}r(64267)},98150:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},98845:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var n=r(43210),i=r(9474),a=r.n(i),o=r(97633),l=r(98986),c=r(54186),u=r(64279),s=r(22989),f=["valueAccessor"],h=["data","dataKey","clockWise","id","textBreakAll"];function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function y(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function v(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var g=e=>Array.isArray(e.value)?a()(e.value):e.value;function m(e){var{valueAccessor:t=g}=e,r=v(e,f),{data:i,dataKey:a,clockWise:p,id:m,textBreakAll:b}=r,x=v(r,h);return i&&i.length?n.createElement(l.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,s.uy)(a)?t(e,r):(0,u.kr)(e&&e.payload,a),l=(0,s.uy)(m)?{}:{id:"".concat(m,"-").concat(r)};return n.createElement(o.J,d({},(0,c.J9)(e,!0),x,l,{parentViewBox:e.parentViewBox,value:i,textBreakAll:b,viewBox:o.J.parseViewBox((0,s.uy)(p)?e:y(y({},e),{},{clockWise:p})),key:"label-".concat(r),index:r}))})):null}m.displayName="LabelList",m.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,l=(0,c.aS)(a,m).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(m,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,o.Z)(r)?n.createElement(m,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(m,d({data:t},r,{key:"labelList-implicit"})):null:null),...l]:l}},98986:(e,t,r)=>{"use strict";r.d(t,{W:()=>c});var n=r(43210),i=r(49384),a=r(54186),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=n.forwardRef((e,t)=>{var{children:r,className:c}=e,u=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.$)("recharts-layer",c);return n.createElement("g",l({className:s},(0,a.J9)(u,!0),{ref:t}),r)})},99857:(e,t,r)=>{"use strict";r.d(t,{R:()=>i,u:()=>a});var n=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"];function i(e){return"string"==typeof e&&n.includes(e)}function a(e){return Object.fromEntries(Object.entries(e).filter(e=>{var[t]=e;return i(t)}))}}};