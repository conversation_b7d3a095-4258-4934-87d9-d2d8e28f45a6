"use strict";exports.id=5746,exports.ids=[5746],exports.modules={1428:(e,r,a)=>{a.d(r,{j:()=>v});var t=a(60687),s=a(96834),i=a(29523),o=a(38553),n=a(95450),l=a(67663),c=a(93383),d=a(56085),h=a(70334),m=a(45583),g=a(41312),u=a(28947),p=a(16189),x=a(43210);function v({variant:e,title:r,subtitle:a,showWelcomeBadge:v=!1,showQuickActions:b=!1,showFeatureBadges:y=!1}){let{isSignedIn:f}=(0,n.P)(),{trackClick:j,trackCustomEvent:k}=(0,o.s)(),N=(0,p.useRouter)(),[w,A]=(0,x.useState)(0),[_,C]=(0,x.useState)(""),[z,M]=(0,x.useState)(!0),[S,E]=(0,x.useState)(""),[F,$]=(0,x.useState)(!1),{setIdeaText:q,startQuestionnaire:L,setCurrentProgress:D,setTotalSteps:T,incrementCompletedSteps:V,setCurrentStep:B,setCreatedProject:H}=(0,c.h)(),{addEventListener:I,removeEventListener:P}=(0,l.a8)(),G=(r,a)=>{"landing"===e&&(j("quick-action-badge","hero-section"),k("badge_clicked",{badge_type:a,location:"hero-section"})),E(r)},Q="dashboard"===e;return(0,t.jsxs)("div",{className:"w-full max-w-sm mx-auto",children:[(r||a||v)&&(0,t.jsxs)("div",{className:"mb-8 text-center",children:[v&&(0,t.jsx)("div",{className:"flex items-center justify-center gap-2 mb-4",children:(0,t.jsxs)(s.E,{variant:"secondary",className:"bg-[#166534]/10 text-[#166534] border-[#166534]/20",children:[(0,t.jsx)(d.A,{className:"w-3 h-3 mr-1"}),"Welcome back"]})}),r&&(0,t.jsx)("h1",{className:"text-xl md:text-2xl lg:text-4xl font-regular text-foreground mb-6 leading-tight",children:r}),a&&(0,t.jsx)("p",{className:"text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-2xl mx-auto",children:a})]}),(0,t.jsxs)("div",{className:`group relative ${Q?"bg-[#166534]/5 hover:bg-[#166534]/8":"bg-card/80"} backdrop-blur border rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300`,children:[(0,t.jsx)("div",{className:`absolute inset-0 bg-gradient-to-br ${Q?"from-[#166534]/8 via-transparent to-[#22c55e]/8":"from-[var(--primary)]/5 via-transparent to-[var(--primary-light)]/5"} pointer-events-none`}),(0,t.jsxs)("div",{className:"relative z-10",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("textarea",{value:S,onChange:r=>{E(r.target.value),"landing"===e&&1===r.target.value.length&&k("project_input_started",{location:"hero-section"})},onFocus:()=>{"landing"===e&&k("project_input_focused",{location:"hero-section"})},placeholder:_,className:`w-full px-4 py-3 pr-16 rounded-lg ${Q?"bg-[#166534]/5 hover:bg-[#166534]/10":"bg-background/50"} backdrop-blur text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 ${Q?"focus:ring-[#26F000]/40":"focus:ring-[var(--primary)]/20 focus:border-[var(--primary)]"} resize-none min-h-[80px] transition-all duration-300 hover:shadow-lg relative z-10`,rows:3,disabled:F}),(0,t.jsx)(i.$,{onClick:()=>{S.trim()&&("dashboard"===e?($(!0),q(S),L(),N.push("/projects/create")):(j("generate-project-button","hero-section"),k("project_generation_attempted",{user_signed_in:f,project_input_length:S.length,has_project_input:S.trim().length>0,redirect_destination:f?"dashboard":"register"}),f&&S.trim()?(q(S),L(),N.push("/projects/create")):f?N.push("/user-dashboard"):N.push("/auth/register")))},disabled:!S.trim()||F,className:`absolute right-3 bottom-4 ${Q?"bg-[#CEFFC5] hover:bg-[#26F000] text-black hover:text-black":"bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]"} shadow-lg z-20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300`,size:"icon",children:F?(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}):(0,t.jsx)(h.A,{className:"w-4 h-4"})})]}),y&&(0,t.jsxs)("div",{className:"flex items-center justify-center gap-4 text-xs text-muted-foreground mt-4",children:[(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),"AI-powered insights"]}),(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full animate-pulse"}),"Smart project planning"]}),(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full animate-pulse"}),"Team collaboration"]})]}),b&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-2 justify-center mt-4",children:[(0,t.jsxs)(s.E,{variant:"outline",className:"text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer",onClick:()=>G("Create a project tracker that manages team tasks and deadlines","project_tracker"),children:[(0,t.jsx)(m.A,{className:"w-3 h-3 mr-1"}),"Project tracker"]}),(0,t.jsxs)(s.E,{variant:"outline",className:"text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer",onClick:()=>G("Create a team collaboration platform for better communication","team_collaboration"),children:[(0,t.jsx)(g.A,{className:"w-3 h-3 mr-1"}),"Team collaboration"]}),(0,t.jsxs)(s.E,{variant:"outline",className:"text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer",onClick:()=>G("Create a goal management system to track objectives and milestones","goal_management"),children:[(0,t.jsx)(u.A,{className:"w-3 h-3 mr-1"}),"Goal management"]})]})]})]})]})}},25541:(e,r,a)=>{a.d(r,{A:()=>t});let t=(0,a(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},28947:(e,r,a)=>{a.d(r,{A:()=>t});let t=(0,a(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},41312:(e,r,a)=>{a.d(r,{A:()=>t});let t=(0,a(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},43984:(e,r,a)=>{a.d(r,{U:()=>d});var t=a(60687);a(43210);var s=a(21134),i=a(363),o=a(10218),n=a(70042),l=a(66420),c=a(21342);function d(){let{setTheme:e}=(0,o.D)();return(0,t.jsxs)(c.rI,{children:[(0,t.jsx)(c.ty,{asChild:!0,children:(0,t.jsxs)("div",{children:[(0,t.jsx)(n.V,{icon:s.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:l.hS.lg,className:"dark:hidden"}),(0,t.jsx)(n.V,{icon:i.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",hoverScale:!0,iconClassName:l.hS.md,className:"hidden dark:block"}),(0,t.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,t.jsxs)(c.SQ,{align:"end",className:"bg-background border-border",children:[(0,t.jsx)(c._2,{onClick:()=>e("light"),className:"hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary",children:"Light"}),(0,t.jsx)(c._2,{onClick:()=>e("dark"),className:"hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary",children:"Dark"}),(0,t.jsx)(c._2,{onClick:()=>e("system"),className:"hover:bg-primary/10 hover:text-primary focus:bg-primary/10 focus:text-primary",children:"System"})]})]})}},45583:(e,r,a)=>{a.d(r,{A:()=>t});let t=(0,a(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},48730:(e,r,a)=>{a.d(r,{A:()=>t});let t=(0,a(62688).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},56085:(e,r,a)=>{a.d(r,{A:()=>t});let t=(0,a(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},70334:(e,r,a)=>{a.d(r,{A:()=>t});let t=(0,a(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},96834:(e,r,a)=>{a.d(r,{E:()=>l});var t=a(60687);a(43210);var s=a(8730),i=a(24224),o=a(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,asChild:a=!1,...i}){let l=a?s.DX:"span";return(0,t.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(n({variant:r}),e),...i})}}};