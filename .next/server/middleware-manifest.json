{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!.*\\..*|_next).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!.*\\..*|_next).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "kvLh7k73MtPVgE4oUXBkN", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "HLzcP9+d7ESUuFMvFHyrlkI5Klk/fAtxhYaFvYQxcew=", "__NEXT_PREVIEW_MODE_ID": "7eaeb74949007ad55e81ef2a905d24c7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "fdf90244e7f2e760db1d409c73f69355e8aff85349fda6bfa012e1b4241ee953", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe1e5370ed13ad53a866874c68f7f160070337b9ee14aa2790dfd84e9654de00"}}}, "functions": {}, "sortedMiddleware": ["/"]}