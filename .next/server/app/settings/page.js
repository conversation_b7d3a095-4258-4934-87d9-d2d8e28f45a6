(()=>{var e={};e.id=4662,e.ids=[4181,4662],e.modules={363:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3363:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(60687),a=r(93854),n=r(74456);function i({children:e,showHeader:t=!0,showFooter:r=!0,constrainHeight:i=!1}){return(0,s.jsxs)("div",{className:`${i?"h-screen":"min-h-screen"} flex flex-col bg-background`,children:[t&&(0,s.jsx)(n.<PERSON><PERSON>,{}),(0,s.jsx)("main",{className:`flex-1 ${i?"min-h-0":""}`,children:e}),r&&(0,s.jsx)(a.Footer,{})]})}function o({children:e}){return(0,s.jsx)(i,{showFooter:!1,constrainHeight:!0,children:(0,s.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:e})})}},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6102:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(43210);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9549:(e,t,r)=>{Promise.resolve().then(r.bind(r,74992))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21134:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},26134:(e,t,r)=>{"use strict";r.d(t,{UC:()=>er,VY:()=>ea,ZL:()=>ee,bL:()=>K,bm:()=>en,hE:()=>es,hJ:()=>et,l9:()=>Q});var s=r(43210),a=r(70569),n=r(98599),i=r(11273),o=r(96963),d=r(65551),c=r(31355),l=r(32547),u=r(25028),p=r(46059),m=r(14163),x=r(1359),f=r(42247),h=r(63376),g=r(8730),v=r(60687),b="Dialog",[y,j]=(0,i.A)(b),[w,N]=y(b),k=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:i,modal:c=!0}=e,l=s.useRef(null),u=s.useRef(null),[p,m]=(0,d.i)({prop:a,defaultProp:n??!1,onChange:i,caller:b});return(0,v.jsx)(w,{scope:t,triggerRef:l,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:p,onOpenChange:m,onOpenToggle:s.useCallback(()=>m(e=>!e),[m]),modal:c,children:r})};k.displayName=b;var A="DialogTrigger",P=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,i=N(A,r),o=(0,n.s)(t,i.triggerRef);return(0,v.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":$(i.open),...s,ref:o,onClick:(0,a.m)(e.onClick,i.onOpenToggle)})});P.displayName=A;var C="DialogPortal",[D,M]=y(C,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,i=N(C,t);return(0,v.jsx)(D,{scope:t,forceMount:r,children:s.Children.map(a,e=>(0,v.jsx)(p.C,{present:r||i.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};E.displayName=C;var R="DialogOverlay",_=s.forwardRef((e,t)=>{let r=M(R,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=N(R,e.__scopeDialog);return n.modal?(0,v.jsx)(p.C,{present:s||n.open,children:(0,v.jsx)(I,{...a,ref:t})}):null});_.displayName=R;var S=(0,g.TL)("DialogOverlay.RemoveScroll"),I=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=N(R,r);return(0,v.jsx)(f.A,{as:S,allowPinchZoom:!0,shards:[a.contentRef],children:(0,v.jsx)(m.sG.div,{"data-state":$(a.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),F="DialogContent",z=s.forwardRef((e,t)=>{let r=M(F,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=N(F,e.__scopeDialog);return(0,v.jsx)(p.C,{present:s||n.open,children:n.modal?(0,v.jsx)(q,{...a,ref:t}):(0,v.jsx)(O,{...a,ref:t})})});z.displayName=F;var q=s.forwardRef((e,t)=>{let r=N(F,e.__scopeDialog),i=s.useRef(null),o=(0,n.s)(t,r.contentRef,i);return s.useEffect(()=>{let e=i.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(U,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),O=s.forwardRef((e,t)=>{let r=N(F,e.__scopeDialog),a=s.useRef(!1),n=s.useRef(!1);return(0,v.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"===t.detail.originalEvent.type&&(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),U=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:o,...d}=e,u=N(F,r),p=s.useRef(null),m=(0,n.s)(t,p);return(0,x.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(l.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,v.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":$(u.open),...d,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Y,{titleId:u.titleId}),(0,v.jsx)(X,{contentRef:p,descriptionId:u.descriptionId})]})]})}),G="DialogTitle",T=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=N(G,r);return(0,v.jsx)(m.sG.h2,{id:a.titleId,...s,ref:t})});T.displayName=G;var B="DialogDescription",Z=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=N(B,r);return(0,v.jsx)(m.sG.p,{id:a.descriptionId,...s,ref:t})});Z.displayName=B;var J="DialogClose",L=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=N(J,r);return(0,v.jsx)(m.sG.button,{type:"button",...s,ref:t,onClick:(0,a.m)(e.onClick,()=>n.onOpenChange(!1))})});function $(e){return e?"open":"closed"}L.displayName=J;var H="DialogTitleWarning",[V,W]=(0,i.q)(H,{contentName:F,titleName:G,docsSlug:"dialog"}),Y=({titleId:e})=>{let t=W(H),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},X=({contentRef:e,descriptionId:t})=>{let r=W("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return s.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(a))},[a,e,t]),null},K=k,Q=P,ee=E,et=_,er=z,es=T,ea=Z,en=L},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35950:(e,t,r)=>{"use strict";r.d(t,{w:()=>i});var s=r(60687);r(43210);var a=r(62369),n=r(4780);function i({className:e,orientation:t="horizontal",decorative:r=!0,...i}){return(0,s.jsx)(a.b,{"data-slot":"separator",decorative:r,orientation:t,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...i})}},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},40211:(e,t,r)=>{"use strict";r.d(t,{C1:()=>N,bL:()=>j});var s=r(43210),a=r(98599),n=r(11273),i=r(70569),o=r(65551),d=r(6102),c=r(18853),l=r(46059),u=r(14163),p=r(60687),m="Checkbox",[x,f]=(0,n.A)(m),[h,g]=x(m);function v(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:n,disabled:i,form:d,name:c,onCheckedChange:l,required:u,value:x="on",internal_do_not_use_render:f}=e,[g,v]=(0,o.i)({prop:r,defaultProp:n??!1,onChange:l,caller:m}),[b,y]=s.useState(null),[j,w]=s.useState(null),N=s.useRef(!1),k=!b||!!d||!!b.closest("form"),A={checked:g,disabled:i,setChecked:v,control:b,setControl:y,name:c,form:d,value:x,hasConsumerStoppedPropagationRef:N,required:u,defaultChecked:!P(n)&&n,isFormControl:k,bubbleInput:j,setBubbleInput:w};return(0,p.jsx)(h,{scope:t,...A,children:"function"==typeof f?f(A):a})}var b="CheckboxTrigger",y=s.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...n},o)=>{let{control:d,value:c,disabled:l,checked:m,required:x,setControl:f,setChecked:h,hasConsumerStoppedPropagationRef:v,isFormControl:y,bubbleInput:j}=g(b,e),w=(0,a.s)(o,f),N=s.useRef(m);return s.useEffect(()=>{let e=d?.form;if(e){let t=()=>h(N.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,h]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":P(m)?"mixed":m,"aria-required":x,"data-state":C(m),"data-disabled":l?"":void 0,disabled:l,value:c,...n,ref:w,onKeyDown:(0,i.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(r,e=>{h(e=>!!P(e)||!e),j&&y&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});y.displayName=b;var j=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:s,checked:a,defaultChecked:n,required:i,disabled:o,value:d,onCheckedChange:c,form:l,...u}=e;return(0,p.jsx)(v,{__scopeCheckbox:r,checked:a,defaultChecked:n,disabled:o,required:i,onCheckedChange:c,name:s,form:l,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(y,{...u,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(A,{__scopeCheckbox:r})]})})});j.displayName=m;var w="CheckboxIndicator",N=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:s,...a}=e,n=g(w,r);return(0,p.jsx)(l.C,{present:s||P(n.checked)||!0===n.checked,children:(0,p.jsx)(u.sG.span,{"data-state":C(n.checked),"data-disabled":n.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});N.displayName=w;var k="CheckboxBubbleInput",A=s.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:n,hasConsumerStoppedPropagationRef:i,checked:o,defaultChecked:l,required:m,disabled:x,name:f,value:h,form:v,bubbleInput:b,setBubbleInput:y}=g(k,e),j=(0,a.s)(r,y),w=(0,d.Z)(o),N=(0,c.X)(n);s.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(w!==o&&e){let r=new Event("click",{bubbles:t});b.indeterminate=P(o),e.call(b,!P(o)&&o),b.dispatchEvent(r)}},[b,w,o,i]);let A=s.useRef(!P(o)&&o);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:l??A.current,required:m,disabled:x,name:f,value:h,form:v,...t,tabIndex:-1,ref:j,style:{...t.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function P(e){return"indeterminate"===e}function C(e){return P(e)?"indeterminate":e?"checked":"unchecked"}A.displayName=k},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>l});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},45933:(e,t,r)=>{Promise.resolve().then(r.bind(r,74198))},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687),a=r(43210),n=r(14163),i=a.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=r(4780);function d({className:e,...t}){return(0,s.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},56896:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var s=r(60687);r(43210);var a=r(40211),n=r(13964),i=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[2px] border-2 transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(n.A,{className:"size-3.5 stroke-[3]"})})})}},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62157:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},62369:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var s=r(43210),a=r(14163),n=r(60687),i="horizontal",o=["horizontal","vertical"],d=s.forwardRef((e,t)=>{var r;let{decorative:s,orientation:d=i,...c}=e,l=(r=d,o.includes(r))?d:i;return(0,n.jsx)(a.sG.div,{"data-orientation":l,...s?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...c,ref:t})});d.displayName="Separator";var c=d},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72575:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},74198:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx","dynamic"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx","default")},74992:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>E,dynamic:()=>P});var s=r(60687),a=r(558),n=r(97051),i=r(41862),o=r(8819),d=r(99891),c=r(5336),l=r(12597),u=r(13861),p=r(11437),m=r(93613),x=r(43210),f=r(27605),h=r(43214),g=r(3363),v=r(96834),b=r(29523),y=r(44493),j=r(56896),w=r(89667),N=r(54300),k=r(35950),A=r(87979);let P="force-dynamic",C=h.Ik({emailNotifications:h.zM(),projectUpdates:h.zM(),securityAlerts:h.zM(),marketingEmails:h.zM()}),D=h.Ik({currentPassword:h.Yj().min(1,"Current password is required"),newPassword:h.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:h.Yj().min(1,"Please confirm your password")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]}),M=e=>{try{return(e instanceof Date?e:new Date(e)).toLocaleDateString()}catch(e){return"Invalid date"}};function E(){let{user:e}=(0,A.A)(),[t,r]=(0,x.useState)(!1),[h,P]=(0,x.useState)(!1),[E,R]=(0,x.useState)(null),{register:_,handleSubmit:S,formState:{isDirty:I},reset:F}=(0,f.mN)({resolver:(0,a.u)(C),defaultValues:{emailNotifications:!0,projectUpdates:!0,securityAlerts:!0,marketingEmails:!1}}),{register:z,handleSubmit:q,formState:{errors:O},reset:U}=(0,f.mN)({resolver:(0,a.u)(D)}),G=async e=>{r(!0),R(null);try{await new Promise(e=>setTimeout(e,1e3)),R({type:"success",text:"Notification settings updated successfully!"}),F(e)}catch(e){R({type:"error",text:e.message||"Failed to update notification settings"})}finally{r(!1)}},T=async e=>{r(!0),R(null);try{await new Promise(e=>setTimeout(e,1e3)),R({type:"success",text:"Password updated successfully!"}),U()}catch(e){R({type:"error",text:e.message||"Failed to update password"})}finally{r(!1)}};return e?(0,s.jsx)(g.N,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Settings"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and preferences."})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(y.Zp,{className:"bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(y.aR,{children:[(0,s.jsxs)(y.ZB,{className:"flex items-center space-x-2 text-card-foreground",children:[(0,s.jsx)(n.A,{className:"h-5 w-5 text-accent"}),(0,s.jsx)("span",{children:"Notification Settings"})]}),(0,s.jsx)(y.BT,{className:"text-muted-foreground",children:"Configure how you receive notifications and updates."})]}),(0,s.jsx)(y.Wu,{children:(0,s.jsxs)("form",{onSubmit:S(G),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.S,{id:"emailNotifications",..._("emailNotifications"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,s.jsx)(N.J,{htmlFor:"emailNotifications",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Email Notifications"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Receive important updates and notifications via email."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.S,{id:"projectUpdates",..._("projectUpdates"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,s.jsx)(N.J,{htmlFor:"projectUpdates",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Project Updates"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Get notified when projects you're working on are updated."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.S,{id:"securityAlerts",..._("securityAlerts"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,s.jsx)(N.J,{htmlFor:"securityAlerts",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Security Alerts"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Receive notifications about security-related activities on your account."})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(j.S,{id:"marketingEmails",..._("marketingEmails"),className:"border-border data-[state=checked]:bg-accent data-[state=checked]:border-accent"}),(0,s.jsx)(N.J,{htmlFor:"marketingEmails",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-card-foreground",children:"Marketing Emails"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground ml-6",children:"Receive promotional emails and newsletters about new features."})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(b.$,{type:"submit",disabled:t||!I,className:"bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border",children:[t&&(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Save Notification Settings"]})})]})})]}),(0,s.jsxs)(y.Zp,{className:"bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(y.aR,{children:[(0,s.jsxs)(y.ZB,{className:"flex items-center space-x-2 text-card-foreground",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 text-accent"}),(0,s.jsx)("span",{children:"Security Settings"})]}),(0,s.jsx)(y.BT,{className:"text-muted-foreground",children:"Manage your account security and password."})]}),(0,s.jsx)(y.Wu,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-card-foreground",children:"Account Status"}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 text-green-500"}),(0,s.jsx)("span",{className:"text-sm text-card-foreground",children:"Email Verified"}),(0,s.jsx)(v.E,{variant:"outline",className:"text-xs border-green-500/20 text-green-600 bg-green-500/10",children:"Verified"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(d.A,{className:"h-4 w-4 text-blue-500"}),(0,s.jsx)("span",{className:"text-sm text-card-foreground",children:"Two-Factor Auth"}),(0,s.jsx)(v.E,{variant:"outline",className:"text-xs border-yellow-500/20 text-yellow-600 bg-yellow-500/10",children:"Disabled"})]})]})]}),(0,s.jsx)(k.w,{className:"bg-border"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-card-foreground",children:"Change Password"}),(0,s.jsxs)("form",{onSubmit:q(T),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"currentPassword",className:"text-card-foreground",children:"Current Password"}),(0,s.jsx)(w.p,{id:"currentPassword",type:"password",...z("currentPassword"),placeholder:"Enter your current password",className:"border-border focus:border-accent focus:ring-accent/20"}),O.currentPassword&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:O.currentPassword.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"newPassword",className:"text-card-foreground",children:"New Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(w.p,{id:"newPassword",type:h?"text":"password",...z("newPassword"),placeholder:"Enter your new password",className:"border-border focus:border-accent focus:ring-accent/20"}),(0,s.jsx)(b.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-accent/10 hover:text-accent",onClick:()=>P(!h),children:h?(0,s.jsx)(l.A,{className:"h-4 w-4"}):(0,s.jsx)(u.A,{className:"h-4 w-4"})})]}),O.newPassword&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:O.newPassword.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(N.J,{htmlFor:"confirmPassword",className:"text-card-foreground",children:"Confirm New Password"}),(0,s.jsx)(w.p,{id:"confirmPassword",type:"password",...z("confirmPassword"),placeholder:"Confirm your new password",className:"border-border focus:border-accent focus:ring-accent/20"}),O.confirmPassword&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:O.confirmPassword.message})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(b.$,{type:"submit",disabled:t,className:"bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border",children:[t&&(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Update Password"]})})]})]})]})})]}),(0,s.jsxs)(y.Zp,{className:"bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(y.aR,{children:[(0,s.jsxs)(y.ZB,{className:"flex items-center space-x-2 text-card-foreground",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 text-accent"}),(0,s.jsx)("span",{children:"Account Information"})]}),(0,s.jsx)(y.BT,{className:"text-muted-foreground",children:"Your account details and preferences."})]}),(0,s.jsx)(y.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(N.J,{className:"text-sm font-medium text-card-foreground",children:"Email Address"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(N.J,{className:"text-sm font-medium text-card-foreground",children:"Account Created"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:M(e.createdAt)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(N.J,{className:"text-sm font-medium text-card-foreground",children:"Last Updated"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:M(e.updatedAt)})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(N.J,{className:"text-sm font-medium text-card-foreground",children:"Account Status"}),(0,s.jsx)(v.E,{variant:"outline",className:"text-xs border-green-500/20 text-green-600 bg-green-500/10",children:"Active"})]})]})})})]})]}),E&&(0,s.jsx)("div",{className:`p-4 rounded-md border ${"success"===E.type?"bg-green-500/10 text-green-700 border-green-500/20":"bg-red-500/10 text-red-700 border-red-500/20"}`,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:["success"===E.type?(0,s.jsx)(c.A,{className:"h-4 w-4"}):(0,s.jsx)(m.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:E.text})]})})]})}):(0,s.jsx)(g.N,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-destructive mb-2",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"You need to be signed in to view this page."})]})})})}},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96293:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74198)),"/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(60687);r(43210);var a=r(8730),n=r(24224),i=r(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...n}){let d=r?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},97051:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},98876:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,878,5788,2604,1838,4336,2403],()=>r(96293));module.exports=s})();