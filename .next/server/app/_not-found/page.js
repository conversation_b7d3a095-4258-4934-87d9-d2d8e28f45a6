(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50211:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>l,pages:()=>u,routeModule:()=>x,tree:()=>p});var s=t(65239),n=t(48088),a=t(88170),o=t.n(a),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,u=[],l={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},96487:()=>{}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[80,4999,8360,1838],()=>t(50211));module.exports=s})();