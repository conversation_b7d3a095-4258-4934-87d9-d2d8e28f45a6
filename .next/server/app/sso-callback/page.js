(()=>{var e={};e.id=1762,e.ids=[1762],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},36005:(e,r,t)=>{Promise.resolve().then(t.bind(t,79202))},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},49157:(e,r,t)=>{Promise.resolve().then(t.bind(t,52760))},52760:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(60687),a=t(14792),n=t(41862);function i(){return(0,s.jsxs)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:[(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)(n.A,{className:"h-8 w-8 animate-spin mx-auto text-primary"}),(0,s.jsx)("h2",{className:"text-lg font-semibold",children:"Completing sign in..."}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Please wait while we redirect you."})]}),(0,s.jsx)(a.B$,{})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79202:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},83437:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["sso-callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79202)),"/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/sso-callback/page",pathname:"/sso-callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[80,4999,8360,1838],()=>t(83437));module.exports=s})();