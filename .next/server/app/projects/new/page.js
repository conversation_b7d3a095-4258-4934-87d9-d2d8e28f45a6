(()=>{var e={};e.id=8580,e.ids=[8580],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3363:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(60687),a=r(93854),n=r(74456);function i({children:e,showHeader:t=!0,showFooter:r=!0,constrainHeight:i=!1}){return(0,s.jsxs)("div",{className:`${i?"h-screen":"min-h-screen"} flex flex-col bg-background`,children:[t&&(0,s.jsx)(n.<PERSON><PERSON>,{}),(0,s.jsx)("main",{className:`flex-1 ${i?"min-h-0":""}`,children:e}),r&&(0,s.jsx)(a<PERSON>,{})]})}function o({children:e}){return(0,s.jsx)(i,{showFooter:!1,constrainHeight:!0,children:(0,s.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:e})})}},5105:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let d={children:["",{children:["projects",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,53484)),"/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/projects/new/page",pathname:"/projects/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21933:(e,t,r)=>{Promise.resolve().then(r.bind(r,87342))},24141:(e,t,r)=>{Promise.resolve().then(r.bind(r,53484))},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43229:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});let s="siift_access_token",a="siift_refresh_token",n="siift_user";class i{static setTokens(e,t=!1){let r=t?localStorage:sessionStorage;r.setItem(s,e.accessToken),e.refreshToken&&r.setItem(a,e.refreshToken)}static getAccessToken(){return localStorage.getItem(s)||sessionStorage.getItem(s)}static getRefreshToken(){return localStorage.getItem(a)||sessionStorage.getItem(a)}static setUser(e,t=!1){(t?localStorage:sessionStorage).setItem(n,JSON.stringify(e))}static getUser(){try{let e=localStorage.getItem(n)||sessionStorage.getItem(n);if(!e)return null;let t=JSON.parse(e);return t.createdAt&&"string"==typeof t.createdAt&&(t.createdAt=new Date(t.createdAt)),t.updatedAt&&"string"==typeof t.updatedAt&&(t.updatedAt=new Date(t.updatedAt)),t}catch(e){return console.error("Error parsing user data:",e),null}}static clearSession(){[localStorage,sessionStorage].forEach(e=>{e.removeItem(s),e.removeItem(a),e.removeItem(n)})}static clearInvalidSession(){let e=this.getAccessToken();e&&(!e.includes(".")||3!==e.split(".").length)&&(console.log("Clearing invalid token format"),this.clearSession())}static isAuthenticated(){return!!this.getAccessToken()}static getAuthHeaders(){let e=this.getAccessToken();return e?{Authorization:`Bearer ${e}`}:{}}}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>l});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},53484:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx","dynamic"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx","default")},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(60687),a=r(43210),n=r(14163),i=a.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=r(4780);function c({className:e,...t}){return(0,s.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},62185:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(43229);class a{async request(e,t={}){let r=`/api${e}`,a={headers:{"Content-Type":"application/json",...s.C.getAuthHeaders(),...t.headers},...t};try{let e=await fetch(r,a);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||`HTTP error! status: ${e.status}`)}let t=await e.json();if(t.success&&void 0!==t.data)return t.data;return t}catch(t){throw console.error(`API request failed: ${e}`,t),t}}async login(e){return this.request("/auth/signin",{method:"POST",body:JSON.stringify(e)})}async register(e){return this.request("/auth/signup",{method:"POST",body:JSON.stringify(e)})}async refreshToken(e){return this.request("/auth/refresh",{method:"POST",body:JSON.stringify({refreshToken:e})})}async verifyToken(e){try{return await this.request("/auth/verify",{method:"POST",headers:{Authorization:`Bearer ${e}`}}),!0}catch{return!1}}async logout(){return this.request("/auth/logout",{method:"POST"})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/profile",{method:"PUT",body:JSON.stringify(e)})}async getProjects(){return this.request("/projects")}async getProject(e){return this.request(`/projects/${e}`)}async createProject(e){return this.request("/projects",{method:"POST",body:JSON.stringify(e)})}async updateProject(e,t){return this.request(`/projects/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteProject(e){return this.request(`/projects/${e}`,{method:"DELETE"})}}let n=new a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},87342:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y,dynamic:()=>v});var s=r(60687),a=r(43210),n=r(16189),i=r(85814),o=r.n(i),c=r(27605),d=r(558),l=r(43214),u=r(28559),p=r(41862),m=r(3363),h=r(29523),f=r(89667),x=r(54300),g=r(44493),j=r(62185);let v="force-dynamic",b=l.Ik({name:l.Yj().min(1,"Project name is required").max(100,"Project name must be less than 100 characters"),description:l.Yj().min(1,"Project description is required").max(500,"Description must be less than 500 characters")});function y(){let[e,t]=(0,a.useState)(!1),[r,i]=(0,a.useState)(null),l=(0,n.useRouter)(),{register:v,handleSubmit:y,formState:{errors:w}}=(0,c.mN)({resolver:(0,d.u)(b),defaultValues:{name:"",description:""}}),P=async e=>{t(!0),i(null);try{let t=await j.Z.createProject(e);l.push(`/projects/${t.id}`)}catch(e){i(e.message||"Failed to create project")}finally{t(!1)}};return(0,s.jsx)(m.N,{children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(h.$,{asChild:!0,variant:"outline",size:"sm",children:(0,s.jsxs)(o(),{href:"/projects",children:[(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4"}),"Back to Projects"]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Create New Project"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Start a new project and bring your ideas to life"})]})]}),(0,s.jsxs)(g.Zp,{children:[(0,s.jsxs)(g.aR,{children:[(0,s.jsx)(g.ZB,{children:"Project Details"}),(0,s.jsx)(g.BT,{children:"Provide basic information about your project"})]}),(0,s.jsx)(g.Wu,{children:(0,s.jsxs)("form",{onSubmit:y(P),className:"space-y-6",children:[r&&(0,s.jsx)("div",{className:"p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md",children:r}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(x.J,{htmlFor:"name",children:"Project Name *"}),(0,s.jsx)(f.p,{id:"name",placeholder:"Enter project name",...v("name"),className:w.name?"border-destructive":""}),w.name&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:w.name.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(x.J,{htmlFor:"description",children:"Description *"}),(0,s.jsx)("textarea",{id:"description",placeholder:"Describe your project...",rows:4,...v("description"),className:`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${w.description?"border-destructive":""}`}),w.description&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:w.description.message})]}),(0,s.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,s.jsx)(h.$,{type:"submit",disabled:e,children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create Project"}),(0,s.jsx)(h.$,{asChild:!0,type:"button",variant:"outline",children:(0,s.jsx)(o(),{href:"/projects",children:"Cancel"})})]})]})})]})]})})}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,878,5788,2604,4181,1838,4336,2403],()=>r(5105));module.exports=s})();