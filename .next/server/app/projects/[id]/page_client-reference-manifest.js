globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/projects/[id]/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(ssr)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx":{"*":{"id":"(ssr)/./src/components/analytics/PostHogProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/ClerkSessionProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/ClerkSessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/QueryProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/QueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/SessionProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/SessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme-provider.tsx":{"*":{"id":"(ssr)/./src/components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(ssr)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/background-context.tsx":{"*":{"id":"(ssr)/./src/contexts/background-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/loading.tsx":{"*":{"id":"(ssr)/./src/app/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(ssr)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/landing-page.tsx":{"*":{"id":"(ssr)/./src/components/landing/landing-page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/user-dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/user-dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/[id]/page.tsx":{"*":{"id":"(ssr)/./src/app/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Data/new era/siift-next/node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"id":"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/script.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/script.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/font/local/target.css?{\"path\":\"src/lib/fonts.ts\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/outfit/Outfit-Thin.ttf\",\"weight\":\"100\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-ExtraLight.ttf\",\"weight\":\"200\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Light.ttf\",\"weight\":\"300\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Regular.ttf\",\"weight\":\"400\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Medium.ttf\",\"weight\":\"500\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-SemiBold.ttf\",\"weight\":\"600\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Bold.ttf\",\"weight\":\"700\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-ExtraBold.ttf\",\"weight\":\"800\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Black.ttf\",\"weight\":\"900\",\"style\":\"normal\"}],\"variable\":\"--font-outfit\",\"display\":\"swap\"}],\"variableName\":\"outfit\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"src/lib/fonts.ts\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"../../public/fonts/outfit/Outfit-Thin.ttf\",\"weight\":\"100\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-ExtraLight.ttf\",\"weight\":\"200\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Light.ttf\",\"weight\":\"300\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Regular.ttf\",\"weight\":\"400\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Medium.ttf\",\"weight\":\"500\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-SemiBold.ttf\",\"weight\":\"600\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Bold.ttf\",\"weight\":\"700\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-ExtraBold.ttf\",\"weight\":\"800\",\"style\":\"normal\"},{\"path\":\"../../public/fonts/outfit/Outfit-Black.ttf\",\"weight\":\"900\",\"style\":\"normal\"}],\"variable\":\"--font-outfit\",\"display\":\"swap\"}],\"variableName\":\"outfit\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/font/local/target.css?{\"path\":\"src/lib/fonts.ts\",\"import\":\"\",\"arguments\":[{\"src\":\"../../public/fonts/outfit/Outfit-VariableFont_wght.ttf\",\"variable\":\"--font-outfit-variable\",\"weight\":\"100 900\",\"display\":\"swap\"}],\"variableName\":\"outfitVariable\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"src/lib/fonts.ts\",\"import\":\"\",\"arguments\":[{\"src\":\"../../public/fonts/outfit/Outfit-VariableFont_wght.ttf\",\"variable\":\"--font-outfit-variable\",\"weight\":\"100 900\",\"display\":\"swap\"}],\"variableName\":\"outfitVariable\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx":{"id":"(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/ClerkSessionProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/QueryProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/SessionProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx":{"id":"(app-pages-browser)/./src/components/theme-provider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx":{"id":"(app-pages-browser)/./src/components/ui/sonner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx":{"id":"(app-pages-browser)/./src/contexts/background-context.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx":{"id":"(app-pages-browser)/./src/app/loading.tsx","name":"*","chunks":["app/loading","static/chunks/app/loading.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx":{"id":"(app-pages-browser)/./src/app/not-found.tsx","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx":{"id":"(app-pages-browser)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/components/landing/landing-page.tsx":{"id":"(app-pages-browser)/./src/components/landing/landing-page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx":{"id":"(app-pages-browser)/./src/app/user-dashboard/page.tsx","name":"*","chunks":[],"async":false},"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx":{"id":"(app-pages-browser)/./src/app/projects/[id]/page.tsx","name":"*","chunks":["app/projects/[id]/page","static/chunks/app/projects/%5Bid%5D/page.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Data/new era/siift-next/src/":[],"/Users/<USER>/Data/new era/siift-next/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/Data/new era/siift-next/src/app/error":[],"/Users/<USER>/Data/new era/siift-next/src/app/loading":[],"/Users/<USER>/Data/new era/siift-next/src/app/not-found":[],"/Users/<USER>/Data/new era/siift-next/src/app/page":[],"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/app-router/client/keyless-cookie-sync.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/PromisifiedAuthProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(rsc)/./node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/script.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/script.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/analytics/PostHogProvider.tsx":{"*":{"id":"(rsc)/./src/components/analytics/PostHogProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/ClerkSessionProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/ClerkSessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/QueryProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/QueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/SessionProvider.tsx":{"*":{"id":"(rsc)/./src/components/providers/SessionProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/theme-provider.tsx":{"*":{"id":"(rsc)/./src/components/theme-provider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/sonner.tsx":{"*":{"id":"(rsc)/./src/components/ui/sonner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/background-context.tsx":{"*":{"id":"(rsc)/./src/contexts/background-context.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(rsc)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/loading.tsx":{"*":{"id":"(rsc)/./src/app/loading.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/not-found.tsx":{"*":{"id":"(rsc)/./src/app/not-found.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/login/page.tsx":{"*":{"id":"(rsc)/./src/app/auth/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/landing/landing-page.tsx":{"*":{"id":"(rsc)/./src/components/landing/landing-page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/user-dashboard/page.tsx":{"*":{"id":"(rsc)/./src/app/user-dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/projects/[id]/page.tsx":{"*":{"id":"(rsc)/./src/app/projects/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}