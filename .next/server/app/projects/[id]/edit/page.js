(()=>{var e={};e.id=2326,e.ids=[2326],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3363:(e,t,s)=>{"use strict";s.d(t,{N:()=>o});var r=s(60687),a=s(93854),i=s(74456);function n({children:e,showHeader:t=!0,showFooter:s=!0,constrainHeight:n=!1}){return(0,r.jsxs)("div",{className:`${n?"h-screen":"min-h-screen"} flex flex-col bg-background`,children:[t&&(0,r.jsx)(i.<PERSON><PERSON>,{}),(0,r.jsx)("main",{className:`flex-1 ${n?"min-h-0":""}`,children:e}),s&&(0,r.jsx)(a<PERSON>,{})]})}function o({children:e}){return(0,r.jsx)(n,{showFooter:!1,constrainHeight:!0,children:(0,r.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:e})})}},10815:(e,t,s)=>{Promise.resolve().then(s.bind(s,77294))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11864:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N,dynamic:()=>y});var r=s(60687),a=s(558),i=s(41862),n=s(28559),o=s(85814),d=s.n(o),c=s(16189),l=s(43210),u=s(27605),p=s(43214),m=s(3363),h=s(29523),x=s(44493),f=s(89667),g=s(54300),v=s(15079),j=s(87979),b=s(62185);let y="force-dynamic",w=p.Ik({name:p.Yj().min(1,"Project name is required").max(100,"Project name must be less than 100 characters"),description:p.Yj().min(1,"Project description is required").max(500,"Description must be less than 500 characters"),status:p.k5(["active","completed","archived"])});function N({params:e}){let[t,s]=(0,l.useState)(null),[o,p]=(0,l.useState)(!0),[y,N]=(0,l.useState)(!1),[P,S]=(0,l.useState)(null),[k,A]=(0,l.useState)(null),q=(0,c.useRouter)(),{user:_}=(0,j.A)(),{register:C,handleSubmit:T,formState:{errors:z},setValue:$,watch:D}=(0,u.mN)({resolver:(0,a.u)(w)}),I=D("status"),O=async e=>{if(t){N(!0),S(null);try{await b.Z.updateProject(t.id,e),q.push(`/projects/${t.id}`)}catch(e){S(e.message||"Failed to update project")}finally{N(!1)}}};return o?(0,r.jsx)(m.N,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(i.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Loading project..."})]})})}):P||!t?(0,r.jsx)(m.N,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-destructive mb-4",children:P||"Project not found"}),(0,r.jsx)(h.$,{asChild:!0,children:(0,r.jsx)(d(),{href:"/projects",children:"Back to Projects"})})]})})}):_?.id!==t.userId&&_?.role!=="admin"?(0,r.jsx)(m.N,{children:(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-destructive mb-4",children:"You don't have permission to edit this project"}),(0,r.jsx)(h.$,{asChild:!0,children:(0,r.jsx)(d(),{href:`/projects/${t.id}`,children:"Back to Project"})})]})})}):(0,r.jsx)(m.N,{children:(0,r.jsxs)("div",{className:"max-w-2xl mx-auto space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(h.$,{asChild:!0,variant:"outline",size:"sm",children:(0,r.jsxs)(d(),{href:`/projects/${t.id}`,children:[(0,r.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Back to Project"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Project"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Update your project details"})]})]}),(0,r.jsxs)(x.Zp,{children:[(0,r.jsxs)(x.aR,{children:[(0,r.jsx)(x.ZB,{children:"Project Details"}),(0,r.jsx)(x.BT,{children:"Modify the information about your project"})]}),(0,r.jsx)(x.Wu,{children:(0,r.jsxs)("form",{onSubmit:T(O),className:"space-y-6",children:[P&&(0,r.jsx)("div",{className:"p-3 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md",children:P}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(g.J,{htmlFor:"name",children:"Project Name *"}),(0,r.jsx)(f.p,{id:"name",placeholder:"Enter project name",...C("name"),className:z.name?"border-destructive":""}),z.name&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:z.name.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(g.J,{htmlFor:"description",children:"Description *"}),(0,r.jsx)("textarea",{id:"description",placeholder:"Describe your project...",rows:4,...C("description"),className:`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${z.description?"border-destructive":""}`}),z.description&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:z.description.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(g.J,{htmlFor:"status",children:"Status"}),(0,r.jsxs)(v.l6,{value:I,onValueChange:e=>$("status",e),children:[(0,r.jsx)(v.bq,{children:(0,r.jsx)(v.yv,{placeholder:"Select status"})}),(0,r.jsxs)(v.gC,{children:[(0,r.jsx)(v.eb,{value:"active",children:"Active"}),(0,r.jsx)(v.eb,{value:"completed",children:"Completed"}),(0,r.jsx)(v.eb,{value:"archived",children:"Archived"})]})]}),z.status&&(0,r.jsx)("p",{className:"text-sm text-destructive",children:z.status.message})]}),(0,r.jsxs)("div",{className:"flex gap-4 pt-4",children:[(0,r.jsx)(h.$,{type:"submit",disabled:y,children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Changes"}),(0,r.jsx)(h.$,{asChild:!0,type:"button",variant:"outline",children:(0,r.jsx)(d(),{href:`/projects/${t.id}`,children:"Cancel"})})]})]})})]})]})})}},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>c,yv:()=>l});var r=s(60687);s(43210);var a=s(72951),i=s(78272),n=s(13964),o=s(3589),d=s(4780);function c({...e}){return(0,r.jsx)(a.bL,{"data-slot":"select",...e})}function l({...e}){return(0,r.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:s,...n}){return(0,r.jsxs)(a.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,r.jsx)(a.In,{asChild:!0,children:(0,r.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...i}){return(0,r.jsx)(a.ZL,{children:(0,r.jsxs)(a.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,r.jsx)(h,{}),(0,r.jsx)(a.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,r.jsx)(x,{})]})})}function m({className:e,children:t,...s}){return(0,r.jsxs)(a.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(a.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(a.p4,{children:t})]})}function h({className:e,...t}){return(0,r.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(o.A,{className:"size-4"})})}function x({className:e,...t}){return(0,r.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43229:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});let r="siift_access_token",a="siift_refresh_token",i="siift_user";class n{static setTokens(e,t=!1){let s=t?localStorage:sessionStorage;s.setItem(r,e.accessToken),e.refreshToken&&s.setItem(a,e.refreshToken)}static getAccessToken(){return localStorage.getItem(r)||sessionStorage.getItem(r)}static getRefreshToken(){return localStorage.getItem(a)||sessionStorage.getItem(a)}static setUser(e,t=!1){(t?localStorage:sessionStorage).setItem(i,JSON.stringify(e))}static getUser(){try{let e=localStorage.getItem(i)||sessionStorage.getItem(i);if(!e)return null;let t=JSON.parse(e);return t.createdAt&&"string"==typeof t.createdAt&&(t.createdAt=new Date(t.createdAt)),t.updatedAt&&"string"==typeof t.updatedAt&&(t.updatedAt=new Date(t.updatedAt)),t}catch(e){return console.error("Error parsing user data:",e),null}}static clearSession(){[localStorage,sessionStorage].forEach(e=>{e.removeItem(r),e.removeItem(a),e.removeItem(i)})}static clearInvalidSession(){let e=this.getAccessToken();e&&(!e.includes(".")||3!==e.split(".").length)&&(console.log("Clearing invalid token format"),this.clearSession())}static isAuthenticated(){return!!this.getAccessToken()}static getAuthHeaders(){let e=this.getAccessToken();return e?{Authorization:`Bearer ${e}`}:{}}}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>l});var r=s(60687);s(43210);var a=s(4780);function i({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function l({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>d});var r=s(60687),a=s(43210),i=s(14163),n=a.forwardRef((e,t)=>(0,r.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=s(4780);function d({className:e,...t}){return(0,r.jsx)(n,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},62185:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var r=s(43229);class a{async request(e,t={}){let s=`/api${e}`,a={headers:{"Content-Type":"application/json",...r.C.getAuthHeaders(),...t.headers},...t};try{let e=await fetch(s,a);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||`HTTP error! status: ${e.status}`)}let t=await e.json();if(t.success&&void 0!==t.data)return t.data;return t}catch(t){throw console.error(`API request failed: ${e}`,t),t}}async login(e){return this.request("/auth/signin",{method:"POST",body:JSON.stringify(e)})}async register(e){return this.request("/auth/signup",{method:"POST",body:JSON.stringify(e)})}async refreshToken(e){return this.request("/auth/refresh",{method:"POST",body:JSON.stringify({refreshToken:e})})}async verifyToken(e){try{return await this.request("/auth/verify",{method:"POST",headers:{Authorization:`Bearer ${e}`}}),!0}catch{return!1}}async logout(){return this.request("/auth/logout",{method:"POST"})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/profile",{method:"PUT",body:JSON.stringify(e)})}async getProjects(){return this.request("/projects")}async getProject(e){return this.request(`/projects/${e}`)}async createProject(e){return this.request("/projects",{method:"POST",body:JSON.stringify(e)})}async updateProject(e,t){return this.request(`/projects/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteProject(e){return this.request(`/projects/${e}`,{method:"DELETE"})}}let i=new a},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69024:(e,t,s)=>{"use strict";s.d(t,{Qg:()=>n,bL:()=>d});var r=s(43210),a=s(14163),i=s(60687),n=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),o=r.forwardRef((e,t)=>(0,i.jsx)(a.sG.span,{...e,ref:t,style:{...n,...e.style}}));o.displayName="VisuallyHidden";var d=o},71063:(e,t,s)=>{Promise.resolve().then(s.bind(s,11864))},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77294:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx","dynamic"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx","default")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(60687);s(43210);var a=s(4780);function i({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},93961:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),o=s(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);s.d(t,d);let c={children:["",{children:["projects",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,77294)),"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/projects/[id]/edit/page",pathname:"/projects/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[80,4999,8360,878,5788,2604,5555,4181,1838,4336,2403],()=>s(93961));module.exports=r})();