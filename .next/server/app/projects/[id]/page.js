(()=>{var e={};e.id=6801,e.ids=[6801],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>g,gC:()=>p,l6:()=>d,yv:()=>c});var a=r(60687);r(43210);var i=r(72951),n=r(78272),s=r(13964),o=r(3589),l=r(4780);function d({...e}){return(0,a.jsx)(i.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(i.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...s}){return(0,a.jsxs)(i.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,children:[r,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...n}){return(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,a.jsx)(m,{}),(0,a.jsx)(i.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function g({className:e,children:t,...r}){return(0,a.jsxs)(i.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(s.A,{className:"size-4"})})}),(0,a.jsx)(i.p4,{children:t})]})}function m({className:e,...t}){return(0,a.jsx)(i.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(i.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},18257:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s1});var a=r(60687),i=r(29523),n=r(70042),s=r(11860),o=r(96474);function l({activeContent:e,setActiveContent:t,mockDraftItems:r,mockFileItems:l}){return"drafts"===e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(n.V,{icon:s.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",borderClassName:"border-1",hoverScale:!0,onClick:()=>t(null)}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Drafts"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[r.length," drafts"]}),(0,a.jsx)(n.V,{icon:o.A,variant:"ghost",size:"lg",showBorder:!0,borderClassName:"border-1",hoverColor:"grey",hoverScale:!0,children:"New Draft"})]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:r.map(e=>(0,a.jsx)("div",{className:"p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 text-lg",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Modified ",e.lastModified]})]}),(0,a.jsx)("div",{className:"text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded",children:e.status})]})},e.id))})]}):"files"===e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("button",{onClick:()=>t(null),className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Files"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[l.length," files"]}),(0,a.jsxs)(i.$,{className:"bg-blue-600 hover:bg-blue-700 text-white",children:[(0,a.jsx)("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"New File"]})]})]}),(0,a.jsx)("div",{className:"grid gap-4",children:l.map(e=>(0,a.jsx)("div",{className:"p-6 bg-white rounded-lg border border-gray-200 hover:shadow-md transition-all cursor-pointer",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500 font-semibold text-sm",children:"image"===e.type?"\uD83D\uDDBC️":"design"===e.type?"\uD83C\uDFA8":"\uD83D\uDCC4"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 text-lg",children:e.title}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:[e.size," • ",e.type]})]})]}),(0,a.jsx)("div",{className:"text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded",children:e.type})]})},e.id))})]}):null}var d=r(21342),c=r(25070),u=r(66420),p=r(45583),g=r(62688);let m=(0,g.A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var h=r(13861),f=r(43210),b=r.n(f);let v=[{id:1,title:"Research competitors",status:"pending",priority:"high"},{id:2,title:"Create wireframes",status:"in-progress",priority:"high"},{id:3,title:"Set up analytics",status:"completed",priority:"medium"},{id:4,title:"Design logo",status:"pending",priority:"medium"},{id:5,title:"Write content",status:"pending",priority:"low"}];function x(){let[e,t]=(0,f.useState)(!1),{isMobile:r}=(0,c.cL)();return(0,a.jsxs)(d.rI,{open:e,onOpenChange:t,children:[(0,a.jsx)(d.ty,{asChild:!0,children:(0,a.jsx)(n.V,{text:r?"P":"Priorities",badge:v.length,variant:"ghost",layout:"horizontal",hoverColor:"green",hoverScale:!0,showBorder:!0,className:"bg-green-100 hover:bg-green-600 text-green-700 hover:text-white border-green-500 border-2"})}),(0,a.jsxs)(d.SQ,{className:"w-80 rounded-lg animate-in fade-in-0 zoom-in-95",align:"end",side:"bottom",sideOffset:4,children:[(0,a.jsxs)(d.lp,{className:"text-muted-foreground text-xs",children:["Project Actions (",v.length,")"]}),v.map(e=>(0,a.jsxs)(d._2,{className:"gap-2 p-3 m-1 border border-transparent transition-colors duration-200 hover:bg-gray-100 hover:border-gray-200 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:border-gray-700 dark:hover:text-gray-100 cursor-pointer rounded-md",children:[(0,a.jsx)("div",{className:"flex size-8 items-center justify-center rounded-md border border-yellow-500 bg-yellow-50",children:(0,a.jsx)(p.A,{className:`${u.hS.md} shrink-0 text-yellow-500`})}),(0,a.jsxs)("div",{className:"flex flex-col flex-1",children:[(0,a.jsx)("span",{className:"font-medium",children:e.priority.toUpperCase()}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground capitalize",children:e.title})]}),(0,a.jsxs)(d.rI,{children:[(0,a.jsx)(d.ty,{asChild:!0,children:(0,a.jsx)("button",{className:"h-8 w-8 p-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded transition-colors duration-200 flex items-center justify-center",children:(0,a.jsx)(m,{className:u.hS.md})})}),(0,a.jsx)(d.SQ,{align:"end",className:"w-40",children:(0,a.jsxs)(d._2,{className:"gap-2 cursor-pointer hover:bg-gray-100 hover:text-gray-900 dark:hover:bg-gray-800 dark:hover:text-gray-100 focus:bg-gray-100 focus:text-gray-900 dark:focus:bg-gray-800 dark:focus:text-gray-100",children:[(0,a.jsx)(h.A,{className:u.hS.md}),"View"]})})]})]},e.id))]})]})}function y({progress:e}){let{isMobile:t}=(0,c.cL)();return t?(0,a.jsx)("div",{className:"w-10 h-10 rounded-sm border border-[var(--progress-border)] bg-[var(--progress-bg)] hover:bg-[var(--primary-light)] flex items-center justify-center   transition-colors duration-200",children:(0,a.jsxs)("span",{className:"text-xs font-medium text-[var(--progress-text)]",children:[e,"%"]})}):(0,a.jsx)("div",{className:"w-full h-20 border-[var(--progress-border)] relative flex items-center",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Progress"}),(0,a.jsxs)("span",{className:"text-xs  text-[var(--progress-text)]",children:[e,"%"]})]}),(0,a.jsxs)("div",{className:"w-full h-2.5 rounded-sm border border-[var(--progress-border)] bg-[var(--progress-bg)] cursor-pointer relative overflow-hidden",children:[(0,a.jsx)("div",{className:"h-full rounded-sm transition-all duration-300 relative",style:{width:`${e}%`,backgroundColor:"var(--progress-fill)"},children:(0,a.jsx)("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:`repeating-linear-gradient(
                    45deg,
                    transparent,
                    transparent 1px,
                    rgba(255, 255, 255, 0.3) 1px,
                    rgba(255, 255, 255, 0.3) 2px
                  )`}})}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-30",style:{backgroundImage:`repeating-linear-gradient(
                  45deg,
                  transparent,
                  transparent 1.5px,
                  rgba(156, 163, 175, 0.4) 1.5px,
                  rgba(156, 163, 175, 0.4) 3px
                )`}})]})]})})}var w=r(4780);let I=f.createContext({open:!1,onOpenChange:()=>{}});function N({children:e,open:t=!1,onOpenChange:r=()=>{}}){return(0,a.jsx)(I.Provider,{value:{open:t,onOpenChange:r},children:(0,a.jsx)("div",{className:"relative inline-block",children:e})})}function k({children:e,onClick:t}){let{open:r,onOpenChange:i}=f.useContext(I);return(0,a.jsx)("div",{onClick:()=>{i(!r),t?.()},className:"cursor-pointer",children:e})}function D({children:e,className:t,align:r="center",side:i="bottom",...n}){let{open:s,onOpenChange:o}=f.useContext(I);return s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>o(!1)}),(0,a.jsx)("div",{className:(0,w.cn)("absolute z-50 bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-4 min-w-[200px] max-w-[300px]",{top:"bottom-full mb-2",right:"left-full ml-2 top-0",bottom:"top-full mt-2",left:"right-full mr-2 top-0"}[i],{start:"left-0",center:"left-1/2 transform -translate-x-1/2",end:"right-0"}[r],t),...n,children:e})]}):null}var A=r(38553);let j=[{id:"problem",title:"Problem",description:"The core challenge or pain points your target customers are experiencing that you are addressing, can be defined in terms of not important, nice to have, or urgent/willing to pay.",question:"What does {PROJECT NAME} solve?",guidance:"Dig into root causes, 5-Whys; quantify pain and frequency; avoid jumping to solutions.",category:"Market",order:1,inputType:"Manual",dependencies:[],status:"validated",values:["Small businesses struggle with manual inventory tracking leading to stockouts and overstock","Remote teams lack effective real-time collaboration tools","Consumers can't easily find sustainable product alternatives"],actions:2,ideas:3,results:1},{id:"unique-value-proposition",title:"Unique Value Proposition",description:"The standout benefits or features that makes your offering distinct and compelling compared to alternatives.",question:"What's unique about what {PROJECT NAME} offers?",guidance:"State value delta in one crisp line; test uniqueness vs comps; anchor on key metric lift.",category:"Market",order:2,inputType:"Suggest",dependencies:["problem"],status:"action",values:["AI-powered inventory prediction with 95% accuracy","Real-time collaboration with zero-latency video and document sharing","Sustainability score for every product with verified impact data"],actions:1,ideas:2,results:0},{id:"audience",title:"Audience",description:"This is target customer market; can be defined in terms of behaviors, demographics, geography, industry, role, organization size or psychographics.",question:"Who is {PROJECT NAME} for?",guidance:"Probe segment, JTBD, personas, buying triggers; verify with data; mirror target jargon & maturity.",category:"Market",order:3,inputType:"Suggest",dependencies:["problem"],status:"validated",values:["Small to medium retail businesses (10-500 employees) with physical inventory","Remote-first companies with 20-200 employees in tech/creative industries","Environmentally conscious consumers aged 25-45 with disposable income"],actions:0,ideas:1,results:1},{id:"alternatives",title:"Alternatives",description:"Competitor products, services, or manual workarounds your target customers currently use to address their problems.",question:"What're current alternatives to {PROJECT NAME}?",guidance:"Benchmark top incumbents & DIY; map feature gaps, switching barriers, and status-quo cost.",category:"Market",order:4,inputType:"Auto",dependencies:["problem","audience","unique-value-proposition"],status:"idea",values:["Excel spreadsheets, QuickBooks inventory, manual counting","Slack + Zoom + Google Docs, Microsoft Teams, Notion","Google search, sustainability blogs, brand websites"],actions:0,ideas:1,results:0},{id:"market-size",title:"Market Size",description:"The estimated number of potential customers or the revenue opportunity available in your target market, definable in terms of TAM, SAM & SOM.",question:"How big is the market for {PROJECT NAME}?",guidance:"Walk through TAM/SAM/SOM math; cite sources; sanity-check with bottom-up calc.",category:"Market",order:5,inputType:"Auto",dependencies:["problem","audience","unique-value-proposition","alternatives"],status:"invalidated",values:["TAM: $50B retail inventory management, SAM: $5B SMB segment, SOM: $500M addressable","TAM: $30B collaboration software, SAM: $8B remote team tools, SOM: $800M target segment","TAM: $150B sustainable products, SAM: $15B conscious consumers, SOM: $1.5B early adopters"],actions:0,ideas:0,results:0},{id:"trends",title:"Trends",description:"Emerging marco-economic or micro-economic shifts, technologies, or behaviors in your target audience or alternatives that could impact the viability of this business, categorizable in terms of headwinds and tailwinds.",question:"What trends should {PROJECT NAME} consider?",guidance:"Surface 3-5 macro/micro tailwinds/headwinds; time-box horizon; tie to urgency.",category:"Market",order:6,inputType:"Auto",dependencies:["problem","audience","unique-value-proposition","alternatives"],status:"invalidated",values:["AI adoption accelerating, supply chain digitization, labor shortage driving automation","Remote work normalization, async communication preference, digital-first workflows","ESG investing growth, Gen Z purchasing power, climate change awareness"],actions:0,ideas:0,results:0},{id:"why",title:"Why",description:"The fundamental mission, vision and values of your business, the 'north stars' that drive your business and decisions.",question:"Why do you want to do {PROJECT NAME}?",guidance:"Articulate inspiring 'why' in <25 words; connect founder story & audience pain authentically.",category:"Solution",order:7,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition"],status:"validated",values:["Eliminate inventory waste and help small businesses thrive through intelligent automation","Enable seamless remote collaboration to unlock human potential regardless of location","Accelerate sustainable consumption by making eco-friendly choices effortless and transparent"],actions:1,ideas:2,results:0},{id:"advantages",title:"Advantages",description:"The specific strengths or assets that give you an edge over existing or potential competitors, like IP, relationships, reputations, networks, domain expertise, etc.",question:"Why are you the right team to make {PROJECT NAME}?",guidance:"Rank moats by copying difficulty & impact; quantify advantages; link to strategy.",category:"Solution",order:8,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition"],status:"validated",values:["10+ years retail operations experience, proprietary ML algorithms, exclusive supplier partnerships","Former Google/Slack engineers, deep async communication research, enterprise sales network","Sustainability certification expertise, verified supplier database, influencer partnerships"],actions:2,ideas:1,results:0},{id:"product",title:"Product or Service",description:"The products or services you develop and offer to your audience to solve their problems, can be broken down into prioritized features or specifications",question:"What does {PROJECT NAME} offer?",guidance:"Outline MVP vs roadmap; map features to JTBD; validate feasibility & delight.",category:"Solution",order:13,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages"],status:"invalidated",values:["AI inventory management platform: demand forecasting, automated reordering, analytics dashboard","Unified collaboration suite: async video messaging, real-time co-editing, smart scheduling","Sustainability marketplace: product scoring, impact tracking, eco-alternative recommendations"],actions:0,ideas:0,results:0},{id:"tech",title:"Tech",description:"The technologies, platforms, tools & intellectual property that underpin and enable your product and unique value proposition.",question:"How does {PROJECT NAME} work?",guidance:"Explain stack choices for scale, security, moat; flag build-vs-buy; keep non-tech founders clear.",category:"Solution",order:14,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product"],status:"invalidated",values:["Python ML pipeline, React dashboard, PostgreSQL, AWS infrastructure, REST APIs","WebRTC for video, React/Node.js, real-time sync engine, cloud storage integration","Sustainability API, React Native app, blockchain verification, third-party integrations"],actions:0,ideas:0,results:0},{id:"packages",title:"Packages",description:"Structured bundles or tiers of your product or service offerings, with deep consideration given to pricing and business model type suitability for the target market given the alternatives.",question:"What does {PROJECT NAME} sell?",guidance:"Design tiers aligned to segments & WTP; show anchor/decoy logic; outline upgrade paths.",category:"Solution",order:16,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","tech","business-model"],status:"invalidated",values:["Starter ($49/mo): Basic forecasting, 1000 SKUs; Pro ($149/mo): Advanced analytics, 10K SKUs; Enterprise ($499/mo): Custom integrations, unlimited","Team ($29/user/mo): Core features, 10 users; Business ($59/user/mo): Advanced tools, 50 users; Enterprise ($99/user/mo): Full suite, unlimited","Basic ($9.99/mo): Product scores; Premium ($19.99/mo): Impact tracking; Pro ($39.99/mo): Marketplace access"],actions:0,ideas:0,results:0},{id:"positioning",title:"Positioning",description:"The strategic place your offering and company occupy your target markets' minds relative to alternatives, mainly informed by the trends, unique value proposition and problems.",question:"How is {PROJECT NAME} framed compared to others?",guidance:"Define category frame & quadrant; craft vivid tagline; stress emotional hook & credibility.",category:"Sales & Marketing",order:9,inputType:"Auto",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages"],status:"invalidated",values:["The AI-first inventory platform for modern retailers who refuse to guess","Async-first collaboration for teams that value deep work over constant meetings","The sustainability compass for conscious consumers who want impact without compromise"],actions:0,ideas:0,results:0},{id:"channels",title:"Channels",description:"The marketing, sales, and distribution paths you use to reach, engage, acquire, and convert your audience into paying customers.",question:"How is {PROJECT NAME} distributed?",guidance:"Use bullseye method, find 3 high-leverage channels; demand early traction data; model CAC payback.",category:"Sales & Marketing",order:10,inputType:"Auto",dependencies:["problem","audience","alternatives","trends","why","advantages"],status:"invalidated",values:["Direct sales to SMB retailers, retail trade shows, partner integrations with POS systems","Product-led growth, content marketing to remote teams, enterprise sales to Fortune 500","Influencer partnerships, sustainability conferences, B2B marketplace listings"],actions:0,ideas:0,results:0},{id:"messaging",title:"Messaging",description:"The specific communication and words you use consistently to convey your unique valupe proposition, positioning and brand to your audience.",question:"How do we communicate {PROJECT NAME}'s value?",guidance:"Craft plain-language benefit first; tailor by funnel stage; maintain voice consistency.",category:"Sales & Marketing",order:11,inputType:"Auto",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","channels"],status:"invalidated",values:["Stop inventory guesswork. Start profit certainty. Our AI predicts what you'll sell before you know you need it.","Work together without being together. Deep collaboration that respects your focus time and delivers better results.","Every purchase is a vote for the planet. Make yours count with verified sustainability scores for everything you buy."],actions:0,ideas:0,results:0},{id:"brand",title:"Brand",description:"The personality, tone, style, visual identity that shape how customers perceive and connect with your business, mainly informed by the positioning and why.",question:"What defines {PROJECT NAME}'s identity?",guidance:"Align identity, tone, visuals to audience values; embed chosen archetype; ensure coherence.",category:"Sales & Marketing",order:17,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","channels","tech","business-model","messaging"],status:"unproven",values:["Professional yet approachable, data-driven, reliability-focused with clean modern design","Innovative, human-centered, productivity-focused with warm collaborative aesthetics","Authentic, earth-conscious, transparency-focused with natural sustainable design"],actions:0,ideas:0,results:0},{id:"assets",title:"Assets",description:"Tangible and intangible resources—like IP, partnerships, or design assets like graphics, logos, websites, videos, content and more—that you leverage to deliver your offering",question:"What is valuable in {PROJECT NAME}?",guidance:"List IP, data, partnerships; rate strategic value & defendability; plan leverage.",category:"Sales & Marketing",order:18,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","channels","tech","brand","messaging"],status:"unproven",values:["Proprietary ML algorithms, retail industry partnerships, comprehensive product database","Collaboration research IP, enterprise customer testimonials, integration partnerships","Sustainability certification database, influencer network, verified impact data"],actions:0,ideas:0,results:0},{id:"sales-motion",title:"Sales Motion",description:"The sequence of steps, strategies, timelines, communications your team employs to convert prospects into paying customers.",question:"What're the steps to making a sale for {PROJECT NAME}?",guidance:"Map buyer journey steps, owners, SLAs; include feedback loops; aim for velocity & expansion.",category:"Sales & Marketing",order:19,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","channels","tech","brand","messaging","assets"],status:"unproven",values:["Demo request → needs assessment → pilot program → ROI analysis → contract negotiation → onboarding","Free trial signup → usage tracking → feature adoption → upgrade prompts → sales call → conversion","Content engagement → email nurture → webinar attendance → consultation call → subscription signup"],actions:0,ideas:0,results:0},{id:"metrics",title:"Metrics",description:"Key performance indicators and data points you monitor to track progress, performance, and business health.",question:"How would {PROJECT NAME} measure success?",guidance:"Pick north-star + 3 inputs; link to problem & revenue; set baselines and review cadence.",category:"Sales & Marketing",order:20,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","channels","tech","brand","messaging","assets","sales-motion"],status:"unproven",values:["Monthly recurring revenue, customer acquisition cost, inventory accuracy improvement, churn rate","Daily active users, feature adoption rate, customer satisfaction score, expansion revenue","Sustainability impact score, user engagement rate, marketplace transaction volume, retention rate"],actions:0,ideas:0,results:0},{id:"risks",title:"Risks",description:"Internal and external threats—trends, alternatives, financial, operational, business model—that could jeopardize your business success.",question:"What are the unique risks for {PROJECT NAME}?",guidance:"Run pre-mortem: list top 5 risks with likelihood/impact; propose mitigations & monitors.",category:"Company",order:12,inputType:"Auto",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","channels","messaging"],status:"unproven",values:["Data privacy regulations, large competitor entry, economic downturn affecting SMB spending","Remote work trend reversal, security breaches, enterprise sales cycle delays","Greenwashing backlash, supply chain verification challenges, consumer behavior shifts"],actions:0,ideas:0,results:0},{id:"business-model",title:"Business Model",description:"The financial model outlining how you create, deliver, and capture value—basic catogories are subscriptions, one-time fees, royalties, re-selling, usage-based, and consulting, deeply coupled with your packages and product",question:"What's {PROJECT NAME}'s business model?",guidance:"Clarify value capture & pricing logic; map cost drivers; run sensitivity scenarios.",category:"Company",order:15,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","channels","product","tech"],status:"unproven",values:["SaaS subscription model with tiered pricing based on SKU volume and feature access","Freemium with usage-based pricing for advanced features and enterprise add-ons","Subscription + transaction fees for marketplace purchases with sustainability verification"],actions:0,ideas:0,results:0},{id:"revenue",title:"Revenue",description:"The various income streams generated from your product or service sales—typically estimated from your business model, audience, alternatives, packaging and channels.",question:"What's the revenue forecast for {PROJECT NAME}?",guidance:"Itemize streams, price drivers; model MRR/ARR & margins; flag dependencies.",category:"Company",order:21,inputType:"Manual",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","channels","tech","brand","messaging","assets","sales-motion"],status:"unproven",values:["Year 1: $500K ARR, Year 2: $2M ARR, Year 3: $8M ARR from subscription revenue","Year 1: $300K ARR, Year 2: $1.5M ARR, Year 3: $6M ARR from freemium conversions","Year 1: $200K ARR, Year 2: $1M ARR, Year 3: $4M ARR from subscriptions + transaction fees"],actions:0,ideas:0,results:0},{id:"cost",title:"Cost",description:"The various expenditures—fixed and variable—required to develop, operate, and scale your business effectively, typically estimated from your business model, audience, alternatives, packaging and channels.",question:"What are {PROJECT NAME}'s typical costs?",guidance:"Split fixed/variable; highlight runway vs milestones; suggest lean cuts if needed.",category:"Company",order:22,inputType:"Manual",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","channels","tech","brand","messaging","assets","sales-motion","metrics"],status:"unproven",values:["Fixed: $50K/mo (team, infrastructure), Variable: $20K/mo (customer acquisition, hosting)","Fixed: $40K/mo (development, operations), Variable: $15K/mo (marketing, support)","Fixed: $30K/mo (team, platform), Variable: $10K/mo (partnerships, verification)"],actions:0,ideas:0,results:0},{id:"team",title:"Team",description:"The group of founders or individuals whose skills, experience, strengths, weaknesses & characters collectively drive your business.",question:"Who is working on {PROJECT NAME}?",guidance:"Assess skill gaps, equity fairness; define hiring roadmap; weave narrative to mission.",category:"Company",order:23,inputType:"Suggest",dependencies:["problem","audience","unique-value-proposition","alternatives","trends","why","advantages","product","channels","tech","brand","messaging","assets","sales-motion","metrics"],status:"unproven",values:["CEO (retail ops), CTO (ML/AI), Head of Sales (B2B), 2 engineers, 1 designer","CEO (product), CTO (distributed systems), Head of Growth (PLG), 3 engineers, 1 designer","CEO (sustainability), CTO (marketplace), Head of Partnerships, 2 engineers, 1 content creator"],actions:0,ideas:0,results:0}],C={problem:{question:"What specific problem are we trying to solve?",defaultAnswer:"Define the core problem that needs to be addressed and its impact on the business.",getAnswer:e=>e?.title?`Problem identified: ${e.title}`:"Define the core problem that needs to be addressed and its impact on the business."},audience:{question:"Who is our target audience for this initiative?",defaultAnswer:"Identify the specific user groups, demographics, and stakeholders who will be affected.",getAnswer:e=>e?.title?`Target audience: ${e.title}`:"Identify the specific user groups, demographics, and stakeholders who will be affected."},alternatives:{question:"What alternative solutions have we considered?",defaultAnswer:"List and evaluate different approaches, technologies, or strategies that could address this need.",getAnswer:e=>e?.title?`Alternative approach: ${e.title}`:"List and evaluate different approaches, technologies, or strategies that could address this need."},solution:{question:"What is our proposed solution approach?",defaultAnswer:"Describe the recommended solution, its key features, and implementation strategy.",getAnswer:e=>e?.title?`Proposed solution: ${e.title}`:"Describe the recommended solution, its key features, and implementation strategy."},market:{question:"What market opportunity does this address?",defaultAnswer:"Define the market size, competitive landscape, and business opportunity.",getAnswer:e=>e?.title?`Market opportunity: ${e.title}`:"Define the market size, competitive landscape, and business opportunity."},technology:{question:"What technology stack and architecture will we use?",defaultAnswer:"Outline the technical requirements, tools, platforms, and infrastructure needed.",getAnswer:e=>e?.title?`Technology approach: ${e.title}`:"Outline the technical requirements, tools, platforms, and infrastructure needed."},timeline:{question:"What is our project timeline and key milestones?",defaultAnswer:"Define project phases, deadlines, dependencies, and critical path items.",getAnswer:e=>e?.title?`Timeline focus: ${e.title}`:"Define project phases, deadlines, dependencies, and critical path items."},budget:{question:"What are the financial requirements and ROI expectations?",defaultAnswer:"Specify budget allocation, cost breakdown, and expected return on investment.",getAnswer:e=>e?.title?`Budget consideration: ${e.title}`:"Specify budget allocation, cost breakdown, and expected return on investment."},risk:{question:"What are the key risks and mitigation strategies?",defaultAnswer:"Identify potential risks, their impact, probability, and mitigation plans.",getAnswer:e=>e?.title?`Risk factor: ${e.title}`:"Identify potential risks, their impact, probability, and mitigation plans."},metrics:{question:"How will we measure success and track progress?",defaultAnswer:"Define KPIs, success criteria, measurement methods, and reporting frequency.",getAnswer:e=>e?.title?`Success metric: ${e.title}`:"Define KPIs, success criteria, measurement methods, and reporting frequency."},team:{question:"What team structure and resources do we need?",defaultAnswer:"Specify roles, responsibilities, skill requirements, and resource allocation.",getAnswer:e=>e?.title?`Team focus: ${e.title}`:"Specify roles, responsibilities, skill requirements, and resource allocation."},default:{question:"What is the main objective of this business item?",defaultAnswer:"Define the core purpose and expected outcomes for this business initiative.",getAnswer:e=>e?.title?`This item focuses on: ${e.title}`:"Define the core purpose and expected outcomes for this business initiative."}};var S=r(28559);let E=(0,g.A)("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);function P({selectedBusinessItem:e,onBackToItems:t}){let{trackClick:r,trackCustomEvent:i}=(0,A.s)(),[s,o]=(0,f.useState)(!1),[l,d]=(0,f.useState)(!1),{state:p,isMobile:g}=(0,c.cL)(),{question:m,answer:h}=function(e){if(!e)return{question:C.default.question,answer:C.default.defaultAnswer};let t=j.find(t=>t.id===e.id);if(t)return{question:t.question?.replace("{PROJECT NAME}","siift")||t.title,answer:t.description||`Working on: ${t.title}`};let r=C[function(e){if(!e?.title)return"default";let t=e.title.toLowerCase();return t.includes("problem")||t.includes("issue")||t.includes("challenge")?"problem":t.includes("audience")||t.includes("user")||t.includes("customer")?"audience":t.includes("alternative")||t.includes("option")||t.includes("approach")?"alternatives":t.includes("solution")||t.includes("resolve")||t.includes("fix")?"solution":t.includes("market")||t.includes("opportunity")||t.includes("competition")?"market":t.includes("technology")||t.includes("tech")||t.includes("platform")?"technology":t.includes("timeline")||t.includes("schedule")||t.includes("deadline")?"timeline":t.includes("budget")||t.includes("cost")||t.includes("financial")?"budget":t.includes("risk")||t.includes("threat")||t.includes("danger")?"risk":t.includes("metric")||t.includes("kpi")||t.includes("measure")?"metrics":t.includes("team")||t.includes("resource")||t.includes("staff")?"team":"default"}(e)]||C.default;return{question:r.question,answer:r.getAnswer?r.getAnswer(e):r.defaultAnswer}}(e);return(0,a.jsx)("header",{className:"flex h-20 shrink-0 items-center gap-2 transition-[width] ease-linear border-b border-border",children:(0,a.jsxs)("div",{className:"flex items-center justify-between w-full h-full px-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 h-full",children:[g&&(0,a.jsx)(n.V,{onClick:()=>{r("back-to-items","project-detail-header"),i("navigation_clicked",{destination:"items",from_page:"item-detail",location:"header"}),t()},icon:S.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",hoverScale:!0,iconClassName:u.hS.lg}),"collapsed"===p&&(0,a.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-gray-100",children:e?.title||"Untitled Item"})]}),(0,a.jsx)("div",{className:"flex-1 flex flex-col justify-center mr-6",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,a.jsx)("div",{className:"text-sm font-bold text-[var(--brand-dark)] dark:text-[var(--primary)]",children:m}),(0,a.jsxs)(N,{open:l,onOpenChange:d,children:[(0,a.jsx)(k,{children:(0,a.jsx)(n.V,{icon:()=>(0,a.jsx)("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})}),variant:"ghost",size:"sm",layout:"icon-only",showBorder:!1,hoverColor:"grey",hoverScale:!0,onClick:()=>{r("info-button","project-header"),i("info_clicked",{from_item:e?.title,location:"header"})},iconClassName:"w-4 h-4"})}),(0,a.jsx)(D,{align:"start",side:"bottom",className:"w-80",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"font-semibold text-sm",children:"Description"}),(0,a.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400 leading-relaxed",children:h})]})})]})]})}),(0,a.jsx)("div",{className:"flex items-center gap-2 h-full",children:(0,a.jsxs)(N,{open:s,onOpenChange:o,children:[(0,a.jsx)(k,{children:(0,a.jsx)(n.V,{icon:E,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",hoverScale:!0,onClick:()=>{r("help-button","project-header"),i("help_clicked",{from_item:e?.title,location:"header"})},iconClassName:u.hS.lg})}),(0,a.jsx)(D,{align:"end",side:"bottom",className:"w-80",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm",children:"Table Guide"}),(0,a.jsxs)("div",{className:"space-y-2 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Idea:"})," What is the main idea of the item?"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-yellow-500 rounded-full"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Action:"})," What was done to achieve the idea?"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Result:"})," What was the outcome of the action?"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 p-2 border-gray-200 dark:border-gray-700 rounded-md border-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-purple-500 rounded-full"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"Status:"})," Current state"]})]})]}),(0,a.jsx)("div",{className:"pt-2 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsx)("p",{className:"text-xs text-gray-600 dark:text-gray-400",children:"Click cells to edit • Drag rows to reorder • Use + to add items"})})]})})]})})]})})}var R=r(32192),T=r(10022),M=r(58869),O=r(16189);function B({activeContent:e,setActiveContent:t,selectedBusinessItem:r,onBackToItems:i}){let s=(0,O.useRouter)(),{trackClick:o,trackCustomEvent:l}=(0,A.s)(),{state:d,isMobile:p}=(0,c.cL)();return r?(0,a.jsx)(P,{selectedBusinessItem:r,onBackToItems:i}):(0,a.jsx)("header",{className:"flex flex-col h-20 shrink-0 transition-[width] ease-linear border-b border-border",children:(0,a.jsxs)("div",{className:`flex items-center w-full flex-1 px-4 ${p?"justify-between":"gap-4"}`,children:[p&&(0,a.jsx)(n.V,{onClick:()=>{o("home-button","project-header"),l("navigation_clicked",{destination:"dashboard",from_page:"project-detail",location:"header"}),s.push("/user-dashboard")},icon:R.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",hoverScale:!0,iconClassName:u.hS.lg}),(0,a.jsxs)("div",{className:`flex items-center ${p?"gap-2":"gap-4"} ${p?"":"flex-1"}`,children:[(0,a.jsx)("div",{className:p?"w-12":"flex-1",children:(0,a.jsx)(y,{progress:35})}),(0,a.jsxs)("div",{className:"flex items-center gap-2 h-full",children:[(0,a.jsx)(x,{}),(0,a.jsx)(n.V,{onClick:()=>{o("drafts-button","project-header"),l("content_section_opened",{section:"drafts",location:"project-header"}),t("drafts")},icon:T.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",hoverScale:!0,iconClassName:u.hS.lg}),(0,a.jsx)(n.V,{icon:M.A,variant:"ghost",size:"lg",layout:"icon-only",showBorder:!0,hoverColor:"grey",hoverScale:!0,onClick:()=>{o("profile-button","project-header"),l("navigation_clicked",{destination:"profile",from_page:"project-detail",location:"header"}),s.push("/profile")},iconClassName:u.hS.lg})]})]})]})})}var L=r(88920),_=r(92576),G=r(76242),$=r(96834),q=r(44493),z=r(15079);function W({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,w.cn)("w-full caption-bottom text-sm",e),...t})})}function F({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,w.cn)("[&_tr]:border-b",e),...t})}function U({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,w.cn)("[&_tr:last-child]:border-0",e),...t})}function V({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,w.cn)("hover:bg-gray-100 dark:hover:bg-gray-800 data-[state=selected]:bg-gray-200 dark:data-[state=selected]:bg-gray-700 border-b transition-colors",e),...t})}function H({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,w.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function J({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,w.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}var Y=r(26787),K=r(59350);let X=(0,Y.v)()((0,K.lt)(e=>({selectedItem:null,itemDetails:[],isLoading:!1,error:null,setSelectedItem:t=>e({selectedItem:t}),setItemDetails:t=>e({itemDetails:t}),setLoading:t=>e({isLoading:t}),setError:t=>e({error:t}),addItemDetail:t=>e(e=>({itemDetails:[...e.itemDetails,t]})),updateItemDetail:(t,r)=>e(e=>({itemDetails:e.itemDetails.map(e=>e.id===t?{...e,...r}:e)})),removeItemDetail:t=>e(e=>({itemDetails:e.itemDetails.filter(e=>e.id!==t)}))}),{name:"business-item-storage"}));var Z=r(51215),Q=r.n(Z),ee=r(11208),et=r(54864),er=r(89653),ea=function(e){var t=e.top,r=e.right,a=e.bottom,i=e.left;return{top:t,right:r,bottom:a,left:i,width:r-i,height:a-t,x:i,y:t,center:{x:(r+i)/2,y:(a+t)/2}}},ei=function(e,t){return{top:e.top-t.top,left:e.left-t.left,bottom:e.bottom+t.bottom,right:e.right+t.right}},en=function(e,t){return{top:e.top+t.top,left:e.left+t.left,bottom:e.bottom-t.bottom,right:e.right-t.right}},es={top:0,right:0,bottom:0,left:0},eo=function(e){var t=e.borderBox,r=e.margin,a=void 0===r?es:r,i=e.border,n=void 0===i?es:i,s=e.padding,o=void 0===s?es:s,l=ea(ei(t,a)),d=ea(en(t,n)),c=ea(en(d,o));return{marginBox:l,borderBox:ea(t),paddingBox:d,contentBox:c,margin:a,border:n,padding:o}},el=function(e){var t=e.slice(0,-2);if("px"!==e.slice(-2))return 0;var r=Number(t);return isNaN(r)&&(0,er.A)(!1),r},ed=function(e,t){var r=e.borderBox,a=e.border,i=e.margin,n=e.padding;return eo({borderBox:{top:r.top+t.y,left:r.left+t.x,bottom:r.bottom+t.y,right:r.right+t.x},border:a,margin:i,padding:n})},ec=function(e,t){return void 0===t&&(t={x:window.pageXOffset,y:window.pageYOffset}),ed(e,t)},eu=function(e,t){return eo({borderBox:e,margin:{top:el(t.marginTop),right:el(t.marginRight),bottom:el(t.marginBottom),left:el(t.marginLeft)},padding:{top:el(t.paddingTop),right:el(t.paddingRight),bottom:el(t.paddingBottom),left:el(t.paddingLeft)},border:{top:el(t.borderTopWidth),right:el(t.borderRightWidth),bottom:el(t.borderBottomWidth),left:el(t.borderLeftWidth)}})},ep=function(e){return eu(e.getBoundingClientRect(),window.getComputedStyle(e))};let eg=function(e){var t=[],r=null,a=function(){for(var a=arguments.length,i=Array(a),n=0;n<a;n++)i[n]=arguments[n];t=i,r||(r=requestAnimationFrame(function(){r=null,e.apply(void 0,t)}))};return a.cancel=function(){r&&(cancelAnimationFrame(r),r=null)},a};function em(){return(em=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(null,arguments)}let eh=/[ \t]{2,}/g,ef=/^[ \t]*/gm,eb=e=>e.replace(eh," ").replace(ef,"").trim(),ev=e=>eb(`
  %c@hello-pangea/dnd

  %c${eb(e)}

  %c👷‍ This is a development only message. It will be removed in production builds.
`);function ex(e,t){}ex.bind(null,"warn");let ey=ex.bind(null,"error");function ew(){}function eI(e,t,r){let a=t.map(t=>{var a;let i=(a=t.options,{...r,...a});return e.addEventListener(t.eventName,t.fn,i),function(){e.removeEventListener(t.eventName,t.fn,i)}});return function(){a.forEach(e=>{e()})}}class eN extends Error{}function ek(e,t){throw new eN("Invariant failed")}eN.prototype.toString=function(){return this.message};class eD extends b().Component{constructor(...e){super(...e),this.callbacks=null,this.unbind=ew,this.onWindowError=e=>{let t=this.getCallbacks();t.isDragging()&&t.tryAbort(),e.error instanceof eN&&e.preventDefault()},this.getCallbacks=()=>{if(!this.callbacks)throw Error("Unable to find AppCallbacks in <ErrorBoundary/>");return this.callbacks},this.setCallbacks=e=>{this.callbacks=e}}componentDidMount(){this.unbind=eI(window,[{eventName:"error",fn:this.onWindowError}])}componentDidCatch(e){if(e instanceof eN)return void this.setState({});throw e}componentWillUnmount(){this.unbind()}render(){return this.props.children(this.setCallbacks)}}let eA=e=>e+1,ej=(e,t)=>{let r=e.droppableId===t.droppableId,a=eA(e.index),i=eA(t.index);return r?`
      You have moved the item from position ${a}
      to position ${i}
    `:`
    You have moved the item from position ${a}
    in list ${e.droppableId}
    to list ${t.droppableId}
    in position ${i}
  `},eC=(e,t,r)=>t.droppableId===r.droppableId?`
      The item ${e}
      has been combined with ${r.draggableId}`:`
      The item ${e}
      in list ${t.droppableId}
      has been combined with ${r.draggableId}
      in list ${r.droppableId}
    `,eS=e=>`
  The item has returned to its starting position
  of ${eA(e.index)}
`,eE={dragHandleUsageInstructions:`
  Press space bar to start a drag.
  When dragging you can use the arrow keys to move the item around and escape to cancel.
  Some screen readers may require you to be in focus mode or to use your pass through key
`,onDragStart:e=>`
  You have lifted an item in position ${eA(e.source.index)}
`,onDragUpdate:e=>{let t=e.destination;if(t)return ej(e.source,t);let r=e.combine;return r?eC(e.draggableId,e.source,r):"You are over an area that cannot be dropped on"},onDragEnd:e=>{if("CANCEL"===e.reason)return`
      Movement cancelled.
      ${eS(e.source)}
    `;let t=e.destination,r=e.combine;return t?`
      You have dropped the item.
      ${ej(e.source,t)}
    `:r?`
      You have dropped the item.
      ${eC(e.draggableId,e.source,r)}
    `:`
    The item has been dropped while not over a drop area.
    ${eS(e.source)}
  `}};function eP(e,t){if(e.length!==t.length)return!1;for(let i=0;i<e.length;i++){var r,a;if(!((r=e[i])===(a=t[i])||Number.isNaN(r)&&Number.isNaN(a))&&1)return!1}return!0}function eR(e,t){let r=(0,f.useState)(()=>({inputs:t,result:e()}))[0],a=(0,f.useRef)(!0),i=(0,f.useRef)(r),n=a.current||t&&i.current.inputs&&eP(t,i.current.inputs)?i.current:{inputs:t,result:e()};return(0,f.useEffect)(()=>{a.current=!1,i.current=n},[n]),n.result}function eT(e,t){return eR(()=>e,t)}let eM={x:0,y:0},eO=(e,t)=>({x:e.x+t.x,y:e.y+t.y}),eB=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),eL=(e,t)=>e.x===t.x&&e.y===t.y,e_=e=>({x:0!==e.x?-e.x:0,y:0!==e.y?-e.y:0}),eG=(e,t,r=0)=>"x"===e?{x:t,y:r}:{x:r,y:t},e$=(e,t)=>Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2),eq=(e,t)=>Math.min(...t.map(t=>e$(e,t))),ez=e=>t=>({x:e(t.x),y:e(t.y)});var eW=(e,t)=>{let r=ea({top:Math.max(t.top,e.top),right:Math.min(t.right,e.right),bottom:Math.min(t.bottom,e.bottom),left:Math.max(t.left,e.left)});return r.width<=0||r.height<=0?null:r};let eF=(e,t)=>({top:e.top+t.y,left:e.left+t.x,bottom:e.bottom+t.y,right:e.right+t.x}),eU=e=>[{x:e.left,y:e.top},{x:e.right,y:e.top},{x:e.left,y:e.bottom},{x:e.right,y:e.bottom}],eV=(e,t)=>t?eF(e,t.scroll.diff.displacement):e,eH=(e,t,r)=>r&&r.increasedBy?{...e,[t.end]:e[t.end]+r.increasedBy[t.line]}:e,eJ=(e,t)=>t&&t.shouldClipSubject?eW(t.pageMarginBox,e):ea(e);var eY=({page:e,withPlaceholder:t,axis:r,frame:a})=>{let i=eJ(eH(eV(e.marginBox,a),r,t),a);return{page:e,withPlaceholder:t,active:i}},eK=(e,t)=>{e.frame||ek();let r=e.frame,a=eB(t,r.scroll.initial),i=e_(a),n={...r,scroll:{initial:r.scroll.initial,current:t,diff:{value:a,displacement:i},max:r.scroll.max}},s=eY({page:e.subject.page,withPlaceholder:e.subject.withPlaceholder,axis:e.axis,frame:n});return{...e,frame:n,subject:s}};function eX(e,t=eP){let r=null;function a(...i){if(r&&r.lastThis===this&&t(i,r.lastArgs))return r.lastResult;let n=e.apply(this,i);return r={lastResult:n,lastArgs:i,lastThis:this},n}return a.clear=function(){r=null},a}let eZ=eX(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),eQ=eX(e=>e.reduce((e,t)=>(e[t.descriptor.id]=t,e),{})),e0=eX(e=>Object.values(e)),e1=eX(e=>Object.values(e));var e2=eX((e,t)=>e1(t).filter(t=>e===t.descriptor.droppableId).sort((e,t)=>e.descriptor.index-t.descriptor.index));function e5(e){return e.at&&"REORDER"===e.at.type?e.at.destination:null}function e3(e){return e.at&&"COMBINE"===e.at.type?e.at.combine:null}var e4=eX((e,t)=>t.filter(t=>t.descriptor.id!==e.descriptor.id)),e8=({isMovingForward:e,draggable:t,destination:r,insideDestination:a,previousImpact:i})=>{if(!r.isCombineEnabled||!e5(i))return null;function n(e){let t={type:"COMBINE",combine:{draggableId:e,droppableId:r.descriptor.id}};return{...i,at:t}}let s=i.displaced.all,o=s.length?s[0]:null;if(e)return o?n(o):null;let l=e4(t,a);if(!o)return l.length?n(l[l.length-1].descriptor.id):null;let d=l.findIndex(e=>e.descriptor.id===o);-1===d&&ek();let c=d-1;return c<0?null:n(l[c].descriptor.id)},e6=(e,t)=>e.descriptor.droppableId===t.descriptor.id;let e9={point:eM,value:0},e7={invisible:{},visible:{},all:[]},te={displaced:e7,displacedBy:e9,at:null};var tt=(e,t)=>r=>e<=r&&r<=t,tr=e=>{let t=tt(e.top,e.bottom),r=tt(e.left,e.right);return a=>{if(t(a.top)&&t(a.bottom)&&r(a.left)&&r(a.right))return!0;let i=t(a.top)||t(a.bottom),n=r(a.left)||r(a.right);if(i&&n)return!0;let s=a.top<e.top&&a.bottom>e.bottom,o=a.left<e.left&&a.right>e.right;return!!s&&!!o||s&&n||o&&i}},ta=e=>{let t=tt(e.top,e.bottom),r=tt(e.left,e.right);return e=>t(e.top)&&t(e.bottom)&&r(e.left)&&r(e.right)};let ti={direction:"vertical",line:"y",crossAxisLine:"x",start:"top",end:"bottom",size:"height",crossAxisStart:"left",crossAxisEnd:"right",crossAxisSize:"width"},tn={direction:"horizontal",line:"x",crossAxisLine:"y",start:"left",end:"right",size:"width",crossAxisStart:"top",crossAxisEnd:"bottom",crossAxisSize:"height"};var ts=e=>t=>{let r=tt(t.top,t.bottom),a=tt(t.left,t.right);return t=>e===ti?r(t.top)&&r(t.bottom):a(t.left)&&a(t.right)};let to=(e,t)=>eF(e,t.frame?t.frame.scroll.diff.displacement:eM),tl=(e,t,r)=>!!t.subject.active&&r(t.subject.active)(e),td=(e,t,r)=>r(t)(e),tc=({target:e,destination:t,viewport:r,withDroppableDisplacement:a,isVisibleThroughFrameFn:i})=>{let n=a?to(e,t):e;return tl(n,t,i)&&td(n,r,i)},tu=e=>tc({...e,isVisibleThroughFrameFn:tr}),tp=e=>tc({...e,isVisibleThroughFrameFn:ta}),tg=e=>tc({...e,isVisibleThroughFrameFn:ts(e.destination.axis)}),tm=(e,t,r)=>{if("boolean"==typeof r)return r;if(!t)return!0;let{invisible:a,visible:i}=t;if(a[e])return!1;let n=i[e];return!n||n.shouldAnimate};function th({afterDragging:e,destination:t,displacedBy:r,viewport:a,forceShouldAnimate:i,last:n}){return e.reduce(function(e,s){var o,l;let d=(o=s,l=r,ea(ei(o.page.marginBox,{top:l.point.y,right:0,bottom:0,left:l.point.x}))),c=s.descriptor.id;if(e.all.push(c),!tu({target:d,destination:t,viewport:a,withDroppableDisplacement:!0}))return e.invisible[s.descriptor.id]=!0,e;let u=tm(c,n,i);return e.visible[c]={draggableId:c,shouldAnimate:u},e},{all:[],visible:{},invisible:{}})}function tf({insideDestination:e,inHomeList:t,displacedBy:r,destination:a}){let i=function(e,t){if(!e.length)return 0;let r=e[e.length-1].descriptor.index;return t.inHomeList?r:r+1}(e,{inHomeList:t});return{displaced:e7,displacedBy:r,at:{type:"REORDER",destination:{droppableId:a.descriptor.id,index:i}}}}function tb({draggable:e,insideDestination:t,destination:r,viewport:a,displacedBy:i,last:n,index:s,forceShouldAnimate:o}){let l=e6(e,r);if(null==s)return tf({insideDestination:t,inHomeList:l,displacedBy:i,destination:r});let d=t.find(e=>e.descriptor.index===s);if(!d)return tf({insideDestination:t,inHomeList:l,displacedBy:i,destination:r});let c=e4(e,t),u=t.indexOf(d);return{displaced:th({afterDragging:c.slice(u),destination:r,displacedBy:i,last:n,viewport:a.frame,forceShouldAnimate:o}),displacedBy:i,at:{type:"REORDER",destination:{droppableId:r.descriptor.id,index:s}}}}function tv(e,t){return!!t.effected[e]}var tx=({isMovingForward:e,destination:t,draggables:r,combine:a,afterCritical:i})=>{if(!t.isCombineEnabled)return null;let n=a.draggableId,s=r[n].descriptor.index;return tv(n,i)?e?s:s-1:e?s+1:s},ty=({isMovingForward:e,isInHomeList:t,insideDestination:r,location:a})=>{if(!r.length)return null;let i=a.index,n=e?i+1:i-1,s=r[0].descriptor.index,o=r[r.length-1].descriptor.index;return n<s||n>(t?o:o+1)?null:n},tw=({isMovingForward:e,isInHomeList:t,draggable:r,draggables:a,destination:i,insideDestination:n,previousImpact:s,viewport:o,afterCritical:l})=>{let d=s.at;if(d||ek(),"REORDER"===d.type){let a=ty({isMovingForward:e,isInHomeList:t,location:d.destination,insideDestination:n});return null==a?null:tb({draggable:r,insideDestination:n,destination:i,viewport:o,last:s.displaced,displacedBy:s.displacedBy,index:a})}let c=tx({isMovingForward:e,destination:i,displaced:s.displaced,draggables:a,combine:d.combine,afterCritical:l});return null==c?null:tb({draggable:r,insideDestination:n,destination:i,viewport:o,last:s.displaced,displacedBy:s.displacedBy,index:c})},tI=({displaced:e,afterCritical:t,combineWith:r,displacedBy:a})=>{let i=!!(e.visible[r]||e.invisible[r]);return tv(r,t)?i?eM:e_(a.point):i?a.point:eM},tN=({afterCritical:e,impact:t,draggables:r})=>{let a=e3(t);a||ek();let i=a.draggableId;return eO(r[i].page.borderBox.center,tI({displaced:t.displaced,afterCritical:e,combineWith:i,displacedBy:t.displacedBy}))};let tk=(e,t)=>t.margin[e.start]+t.borderBox[e.size]/2,tD=(e,t)=>t.margin[e.end]+t.borderBox[e.size]/2,tA=(e,t,r)=>t[e.crossAxisStart]+r.margin[e.crossAxisStart]+r.borderBox[e.crossAxisSize]/2,tj=({axis:e,moveRelativeTo:t,isMoving:r})=>eG(e.line,t.marginBox[e.end]+tk(e,r),tA(e,t.marginBox,r)),tC=({axis:e,moveRelativeTo:t,isMoving:r})=>eG(e.line,t.marginBox[e.start]-tD(e,r),tA(e,t.marginBox,r)),tS=({axis:e,moveInto:t,isMoving:r})=>eG(e.line,t.contentBox[e.start]+tk(e,r),tA(e,t.contentBox,r));var tE=({impact:e,draggable:t,draggables:r,droppable:a,afterCritical:i})=>{let n=e2(a.descriptor.id,r),s=t.page,o=a.axis;if(!n.length)return tS({axis:o,moveInto:a.page,isMoving:s});let{displaced:l,displacedBy:d}=e,c=l.all[0];if(c){let e=r[c];return tv(c,i)?tC({axis:o,moveRelativeTo:e.page,isMoving:s}):tC({axis:o,moveRelativeTo:ed(e.page,d.point),isMoving:s})}let u=n[n.length-1];return u.descriptor.id===t.descriptor.id?s.borderBox.center:tv(u.descriptor.id,i)?tj({axis:o,moveRelativeTo:ed(u.page,e_(i.displacedBy.point)),isMoving:s}):tj({axis:o,moveRelativeTo:u.page,isMoving:s})},tP=(e,t)=>{let r=e.frame;return r?eO(t,r.scroll.diff.displacement):t};let tR=({impact:e,draggable:t,droppable:r,draggables:a,afterCritical:i})=>{let n=t.page.borderBox.center,s=e.at;return r&&s?"REORDER"===s.type?tE({impact:e,draggable:t,draggables:a,droppable:r,afterCritical:i}):tN({impact:e,draggables:a,afterCritical:i}):n};var tT=e=>{let t=tR(e),r=e.droppable;return r?tP(r,t):t},tM=(e,t)=>{let r=eB(t,e.scroll.initial),a=e_(r);return{frame:ea({top:t.y,bottom:t.y+e.frame.height,left:t.x,right:t.x+e.frame.width}),scroll:{initial:e.scroll.initial,max:e.scroll.max,current:t,diff:{value:r,displacement:a}}}};function tO(e,t){return e.map(e=>t[e])}var tB=({impact:e,viewport:t,destination:r,draggables:a,maxScrollChange:i})=>{let n=tM(t,eO(t.scroll.current,i)),s=r.frame?eK(r,eO(r.frame.scroll.current,i)):r,o=e.displaced,l=th({afterDragging:tO(o.all,a),destination:r,displacedBy:e.displacedBy,viewport:n.frame,last:o,forceShouldAnimate:!1}),d=th({afterDragging:tO(o.all,a),destination:s,displacedBy:e.displacedBy,viewport:t.frame,last:o,forceShouldAnimate:!1}),c={},u={},p=[o,l,d];return o.all.forEach(e=>{let t=function(e,t){for(let r=0;r<t.length;r++){let a=t[r].visible[e];if(a)return a}return null}(e,p);if(t){u[e]=t;return}c[e]=!0}),{...e,displaced:{all:o.all,invisible:c,visible:u}}},tL=(e,t)=>eO(e.scroll.diff.displacement,t),t_=({pageBorderBoxCenter:e,draggable:t,viewport:r})=>{let a=eB(tL(r,e),t.page.borderBox.center);return eO(t.client.borderBox.center,a)},tG=({draggable:e,destination:t,newPageBorderBoxCenter:r,viewport:a,withDroppableDisplacement:i,onlyOnMainAxis:n=!1})=>{let s=eB(r,e.page.borderBox.center),o={target:eF(e.page.borderBox,s),destination:t,withDroppableDisplacement:i,viewport:a};return n?tg(o):tp(o)},t$=({isMovingForward:e,draggable:t,destination:r,draggables:a,previousImpact:i,viewport:n,previousPageBorderBoxCenter:s,previousClientSelection:o,afterCritical:l})=>{if(!r.isEnabled)return null;let d=e2(r.descriptor.id,a),c=e6(t,r),u=e8({isMovingForward:e,draggable:t,destination:r,insideDestination:d,previousImpact:i})||tw({isMovingForward:e,isInHomeList:c,draggable:t,draggables:a,destination:r,insideDestination:d,previousImpact:i,viewport:n,afterCritical:l});if(!u)return null;let p=tT({impact:u,draggable:t,droppable:r,draggables:a,afterCritical:l});if(tG({draggable:t,destination:r,newPageBorderBoxCenter:p,viewport:n.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0}))return{clientSelection:t_({pageBorderBoxCenter:p,draggable:t,viewport:n}),impact:u,scrollJumpRequest:null};let g=eB(p,s);return{clientSelection:o,impact:tB({impact:u,viewport:n,destination:r,draggables:a,maxScrollChange:g}),scrollJumpRequest:g}};let tq=e=>{let t=e.subject.active;return t||ek(),t};var tz=({isMovingForward:e,pageBorderBoxCenter:t,source:r,droppables:a,viewport:i})=>{let n=r.subject.active;if(!n)return null;let s=r.axis,o=tt(n[s.start],n[s.end]),l=e0(a).filter(e=>e!==r).filter(e=>e.isEnabled).filter(e=>!!e.subject.active).filter(e=>tr(i.frame)(tq(e))).filter(t=>{let r=tq(t);return e?n[s.crossAxisEnd]<r[s.crossAxisEnd]:r[s.crossAxisStart]<n[s.crossAxisStart]}).filter(e=>{let t=tq(e),r=tt(t[s.start],t[s.end]);return o(t[s.start])||o(t[s.end])||r(n[s.start])||r(n[s.end])}).sort((t,r)=>{let a=tq(t)[s.crossAxisStart],i=tq(r)[s.crossAxisStart];return e?a-i:i-a}).filter((e,t,r)=>tq(e)[s.crossAxisStart]===tq(r[0])[s.crossAxisStart]);if(!l.length)return null;if(1===l.length)return l[0];let d=l.filter(e=>tt(tq(e)[s.start],tq(e)[s.end])(t[s.line]));return 1===d.length?d[0]:d.length>1?d.sort((e,t)=>tq(e)[s.start]-tq(t)[s.start])[0]:l.sort((e,r)=>{let a=eq(t,eU(tq(e))),i=eq(t,eU(tq(r)));return a!==i?a-i:tq(e)[s.start]-tq(r)[s.start]})[0]};let tW=(e,t)=>{let r=e.page.borderBox.center;return tv(e.descriptor.id,t)?eB(r,t.displacedBy.point):r},tF=(e,t)=>{let r=e.page.borderBox;return tv(e.descriptor.id,t)?eF(r,e_(t.displacedBy.point)):r};var tU=({pageBorderBoxCenter:e,viewport:t,destination:r,insideDestination:a,afterCritical:i})=>a.filter(e=>tp({target:tF(e,i),destination:r,viewport:t.frame,withDroppableDisplacement:!0})).sort((t,a)=>{let n=e$(e,tP(r,tW(t,i))),s=e$(e,tP(r,tW(a,i)));return n<s?-1:s<n?1:t.descriptor.index-a.descriptor.index})[0]||null,tV=eX(function(e,t){let r=t[e.line];return{value:r,point:eG(e.line,r)}});let tH=(e,t,r)=>{let a=e.axis;if("virtual"===e.descriptor.mode)return eG(a.line,t[a.line]);let i=e.subject.page.contentBox[a.size],n=e2(e.descriptor.id,r).reduce((e,t)=>e+t.client.marginBox[a.size],0)+t[a.line]-i;return n<=0?null:eG(a.line,n)},tJ=(e,t)=>({...e,scroll:{...e.scroll,max:t}}),tY=(e,t,r)=>{let a=e.frame;e6(t,e)&&ek(),e.subject.withPlaceholder&&ek();let i=tV(e.axis,t.displaceBy).point,n=tH(e,i,r),s={placeholderSize:i,increasedBy:n,oldFrameMaxScroll:e.frame?e.frame.scroll.max:null};if(!a){let t=eY({page:e.subject.page,withPlaceholder:s,axis:e.axis,frame:e.frame});return{...e,subject:t}}let o=n?eO(a.scroll.max,n):a.scroll.max,l=tJ(a,o),d=eY({page:e.subject.page,withPlaceholder:s,axis:e.axis,frame:l});return{...e,subject:d,frame:l}},tK=e=>{let t=e.subject.withPlaceholder;t||ek();let r=e.frame;if(!r){let t=eY({page:e.subject.page,axis:e.axis,frame:null,withPlaceholder:null});return{...e,subject:t}}let a=t.oldFrameMaxScroll;a||ek();let i=tJ(r,a),n=eY({page:e.subject.page,axis:e.axis,frame:i,withPlaceholder:null});return{...e,subject:n,frame:i}};var tX=({previousPageBorderBoxCenter:e,moveRelativeTo:t,insideDestination:r,draggable:a,draggables:i,destination:n,viewport:s,afterCritical:o})=>{if(!t){if(r.length)return null;let e={displaced:e7,displacedBy:e9,at:{type:"REORDER",destination:{droppableId:n.descriptor.id,index:0}}},t=tT({impact:e,draggable:a,droppable:n,draggables:i,afterCritical:o}),l=e6(a,n)?n:tY(n,a,i);return tG({draggable:a,destination:l,newPageBorderBoxCenter:t,viewport:s.frame,withDroppableDisplacement:!1,onlyOnMainAxis:!0})?e:null}let l=e[n.axis.line]<=t.page.borderBox.center[n.axis.line],d=(()=>{let e=t.descriptor.index;return t.descriptor.id===a.descriptor.id||l?e:e+1})(),c=tV(n.axis,a.displaceBy);return tb({draggable:a,insideDestination:r,destination:n,viewport:s,displacedBy:c,last:e7,index:d})},tZ=({isMovingForward:e,previousPageBorderBoxCenter:t,draggable:r,isOver:a,draggables:i,droppables:n,viewport:s,afterCritical:o})=>{let l=tz({isMovingForward:e,pageBorderBoxCenter:t,source:a,droppables:n,viewport:s});if(!l)return null;let d=e2(l.descriptor.id,i),c=tU({pageBorderBoxCenter:t,viewport:s,destination:l,insideDestination:d,afterCritical:o}),u=tX({previousPageBorderBoxCenter:t,destination:l,draggable:r,draggables:i,moveRelativeTo:c,insideDestination:d,viewport:s,afterCritical:o});return u?{clientSelection:t_({pageBorderBoxCenter:tT({impact:u,draggable:r,droppable:l,draggables:i,afterCritical:o}),draggable:r,viewport:s}),impact:u,scrollJumpRequest:null}:null},tQ=e=>{let t=e.at;return t?"REORDER"===t.type?t.destination.droppableId:t.combine.droppableId:null};let t0=(e,t)=>{let r=tQ(e);return r?t[r]:null};var t1=({state:e,type:t})=>{let r=t0(e.impact,e.dimensions.droppables),a=!!r,i=e.dimensions.droppables[e.critical.droppable.id],n=r||i,s=n.axis.direction,o="vertical"===s&&("MOVE_UP"===t||"MOVE_DOWN"===t)||"horizontal"===s&&("MOVE_LEFT"===t||"MOVE_RIGHT"===t);if(o&&!a)return null;let l="MOVE_DOWN"===t||"MOVE_RIGHT"===t,d=e.dimensions.draggables[e.critical.draggable.id],c=e.current.page.borderBoxCenter,{draggables:u,droppables:p}=e.dimensions;return o?t$({isMovingForward:l,previousPageBorderBoxCenter:c,draggable:d,destination:n,draggables:u,viewport:e.viewport,previousClientSelection:e.current.client.selection,previousImpact:e.impact,afterCritical:e.afterCritical}):tZ({isMovingForward:l,previousPageBorderBoxCenter:c,draggable:d,isOver:n,draggables:u,droppables:p,viewport:e.viewport,afterCritical:e.afterCritical})};function t2(e){return"DRAGGING"===e.phase||"COLLECTING"===e.phase}function t5(e){let t=tt(e.top,e.bottom),r=tt(e.left,e.right);return function(e){return t(e.y)&&r(e.x)}}let t3=(e,t)=>ea(eF(e,t));var t4=(e,t)=>{let r=e.frame;return r?t3(t,r.scroll.diff.value):t};function t8({displaced:e,id:t}){return!!(e.visible[t]||e.invisible[t])}var t6=({pageBorderBoxWithDroppableScroll:e,draggable:t,destination:r,insideDestination:a,last:i,viewport:n,afterCritical:s})=>{let o=r.axis,l=tV(r.axis,t.displaceBy),d=l.value,c=e[o.start],u=e[o.end],p=e4(t,a).find(e=>{let t=e.descriptor.id,r=e.page.borderBox.center[o.line],a=tv(t,s),n=t8({displaced:i,id:t});return a?n?u<=r:c<r-d:n?u<=r+d:c<r})||null,g=function({draggable:e,closest:t,inHomeList:r}){return t?r&&t.descriptor.index>e.descriptor.index?t.descriptor.index-1:t.descriptor.index:null}({draggable:t,closest:p,inHomeList:e6(t,r)});return tb({draggable:t,insideDestination:a,destination:r,viewport:n,last:i,displacedBy:l,index:g})},t9=({draggable:e,pageBorderBoxWithDroppableScroll:t,previousImpact:r,destination:a,insideDestination:i,afterCritical:n})=>{if(!a.isCombineEnabled)return null;let s=a.axis,o=tV(a.axis,e.displaceBy),l=o.value,d=t[s.start],c=t[s.end],u=e4(e,i).find(e=>{let t=e.descriptor.id,a=e.page.borderBox,i=a[s.size]/4,o=tv(t,n),u=t8({displaced:r.displaced,id:t});return o?u?c>a[s.start]+i&&c<a[s.end]-i:d>a[s.start]-l+i&&d<a[s.end]-l-i:u?c>a[s.start]+l+i&&c<a[s.end]+l-i:d>a[s.start]+i&&d<a[s.end]-i});return u?{displacedBy:o,displaced:r.displaced,at:{type:"COMBINE",combine:{draggableId:u.descriptor.id,droppableId:a.descriptor.id}}}:null},t7=({pageOffset:e,draggable:t,draggables:r,droppables:a,previousImpact:i,viewport:n,afterCritical:s})=>{let o=t3(t.page.borderBox,e),l=function({pageBorderBox:e,draggable:t,droppables:r}){let a=e0(r).filter(t=>{if(!t.isEnabled)return!1;let r=t.subject.active;if(!r||!(e.left<r.right)||!(e.right>r.left)||!(e.top<r.bottom)||!(e.bottom>r.top))return!1;if(t5(r)(e.center))return!0;let a=t.axis,i=r.center[a.crossAxisLine],n=e[a.crossAxisStart],s=e[a.crossAxisEnd],o=tt(r[a.crossAxisStart],r[a.crossAxisEnd]),l=o(n),d=o(s);return!l&&!d||(l?n<i:s>i)});return a.length?1===a.length?a[0].descriptor.id:function({pageBorderBox:e,draggable:t,candidates:r}){let a=t.page.borderBox.center,i=r.map(t=>{let r=t.axis,i=eG(t.axis.line,e.center[r.line],t.page.borderBox.center[r.crossAxisLine]);return{id:t.descriptor.id,distance:e$(a,i)}}).sort((e,t)=>t.distance-e.distance);return i[0]?i[0].id:null}({pageBorderBox:e,draggable:t,candidates:a}):null}({pageBorderBox:o,draggable:t,droppables:a});if(!l)return te;let d=a[l],c=e2(d.descriptor.id,r),u=t4(d,o);return t9({pageBorderBoxWithDroppableScroll:u,draggable:t,previousImpact:i,destination:d,insideDestination:c,afterCritical:s})||t6({pageBorderBoxWithDroppableScroll:u,draggable:t,destination:d,insideDestination:c,last:i.displaced,viewport:n,afterCritical:s})},re=(e,t)=>({...e,[t.descriptor.id]:t});let rt=({previousImpact:e,impact:t,droppables:r})=>{let a=tQ(e),i=tQ(t);if(!a||a===i)return r;let n=r[a];return n.subject.withPlaceholder?re(r,tK(n)):r};var rr=({draggable:e,draggables:t,droppables:r,previousImpact:a,impact:i})=>{let n=rt({previousImpact:a,impact:i,droppables:r}),s=tQ(i);if(!s)return n;let o=r[s];return e6(e,o)||o.subject.withPlaceholder?n:re(n,tY(o,e,t))},ra=({state:e,clientSelection:t,dimensions:r,viewport:a,impact:i,scrollJumpRequest:n})=>{let s=a||e.viewport,o=r||e.dimensions,l=t||e.current.client.selection,d=eB(l,e.initial.client.selection),c={offset:d,selection:l,borderBoxCenter:eO(e.initial.client.borderBoxCenter,d)},u={selection:eO(c.selection,s.scroll.current),borderBoxCenter:eO(c.borderBoxCenter,s.scroll.current),offset:eO(c.offset,s.scroll.diff.value)},p={client:c,page:u};if("COLLECTING"===e.phase)return{...e,dimensions:o,viewport:s,current:p};let g=o.draggables[e.critical.draggable.id],m=i||t7({pageOffset:u.offset,draggable:g,draggables:o.draggables,droppables:o.droppables,previousImpact:e.impact,viewport:s,afterCritical:e.afterCritical}),h=rr({draggable:g,impact:m,previousImpact:e.impact,draggables:o.draggables,droppables:o.droppables});return{...e,current:p,dimensions:{draggables:o.draggables,droppables:h},impact:m,viewport:s,scrollJumpRequest:n||null,forceShouldAnimate:!n&&null}},ri=({impact:e,viewport:t,draggables:r,destination:a,forceShouldAnimate:i})=>{var n;let s=e.displaced,o=th({afterDragging:(n=s.all,n.map(e=>r[e])),destination:a,displacedBy:e.displacedBy,viewport:t.frame,forceShouldAnimate:i,last:s});return{...e,displaced:o}},rn=({impact:e,draggable:t,droppable:r,draggables:a,viewport:i,afterCritical:n})=>t_({pageBorderBoxCenter:tT({impact:e,draggable:t,draggables:a,droppable:r,afterCritical:n}),draggable:t,viewport:i}),rs=({state:e,dimensions:t,viewport:r})=>{"SNAP"!==e.movementMode&&ek();let a=e.impact,i=r||e.viewport,n=t||e.dimensions,{draggables:s,droppables:o}=n,l=s[e.critical.draggable.id],d=tQ(a);d||ek();let c=o[d],u=ri({impact:a,viewport:i,destination:c,draggables:s}),p=rn({impact:u,draggable:l,droppable:c,draggables:s,viewport:i,afterCritical:e.afterCritical});return ra({impact:u,clientSelection:p,state:e,dimensions:n,viewport:i})},ro=e=>({index:e.index,droppableId:e.droppableId}),rl=({draggable:e,home:t,draggables:r,viewport:a})=>{let i=tV(t.axis,e.displaceBy),n=e2(t.descriptor.id,r),s=n.indexOf(e);-1===s&&ek();let o=n.slice(s+1),l=o.reduce((e,t)=>(e[t.descriptor.id]=!0,e),{}),d={inVirtualList:"virtual"===t.descriptor.mode,displacedBy:i,effected:l};return{impact:{displaced:th({afterDragging:o,destination:t,displacedBy:i,last:null,viewport:a.frame,forceShouldAnimate:!1}),displacedBy:i,at:{type:"REORDER",destination:ro(e.descriptor)}},afterCritical:d}},rd=(e,t)=>({draggables:e.draggables,droppables:re(e.droppables,t)});let rc=e=>{},ru=e=>{};var rp=({draggable:e,offset:t,initialWindowScroll:r})=>{let a=ed(e.client,t),i=ec(a,r);return{...e,placeholder:{...e.placeholder,client:a},client:a,page:i}},rg=e=>{let t=e.frame;return t||ek(),t},rm=({additions:e,updatedDroppables:t,viewport:r})=>{let a=r.scroll.diff.value;return e.map(e=>{let i=eO(a,rg(t[e.descriptor.droppableId]).scroll.diff.value);return rp({draggable:e,offset:i,initialWindowScroll:r.scroll.initial})})},rh=({state:e,published:t})=>{rc();let r=t.modified.map(t=>eK(e.dimensions.droppables[t.droppableId],t.scroll)),a={...e.dimensions.droppables,...eZ(r)},i=eQ(rm({additions:t.additions,updatedDroppables:a,viewport:e.viewport})),n={...e.dimensions.draggables,...i};t.removals.forEach(e=>{delete n[e]});let s={droppables:a,draggables:n},o=tQ(e.impact),l=o?s.droppables[o]:null,{impact:d,afterCritical:c}=rl({draggable:s.draggables[e.critical.draggable.id],home:s.droppables[e.critical.droppable.id],draggables:n,viewport:e.viewport}),u=l&&l.isCombineEnabled?e.impact:d,p=t7({pageOffset:e.current.page.offset,draggable:s.draggables[e.critical.draggable.id],draggables:s.draggables,droppables:s.droppables,previousImpact:u,viewport:e.viewport,afterCritical:c});ru();let g={...e,phase:"DRAGGING",impact:p,onLiftImpact:d,dimensions:s,afterCritical:c,forceShouldAnimate:!1};return"COLLECTING"===e.phase?g:{...g,phase:"DROP_PENDING",reason:e.reason,isWaiting:!1}};let rf=e=>"SNAP"===e.movementMode,rb=(e,t,r)=>{let a=rd(e.dimensions,t);return!rf(e)||r?ra({state:e,dimensions:a}):rs({state:e,dimensions:a})};function rv(e){return e.isDragging&&"SNAP"===e.movementMode?{...e,scrollJumpRequest:null}:e}let rx={phase:"IDLE",completed:null,shouldFlush:!1};var ry=(e=rx,t)=>{if("FLUSH"===t.type)return{...rx,shouldFlush:!0};if("INITIAL_PUBLISH"===t.type){"IDLE"!==e.phase&&ek();let{critical:r,clientSelection:a,viewport:i,dimensions:n,movementMode:s}=t.payload,o=n.draggables[r.draggable.id],l=n.droppables[r.droppable.id],d={selection:a,borderBoxCenter:o.client.borderBox.center,offset:eM},c={client:d,page:{selection:eO(d.selection,i.scroll.initial),borderBoxCenter:eO(d.selection,i.scroll.initial),offset:eO(d.selection,i.scroll.diff.value)}},u=e0(n.droppables).every(e=>!e.isFixedOnPage),{impact:p,afterCritical:g}=rl({draggable:o,home:l,draggables:n.draggables,viewport:i});return{phase:"DRAGGING",isDragging:!0,critical:r,movementMode:s,dimensions:n,initial:c,current:c,isWindowScrollAllowed:u,impact:p,afterCritical:g,onLiftImpact:p,viewport:i,scrollJumpRequest:null,forceShouldAnimate:null}}if("COLLECTION_STARTING"===t.type)return"COLLECTING"===e.phase||"DROP_PENDING"===e.phase?e:("DRAGGING"!==e.phase&&ek(),{...e,phase:"COLLECTING"});if("PUBLISH_WHILE_DRAGGING"===t.type)return"COLLECTING"!==e.phase&&"DROP_PENDING"!==e.phase&&ek(),rh({state:e,published:t.payload});if("MOVE"===t.type){if("DROP_PENDING"===e.phase)return e;t2(e)||ek();let{client:r}=t.payload;return eL(r,e.current.client.selection)?e:ra({state:e,clientSelection:r,impact:rf(e)?e.impact:null})}if("UPDATE_DROPPABLE_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"COLLECTING"===e.phase)return rv(e);t2(e)||ek();let{id:r,newScroll:a}=t.payload,i=e.dimensions.droppables[r];return i?rb(e,eK(i,a),!1):e}if("UPDATE_DROPPABLE_IS_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;t2(e)||ek();let{id:r,isEnabled:a}=t.payload,i=e.dimensions.droppables[r];return i||ek(),i.isEnabled===a&&ek(),rb(e,{...i,isEnabled:a},!0)}if("UPDATE_DROPPABLE_IS_COMBINE_ENABLED"===t.type){if("DROP_PENDING"===e.phase)return e;t2(e)||ek();let{id:r,isCombineEnabled:a}=t.payload,i=e.dimensions.droppables[r];return i||ek(),i.isCombineEnabled===a&&ek(),rb(e,{...i,isCombineEnabled:a},!0)}if("MOVE_BY_WINDOW_SCROLL"===t.type){if("DROP_PENDING"===e.phase||"DROP_ANIMATING"===e.phase)return e;t2(e)||ek(),e.isWindowScrollAllowed||ek();let r=t.payload.newScroll;if(eL(e.viewport.scroll.current,r))return rv(e);let a=tM(e.viewport,r);return rf(e)?rs({state:e,viewport:a}):ra({state:e,viewport:a})}if("UPDATE_VIEWPORT_MAX_SCROLL"===t.type){if(!t2(e))return e;let r=t.payload.maxScroll;if(eL(r,e.viewport.scroll.max))return e;let a={...e.viewport,scroll:{...e.viewport.scroll,max:r}};return{...e,viewport:a}}if("MOVE_UP"===t.type||"MOVE_DOWN"===t.type||"MOVE_LEFT"===t.type||"MOVE_RIGHT"===t.type){if("COLLECTING"===e.phase||"DROP_PENDING"===e.phase)return e;"DRAGGING"!==e.phase&&ek();let r=t1({state:e,type:t.type});return r?ra({state:e,impact:r.impact,clientSelection:r.clientSelection,scrollJumpRequest:r.scrollJumpRequest}):e}if("DROP_PENDING"===t.type){let r=t.payload.reason;return"COLLECTING"!==e.phase&&ek(),{...e,phase:"DROP_PENDING",isWaiting:!0,reason:r}}if("DROP_ANIMATE"===t.type){let{completed:r,dropDuration:a,newHomeClientOffset:i}=t.payload;return"DRAGGING"!==e.phase&&"DROP_PENDING"!==e.phase&&ek(),{phase:"DROP_ANIMATING",completed:r,dropDuration:a,newHomeClientOffset:i,dimensions:e.dimensions}}if("DROP_COMPLETE"===t.type){let{completed:e}=t.payload;return{phase:"IDLE",completed:e,shouldFlush:!1}}return e};function rw(e,t){return e instanceof Object&&"type"in e&&e.type===t}let rI=e=>({type:"BEFORE_INITIAL_CAPTURE",payload:e}),rN=e=>({type:"LIFT",payload:e}),rk=e=>({type:"INITIAL_PUBLISH",payload:e}),rD=e=>({type:"PUBLISH_WHILE_DRAGGING",payload:e}),rA=()=>({type:"COLLECTION_STARTING",payload:null}),rj=e=>({type:"UPDATE_DROPPABLE_SCROLL",payload:e}),rC=e=>({type:"UPDATE_DROPPABLE_IS_ENABLED",payload:e}),rS=e=>({type:"UPDATE_DROPPABLE_IS_COMBINE_ENABLED",payload:e}),rE=e=>({type:"MOVE",payload:e}),rP=e=>({type:"MOVE_BY_WINDOW_SCROLL",payload:e}),rR=()=>({type:"MOVE_UP",payload:null}),rT=()=>({type:"MOVE_DOWN",payload:null}),rM=()=>({type:"MOVE_RIGHT",payload:null}),rO=()=>({type:"MOVE_LEFT",payload:null}),rB=()=>({type:"FLUSH",payload:null}),rL=e=>({type:"DROP_ANIMATE",payload:e}),r_=e=>({type:"DROP_COMPLETE",payload:e}),rG=e=>({type:"DROP",payload:e}),r$=e=>({type:"DROP_PENDING",payload:e}),rq=()=>({type:"DROP_ANIMATION_FINISHED",payload:null});var rz=e=>({getState:t,dispatch:r})=>a=>i=>{if(!rw(i,"LIFT"))return void a(i);let{id:n,clientSelection:s,movementMode:o}=i.payload,l=t();"DROP_ANIMATING"===l.phase&&r(r_({completed:l.completed})),"IDLE"!==t().phase&&ek(),r(rB()),r(rI({draggableId:n,movementMode:o}));let{critical:d,dimensions:c,viewport:u}=e.startPublishing({draggableId:n,scrollOptions:{shouldPublishImmediately:"SNAP"===o}});r(rk({critical:d,dimensions:c,clientSelection:s,movementMode:o,viewport:u}))},rW=e=>()=>t=>r=>{rw(r,"INITIAL_PUBLISH")&&e.dragging(),rw(r,"DROP_ANIMATE")&&e.dropping(r.payload.completed.result.reason),(rw(r,"FLUSH")||rw(r,"DROP_COMPLETE"))&&e.resting(),t(r)};let rF={outOfTheWay:"cubic-bezier(0.2, 0, 0, 1)",drop:"cubic-bezier(.2,1,.1,1)"},rU={opacity:{drop:0,combining:.7},scale:{drop:.75}},rV={outOfTheWay:.2,minDropTime:.33,maxDropTime:.55},rH=`${rV.outOfTheWay}s ${rF.outOfTheWay}`,rJ={fluid:`opacity ${rH}`,snap:`transform ${rH}, opacity ${rH}`,drop:e=>{let t=`${e}s ${rF.drop}`;return`transform ${t}, opacity ${t}`},outOfTheWay:`transform ${rH}`,placeholder:`height ${rH}, width ${rH}, margin ${rH}`},rY=e=>eL(e,eM)?void 0:`translate(${e.x}px, ${e.y}px)`,rK={moveTo:rY,drop:(e,t)=>{let r=rY(e);if(r)return t?`${r} scale(${rU.scale.drop})`:r}},{minDropTime:rX,maxDropTime:rZ}=rV,rQ=rZ-rX;var r0=({current:e,destination:t,reason:r})=>{let a=e$(e,t);if(a<=0)return rX;if(a>=1500)return rZ;let i=rX+a/1500*rQ;return Number(("CANCEL"===r?.6*i:i).toFixed(2))},r1=({impact:e,draggable:t,dimensions:r,viewport:a,afterCritical:i})=>{let{draggables:n,droppables:s}=r,o=tQ(e),l=o?s[o]:null,d=s[t.descriptor.droppableId];return eB(rn({impact:e,draggable:t,draggables:n,afterCritical:i,droppable:l||d,viewport:a}),t.client.borderBox.center)},r2=({draggables:e,reason:t,lastImpact:r,home:a,viewport:i,onLiftImpact:n})=>r.at&&"DROP"===t?"REORDER"===r.at.type?{impact:r,didDropInsideDroppable:!0}:{impact:{...r,displaced:e7},didDropInsideDroppable:!0}:{impact:ri({draggables:e,impact:n,destination:a,viewport:i,forceShouldAnimate:!0}),didDropInsideDroppable:!1};let r5=({getState:e,dispatch:t})=>r=>a=>{if(!rw(a,"DROP"))return void r(a);let i=e(),n=a.payload.reason;if("COLLECTING"===i.phase)return void t(r$({reason:n}));if("IDLE"===i.phase)return;"DROP_PENDING"===i.phase&&i.isWaiting&&ek(),"DRAGGING"!==i.phase&&"DROP_PENDING"!==i.phase&&ek();let s=i.critical,o=i.dimensions,l=o.draggables[i.critical.draggable.id],{impact:d,didDropInsideDroppable:c}=r2({reason:n,lastImpact:i.impact,afterCritical:i.afterCritical,onLiftImpact:i.onLiftImpact,home:i.dimensions.droppables[i.critical.droppable.id],viewport:i.viewport,draggables:i.dimensions.draggables}),u=c?e5(d):null,p=c?e3(d):null,g={index:s.draggable.index,droppableId:s.droppable.id},m={draggableId:l.descriptor.id,type:l.descriptor.type,source:g,reason:n,mode:i.movementMode,destination:u,combine:p},h=r1({impact:d,draggable:l,dimensions:o,viewport:i.viewport,afterCritical:i.afterCritical}),f={critical:i.critical,afterCritical:i.afterCritical,result:m,impact:d};if(!(!eL(i.current.client.offset,h)||m.combine))return void t(r_({completed:f}));let b=r0({current:i.current.client.offset,destination:h,reason:n});t(rL({newHomeClientOffset:h,dropDuration:b,completed:f}))};var r3=()=>({x:window.pageXOffset,y:window.pageYOffset});let r4=e=>rw(e,"DROP_COMPLETE")||rw(e,"DROP_ANIMATE")||rw(e,"FLUSH"),r8=e=>{let t=function({onWindowScroll:e}){let t=eg(function(){e(r3())}),r={eventName:"scroll",options:{passive:!0,capture:!1},fn:e=>{(e.target===window||e.target===window.document)&&t()}},a=ew;function i(){return a!==ew}return{start:function(){i()&&ek(),a=eI(window,[r])},stop:function(){i()||ek(),t.cancel(),a(),a=ew},isActive:i}}({onWindowScroll:t=>{e.dispatch(rP({newScroll:t}))}});return e=>r=>{!t.isActive()&&rw(r,"INITIAL_PUBLISH")&&t.start(),t.isActive()&&r4(r)&&t.stop(),e(r)}};var r6=e=>{let t=!1,r=!1,a=setTimeout(()=>{r=!0}),i=i=>{!t&&(r||(t=!0,e(i),clearTimeout(a)))};return i.wasCalled=()=>t,i},r9=()=>{let e=[],t=t=>{let r=e.findIndex(e=>e.timerId===t);-1===r&&ek();let[a]=e.splice(r,1);a.callback()};return{add:r=>{let a=setTimeout(()=>t(a));e.push({timerId:a,callback:r})},flush:()=>{if(!e.length)return;let t=[...e];e.length=0,t.forEach(e=>{clearTimeout(e.timerId),e.callback()})}}};let r7=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.droppableId===t.droppableId&&e.index===t.index,ae=(e,t)=>null==e&&null==t||null!=e&&null!=t&&e.draggableId===t.draggableId&&e.droppableId===t.droppableId,at=(e,t)=>{if(e===t)return!0;let r=e.draggable.id===t.draggable.id&&e.draggable.droppableId===t.draggable.droppableId&&e.draggable.type===t.draggable.type&&e.draggable.index===t.draggable.index,a=e.droppable.id===t.droppable.id&&e.droppable.type===t.droppable.type;return r&&a},ar=(e,t)=>{rc(),t(),ru()},aa=(e,t)=>({draggableId:e.draggable.id,type:e.droppable.type,source:{droppableId:e.droppable.id,index:e.draggable.index},mode:t});function ai(e,t,r,a){if(!e)return void r(a(t));let i=r6(r);e(t,{announce:i}),i.wasCalled()||r(a(t))}var an=(e,t)=>{let r=r9(),a=null,i=r=>{a||ek(),a=null,ar("onDragEnd",()=>ai(e().onDragEnd,r,t,eE.onDragEnd))};return{beforeCapture:(t,r)=>{a&&ek(),ar("onBeforeCapture",()=>{let a=e().onBeforeCapture;a&&a({draggableId:t,mode:r})})},beforeStart:(t,r)=>{a&&ek(),ar("onBeforeDragStart",()=>{let a=e().onBeforeDragStart;a&&a(aa(t,r))})},start:(i,n)=>{a&&ek();let s=aa(i,n);a={mode:n,lastCritical:i,lastLocation:s.source,lastCombine:null},r.add(()=>{ar("onDragStart",()=>ai(e().onDragStart,s,t,eE.onDragStart))})},update:(i,n)=>{let s=e5(n),o=e3(n);a||ek();let l=!at(i,a.lastCritical);l&&(a.lastCritical=i);let d=!r7(a.lastLocation,s);d&&(a.lastLocation=s);let c=!ae(a.lastCombine,o);if(c&&(a.lastCombine=o),!l&&!d&&!c)return;let u={...aa(i,a.mode),combine:o,destination:s};r.add(()=>{ar("onDragUpdate",()=>ai(e().onDragUpdate,u,t,eE.onDragUpdate))})},flush:()=>{a||ek(),r.flush()},drop:i,abort:()=>{a&&i({...aa(a.lastCritical,a.mode),combine:null,destination:null,reason:"CANCEL"})}}},as=(e,t)=>{let r=an(e,t);return e=>t=>a=>{if(rw(a,"BEFORE_INITIAL_CAPTURE"))return void r.beforeCapture(a.payload.draggableId,a.payload.movementMode);if(rw(a,"INITIAL_PUBLISH")){let e=a.payload.critical;r.beforeStart(e,a.payload.movementMode),t(a),r.start(e,a.payload.movementMode);return}if(rw(a,"DROP_COMPLETE")){let e=a.payload.completed.result;r.flush(),t(a),r.drop(e);return}if(t(a),rw(a,"FLUSH"))return void r.abort();let i=e.getState();"DRAGGING"===i.phase&&r.update(i.critical,i.impact)}};let ao=e=>t=>r=>{if(!rw(r,"DROP_ANIMATION_FINISHED"))return void t(r);let a=e.getState();"DROP_ANIMATING"!==a.phase&&ek(),e.dispatch(r_({completed:a.completed}))},al=e=>{let t=null,r=null;return a=>i=>{if((rw(i,"FLUSH")||rw(i,"DROP_COMPLETE")||rw(i,"DROP_ANIMATION_FINISHED"))&&(r&&(cancelAnimationFrame(r),r=null),t&&(t(),t=null)),a(i),!rw(i,"DROP_ANIMATE"))return;let n={eventName:"scroll",options:{capture:!0,passive:!1,once:!0},fn:function(){"DROP_ANIMATING"===e.getState().phase&&e.dispatch(rq())}};r=requestAnimationFrame(()=>{r=null,t=eI(window,[n])})}};var ad=e=>()=>t=>r=>{(rw(r,"DROP_COMPLETE")||rw(r,"FLUSH")||rw(r,"DROP_ANIMATE"))&&e.stopPublishing(),t(r)},ac=e=>{let t=!1;return()=>r=>a=>{if(rw(a,"INITIAL_PUBLISH")){t=!0,e.tryRecordFocus(a.payload.critical.draggable.id),r(a),e.tryRestoreFocusRecorded();return}if(r(a),t){if(rw(a,"FLUSH")){t=!1,e.tryRestoreFocusRecorded();return}if(rw(a,"DROP_COMPLETE")){t=!1;let r=a.payload.completed.result;r.combine&&e.tryShiftRecord(r.draggableId,r.combine.draggableId),e.tryRestoreFocusRecorded()}}}};let au=e=>rw(e,"DROP_COMPLETE")||rw(e,"DROP_ANIMATE")||rw(e,"FLUSH");var ap=e=>t=>r=>a=>{if(au(a)){e.stop(),r(a);return}if(rw(a,"INITIAL_PUBLISH")){r(a);let i=t.getState();"DRAGGING"!==i.phase&&ek(),e.start(i);return}r(a),e.scroll(t.getState())};let ag=e=>t=>r=>{if(t(r),!rw(r,"PUBLISH_WHILE_DRAGGING"))return;let a=e.getState();"DROP_PENDING"===a.phase&&(a.isWaiting||e.dispatch(rG({reason:a.reason})))},am=ee.Zz;var ah=({dimensionMarshal:e,focusMarshal:t,styleMarshal:r,getResponders:a,announce:i,autoScroller:n})=>(0,ee.y$)(ry,am((0,ee.Tw)(rW(r),ad(e),rz(e),r5,ao,al,ag,ap(n),r8,ac(t),as(a,i))));let af=()=>({additions:{},removals:{},modified:{}});var ab=({scrollHeight:e,scrollWidth:t,height:r,width:a})=>{let i=eB({x:t,y:e},{x:a,y:r});return{x:Math.max(0,i.x),y:Math.max(0,i.y)}},av=()=>{let e=document.documentElement;return e||ek(),e},ax=()=>{let e=av();return ab({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,width:e.clientWidth,height:e.clientHeight})},ay=()=>{let e=r3(),t=ax(),r=e.y,a=e.x,i=av(),n=i.clientWidth;return{frame:ea({top:r,left:a,right:a+n,bottom:r+i.clientHeight}),scroll:{initial:e,current:e,max:t,diff:{value:eM,displacement:eM}}}},aw=({critical:e,scrollOptions:t,registry:r})=>{rc();let a=ay(),i=a.scroll.current,n=e.droppable,s=r.droppable.getAllByType(n.type).map(e=>e.callbacks.getDimensionAndWatchScroll(i,t)),o={draggables:eQ(r.draggable.getAllByType(e.draggable.type).map(e=>e.getDimension(i))),droppables:eZ(s)};return ru(),{dimensions:o,critical:e,viewport:a}};function aI(e,t,r){return r.descriptor.id!==t.id&&r.descriptor.type===t.type&&"virtual"===e.droppable.getById(r.descriptor.droppableId).descriptor.mode}var aN=(e,t)=>{let r=null,a=function({registry:e,callbacks:t}){let r=af(),a=null,i=()=>{a||(t.collectionStarting(),a=requestAnimationFrame(()=>{a=null,rc();let{additions:i,removals:n,modified:s}=r,o=Object.keys(i).map(t=>e.draggable.getById(t).getDimension(eM)).sort((e,t)=>e.descriptor.index-t.descriptor.index),l=Object.keys(s).map(t=>{let r=e.droppable.getById(t).callbacks.getScrollWhileDragging();return{droppableId:t,scroll:r}}),d={additions:o,removals:Object.keys(n),modified:l};r=af(),ru(),t.publish(d)}))};return{add:e=>{let t=e.descriptor.id;r.additions[t]=e,r.modified[e.descriptor.droppableId]=!0,r.removals[t]&&delete r.removals[t],i()},remove:e=>{let t=e.descriptor;r.removals[t.id]=!0,r.modified[t.droppableId]=!0,r.additions[t.id]&&delete r.additions[t.id],i()},stop:()=>{a&&(cancelAnimationFrame(a),a=null,r=af())}}}({callbacks:{publish:t.publishWhileDragging,collectionStarting:t.collectionStarting},registry:e}),i=t=>{r||ek();let i=r.critical.draggable;"ADDITION"===t.type&&aI(e,i,t.value)&&a.add(t.value),"REMOVAL"===t.type&&aI(e,i,t.value)&&a.remove(t.value)};return{updateDroppableIsEnabled:(a,i)=>{e.droppable.exists(a)||ek(),r&&t.updateDroppableIsEnabled({id:a,isEnabled:i})},updateDroppableIsCombineEnabled:(a,i)=>{r&&(e.droppable.exists(a)||ek(),t.updateDroppableIsCombineEnabled({id:a,isCombineEnabled:i}))},scrollDroppable:(t,a)=>{r&&e.droppable.getById(t).callbacks.scroll(a)},updateDroppableScroll:(a,i)=>{r&&(e.droppable.exists(a)||ek(),t.updateDroppableScroll({id:a,newScroll:i}))},startPublishing:t=>{r&&ek();let a=e.draggable.getById(t.draggableId),n=e.droppable.getById(a.descriptor.droppableId),s={draggable:a.descriptor,droppable:n.descriptor};return r={critical:s,unsubscribe:e.subscribe(i)},aw({critical:s,registry:e,scrollOptions:t.scrollOptions})},stopPublishing:()=>{if(!r)return;a.stop();let t=r.critical.droppable;e.droppable.getAllByType(t.type).forEach(e=>e.callbacks.dragStopped()),r.unsubscribe(),r=null}}},ak=(e,t)=>"IDLE"===e.phase||"DROP_ANIMATING"===e.phase&&e.completed.result.draggableId!==t&&"DROP"===e.completed.result.reason,aD=e=>{window.scrollBy(e.x,e.y)};let aA=eX(e=>e0(e).filter(e=>!!e.isEnabled&&!!e.frame)),aj=(e,t)=>aA(t).find(t=>(t.frame||ek(),t5(t.frame.pageMarginBox)(e)))||null;var aC=({center:e,destination:t,droppables:r})=>{if(t){let e=r[t];return e.frame?e:null}return aj(e,r)};let aS={startFromPercentage:.25,maxScrollAtPercentage:.05,maxPixelScroll:28,ease:e=>e**2,durationDampening:{stopDampeningAt:1200,accelerateAt:360},disabled:!1};var aE=(e,t,r=()=>aS)=>{let a=r(),i=e[t.size]*a.startFromPercentage;return{startScrollingFrom:i,maxScrollValueAt:e[t.size]*a.maxScrollAtPercentage}},aP=({startOfRange:e,endOfRange:t,current:r})=>{let a=t-e;return 0===a?0:(r-e)/a},aR=(e,t,r=()=>aS)=>{let a=r();if(e>t.startScrollingFrom)return 0;if(e<=t.maxScrollValueAt)return a.maxPixelScroll;if(e===t.startScrollingFrom)return 1;let i=aP({startOfRange:t.maxScrollValueAt,endOfRange:t.startScrollingFrom,current:e});return Math.ceil(a.maxPixelScroll*a.ease(1-i))},aT=(e,t,r)=>{let a=r(),i=a.durationDampening.accelerateAt,n=a.durationDampening.stopDampeningAt,s=Date.now()-t;if(s>=n)return e;if(s<i)return 1;let o=aP({startOfRange:i,endOfRange:n,current:s});return Math.ceil(e*a.ease(o))},aM=({distanceToEdge:e,thresholds:t,dragStartTime:r,shouldUseTimeDampening:a,getAutoScrollerOptions:i})=>{let n=aR(e,t,i);return 0===n?0:a?Math.max(aT(n,r,i),1):n},aO=({container:e,distanceToEdges:t,dragStartTime:r,axis:a,shouldUseTimeDampening:i,getAutoScrollerOptions:n})=>{let s=aE(e,a,n);return t[a.end]<t[a.start]?aM({distanceToEdge:t[a.end],thresholds:s,dragStartTime:r,shouldUseTimeDampening:i,getAutoScrollerOptions:n}):-1*aM({distanceToEdge:t[a.start],thresholds:s,dragStartTime:r,shouldUseTimeDampening:i,getAutoScrollerOptions:n})},aB=({container:e,subject:t,proposedScroll:r})=>{let a=t.height>e.height,i=t.width>e.width;return i||a?i&&a?null:{x:i?0:r.x,y:a?0:r.y}:r};let aL=ez(e=>0===e?0:e);var a_=({dragStartTime:e,container:t,subject:r,center:a,shouldUseTimeDampening:i,getAutoScrollerOptions:n})=>{let s={top:a.y-t.top,right:t.right-a.x,bottom:t.bottom-a.y,left:a.x-t.left},o=aO({container:t,distanceToEdges:s,dragStartTime:e,axis:ti,shouldUseTimeDampening:i,getAutoScrollerOptions:n}),l=aL({x:aO({container:t,distanceToEdges:s,dragStartTime:e,axis:tn,shouldUseTimeDampening:i,getAutoScrollerOptions:n}),y:o});if(eL(l,eM))return null;let d=aB({container:t,subject:r,proposedScroll:l});return d?eL(d,eM)?null:d:null};let aG=ez(e=>0===e?0:e>0?1:-1),a$=(()=>{let e=(e,t)=>e<0?e:e>t?e-t:0;return({current:t,max:r,change:a})=>{let i=eO(t,a),n={x:e(i.x,r.x),y:e(i.y,r.y)};return eL(n,eM)?null:n}})(),aq=({max:e,current:t,change:r})=>{let a={x:Math.max(t.x,e.x),y:Math.max(t.y,e.y)},i=aG(r),n=a$({max:a,current:t,change:i});return!n||0!==i.x&&0===n.x||0!==i.y&&0===n.y},az=(e,t)=>aq({current:e.scroll.current,max:e.scroll.max,change:t}),aW=(e,t)=>{if(!az(e,t))return null;let r=e.scroll.max;return a$({current:e.scroll.current,max:r,change:t})},aF=(e,t)=>{let r=e.frame;return!!r&&aq({current:r.scroll.current,max:r.scroll.max,change:t})},aU=(e,t)=>{let r=e.frame;return r&&aF(e,t)?a$({current:r.scroll.current,max:r.scroll.max,change:t}):null};var aV=({viewport:e,subject:t,center:r,dragStartTime:a,shouldUseTimeDampening:i,getAutoScrollerOptions:n})=>{let s=a_({dragStartTime:a,container:e.frame,subject:t,center:r,shouldUseTimeDampening:i,getAutoScrollerOptions:n});return s&&az(e,s)?s:null},aH=({droppable:e,subject:t,center:r,dragStartTime:a,shouldUseTimeDampening:i,getAutoScrollerOptions:n})=>{let s=e.frame;if(!s)return null;let o=a_({dragStartTime:a,container:s.pageMarginBox,subject:t,center:r,shouldUseTimeDampening:i,getAutoScrollerOptions:n});return o&&aF(e,o)?o:null},aJ=({state:e,dragStartTime:t,shouldUseTimeDampening:r,scrollWindow:a,scrollDroppable:i,getAutoScrollerOptions:n})=>{let s=e.current.page.borderBoxCenter,o=e.dimensions.draggables[e.critical.draggable.id].page.marginBox;if(e.isWindowScrollAllowed){let i=aV({dragStartTime:t,viewport:e.viewport,subject:o,center:s,shouldUseTimeDampening:r,getAutoScrollerOptions:n});if(i)return void a(i)}let l=aC({center:s,destination:tQ(e.impact),droppables:e.dimensions.droppables});if(!l)return;let d=aH({dragStartTime:t,droppable:l,subject:o,center:s,shouldUseTimeDampening:r,getAutoScrollerOptions:n});d&&i(l.descriptor.id,d)},aY=({scrollWindow:e,scrollDroppable:t,getAutoScrollerOptions:r=()=>aS})=>{let a=eg(e),i=eg(t),n=null,s=e=>{n||ek();let{shouldUseTimeDampening:t,dragStartTime:s}=n;aJ({state:e,scrollWindow:a,scrollDroppable:i,dragStartTime:s,shouldUseTimeDampening:t,getAutoScrollerOptions:r})};return{start:e=>{rc(),n&&ek();let t=Date.now(),a=!1,i=()=>{a=!0};aJ({state:e,dragStartTime:0,shouldUseTimeDampening:!1,scrollWindow:i,scrollDroppable:i,getAutoScrollerOptions:r}),n={dragStartTime:t,shouldUseTimeDampening:a},ru(),a&&s(e)},stop:()=>{n&&(a.cancel(),i.cancel(),n=null)},scroll:s}},aK=({move:e,scrollDroppable:t,scrollWindow:r})=>{let a=(t,r)=>{e({client:eO(t.current.client.selection,r)})},i=(e,r)=>{if(!aF(e,r))return r;let a=aU(e,r);if(!a)return t(e.descriptor.id,r),null;let i=eB(r,a);return t(e.descriptor.id,i),eB(r,i)},n=(e,t,a)=>{if(!e||!az(t,a))return a;let i=aW(t,a);if(!i)return r(a),null;let n=eB(a,i);return r(n),eB(a,n)};return e=>{let t=e.scrollJumpRequest;if(!t)return;let r=tQ(e.impact);r||ek();let s=i(e.dimensions.droppables[r],t);if(!s)return;let o=e.viewport,l=n(e.isWindowScrollAllowed,o,s);l&&a(e,l)}},aX=({scrollDroppable:e,scrollWindow:t,move:r,getAutoScrollerOptions:a})=>{let i=aY({scrollWindow:t,scrollDroppable:e,getAutoScrollerOptions:a}),n=aK({move:r,scrollWindow:t,scrollDroppable:e});return{scroll:e=>{if(!a().disabled&&"DRAGGING"===e.phase){if("FLUID"===e.movementMode)return void i.scroll(e);e.scrollJumpRequest&&n(e)}},start:i.start,stop:i.stop}};let aZ="data-rfd",aQ=(()=>{let e=`${aZ}-drag-handle`;return{base:e,draggableId:`${e}-draggable-id`,contextId:`${e}-context-id`}})(),a0=(()=>{let e=`${aZ}-draggable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),a1=(()=>{let e=`${aZ}-droppable`;return{base:e,contextId:`${e}-context-id`,id:`${e}-id`}})(),a2={contextId:`${aZ}-scroll-container-context-id`},a5=e=>t=>`[${t}="${e}"]`,a3=(e,t)=>e.map(e=>{let r=e.styles[t];return r?`${e.selector} { ${r} }`:""}).join(" ");var a4=e=>{let t=a5(e),r=(()=>{let e=`
      cursor: -webkit-grab;
      cursor: grab;
    `;return{selector:t(aQ.contextId),styles:{always:`
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: rgba(0,0,0,0);
          touch-action: manipulation;
        `,resting:e,dragging:"pointer-events: none;",dropAnimating:e}}})(),a=(()=>{let e=`
      transition: ${rJ.outOfTheWay};
    `;return{selector:t(a0.contextId),styles:{dragging:e,dropAnimating:e,userCancel:e}}})(),i=[a,r,{selector:t(a1.contextId),styles:{always:"overflow-anchor: none;"}},{selector:"body",styles:{dragging:`
        cursor: grabbing;
        cursor: -webkit-grabbing;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        overflow-anchor: none;
      `}}];return{always:a3(i,"always"),resting:a3(i,"resting"),dragging:a3(i,"dragging"),dropAnimating:a3(i,"dropAnimating"),userCancel:a3(i,"userCancel")}};let a8="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?f.useLayoutEffect:f.useEffect,a6=()=>{let e=document.querySelector("head");return e||ek(),e},a9=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.type="text/css",t};function a7(e,t){return Array.from(e.querySelectorAll(t))}var ie=e=>e&&e.ownerDocument&&e.ownerDocument.defaultView?e.ownerDocument.defaultView:window;function it(e){return e instanceof ie(e).HTMLElement}function ir(e,t){let r=a7(document,`[${aQ.contextId}="${e}"]`);if(!r.length)return null;let a=r.find(e=>e.getAttribute(aQ.draggableId)===t);return a&&it(a)?a:null}function ia(){let e={draggables:{},droppables:{}},t=[];function r(e){t.length&&t.forEach(t=>t(e))}function a(t){return e.draggables[t]||null}function i(t){return e.droppables[t]||null}return{draggable:{register:t=>{e.draggables[t.descriptor.id]=t,r({type:"ADDITION",value:t})},update:(t,r)=>{let a=e.draggables[r.descriptor.id];a&&a.uniqueId===t.uniqueId&&(delete e.draggables[r.descriptor.id],e.draggables[t.descriptor.id]=t)},unregister:t=>{let i=t.descriptor.id,n=a(i);n&&t.uniqueId===n.uniqueId&&(delete e.draggables[i],e.droppables[t.descriptor.droppableId]&&r({type:"REMOVAL",value:t}))},getById:function(e){let t=a(e);return t||ek(),t},findById:a,exists:e=>!!a(e),getAllByType:t=>Object.values(e.draggables).filter(e=>e.descriptor.type===t)},droppable:{register:t=>{e.droppables[t.descriptor.id]=t},unregister:t=>{let r=i(t.descriptor.id);r&&t.uniqueId===r.uniqueId&&delete e.droppables[t.descriptor.id]},getById:function(e){let t=i(e);return t||ek(),t},findById:i,exists:e=>!!i(e),getAllByType:t=>Object.values(e.droppables).filter(e=>e.descriptor.type===t)},subscribe:function(e){return t.push(e),function(){let r=t.indexOf(e);-1!==r&&t.splice(r,1)}},clean:function(){e.draggables={},e.droppables={},t.length=0}}}var ii=b().createContext(null),is=()=>{let e=document.body;return e||ek(),e};let io={position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0 0 0 0)","clip-path":"inset(100%)"},il=e=>`rfd-announcement-${e}`,id={separator:"::"};function ic(e,t=id){let r=b().useId();return eR(()=>`${e}${t.separator}${r}`,[t.separator,e,r])}var iu=b().createContext(null),ip={react:"^18.0.0 || ^19.0.0"};let ig=/(\d+)\.(\d+)\.(\d+)/,im=e=>{let t=ig.exec(e);null==t&&ek();let r=Number(t[1]);return{major:r,minor:Number(t[2]),patch:Number(t[3]),raw:e}},ih=(e,t)=>t.major>e.major||!(t.major<e.major)&&(t.minor>e.minor||!(t.minor<e.minor)&&t.patch>=e.patch);var ib=(e,t)=>{if(ih(im(e),im(t)))return},iv=e=>{let t=e.doctype;t&&(t.name.toLowerCase(),t.publicId)};function ix(e){}function iy(e,t){}function iw(e){let t=(0,f.useRef)(e);return(0,f.useEffect)(()=>{t.current=e}),t}function iI(e){return"IDLE"!==e.phase&&"DROP_ANIMATING"!==e.phase&&e.isDragging}let iN={13:!0,9:!0};var ik=e=>{iN[e.keyCode]&&e.preventDefault()};let iD=(()=>{let e="visibilitychange";return"undefined"==typeof document?e:[e,`ms${e}`,`webkit${e}`,`moz${e}`,`o${e}`].find(e=>`on${e}`in document)||e})(),iA={type:"IDLE"};function ij(){}let iC={34:!0,33:!0,36:!0,35:!0},iS={type:"IDLE"},iE=["input","button","textarea","select","option","optgroup","video","audio"];var iP=e=>ea(e.getBoundingClientRect()).center;let iR=(()=>{let e="matches";return"undefined"==typeof document?e:[e,"msMatchesSelector","webkitMatchesSelector"].find(e=>e in Element.prototype)||e})();function iT(e){e.preventDefault()}function iM({expected:e,phase:t,isLockActive:r,shouldWarn:a}){return!!r()&&e===t}function iO({lockAPI:e,store:t,registry:r,draggableId:a}){if(e.isClaimed())return!1;let i=r.draggable.findById(a);return!!i&&!!i.options.isEnabled&&!!ak(t.getState(),a)}let iB=[function(e){let t=(0,f.useRef)(iA),r=(0,f.useRef)(ew),a=eR(()=>({eventName:"mousedown",fn:function(t){if(t.defaultPrevented||0!==t.button||t.ctrlKey||t.metaKey||t.shiftKey||t.altKey)return;let a=e.findClosestDraggableId(t);if(!a)return;let i=e.tryGetLock(a,s,{sourceEvent:t});if(!i)return;t.preventDefault();let n={x:t.clientX,y:t.clientY};r.current(),d(i,n)}}),[e]),i=eR(()=>({eventName:"webkitmouseforcewillbegin",fn:t=>{if(t.defaultPrevented)return;let r=e.findClosestDraggableId(t);if(!r)return;let a=e.findOptionsForDraggable(r);a&&!a.shouldRespectForcePress&&e.canGetLock(r)&&t.preventDefault()}}),[e]),n=eT(function(){r.current=eI(window,[i,a],{passive:!1,capture:!0})},[i,a]),s=eT(()=>{"IDLE"!==t.current.type&&(t.current=iA,r.current(),n())},[n]),o=eT(()=>{let e=t.current;s(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[s]),l=eT(function(){r.current=eI(window,function({cancel:e,completed:t,getPhase:r,setPhase:a}){return[{eventName:"mousemove",fn:e=>{var t;let{button:i,clientX:n,clientY:s}=e;if(0!==i)return;let o={x:n,y:s},l=r();if("DRAGGING"===l.type){e.preventDefault(),l.actions.move(o);return}"PENDING"!==l.type&&ek(),t=l.point,(Math.abs(o.x-t.x)>=5||Math.abs(o.y-t.y)>=5)&&(e.preventDefault(),a({type:"DRAGGING",actions:l.actions.fluidLift(o)}))}},{eventName:"mouseup",fn:a=>{let i=r();if("DRAGGING"!==i.type)return void e();a.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"mousedown",fn:t=>{"DRAGGING"===r().type&&t.preventDefault(),e()}},{eventName:"keydown",fn:t=>{if("PENDING"===r().type)return void e();if(27===t.keyCode){t.preventDefault(),e();return}ik(t)}},{eventName:"resize",fn:e},{eventName:"scroll",options:{passive:!0,capture:!1},fn:()=>{"PENDING"===r().type&&e()}},{eventName:"webkitmouseforcedown",fn:t=>{let a=r();if("IDLE"===a.type&&ek(),a.actions.shouldRespectForcePress())return void e();t.preventDefault()}},{eventName:iD,fn:e}]}({cancel:o,completed:s,getPhase:()=>t.current,setPhase:e=>{t.current=e}}),{capture:!0,passive:!1})},[o,s]),d=eT(function(e,r){"IDLE"!==t.current.type&&ek(),t.current={type:"PENDING",point:r,actions:e},l()},[l]);a8(function(){return n(),function(){r.current()}},[n])},function(e){let t=(0,f.useRef)(ij),r=eR(()=>({eventName:"keydown",fn:function(r){if(r.defaultPrevented||32!==r.keyCode)return;let i=e.findClosestDraggableId(r);if(!i)return;let n=e.tryGetLock(i,l,{sourceEvent:r});if(!n)return;r.preventDefault();let s=!0,o=n.snapLift();function l(){s||ek(),s=!1,t.current(),a()}t.current(),t.current=eI(window,function(e,t){function r(){t(),e.cancel()}return[{eventName:"keydown",fn:a=>{if(27===a.keyCode){a.preventDefault(),r();return}if(32===a.keyCode){a.preventDefault(),t(),e.drop();return}if(40===a.keyCode){a.preventDefault(),e.moveDown();return}if(38===a.keyCode){a.preventDefault(),e.moveUp();return}if(39===a.keyCode){a.preventDefault(),e.moveRight();return}if(37===a.keyCode){a.preventDefault(),e.moveLeft();return}if(iC[a.keyCode])return void a.preventDefault();ik(a)}},{eventName:"mousedown",fn:r},{eventName:"mouseup",fn:r},{eventName:"click",fn:r},{eventName:"touchstart",fn:r},{eventName:"resize",fn:r},{eventName:"wheel",fn:r,options:{passive:!0}},{eventName:iD,fn:r}]}(o,l),{capture:!0,passive:!1})}}),[e]),a=eT(function(){t.current=eI(window,[r],{passive:!1,capture:!0})},[r]);a8(function(){return a(),function(){t.current()}},[a])},function(e){let t=(0,f.useRef)(iS),r=(0,f.useRef)(ew),a=eT(function(){return t.current},[]),i=eT(function(e){t.current=e},[]),n=eR(()=>({eventName:"touchstart",fn:function(t){if(t.defaultPrevented)return;let a=e.findClosestDraggableId(t);if(!a)return;let i=e.tryGetLock(a,o,{sourceEvent:t});if(!i)return;let{clientX:n,clientY:s}=t.touches[0];r.current(),u(i,{x:n,y:s})}}),[e]),s=eT(function(){r.current=eI(window,[n],{capture:!0,passive:!1})},[n]),o=eT(()=>{let e=t.current;"IDLE"!==e.type&&("PENDING"===e.type&&clearTimeout(e.longPressTimerId),i(iS),r.current(),s())},[s,i]),l=eT(()=>{let e=t.current;o(),"DRAGGING"===e.type&&e.actions.cancel({shouldBlockNextClick:!0}),"PENDING"===e.type&&e.actions.abort()},[o]),d=eT(function(){let e={capture:!0,passive:!1},t={cancel:l,completed:o,getPhase:a},i=eI(window,function({cancel:e,completed:t,getPhase:r}){return[{eventName:"touchmove",options:{capture:!1},fn:t=>{let a=r();if("DRAGGING"!==a.type)return void e();a.hasMoved=!0;let{clientX:i,clientY:n}=t.touches[0];t.preventDefault(),a.actions.move({x:i,y:n})}},{eventName:"touchend",fn:a=>{let i=r();if("DRAGGING"!==i.type)return void e();a.preventDefault(),i.actions.drop({shouldBlockNextClick:!0}),t()}},{eventName:"touchcancel",fn:t=>{if("DRAGGING"!==r().type)return void e();t.preventDefault(),e()}},{eventName:"touchforcechange",fn:t=>{let a=r();"IDLE"===a.type&&ek();let i=t.touches[0];if(!i||!(i.force>=.15))return;let n=a.actions.shouldRespectForcePress();if("PENDING"===a.type){n&&e();return}if(n)return a.hasMoved?void t.preventDefault():void e();t.preventDefault()}},{eventName:iD,fn:e}]}(t),e),n=eI(window,function({cancel:e,getPhase:t}){return[{eventName:"orientationchange",fn:e},{eventName:"resize",fn:e},{eventName:"contextmenu",fn:e=>{e.preventDefault()}},{eventName:"keydown",fn:r=>{if("DRAGGING"!==t().type)return void e();27===r.keyCode&&r.preventDefault(),e()}},{eventName:iD,fn:e}]}(t),e);r.current=function(){i(),n()}},[l,a,o]),c=eT(function(){let e=a();"PENDING"!==e.type&&ek(),i({type:"DRAGGING",actions:e.actions.fluidLift(e.point),hasMoved:!1})},[a,i]),u=eT(function(e,t){"IDLE"!==a().type&&ek(),i({type:"PENDING",point:t,actions:e,longPressTimerId:setTimeout(c,120)}),d()},[d,a,i,c]);a8(function(){return s(),function(){r.current();let e=a();"PENDING"===e.type&&(clearTimeout(e.longPressTimerId),i(iS))}},[a,s,i]),a8(function(){return eI(window,[{eventName:"touchmove",fn:()=>{},options:{capture:!1,passive:!1}}])},[])}],iL=e=>({onBeforeCapture:t=>{(0,Z.flushSync)(()=>{e.onBeforeCapture&&e.onBeforeCapture(t)})},onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragEnd:e.onDragEnd,onDragUpdate:e.onDragUpdate}),i_=e=>({...aS,...e.autoScrollerOptions,durationDampening:{...aS.durationDampening,...e.autoScrollerOptions}});function iG(e){return e.current||ek(),e.current}function i$(e){let{contextId:t,setCallbacks:r,sensors:a,nonce:i,dragHandleUsageInstructions:n}=e,s=(0,f.useRef)(null);iy(()=>{ib(ip.react,b().version),iv(document)},[]);let o=iw(e),l=eT(()=>iL(o.current),[o]),d=eT(()=>i_(o.current),[o]),c=function(e){let t=eR(()=>il(e),[e]),r=(0,f.useRef)(null);return(0,f.useEffect)(function(){let e=document.createElement("div");return r.current=e,e.id=t,e.setAttribute("aria-live","assertive"),e.setAttribute("aria-atomic","true"),em(e.style,io),is().appendChild(e),function(){setTimeout(function(){let t=is();t.contains(e)&&t.removeChild(e),e===r.current&&(r.current=null)})}},[t]),eT(e=>{let t=r.current;if(t){t.textContent=e;return}},[])}(t),u=function({contextId:e,text:t}){let r=ic("hidden-text",{separator:"-"}),a=eR(()=>(function({contextId:e,uniqueId:t}){return`rfd-hidden-text-${e}-${t}`})({contextId:e,uniqueId:r}),[r,e]);return(0,f.useEffect)(function(){let e=document.createElement("div");return e.id=a,e.textContent=t,e.style.display="none",is().appendChild(e),function(){let t=is();t.contains(e)&&t.removeChild(e)}},[a,t]),a}({contextId:t,text:n}),p=function(e,t){let r=eR(()=>a4(e),[e]),a=(0,f.useRef)(null),i=(0,f.useRef)(null),n=eT(eX(e=>{let t=i.current;t||ek(),t.textContent=e}),[]),s=eT(e=>{let t=a.current;t||ek(),t.textContent=e},[]);a8(()=>{(a.current||i.current)&&ek();let o=a9(t),l=a9(t);return a.current=o,i.current=l,o.setAttribute(`${aZ}-always`,e),l.setAttribute(`${aZ}-dynamic`,e),a6().appendChild(o),a6().appendChild(l),s(r.always),n(r.resting),()=>{let e=e=>{let t=e.current;t||ek(),a6().removeChild(t),e.current=null};e(a),e(i)}},[t,s,n,r.always,r.resting,e]);let o=eT(()=>n(r.dragging),[n,r.dragging]),l=eT(e=>{if("DROP"===e)return void n(r.dropAnimating);n(r.userCancel)},[n,r.dropAnimating,r.userCancel]),d=eT(()=>{i.current&&n(r.resting)},[n,r.resting]);return eR(()=>({dragging:o,dropping:l,resting:d}),[o,l,d])}(t,i),g=eT(e=>{iG(s).dispatch(e)},[]),m=eR(()=>(0,ee.zH)({publishWhileDragging:rD,updateDroppableScroll:rj,updateDroppableIsEnabled:rC,updateDroppableIsCombineEnabled:rS,collectionStarting:rA},g),[g]),h=function(){let e=eR(ia,[]);return(0,f.useEffect)(()=>function(){e.clean()},[e]),e}(),v=eR(()=>aN(h,m),[h,m]),x=eR(()=>aX({scrollWindow:aD,scrollDroppable:v.scrollDroppable,getAutoScrollerOptions:d,...(0,ee.zH)({move:rE},g)}),[v.scrollDroppable,g,d]),y=function(e){let t=(0,f.useRef)({}),r=(0,f.useRef)(null),a=(0,f.useRef)(null),i=(0,f.useRef)(!1),n=eT(function(e,r){let a={id:e,focus:r};return t.current[e]=a,function(){let r=t.current;r[e]!==a&&delete r[e]}},[]),s=eT(function(t){let r=ir(e,t);r&&r!==document.activeElement&&r.focus()},[e]),o=eT(function(e,t){r.current===e&&(r.current=t)},[]),l=eT(function(){!a.current&&i.current&&(a.current=requestAnimationFrame(()=>{a.current=null;let e=r.current;e&&s(e)}))},[s]),d=eT(function(e){r.current=null;let t=document.activeElement;t&&t.getAttribute(aQ.draggableId)===e&&(r.current=e)},[]);return a8(()=>(i.current=!0,function(){i.current=!1;let e=a.current;e&&cancelAnimationFrame(e)}),[]),eR(()=>({register:n,tryRecordFocus:d,tryRestoreFocusRecorded:l,tryShiftRecord:o}),[n,d,l,o])}(t),w=eR(()=>ah({announce:c,autoScroller:x,dimensionMarshal:v,focusMarshal:y,getResponders:l,styleMarshal:p}),[c,x,v,y,l,p]);s.current=w;let I=eT(()=>{let e=iG(s);"IDLE"!==e.getState().phase&&e.dispatch(rB())},[]),N=eT(()=>{let e=iG(s).getState();return"DROP_ANIMATING"===e.phase||"IDLE"!==e.phase&&e.isDragging},[]);r(eR(()=>({isDragging:N,tryAbort:I}),[N,I]));let k=eT(e=>ak(iG(s).getState(),e),[]),D=eT(()=>t2(iG(s).getState()),[]),A=eR(()=>({marshal:v,focus:y,contextId:t,canLift:k,isMovementAllowed:D,dragHandleUsageInstructionsId:u,registry:h}),[t,v,u,y,k,D,h]);return!function({contextId:e,store:t,registry:r,customSensors:a,enableDefaultSensors:i}){let n=[...i?iB:[],...a||[]],s=(0,f.useState)(()=>(function(){let e=null;function t(){e||ek(),e=null}return{isClaimed:function(){return!!e},isActive:function(t){return t===e},claim:function(t){e&&ek();let r={abandon:t};return e=r,r},release:t,tryAbandon:function(){e&&(e.abandon(),t())}}})())[0],o=eT(function(e,t){iI(e)&&!iI(t)&&s.tryAbandon()},[s]);a8(function(){let e=t.getState();return t.subscribe(()=>{let r=t.getState();o(e,r),e=r})},[s,t,o]),a8(()=>s.tryAbandon,[s.tryAbandon]);let l=eT(e=>iO({lockAPI:s,registry:r,store:t,draggableId:e}),[s,r,t]),d=eT((a,i,n)=>(function({lockAPI:e,contextId:t,store:r,registry:a,draggableId:i,forceSensorStop:n,sourceEvent:s}){if(!iO({lockAPI:e,store:r,registry:a,draggableId:i}))return null;let o=a.draggable.getById(i),l=function(e,t){let r=a7(document,`[${a0.contextId}="${e}"]`).find(e=>e.getAttribute(a0.id)===t);return r&&it(r)?r:null}(t,o.descriptor.id);if(!l||s&&!o.options.canDragInteractiveElements&&function(e,t){let r=t.target;return!!it(r)&&function e(t,r){if(null==r)return!1;if(iE.includes(r.tagName.toLowerCase()))return!0;let a=r.getAttribute("contenteditable");return"true"===a||""===a||r!==t&&e(t,r.parentElement)}(e,r)}(l,s))return null;let d=e.claim(n||ew),c="PRE_DRAG";function u(){return o.options.shouldRespectForcePress}function p(){return e.isActive(d)}let g=(function(e,t){iM({expected:e,phase:c,isLockActive:p,shouldWarn:!0})&&r.dispatch(t())}).bind(null,"DRAGGING");function m(t){function a(){e.release(),c="COMPLETED"}function i(e,n={shouldBlockNextClick:!1}){t.cleanup(),n.shouldBlockNextClick&&setTimeout(eI(window,[{eventName:"click",fn:iT,options:{once:!0,passive:!1,capture:!0}}])),a(),r.dispatch(rG({reason:e}))}return"PRE_DRAG"!==c&&(a(),ek()),r.dispatch(rN(t.liftActionArgs)),c="DRAGGING",{isActive:()=>iM({expected:"DRAGGING",phase:c,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:u,drop:e=>i("DROP",e),cancel:e=>i("CANCEL",e),...t.actions}}return{isActive:()=>iM({expected:"PRE_DRAG",phase:c,isLockActive:p,shouldWarn:!1}),shouldRespectForcePress:u,fluidLift:function(e){let t=eg(e=>{g(()=>rE({client:e}))});return{...m({liftActionArgs:{id:i,clientSelection:e,movementMode:"FLUID"},cleanup:()=>t.cancel(),actions:{move:t}}),move:t}},snapLift:function(){return m({liftActionArgs:{id:i,clientSelection:iP(l),movementMode:"SNAP"},cleanup:ew,actions:{moveUp:()=>g(rR),moveRight:()=>g(rM),moveDown:()=>g(rT),moveLeft:()=>g(rO)}})},abort:function(){iM({expected:"PRE_DRAG",phase:c,isLockActive:p,shouldWarn:!0})&&e.release()}}})({lockAPI:s,registry:r,contextId:e,store:t,draggableId:a,forceSensorStop:i||null,sourceEvent:n&&n.sourceEvent?n.sourceEvent:null}),[e,s,r,t]),c=eT(t=>(function(e,t){let r=function(e,t){let r=t.target;if(!(r instanceof ie(r).Element))return null;let a=`[${aQ.contextId}="${e}"]`,i=r.closest?r.closest(a):function e(t,r){return null==t?null:t[iR](r)?t:e(t.parentElement,r)}(r,a);return i&&it(i)?i:null}(e,t);return r?r.getAttribute(aQ.draggableId):null})(e,t),[e]),u=eT(e=>{let t=r.draggable.findById(e);return t?t.options:null},[r.draggable]),p=eT(function(){s.isClaimed()&&(s.tryAbandon(),"IDLE"!==t.getState().phase&&t.dispatch(rB()))},[s,t]),g=eT(()=>s.isClaimed(),[s]),m=eR(()=>({canGetLock:l,tryGetLock:d,findClosestDraggableId:c,findOptionsForDraggable:u,tryReleaseLock:p,isLockClaimed:g}),[l,d,c,u,p,g]);for(let e=0;e<n.length;e++)n[e](m)}({contextId:t,store:w,registry:h,customSensors:a||null,enableDefaultSensors:!1!==e.enableDefaultSensors}),(0,f.useEffect)(()=>I,[I]),b().createElement(iu.Provider,{value:A},b().createElement(et.Kq,{context:ii,store:w},e.children))}function iq(e){let t=b().useId(),r=e.dragHandleUsageInstructions||eE.dragHandleUsageInstructions;return b().createElement(eD,null,a=>b().createElement(i$,{nonce:e.nonce,contextId:t,setCallbacks:a,dragHandleUsageInstructions:r,enableDefaultSensors:e.enableDefaultSensors,sensors:e.sensors,onBeforeCapture:e.onBeforeCapture,onBeforeDragStart:e.onBeforeDragStart,onDragStart:e.onDragStart,onDragUpdate:e.onDragUpdate,onDragEnd:e.onDragEnd,autoScrollerOptions:e.autoScrollerOptions},e.children))}let iz={dragging:5e3,dropAnimating:4500},iW=(e,t)=>t?rJ.drop(t.duration):e?rJ.snap:rJ.fluid,iF=(e,t)=>{if(e)return t?rU.opacity.drop:rU.opacity.combining},iU=e=>null!=e.forceShouldAnimate?e.forceShouldAnimate:"SNAP"===e.mode;var iV=b().createContext(null);function iH(e){e&&it(e)||ek()}function iJ(e){let t=(0,f.useContext)(e);return t||ek(),t}function iY(e){e.preventDefault()}var iK=(e,t)=>e===t,iX=e=>{let{combine:t,destination:r}=e;return r?r.droppableId:t?t.droppableId:null};let iZ=e=>e.combine?e.combine.draggableId:null,iQ=e=>e.at&&"COMBINE"===e.at.type?e.at.combine.draggableId:null;function i0(e=null){return{isDragging:!1,isDropAnimating:!1,isClone:!1,dropAnimation:null,mode:null,draggingOver:null,combineTargetFor:e,combineWith:null}}let i1={mapped:{type:"SECONDARY",offset:eM,combineTargetFor:null,shouldAnimateDisplacement:!0,snapshot:i0(null)}},i2=(0,et.Ng)(()=>{let e=function(){let e=eX((e,t)=>({x:e,y:t})),t=eX((e,t,r=null,a=null,i=null)=>({isDragging:!0,isClone:t,isDropAnimating:!!i,dropAnimation:i,mode:e,draggingOver:r,combineWith:a,combineTargetFor:null})),r=eX((e,r,a,i,n=null,s=null,o=null)=>({mapped:{type:"DRAGGING",dropping:null,draggingOver:n,combineWith:s,mode:r,offset:e,dimension:a,forceShouldAnimate:o,snapshot:t(r,i,n,s,null)}}));return(a,i)=>{if(iI(a)){if(a.critical.draggable.id!==i.draggableId)return null;let t=a.current.client.offset,n=a.dimensions.draggables[i.draggableId],s=tQ(a.impact),o=iQ(a.impact),l=a.forceShouldAnimate;return r(e(t.x,t.y),a.movementMode,n,i.isClone,s,o,l)}if("DROP_ANIMATING"===a.phase){let e=a.completed;if(e.result.draggableId!==i.draggableId)return null;let r=i.isClone,n=a.dimensions.draggables[i.draggableId],s=e.result,o=s.mode,l=iX(s),d=iZ(s),c={duration:a.dropDuration,curve:rF.drop,moveTo:a.newHomeClientOffset,opacity:d?rU.opacity.drop:null,scale:d?rU.scale.drop:null};return{mapped:{type:"DRAGGING",offset:a.newHomeClientOffset,dimension:n,dropping:c,draggingOver:l,combineWith:d,mode:o,forceShouldAnimate:null,snapshot:t(o,r,l,d,c)}}}return null}}(),t=function(){let e=eX((e,t)=>({x:e,y:t})),t=eX(i0),r=eX((e,r=null,a)=>({mapped:{type:"SECONDARY",offset:e,combineTargetFor:r,shouldAnimateDisplacement:a,snapshot:t(r)}})),a=e=>e?r(eM,e,!0):null,i=(t,i,n,s)=>{let o=n.displaced.visible[t],l=!!(s.inVirtualList&&s.effected[t]),d=e3(n),c=d&&d.draggableId===t?i:null;if(!o){if(!l)return a(c);if(n.displaced.invisible[t])return null;let i=e_(s.displacedBy.point);return r(e(i.x,i.y),c,!0)}if(l)return a(c);let u=n.displacedBy.point;return r(e(u.x,u.y),c,o.shouldAnimate)};return(e,t)=>{if(iI(e))return e.critical.draggable.id===t.draggableId?null:i(t.draggableId,e.critical.draggable.id,e.impact,e.afterCritical);if("DROP_ANIMATING"===e.phase){let r=e.completed;return r.result.draggableId===t.draggableId?null:i(t.draggableId,r.result.draggableId,r.impact,r.afterCritical)}return null}}();return(r,a)=>e(r,a)||t(r,a)||i1},{dropAnimationFinished:rq},null,{context:ii,areStatePropsEqual:iK})(e=>{let t=(0,f.useRef)(null),r=eT((e=null)=>{t.current=e},[]),a=eT(()=>t.current,[]),{contextId:i,dragHandleUsageInstructionsId:n,registry:s}=iJ(iu),{type:o,droppableId:l}=iJ(iV),d=eR(()=>({id:e.draggableId,index:e.index,type:o,droppableId:l}),[e.draggableId,e.index,o,l]),{children:c,draggableId:u,isEnabled:p,shouldRespectForcePress:g,canDragInteractiveElements:m,isClone:h,mapped:v,dropAnimationFinished:x}=e;!function(e,t,r){iy(()=>{let a=e.draggableId;a||ek(!1),"string"!=typeof a&&ek(!1),Number.isInteger(e.index)||ek(!1),"DRAGGING"!==e.mapped.type&&(iH(r()),e.isEnabled&&(ir(t,a)||ek(!1)))})}(e,i,a),h||function(e){let t=ic("draggable"),{descriptor:r,registry:a,getDraggableRef:i,canDragInteractiveElements:n,shouldRespectForcePress:s,isEnabled:o}=e,l=eR(()=>({canDragInteractiveElements:n,shouldRespectForcePress:s,isEnabled:o}),[n,o,s]),d=eT(e=>{let t=i();return t||ek(),function(e,t,r=eM){let a=window.getComputedStyle(t),i=eu(t.getBoundingClientRect(),a),n=ec(i,r),s={client:i,tagName:t.tagName.toLowerCase(),display:a.display};return{descriptor:e,placeholder:s,displaceBy:{x:i.marginBox.width,y:i.marginBox.height},client:i,page:n}}(r,t,e)},[r,i]),c=eR(()=>({uniqueId:t,descriptor:r,options:l,getDimension:d}),[r,d,l,t]),u=(0,f.useRef)(c),p=(0,f.useRef)(!0);a8(()=>(a.draggable.register(u.current),()=>a.draggable.unregister(u.current)),[a.draggable]),a8(()=>{if(p.current){p.current=!1;return}let e=u.current;u.current=c,a.draggable.update(c,e)},[c,a.draggable])}(eR(()=>({descriptor:d,registry:s,getDraggableRef:a,canDragInteractiveElements:m,shouldRespectForcePress:g,isEnabled:p}),[d,s,a,m,g,p]));let y=eR(()=>p?{tabIndex:0,role:"button","aria-describedby":n,"data-rfd-drag-handle-draggable-id":u,"data-rfd-drag-handle-context-id":i,draggable:!1,onDragStart:iY}:null,[i,n,u,p]),w=eT(e=>{"DRAGGING"===v.type&&v.dropping&&"transform"===e.propertyName&&(0,Z.flushSync)(x)},[x,v]),I=eR(()=>{let e=function(e){return"DRAGGING"===e.type?function(e){let t=e.dimension.client,{offset:r,combineWith:a,dropping:i}=e,n=!!a,s=iU(e),o=!!i,l=o?rK.drop(r,n):rK.moveTo(r);return{position:"fixed",top:t.marginBox.top,left:t.marginBox.left,boxSizing:"border-box",width:t.borderBox.width,height:t.borderBox.height,transition:iW(s,i),transform:l,opacity:iF(n,o),zIndex:o?iz.dropAnimating:iz.dragging,pointerEvents:"none"}}(e):{transform:rK.moveTo(e.offset),transition:e.shouldAnimateDisplacement?void 0:"none"}}(v);return{innerRef:r,draggableProps:{"data-rfd-draggable-context-id":i,"data-rfd-draggable-id":u,style:e,onTransitionEnd:"DRAGGING"===v.type&&v.dropping?w:void 0},dragHandleProps:y}},[i,y,u,v,w,r]),N=eR(()=>({draggableId:d.id,type:d.type,source:{index:d.index,droppableId:d.droppableId}}),[d.droppableId,d.id,d.index,d.type]);return b().createElement(b().Fragment,null,c(I,v.snapshot,N))});function i5(e){return iJ(iV).isUsingCloneFor!==e.draggableId||e.isClone?b().createElement(i2,e):null}function i3(e){let t="boolean"!=typeof e.isDragDisabled||!e.isDragDisabled,r=!!e.disableInteractiveElementBlocking,a=!!e.shouldRespectForcePress;return b().createElement(i5,em({},e,{isClone:!1,isEnabled:t,canDragInteractiveElements:r,shouldRespectForcePress:a}))}let i4=e=>t=>e===t,i8=i4("scroll"),i6=i4("auto");i4("visible");let i9=(e,t)=>t(e.overflowX)||t(e.overflowY),i7=e=>{let t=window.getComputedStyle(e),r={overflowX:t.overflowX,overflowY:t.overflowY};return i9(r,i8)||i9(r,i6)},ne=()=>!1,nt=e=>null==e?null:e===document.body?ne()?e:null:e===document.documentElement?null:i7(e)?e:nt(e.parentElement);var nr=e=>({x:e.scrollLeft,y:e.scrollTop});let na=e=>!!e&&("fixed"===window.getComputedStyle(e).position||na(e.parentElement));var ni=e=>({closestScrollable:nt(e),isFixedOnPage:na(e)}),nn=({descriptor:e,isEnabled:t,isCombineEnabled:r,isFixedOnPage:a,direction:i,client:n,page:s,closest:o})=>{let l=(()=>{if(!o)return null;let{scrollSize:e,client:t}=o,r=ab({scrollHeight:e.scrollHeight,scrollWidth:e.scrollWidth,height:t.paddingBox.height,width:t.paddingBox.width});return{pageMarginBox:o.page.marginBox,frameClient:t,scrollSize:e,shouldClipSubject:o.shouldClipSubject,scroll:{initial:o.scroll,current:o.scroll,max:r,diff:{value:eM,displacement:eM}}}})(),d="vertical"===i?ti:tn,c=eY({page:s,withPlaceholder:null,axis:d,frame:l});return{descriptor:e,isCombineEnabled:r,isFixedOnPage:a,axis:d,isEnabled:t,client:n,page:s,frame:l,subject:c}};let ns=(e,t)=>{let r=ep(e);if(!t||e!==t)return r;let a=r.paddingBox.top-t.scrollTop,i=r.paddingBox.left-t.scrollLeft,n=a+t.scrollHeight;return eo({borderBox:ei({top:a,right:i+t.scrollWidth,bottom:n,left:i},r.border),margin:r.margin,border:r.border,padding:r.padding})};var no=({ref:e,descriptor:t,env:r,windowScroll:a,direction:i,isDropDisabled:n,isCombineEnabled:s,shouldClipSubject:o})=>{let l=r.closestScrollable,d=ns(e,l),c=ec(d,a),u=(()=>{if(!l)return null;let e=ep(l),t={scrollHeight:l.scrollHeight,scrollWidth:l.scrollWidth};return{client:e,page:ec(e,a),scroll:nr(l),scrollSize:t,shouldClipSubject:o}})();return nn({descriptor:t,isEnabled:!n,isCombineEnabled:s,isFixedOnPage:r.isFixedOnPage,direction:i,client:d,page:c,closest:u})};let nl={passive:!1},nd={passive:!0};var nc=e=>e.shouldPublishImmediately?nl:nd;let nu=e=>e&&e.env.closestScrollable||null;function np(){}let ng={width:0,height:0,margin:{top:0,right:0,bottom:0,left:0}},nm=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>e||"close"===r?ng:{height:t.client.borderBox.height,width:t.client.borderBox.width,margin:t.client.margin},nh=({isAnimatingOpenOnMount:e,placeholder:t,animate:r})=>{let a=nm({isAnimatingOpenOnMount:e,placeholder:t,animate:r});return{display:t.display,boxSizing:"border-box",width:a.width,height:a.height,marginTop:a.margin.top,marginRight:a.margin.right,marginBottom:a.margin.bottom,marginLeft:a.margin.left,flexShrink:"0",flexGrow:"0",pointerEvents:"none",transition:"none"!==r?rJ.placeholder:null}};var nf=b().memo(e=>{let t=(0,f.useRef)(null),r=eT(()=>{t.current&&(clearTimeout(t.current),t.current=null)},[]),{animate:a,onTransitionEnd:i,onClose:n,contextId:s}=e,[o,l]=(0,f.useState)("open"===e.animate);(0,f.useEffect)(()=>o?"open"!==a?(r(),l(!1),np):t.current?np:(t.current=setTimeout(()=>{t.current=null,l(!1)}),r):np,[a,o,r]);let d=eT(e=>{"height"===e.propertyName&&(i(),"close"===a&&n())},[a,n,i]),c=nh({isAnimatingOpenOnMount:o,animate:e.animate,placeholder:e.placeholder});return b().createElement(e.placeholder.tagName,{style:c,"data-rfd-placeholder-context-id":s,onTransitionEnd:d,ref:e.innerRef})});function nb(e){return"boolean"==typeof e}function nv(e,t){t.forEach(t=>t(e))}let nx=[function({props:e}){e.droppableId||ek(),"string"!=typeof e.droppableId&&ek()},function({props:e}){nb(e.isDropDisabled)||ek(),nb(e.isCombineEnabled)||ek(),nb(e.ignoreContainerClipping)||ek()},function({getDroppableRef:e}){iH(e())}],ny=[function({props:e,getPlaceholderRef:t}){if(!e.placeholder||t())return}],nw=[function({props:e}){e.renderClone||ek()},function({getPlaceholderRef:e}){e()&&ek()}];class nI extends b().PureComponent{constructor(...e){super(...e),this.state={isVisible:!!this.props.on,data:this.props.on,animate:this.props.shouldAnimate&&this.props.on?"open":"none"},this.onClose=()=>{"close"===this.state.animate&&this.setState({isVisible:!1})}}static getDerivedStateFromProps(e,t){return e.shouldAnimate?e.on?{isVisible:!0,data:e.on,animate:"open"}:t.isVisible?{isVisible:!0,data:t.data,animate:"close"}:{isVisible:!1,animate:"close",data:null}:{isVisible:!!e.on,data:e.on,animate:"none"}}render(){if(!this.state.isVisible)return null;let e={onClose:this.onClose,data:this.state.data,animate:this.state.animate};return this.props.children(e)}}let nN={mode:"standard",type:"DEFAULT",direction:"vertical",isDropDisabled:!1,isCombineEnabled:!1,ignoreContainerClipping:!1,renderClone:null,getContainerForClone:function(){return document.body||ek(),document.body}},nk=e=>{let t,r={...e};for(t in nN)void 0===e[t]&&(r={...r,[t]:nN[t]});return r},nD=(e,t)=>e===t.droppable.type,nA=(e,t)=>t.draggables[e.draggable.id],nj=(0,et.Ng)(()=>{let e={placeholder:null,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:!1,draggingOverWith:null,draggingFromThisWith:null,isUsingPlaceholder:!1},useClone:null},t={...e,shouldAnimatePlaceholder:!1},r=eX(e=>({draggableId:e.id,type:e.type,source:{index:e.index,droppableId:e.droppableId}})),a=eX((a,i,n,s,o,l)=>{let d=o.descriptor.id;if(o.descriptor.droppableId===a){let e=l?{render:l,dragging:r(o.descriptor)}:null;return{placeholder:o.placeholder,shouldAnimatePlaceholder:!1,snapshot:{isDraggingOver:n,draggingOverWith:n?d:null,draggingFromThisWith:d,isUsingPlaceholder:!0},useClone:e}}return i?s?{placeholder:o.placeholder,shouldAnimatePlaceholder:!0,snapshot:{isDraggingOver:n,draggingOverWith:d,draggingFromThisWith:null,isUsingPlaceholder:!0},useClone:null}:e:t});return(r,i)=>{let n=nk(i),s=n.droppableId,o=n.type,l=!n.isDropDisabled,d=n.renderClone;if(iI(r)){let e=r.critical;if(!nD(o,e))return t;let i=nA(e,r.dimensions),n=tQ(r.impact)===s;return a(s,l,n,n,i,d)}if("DROP_ANIMATING"===r.phase){let e=r.completed;if(!nD(o,e.critical))return t;let i=nA(e.critical,r.dimensions);return a(s,l,iX(e.result)===s,tQ(e.impact)===s,i,d)}if("IDLE"===r.phase&&r.completed&&!r.shouldFlush){let a=r.completed;if(!nD(o,a.critical))return t;let i=tQ(a.impact)===s,n=!!(a.impact.at&&"COMBINE"===a.impact.at.type),l=a.critical.droppable.id===s;if(i)return n?e:t;if(l)return e}return t}},{updateViewportMaxScroll:e=>({type:"UPDATE_VIEWPORT_MAX_SCROLL",payload:e})},(e,t,r)=>({...nk(r),...e,...t}),{context:ii,areStatePropsEqual:iK})(e=>{let t=(0,f.useContext)(iu);t||ek();let{contextId:r,isMovementAllowed:a}=t,i=(0,f.useRef)(null),n=(0,f.useRef)(null),{children:s,droppableId:o,type:l,mode:d,direction:c,ignoreContainerClipping:u,isDropDisabled:p,isCombineEnabled:g,snapshot:m,useClone:h,updateViewportMaxScroll:v,getContainerForClone:x}=e,y=eT(()=>i.current,[]),w=eT((e=null)=>{i.current=e},[]),I=eT(()=>n.current,[]),N=eT((e=null)=>{n.current=e},[]);!function(e){iy(()=>{nv(e,nx),"standard"===e.props.mode&&nv(e,ny),"virtual"===e.props.mode&&nv(e,nw)})}({props:e,getDroppableRef:y,getPlaceholderRef:I});let k=eT(()=>{a()&&v({maxScroll:ax()})},[a,v]);!function(e){let t=(0,f.useRef)(null),r=iJ(iu),a=ic("droppable"),{registry:i,marshal:n}=r,s=iw(e),o=eR(()=>({id:e.droppableId,type:e.type,mode:e.mode}),[e.droppableId,e.mode,e.type]),l=(0,f.useRef)(o),d=eR(()=>eX((e,r)=>{t.current||ek(),n.updateDroppableScroll(o.id,{x:e,y:r})}),[o.id,n]),c=eT(()=>{let e=t.current;return e&&e.env.closestScrollable?nr(e.env.closestScrollable):eM},[]),u=eT(()=>{let e=c();d(e.x,e.y)},[c,d]),p=eR(()=>eg(u),[u]),g=eT(()=>{let e=t.current,r=nu(e);if(e&&r||ek(),e.scrollOptions.shouldPublishImmediately)return void u();p()},[p,u]),m=eT((e,a)=>{t.current&&ek();let i=s.current,n=i.getDroppableRef();n||ek();let l=ni(n),d={ref:n,descriptor:o,env:l,scrollOptions:a};t.current=d;let c=no({ref:n,descriptor:o,env:l,windowScroll:e,direction:i.direction,isDropDisabled:i.isDropDisabled,isCombineEnabled:i.isCombineEnabled,shouldClipSubject:!i.ignoreContainerClipping}),u=l.closestScrollable;return u&&(u.setAttribute(a2.contextId,r.contextId),u.addEventListener("scroll",g,nc(d.scrollOptions))),c},[r.contextId,o,g,s]),h=eT(()=>{let e=t.current,r=nu(e);return e&&r||ek(),nr(r)},[]),b=eT(()=>{let e=t.current;e||ek();let r=nu(e);t.current=null,r&&(p.cancel(),r.removeAttribute(a2.contextId),r.removeEventListener("scroll",g,nc(e.scrollOptions)))},[g,p]),v=eT(e=>{let r=t.current;r||ek();let a=nu(r);a||ek(),a.scrollTop+=e.y,a.scrollLeft+=e.x},[]),x=eR(()=>({getDimensionAndWatchScroll:m,getScrollWhileDragging:h,dragStopped:b,scroll:v}),[b,m,h,v]),y=eR(()=>({uniqueId:a,descriptor:o,callbacks:x}),[x,o,a]);a8(()=>(l.current=y.descriptor,i.droppable.register(y),()=>{t.current&&b(),i.droppable.unregister(y)}),[x,o,b,y,n,i.droppable]),a8(()=>{t.current&&n.updateDroppableIsEnabled(l.current.id,!e.isDropDisabled)},[e.isDropDisabled,n]),a8(()=>{t.current&&n.updateDroppableIsCombineEnabled(l.current.id,e.isCombineEnabled)},[e.isCombineEnabled,n])}({droppableId:o,type:l,mode:d,direction:c,isDropDisabled:p,isCombineEnabled:g,ignoreContainerClipping:u,getDroppableRef:y});let D=eR(()=>b().createElement(nI,{on:e.placeholder,shouldAnimate:e.shouldAnimatePlaceholder},({onClose:e,data:t,animate:a})=>b().createElement(nf,{placeholder:t,onClose:e,innerRef:N,animate:a,contextId:r,onTransitionEnd:k})),[r,k,e.placeholder,e.shouldAnimatePlaceholder,N]),A=eR(()=>({innerRef:w,placeholder:D,droppableProps:{"data-rfd-droppable-id":o,"data-rfd-droppable-context-id":r}}),[r,o,D,w]),j=h?h.dragging.draggableId:null,C=eR(()=>({droppableId:o,type:l,isUsingCloneFor:j}),[o,j,l]);return b().createElement(iV.Provider,{value:C},s(A,m),function(){if(!h)return null;let{dragging:e,render:t}=h,r=b().createElement(i5,{draggableId:e.draggableId,index:e.source.index,isClone:!0,isEnabled:!0,shouldRespectForcePress:!1,canDragInteractiveElements:!0},(r,a)=>t(r,a,e));return Q().createPortal(r,x())}())}),nC=(0,g.A)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);var nS=r(93661),nE=r(88233),nP=r(89667),nR=r(34729);function nT({id:e,field:t,value:r,multiline:i=!1,className:n="",editingCell:s,setEditingCell:o,onSave:l,newRowData:d}){let[c,u]=(0,f.useState)(r),p=s?.id===e&&s?.field===t,g="new-row"===e,m=g?d?.[t]||"":r,h=()=>{l(e,t,c)},b=e=>{"Enter"!==e.key||e.shiftKey?"Escape"===e.key&&(u(g?d?.[t]||"":r),o(null)):(e.preventDefault(),h())};if(p)if(i)return(0,a.jsx)(nR.T,{value:c,onChange:e=>u(e.target.value),onBlur:h,onKeyDown:b,className:"min-h-[32px] resize-none overflow-hidden",style:{height:"auto",minHeight:"32px"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=t.scrollHeight+"px"},autoFocus:!0});else return(0,a.jsx)(nP.p,{value:c,onChange:e=>u(e.target.value),onBlur:h,onKeyDown:b,className:"h-8",autoFocus:!0});return(0,a.jsx)("div",{className:`cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors rounded p-2 -m-2 ${n} ${g?"text-gray-500 dark:text-gray-400 italic":""}`,onClick:()=>{o({id:e,field:t}),u(g?d?.[t]||"":r)},title:"Click to edit",children:(0,a.jsx)("div",{className:"whitespace-pre-wrap break-words",children:m||(g?"Click to add...":"Click to edit...")})})}let nM=e=>{switch(e){case"idea":return"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200";case"action":return"bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200";case"validated":return"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";case"invalidated":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";default:return"bg-muted text-muted-foreground"}},nO=(e,t,r)=>{let a=""!==e.trim(),i=""!==t.trim(),n=""!==r.trim();if(!a||i||n){if(a&&i&&!n)return"action";else if(a&&i&&n)return"validated"}return"idea"},nB=(e,t,r)=>{let a=""!==e.trim(),i=""!==t.trim(),n=""!==r.trim();return a&&i&&n};function nL({value:e,onChange:t,placeholder:r,disabled:i=!1,detail:n}){if(!nB(n.title,n.actions,n.result)){let e=nO(n.title,n.actions,n.result);return(0,a.jsx)($.E,{className:`${nM(e)} capitalize`,children:e})}return(0,a.jsxs)(z.l6,{value:e,onValueChange:t,disabled:i,children:[(0,a.jsx)(z.bq,{className:"w-[120px] h-7 px-2",children:(0,a.jsx)(z.yv,{placeholder:r})}),(0,a.jsxs)(z.gC,{children:[(0,a.jsx)(z.eb,{value:"validated",children:(0,a.jsx)($.E,{className:nM("validated"),children:"Validated"})}),(0,a.jsx)(z.eb,{value:"invalidated",children:(0,a.jsx)($.E,{className:nM("invalidated"),children:"Invalidated"})})]})]})}function n_({provided:e}){return(0,a.jsx)("div",{...e.dragHandleProps,className:"cursor-grab py-3 px-3 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 hover:bg-muted/50",title:"Drag to reorder",children:(0,a.jsx)(nC,{className:"h-4 w-4 text-muted-foreground hover:text-foreground transition-colors"})})}function nG({detail:e,index:t,editingCell:r,setEditingCell:n,onSave:s,onStatusChange:o}){return(0,a.jsx)(i3,{draggableId:e.id,index:t,children:(l,c)=>(0,a.jsxs)(V,{ref:l.innerRef,...l.draggableProps,className:`group hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200 ${c.isDragging?"bg-gray-200 dark:bg-gray-700 shadow-lg scale-[1.02] rotate-1":""} ${t%2==1?"bg-gray-50 dark:bg-gray-900":"bg-background"}`,children:[(0,a.jsx)(J,{className:"py-0 px-0 border-r",children:(0,a.jsx)(n_,{provided:l})}),(0,a.jsx)(J,{className:"font-medium py-3 px-3 border-r w-1/3",children:(0,a.jsx)(nT,{id:e.id,field:"title",value:e.title,multiline:!0,className:"font-semibold",editingCell:r,setEditingCell:n,onSave:s})}),(0,a.jsx)(J,{className:"py-3 px-3 border-r w-1/3",children:(0,a.jsx)(nT,{id:e.id,field:"actions",value:e.actions,multiline:!0,editingCell:r,setEditingCell:n,onSave:s})}),(0,a.jsx)(J,{className:"py-3 px-3 border-r w-1/3",children:(0,a.jsx)(nT,{id:e.id,field:"result",value:e.result,multiline:!0,editingCell:r,setEditingCell:n,onSave:s})}),(0,a.jsx)(J,{className:"py-3 px-3 border-r",children:(0,a.jsx)(nL,{value:e.status,onChange:t=>o(e.id,t),detail:e})}),(0,a.jsx)(J,{className:"py-3 px-3",children:(0,a.jsxs)(d.rI,{children:[(0,a.jsx)(d.ty,{asChild:!0,children:(0,a.jsxs)(i.$,{variant:"ghost",className:"h-8 w-8 p-0",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open menu"}),(0,a.jsx)(nS.A,{className:"h-4 w-4"})]})}),(0,a.jsx)(d.SQ,{align:"end",children:(0,a.jsxs)(d._2,{className:"gap-2 text-red-600",children:[(0,a.jsx)(nE.A,{className:"h-4 w-4"}),"Delete"]})})]})})]})},e.id)}function n$({newRowData:e,editingCell:t,setEditingCell:r,onSave:i,onStatusChange:n}){let s={id:"new-row",title:e.title,actions:e.actions,result:e.result,status:e.status};return(0,a.jsxs)(V,{className:"bg-gray-50 dark:bg-gray-900 border-t-2 border-dashed border-gray-300 dark:border-gray-600",children:[(0,a.jsx)(J,{className:"py-3 px-3 border-r",children:(0,a.jsx)("div",{className:"h-4 w-4"})}),(0,a.jsx)(J,{className:"font-medium py-3 px-3 border-r w-1/3",children:(0,a.jsx)(nT,{id:"new-row",field:"title",value:"",multiline:!0,className:"font-semibold",editingCell:t,setEditingCell:r,onSave:i,newRowData:e})}),(0,a.jsx)(J,{className:"py-3 px-3 border-r w-1/3",children:(0,a.jsx)(nT,{id:"new-row",field:"actions",value:"",multiline:!0,editingCell:t,setEditingCell:r,onSave:i,newRowData:e})}),(0,a.jsx)(J,{className:"py-3 px-3 border-r w-1/3",children:(0,a.jsx)(nT,{id:"new-row",field:"result",value:"",multiline:!0,editingCell:t,setEditingCell:r,onSave:i,newRowData:e})}),(0,a.jsx)(J,{className:"py-3 px-3 border-r",children:(0,a.jsx)(nL,{value:e.status,onChange:e=>n("new-row",e),placeholder:"Status",detail:s})}),(0,a.jsx)(J,{className:"py-3 px-3",children:(0,a.jsx)("div",{className:"h-8 w-8"})})]})}function nq({itemDetails:e}){let{updateItemDetail:t,addItemDetail:r}=X(),[i,n]=(0,f.useState)(e),[s,o]=(0,f.useState)(null),[l,d]=(0,f.useState)(!1),[c,u]=(0,f.useState)({id:"new-row",title:"",actions:"",result:"",status:"idea"}),p=(e,a,s)=>{if("new-row"===e){let e={...c,[a]:s},t=nO("title"===a?s:c.title,"actions"===a?s:c.actions,"result"===a?s:c.result),o={id:`item-${Date.now()}`,title:"title"===a?s:c.title,actions:"actions"===a?s:c.actions,result:"result"===a?s:c.result,status:t,description:"",updatedAt:new Date().toISOString()};o[a]=s,""!==s.trim()?(n([...i,o]),r(o),u({id:"new-row",title:"",actions:"",result:"",status:"idea"})):u(e)}else{let r=i.find(t=>t.id===e);if(r){let o={...r,[a]:s},l=nO("title"===a?s:r.title,"actions"===a?s:r.actions,"result"===a?s:r.result);nB(o.title,o.actions,o.result)&&("validated"===r.status||"invalidated"===r.status)||(o.status=l),n(i.map(t=>t.id===e?o:t)),t(e,{[a]:s,status:o.status})}}o(null)},g=(e,r)=>{if("new-row"===e)return void u(e=>({...e,status:r}));n(i.map(t=>t.id===e?{...t,status:r}:t)),t(e,{status:r})};return(0,a.jsx)(G.Bc,{children:(0,a.jsx)("div",{className:"w-full max-w-full overflow-hidden -m-0 -p-0",children:(0,a.jsx)(q.Zp,{className:"w-full p-0 m-0 overflow-hidden border-0 shadow-none rounded-none",children:(0,a.jsxs)(q.Wu,{className:"p-0 m-0",children:[(0,a.jsx)(iq,{onDragEnd:e=>{if(!e.destination)return;let{source:t,destination:r}=e;if(t.index===r.index)return;let a=Array.from(i),[s]=a.splice(t.index,1);a.splice(r.index,0,s),n(a)},children:(0,a.jsx)("div",{className:"w-full overflow-auto max-h-[calc(100vh-200px)] m-0 p-0",children:(0,a.jsxs)(W,{className:"m-0 rounded-none",children:[(0,a.jsx)(F,{className:"sticky top-0 bg-gray-200 dark:bg-gray-800 backdrop-blur-sm z-10",children:(0,a.jsxs)(V,{className:"border-b-2",children:[(0,a.jsx)(H,{className:"bg-gray-200 dark:bg-gray-800 border-r"}),(0,a.jsx)(H,{className:"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3",children:"Idea"}),(0,a.jsx)(H,{className:"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3",children:"Action"}),(0,a.jsx)(H,{className:"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-1/3",children:"Result"}),(0,a.jsx)(H,{className:"font-semibold border-r bg-gray-200 dark:bg-gray-800 w-32",children:"Status"}),(0,a.jsx)(H,{className:"font-semibold bg-gray-200 dark:bg-gray-800 w-20"})]})}),(0,a.jsx)(nj,{droppableId:"business-items",children:e=>(0,a.jsxs)(U,{...e.droppableProps,ref:e.innerRef,children:[i.map((e,t)=>(0,a.jsx)(nG,{detail:e,index:t,editingCell:s,setEditingCell:o,onSave:p,onStatusChange:g},e.id)),(0,a.jsx)(n$,{newRowData:c,editingCell:s,setEditingCell:o,onSave:p,onStatusChange:g}),e.placeholder]})})]})})}),(0,a.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700"})]})})})})}function nz({selectedItem:e,itemDetails:t,onBackToItems:r}){let[i,n]=(0,f.useState)(!1);return(0,a.jsx)(G.Bc,{children:(0,a.jsx)(nq,{itemDetails:t,selectedBusinessItem:e,onBackToItems:r})})}var nW=r(8759);class nF{constructor(e=j){this.items=e}checkDependencies(e){let t=this.getItem(e);if(!t)return{isValid:!1,missingDependencies:[],readyToProgress:!1,message:"Item not found"};if(!t.dependencies||0===t.dependencies.length)return{isValid:!0,missingDependencies:[],readyToProgress:!0,message:"No dependencies required"};let r=t.dependencies.filter(e=>{let t=this.getItem(e);return!t||"validated"!==t.status}),a=0===r.length;return{isValid:a,missingDependencies:r,readyToProgress:a&&"validated"!==t.status,message:a?"All dependencies satisfied":`Missing: ${r.map(e=>this.getItem(e)?.title||e).join(", ")}`}}getDependents(e){return this.items.filter(t=>t.dependencies?.includes(e)).map(e=>e.id)}getDependencyStatus(e){let t=this.getItem(e),r=this.checkDependencies(e),a=this.getDependents(e);if(!t)return{itemId:e,canProgress:!1,blockedBy:[],dependents:[],completionPercentage:0};let i=t.dependencies?.length?(t.dependencies.length-r.missingDependencies.length)/t.dependencies.length*100:100;return{itemId:e,canProgress:r.readyToProgress,blockedBy:r.missingDependencies,dependents:a,completionPercentage:i}}getReadyItems(){return this.items.filter(e=>this.checkDependencies(e.id).readyToProgress).map(e=>e.id)}getBlockedItems(){return this.items.filter(e=>!this.checkDependencies(e.id).isValid&&"validated"!==e.status).map(e=>e.id)}getDependencyChain(e,t=new Set){if(t.has(e))return[];let r=this.getItem(e);if(!r||!r.dependencies)return[];t.add(e);let a=[];for(let e of r.dependencies){a.push(e);let r=this.getDependencyChain(e,t);a.push(...r)}return[...new Set(a)]}getImpactChain(e,t=new Set){if(t.has(e))return[];t.add(e);let r=this.getDependents(e),a=[];for(let e of r){a.push(e);let r=this.getImpactChain(e,t);a.push(...r)}return[...new Set(a)]}validateStatusChange(e,t){let r=this.getItem(e);if(!r)return{isValid:!1,missingDependencies:[],readyToProgress:!1,message:"Item not found"};if("validated"===r.status&&"validated"!==t){let t=this.getImpactChain(e).filter(e=>{let t=this.getItem(e);return t?.status==="validated"});if(t.length>0)return{isValid:!1,missingDependencies:[],readyToProgress:!1,message:`Cannot change status: ${t.length} confirmed items depend on this`}}return"validated"===t?this.checkDependencies(e):{isValid:!0,missingDependencies:[],readyToProgress:!0,message:"Status change is valid"}}getSuggestedNextItems(e=5){return this.getReadyItems().map(e=>({itemId:e,dependentCount:this.getDependents(e).length,order:this.getItem(e)?.order||999})).sort((e,t)=>e.dependentCount!==t.dependentCount?t.dependentCount-e.dependentCount:e.order-t.order).slice(0,e).map(e=>e.itemId)}getCompletionStats(){let e=this.items.length,t=this.items.filter(e=>"validated"===e.status).length,r=this.getReadyItems().length,a=this.getBlockedItems().length;return{total:e,validated:t,ready:r,blocked:a,completionPercentage:t/e*100,readyPercentage:r/e*100,blockedPercentage:a/e*100}}getItem(e){return this.items.find(t=>t.id===e)}}let nU=new nF,nV=e=>nU.checkDependencies(e),nH=(0,g.A)("lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var nJ=r(13964),nY=r(3589);let nK=({item:e,onItemClick:t})=>{let r=e.dependencies?nV(e.id):null,i=r&&!r.isValid;return(0,a.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg mb-3 transition-all cursor-pointer hover:shadow-md hover:scale-[1.02] ${((e,t=!1)=>t?"bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 opacity-60":"bg-gray-100 dark:bg-background text-gray-600 dark:text-gray-300")(e.status,i??!1)} hover:bg-primary/10 dark:hover:bg-primary/20 border relative z-40 ${i?"cursor-not-allowed opacity-50 group":""}`,onClick:()=>{i||t(e)},children:[(0,a.jsx)("div",{className:`flex items-center gap-3 flex-1 min-w-0 ${i?"opacity-50":""}`,children:(0,a.jsx)("div",{className:"flex-1 min-w-0",children:(0,a.jsx)("span",{className:"font-medium text-sm truncate",children:e.title})})}),i&&(0,a.jsx)("div",{className:"absolute inset-0 bg-gray-500/60  dark:bg-gray-800/60 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 ",children:(0,a.jsx)("span",{className:"text-white text-sm font-medium",children:"It's too early to work on this"})}),(0,a.jsx)("div",{className:`flex items-center gap-1 flex-shrink-0 ${i?"opacity-50":""}`,children:"invalidated"!==e.status&&(0,a.jsxs)(a.Fragment,{children:[e.actions>0&&(0,a.jsxs)($.E,{variant:"secondary",className:"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs px-1.5 border border-blue-200 dark:border-blue-700 h-6",children:[(0,a.jsx)(p.A,{className:"h-3 w-3"}),e.actions>1?e.actions:""]}),e.ideas>0&&(0,a.jsxs)($.E,{variant:"secondary",className:"bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white text-xs px-1.5 py-0.5 border border-yellow-300 dark:border-yellow-700 h-6",children:[(0,a.jsx)(nH,{className:"h-3 w-3"}),e.ideas>1?e.ideas:""]}),e.results>0&&(0,a.jsxs)($.E,{variant:"secondary",className:"bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 text-xs px-1.5 border border-green-700 dark:border-green-600 h-6",children:[(0,a.jsx)(nJ.A,{className:"h-4 w-4"}),e.results>1?e.results:""]})]})})]})},nX=({section:e,onItemClick:t})=>{let[r,i]=(0,f.useState)(!0);return(0,a.jsx)(q.Zp,{className:"bg-white dark:bg-card border border-gray-200 dark:border-border shadow-lg hover:shadow-xl transition-all duration-200 h-fit py-0 relative z-40",children:(0,a.jsxs)(nW.Nt,{open:r,onOpenChange:i,children:[(0,a.jsx)(nW.R6,{asChild:!0,children:(0,a.jsx)(q.aR,{className:"cursor-pointer hover:bg-gray-50 dark:hover:bg-background transition-colors pb-2 px-4 py-2 mt-1 mb-2",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"flex items-center gap-3",children:(0,a.jsx)(q.ZB,{className:"text-lg font-semibold text-gray-900 dark:text-gray-100",children:e.title})}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:(0,a.jsx)(nY.A,{className:`h-5 w-5 text-gray-500 dark:text-gray-400 transition-transform ${r?"rotate-0":"rotate-180"}`})})]})})}),(0,a.jsx)(nW.Ke,{children:(0,a.jsx)(q.Wu,{className:"py-0 px-3",children:e.items.sort((e,t)=>(e.order||0)-(t.order||0)).map(e=>(0,a.jsx)(nK,{item:e,onItemClick:t},e.id))})})]})})};function nZ({sections:e,onItemClick:t}){return(0,a.jsx)("div",{className:"w-full relative z-40",children:(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3 auto-rows-min",children:e.map(e=>(0,a.jsx)(nX,{section:e,onItemClick:t},e.id))})})})}function nQ({activeContent:e,setActiveContent:t,mockDraftItems:r,mockFileItems:n,selectedItem:s,itemDetails:o,sections:d,isLoading:c,error:p,onBusinessItemClick:g,onBackToItems:m}){return(0,a.jsxs)("div",{className:"flex-1 min-w-0 min-h-0 w-full max-w-full p-4 pb-20 overflow-y-auto bg-gray-50 dark:bg-gray-900 relative transition-all duration-300 ease-in-out main-content-bottom-fade",children:[(0,a.jsx)("div",{className:"absolute inset-0 project-main-content pointer-events-none"}),(0,a.jsxs)("div",{className:"relative z-30",children:[(0,a.jsx)(l,{activeContent:e,setActiveContent:t,mockDraftItems:r,mockFileItems:n}),(0,a.jsxs)(L.N,{mode:"wait",children:[!e&&!s&&(0,a.jsxs)(_.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3,ease:"easeInOut"},children:[c&&(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:`animate-spin rounded-full ${u.hS.lg} border-b-2 border-gray-900 mx-auto mb-4`}),(0,a.jsx)("p",{className:"text-gray-600",children:"Loading business sections..."})]})}),p&&(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:p}),(0,a.jsx)(i.$,{onClick:()=>window.location.reload(),children:"Try Again"})]})}),!c&&!p&&d.length>0&&(0,a.jsx)(nZ,{sections:d,onItemClick:g}),!c&&!p&&0===d.length&&(0,a.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"No business sections found"}),(0,a.jsx)(i.$,{onClick:()=>window.location.reload(),children:"Reload"})]})})]},"business-sections"),!e&&s&&(0,a.jsx)(_.P.div,{initial:{opacity:0,x:30},animate:{opacity:1,x:0},exit:{opacity:0,x:-30},transition:{duration:.3,ease:"easeInOut"},className:"-m-4 -p-4",children:(0,a.jsx)(nz,{selectedItem:s,itemDetails:o,onBackToItems:m})},"business-detail")]})]})]})}var n0=r(51214);let n1=(0,g.A)("panel-left-close",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]]);var n2=r(33872);function n5(){return(0,a.jsxs)("svg",{width:"24",height:"24",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",className:"text-foreground",children:[(0,a.jsx)("circle",{cx:"4",cy:"12",r:"2",fill:"currentColor",children:(0,a.jsx)("animate",{id:"spinner_qFRN",begin:"0;spinner_OcgL.end+0.25s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),(0,a.jsx)("circle",{cx:"12",cy:"12",r:"2",fill:"currentColor",children:(0,a.jsx)("animate",{begin:"spinner_qFRN.begin+0.1s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})}),(0,a.jsx)("circle",{cx:"20",cy:"12",r:"2",fill:"currentColor",children:(0,a.jsx)("animate",{id:"spinner_OcgL",begin:"spinner_qFRN.begin+0.2s",attributeName:"cy",calcMode:"spline",dur:"0.6s",values:"12;6;12",keySplines:".33,.66,.66,1;.33,0,.66,.33"})})]})}function n3({variant:e="received",layout:t="default",className:r,children:i}){return(0,a.jsx)("div",{className:(0,w.cn)("flex items-start gap-2 mb-4","sent"===e&&"flex-row-reverse",r),children:i})}function n4({variant:e="received",isLoading:t,className:r,children:i}){return(0,a.jsx)("div",{className:(0,w.cn)("sent"===e?"rounded-lg p-3  bg-[#ced3e0]/20 border border-border/50 text-foreground":""),children:t?(0,a.jsx)("div",{className:"flex items-center space-x-2",children:(0,a.jsx)(n5,{})}):i})}r(32584);var n8=r(89422);let n6=f.forwardRef(({className:e,children:t,smooth:r=!1,...n},s)=>{let{scrollRef:o,isAtBottom:l,autoScrollEnabled:d,scrollToBottom:c,disableAutoScroll:u}=function(e={}){let{offset:t=20,smooth:r=!1,content:a}=e,i=(0,f.useRef)(null);(0,f.useRef)(0);let n=(0,f.useRef)(!1),[s,o]=(0,f.useState)({isAtBottom:!0,autoScrollEnabled:!0}),l=(0,f.useCallback)(e=>{let{scrollTop:r,scrollHeight:a,clientHeight:i}=e;return Math.abs(a-r-i)<=t},[t]),d=(0,f.useCallback)(e=>{if(!i.current)return;let t=i.current.scrollHeight-i.current.clientHeight;e?i.current.scrollTop=t:i.current.scrollTo({top:t,behavior:r?"smooth":"auto"}),o({isAtBottom:!0,autoScrollEnabled:!0}),n.current=!1},[r]);(0,f.useCallback)(()=>{if(!i.current)return;let e=l(i.current);o(t=>({isAtBottom:e,autoScrollEnabled:!!e||t.autoScrollEnabled}))},[l]);let c=(0,f.useCallback)(()=>{i.current&&l(i.current)||(n.current=!0,o(e=>({...e,autoScrollEnabled:!1})))},[l]);return{scrollRef:i,isAtBottom:s.isAtBottom,autoScrollEnabled:s.autoScrollEnabled,scrollToBottom:()=>d(!1),disableAutoScroll:c}}({smooth:r,content:t});return(0,a.jsxs)("div",{className:"relative w-full h-full",children:[(0,a.jsx)("div",{className:`flex flex-col w-full h-full p-4 overflow-y-auto ${e}`,ref:o,onWheel:u,onTouchMove:u,...n,children:(0,a.jsx)("div",{className:"flex flex-col",children:t})}),!l&&(0,a.jsx)(i.$,{onClick:()=>{c()},size:"icon",variant:"outline",className:"absolute bottom-2 left-1/2 transform -translate-x-1/2 inline-flex rounded-full shadow-md","aria-label":"Scroll to bottom",children:(0,a.jsx)(n8.A,{className:"h-4 w-4"})})]})});n6.displayName="ChatMessageList";var n9=r(23166),n7=r(78898);let se=[{id:1,user:"Siift AI",avatar:"",message:"Welcome to your project! I'm here to help you manage tasks, answer questions, and provide insights.",timestamp:"9:00 AM",isCurrentUser:!1},{id:2,user:"You",avatar:"",message:"Hey! I've finished the homepage design. Can you review it?",timestamp:"10:30 AM",isCurrentUser:!0},{id:3,user:"Siift AI",avatar:"",message:"Looks great! The design follows modern UI principles. I can help you implement the responsive layout if needed.",timestamp:"10:32 AM",isCurrentUser:!1},{id:4,user:"You",avatar:"",message:"Perfect! I'll work on the component library in parallel.",timestamp:"10:35 AM",isCurrentUser:!0},{id:5,user:"Siift AI",avatar:"",message:"I notice you're making great progress on the responsive layout. Would you like me to suggest some best practices for mobile optimization?",timestamp:"10:45 AM",isCurrentUser:!1},{id:6,user:"You",avatar:"",message:"Great progress everyone! Let's sync up tomorrow morning.",timestamp:"11:15 AM",isCurrentUser:!0},{id:7,user:"Siift AI",avatar:"",message:"Sounds good! I should have the navigation component ready by then.",timestamp:"11:18 AM",isCurrentUser:!1},{id:8,user:"You",avatar:"",message:"I'll prepare the test cases for the new features.",timestamp:"11:45 AM",isCurrentUser:!0}];function st({projectId:e,isCollapsed:t,onCollapseChange:r,embedded:i=!1,chatWidth:n,setChatWidth:s,isChatCollapsed:o,setIsChatCollapsed:l,selectedBusinessItem:d}){let[u,p]=(0,f.useState)(se),[g,m]=(0,f.useState)(!1),[h,b]=(0,f.useState)(!1),[v,x]=(0,f.useState)("45%"),{setOpen:y,state:w}=(0,c.cL)();if(i)return"collapsed"===w?null:(0,a.jsxs)("div",{className:`w-full h-full border-t border-border ${d?"bg-[#E8F5E9]/50 dark:bg-green-950/20":"bg-[#F5F5F5] dark:bg-yellow-950/20"} backdrop-blur-sm flex flex-col transition-all duration-300`,children:[(0,a.jsx)("div",{className:"flex-1 overflow-hidden w-full min-h-0 relative",children:(0,a.jsx)(n6,{className:"w-full h-full",children:u.map(e=>(0,a.jsxs)(n3,{className:"mt-2",variant:e.isCurrentUser?"sent":"received",children:[!e.isCurrentUser&&(0,a.jsx)("div",{className:"h-8 w-8 shrink-0 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,a.jsx)(n9.g,{size:20})}),(0,a.jsx)(n4,{variant:e.isCurrentUser?"sent":"received",children:(0,a.jsx)("div",{className:"text-sm leading-relaxed whitespace-pre-line",children:e.message})})]},e.id))})}),(0,a.jsxs)("div",{className:"p-3 border-t border-border/50 bg-muted/30 backdrop-blur-sm w-full flex-shrink-0",children:[(0,a.jsx)(n7.AI_Prompt,{}),(0,a.jsx)("div",{className:"mt-1 px-2",children:(0,a.jsx)("p",{className:"text-[10px] text-center opacity-50 leading-tight",children:"Siift can make mistakes. Please double check answers to ensure accuracy."})})]})]})}function sr({onMouseDown:e,isDragging:t,className:r}){return(0,a.jsx)("div",{className:(0,w.cn)("absolute right-0 top-0 bottom-0 w-1 cursor-col-resize group hover:w-2 transition-all duration-200","bg-transparent hover:bg-border/50",t&&"bg-primary/50 w-2",r),onMouseDown:e,children:(0,a.jsx)("div",{className:(0,w.cn)("absolute right-0 top-1/2 -translate-y-1/2 w-1 h-8 bg-border/30 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200",t&&"opacity-100 bg-primary/70")})})}let sa=[{id:"1",name:"Website Redesign",status:"in-progress"},{id:"2",name:"Mobile App",status:"planning"},{id:"3",name:"Marketing Campaign",status:"completed"},{id:"4",name:"API Integration",status:"in-progress"}];function si({projectId:e,chatWidth:t,setChatWidth:r,isChatCollapsed:i,setIsChatCollapsed:s,selectedBusinessItem:o,showDescription:l=!1,onBackToProject:d,resizeHandle:p,...g}){let{setOpen:m,state:h,isMobile:f}=(0,c.cL)(),{trackClick:b,trackCustomEvent:v}=(0,A.s)(),x=(0,O.useRouter)(),y=sa.find(t=>t.id===e)||sa[0],w="collapsed"===h;return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(c.Bx,{collapsible:"icon",className:`border-r flex flex-col h-screen relative overflow-hidden transition-all duration-300 ease-in-out ${w?"pt-2":""}`,...g,children:[(0,a.jsx)(c.Gh,{className:"h-[79px] px-4 py-4 flex items-center",children:w?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center gap-4 w-full h-full py-11",children:[(0,a.jsx)(n.V,{icon:n0.A,variant:"ghost",size:"lg",layout:"icon-only",hoverColor:"grey",hoverScale:!0,showBorder:!0,onClick:()=>{b("sidebar-toggle","project-sidebar"),v("sidebar_toggled",{from_state:"collapsed",to_state:"expanded",location:"project-sidebar"}),m(!0)},iconClassName:u.hS.lg}),(0,a.jsx)(n.V,{onClick:()=>{l&&d?(b("back-to-project","project-sidebar"),v("navigation_clicked",{destination:"project",from_page:"item-detail",location:"sidebar"}),d()):(b("back-to-dashboard","project-header"),v("navigation_clicked",{destination:"dashboard",from_page:"project-detail",location:"header"}),x.push("/user-dashboard"))},icon:l?S.A:R.A,variant:"ghost",size:"lg",hoverColor:"grey",layout:"icon-only",showBorder:!0,hoverScale:!0,iconClassName:u.hS.lg})]}):(0,a.jsx)("div",{className:"flex items-center gap-2 h-full w-full justify-between",children:(0,a.jsxs)("div",{className:"flex items-center gap-2 justify-between w-full",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(n.V,{onClick:()=>{l&&d?(b("back-to-project","project-sidebar"),v("navigation_clicked",{destination:"project",from_page:"item-detail",location:"sidebar"}),d()):(b("back-to-dashboard","project-header"),v("navigation_clicked",{destination:"dashboard",from_page:"project-detail",location:"header"}),x.push("/user-dashboard"))},icon:l?S.A:R.A,variant:"ghost",size:"lg",hoverColor:"grey",layout:"icon-only",showBorder:!0,hoverScale:!0,iconClassName:u.hS.lg}),l?(0,a.jsx)("span",{className:"text-sm font-bold text-gray-700 dark:text-gray-300 truncate",children:o?.title||"Untitled Item"}):(0,a.jsx)("span",{className:"text-lg font-bold text-gray-700 dark:text-gray-300 truncate",children:y.name})]}),!l&&!f&&(0,a.jsx)(n.V,{icon:n1,variant:"ghost",size:"lg",layout:"icon-only",hoverColor:"grey",hoverScale:!0,showBorder:!0,onClick:()=>{b("sidebar-toggle","project-sidebar"),v("sidebar_toggled",{from_state:"expanded",to_state:"collapsed",location:"project-sidebar"}),m(!1)},iconClassName:u.hS.lg})]})})}),f&&(0,a.jsxs)("div",{className:"flex items-center gap-2 p-4 border-b border-border",children:[(0,a.jsx)(n.V,{onClick:()=>{l&&d?(b("back-to-project","project-sidebar-mobile"),v("navigation_clicked",{destination:"project",from_page:"item-detail",location:"sidebar-mobile"}),d()):(b("back-to-dashboard","project-sidebar-mobile"),v("navigation_clicked",{destination:"dashboard",from_page:"project-detail",location:"sidebar-mobile"}),x.push("/user-dashboard"))},icon:l?S.A:R.A,variant:"ghost",size:"lg",hoverColor:"grey",layout:"icon-only",showBorder:!0,hoverScale:!0,iconClassName:u.hS.lg}),(0,a.jsx)("span",{className:`${l?"text-sm font-bold text-gray-700 dark:text-gray-300 truncate":"text-lg font-bold text-gray-700 dark:text-gray-300 truncate"}`,children:l?o?.title||"Untitled Item":y.name})]}),(0,a.jsxs)(c.Yv,{className:"flex-1 min-h-0 flex flex-col w-full",children:[w&&(0,a.jsx)("div",{className:"flex-shrink-0 mt-auto mb-4 flex justify-center",children:(0,a.jsx)(n.V,{icon:n2.A,variant:"secondary",size:"lg",layout:"icon-only",showBorder:!1,hoverColor:"green",hoverScale:!0,onClick:()=>{b("chat-expand-button","project-sidebar"),v("sidebar_chat_expanded",{from_state:"collapsed",project_id:e}),m(!0)},iconClassName:u.hS.lg})}),!w&&(0,a.jsx)("div",{className:"flex-1 min-h-0 w-full",children:(0,a.jsx)(st,{projectId:e,embedded:!0,chatWidth:t,setChatWidth:r,isChatCollapsed:i,setIsChatCollapsed:s,selectedBusinessItem:o})})]}),(0,a.jsx)(c.jM,{}),p&&(0,a.jsx)(sr,{onMouseDown:p.onMouseDown,isDragging:p.isDragging})]})})}var sn=r(43649),ss=r(64398),so=r(41312),sl=r(53411),sd=r(25541);let sc=(0,g.A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var su=r(99891);let sp=(0,g.A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]]);var sg=r(84027),sm=r(28947);let sh=(0,g.A)("megaphone",[["path",{d:"M11 6a13 13 0 0 0 8.4-2.8A1 1 0 0 1 21 4v12a1 1 0 0 1-1.6.8A13 13 0 0 0 11 14H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2z",key:"q8bfy3"}],["path",{d:"M6 14a12 12 0 0 0 2.4 7.2 2 2 0 0 0 3.2-2.4A8 8 0 0 1 10 14",key:"1853fq"}],["path",{d:"M8 6v8",key:"15ugcq"}]]),sf=(0,g.A)("palette",[["path",{d:"M12 22a1 1 0 0 1 0-20 10 9 0 0 1 10 9 5 5 0 0 1-5 5h-2.25a1.75 1.75 0 0 0-1.4 2.8l.3.4a1.75 1.75 0 0 1-1.4 2.8z",key:"e79jfc"}],["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}]]),sb=(0,g.A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]);var sv=r(48730);let sx=(0,g.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var sy=r(23928);let sw=(0,g.A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]]),sI={problem:sn.A,"unique-value-proposition":ss.A,audience:so.A,alternatives:nH,"market-size":sl.A,trends:sd.A,why:sc,advantages:su.A,product:sp,tech:sg.A,packages:sp,positioning:sm.A,channels:sh,messaging:n2.A,brand:sf,assets:T.A,"sales-motion":sb,metrics:sv.A,risks:su.A,"business-model":sx,revenue:sy.A,cost:sy.A,team:sw},sN=e=>({id:e.id,title:e.title,description:e.description,question:e.question,guidance:e.guidance,category:e.category,order:e.order,inputType:e.inputType,dependencies:e.dependencies,status:e.status,values:e.values,actions:e.actions,ideas:e.ideas,results:e.results,icon:sI[e.id]||sp}),sk=["Market","Solution","Sales & Marketing","Company"].map(e=>{let t=j.filter(t=>t.category===e).sort((e,t)=>e.order-t.order).map(sN);return{id:e.toLowerCase().replace(/\s+/g,"-"),title:e,items:t}}),sD=async()=>new Promise(e=>{setTimeout(()=>{e(sk)},500)});class sA extends Error{constructor(e,t,r){super(e),this.status=t,this.code=r,this.name="ApiError"}}class sj{static async fetchSections(e){try{return await sD()}catch(e){throw console.error("Failed to fetch business sections:",e),e}}static async fetchItem(e,t){try{let r=(await this.fetchSections(e)).flatMap(e=>e.items).find(e=>e.id===t);if(!r)throw new sA("Business item not found",404,"ITEM_NOT_FOUND");return r}catch(e){throw console.error("Failed to fetch business item:",e),e}}static async updateItem(e,t,r){try{let a={...await this.fetchItem(e,t),...r};return await new Promise(e=>setTimeout(e,300)),a}catch(e){throw console.error("Failed to update business item:",e),e}}static async createItem(e,t,r){try{let e={...r,id:`item-${Date.now()}`};return await new Promise(e=>setTimeout(e,300)),e}catch(e){throw console.error("Failed to create business item:",e),e}}static async deleteItem(e,t){try{await new Promise(e=>setTimeout(e,300))}catch(e){throw console.error("Failed to delete business item:",e),e}}static async bulkUpdateItems(e,t){try{let r=[];for(let{itemId:a,updates:i}of t){let t=await this.updateItem(e,a,i);r.push(t)}return r}catch(e){throw console.error("Failed to bulk update business items:",e),e}}static async getAnalytics(e){try{let t=await this.fetchSections(e),r=t.flatMap(e=>e.items),a=r.length,i=r.filter(e=>"validated"===e.status).length,n={};return t.forEach(e=>{let t=e.items.length,r=e.items.filter(e=>"validated"===e.status).length;n[e.title]={total:t,completed:r}}),{totalItems:a,completedItems:i,completionPercentage:a>0?i/a*100:0,categoryBreakdown:n}}catch(e){throw console.error("Failed to fetch business sections analytics:",e),e}}}let{fetchSections:sC,fetchItem:sS,updateItem:sE,createItem:sP,deleteItem:sR,bulkUpdateItems:sT,getAnalytics:sM}=sj,sO={BUSINESS_ITEM_DETAILS:"siift_business_item_details",LAST_UPDATED:"siift_last_updated"};class sB{getAllItemDetails(){if(!this.isClient)return{};try{let e=localStorage.getItem(sO.BUSINESS_ITEM_DETAILS);return e?JSON.parse(e):{}}catch(e){return console.error("Error reading from localStorage:",e),{}}}getItemDetails(e,t){return this.getAllItemDetails()[t?`${t}_${e}`:e]||[]}saveItemDetails(e,t,r){if(this.isClient)try{let a=this.getAllItemDetails(),i=r?`${r}_${e}`:e,n=t.map(e=>({...e,projectId:r,lastModified:new Date().toISOString()}));a[i]=n,localStorage.setItem(sO.BUSINESS_ITEM_DETAILS,JSON.stringify(a)),localStorage.setItem(sO.LAST_UPDATED,new Date().toISOString())}catch(e){console.error("Error saving to localStorage:",e)}}addItemDetail(e,t,r){let a={...t,id:`${e}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},i=[...this.getItemDetails(e,r),a];return this.saveItemDetails(e,i,r),a}updateItemDetail(e,t,r,a){let i=this.getItemDetails(e,a),n=i.findIndex(e=>e.id===t);if(-1===n)return!1;let s=[...i];return s[n]={...s[n],...r,updatedAt:new Date().toISOString()},this.saveItemDetails(e,s,a),!0}deleteItemDetail(e,t,r){let a=this.getItemDetails(e,r),i=a.filter(e=>e.id!==t);return i.length!==a.length&&(this.saveItemDetails(e,i,r),!0)}clearItemDetails(e,t){if(this.isClient)try{let r=this.getAllItemDetails(),a=t?`${t}_${e}`:e;delete r[a],localStorage.setItem(sO.BUSINESS_ITEM_DETAILS,JSON.stringify(r)),localStorage.setItem(sO.LAST_UPDATED,new Date().toISOString())}catch(e){console.error("Error clearing localStorage:",e)}}clearAllData(){if(this.isClient)try{localStorage.removeItem(sO.BUSINESS_ITEM_DETAILS),localStorage.removeItem(sO.LAST_UPDATED)}catch(e){console.error("Error clearing all localStorage:",e)}}getStorageStats(){if(!this.isClient)return{totalItems:0,totalDetails:0,storageSize:0,lastUpdated:null};try{let e=this.getAllItemDetails(),t=Object.keys(e).length,r=Object.values(e).reduce((e,t)=>e+t.length,0),a=localStorage.getItem(sO.BUSINESS_ITEM_DETAILS)||"",i=new Blob([a]).size,n=localStorage.getItem(sO.LAST_UPDATED);return{totalItems:t,totalDetails:r,storageSize:i,lastUpdated:n}}catch(e){return console.error("Error getting storage stats:",e),{totalItems:0,totalDetails:0,storageSize:0,lastUpdated:null}}}exportData(){let e=this.getAllItemDetails(),t=this.getStorageStats();return JSON.stringify({version:"1.0",exportDate:new Date().toISOString(),stats:t,data:e},null,2)}importData(e){if(!this.isClient)return!1;try{let t=JSON.parse(e);if(!t.data||"object"!=typeof t.data)throw Error("Invalid data format");return localStorage.setItem(sO.BUSINESS_ITEM_DETAILS,JSON.stringify(t.data)),localStorage.setItem(sO.LAST_UPDATED,new Date().toISOString()),!0}catch(e){return console.error("Error importing data:",e),!1}}hasItemDetails(e,t){return this.getItemDetails(e,t).length>0}getRecentlyModified(e=24){let t=this.getAllItemDetails(),r=new Date(Date.now()-60*e*6e4),a=[];return Object.entries(t).forEach(([e,t])=>{let i=t.reduce((e,t)=>new Date(t.lastModified)>new Date(e.lastModified)?t:e);if(new Date(i.lastModified)>r){let[t,r]=e.includes("_")?e.split("_"):[void 0,e];a.push({itemId:r||e,projectId:t,lastModified:i.lastModified})}}),a.sort((e,t)=>new Date(t.lastModified).getTime()-new Date(e.lastModified).getTime())}constructor(){this.isClient=!1}}let sL=new sB,{getAllItemDetails:s_,getItemDetails:sG,saveItemDetails:s$,addItemDetail:sq,updateItemDetail:sz,deleteItemDetail:sW,clearItemDetails:sF,clearAllData:sU,getStorageStats:sV,exportData:sH,importData:sJ,hasItemDetails:sY,getRecentlyModified:sK}=sL,sX=(0,Y.v)()((0,K.lt)((e,t)=>({selectedItem:null,itemDetails:[],isLoading:!1,error:null,setSelectedItem:t=>e({selectedItem:t}),setItemDetails:t=>e({itemDetails:t}),setLoading:t=>e({isLoading:t}),setError:t=>e({error:t}),fetchItemDetails:async(t,r="default")=>{try{e({isLoading:!0,error:null}),console.log("Creating fresh initial data for",t);let a=j.find(e=>e.id===t);if(!a)throw Error(`Item with ID ${t} not found`);let i=[];a.values&&a.values.length>0&&a.values.forEach((e,t)=>{i.push({id:`${a.id}-value-${t}`,title:`Response ${t+1}`,status:"idea",actions:"Sample response - edit to customize",result:e,description:`Sample response for: ${a.question?.replace("{PROJECT NAME}","your project")||a.title}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()})}),i.push({id:`${a.id}-new-entry`,title:"Add your response",status:"invalidated",actions:"Click to edit and add your own analysis",result:"Enter your findings here...",description:`Your analysis for: ${a.question?.replace("{PROJECT NAME}","your project")||a.title}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}),sL.saveItemDetails(t,i,r),console.log("Created initial details for",t,i),e({itemDetails:i,isLoading:!1})}catch(t){console.error("Error fetching item details:",t),e({error:t instanceof Error?t.message:"Failed to fetch item details",isLoading:!1})}},updateItemStatus:async(r,a,i="default")=>{try{if(e({isLoading:!0,error:null}),"validated"===a&&!t().checkDependencies(r))throw Error("Cannot mark as validated: dependencies not satisfied");await sj.updateItem(i,r,{status:a});let{selectedItem:n}=t();n&&n.id===r&&e({selectedItem:{...n,status:a}}),e({isLoading:!1})}catch(t){console.error("Error updating item status:",t),e({error:t instanceof Error?t.message:"Failed to update item status",isLoading:!1})}},addItemDetail:r=>{let{selectedItem:a}=t();if(!a)return;let i=[...t().itemDetails,r];e({itemDetails:i}),sL.saveItemDetails(a.id,i,"default")},updateItemDetail:(r,a)=>{let{selectedItem:i}=t();if(!i)return;let n=t().itemDetails.map(e=>e.id===r?{...e,...a,updatedAt:new Date().toISOString()}:e);e({itemDetails:n}),sL.saveItemDetails(i.id,n,"default")},removeItemDetail:r=>{let{selectedItem:a}=t();if(!a)return;let i=t().itemDetails.filter(e=>e.id!==r);e({itemDetails:i}),sL.saveItemDetails(a.id,i,"default")},getItemData:e=>j.find(t=>t.id===e),checkDependencies:e=>{let t=j.find(t=>t.id===e);return!t||!t.dependencies||0===t.dependencies.length||t.dependencies.every(e=>{let t=j.find(t=>t.id===e);return t?.status==="validated"})},getRelatedItems:e=>{let t=j.find(t=>t.id===e);return{dependencies:t?.dependencies?.map(e=>j.find(t=>t.id===e)).filter(Boolean)||[],dependents:j.filter(t=>t.dependencies?.includes(e))}}}),{name:"business-item-store-enhanced"})),sZ=(0,Y.v)()((0,K.lt)((e,t)=>({sections:[],isLoading:!1,error:null,setSections:t=>e({sections:t,error:null}),setLoading:t=>e({isLoading:t}),setError:t=>e({error:t}),updateItem:(t,r,a)=>{e(e=>({sections:e.sections.map(e=>e.id===t?{...e,items:e.items.map(e=>e.id===r?{...e,...a}:e)}:e)}))},addItem:(t,r)=>{e(e=>({sections:e.sections.map(e=>e.id===t?{...e,items:[...e.items,r]}:e)}))},removeItem:(t,r)=>{e(e=>({sections:e.sections.map(e=>e.id===t?{...e,items:e.items.filter(e=>e.id!==r)}:e)}))}}),{name:"business-section-store"})),sQ=[{id:1,title:"Project proposal",status:"draft",lastModified:"2 hours ago"},{id:2,title:"Design brief",status:"draft",lastModified:"1 day ago"},{id:3,title:"Technical specs",status:"draft",lastModified:"3 days ago"}],s0=[{id:1,title:"logo.svg",type:"image",size:"24KB"},{id:2,title:"wireframes.fig",type:"design",size:"1.2MB"},{id:3,title:"requirements.pdf",type:"document",size:"156KB"},{id:4,title:"styleguide.pdf",type:"document",size:"2.1MB"}];function s1(){let e=(0,O.useParams)().id,[t,r]=(0,f.useState)(null),[i,n]=(0,f.useState)("45%"),[s,o]=(0,f.useState)(!1),[l,d]=(0,f.useState)("45vw"),{sections:u,isLoading:p,error:g,setSections:m,setLoading:h,setError:b}=sZ(),{selectedItem:v,itemDetails:x,setSelectedItem:y,setItemDetails:w,fetchItemDetails:I}=sX(),N=v?"30vw":l,k=async t=>{y(t),await I(t.id,e)},D=()=>{y(null)},A=function({initialWidth:e,minWidthPercent:t,maxWidthPercent:r,onWidthChange:a,onCollapse:i}){let[n,s]=(0,f.useState)(!1),[o,l]=(0,f.useState)(e),d=(0,f.useRef)(0),c=(0,f.useRef)(0),u=(0,f.useCallback)(e=>{e.preventDefault(),s(!0),d.current=e.clientX,c.current=parseFloat(o.replace("%",""))/100*window.innerWidth},[o]);return(0,f.useCallback)(e=>{if(!n)return;let s=e.clientX-d.current,o=Math.max(t,Math.min(r,(c.current+s)/window.innerWidth*100));if(o<10&&i)return void i();let u=`${o}%`;l(u),a(u)},[n,t,r,a,i]),(0,f.useCallback)(()=>{s(!1)},[]),{isDragging:n,currentWidth:o,handleMouseDown:u}}({initialWidth:l,minWidthPercent:10,maxWidthPercent:70,onWidthChange:e=>{let t=parseFloat(e.replace("%",""));d(`${t}vw`),n("45%")},onCollapse:()=>{o(!0)}});return(0,a.jsx)("div",{className:"h-screen overflow-hidden",children:(0,a.jsxs)(c.GB,{defaultOpen:!0,style:{"--sidebar-width":N,"--sidebar-width-mobile":"18rem","--sidebar-width-icon":"5rem",transition:"all 0.3s ease-in-out"},children:[(0,a.jsx)(si,{projectId:e,chatWidth:i,setChatWidth:e=>{n(e),d("45vw")},isChatCollapsed:s,setIsChatCollapsed:o,selectedBusinessItem:v,showDescription:!!v,onBackToProject:D,resizeHandle:{onMouseDown:A.handleMouseDown,isDragging:A.isDragging}}),(0,a.jsxs)(c.sF,{className:"flex-1 flex flex-col h-screen overflow-hidden",children:[(0,a.jsx)(B,{activeContent:t,setActiveContent:r,selectedBusinessItem:v,onBackToItems:D}),(0,a.jsx)(nQ,{activeContent:t,setActiveContent:r,mockDraftItems:sQ,mockFileItems:s0,selectedItem:v,itemDetails:x,sections:u,isLoading:p,error:g,onBusinessItemClick:k,onBackToItems:D}),(0,a.jsx)("div",{className:"fixed bottom-0 h-16 bg-gradient-to-t from-gray-50 via-gray-50/80 to-transparent dark:from-gray-900 dark:via-gray-900/80 dark:to-transparent pointer-events-none z-10",style:{left:"var(--sidebar-width, 45vw)",right:"0"}}),(0,a.jsx)("div",{className:"fixed bottom-0 h-8 backdrop-blur-sm pointer-events-none z-10",style:{left:"var(--sidebar-width, 45vw)",right:"0"}})]})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21342:(e,t,r)=>{"use strict";r.d(t,{I:()=>d,SQ:()=>l,V0:()=>g,_2:()=>c,lp:()=>u,mB:()=>p,rI:()=>s,ty:()=>o});var a=r(60687);r(43210);var i=r(26312),n=r(4780);function s({...e}){return(0,a.jsx)(i.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,a.jsx)(i.l9,{"data-slot":"dropdown-menu-trigger",...e})}function l({className:e,sideOffset:t=4,...r}){return(0,a.jsx)(i.ZL,{children:(0,a.jsx)(i.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",e),...r})})}function d({...e}){return(0,a.jsx)(i.YJ,{"data-slot":"dropdown-menu-group",...e})}function c({className:e,inset:t,variant:r="default",...s}){return(0,a.jsx)(i.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,n.cn)("focus:bg-gray-100 border border-transparent focus:text-gray-900 data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function u({className:e,inset:t,...r}){return(0,a.jsx)(i.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function p({className:e,...t}){return(0,a.jsx)(i.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",e),...t})}function g({className:e,...t}){return(0,a.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,n.cn)("text-muted-foreground ml-auto text-xs tracking-widest",e),...t})}},23928:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},28947:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(60687),i=r(8730),n=r(24224);r(43210);var s=r(4780);let o=(0,n.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let d=n?i.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,s.cn)(o({variant:t,size:r,className:e})),...l})}},33872:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>s});var a=r(60687),i=r(43210),n=r(4780);let s=i.forwardRef(({className:e,...t},r)=>(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));s.displayName="Textarea"},42120:(e,t,r)=>{Promise.resolve().then(r.bind(r,18257))},42763:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx","default")},45583:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},52387:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=r(65239),i=r(48088),n=r(88170),s=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["projects",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,42763)),"/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/projects/[id]/page",pathname:"/projects/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},67146:(e,t,r)=>{"use strict";r.d(t,{CG:()=>l,Fm:()=>p,Qs:()=>m,cj:()=>o,h:()=>u,qp:()=>g});var a=r(60687);r(43210);var i=r(26134),n=r(11860),s=r(4780);function o({...e}){return(0,a.jsx)(i.bL,{"data-slot":"sheet",...e})}function l({...e}){return(0,a.jsx)(i.l9,{"data-slot":"sheet-trigger",...e})}function d({...e}){return(0,a.jsx)(i.ZL,{"data-slot":"sheet-portal",...e})}function c({className:e,...t}){return(0,a.jsx)(i.hJ,{"data-slot":"sheet-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function u({className:e,children:t,side:r="right",...o}){return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(i.UC,{"data-slot":"sheet-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...o,children:[t,(0,a.jsxs)(i.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(n.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,s.cn)("flex flex-col gap-1.5 p-4",e),...t})}function g({className:e,...t}){return(0,a.jsx)(i.hE,{"data-slot":"sheet-title",className:(0,s.cn)("text-foreground font-semibold",e),...t})}function m({className:e,...t}){return(0,a.jsx)(i.VY,{"data-slot":"sheet-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78568:(e,t,r)=>{Promise.resolve().then(r.bind(r,42763))},78898:(e,t,r)=>{"use strict";r.d(t,{AI_Prompt:()=>d});var a=r(60687),i=r(70042),n=r(34729),s=r(4780),o=r(70334),l=r(43210);function d(){let[e,t]=(0,l.useState)(""),{textareaRef:r,adjustHeight:d}=function({minHeight:e,maxHeight:t}){let r=(0,l.useRef)(null),a=(0,l.useCallback)(a=>{let i=r.current;if(!i)return;if(a){i.style.height=`${e}px`;return}i.style.height=`${e}px`;let n=Math.max(e,Math.min(i.scrollHeight,t??Number.POSITIVE_INFINITY));i.style.height=`${n}px`},[e,t]);return{textareaRef:r,adjustHeight:a}}({minHeight:60,maxHeight:200}),[c,u]=(0,l.useState)("GPT-4-1 Mini");return(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)("div",{className:"bg-black/5 dark:bg-white/5 rounded-2xl p-1.5",children:(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"relative flex flex-col",children:(0,a.jsxs)("div",{className:"overflow-y-auto relative",style:{maxHeight:"400px"},children:[(0,a.jsx)(n.T,{id:"ai-input-15",value:e,placeholder:"What can I do for you?",className:(0,s.cn)("w-full bg-white/50 dark:bg-gray-700/60 rounded-xl px-3 py-2 pr-12 border border-gray-300/100 dark:border-gray-600/50 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-400 resize-none focus-visible:ring-0 focus-visible:ring-offset-0 transition-all duration-200","min-h-[120px]","hover:bg-gray-200/80 dark:hover:bg-gray-700/80 hover:border-gray-400 dark:hover:border-gray-500",e&&"bg-white/50 dark:bg-gray-700/90 border-gray-400 dark:border-gray-500"),ref:r,onKeyDown:r=>{"Enter"===r.key&&!r.shiftKey&&e.trim()&&(r.preventDefault(),t(""),d(!0))},onChange:e=>{t(e.target.value),d()}}),(0,a.jsx)("div",{className:"absolute bottom-2 right-2",children:(0,a.jsx)(i.V,{type:"button",icon:o.A,layout:"icon-only",size:"md",variant:e.trim()?"secondary":"ghost",iconClassName:"text-white",disabled:!e.trim(),"aria-label":"Send message",onClick:()=>{e.trim()&&(t(""),d(!0))},className:(0,s.cn)("transition-all duration-200",e.trim()?"bg-accent hover:bg-accent/90 text-accent-foreground shadow-lg hover:shadow-xl":"opacity-40 bg-gray-300/50 dark:bg-gray-600/50 hover:opacity-60")})})]})})})})})}},79551:e=>{"use strict";e.exports=require("url")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},88920:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var a=r(60687),i=r(43210),n=r(12157),s=r(72789),o=r(15124),l=r(21279),d=r(18171),c=r(32582);class u extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=t.offsetParent,r=(0,d.s)(e)&&e.offsetWidth||0,a=this.props.sizeRef.current;a.height=t.offsetHeight||0,a.width=t.offsetWidth||0,a.top=t.offsetTop,a.left=t.offsetLeft,a.right=r-a.width-a.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p({children:e,isPresent:t,anchorX:r,root:n}){let s=(0,i.useId)(),o=(0,i.useRef)(null),l=(0,i.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:d}=(0,i.useContext)(c.Q);return(0,i.useInsertionEffect)(()=>{let{width:e,height:a,top:i,left:c,right:u}=l.current;if(t||!o.current||!e||!a)return;let p="left"===r?`left: ${c}`:`right: ${u}`;o.current.dataset.motionPopId=s;let g=document.createElement("style");d&&(g.nonce=d);let m=n??document.head;return m.appendChild(g),g.sheet&&g.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${a}px !important;
            ${p}px !important;
            top: ${i}px !important;
          }
        `),()=>{m.contains(g)&&m.removeChild(g)}},[t]),(0,a.jsx)(u,{isPresent:t,childRef:o,sizeRef:l,children:i.cloneElement(e,{ref:o})})}let g=({children:e,initial:t,isPresent:r,onExitComplete:n,custom:o,presenceAffectsLayout:d,mode:c,anchorX:u,root:g})=>{let h=(0,s.M)(m),f=(0,i.useId)(),b=!0,v=(0,i.useMemo)(()=>(b=!1,{id:f,initial:t,isPresent:r,custom:o,onExitComplete:e=>{for(let t of(h.set(e,!0),h.values()))if(!t)return;n&&n()},register:e=>(h.set(e,!1),()=>h.delete(e))}),[r,h,n]);return d&&b&&(v={...v}),(0,i.useMemo)(()=>{h.forEach((e,t)=>h.set(t,!1))},[r]),i.useEffect(()=>{r||h.size||!n||n()},[r]),"popLayout"===c&&(e=(0,a.jsx)(p,{isPresent:r,anchorX:u,root:g,children:e})),(0,a.jsx)(l.t.Provider,{value:v,children:e})};function m(){return new Map}var h=r(86044);let f=e=>e.key||"";function b(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}let v=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:d=!0,mode:c="sync",propagate:u=!1,anchorX:p="left",root:m})=>{let[v,x]=(0,h.xQ)(u),y=(0,i.useMemo)(()=>b(e),[e]),w=u&&!v?[]:y.map(f),I=(0,i.useRef)(!0),N=(0,i.useRef)(y),k=(0,s.M)(()=>new Map),[D,A]=(0,i.useState)(y),[j,C]=(0,i.useState)(y);(0,o.E)(()=>{I.current=!1,N.current=y;for(let e=0;e<j.length;e++){let t=f(j[e]);w.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[j,w.length,w.join("-")]);let S=[];if(y!==D){let e=[...y];for(let t=0;t<j.length;t++){let r=j[t],a=f(r);w.includes(a)||(e.splice(t,0,r),S.push(r))}return"wait"===c&&S.length&&(e=S),C(b(e)),A(y),null}let{forceRender:E}=(0,i.useContext)(n.L);return(0,a.jsx)(a.Fragment,{children:j.map(e=>{let i=f(e),n=(!u||!!v)&&(y===j||w.includes(i));return(0,a.jsx)(g,{isPresent:n,initial:(!I.current||!!r)&&void 0,custom:t,presenceAffectsLayout:d,mode:c,root:m,onExitComplete:n?void 0:()=>{if(!k.has(i))return;k.set(i,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(E?.(),C(N.current),u&&x?.(),l&&l())},anchorX:p,children:e},i)})})}},89422:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},93661:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(60687);r(43210);var i=r(8730),n=r(24224),s=r(4780);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?i.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(o({variant:t}),e),...n})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,8562,7359,5555,4541,2576,1838,4987],()=>r(52387));module.exports=a})();