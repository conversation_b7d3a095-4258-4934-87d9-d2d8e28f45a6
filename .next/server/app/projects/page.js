(()=>{var e={};e.id=893,e.ids=[893],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3363:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var a=r(60687),n=r(93854),s=r(74456);function i({children:e,showHeader:t=!0,showFooter:r=!0,constrainHeight:i=!1}){return(0,a.jsxs)("div",{className:`${i?"h-screen":"min-h-screen"} flex flex-col bg-background`,children:[t&&(0,a.jsx)(s.<PERSON>,{}),(0,a.jsx)("main",{className:`flex-1 ${i?"min-h-0":""}`,children:e}),r&&(0,a.jsx)(n<PERSON>,{})]})}function o({children:e}){return(0,a.jsx)(i,{showFooter:!1,constrainHeight:!0,children:(0,a.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:e})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26794:(e,t,r)=>{Promise.resolve().then(r.bind(r,32417))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32417:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},43229:(e,t,r)=>{"use strict";r.d(t,{C:()=>i});let a="siift_access_token",n="siift_refresh_token",s="siift_user";class i{static setTokens(e,t=!1){let r=t?localStorage:sessionStorage;r.setItem(a,e.accessToken),e.refreshToken&&r.setItem(n,e.refreshToken)}static getAccessToken(){return localStorage.getItem(a)||sessionStorage.getItem(a)}static getRefreshToken(){return localStorage.getItem(n)||sessionStorage.getItem(n)}static setUser(e,t=!1){(t?localStorage:sessionStorage).setItem(s,JSON.stringify(e))}static getUser(){try{let e=localStorage.getItem(s)||sessionStorage.getItem(s);if(!e)return null;let t=JSON.parse(e);return t.createdAt&&"string"==typeof t.createdAt&&(t.createdAt=new Date(t.createdAt)),t.updatedAt&&"string"==typeof t.updatedAt&&(t.updatedAt=new Date(t.updatedAt)),t}catch(e){return console.error("Error parsing user data:",e),null}}static clearSession(){[localStorage,sessionStorage].forEach(e=>{e.removeItem(a),e.removeItem(n),e.removeItem(s)})}static clearInvalidSession(){let e=this.getAccessToken();e&&(!e.includes(".")||3!==e.split(".").length)&&(console.log("Clearing invalid token format"),this.clearSession())}static isAuthenticated(){return!!this.getAccessToken()}static getAuthHeaders(){let e=this.getAccessToken();return e?{Authorization:`Bearer ${e}`}:{}}}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>i,wL:()=>c});var a=r(60687);r(43210);var n=r(4780);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},57055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var a=r(60687),n=r(43210),s=r(85814),i=r.n(s),o=r(3363),l=r(29523),d=r(89667),c=r(96834),u=r(44493),h=r(62185),m=r(41862),f=r(96474),g=r(99270),p=r(80462),x=r(93661);let v=Symbol.for("constructDateFrom");function b(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&v in e?e[v](t):e instanceof Date?new e.constructor(t):new Date(t)}let y={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function j(e){return (t={})=>{let r=t.width?String(t.width):e.defaultWidth;return e.formats[r]||e.formats[e.defaultWidth]}}let w={date:j({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:j({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:j({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},k={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function M(e){return(t,r)=>{let a;if("formatting"===(r?.context?String(r.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,n=r?.width?String(r.width):t;a=e.formattingValues[n]||e.formattingValues[t]}else{let t=e.defaultWidth,n=r?.width?String(r.width):e.defaultWidth;a=e.values[n]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}function N(e){return(t,r={})=>{let a,n=r.width,s=n&&e.matchPatterns[n]||e.matchPatterns[e.defaultMatchWidth],i=t.match(s);if(!i)return null;let o=i[0],l=n&&e.parsePatterns[n]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(l)?function(e,t){for(let r=0;r<e.length;r++)if(t(e[r]))return r}(l,e=>e.test(o)):function(e,t){for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return r}(l,e=>e.test(o));return a=e.valueCallback?e.valueCallback(d):d,{value:a=r.valueCallback?r.valueCallback(a):a,rest:t.slice(o.length)}}}let P={code:"en-US",formatDistance:(e,t,r)=>{let a,n=y[e];if(a="string"==typeof n?n:1===t?n.one:n.other.replace("{{count}}",t.toString()),r?.addSuffix)if(r.comparison&&r.comparison>0)return"in "+a;else return a+" ago";return a},formatLong:w,formatRelative:(e,t,r,a)=>k[e],localize:{ordinalNumber:(e,t)=>{let r=Number(e),a=r%100;if(a>20||a<10)switch(a%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},era:M({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:M({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:M({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:M({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:M({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(t,r={})=>{let a=t.match(e.matchPattern);if(!a)return null;let n=a[0],s=t.match(e.parsePattern);if(!s)return null;let i=e.valueCallback?e.valueCallback(s[0]):s[0];return{value:i=r.valueCallback?r.valueCallback(i):i,rest:t.slice(n.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:N({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:N({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:N({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:N({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:N({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},S={};function A(e,t){return b(t||e,e)}function D(e){let t=A(e),r=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return r.setUTCFullYear(t.getFullYear()),e-r}function T(e,...t){let r=b.bind(null,e||t.find(e=>"object"==typeof e));return t.map(r)}function C(e,t){let r=A(e)-A(t);return r<0?-1:r>0?1:r}var W=r(21342);let q={active:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",completed:"bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",archived:"bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300"};function _(){let[e,t]=(0,n.useState)([]),[r,s]=(0,n.useState)([]),[v,y]=(0,n.useState)(!0),[j,w]=(0,n.useState)(null),[k,M]=(0,n.useState)(""),[N,_]=(0,n.useState)("all"),O=async e=>{if(confirm("Are you sure you want to delete this project?"))try{await h.Z.deleteProject(e),t(t=>t.filter(t=>t.id!==e))}catch(e){alert(e.message||"Failed to delete project")}};return v?(0,a.jsx)(o.N,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading projects..."})]})})}):j?(0,a.jsx)(o.N,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:j}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"text-primary hover:underline",children:"Try again"})]})})}):(0,a.jsx)(o.N,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Projects"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Manage and organize your projects"})]}),(0,a.jsx)(l.$,{asChild:!0,className:"border",children:(0,a.jsxs)(i(),{href:"/user-dashboard",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"New Project"]})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(d.p,{placeholder:"Search projects...",value:k,onChange:e=>M(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(W.rI,{children:[(0,a.jsx)(W.ty,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Status: ","all"===N?"All":N]})}),(0,a.jsxs)(W.SQ,{children:[(0,a.jsx)(W._2,{onClick:()=>_("all"),children:"All"}),(0,a.jsx)(W._2,{onClick:()=>_("active"),children:"Active"}),(0,a.jsx)(W._2,{onClick:()=>_("completed"),children:"Completed"}),(0,a.jsx)(W._2,{onClick:()=>_("archived"),children:"Archived"})]})]})]}),0===r.length?(0,a.jsx)(u.Zp,{children:(0,a.jsx)(u.Wu,{className:"flex flex-col items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-2",children:0===e.length?"No projects yet":"No projects found"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-4",children:0===e.length?"Create your first project to get started":"Try adjusting your search or filter criteria"}),0===e.length&&(0,a.jsx)(l.$,{asChild:!0,className:"border",children:(0,a.jsxs)(i(),{href:"/user-dashboard",children:[(0,a.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Create Project"]})})]})})}):(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2 lg:grid-cols-3",children:r.map(e=>{var t,r;return(0,a.jsxs)(u.Zp,{className:"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer border",children:[(0,a.jsx)(u.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)(u.ZB,{className:"truncate",children:e.name}),(0,a.jsx)("div",{className:"flex items-center gap-2 mt-1",children:(0,a.jsx)(c.E,{variant:"secondary",className:q[e.status],children:e.status})})]}),(0,a.jsxs)(W.rI,{children:[(0,a.jsx)(W.ty,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(W.SQ,{align:"end",children:[(0,a.jsx)(W._2,{asChild:!0,children:(0,a.jsx)(i(),{href:`/projects/${e.id}`,children:"View Details"})}),(0,a.jsx)(W._2,{asChild:!0,children:(0,a.jsx)(i(),{href:`/projects/${e.id}/edit`,children:"Edit"})}),(0,a.jsx)(W._2,{onClick:()=>O(e.id),className:"text-destructive",children:"Delete"})]})]})]})}),(0,a.jsxs)(u.Wu,{children:[(0,a.jsx)(u.BT,{className:"line-clamp-3 mb-4",children:e.description}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Updated"," ",(t=new Date(e.updatedAt),r={addSuffix:!0},function(e,t,r){let a,n=r?.locale??S.locale??P,s=C(e,t);if(isNaN(s))throw RangeError("Invalid time value");let i=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:s}),[o,l]=T(r?.in,...s>0?[t,e]:[e,t]),d=function(e,t,r){var a;return(a=void 0,e=>{let t=(a?Math[a]:Math.trunc)(e);return 0===t?0:t})((A(e)-A(t))/1e3)}(l,o),c=Math.round((d-(D(l)-D(o))/1e3)/60);if(c<2)if(r?.includeSeconds)if(d<5)return n.formatDistance("lessThanXSeconds",5,i);else if(d<10)return n.formatDistance("lessThanXSeconds",10,i);else if(d<20)return n.formatDistance("lessThanXSeconds",20,i);else if(d<40)return n.formatDistance("halfAMinute",0,i);else if(d<60)return n.formatDistance("lessThanXMinutes",1,i);else return n.formatDistance("xMinutes",1,i);else if(0===c)return n.formatDistance("lessThanXMinutes",1,i);else return n.formatDistance("xMinutes",c,i);if(c<45)return n.formatDistance("xMinutes",c,i);if(c<90)return n.formatDistance("aboutXHours",1,i);if(c<1440){let e=Math.round(c/60);return n.formatDistance("aboutXHours",e,i)}if(c<2520)return n.formatDistance("xDays",1,i);else if(c<43200){let e=Math.round(c/1440);return n.formatDistance("xDays",e,i)}else if(c<86400)return a=Math.round(c/43200),n.formatDistance("aboutXMonths",a,i);if((a=function(e,t,r){let[a,n,s]=T(void 0,e,e,t),i=C(n,s),o=Math.abs(function(e,t,r){let[a,n]=T(void 0,e,t);return 12*(a.getFullYear()-n.getFullYear())+(a.getMonth()-n.getMonth())}(n,s));if(o<1)return 0;1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-i*o);let l=C(n,s)===-i;(function(e,t){let r=A(e,void 0);return+function(e,t){let r=A(e,t?.in);return r.setHours(23,59,59,999),r}(r,void 0)==+function(e,t){let r=A(e,t?.in),a=r.getMonth();return r.setFullYear(r.getFullYear(),a+1,0),r.setHours(23,59,59,999),r}(r,t)})(a)&&1===o&&1===C(a,s)&&(l=!1);let d=i*(o-l);return 0===d?0:d}(l,o))<12){let e=Math.round(c/43200);return n.formatDistance("xMonths",e,i)}{let e=a%12,t=Math.trunc(a/12);return e<3?n.formatDistance("aboutXYears",t,i):e<9?n.formatDistance("overXYears",t,i):n.formatDistance("almostXYears",t+1,i)}}(t,b(t,Date.now()),r))]})]})]},e.id)})})]})})}},62185:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var a=r(43229);class n{async request(e,t={}){let r=`/api${e}`,n={headers:{"Content-Type":"application/json",...a.C.getAuthHeaders(),...t.headers},...t};try{let e=await fetch(r,n);if(!e.ok){let t=await e.json().catch(()=>({}));throw Error(t.message||`HTTP error! status: ${e.status}`)}let t=await e.json();if(t.success&&void 0!==t.data)return t.data;return t}catch(t){throw console.error(`API request failed: ${e}`,t),t}}async login(e){return this.request("/auth/signin",{method:"POST",body:JSON.stringify(e)})}async register(e){return this.request("/auth/signup",{method:"POST",body:JSON.stringify(e)})}async refreshToken(e){return this.request("/auth/refresh",{method:"POST",body:JSON.stringify({refreshToken:e})})}async verifyToken(e){try{return await this.request("/auth/verify",{method:"POST",headers:{Authorization:`Bearer ${e}`}}),!0}catch{return!1}}async logout(){return this.request("/auth/logout",{method:"POST"})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/profile",{method:"PUT",body:JSON.stringify(e)})}async getProjects(){return this.request("/projects")}async getProject(e){return this.request(`/projects/${e}`)}async createProject(e){return this.request("/projects",{method:"POST",body:JSON.stringify(e)})}async updateProject(e,t){return this.request(`/projects/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteProject(e){return this.request(`/projects/${e}`,{method:"DELETE"})}}let s=new n},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66546:(e,t,r)=>{Promise.resolve().then(r.bind(r,57055))},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var a=r(60687);r(43210);var n=r(4780);function s({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},89905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>h,tree:()=>d});var a=r(65239),n=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32417)),"/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/projects/page",pathname:"/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},93661:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(60687);r(43210);var n=r(8730),s=r(24224),i=r(4780);let o=(0,s.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...s}){let l=r?n.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...s})}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,4181,1838,4336,2403],()=>r(89905));module.exports=a})();