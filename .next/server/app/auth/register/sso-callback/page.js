(()=>{var e={};e.id=3953,e.ids=[3953],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6645:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,dynamic:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx","dynamic"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx","default")},7350:(e,t,r)=>{Promise.resolve().then(r.bind(r,22283))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22283:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>i});var s=r(60687),a=r(14792);let i="force-dynamic";function n(){return(0,s.jsx)(a.B$,{})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44302:(e,t,r)=>{Promise.resolve().then(r.bind(r,6645))},55237:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["register",{children:["sso-callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6645)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96394)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/register/sso-callback/page",pathname:"/auth/register/sso-callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},96394:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>a,metadata:()=>n,revalidate:()=>i});var s=r(37413);let a="force-dynamic",i=0,n={title:"Authentication - Siift",description:"Sign in or create an account to access Siift"};function o({children:e}){return(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 dark:from-[#166534]/10 dark:via-transparent dark:to-[#22c55e]/10"}),(0,s.jsx)("div",{className:"relative z-10",children:e})]})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,1838],()=>r(55237));module.exports=s})();