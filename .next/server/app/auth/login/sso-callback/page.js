(()=>{var e={};e.id=5029,e.ids=[5029],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43277:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,dynamic:()=>n});var a=r(60687),s=r(14792);let n="force-dynamic";function i(){return(0,a.jsx)(s.B$,{})}},46184:(e,t,r)=>{Promise.resolve().then(r.bind(r,83783))},54447:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=r(65239),s=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["login",{children:["sso-callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83783)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96394)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/login/sso-callback/page",pathname:"/auth/login/sso-callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},83783:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>s});var a=r(12907);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx","dynamic"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx","default")},93384:(e,t,r)=>{Promise.resolve().then(r.bind(r,43277))},96394:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>s,metadata:()=>i,revalidate:()=>n});var a=r(37413);let s="force-dynamic",n=0,i={title:"Authentication - Siift",description:"Sign in or create an account to access Siift"};function o({children:e}){return(0,a.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 dark:from-[#166534]/10 dark:via-transparent dark:to-[#22c55e]/10"}),(0,a.jsx)("div",{className:"relative z-10",children:e})]})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[80,4999,8360,1838],()=>r(54447));module.exports=a})();