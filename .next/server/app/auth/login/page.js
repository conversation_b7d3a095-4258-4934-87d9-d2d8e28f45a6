(()=>{var e={};e.id=4859,e.ids=[4859],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,Dc:()=>c,TL:()=>n});var s=r(43210),a=r(98599),i=r(60687);function n(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...i}=e;if(s.isValidElement(r)){var n;let e,o,l=(n=r,(o=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),c=function(e,t){let r={...t};for(let s in t){let a=e[s],i=t[s];/^on[A-Z]/.test(s)?a&&i?r[s]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...i}:"className"===s&&(r[s]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==s.Fragment&&(c.ref=t?(0,a.t)(t,l):l),s.cloneElement(r,c)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...n}=e,o=s.Children.toArray(a),l=o.find(d);if(l){let e=l.props.children,a=o.map(t=>t!==l?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...n,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...n,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var o=n("Slot"),l=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function d(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>o});var s=r(43210),a=r(51215),i=r(8730),n=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...i,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function l(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21181:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81351)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96394)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(49384);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:o}=t,l=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let i=a(t)||a(s);return n[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...c}[t]):({...o,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(60687),a=r(8730),i=r(24224);r(43210);var n=r(4780);let o=(0,i.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:i=!1,...l}){let c=i?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},35950:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var s=r(60687);r(43210);var a=r(62369),i=r(4780);function n({className:e,orientation:t="horizontal",decorative:r=!0,...n}){return(0,s.jsx)(a.b,{"data-slot":"separator",decorative:r,orientation:t,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...n})}},38885:(e,t,r)=>{"use strict";r.d(t,{Zd:()=>c,j9:()=>l});var s=r(8693),a=r(54050),i=r(95450),n=r(50582),o=r(52581);function l(){let{userId:e,getToken:t}=(0,i.P)();(0,s.jE)();let r=function(){let{userId:e,getToken:t}=(0,i.P)(),r=(0,s.jE)();return(0,a.n)({mutationFn:async({internalUserId:r,userData:s})=>{let a=await t(),i=await fetch(`http://localhost:3000/api/users/${r}`,{method:"PATCH",headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}},body:JSON.stringify({...s,clerkId:e})});if(!i.ok){let e=await i.text();throw Error(`Failed to update user: ${i.status} ${e}`)}return i.json()},onMutate:async({userData:t})=>{await r.cancelQueries({queryKey:n.lH.user(e)});let s=r.getQueryData(n.lH.user(e));if(s){let a={...s,...t,updatedAt:new Date().toISOString()};r.setQueryData(n.lH.user(e),a)}return{previousUser:s}},onError:(t,s,a)=>{a?.previousUser&&r.setQueryData(n.lH.user(e),a.previousUser),console.error("Failed to update user:",t),o.oR.error("Failed to update profile. Please try again.")},onSettled:()=>{n.WG.user(e)},onSuccess:e=>{o.oR.success("Profile updated successfully!"),console.log("User updated successfully:",e)}})}();return(0,a.n)({mutationFn:async s=>{let a=await t(),i=await fetch(`http://localhost:3000/api/users/clerk/${e}`,{headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}}});if(!i.ok)throw Error("User not found in backend. Please sync your account first.");let n=await i.json();return r.mutateAsync({internalUserId:n.id,userData:s})},onError:e=>{console.error("Failed to update user by Clerk ID:",e),o.oR.error(e.message||"Failed to update profile. Please try again.")}})}function c(){let{user:e,userId:t,email:r,firstName:l,lastName:c}=(0,i.P)(),d=function(){let{getToken:e}=(0,i.P)(),t=(0,s.jE)();return(0,a.n)({mutationFn:async t=>{let r=await e(),s="http://localhost:3000",a=await fetch(`${s}/api/users`,{method:"POST",headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}},body:JSON.stringify({...t,role:t.role||"user",status:t.status||"active",timezone:t.timezone||"UTC",preferences:t.preferences||{notifications:!0,theme:"system",language:"en"}})});if(!a.ok){let e=await a.text();if(console.error("Backend response error:",{status:a.status,statusText:a.statusText,errorText:e,url:`${s}/api/users`}),400===a.status&&(e.includes("already exists")||e.includes("clerk ID already exists"))){console.log("User already exists, fetching existing user...");let e=await fetch(`${s}/api/users/clerk/${t.clerkId}`,{headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}}});if(e.ok){let t=await e.json();return console.log("Found existing user:",t),t}throw console.log("Could not fetch existing user, but user exists in backend"),Error("User already exists in backend")}throw Error(`Failed to create user: ${a.status} ${a.statusText} - ${e}`)}return a.json()},onSuccess:(e,r)=>{t.setQueryData(n.lH.user(r.clerkId),e),n.WG.user(r.clerkId),o.oR.success("Account synced successfully!"),console.log("User synced successfully in backend:",e)},onError:(e,t)=>{console.error("Failed to create user in backend:",e),console.error("User data that failed:",t),e.message&&e.message.includes("already exists")?console.log("User already exists in backend - this is expected"):o.oR.error(`Failed to sync account: ${e.message}`)}})}();return(0,a.n)({mutationFn:async()=>{if(!e||!t)throw Error("User not available");let s={email:r||"",firstName:l||"",lastName:c||"",clerkId:t,role:"user",status:"active",avatarUrl:e.imageUrl||"",bio:"",timezone:"UTC",preferences:{notifications:!0,theme:"system",language:"en"}};return d.mutateAsync(s)},onError:e=>{console.error("Failed to sync user to backend:",e),o.oR.error("Failed to sync account. Please contact support.")}})}},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>d});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},49457:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_,dynamic:()=>P});var s=r(60687),a=r(14792),i=r(558),n=r(41550),o=r(64021),l=r(12597),c=r(13861),d=r(41862),u=r(32192),h=r(85814),p=r.n(h),m=r(16189),f=r(43210),g=r(27605),v=r(43214),x=r(88054),b=r(29523),y=r(89667),w=r(54300),j=r(35950),k=r(52581),N=r(38885),C=r(70042);let P="force-dynamic",S=v.Ik({email:v.Yj().email("Please enter a valid email address"),password:v.Yj().min(1,"Password is required")});function _(){let{isLoaded:e,signIn:t,setActive:r}=(0,a.go)(),[h,v]=(0,f.useState)(!1),[P,_]=(0,f.useState)(!1),E=(0,m.useRouter)(),R=(0,N.Zd)(),{register:A,handleSubmit:U,formState:{errors:M}}=(0,g.mN)({resolver:(0,i.u)(S)}),O=async s=>{if(e){v(!0);try{let e=await t.create({identifier:s.email,password:s.password});if("complete"===e.status){await r({session:e.createdSessionId});try{console.log("Checking/syncing user to backend..."),await R.mutateAsync(),console.log("User sync check completed")}catch(e){console.warn("User sync check failed (user might already exist):",e)}k.oR.success("Welcome back!"),E.push("/user-dashboard")}else console.error("Sign in not complete:",e),k.oR.error("Sign in failed. Please try again.")}catch(t){console.error("Sign in error:",t);let e="Sign in failed. Please try again.";if(t.errors&&t.errors.length>0){let r=t.errors[0];switch(r.code){case"form_identifier_not_found":e="No account found with this email. Please check your email or sign up.";break;case"form_password_incorrect":e="Incorrect password. Please try again or reset your password.";break;case"form_identifier_exists":e="Please complete your account setup or try signing in.";break;case"session_exists":e="You're already signed in. Redirecting...",setTimeout(()=>E.push("/user-dashboard"),1e3);break;case"too_many_requests":e="Too many sign in attempts. Please wait a few minutes and try again.";break;default:r.message&&(e=r.message)}}k.oR.error(e)}finally{v(!1)}}},z=async r=>{if(e)try{await t.authenticateWithRedirect({strategy:r,redirectUrl:"/sso-callback",redirectUrlComplete:"/user-dashboard"})}catch(t){console.error("Social sign in error:",t);let e="Social sign in failed. Please try again.";if(t.errors&&t.errors.length>0){let r=t.errors[0];switch(r.code){case"oauth_access_denied":e="Access was denied. Please try again and allow the necessary permissions.";break;case"oauth_email_domain_reserved_by_saml":e="This email domain requires SAML authentication. Please contact your administrator.";break;case"external_account_exists":e="An account with this email already exists. Please try signing in instead.";break;default:r.message&&(e=r.message)}}k.oR.error(e)}};return(0,s.jsxs)("div",{className:"min-h-screen flex flex-col items-center justify-center",children:[(0,s.jsxs)(x.V,{title:"Welcome back",description:"Sign in to your account to continue",footer:(0,s.jsxs)("div",{className:"text-center text-sm text-muted-foreground",children:["Don't have an account?"," ",(0,s.jsx)(p(),{href:"/auth/register",className:"text-primary hover:text-primary/90 font-medium",children:"Sign up"})]}),children:[(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)(C.V,{type:"button",layout:"horizontal",hoverColor:"green",hoverScale:!0,showBorder:!0,borderClassName:"grey border-1",variant:"outline",className:"w-full",onClick:()=>z("oauth_google"),disabled:!e,children:[(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]})}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)(j.w,{className:"w-full"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-xs uppercase",children:(0,s.jsx)("span",{className:"bg-background px-2 text-muted-foreground",children:"Or continue with email"})})]}),(0,s.jsxs)("form",{onSubmit:U(O),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"email",children:"Email address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(y.p,{id:"email",type:"email",placeholder:"Enter your email",...A("email"),className:`pl-10 ${M.email?"border-destructive":""}`})]}),M.email&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:M.email.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(y.p,{id:"password",type:P?"text":"password",placeholder:"Enter your password",...A("password"),className:`pl-10 pr-10 ${M.password?"border-destructive":""}`}),(0,s.jsx)("button",{type:"button",onClick:()=>_(!P),className:"absolute right-3 top-1.5 h-4 w-4 text-muted-foreground hover:text-foreground",children:P?(0,s.jsx)(l.A,{}):(0,s.jsx)(c.A,{})})]}),M.password&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:M.password.message})]}),(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)(p(),{href:"/auth/forgot-password",className:"text-sm text-primary hover:text-primary/90",children:"Forgot password?"})}),(0,s.jsx)(b.$,{type:"submit",className:"w-full",disabled:h||!e,children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign in"})]})]}),(0,s.jsx)("div",{className:"text-center mt-6",children:(0,s.jsxs)(p(),{href:"/",className:"inline-flex items-center gap-2 text-sm text-muted-foreground hover:text-foreground transition-colors",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),"Back to home"]})})]})}},54050:(e,t,r)=>{"use strict";r.d(t,{n:()=>d});var s=r(43210),a=r(65406),i=r(33465),n=r(35536),o=r(31212),l=class extends n.Q{#e;#t=void 0;#r;#s;constructor(e,t){super(),this.#e=e,this.setOptions(t),this.bindMethods(),this.#a()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#e.defaultMutationOptions(e),(0,o.f8)(this.options,t)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#r,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,o.EN)(t.mutationKey)!==(0,o.EN)(this.options.mutationKey)?this.reset():this.#r?.state.status==="pending"&&this.#r.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#r?.removeObserver(this)}onMutationUpdate(e){this.#a(),this.#i(e)}getCurrentResult(){return this.#t}reset(){this.#r?.removeObserver(this),this.#r=void 0,this.#a(),this.#i()}mutate(e,t){return this.#s=t,this.#r?.removeObserver(this),this.#r=this.#e.getMutationCache().build(this.#e,this.options),this.#r.addObserver(this),this.#r.execute(e)}#a(){let e=this.#r?.state??(0,a.$)();this.#t={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#i(e){i.jG.batch(()=>{if(this.#s&&this.hasListeners()){let t=this.#t.variables,r=this.#t.context;e?.type==="success"?(this.#s.onSuccess?.(e.data,t,r),this.#s.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#s.onError?.(e.error,t,r),this.#s.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#t)})})}},c=r(8693);function d(e,t){let r=(0,c.jE)(t),[a]=s.useState(()=>new l(r,e));s.useEffect(()=>{a.setOptions(e)},[a,e]);let n=s.useSyncExternalStore(s.useCallback(e=>a.subscribe(i.jG.batchCalls(e)),[a]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),d=s.useCallback((e,t)=>{a.mutate(e,t).catch(o.lQ)},[a]);if(n.error&&(0,o.GU)(a.options.throwOnError,[n.error]))throw n.error;return{...n,mutate:d,mutateAsync:n.mutate}}},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>l});var s=r(60687),a=r(43210),i=r(14163),n=a.forwardRef((e,t)=>(0,s.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=r(4780);function l({className:e,...t}){return(0,s.jsx)(n,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},62369:(e,t,r)=>{"use strict";r.d(t,{b:()=>c});var s=r(43210),a=r(14163),i=r(60687),n="horizontal",o=["horizontal","vertical"],l=s.forwardRef((e,t)=>{var r;let{decorative:s,orientation:l=n,...c}=e,d=(r=l,o.includes(r))?l:n;return(0,i.jsx)(a.sG.div,{"data-orientation":d,...s?{role:"none"}:{"aria-orientation":"vertical"===d?d:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},64098:(e,t,r)=>{Promise.resolve().then(r.bind(r,49457))},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81351:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,dynamic:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx","dynamic"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx","default")},88054:(e,t,r)=>{"use strict";r.d(t,{V:()=>n});var s=r(60687),a=r(44493),i=r(23166);r(43210);let n=({title:e,description:t,children:r,footer:n,header:o})=>(0,s.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,s.jsx)("div",{className:"pt-8",children:(0,s.jsx)(i.g,{size:40,animated:!1,href:"/"})}),(0,s.jsxs)(a.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(a.aR,{className:"space-y-1",children:[o,(0,s.jsx)(a.ZB,{className:"text-2xl font-bold text-center",children:e}),t&&(0,s.jsx)(a.BT,{className:"text-center",children:t})]}),(0,s.jsx)(a.Wu,{className:"space-y-4",children:r}),n&&(0,s.jsx)(a.wL,{children:n})]})]})},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},95450:(e,t,r)=>{"use strict";r.d(t,{P:()=>o});var s=r(41330),a=r(14792),i=r(16189),n=r(52581);function o(){let{isSignedIn:e,signOut:t,getToken:r}=(0,s.d)(),{user:o,isLoaded:l}=(0,a.Jd)(),{signIn:c,isLoaded:d}=(0,a.go)(),{signUp:u,isLoaded:h}=(0,a.yC)(),p=(0,i.useRouter)();return{isSignedIn:e,isLoaded:l,user:o,signOut:async()=>{try{await t(),n.oR.success("Signed out successfully"),p.push("/")}catch(e){n.oR.error("Failed to sign out"),console.error("Sign out error:",e)}},getToken:async()=>{try{return await r()}catch(e){return console.error("Error getting Clerk token:",e),null}},signIn:c,signUp:u,signInLoaded:d,signUpLoaded:h,userId:o?.id,email:o?.emailAddresses[0]?.emailAddress,firstName:o?.firstName,lastName:o?.lastName,fullName:o?.fullName,imageUrl:o?.imageUrl,isEmailVerified:o?.emailAddresses[0]?.verification?.status==="verified"}}},95642:(e,t,r)=>{Promise.resolve().then(r.bind(r,81351))},96394:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>a,metadata:()=>n,revalidate:()=>i});var s=r(37413);let a="force-dynamic",i=0,n={title:"Authentication - Siift",description:"Sign in or create an account to access Siift"};function o({children:e}){return(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 dark:from-[#166534]/10 dark:via-transparent dark:to-[#22c55e]/10"}),(0,s.jsx)("div",{className:"relative z-10",children:e})]})}},96487:()=>{},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>n,t:()=>i});var s=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function n(...e){return s.useCallback(i(...e),e)}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,2604,1838],()=>r(21181));module.exports=s})();