(()=>{var e={};e.id=3270,e.ids=[3270],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,Dc:()=>d,TL:()=>n});var a=r(43210),i=r(98599),s=r(60687);function n(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...s}=e;if(a.isValidElement(r)){var n;let e,o,l=(n=r,(o=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(o=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,t){let r={...t};for(let a in t){let i=e[a],s=t[a];/^on[A-Z]/.test(a)?i&&s?r[a]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[a]=i):"style"===a?r[a]={...i,...s}:"className"===a&&(r[a]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==a.Fragment&&(d.ref=t?(0,i.t)(t,l):l),a.cloneElement(r,d)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:i,...n}=e,o=a.Children.toArray(i),l=o.find(c);if(l){let e=l.props.children,i=o.map(t=>t!==l?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...n,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,i):null})}return(0,s.jsx)(t,{...n,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var o=n("Slot"),l=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var a=r(49384);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=a.$,n=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:o}=t,l=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],a=null==o?void 0:o[e];if(null===t)return null;let s=i(t)||i(a);return n[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return s(e,l,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...d}[t]):({...o,...d})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var a=r(60687),i=r(8730),s=r(24224);r(43210);var n=r(4780);let o=(0,s.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:s=!1,...l}){let d=s?i.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,n.cn)(o({variant:t,size:r,className:e})),...l})}},29852:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,dynamic:()=>i});var a=r(12907);let i=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx","dynamic"),s=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx","default")},30449:(e,t,r)=>{Promise.resolve().then(r.bind(r,29852))},33873:e=>{"use strict";e.exports=require("path")},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>s,aR:()=>n,wL:()=>c});var a=r(60687);r(43210);var i=r(4780);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,i.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},59935:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>f,tree:()=>d});var a=r(65239),i=r(48088),s=r(88170),n=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29852)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96394)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},f=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/auth/verify-email/page",pathname:"/auth/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67074:(e,t,r)=>{"use strict";r.d(t,{GF:()=>i,Ni:()=>s});var a=r(52581);let i=(e,t)=>{a.oR.success(e,{description:t?.description,duration:t?.duration||4e3})},s=(e,t)=>{a.oR.error(e,{description:t?.description,duration:t?.duration||5e3})}},73024:e=>{"use strict";e.exports=require("node:fs")},74994:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>k,dynamic:()=>y});var a=r(60687),i=r(558),s=r(99891),n=r(41550),o=r(28559),l=r(41862),d=r(85814),c=r.n(d),u=r(16189),f=r(43210),p=r(27605),m=r(43214),h=r(88054),g=r(29523),x=r(89667),v=r(87979),b=r(67074);let y="force-dynamic",w=m.Ik({code:m.Yj().min(6,"Verification code must be 6 digits").max(6,"Verification code must be 6 digits")});function j(){let[e,t]=(0,f.useState)(["","","","","",""]),[r,d]=(0,f.useState)(!1),[m,y]=(0,f.useState)(0),[j,k]=(0,f.useState)(!1),N=(0,u.useRouter)(),C=(0,u.useSearchParams)().get("email")||"",{verifyEmail:E,resendEmailVerification:A,isLoading:_,clearError:P,emailVerificationSent:D}=(0,v.A)(),{register:S,handleSubmit:V,formState:{errors:R},setValue:F,reset:q}=(0,p.mN)({resolver:(0,i.u)(w),defaultValues:{code:""}}),z=async e=>{P(),k(!0);try{await E(C,e.code),d(!0),(0,b.GF)("Email verified successfully!",{description:"Redirecting to dashboard..."}),setTimeout(()=>{N.push("/dashboard")},2e3)}catch(t){let e=t instanceof Error?t.message:"Verification failed";(0,b.Ni)("Verification failed",{description:e}),console.error("Email verification failed:",t)}finally{k(!1)}},$=async()=>{if(!(m>0)&&C){P(),y(60);try{await A(C),(0,b.GF)("Verification code sent!",{description:"Check your email for the new code"})}catch(t){let e=t instanceof Error?t.message:"Failed to resend code";(0,b.Ni)("Failed to resend code",{description:e}),console.error("Failed to resend verification code:",t),y(0)}}};return r?(0,a.jsx)(h.V,{title:"Email verified successfully!",header:(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(s.A,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Your email has been verified. You now have full access to your account."})}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Redirecting to dashboard..."}),(0,a.jsx)(c(),{href:"/dashboard",className:"font-medium text-primary hover:underline",children:"Continue to dashboard"})]})}):(0,a.jsx)(h.V,{title:"Verify your email",description:`Enter the verification code sent to ${C}`,header:(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(n.A,{className:"h-6 w-6 text-blue-600 dark:text-blue-400"})}),footer:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["Didn't receive the code?"," ",(0,a.jsx)(g.$,{variant:"link",className:"px-0 font-normal",onClick:$,disabled:_||m>0||!C,children:m>0?`Resend in ${m}s`:"Resend code"})]}),(0,a.jsx)("div",{className:"flex w-full",children:(0,a.jsxs)(c(),{href:"/auth/login",className:"flex items-center text-sm font-medium text-primary hover:underline w-full pl-0",style:{marginLeft:0},children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]}),children:(0,a.jsxs)("form",{onSubmit:V(z),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Enter the 6-digit code sent to your email"}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:Array.from({length:6}).map((r,i)=>(0,a.jsx)(x.p,{type:"text",inputMode:"numeric",maxLength:1,value:e[i],autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:!1,className:`w-12 h-12 text-center text-lg font-semibold ${R.code?"border-destructive":""}`,onChange:r=>{let a=r.target.value.replace(/\D/g,""),s=[...e];if(s[i]=a,t(s),F("code",s.join("")),i<5&&a){let e=r.target.parentElement?.children[i+1];e?.focus()}},onKeyDown:r=>{if("Backspace"===r.key){if(!r.currentTarget.value&&i>0){let e=r.target.parentElement?.children[i-1];e?.focus()}else if(r.currentTarget.value){let r=[...e];r[i]="",t(r),F("code",r.join(""))}}},onPaste:e=>{e.preventDefault();let r=e.clipboardData.getData("text").replace(/\D/g,"");if(r.length<=6){t(r.split("").concat(Array(6).fill("")).slice(0,6)),F("code",r);let a=Math.min(r.length,5),i=e.target.parentElement?.children[a];i?.focus()}}},i))}),(0,a.jsx)("input",{type:"hidden",...S("code")}),R.code&&(0,a.jsx)("p",{className:"text-sm text-destructive text-center",children:R.code.message})]}),(0,a.jsx)(g.$,{type:"submit",className:"w-full",disabled:j,children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Verifying..."]}):"Verify Email"})]})})}function k(){return(0,a.jsx)(f.Suspense,{fallback:(0,a.jsx)("div",{children:"Loading..."}),children:(0,a.jsx)(j,{})})}},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},87979:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(78941),i=r(16189),s=r(43210),n=r(67074);function o(){let{user:e,isAuthenticated:t,error:r,pendingEmailVerification:o,emailVerificationSent:l,actions:d}=(0,a.B)(),c=(0,i.useRouter)(),[u,f]=(0,s.useState)(!1),p=(0,s.useCallback)(async e=>{f(!0);try{await d.login(e);let{user:t}=a.B.getState();(0,n.GF)("Welcome back!",{description:`Logged in as ${t?.email}`}),t?.role==="admin"?c.replace("/admin"):c.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Login failed";throw(0,n.Ni)("Login Failed",{description:e}),t}finally{f(!1)}},[d,c,f]),m=(0,s.useCallback)(async e=>{f(!0);try{await d.signup(e);let{user:t}=a.B.getState();(0,n.GF)("Account created successfully!",{description:`Welcome ${t?.name||t?.email}!`}),t?.role==="admin"?c.replace("/admin"):c.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Signup failed";throw(0,n.Ni)("Signup Failed",{description:e}),t}finally{f(!1)}},[d,c,f]),h=(0,s.useCallback)(()=>{d.logout(),c.push("/auth/login")},[d,c]),g=(0,s.useCallback)(()=>{d.setError(null)},[d]),x=(0,s.useCallback)(async(e,t)=>{try{await d.sendEmailVerification(e,t)}catch(e){throw e}},[d]);return{user:e,isAuthenticated:t,isLoading:u,error:r,pendingEmailVerification:o,emailVerificationSent:l,login:p,signup:m,logout:h,clearError:g,sendEmailVerification:x,verifyEmail:(0,s.useCallback)(async(e,t)=>{try{await d.verifyEmail(e,t)}catch(e){throw e}},[d]),resendEmailVerification:(0,s.useCallback)(async e=>{try{await d.resendEmailVerification(e)}catch(e){throw e}},[d]),updateUser:d.updateUser,refreshToken:d.refreshToken}}},88054:(e,t,r)=>{"use strict";r.d(t,{V:()=>n});var a=r(60687),i=r(44493),s=r(23166);r(43210);let n=({title:e,description:t,children:r,footer:n,header:o})=>(0,a.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,a.jsx)("div",{className:"pt-8",children:(0,a.jsx)(s.g,{size:40,animated:!1,href:"/"})}),(0,a.jsxs)(i.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,a.jsxs)(i.aR,{className:"space-y-1",children:[o,(0,a.jsx)(i.ZB,{className:"text-2xl font-bold text-center",children:e}),t&&(0,a.jsx)(i.BT,{className:"text-center",children:t})]}),(0,a.jsx)(i.Wu,{className:"space-y-4",children:r}),n&&(0,a.jsx)(i.wL,{children:n})]})]})},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>s});var a=r(60687);r(43210);var i=r(4780);function s({className:e,type:t,...r}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},95601:(e,t,r)=>{Promise.resolve().then(r.bind(r,74994))},96394:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>i,metadata:()=>n,revalidate:()=>s});var a=r(37413);let i="force-dynamic",s=0,n={title:"Authentication - Siift",description:"Sign in or create an account to access Siift"};function o({children:e}){return(0,a.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 dark:from-[#166534]/10 dark:via-transparent dark:to-[#22c55e]/10"}),(0,a.jsx)("div",{className:"relative z-10",children:e})]})}},96487:()=>{},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>n,t:()=>s});var a=r(43210);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,a=e.map(e=>{let a=i(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():i(e[t],null)}}}}function n(...e){return a.useCallback(s(...e),e)}},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[80,4999,8360,2604,1838],()=>r(59935));module.exports=a})();