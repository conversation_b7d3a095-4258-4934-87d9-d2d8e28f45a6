(()=>{var e={};e.id=4089,e.ids=[4089],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5697:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C,dynamic:()=>N});var s=r(60687),a=r(558),n=r(99891),i=r(28559),o=r(64021),d=r(12597),l=r(13861),c=r(41862),u=r(85814),p=r.n(u),f=r(16189),m=r(43210),x=r(27605),h=r(43214),g=r(88054),v=r(29523),b=r(89667),w=r(54300),y=r(67074),j=r(61570);let N="force-dynamic",k=h.Ik({code:h.Yj().min(6,"Verification code must be 6 digits").max(6,"Verification code must be 6 digits"),newPassword:h.Yj().min(8,"Password must be at least 8 characters"),confirmPassword:h.Yj().min(8,"Password must be at least 8 characters")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Passwords don't match",path:["confirmPassword"]});function P(){let[e,t]=(0,m.useState)(!1),[r,u]=(0,m.useState)(!1),[h,N]=(0,m.useState)(!1),[P,C]=(0,m.useState)(null),[A,E]=(0,m.useState)(!1),[_,D]=(0,m.useState)(["","","","","",""]),R=(0,f.useRouter)(),S=(0,f.useSearchParams)().get("email")||"",{register:z,handleSubmit:$,formState:{errors:V},setValue:F,reset:M}=(0,x.mN)({resolver:(0,a.u)(k),defaultValues:{code:"",newPassword:"",confirmPassword:""}}),q=async e=>{N(!0);try{await j.N.resetPassword(S,e.code,e.newPassword),(0,y.GF)("Password reset successful!",{description:"You can now log in with your new password"}),setTimeout(()=>{R.push("/auth/login")},2e3)}catch(t){let e=t instanceof Error?t.message:"Failed to reset password. Please try again.";(0,y.Ni)("Password reset failed",{description:e})}finally{N(!1)}},T=async()=>{if(S){N(!0),C(null);try{await j.N.forgotPassword(S),C("Verification code sent successfully!"),setTimeout(()=>C(null),3e3)}catch(e){C(e instanceof Error?e.message:"Failed to resend code. Please try again.")}finally{N(!1)}}};return A?(0,s.jsx)(g.V,{title:"Password reset successful!",header:(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"h-6 w-6 text-green-600 dark:text-green-400"})}),children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"You can now sign in with your new password."})}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Redirecting to login page..."}),(0,s.jsx)(p(),{href:"/auth/login",className:"font-medium text-primary hover:underline",children:"Continue to login"})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)(g.V,{title:"Reset your password",description:"Enter the verification code sent to your email address",footer:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsxs)("p",{className:"text-sm text-muted-foreground text-center",children:["Didn't receive the code?"," ",(0,s.jsx)(v.$,{variant:"link",className:"px-0 font-normal",onClick:T,disabled:h||!S,children:"Resend code"})]}),(0,s.jsx)("div",{className:"flex w-full",children:(0,s.jsxs)(p(),{href:"/auth/forgot-password",className:"flex items-center text-sm font-medium text-primary hover:underline w-full pl-0",style:{marginLeft:0},children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})})]}),children:(0,s.jsxs)("form",{onSubmit:$(q),className:"space-y-4",children:[P&&(0,s.jsx)("div",{className:`p-3 text-sm border rounded-md ${P.includes("successfully")?"text-green-700 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800":"text-destructive bg-destructive/10 border-destructive/20"}`,children:P}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground text-center",children:"Enter the 6-digit code sent to your email"}),(0,s.jsx)("div",{className:"flex justify-center gap-2",children:Array.from({length:6}).map((e,t)=>(0,s.jsx)(b.p,{type:"text",inputMode:"numeric",maxLength:1,value:_[t],autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:!1,className:`w-12 h-12 text-center text-lg font-semibold ${V.code?"border-destructive":""}`,onChange:e=>{let r=e.target.value.replace(/\D/g,""),s=[..._];if(s[t]=r,D(s),F("code",s.join("")),t<5&&r){let r=e.target.parentElement?.children[t+1];r?.focus()}},onKeyDown:e=>{if("Backspace"===e.key){if(!e.currentTarget.value&&t>0){let r=e.target.parentElement?.children[t-1];r?.focus()}else if(e.currentTarget.value){let e=[..._];e[t]="",D(e),F("code",e.join(""))}}},onPaste:e=>{e.preventDefault();let t=e.clipboardData.getData("text").replace(/\D/g,"");if(t.length<=6){D(t.split("").concat(Array(6).fill("")).slice(0,6)),F("code",t);let r=Math.min(t.length,5),s=e.target.parentElement?.children[r];s?.focus()}}},t))}),(0,s.jsx)("input",{type:"hidden",...z("code")}),V.code&&(0,s.jsx)("p",{className:"text-sm text-destructive text-center",children:V.code.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"newPassword",children:"New password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(b.p,{id:"newPassword",type:e?"text":"password",placeholder:"Enter new password",autoComplete:"new-password",...z("newPassword"),className:`pl-10 pr-10 ${V.newPassword?"border-destructive":""}`}),(0,s.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>t(!e),children:e?(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):(0,s.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})})]}),V.newPassword&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:V.newPassword.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(w.J,{htmlFor:"confirmPassword",children:"Confirm new password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(b.p,{id:"confirmPassword",type:r?"text":"password",placeholder:"Confirm new password",autoComplete:"new-password",...z("confirmPassword"),className:`pl-10 pr-10 ${V.confirmPassword?"border-destructive":""}`}),(0,s.jsx)(v.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>u(!r),children:r?(0,s.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"}):(0,s.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})})]}),V.confirmPassword&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:V.confirmPassword.message})]}),(0,s.jsx)(v.$,{type:"submit",className:"w-full",disabled:h,children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Resetting password..."]}):"Reset password"})]})})})})}function C(){return(0,s.jsx)(m.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(P,{})})}},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,Dc:()=>l,TL:()=>i});var s=r(43210),a=r(98599),n=r(60687);function i(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var i;let e,o,d=(i=r,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),l=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(l.ref=t?(0,a.t)(t,d):d),s.cloneElement(r,l)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...i}=e,o=s.Children.toArray(a),d=o.find(c);if(d){let e=d.props.children,a=o.map(t=>t!==d?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...i,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...i,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var o=i("Slot"),d=Symbol("radix.slottable");function l(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=d,t}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}},10038:(e,t,r)=>{Promise.resolve().then(r.bind(r,5697))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>o});var s=r(43210),a=r(51215),n=r(8730),i=r(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.TL)(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(a?r:t,{...n,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function d(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},17247:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,59379)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96394)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});var s=r(49384);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,i=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:o}=t,d=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],s=null==o?void 0:o[e];if(null===t)return null;let n=a(t)||a(s);return i[e][n]}),l=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,d,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...o,...l}[t]):({...o,...l})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(60687),a=r(8730),n=r(24224);r(43210);var i=r(4780);let o=(0,n.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:n=!1,...d}){let l=n?a.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},46990:(e,t,r)=>{Promise.resolve().then(r.bind(r,59379))},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>d});var s=r(60687),a=r(43210),n=r(14163),i=a.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=r(4780);function d({className:e,...t}){return(0,s.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},59379:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx","dynamic"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},67074:(e,t,r)=>{"use strict";r.d(t,{GF:()=>a,Ni:()=>n});var s=r(52581);let a=(e,t)=>{s.oR.success(e,{description:t?.description,duration:t?.duration||4e3})},n=(e,t)=>{s.oR.error(e,{description:t?.description,duration:t?.duration||5e3})}},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},88054:(e,t,r)=>{"use strict";r.d(t,{V:()=>i});var s=r(60687),a=r(44493),n=r(23166);r(43210);let i=({title:e,description:t,children:r,footer:i,header:o})=>(0,s.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,s.jsx)("div",{className:"pt-8",children:(0,s.jsx)(n.g,{size:40,animated:!1,href:"/"})}),(0,s.jsxs)(a.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(a.aR,{className:"space-y-1",children:[o,(0,s.jsx)(a.ZB,{className:"text-2xl font-bold text-center",children:e}),t&&(0,s.jsx)(a.BT,{className:"text-center",children:t})]}),(0,s.jsx)(a.Wu,{className:"space-y-4",children:r}),i&&(0,s.jsx)(a.wL,{children:i})]})]})},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},96394:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>a,metadata:()=>i,revalidate:()=>n});var s=r(37413);let a="force-dynamic",n=0,i={title:"Authentication - Siift",description:"Sign in or create an account to access Siift"};function o({children:e}){return(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 dark:from-[#166534]/10 dark:via-transparent dark:to-[#22c55e]/10"}),(0,s.jsx)("div",{className:"relative z-10",children:e})]})}},96487:()=>{},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>i,t:()=>n});var s=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function i(...e){return s.useCallback(n(...e),e)}},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,2604,1838],()=>r(17247));module.exports=s})();