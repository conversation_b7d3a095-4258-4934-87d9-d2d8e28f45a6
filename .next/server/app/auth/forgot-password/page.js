(()=>{var e={};e.id=9413,e.ids=[9413],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8730:(e,r,t)=>{"use strict";t.d(r,{DX:()=>o,Dc:()=>d,TL:()=>i});var a=t(43210),s=t(98599),n=t(60687);function i(e){let r=function(e){let r=a.forwardRef((e,r)=>{let{children:t,...n}=e;if(a.isValidElement(t)){var i;let e,o,l=(i=t,(o=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(o=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,r){let t={...r};for(let a in r){let s=e[a],n=r[a];/^on[A-Z]/.test(a)?s&&n?t[a]=(...e)=>{let r=n(...e);return s(...e),r}:s&&(t[a]=s):"style"===a?t[a]={...s,...n}:"className"===a&&(t[a]=[s,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==a.Fragment&&(d.ref=r?(0,s.t)(r,l):l),a.cloneElement(t,d)}return a.Children.count(t)>1?a.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=a.forwardRef((e,t)=>{let{children:s,...i}=e,o=a.Children.toArray(s),l=o.find(c);if(l){let e=l.props.children,s=o.map(r=>r!==l?r:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...i,ref:t,children:a.isValidElement(e)?a.cloneElement(e,void 0,s):null})}return(0,n.jsx)(r,{...i,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}var o=i("Slot"),l=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=l,r}function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>o});var a=t(43210),s=t(51215),n=t(8730),i=t(60687),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),s=a.forwardRef((e,a)=>{let{asChild:s,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(s?t:r,{...n,ref:a})});return s.displayName=`Primitive.${r}`,{...e,[r]:s}},{});function l(e,r){e&&s.flushSync(()=>e.dispatchEvent(r))}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24224:(e,r,t)=>{"use strict";t.d(r,{F:()=>i});var a=t(49384);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=a.$,i=(e,r)=>t=>{var a;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:i,defaultVariants:o}=r,l=Object.keys(i).map(e=>{let r=null==t?void 0:t[e],a=null==o?void 0:o[e];if(null===r)return null;let n=s(r)||s(a);return i[e][n]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,a]=r;return void 0===a||(e[t]=a),e},{});return n(e,l,null==r||null==(a=r.compoundVariants)?void 0:a.reduce((e,r)=>{let{class:t,className:a,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...d}[r]):({...o,...d})[r]===t})?[...e,t,a]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},27228:(e,r,t)=>{Promise.resolve().then(t.bind(t,31843))},28559:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(60687),s=t(8730),n=t(24224);t(43210);var i=t(4780);let o=(0,n.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:n=!1,...l}){let d=n?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:t,className:e})),...l})}},31843:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>j,dynamic:()=>y});var a=t(60687),s=t(558),n=t(28559),i=t(41550),o=t(41862),l=t(85814),d=t.n(l),c=t(16189),u=t(43210),p=t(27605),f=t(43214),m=t(88054),v=t(29523),g=t(89667),x=t(54300),h=t(67074),b=t(61570);let y="force-dynamic",w=f.Ik({email:f.Yj().email("Please enter a valid email address")}),j=function(){let[e,r]=(0,u.useState)(!1),t=(0,c.useRouter)(),{register:l,handleSubmit:f,formState:{errors:y},getValues:j}=(0,p.mN)({resolver:(0,s.u)(w),defaultValues:{email:""}}),k=async e=>{r(!0);try{await b.N.forgotPassword(e.email),(0,h.GF)("Reset code sent!",{description:"Check your email for the verification code"}),t.push(`/auth/reset-password?email=${encodeURIComponent(e.email)}`)}catch(r){let e=r instanceof Error?r.message:"Failed to send reset code. Please try again.";(0,h.Ni)("Failed to send reset code",{description:e})}finally{r(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(m.V,{title:"Forgot your password?",description:"Enter your email address and we'll send you a verification code to reset your password",footer:(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)(d(),{href:"/auth/login",className:"inline-flex items-center text-sm font-medium text-primary hover:underline",children:[(0,a.jsx)(n.A,{className:"mr-2 h-4 w-4"}),"Back to login"]})}),children:(0,a.jsxs)("form",{onSubmit:f(k),className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(x.J,{htmlFor:"email",children:"Email address"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-3 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(g.p,{id:"email",type:"email",placeholder:"Enter your email address",...l("email"),className:`pl-10 ${y.email?"border-destructive":""}`})]}),y.email&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:y.email.message})]}),(0,a.jsx)(v.$,{type:"submit",className:"w-full",disabled:e,children:e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Sending code..."]}):"Send verification code"})]})})})})}},33873:e=>{"use strict";e.exports=require("path")},41550:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var a=t(60687);t(43210);var s=t(4780);function n({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...r})}function i({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function o({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex px-6 [.border-t]:pt-6",e),...r})}},54300:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var a=t(60687),s=t(43210),n=t(14163),i=s.forwardRef((e,r)=>(0,a.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var o=t(4780);function l({className:e,...r}){return(0,a.jsx)(i,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67074:(e,r,t)=>{"use strict";t.d(r,{GF:()=>s,Ni:()=>n});var a=t(52581);let s=(e,r)=>{a.oR.success(e,{description:r?.description,duration:r?.duration||4e3})},n=(e,r)=>{a.oR.error(e,{description:r?.description,duration:r?.duration||5e3})}},69084:(e,r,t)=>{Promise.resolve().then(t.bind(t,81165))},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},81165:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,dynamic:()=>s});var a=t(12907);let s=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx","dynamic"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx","default")},86385:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=t(65239),s=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["forgot-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,81165)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,96394)),"/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/forgot-password/page",pathname:"/auth/forgot-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},88054:(e,r,t)=>{"use strict";t.d(r,{V:()=>i});var a=t(60687),s=t(44493),n=t(23166);t(43210);let i=({title:e,description:r,children:t,footer:i,header:o})=>(0,a.jsxs)("div",{className:"flex flex-col items-center px-6 py-4 md:px-8 space-y-6",children:[(0,a.jsx)("div",{className:"pt-8",children:(0,a.jsx)(n.g,{size:40,animated:!1,href:"/"})}),(0,a.jsxs)(s.Zp,{className:"w-full max-w-sm mx-4 md:mx-0 md:min-w-[50vw] md:max-w-2xl lg:max-w-3xl bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,a.jsxs)(s.aR,{className:"space-y-1",children:[o,(0,a.jsx)(s.ZB,{className:"text-2xl font-bold text-center",children:e}),r&&(0,a.jsx)(s.BT,{className:"text-center",children:r})]}),(0,a.jsx)(s.Wu,{className:"space-y-4",children:t}),i&&(0,a.jsx)(s.wL,{children:i})]})]})},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var a=t(60687);t(43210);var s=t(4780);function n({className:e,type:r,...t}){return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},96394:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,dynamic:()=>s,metadata:()=>i,revalidate:()=>n});var a=t(37413);let s="force-dynamic",n=0,i={title:"Authentication - Siift",description:"Sign in or create an account to access Siift"};function o({children:e}){return(0,a.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-[#166534]/5 via-transparent to-[#22c55e]/5 dark:from-[#166534]/10 dark:via-transparent dark:to-[#22c55e]/10"}),(0,a.jsx)("div",{className:"relative z-10",children:e})]})}},96487:()=>{},98599:(e,r,t)=>{"use strict";t.d(r,{s:()=>i,t:()=>n});var a=t(43210);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,a=e.map(e=>{let a=s(e,r);return t||"function"!=typeof a||(t=!0),a});if(t)return()=>{for(let r=0;r<a.length;r++){let t=a[r];"function"==typeof t?t():s(e[r],null)}}}}function i(...e){return a.useCallback(n(...e),e)}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[80,4999,8360,2604,1838],()=>t(86385));module.exports=a})();