(()=>{var e={};e.id=2772,e.ids=[2772],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15823:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p,HEAD:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function p(){try{let e={status:"healthy",timestamp:new Date().toISOString(),uptime:process.uptime(),environment:"production",version:process.env.npm_package_version||"1.0.0",memory:{used:Math.round(process.memoryUsage().heapUsed/1024/1024*100)/100,total:Math.round(process.memoryUsage().heapTotal/1024/1024*100)/100}};return i.NextResponse.json(e,{status:200})}catch(e){return i.NextResponse.json({status:"unhealthy",error:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()},{status:500})}}async function u(){return new i.NextResponse(null,{status:200})}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"/Users/<USER>/Data/new era/siift-next/src/app/api/health/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=c;function m(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80],()=>r(15823));module.exports=s})();