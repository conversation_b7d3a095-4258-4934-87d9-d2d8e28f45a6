(()=>{var e={};e.id=64,e.ids=[64],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63675:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>u,POST:()=>c});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);let d=[{id:"1",name:"Website Redesign",description:"Complete redesign of company website with modern UI",status:"active",createdAt:new Date("2024-01-15"),updatedAt:new Date("2024-01-20"),userId:"user1"},{id:"2",name:"Mobile App",description:"iOS and Android app development for the platform",status:"active",createdAt:new Date("2024-01-10"),updatedAt:new Date("2024-01-18"),userId:"user1"},{id:"3",name:"Marketing Campaign",description:"Q1 marketing campaign planning and execution",status:"completed",createdAt:new Date("2024-01-05"),updatedAt:new Date("2024-01-15"),userId:"user1"},{id:"4",name:"API Documentation",description:"Comprehensive API documentation and developer guides",status:"active",createdAt:new Date("2024-01-12"),updatedAt:new Date("2024-01-19"),userId:"user1"},{id:"5",name:"Database Migration",description:"Migrate legacy database to new cloud infrastructure",status:"archived",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-16"),userId:"user1"}];async function u(e){try{return i.NextResponse.json({success:!0,data:d})}catch(e){return console.error("Error fetching projects:",e),i.NextResponse.json({success:!1,error:"Failed to fetch projects"},{status:500})}}async function c(e){try{let{name:t,description:r}=await e.json();if(!t||!r)return i.NextResponse.json({success:!1,error:"Name and description are required"},{status:400});let s={id:`project_${Date.now()}`,name:t,description:r,status:"active",createdAt:new Date,updatedAt:new Date,userId:"user1"};return d.push(s),i.NextResponse.json({success:!0,data:s})}catch(e){return console.error("Error creating project:",e),i.NextResponse.json({success:!1,error:"Failed to create project"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/projects/route",pathname:"/api/projects",filename:"route",bundlePath:"app/api/projects/route"},resolvedPagePath:"/Users/<USER>/Data/new era/siift-next/src/app/api/projects/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=p;function w(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80],()=>r(63675));module.exports=s})();