(()=>{var e={};e.id=9514,e.ids=[9514],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34154:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>j});var s={};r.r(s),r.d(s,{DELETE:()=>p,GET:()=>u,PUT:()=>c});var n=r(96559),a=r(48088),o=r(37719),i=r(32190);let d=[{id:"1",name:"Website Redesign",description:"Complete redesign of company website with modern UI",status:"active",createdAt:new Date("2024-01-15"),updatedAt:new Date("2024-01-20"),userId:"user1"},{id:"2",name:"Mobile App",description:"iOS and Android app development for the platform",status:"active",createdAt:new Date("2024-01-10"),updatedAt:new Date("2024-01-18"),userId:"user1"},{id:"3",name:"Marketing Campaign",description:"Q1 marketing campaign planning and execution",status:"completed",createdAt:new Date("2024-01-05"),updatedAt:new Date("2024-01-15"),userId:"user1"},{id:"4",name:"API Documentation",description:"Comprehensive API documentation and developer guides",status:"active",createdAt:new Date("2024-01-12"),updatedAt:new Date("2024-01-19"),userId:"user1"},{id:"5",name:"Database Migration",description:"Migrate legacy database to new cloud infrastructure",status:"archived",createdAt:new Date("2024-01-08"),updatedAt:new Date("2024-01-16"),userId:"user1"}];async function u(e,{params:t}){try{let{id:e}=await t,r=d.find(t=>t.id===e);if(!r)return i.NextResponse.json({success:!1,error:"Project not found"},{status:404});return i.NextResponse.json({success:!0,data:r})}catch(e){return console.error("Error fetching project:",e),i.NextResponse.json({success:!1,error:"Failed to fetch project"},{status:500})}}async function c(e,{params:t}){try{let{id:r}=await t,s=await e.json(),n=d.findIndex(e=>e.id===r);if(-1===n)return i.NextResponse.json({success:!1,error:"Project not found"},{status:404});let a={...d[n],...s,updatedAt:new Date};return d[n]=a,i.NextResponse.json({success:!0,data:a})}catch(e){return console.error("Error updating project:",e),i.NextResponse.json({success:!1,error:"Failed to update project"},{status:500})}}async function p(e,{params:t}){try{let{id:e}=await t,r=d.findIndex(t=>t.id===e);if(-1===r)return i.NextResponse.json({success:!1,error:"Project not found"},{status:404});return d.splice(r,1),i.NextResponse.json({success:!0,message:"Project deleted successfully"})}catch(e){return console.error("Error deleting project:",e),i.NextResponse.json({success:!1,error:"Failed to delete project"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/projects/[id]/route",pathname:"/api/projects/[id]",filename:"route",bundlePath:"app/api/projects/[id]/route"},resolvedPagePath:"/Users/<USER>/Data/new era/siift-next/src/app/api/projects/[id]/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:j,serverHooks:m}=l;function f(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:j})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80],()=>r(34154));module.exports=s})();