(()=>{var e={};e.id=8673,e.ids=[8673],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17053:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>c,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p});var a=t(96559),n=t(48088),o=t(37719),u=t(32190);let i={id:"user1",email:"<EMAIL>",name:"<PERSON>",role:"user",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-20")};async function p(e){try{let r=e.headers.get("authorization");if(!r||!r.startsWith("Bearer "))return u.NextResponse.json({success:!1,error:"Unauthorized"},{status:401});return u.NextResponse.json({success:!0,data:i})}catch(e){return console.error("Error fetching user:",e),u.NextResponse.json({success:!1,error:"Failed to fetch user"},{status:500})}}let c=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"/Users/<USER>/Data/new era/siift-next/src/app/api/auth/me/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:l}=c;function h(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[80],()=>t(17053));module.exports=s})();