(()=>{var e={};e.id=4957,e.ids=[4957],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},86161:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(96559),n=t(48088),i=t(37719),o=t(32190);let u=[{id:"user1",email:"<EMAIL>",name:"John Doe",role:"user",password:"password123",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-20")},{id:"admin1",email:"<EMAIL>",name:"Admin User",role:"admin",password:"123456789@",createdAt:new Date("2024-01-01"),updatedAt:new Date("2024-01-20")}];async function d(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return o.NextResponse.json({success:!1,error:"Email and password are required"},{status:400});let s=u.find(e=>e.email===r);if(!s||s.password!==t)return o.NextResponse.json({success:!1,error:"Invalid credentials"},{status:401});let a=`mock_access_token_${s.id}_${Date.now()}`,n=`mock_refresh_token_${s.id}_${Date.now()}`,{password:i,...d}=s;return o.NextResponse.json({success:!0,data:{user:d,accessToken:a,refreshToken:n}})}catch(e){return console.error("Error signing in:",e),o.NextResponse.json({success:!1,error:"Failed to sign in"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/signin/route",pathname:"/api/auth/signin",filename:"route",bundlePath:"app/api/auth/signin/route"},resolvedPagePath:"/Users/<USER>/Data/new era/siift-next/src/app/api/auth/signin/route.ts",nextConfigOutput:"standalone",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:x}=p;function m(){return(0,i.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[80],()=>t(86161));module.exports=s})();