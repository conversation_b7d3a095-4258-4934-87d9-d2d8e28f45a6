(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3363:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(60687),a=r(93854),n=r(74456);function o({children:e,showHeader:t=!0,showFooter:r=!0,constrainHeight:o=!1}){return(0,s.jsxs)("div",{className:`${o?"h-screen":"min-h-screen"} flex flex-col bg-background`,children:[t&&(0,s.jsx)(n.<PERSON><PERSON>,{}),(0,s.jsx)("main",{className:`flex-1 ${o?"min-h-0":""}`,children:e}),r&&(0,s.jsx)(a<PERSON>,{})]})}function i({children:e}){return(0,s.jsx)(o,{showFooter:!1,constrainHeight:!0,children:(0,s.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:e})})}},6959:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>l});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let l={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75758)),"/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32584:(e,t,r)=>{"use strict";r.d(t,{BK:()=>i,eu:()=>o,q5:()=>c});var s=r(60687);r(43210);var a=r(11096),n=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function i({className:e,...t}){return(0,s.jsx)(a._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",e),...t})}function c({className:e,...t}){return(0,s.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},33873:e=>{"use strict";e.exports=require("path")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>o});var s=r(60687),a=r(43210),n=r(4780);let o=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...t}));o.displayName="Textarea"},35950:(e,t,r)=>{"use strict";r.d(t,{w:()=>o});var s=r(60687);r(43210);var a=r(62369),n=r(4780);function o({className:e,orientation:t="horizontal",decorative:r=!0,...o}){return(0,s.jsx)(a.b,{"data-slot":"separator",decorative:r,orientation:t,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",e),...o})}},38885:(e,t,r)=>{"use strict";r.d(t,{Zd:()=>l,j9:()=>c});var s=r(8693),a=r(54050),n=r(95450),o=r(50582),i=r(52581);function c(){let{userId:e,getToken:t}=(0,n.P)();(0,s.jE)();let r=function(){let{userId:e,getToken:t}=(0,n.P)(),r=(0,s.jE)();return(0,a.n)({mutationFn:async({internalUserId:r,userData:s})=>{let a=await t(),n=await fetch(`http://localhost:3000/api/users/${r}`,{method:"PATCH",headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}},body:JSON.stringify({...s,clerkId:e})});if(!n.ok){let e=await n.text();throw Error(`Failed to update user: ${n.status} ${e}`)}return n.json()},onMutate:async({userData:t})=>{await r.cancelQueries({queryKey:o.lH.user(e)});let s=r.getQueryData(o.lH.user(e));if(s){let a={...s,...t,updatedAt:new Date().toISOString()};r.setQueryData(o.lH.user(e),a)}return{previousUser:s}},onError:(t,s,a)=>{a?.previousUser&&r.setQueryData(o.lH.user(e),a.previousUser),console.error("Failed to update user:",t),i.oR.error("Failed to update profile. Please try again.")},onSettled:()=>{o.WG.user(e)},onSuccess:e=>{i.oR.success("Profile updated successfully!"),console.log("User updated successfully:",e)}})}();return(0,a.n)({mutationFn:async s=>{let a=await t(),n=await fetch(`http://localhost:3000/api/users/clerk/${e}`,{headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}}});if(!n.ok)throw Error("User not found in backend. Please sync your account first.");let o=await n.json();return r.mutateAsync({internalUserId:o.id,userData:s})},onError:e=>{console.error("Failed to update user by Clerk ID:",e),i.oR.error(e.message||"Failed to update profile. Please try again.")}})}function l(){let{user:e,userId:t,email:r,firstName:c,lastName:l}=(0,n.P)(),d=function(){let{getToken:e}=(0,n.P)(),t=(0,s.jE)();return(0,a.n)({mutationFn:async t=>{let r=await e(),s="http://localhost:3000",a=await fetch(`${s}/api/users`,{method:"POST",headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}},body:JSON.stringify({...t,role:t.role||"user",status:t.status||"active",timezone:t.timezone||"UTC",preferences:t.preferences||{notifications:!0,theme:"system",language:"en"}})});if(!a.ok){let e=await a.text();if(console.error("Backend response error:",{status:a.status,statusText:a.statusText,errorText:e,url:`${s}/api/users`}),400===a.status&&(e.includes("already exists")||e.includes("clerk ID already exists"))){console.log("User already exists, fetching existing user...");let e=await fetch(`${s}/api/users/clerk/${t.clerkId}`,{headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}}});if(e.ok){let t=await e.json();return console.log("Found existing user:",t),t}throw console.log("Could not fetch existing user, but user exists in backend"),Error("User already exists in backend")}throw Error(`Failed to create user: ${a.status} ${a.statusText} - ${e}`)}return a.json()},onSuccess:(e,r)=>{t.setQueryData(o.lH.user(r.clerkId),e),o.WG.user(r.clerkId),i.oR.success("Account synced successfully!"),console.log("User synced successfully in backend:",e)},onError:(e,t)=>{console.error("Failed to create user in backend:",e),console.error("User data that failed:",t),e.message&&e.message.includes("already exists")?console.log("User already exists in backend - this is expected"):i.oR.error(`Failed to sync account: ${e.message}`)}})}();return(0,a.n)({mutationFn:async()=>{if(!e||!t)throw Error("User not available");let s={email:r||"",firstName:c||"",lastName:l||"",clerkId:t,role:"user",status:"active",avatarUrl:e.imageUrl||"",bio:"",timezone:"UTC",preferences:{notifications:!0,theme:"system",language:"en"}};return d.mutateAsync(s)},onError:e=>{console.error("Failed to sync user to backend:",e),i.oR.error("Failed to sync account. Please contact support.")}})}},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>d});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},54300:(e,t,r)=>{"use strict";r.d(t,{J:()=>c});var s=r(60687),a=r(43210),n=r(14163),o=a.forwardRef((e,t)=>(0,s.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=r(4780);function c({className:e,...t}){return(0,s.jsx)(o,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},62157:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72575:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},72745:(e,t,r)=>{"use strict";r.d(t,{Jd:()=>o,rE:()=>i});var s=r(95450),a=r(50582),n=r(51423);function o(){let{userId:e,getToken:t,isSignedIn:r}=(0,s.P)();return(0,n.I)({queryKey:a.lH.user(e),queryFn:async()=>{if(!e)throw Error("User ID not available");let r=await t(),s=`http://localhost:3000/api/users/clerk/${e}`;console.log("Fetching user from backend:",{url:s,userId:e,hasToken:!!r});let a=new AbortController,n=setTimeout(()=>a.abort(),1e4),o=await fetch(s,{headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}},signal:a.signal});if(clearTimeout(n),console.log("Backend response:",{status:o.status,statusText:o.statusText,ok:o.ok}),!o.ok){if(404===o.status)throw Error("User not found in backend");throw Error(`Failed to fetch user: ${o.status} ${o.statusText}`)}return o.json()},enabled:!!e&&r,staleTime:3e5,gcTime:6e5,retry:(e,t)=>!(t?.message?.includes("User not found")||t?.message?.includes("Failed to fetch"))&&t?.name!=="AbortError"&&e<2,throwOnError:!1})}function i(){let{userId:e,getToken:t,isSignedIn:r}=(0,s.P)();return(0,n.I)({queryKey:["user","exists",e],queryFn:async()=>{if(!e)return!1;try{let r=await t(),s="http://localhost:3000";s.includes("localhost");let a=new AbortController,n=setTimeout(()=>a.abort(),5e3),o=await fetch(`${s}/api/users/clerk/${e}`,{method:"GET",headers:{"Content-Type":"application/json",...r&&{Authorization:`Bearer ${r}`}},signal:a.signal});return clearTimeout(n),console.log("User exists check:",{userId:e,status:o.status,exists:o.ok}),o.ok}catch(e){return console.error("Error checking if user exists:",e),!1}},enabled:!!e&&r,staleTime:3e4,retry:!1,throwOnError:!1})}},73024:e=>{"use strict";e.exports=require("node:fs")},75758:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx","dynamic"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx","default")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},84037:(e,t,r)=>{Promise.resolve().then(r.bind(r,87032))},87032:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>U,dynamic:()=>A});var s=r(60687),a=r(558),n=r(41550),o=r(40228),i=r(99891),c=r(41862),l=r(8819),d=r(43210),u=r(27605),m=r(43214),p=r(3363),x=r(32584),f=r(96834),h=r(29523),g=r(44493),b=r(89667),v=r(54300),y=r(35950),j=r(34729),N=r(95450),w=r(72745),k=r(38885);let A="force-dynamic",P=m.Ik({firstName:m.Yj().min(1,"First name is required"),lastName:m.Yj().min(1,"Last name is required"),bio:m.Yj().max(500,"Bio must be less than 500 characters").optional(),location:m.Yj().max(100,"Location must be less than 100 characters").optional(),website:m.Yj().url("Please enter a valid URL").optional().or(m.eu(""))});function U(){let{user:e,firstName:t,lastName:r,email:m,imageUrl:A,fullName:U}=(0,N.P)(),{data:E,isLoading:T}=(0,w.Jd)(),F=(0,k.j9)(),[C,$]=(0,d.useState)(!1),[q,z]=(0,d.useState)(null),[D,B]=(0,d.useState)({bio:"Software developer passionate about building great products",location:"San Francisco, CA",website:"https://example.com"}),{register:S,handleSubmit:_,formState:{errors:M,isDirty:R},reset:I,setValue:H}=(0,u.mN)({resolver:(0,a.u)(P),defaultValues:{firstName:t||"",lastName:r||"",bio:D.bio||"",location:D.location||"",website:D.website||""}}),J=async t=>{if(e){$(!0),z(null);try{await F.mutateAsync({firstName:t.firstName,lastName:t.lastName,bio:t.bio,timezone:"UTC",preferences:{...D,location:t.location,website:t.website}}),B({bio:t.bio||"",location:t.location||"",website:t.website||""}),z({type:"success",text:"Profile updated successfully!"}),I(t)}catch(e){z({type:"error",text:e.message||"Failed to update profile"})}finally{$(!1)}}};return e?(`${t?.[0]||""}${r?.[0]||""}`.toUpperCase(),(0,s.jsx)(p.N,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight text-foreground",children:"Profile"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Manage your account settings and profile information."})]}),(0,s.jsxs)("div",{className:"grid gap-6 lg:grid-cols-3",children:[(0,s.jsxs)(g.Zp,{className:"lg:col-span-1 bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(g.aR,{children:[(0,s.jsx)(g.ZB,{className:"text-card-foreground",children:"Profile Information"}),(0,s.jsx)(g.BT,{className:"text-muted-foreground",children:"Your personal information and account details."})]}),(0,s.jsxs)(g.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(x.eu,{className:"h-16 w-16 border-2 border-accent/20",children:[(0,s.jsx)(x.BK,{src:A||"",alt:U||"User"}),(0,s.jsxs)(x.q5,{className:"text-lg bg-accent text-accent-foreground font-semibold",children:[t?.[0],r?.[0]]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold text-card-foreground",children:U||"User"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:m}),(0,s.jsx)(f.E,{variant:"secondary",className:"mt-1 bg-accent/10 text-accent border-accent/20",children:"User"})]})]}),(0,s.jsx)(y.w,{className:"bg-border"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)(n.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"text-muted-foreground",children:"Email:"}),(0,s.jsx)("span",{className:"text-card-foreground",children:m})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"text-muted-foreground",children:"Joined:"}),(0,s.jsx)("span",{className:"text-card-foreground",children:e?.createdAt?new Date(e.createdAt).toLocaleDateString():"Recently"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,s.jsx)(i.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,s.jsx)(f.E,{variant:"outline",className:"text-xs border-green-500/20 text-green-600 bg-green-500/10",children:"Active"})]})]})]})]}),(0,s.jsxs)(g.Zp,{className:"lg:col-span-2 bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200",children:[(0,s.jsxs)(g.aR,{children:[(0,s.jsx)(g.ZB,{className:"text-card-foreground",children:"Edit Profile"}),(0,s.jsx)(g.BT,{className:"text-muted-foreground",children:"Update your personal information and preferences."})]}),(0,s.jsx)(g.Wu,{children:(0,s.jsxs)("form",{onSubmit:_(J),className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.J,{htmlFor:"firstName",className:"text-card-foreground",children:"First Name"}),(0,s.jsx)(b.p,{id:"firstName",...S("firstName"),placeholder:"Enter your first name",className:"border-border focus:border-accent focus:ring-accent/20"}),M.firstName&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:M.firstName.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.J,{htmlFor:"lastName",className:"text-card-foreground",children:"Last Name"}),(0,s.jsx)(b.p,{id:"lastName",...S("lastName"),placeholder:"Enter your last name",className:"border-border focus:border-accent focus:ring-accent/20"}),M.lastName&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:M.lastName.message})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.J,{htmlFor:"bio",className:"text-card-foreground",children:"Bio"}),(0,s.jsx)(j.T,{id:"bio",...S("bio"),placeholder:"Tell us about yourself...",rows:3,className:"border-border focus:border-accent focus:ring-accent/20"}),M.bio&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:M.bio.message})]}),(0,s.jsxs)("div",{className:"grid gap-4 sm:grid-cols-2",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.J,{htmlFor:"location",className:"text-card-foreground",children:"Location"}),(0,s.jsx)(b.p,{id:"location",...S("location"),placeholder:"City, Country",className:"border-border focus:border-accent focus:ring-accent/20"}),M.location&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:M.location.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(v.J,{htmlFor:"website",className:"text-card-foreground",children:"Website"}),(0,s.jsx)(b.p,{id:"website",...S("website"),placeholder:"https://example.com",className:"border-border focus:border-accent focus:ring-accent/20"}),M.website&&(0,s.jsx)("p",{className:"text-sm text-destructive",children:M.website.message})]})]}),q&&(0,s.jsx)("div",{className:`p-3 rounded-md text-sm border ${"success"===q.type?"bg-green-500/10 text-green-700 border-green-500/20":"bg-red-500/10 text-red-700 border-red-500/20"}`,children:q.text}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsxs)(h.$,{type:"submit",disabled:C||F.isPending||!R,className:"bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border",children:[(C||F.isPending)&&(0,s.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),F.isPending?"Updating...":"Save Changes"]})})]})})]})]})]})})):(0,s.jsx)(p.N,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-destructive mb-2",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"You need to be signed in to view this page."})]})})})}},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},90117:(e,t,r)=>{Promise.resolve().then(r.bind(r,75758))},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var s=r(60687);r(43210);var a=r(8730),n=r(24224),o=r(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:t,asChild:r=!1,...n}){let c=r?a.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}},98876:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,878,5788,8562,2604,9647,1838,4336,2403],()=>r(6959));module.exports=s})();