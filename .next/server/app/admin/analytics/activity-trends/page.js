(()=>{var e={};e.id=154,e.ids=[154],e.modules={2041:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(43210),a=s(49605),i=s(54024),n=["axis","item"],l=(0,r.forwardRef)((e,t)=>r.createElement(i.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:n,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:t}))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23026:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},23947:(e,t,s)=>{Promise.resolve().then(s.bind(s,73578))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},52219:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>o});var r=s(65239),a=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["analytics",{children:["activity-trends",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,94264)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/analytics/activity-trends/page",pathname:"/admin/analytics/activity-trends",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63699:(e,t,s)=>{Promise.resolve().then(s.bind(s,94264))},73024:e=>{"use strict";e.exports=require("node:fs")},73578:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>D,dynamic:()=>U});var r=s(60687),a=s(7126),i=s(96834),n=s(29523),l=s(44493),d=s(98647),o=s(58559),c=s(41312),x=s(23026),h=s(25541),u=s(41862),m=s(78122),p=s(93613),v=s(43210),f=s(26101),g=s(61678),y=s(85168),j=s(27747),b=s(19598),w=s(64635),N=s(66424),k=s(2041),A=s(22344);let U="force-dynamic";function D(){let[e,t]=(0,v.useState)(null),[s,U]=(0,v.useState)(!1),[D,P]=(0,v.useState)(null),[q,_]=(0,v.useState)(null),C=async()=>{U(!0),P(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,d.R)(e);let s=await d.i.getActivityTrends();t(s),_(new Date)}catch(e){P(e.message)}finally{U(!1)}},R=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),S=e=>{if(e<60)return`${e.toFixed(1)}s`;let t=Math.floor(e/60),s=e%60;return`${t}m ${s.toFixed(0)}s`},T=e?.summary?[{label:"Total Days",value:e.summary.totalDays,icon:o.A,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Avg Active Users",value:R(e.summary.averageActiveUsers),icon:c.A,color:"text-green-600",bgColor:"bg-green-50"},{label:"Total New Users",value:R(e.summary.totalNewUsers),icon:x.A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Growth Rate",value:`${e.summary.growthRate.toFixed(1)}%`,icon:h.A,color:"text-orange-600",bgColor:"bg-orange-50"}]:[],W=e?.data?.map(e=>({...e,date:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"})}))||[];return(0,r.jsx)(a.U,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Activity Trends"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Visualize user activity patterns and trends over time."})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[q&&(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",q.toLocaleTimeString()]}),(0,r.jsxs)(n.$,{onClick:C,disabled:s,variant:"outline",children:[s?(0,r.jsx)(u.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),D&&(0,r.jsx)(l.Zp,{className:"border-red-200 bg-red-50",children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("p",{className:"text-red-800",children:D})]})})}),s&&!e&&(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsx)(u.A,{className:"h-8 w-8 animate-spin"})}),e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:T.map((e,t)=>(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:`p-3 rounded-full ${e.bgColor}`,children:(0,r.jsx)(e.icon,{className:`h-6 w-6 ${e.color}`})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:e.value})]})]})})},t))}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Active Users Trend"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"h-80",children:(0,r.jsx)(f.u,{width:"100%",height:"100%",children:(0,r.jsxs)(g.b,{data:W,children:[(0,r.jsx)(y.d,{strokeDasharray:"3 3"}),(0,r.jsx)(j.W,{dataKey:"date"}),(0,r.jsx)(b.h,{}),(0,r.jsx)(w.m,{formatter:(e,t)=>[R(e),"activeUsers"===t?"Active Users":"New Users"]}),(0,r.jsx)(N.N,{type:"monotone",dataKey:"activeUsers",stroke:"#166534",strokeWidth:2,dot:{fill:"#166534",strokeWidth:2,r:4}}),(0,r.jsx)(N.N,{type:"monotone",dataKey:"newUsers",stroke:"#7c3aed",strokeWidth:2,dot:{fill:"#7c3aed",strokeWidth:2,r:4}})]})})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Total Requests"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(f.u,{width:"100%",height:"100%",children:(0,r.jsxs)(k.E,{data:W,children:[(0,r.jsx)(y.d,{strokeDasharray:"3 3"}),(0,r.jsx)(j.W,{dataKey:"date"}),(0,r.jsx)(b.h,{}),(0,r.jsx)(w.m,{formatter:e=>[R(e),"Requests"]}),(0,r.jsx)(A.y,{dataKey:"totalRequests",fill:"#166534"})]})})})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Average Session Time"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(f.u,{width:"100%",height:"100%",children:(0,r.jsxs)(g.b,{data:W,children:[(0,r.jsx)(y.d,{strokeDasharray:"3 3"}),(0,r.jsx)(j.W,{dataKey:"date"}),(0,r.jsx)(b.h,{}),(0,r.jsx)(w.m,{formatter:e=>[S(e),"Session Time"]}),(0,r.jsx)(N.N,{type:"monotone",dataKey:"averageSessionTime",stroke:"#ea580c",strokeWidth:2,dot:{fill:"#ea580c",strokeWidth:2,r:4}})]})})})})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Detailed Data"})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full text-sm",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Active Users"}),(0,r.jsx)("th",{className:"text-right p-2",children:"New Users"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Total Requests"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Avg Session Time"})]})}),(0,r.jsx)("tbody",{children:e.data?.slice(0,10).map((e,t)=>(0,r.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,r.jsx)("td",{className:"p-2",children:new Date(e.date).toLocaleDateString()}),(0,r.jsx)("td",{className:"text-right p-2",children:R(e.activeUsers)}),(0,r.jsx)("td",{className:"text-right p-2",children:R(e.newUsers)}),(0,r.jsx)("td",{className:"text-right p-2",children:R(e.totalRequests)}),(0,r.jsx)("td",{className:"text-right p-2",children:S(e.averageSessionTime)})]},t))})]})}),e.data?.length>10&&(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)(i.E,{variant:"outline",children:["Showing first 10 of ",e.data.length," records"]})})]})]})]})]})})}},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94264:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx","dynamic"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx","default")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var r=s(60687);s(43210);var a=s(8730),i=s(24224),n=s(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...i}){let d=s?a.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),e),...i})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[80,4999,8360,878,5788,8562,7359,4541,1529,2775,1838,4336,4987,2140],()=>s(52219));module.exports=r})();