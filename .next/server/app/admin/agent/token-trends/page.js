(()=>{var e={};e.id=7961,e.ids=[7961],e.modules={413:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,dynamic:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx","dynamic"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx","default")},2041:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var r=s(43210),a=s(49605),n=s(54024),i=["axis","item"],l=(0,r.forwardRef)((e,t)=>r.createElement(n.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:i,tooltipPayloadSearcher:a.uN,categoricalChartProps:e,ref:t}))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14446:(e,t,s)=>{Promise.resolve().then(s.bind(s,413))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50929:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["agent",{children:["token-trends",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,413)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/agent/token-trends/page",pathname:"/admin/agent/token-trends",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77494:(e,t,s)=>{Promise.resolve().then(s.bind(s,95267))},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},95267:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>A,dynamic:()=>P});var r=s(60687),a=s(7126),n=s(96834),i=s(29523),l=s(44493),o=s(98647),d=s(53411),c=s(61611),x=s(23928),h=s(25541),m=s(41862),p=s(78122),u=s(93613),g=s(43210),f=s(26101),j=s(61855),v=s(85168),y=s(27747),b=s(19598),k=s(64635),N=s(77814),w=s(61678),T=s(66424),C=s(2041),D=s(22344);let P="force-dynamic";function A(){let[e,t]=(0,g.useState)(null),[s,P]=(0,g.useState)(!1),[A,_]=(0,g.useState)(null),[S,E]=(0,g.useState)(null),R=async()=>{P(!0),_(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,o.R)(e);let s=await o.i.getTokenTrends();t(s),E(new Date)}catch(e){_(e instanceof Error?e.message:"Unknown error")}finally{P(!1)}},U=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),W=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),q=e?.summary?[{label:"Total Days",value:e.summary.totalDays,icon:d.A,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Total Tokens",value:U(e.summary.totalTokens),icon:c.A,color:"text-green-600",bgColor:"bg-green-50"},{label:"Total Cost",value:W(e.summary.totalCost),icon:x.A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Growth Rate",value:`${e.summary.growthRate.toFixed(1)}%`,icon:h.A,color:"text-indigo-600",bgColor:"bg-indigo-50"}]:[],Z=e?.summary?[{label:"Avg Tokens/Day",value:U(e.summary.averageTokensPerDay)},{label:"Avg Cost/Day",value:W(e.summary.averageCostPerDay)}]:[],K=e?.data?.map(e=>({...e,date:new Date(e.date).toLocaleDateString("en-US",{month:"short",day:"numeric"})}))||[];return(0,r.jsx)(a.U,{children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Token Trends"}),(0,r.jsx)("p",{className:"text-muted-foreground",children:"Track token usage patterns and cost trends over time."})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[S&&(0,r.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",S.toLocaleTimeString()]}),(0,r.jsxs)(i.$,{onClick:R,disabled:s,variant:"outline",children:[s?(0,r.jsx)(m.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),A&&(0,r.jsx)(l.Zp,{className:"border-red-200 bg-red-50",children:(0,r.jsx)(l.Wu,{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(u.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("p",{className:"text-red-800",children:A})]})})}),s&&!e&&(0,r.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,r.jsx)(m.A,{className:"h-8 w-8 animate-spin"})}),e&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:q.map((e,t)=>(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:`p-3 rounded-full ${e.bgColor}`,children:(0,r.jsx)(e.icon,{className:`h-6 w-6 ${e.color}`})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,r.jsx)("p",{className:"text-3xl font-bold",children:e.value})]})]})})},t))}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Token Usage Over Time"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"h-80",children:(0,r.jsx)(f.u,{width:"100%",height:"100%",children:(0,r.jsxs)(j.Q,{data:K,children:[(0,r.jsx)(v.d,{strokeDasharray:"3 3"}),(0,r.jsx)(y.W,{dataKey:"date"}),(0,r.jsx)(b.h,{}),(0,r.jsx)(k.m,{formatter:(e,t)=>"totalTokens"===t?[U(e),"Total Tokens"]:"inputTokens"===t?[U(e),"Input Tokens"]:"outputTokens"===t?[U(e),"Output Tokens"]:[U(e),t]}),(0,r.jsx)(N.Gk,{type:"monotone",dataKey:"totalTokens",stackId:"1",stroke:"#166534",fill:"#166534",fillOpacity:.6}),(0,r.jsx)(N.Gk,{type:"monotone",dataKey:"inputTokens",stackId:"2",stroke:"#7c3aed",fill:"#7c3aed",fillOpacity:.6}),(0,r.jsx)(N.Gk,{type:"monotone",dataKey:"outputTokens",stackId:"3",stroke:"#ea580c",fill:"#ea580c",fillOpacity:.6})]})})})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Cost Trends"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(f.u,{width:"100%",height:"100%",children:(0,r.jsxs)(w.b,{data:K,children:[(0,r.jsx)(v.d,{strokeDasharray:"3 3"}),(0,r.jsx)(y.W,{dataKey:"date"}),(0,r.jsx)(b.h,{}),(0,r.jsx)(k.m,{formatter:e=>[W(e),"Total Cost"]}),(0,r.jsx)(T.N,{type:"monotone",dataKey:"totalCost",stroke:"#7c3aed",strokeWidth:3,dot:{fill:"#7c3aed",strokeWidth:2,r:4}})]})})})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Call Volume"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(f.u,{width:"100%",height:"100%",children:(0,r.jsxs)(C.E,{data:K,children:[(0,r.jsx)(v.d,{strokeDasharray:"3 3"}),(0,r.jsx)(y.W,{dataKey:"date"}),(0,r.jsx)(b.h,{}),(0,r.jsx)(k.m,{formatter:e=>[U(e),"Calls"]}),(0,r.jsx)(D.y,{dataKey:"callCount",fill:"#166534"})]})})})})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Efficiency Metrics"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsx)("div",{className:"h-64",children:(0,r.jsx)(f.u,{width:"100%",height:"100%",children:(0,r.jsxs)(w.b,{data:K,children:[(0,r.jsx)(v.d,{strokeDasharray:"3 3"}),(0,r.jsx)(y.W,{dataKey:"date"}),(0,r.jsx)(b.h,{}),(0,r.jsx)(k.m,{formatter:e=>[U(e),"Avg Tokens/Call"]}),(0,r.jsx)(T.N,{type:"monotone",dataKey:"averageTokensPerCall",stroke:"#ea580c",strokeWidth:2,dot:{fill:"#ea580c",strokeWidth:2,r:4}})]})})})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Summary Statistics"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[q.map((e,t)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:e.value})]},t)),Z.map((e,t)=>(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,r.jsx)("p",{className:"text-2xl font-bold",children:e.value})]},`avg-${t}`))]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Recent Data"})}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full text-sm",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b",children:[(0,r.jsx)("th",{className:"text-left p-2",children:"Date"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Total Tokens"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Input Tokens"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Output Tokens"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Total Cost"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Calls"}),(0,r.jsx)("th",{className:"text-right p-2",children:"Avg Tokens/Call"})]})}),(0,r.jsx)("tbody",{children:e.data?.slice(0,10).map((e,t)=>(0,r.jsxs)("tr",{className:"border-b hover:bg-muted/50",children:[(0,r.jsx)("td",{className:"p-2",children:new Date(e.date).toLocaleDateString()}),(0,r.jsx)("td",{className:"text-right p-2",children:U(e.totalTokens)}),(0,r.jsx)("td",{className:"text-right p-2",children:U(e.inputTokens)}),(0,r.jsx)("td",{className:"text-right p-2",children:U(e.outputTokens)}),(0,r.jsx)("td",{className:"text-right p-2",children:W(e.totalCost)}),(0,r.jsx)("td",{className:"text-right p-2",children:U(e.callCount)}),(0,r.jsx)("td",{className:"text-right p-2",children:U(e.averageTokensPerCall)})]},t))})]})}),e.data&&e.data.length>10&&(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)(n.E,{variant:"outline",children:["Showing first 10 of ",e.data.length," records"]})})]})]})]})]})})}},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(8730),n=s(24224),i=s(4780);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...n}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,i.cn)(l({variant:t}),e),...n})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[80,4999,8360,878,5788,8562,7359,4541,1529,2775,7972,1838,4336,4987,2140],()=>s(50929));module.exports=r})();