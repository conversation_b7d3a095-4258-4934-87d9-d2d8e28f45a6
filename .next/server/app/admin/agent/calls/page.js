(()=>{var e={};e.id=2930,e.ids=[2930],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>u,gC:()=>p,l6:()=>c,yv:()=>o});var a=s(60687);s(43210);var r=s(72951),l=s(78272),i=s(13964),n=s(3589),d=s(4780);function c({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function o({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function x({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...l}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...l,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(m,{})]})})}function u({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}function m({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}},17458:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("file-chart-column-increasing",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 18v-2",key:"qcmpov"}],["path",{d:"M12 18v-4",key:"q1q25u"}],["path",{d:"M16 18v-6",key:"15y0np"}]])},17971:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},18179:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},24413:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34318:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},40083:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},47033:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},56085:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},66798:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k,dynamic:()=>b});var a=s(60687),r=s(7126),l=s(96834),i=s(29523),n=s(44493),d=s(15079),c=s(98647),o=s(41862),x=s(78122),p=s(80462),u=s(93613),h=s(45583),m=s(61611),g=s(23928),v=s(48730),y=s(47033),f=s(14952),j=s(43210);let b="force-dynamic";function k(){let[e,t]=(0,j.useState)(null),[s,b]=(0,j.useState)(!1),[k,w]=(0,j.useState)(null),[A,N]=(0,j.useState)(null),[M,q]=(0,j.useState)({page:1,limit:10,agentType:"all",callType:"all",modelProvider:"all",status:"all"}),C=(0,j.useCallback)(async()=>{b(!0),w(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,c.R)(e);let s={page:M.page,limit:M.limit};M.agentType&&"all"!==M.agentType&&(s.agentType=M.agentType),M.callType&&"all"!==M.callType&&(s.callType=M.callType),M.modelProvider&&"all"!==M.modelProvider&&(s.modelProvider=M.modelProvider),M.status&&"all"!==M.status&&(s.status=M.status);let a=await c.i.getAgentCalls(s);t(a),N(new Date)}catch(e){w(e instanceof Error?e.message:"Unknown error")}finally{b(!1)}},[M]),P=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),z=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),T=e=>e<1e3?`${e}ms`:`${(e/1e3).toFixed(1)}s`,_=(e,t)=>{q(s=>({...s,[e]:t,page:1}))},S=e=>{q(t=>({...t,page:e}))},D=e=>{switch(e){case"success":return"bg-green-100 text-green-800";case"error":return"bg-red-100 text-red-800";case"timeout":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},E=e=>({intake:"bg-blue-100 text-blue-800",validate:"bg-green-100 text-green-800",build:"bg-purple-100 text-purple-800",grow:"bg-indigo-100 text-indigo-800",coordinator:"bg-pink-100 text-pink-800",topic:"bg-indigo-100 text-indigo-800"})[e]||"bg-gray-100 text-gray-800";return(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Agent Calls"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Monitor and analyze individual agent execution calls."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[A&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",A.toLocaleTimeString()]}),(0,a.jsxs)(i.$,{onClick:C,disabled:s,variant:"outline",children:[s?(0,a.jsx)(o.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(p.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Filters"})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsxs)(d.l6,{value:M.agentType,onValueChange:e=>_("agentType",e),children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{placeholder:"Agent Type"})}),(0,a.jsxs)(d.gC,{children:[(0,a.jsx)(d.eb,{value:"all",children:"All Types"}),(0,a.jsx)(d.eb,{value:"intake",children:"Intake"}),(0,a.jsx)(d.eb,{value:"validate",children:"Validate"}),(0,a.jsx)(d.eb,{value:"build",children:"Build"}),(0,a.jsx)(d.eb,{value:"grow",children:"Grow"}),(0,a.jsx)(d.eb,{value:"coordinator",children:"Coordinator"}),(0,a.jsx)(d.eb,{value:"topic",children:"Topic"})]})]}),(0,a.jsxs)(d.l6,{value:M.callType,onValueChange:e=>_("callType",e),children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{placeholder:"Call Type"})}),(0,a.jsxs)(d.gC,{children:[(0,a.jsx)(d.eb,{value:"all",children:"All Types"}),(0,a.jsx)(d.eb,{value:"agent_execution",children:"Agent Execution"}),(0,a.jsx)(d.eb,{value:"llm_call",children:"LLM Call"}),(0,a.jsx)(d.eb,{value:"tool_call",children:"Tool Call"})]})]}),(0,a.jsxs)(d.l6,{value:M.modelProvider,onValueChange:e=>_("modelProvider",e),children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{placeholder:"Provider"})}),(0,a.jsxs)(d.gC,{children:[(0,a.jsx)(d.eb,{value:"all",children:"All Providers"}),(0,a.jsx)(d.eb,{value:"openai",children:"OpenAI"}),(0,a.jsx)(d.eb,{value:"deepseek",children:"DeepSeek"}),(0,a.jsx)(d.eb,{value:"anthropic",children:"Anthropic"})]})]}),(0,a.jsxs)(d.l6,{value:M.status,onValueChange:e=>_("status",e),children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{placeholder:"Status"})}),(0,a.jsxs)(d.gC,{children:[(0,a.jsx)(d.eb,{value:"all",children:"All Status"}),(0,a.jsx)(d.eb,{value:"success",children:"Success"}),(0,a.jsx)(d.eb,{value:"error",children:"Error"}),(0,a.jsx)(d.eb,{value:"timeout",children:"Timeout"}),(0,a.jsx)(d.eb,{value:"cancelled",children:"Cancelled"})]})]}),(0,a.jsxs)(d.l6,{value:M.limit.toString(),onValueChange:e=>q(t=>({...t,limit:parseInt(e),page:1})),children:[(0,a.jsx)(d.bq,{children:(0,a.jsx)(d.yv,{})}),(0,a.jsxs)(d.gC,{children:[(0,a.jsx)(d.eb,{value:"5",children:"5 per page"}),(0,a.jsx)(d.eb,{value:"10",children:"10 per page"}),(0,a.jsx)(d.eb,{value:"25",children:"25 per page"}),(0,a.jsx)(d.eb,{value:"50",children:"50 per page"})]})]})]})})]}),k&&(0,a.jsx)(n.Zp,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:k})]})})}),s&&!e&&(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(o.A,{className:"h-8 w-8 animate-spin"})}),e&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),(0,a.jsxs)("span",{children:["Agent Calls (",e.pagination?.total||0,")"]})]}),(0,a.jsxs)(l.E,{variant:"outline",children:["Page ",e.pagination?.page||1," of"," ",e.pagination?.totalPages||1]})]})}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"space-y-4",children:e.data?.map((e,t)=>(0,a.jsx)(n.Zp,{className:"border-l-4 border-l-blue-500",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.E,{className:E(e.agentType),children:e.agentType}),(0,a.jsx)(l.E,{variant:"outline",children:e.callType}),(0,a.jsx)(l.E,{className:D(e.status),children:e.status})]}),(0,a.jsx)("div",{className:"text-right text-sm text-muted-foreground",children:new Date(e.createdAt).toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:[e.modelProvider," - ",e.modelName]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Workflow: ",e.workflowType]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsxs)("span",{children:[P(e.totalTokens)," tokens"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{children:z(e.cost)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 text-purple-600"}),(0,a.jsx)("span",{children:T(e.duration)})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-xs text-muted-foreground",children:[(0,a.jsxs)("div",{children:["Input: ",P(e.inputTokens)," tokens"]}),(0,a.jsxs)("div",{children:["Output: ",P(e.outputTokens)," tokens"]}),(0,a.jsxs)("div",{children:["Tools: ",e.toolsCalled?.length||0," called"]})]}),e.toolsCalled&&e.toolsCalled.length>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"Tools:"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-1",children:e.toolsCalled.map((e,t)=>(0,a.jsx)(l.E,{variant:"secondary",className:"text-xs",children:e},t))})]}),e.errorMessage&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-2",children:(0,a.jsx)("p",{className:"text-xs text-red-800",children:e.errorMessage})})]})})},t))}),e.pagination&&e.pagination.totalPages>1&&(()=>{let t=e.pagination;return(0,a.jsxs)("div",{className:"flex items-center justify-between mt-6",children:[(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>S(t.page-1),disabled:!t.hasPrev,children:[(0,a.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Previous"]}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Showing ",(t.page-1)*t.limit+1," ","to"," ",Math.min(t.page*t.limit,t.total)," ","of ",t.total," calls"]}),(0,a.jsxs)(i.$,{variant:"outline",onClick:()=>S(t.page+1),disabled:!t.hasNext,children:["Next",(0,a.jsx)(f.A,{className:"h-4 w-4 ml-2"})]})]})})()]})]})})]})})}},70742:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78200:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},85252:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l,dynamic:()=>r});var a=s(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx","dynamic"),l=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx","default")},85778:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},87535:(e,t,s)=>{Promise.resolve().then(s.bind(s,85252))},92687:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>p,tree:()=>c});var a=s(65239),r=s(48088),l=s(88170),i=s.n(l),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["agent",{children:["calls",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,85252)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/agent/calls/page",pathname:"/admin/agent/calls",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},95783:(e,t,s)=>{Promise.resolve().then(s.bind(s,66798))},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(60687);s(43210);var r=s(8730),l=s(24224),i=s(4780);let n=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...l}){let d=s?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(n({variant:t}),e),...l})}},97051:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,8562,7359,5555,1838,4336,4987,2140],()=>s(92687));module.exports=a})();