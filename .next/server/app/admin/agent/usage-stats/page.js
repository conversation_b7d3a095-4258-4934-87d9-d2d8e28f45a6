(()=>{var e={};e.id=5598,e.ids=[5598],e.modules={2041:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var a=r(43210),n=r(49605),s=r(54024),l=["axis","item"],i=(0,a.forwardRef)((e,t)=>a.createElement(s.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:l,tooltipPayloadSearcher:n.uN,categoricalChartProps:e,ref:t}))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45200:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,dynamic:()=>n});var a=r(12907);let n=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx","dynamic"),s=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx","default")},45583:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},55355:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>e9,dynamic:()=>e8});var a=r(60687),n=r(7126),s=r(29523),l=r(44493),i=r(98647),o=r(45583),c=r(5336),d=r(23928),u=r(48730),p=r(41862),m=r(78122),f=r(93613),g=r(61611),x=r(43210),h=r(26101),y=r(2041),v=r(85168),b=r(27747),j=r(19598),A=r(64635),P=r(22344),w=r(49605),N=r(48806),E=r(13420),O=r(71680),k=r(25893),C=r(43209);function M(e){return(0,C.j)(),null}r(61645);var S=r(84071),R=r(73865),D=r(12128),T=["width","height","layout"];function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(null,arguments)}var K={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},I=(0,x.forwardRef)(function(e,t){var r,a=(0,R.e)(e.categoricalChartProps,K),{width:n,height:s,layout:l}=a,i=function(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(({}).hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(a,T);if(!(0,D.F)(n)||!(0,D.F)(s))return null;var{chartName:o,defaultTooltipEventType:c,validateTooltipEventTypes:d,tooltipPayloadSearcher:u}=e;return x.createElement(N.J,{preloadedState:{options:{chartName:o,defaultTooltipEventType:c,validateTooltipEventTypes:d,tooltipPayloadSearcher:u,eventEmitter:void 0}},reduxStoreName:null!=(r=a.id)?r:o},x.createElement(E.TK,{chartData:a.data}),x.createElement(O.s,{width:n,height:s,layout:l,margin:a.margin}),x.createElement(k.p,{accessibilityLayer:a.accessibilityLayer,barCategoryGap:a.barCategoryGap,maxBarSize:a.maxBarSize,stackOffset:a.stackOffset,barGap:a.barGap,barSize:a.barSize,syncId:a.syncId,syncMethod:a.syncMethod,className:a.className}),x.createElement(M,{cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius}),x.createElement(S.L,z({width:n,height:s},i,{ref:t})))}),U=["item"],_={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},F=(0,x.forwardRef)((e,t)=>{var r=(0,R.e)(e,_);return x.createElement(I,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:U,tooltipPayloadSearcher:w.uN,categoricalChartProps:r,ref:t})}),q=r(5664),B=r.n(q),L=r(49384),Z=r(84648),G=r(57282),W=r(35034),$=r(64279),V=r(85621),J=r(51426),H=r(36166),X=r(60559),Y=r(97350),Q=e=>e.graphicalItems.polarItems,ee=(0,Z.Mz)([H.N,X.E],V.eo),et=(0,Z.Mz)([Q,V.DP,ee],V.ec),er=(0,Z.Mz)([et],V.rj),ea=(0,Z.Mz)([er,G.z3],V.Nk),en=(0,Z.Mz)([ea,V.DP,et],V.fb),es=(0,Z.Mz)([ea,V.DP,et],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var a;return{value:(0,$.kr)(e,null!=(a=t.dataKey)?a:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,$.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),el=()=>void 0,ei=(0,Z.Mz)([V.DP,V.AV,el,es,el,J.fz,H.N],V.wL),eo=(0,Z.Mz)([V.DP,J.fz,ea,en,Y.eC,H.N,ei],V.tP),ec=(0,Z.Mz)([eo,V.DP,V.xM],V.xp);function ed(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function eu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ed(Object(r),!0).forEach(function(t){var a,n,s;a=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in a?Object.defineProperty(a,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ed(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,Z.Mz)([V.DP,eo,ec,H.N],V.g1);var ep=(0,Z.Mz)([Q,(e,t)=>t],(e,t)=>e.filter(e=>"pie"===e.type).find(e=>e.id===t)),em=[],ef=(e,t,r)=>(null==r?void 0:r.length)===0?em:r,eg=(0,Z.Mz)([G.z3,ep,ef],(e,t,r)=>{var a,{chartData:n}=e;if(null!=t&&((a=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:n)&&a.length||null==r||(a=r.map(e=>eu(eu({},t.presentationProps),e.props))),null!=a))return a}),ex=(0,Z.Mz)([eg,ep,ef],(e,t,r)=>{if(null!=e&&null!=t)return e.map((e,a)=>{var n,s,l=(0,$.kr)(e,t.nameKey,t.name);return s=null!=r&&null!=(n=r[a])&&null!=(n=n.props)&&n.fill?r[a].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,$.uM)(l,t.dataKey),color:s,payload:e,type:t.legendType}})}),eh=(0,Z.Mz)([eg,ep,ef,W.HZ],(e,t,r,a)=>{if(null!=t&&null!=e)return function(e){var t,r,a,{pieSettings:n,displayedData:s,cells:l,offset:i}=e,{cornerRadius:o,startAngle:c,endAngle:d,dataKey:u,nameKey:p,tooltipType:m}=n,f=Math.abs(n.minAngle),g=eH(c,d),x=Math.abs(g),h=s.length<=1?0:null!=(t=n.paddingAngle)?t:0,y=s.filter(e=>0!==(0,$.kr)(e,u,0)).length,v=x-y*f-(x>=360?y:y-1)*h,b=s.reduce((e,t)=>{var r=(0,$.kr)(t,u,0);return e+((0,eN.Et)(r)?r:0)},0);return b>0&&(r=s.map((e,t)=>{var r,s=(0,$.kr)(e,u,0),d=(0,$.kr)(e,p,t),x=eJ(n,i,e),y=((0,eN.Et)(s)?s:0)/b,j=eL(eL({},e),l&&l[t]&&l[t].props),A=(r=t?a.endAngle+(0,eN.sA)(g)*h*(0!==s):c)+(0,eN.sA)(g)*((0!==s?f:0)+y*v),P=(r+A)/2,w=(x.innerRadius+x.outerRadius)/2,N=[{name:d,value:s,payload:j,dataKey:u,type:m}],E=(0,ew.IZ)(x.cx,x.cy,w,P);return a=eL(eL(eL(eL({},n.presentationProps),{},{percent:y,cornerRadius:o,name:d,tooltipPayload:N,midAngle:P,middleRadius:w,tooltipPosition:E},j),x),{},{value:(0,$.kr)(e,u),startAngle:r,endAngle:A,payload:j,paddingAngle:(0,eN.sA)(g)*h})})),r}({offset:a,pieSettings:t,displayedData:e,cells:r})}),ey=r(98986),ev=r(81888),eb=r(23561),ej=r(25679),eA=r(54186),eP=r(20237),ew=r(19335),eN=r(22989),eE=r(4057),eO=r(47371),ek=r(61545),eC=r(37625),eM=r(69009),eS=r(14956),eR=r(75601),eD=r(36304),eT=r(3785),ez=r(75787),eK=r(99857),eI=r(23758),eU=["onMouseEnter","onClick","onMouseLeave"],e_=["id"],eF=["id"];function eq(e,t){if(null==e)return{};var r,a,n=function(e,t){if(null==e)return{};var r={};for(var a in e)if(({}).hasOwnProperty.call(e,a)){if(-1!==t.indexOf(a))continue;r[a]=e[a]}return r}(e,t);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);for(a=0;a<s.length;a++)r=s[a],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function eB(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);t&&(a=a.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,a)}return r}function eL(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eB(Object(r),!0).forEach(function(t){var a,n,s;a=e,n=t,s=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,t||"default");if("object"!=typeof a)return a;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in a?Object.defineProperty(a,n,{value:s,enumerable:!0,configurable:!0,writable:!0}):a[n]=s}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eB(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eZ(){return(eZ=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(null,arguments)}function eG(e){var t=(0,x.useMemo)(()=>(0,eA.aS)(e.children,ej.f),[e.children]),r=(0,C.G)(r=>ex(r,e.id,t));return null==r?null:x.createElement(eS._,{legendPayload:r})}function eW(e){var{dataKey:t,nameKey:r,sectors:a,stroke:n,strokeWidth:s,fill:l,name:i,hide:o,tooltipType:c}=e;return{dataDefinedOnItem:null==a?void 0:a.map(e=>e.tooltipPayload),positions:null==a?void 0:a.map(e=>e.tooltipPosition),settings:{stroke:n,strokeWidth:s,fill:l,dataKey:t,nameKey:r,name:(0,$.uM)(i,t),hide:o,type:c,color:l,unit:""}}}var e$=(e,t)=>e>t?"start":e<t?"end":"middle",eV=(e,t,r)=>"function"==typeof t?t(e):(0,eN.F4)(t,r,.8*r),eJ=(e,t,r)=>{var{top:a,left:n,width:s,height:l}=t,i=(0,ew.lY)(s,l),o=n+(0,eN.F4)(e.cx,s,s/2),c=a+(0,eN.F4)(e.cy,l,l/2),d=(0,eN.F4)(e.innerRadius,i,0);return{cx:o,cy:c,innerRadius:d,outerRadius:eV(r,e.outerRadius,i),maxRadius:e.maxRadius||Math.sqrt(s*s+l*l)/2}},eH=(e,t)=>(0,eN.sA)(t-e)*Math.min(Math.abs(t-e),360),eX=(e,t)=>{if(x.isValidElement(e))return x.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,L.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return x.createElement(ev.I,eZ({},t,{type:"linear",className:r}))},eY=(e,t,r)=>{if(x.isValidElement(e))return x.cloneElement(e,t);var a=r;if("function"==typeof e&&(a=e(t),x.isValidElement(a)))return a;var n=(0,L.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return x.createElement(eb.E,eZ({},t,{alignmentBaseline:"middle",className:n}),a)};function eQ(e){var{sectors:t,props:r,showLabels:a}=e,{label:n,labelLine:s,dataKey:l}=r;if(!a||!n||!t)return null;var i=(0,eK.u)(r),o=(0,eA.J9)(n,!1),c=(0,eA.J9)(s,!1),d="object"==typeof n&&"offsetRadius"in n&&n.offsetRadius||20,u=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,a=(0,ew.IZ)(e.cx,e.cy,e.outerRadius+d,r),u=eL(eL(eL(eL({},i),e),{},{stroke:"none"},o),{},{index:t,textAnchor:e$(a.x,e.cx)},a),p=eL(eL(eL(eL({},i),e),{},{fill:"none",stroke:e.fill},c),{},{index:t,points:[(0,ew.IZ)(e.cx,e.cy,e.outerRadius,r),a],key:"line"});return x.createElement(ey.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},s&&eX(s,p),eY(n,u,(0,$.kr)(e,l)))});return x.createElement(ey.W,{className:"recharts-pie-labels"},u)}function e0(e){var{sectors:t,activeShape:r,inactiveShape:a,allOtherPieProps:n,showLabels:s}=e,l=(0,C.G)(eM.A2),{onMouseEnter:i,onClick:o,onMouseLeave:c}=n,d=eq(n,eU),u=(0,ek.Cj)(i,n.dataKey),p=(0,ek.Pg)(c),m=(0,ek.Ub)(o,n.dataKey);return null==t?null:x.createElement(x.Fragment,null,t.map((e,s)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var i=r&&String(s)===l,o=i?r:l?a:null,c=eL(eL({},e),{},{stroke:e.stroke,tabIndex:-1,[eR.F0]:s,[eR.um]:n.dataKey});return x.createElement(ey.W,eZ({tabIndex:-1,className:"recharts-pie-sector"},(0,eE.XC)(d,e,s),{onMouseEnter:u(e,s),onMouseLeave:p(e,s),onClick:m(e,s),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(s)}),x.createElement(eO.y,eZ({option:o,isActive:i,shapeType:"sector"},c)))}),x.createElement(eQ,{sectors:t,props:n,showLabels:s}))}function e1(e){var{props:t,previousSectorsRef:r}=e,{sectors:a,isAnimationActive:n,animationBegin:s,animationDuration:l,animationEasing:i,activeShape:o,inactiveShape:c,onAnimationStart:d,onAnimationEnd:u}=t,p=(0,eD.n)(t,"recharts-pie-"),m=r.current,[f,g]=(0,x.useState)(!0),h=(0,x.useCallback)(()=>{"function"==typeof u&&u(),g(!1)},[u]),y=(0,x.useCallback)(()=>{"function"==typeof d&&d(),g(!0)},[d]);return x.createElement(eI.J,{begin:s,duration:l,isActive:n,easing:i,onAnimationStart:y,onAnimationEnd:h,key:p},e=>{var n=[],s=(a&&a[0]).startAngle;return a.forEach((t,r)=>{var a=m&&m[r],l=r>0?B()(t,"paddingAngle",0):0;if(a){var i=(0,eN.Dj)(a.endAngle-a.startAngle,t.endAngle-t.startAngle),o=eL(eL({},t),{},{startAngle:s+l,endAngle:s+i(e)+l});n.push(o),s=o.endAngle}else{var{endAngle:c,startAngle:d}=t,u=(0,eN.Dj)(0,c-d)(e),p=eL(eL({},t),{},{startAngle:s+l,endAngle:s+u+l});n.push(p),s=p.endAngle}}),r.current=n,x.createElement(ey.W,null,x.createElement(e0,{sectors:n,activeShape:o,inactiveShape:c,allOtherPieProps:t,showLabels:!f}))})}function e2(e){var{sectors:t,isAnimationActive:r,activeShape:a,inactiveShape:n}=e,s=(0,x.useRef)(null),l=s.current;return r&&t&&t.length&&(!l||l!==t)?x.createElement(e1,{props:e,previousSectorsRef:s}):x.createElement(e0,{sectors:t,activeShape:a,inactiveShape:n,allOtherPieProps:e,showLabels:!0})}function e5(e){var{hide:t,className:r,rootTabIndex:a}=e,n=(0,L.$)("recharts-pie",r);return t?null:x.createElement(ey.W,{tabIndex:a,className:n},x.createElement(e2,e))}var e6={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!eP.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function e3(e){var{id:t}=e,r=eq(e,e_),a=(0,x.useMemo)(()=>(0,eA.aS)(e.children,ej.f),[e.children]),n=(0,C.G)(e=>eh(e,t,a));return x.createElement(x.Fragment,null,x.createElement(eC.r,{fn:eW,args:eL(eL({},e),{},{sectors:n})}),x.createElement(e5,eZ({},r,{sectors:n})))}function e4(e){var t=(0,R.e)(e,e6),{id:r}=t,a=eq(t,eF),n=(0,eK.u)(a);return x.createElement(eT.x,{id:r,type:"pie"},e=>x.createElement(x.Fragment,null,x.createElement(ez.v,{type:"pie",id:e,data:a.data,dataKey:a.dataKey,hide:a.hide,angleAxisId:0,radiusAxisId:0,name:a.name,nameKey:a.nameKey,tooltipType:a.tooltipType,legendType:a.legendType,fill:a.fill,cx:a.cx,cy:a.cy,startAngle:a.startAngle,endAngle:a.endAngle,paddingAngle:a.paddingAngle,minAngle:a.minAngle,innerRadius:a.innerRadius,outerRadius:a.outerRadius,cornerRadius:a.cornerRadius,presentationProps:n}),x.createElement(eG,eZ({},a,{id:e})),x.createElement(e3,eZ({},a,{id:e})),a.children))}e4.displayName="Pie";let e8="force-dynamic";function e9(){var e;let[t,r]=(0,x.useState)(null),[w,N]=(0,x.useState)(!1),[E,O]=(0,x.useState)(null),[k,C]=(0,x.useState)(null),M=async()=>{N(!0),O(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,i.R)(e);let t=await i.i.getAgentUsageStats();r(t),C(new Date)}catch(e){O(e.message)}finally{N(!1)}},S=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),R=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),D=t?[{label:"Total Calls",value:S(t.totalCalls),icon:o.A,color:"text-blue-600",bgColor:"bg-blue-50"},{label:"Success Rate",value:`${t.successRate.toFixed(1)}%`,icon:c.A,color:"text-green-600",bgColor:"bg-green-50"},{label:"Total Cost",value:R(t.totalCost),icon:d.A,color:"text-purple-600",bgColor:"bg-purple-50"},{label:"Avg Duration",value:(e=t.averageDuration)<1e3?`${e}ms`:`${(e/1e3).toFixed(1)}s`,icon:u.A,color:"text-indigo-600",bgColor:"bg-indigo-50"}]:[],T=t?[{label:"Successful Calls",value:S(t.successfulCalls)},{label:"Failed Calls",value:S(t.failedCalls)},{label:"Total Tokens",value:S(t.totalTokens)},{label:"Avg Tokens/Call",value:S(t.averageTokensPerCall)},{label:"Avg Cost/Call",value:R(t.averageCostPerCall)},{label:"Most Used Agent",value:t.mostUsedAgentType},{label:"Most Used Model",value:t.mostUsedModel}]:[],z=t?.callsByAgentType?Object.entries(t.callsByAgentType).map(([e,t])=>({type:e,count:t})):[],K=t?.callsByProvider?Object.entries(t.callsByProvider).map(([e,t])=>({provider:e,count:t})):[],I=t?.costByModel?Object.entries(t.costByModel).map(([e,t])=>({model:e,cost:t})):[],U=["#166534","#7c3aed","#ea580c","#dc2626","#0891b2","#059669"];return(0,a.jsx)(n.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Agent Usage Statistics"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Comprehensive overview of agent performance and usage metrics."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[k&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",k.toLocaleTimeString()]}),(0,a.jsxs)(s.$,{onClick:M,disabled:w,variant:"outline",children:[w?(0,a.jsx)(p.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),E&&(0,a.jsx)(l.Zp,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:E})]})})}),w&&!t&&(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:D.map((e,t)=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:`p-3 rounded-full ${e.bgColor}`,children:(0,a.jsx)(e.icon,{className:`h-6 w-6 ${e.color}`})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-3xl font-bold",children:e.value})]})]})})},t))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Calls by Agent Type"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(h.u,{width:"100%",height:"100%",children:(0,a.jsxs)(y.E,{data:z,children:[(0,a.jsx)(v.d,{strokeDasharray:"3 3"}),(0,a.jsx)(b.W,{dataKey:"type"}),(0,a.jsx)(j.h,{}),(0,a.jsx)(A.m,{formatter:e=>[S(e),"Calls"]}),(0,a.jsx)(P.y,{dataKey:"count",fill:"#166534"})]})})})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Calls by Provider"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(h.u,{width:"100%",height:"100%",children:(0,a.jsxs)(F,{children:[(0,a.jsx)(e4,{data:K,cx:"50%",cy:"50%",labelLine:!1,label:e=>`${e.provider} ${(100*(e.percent||0)).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"count",children:K.map((e,t)=>(0,a.jsx)(ej.f,{fill:U[t%U.length]},`cell-${t}`))}),(0,a.jsx)(A.m,{formatter:e=>[S(e),"Calls"]})]})})})})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Cost by Model"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(h.u,{width:"100%",height:"100%",children:(0,a.jsxs)(y.E,{data:I,children:[(0,a.jsx)(v.d,{strokeDasharray:"3 3"}),(0,a.jsx)(b.W,{dataKey:"model"}),(0,a.jsx)(j.h,{}),(0,a.jsx)(A.m,{formatter:e=>[R(e),"Cost"]}),(0,a.jsx)(P.y,{dataKey:"cost",fill:"#7c3aed"})]})})})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Detailed Statistics"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:T.map((e,t)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]},t))})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Performance Summary"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(c.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Success Rate"})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${t.successRate}%`}})}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:[t.successfulCalls," successful out of ",t.totalCalls," ","total calls"]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Token Efficiency"})]}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:S(t.averageTokensPerCall)}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average tokens per call"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsx)("span",{className:"font-medium",children:"Cost Efficiency"})]}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:R(t.averageCostPerCall)}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Average cost per call"})]})]})})]})]})]})})}},56123:(e,t,r)=>{Promise.resolve().then(r.bind(r,45200))},61611:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},89655:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(65239),n=r(48088),s=r(88170),l=r.n(s),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let c={children:["",{children:["admin",{children:["agent",{children:["usage-stats",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,45200)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/agent/usage-stats/page",pathname:"/admin/agent/usage-stats",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},92915:(e,t,r)=>{Promise.resolve().then(r.bind(r,55355))},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,8562,7359,4541,1529,1838,4336,4987,2140],()=>r(89655));module.exports=a})();