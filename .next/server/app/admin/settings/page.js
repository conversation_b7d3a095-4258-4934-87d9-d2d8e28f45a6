(()=>{var e={};e.id=7122,e.ids=[7122],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6102:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(43210);function r(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17458:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("file-chart-column-increasing",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 18v-2",key:"qcmpov"}],["path",{d:"M12 18v-4",key:"q1q25u"}],["path",{d:"M16 18v-6",key:"15y0np"}]])},17971:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},18179:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},22542:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>r});var a=s(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx","dynamic"),i=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx","default")},24413:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34318:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>n});var a=s(60687),r=s(43210),i=s(4780);let n=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n.displayName="Textarea"},40083:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41550:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54300:(e,t,s)=>{"use strict";s.d(t,{J:()=>l});var a=s(60687),r=s(43210),i=s(14163),n=r.forwardRef((e,t)=>(0,a.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var d=s(4780);function l({className:e,...t}){return(0,a.jsx)(n,{"data-slot":"label",className:(0,d.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},56085:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63683:(e,t,s)=>{Promise.resolve().then(s.bind(s,69635))},64021:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},69635:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>J,dynamic:()=>H});var a=s(60687),r=s(43210),i=s(7126),n=s(44493),d=s(29523),l=s(89667),c=s(54300),o=s(34729),u=s(70569),p=s(98599),h=s(11273),m=s(65551),x=s(6102),y=s(18853),f=s(14163),g="Switch",[v,b]=(0,h.A)(g),[j,k]=v(g),w=r.forwardRef((e,t)=>{let{__scopeSwitch:s,name:i,checked:n,defaultChecked:d,required:l,disabled:c,value:o="on",onCheckedChange:h,form:x,...y}=e,[v,b]=r.useState(null),k=(0,p.s)(t,e=>b(e)),w=r.useRef(!1),A=!v||x||!!v.closest("form"),[N,C]=(0,m.i)({prop:n,defaultProp:d??!1,onChange:h,caller:g});return(0,a.jsxs)(j,{scope:s,checked:N,disabled:c,children:[(0,a.jsx)(f.sG.button,{type:"button",role:"switch","aria-checked":N,"aria-required":l,"data-state":q(N),"data-disabled":c?"":void 0,disabled:c,value:o,...y,ref:k,onClick:(0,u.m)(e.onClick,e=>{C(e=>!e),A&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})}),A&&(0,a.jsx)(M,{control:v,bubbles:!w.current,name:i,value:o,checked:N,required:l,disabled:c,form:x,style:{transform:"translateX(-100%)"}})]})});w.displayName=g;var A="SwitchThumb",N=r.forwardRef((e,t)=>{let{__scopeSwitch:s,...r}=e,i=k(A,s);return(0,a.jsx)(f.sG.span,{"data-state":q(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:t})});N.displayName=A;var M=r.forwardRef(({__scopeSwitch:e,control:t,checked:s,bubbles:i=!0,...n},d)=>{let l=r.useRef(null),c=(0,p.s)(l,d),o=(0,x.Z)(s),u=(0,y.X)(t);return r.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==s&&t){let a=new Event("click",{bubbles:i});t.call(e,s),e.dispatchEvent(a)}},[o,s,i]),(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...n,tabIndex:-1,ref:c,style:{...n.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function q(e){return e?"checked":"unchecked"}M.displayName="SwitchBubbleInput";var C=s(4780);function P({className:e,...t}){return(0,a.jsx)(w,{"data-slot":"switch",className:(0,C.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(N,{"data-slot":"switch-thumb",className:(0,C.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}var R=s(96834),E=s(41862),D=s(8819),S=s(84027),z=s(41312),U=s(58559),Z=s(99891),F=s(61611),T=s(78122),_=s(41550),L=s(97051);let H="force-dynamic";function J(){let[e,t]=(0,r.useState)(!1),[s,u]=(0,r.useState)({siteName:"Siift Platform",siteDescription:"Modern project management platform",adminEmail:"<EMAIL>",maxUsers:1e4,sessionTimeout:30,enableRegistration:!0,enableNotifications:!0,enableAnalytics:!0,maintenanceMode:!1,apiRateLimit:1e3,backupFrequency:"daily"}),p=async()=>{t(!0);try{await new Promise(e=>setTimeout(e,1e3)),console.log("Settings saved:",s)}catch(e){console.error("Failed to save settings:",e)}finally{t(!1)}},h=(e,t)=>{u(s=>({...s,[e]:t}))};return(0,a.jsx)(i.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Admin Settings"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Configure system settings and preferences."})]}),(0,a.jsxs)(d.$,{onClick:p,disabled:e,children:[e?(0,a.jsx)(E.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(D.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})]}),(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(S.A,{className:"h-5 w-5"}),"General Settings"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"siteName",children:"Site Name"}),(0,a.jsx)(l.p,{id:"siteName",value:s.siteName,onChange:e=>h("siteName",e.target.value)})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"siteDescription",children:"Site Description"}),(0,a.jsx)(o.T,{id:"siteDescription",value:s.siteDescription,onChange:e=>h("siteDescription",e.target.value),rows:3})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"adminEmail",children:"Admin Email"}),(0,a.jsx)(l.p,{id:"adminEmail",type:"email",value:s.adminEmail,onChange:e=>h("adminEmail",e.target.value)})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(z.A,{className:"h-5 w-5"}),"User Management"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"maxUsers",children:"Maximum Users"}),(0,a.jsx)(l.p,{id:"maxUsers",type:"number",value:s.maxUsers,onChange:e=>h("maxUsers",parseInt(e.target.value))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"sessionTimeout",children:"Session Timeout (minutes)"}),(0,a.jsx)(l.p,{id:"sessionTimeout",type:"number",value:s.sessionTimeout,onChange:e=>h("sessionTimeout",parseInt(e.target.value))})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(c.J,{children:"Enable User Registration"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Allow new users to register"})]}),(0,a.jsx)(P,{checked:s.enableRegistration,onCheckedChange:e=>h("enableRegistration",e)})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(U.A,{className:"h-5 w-5"}),"System Features"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(c.J,{children:"Enable Notifications"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Send system notifications to users"})]}),(0,a.jsx)(P,{checked:s.enableNotifications,onCheckedChange:e=>h("enableNotifications",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(c.J,{children:"Enable Analytics"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Collect usage analytics"})]}),(0,a.jsx)(P,{checked:s.enableAnalytics,onCheckedChange:e=>h("enableAnalytics",e)})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(c.J,{children:"Maintenance Mode"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Put system in maintenance mode"})]}),(0,a.jsx)(P,{checked:s.maintenanceMode,onCheckedChange:e=>h("maintenanceMode",e)})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(Z.A,{className:"h-5 w-5"}),"API & Security"]})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"apiRateLimit",children:"API Rate Limit (requests/hour)"}),(0,a.jsx)(l.p,{id:"apiRateLimit",type:"number",value:s.apiRateLimit,onChange:e=>h("apiRateLimit",parseInt(e.target.value))})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"backupFrequency",children:"Backup Frequency"}),(0,a.jsxs)("select",{id:"backupFrequency",value:s.backupFrequency,onChange:e=>h("backupFrequency",e.target.value),className:"w-full px-3 py-2 border border-input bg-background rounded-md text-sm",children:[(0,a.jsx)("option",{value:"hourly",children:"Hourly"}),(0,a.jsx)("option",{value:"daily",children:"Daily"}),(0,a.jsx)("option",{value:"weekly",children:"Weekly"}),(0,a.jsx)("option",{value:"monthly",children:"Monthly"})]})]})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,a.jsx)(F.A,{className:"h-5 w-5"}),"System Status"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Database"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Connection status"})]}),(0,a.jsx)(R.E,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"Connected"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Email Service"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"SMTP status"})]}),(0,a.jsx)(R.E,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"Operational"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:"Analytics"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Data collection"})]}),(0,a.jsx)(R.E,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"Active"})]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Quick Actions"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsxs)(d.$,{variant:"outline",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Clear Cache"]}),(0,a.jsxs)(d.$,{variant:"outline",children:[(0,a.jsx)(F.A,{className:"h-4 w-4 mr-2"}),"Backup Database"]}),(0,a.jsxs)(d.$,{variant:"outline",children:[(0,a.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Test Email"]}),(0,a.jsxs)(d.$,{variant:"outline",children:[(0,a.jsx)(L.A,{className:"h-4 w-4 mr-2"}),"Send Test Notification"]})]})})]})]})})}},70742:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78200:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79551:e=>{"use strict";e.exports=require("url")},79761:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>p,tree:()=>c});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),d=s(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);s.d(t,l);let c={children:["",{children:["admin",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,22542)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/settings/page",pathname:"/admin/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},85778:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},87659:(e,t,s)=>{Promise.resolve().then(s.bind(s,22542))},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(60687);s(43210);var r=s(8730),i=s(24224),n=s(4780);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:s=!1,...i}){let l=s?r.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:t}),e),...i})}},97051:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,8562,7359,1838,4336,4987,2140],()=>s(79761));module.exports=a})();