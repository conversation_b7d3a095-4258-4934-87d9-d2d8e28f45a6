(()=>{var e={};e.id=3200,e.ids=[3200],e.modules={2041:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(43210),r=s(49605),n=s(54024),i=["axis","item"],l=(0,a.forwardRef)((e,t)=>a.createElement(n.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:i,tooltipPayloadSearcher:r.uN,categoricalChartProps:e,ref:t}))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31683:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eb,dynamic:()=>ej});var a=s(60687),r=s(7126),n=s(96834),i=s(29523),l=s(44493),d=s(43210),c=s(11273),o=s(14163),u="Progress",[m,x]=(0,c.A)(u),[h,p]=m(u),g=d.forwardRef((e,t)=>{var s,r;let{__scopeProgress:n,value:i=null,max:l,getValueLabel:d=j,...c}=e;(l||0===l)&&!N(l)&&console.error((s=`${l}`,`Invalid prop \`max\` of value \`${s}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let u=N(l)?l:100;null===i||w(i,u)||console.error((r=`${i}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let m=w(i,u)?i:null,x=y(m)?d(m,u):void 0;return(0,a.jsx)(h,{scope:n,value:m,max:u,children:(0,a.jsx)(o.sG.div,{"aria-valuemax":u,"aria-valuemin":0,"aria-valuenow":y(m)?m:void 0,"aria-valuetext":x,role:"progressbar","data-state":b(m,u),"data-value":m??void 0,"data-max":u,...c,ref:t})})});g.displayName=u;var v="ProgressIndicator",f=d.forwardRef((e,t)=>{let{__scopeProgress:s,...r}=e,n=p(v,s);return(0,a.jsx)(o.sG.div,{"data-state":b(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...r,ref:t})});function j(e,t){return`${Math.round(e/t*100)}%`}function b(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function y(e){return"number"==typeof e}function N(e){return y(e)&&!isNaN(e)&&e>0}function w(e,t){return y(e)&&!isNaN(e)&&e<=t&&e>=0}f.displayName=v;var A=s(4780);function k({className:e,value:t,showValue:s=!1,...r}){return(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g,{"data-slot":"progress",className:(0,A.cn)("relative h-2 w-full overflow-hidden rounded-full","bg-[var(--progress-bg)] border border-[var(--progress-border)]",e),...r,children:(0,a.jsx)(f,{"data-slot":"progress-indicator",className:"h-full w-full flex-1 transition-all bg-[var(--progress-fill)]",style:{transform:`translateX(-${100-(t||0)}%)`}})}),s&&(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,a.jsxs)("span",{className:"text-xs font-medium text-[var(--progress-text)] drop-shadow-sm",children:[Math.round(t||0),"%"]})})]})}var T=s(70569),C=s(72942),P=s(46059),D=s(43),R=s(65551),U=s(96963),S="Tabs",[E,_]=(0,c.A)(S,[C.RG]),M=(0,C.RG)(),[$,I]=E(S),Z=d.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,onValueChange:n,defaultValue:i,orientation:l="horizontal",dir:d,activationMode:c="automatic",...u}=e,m=(0,D.jH)(d),[x,h]=(0,R.i)({prop:r,onChange:n,defaultProp:i??"",caller:S});return(0,a.jsx)($,{scope:s,baseId:(0,U.B)(),value:x,onValueChange:h,orientation:l,dir:m,activationMode:c,children:(0,a.jsx)(o.sG.div,{dir:m,"data-orientation":l,...u,ref:t})})});Z.displayName=S;var q="TabsList",F=d.forwardRef((e,t)=>{let{__scopeTabs:s,loop:r=!0,...n}=e,i=I(q,s),l=M(s);return(0,a.jsx)(C.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,a.jsx)(o.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});F.displayName=q;var z="TabsTrigger",W=d.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...i}=e,l=I(z,s),d=M(s),c=V(l.baseId,r),u=H(l.baseId,r),m=r===l.value;return(0,a.jsx)(C.q7,{asChild:!0,...d,focusable:!n,active:m,children:(0,a.jsx)(o.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:c,...i,ref:t,onMouseDown:(0,T.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(r)}),onKeyDown:(0,T.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(r)}),onFocus:(0,T.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;m||n||!e||l.onValueChange(r)})})})});W.displayName=z;var G="TabsContent",B=d.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,forceMount:n,children:i,...l}=e,c=I(G,s),u=V(c.baseId,r),m=H(c.baseId,r),x=r===c.value,h=d.useRef(x);return d.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,a.jsx)(P.C,{present:n||x,children:({present:s})=>(0,a.jsx)(o.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:m,tabIndex:0,...l,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:s&&i})})});function V(e,t){return`${e}-trigger-${t}`}function H(e,t){return`${e}-content-${t}`}function K({className:e,...t}){return(0,a.jsx)(Z,{"data-slot":"tabs",className:(0,A.cn)("flex flex-col gap-2",e),...t})}function L({className:e,...t}){return(0,a.jsx)(F,{"data-slot":"tabs-list",className:(0,A.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",e),...t})}function O({className:e,...t}){return(0,a.jsx)(W,{"data-slot":"tabs-trigger",className:(0,A.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function X({className:e,...t}){return(0,a.jsx)(B,{"data-slot":"tabs-content",className:(0,A.cn)("flex-1 outline-none",e),...t})}B.displayName=G;var J=s(98647),Y=s(5336),Q=s(35071),ee=s(41862),et=s(53411),es=s(78200),ea=s(24413),er=s(41312),en=s(58559),ei=s(25541),el=s(45583),ed=s(23928),ec=s(48730);let eo=(0,s(62688).A)("play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);var eu=s(61611),em=s(26101),ex=s(2041),eh=s(85168),ep=s(27747),eg=s(19598),ev=s(64635),ef=s(22344);let ej="force-dynamic";function eb(){let[e,t]=(0,d.useState)(!1),[s,c]=(0,d.useState)([]),[o,u]=(0,d.useState)(!1),[m,x]=(0,d.useState)(0),h=[{name:"Analytics Summary",endpoint:"/admin/analytics/summary",category:"analytics",method:()=>J.i.getAnalyticsSummary()},{name:"User Analytics",endpoint:"/admin/analytics/users",category:"analytics",method:()=>J.i.getUserAnalytics({limit:5})},{name:"Activity Metrics",endpoint:"/admin/analytics/activity-metrics",category:"analytics",method:()=>J.i.getActivityMetrics()},{name:"Activity Trends",endpoint:"/admin/analytics/activity-trends",category:"analytics",method:()=>J.i.getActivityTrends()},{name:"Agent Calls",endpoint:"/admin/agent-analytics/calls",category:"agent",method:()=>J.i.getAgentCalls({limit:5})},{name:"Agent Usage Stats",endpoint:"/admin/agent-analytics/usage-stats",category:"agent",method:()=>J.i.getAgentUsageStats()},{name:"Token Trends",endpoint:"/admin/agent-analytics/token-trends",category:"agent",method:()=>J.i.getTokenTrends()},{name:"Health Check",endpoint:"/admin/health",category:"system",method:()=>J.i.getHealthCheck()}],p=async()=>{t(!0),c([]),x(0);let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e){c([{endpoint:"Authentication",name:"Authentication",category:"system",status:"error",error:"No admin token found. Please login first."}]),t(!1);return}console.log("\uD83D\uDD11 Initializing admin API with token:",e?`${e.substring(0,20)}...`:"null"),(0,J.R)(e),console.log("\uD83D\uDD0D Admin API token after initialization:",J.i.getToken()?"✅ Set":"❌ Not set");let s=[];for(let e=0;e<h.length;e++){let t=h[e];x(e);let a=Date.now();try{console.log(`Testing ${t.name}...`);let e=await t.method(),r=Date.now()-a;s.push({endpoint:t.endpoint,name:t.name,category:t.category,status:"success",data:e,duration:r}),console.log(`✅ ${t.name} succeeded:`,e)}catch(r){let e=Date.now()-a;s.push({endpoint:t.endpoint,name:t.name,category:t.category,status:"error",error:r.message,duration:e}),console.log(`❌ ${t.name} failed:`,r.message)}c([...s])}t(!1),x(0)},g=e=>{switch(e){case"success":return(0,a.jsx)(Y.A,{className:"h-5 w-5 text-green-600"});case"error":return(0,a.jsx)(Q.A,{className:"h-5 w-5 text-red-600"});case"pending":return(0,a.jsx)(ee.A,{className:"h-5 w-5 animate-spin text-blue-600"})}},v=e=>{switch(e){case"success":return(0,a.jsx)(n.E,{className:"bg-green-100 text-green-800 hover:bg-green-100",children:"Success"});case"error":return(0,a.jsx)(n.E,{className:"bg-red-100 text-red-800 hover:bg-red-100",children:"Error"});case"pending":return(0,a.jsx)(n.E,{className:"bg-blue-100 text-blue-800 hover:bg-blue-100",children:"Pending"})}},f=e=>{switch(e){case"analytics":return(0,a.jsx)(et.A,{className:"h-5 w-5"});case"agent":return(0,a.jsx)(es.A,{className:"h-5 w-5"});case"system":return(0,a.jsx)(ea.A,{className:"h-5 w-5"})}},j=e=>e>=1e6?(e/1e6).toFixed(1)+"M":e>=1e3?(e/1e3).toFixed(1)+"K":e.toString(),b=e=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:4}).format(e),y=e=>e<1e3?`${e}ms`:`${(e/1e3).toFixed(1)}s`,N=e=>{if(!e)return null;let t=[{label:"Total Users",value:e.totalUsers,icon:er.A,color:"text-blue-600"},{label:"Active Today",value:e.activeToday,icon:en.A,color:"text-green-600"},{label:"New This Week",value:e.newUsersThisWeek,icon:ei.A,color:"text-purple-600"},{label:"Feedback Rate",value:`${e.feedbackRate}%`,icon:et.A,color:"text-orange-600"}];return(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map((e,t)=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(e.icon,{className:`h-5 w-5 ${e.color}`}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]})]})})},t))})},w=e=>e?.data?(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("div",{className:"grid gap-4",children:e.data.slice(0,5).map((e,t)=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"font-medium",children:[e.firstName," ",e.lastName]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.email}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Role: ",e.role]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:[e.actionCount," actions"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[y(1e3*e.averageSessionTime)," avg session"]}),(0,a.jsx)(n.E,{variant:e.hasSubmittedFeedback?"default":"secondary",children:e.hasSubmittedFeedback?"Has Feedback":"No Feedback"})]})]})})},t))})}):null,A=e=>{if(!e)return null;let t=[{label:"Daily Active Users",value:j(e.dau),change:e.dauChange},{label:"Weekly Active Users",value:j(e.wau),change:e.wauChange},{label:"Monthly Active Users",value:j(e.mau),change:e.mauChange},{label:"Yearly Active Users",value:j(e.yau),change:e.yauChange}];return(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map((e,t)=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[e.change>0?(0,a.jsx)(ei.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(ei.A,{className:"h-4 w-4 text-red-600 rotate-180"}),(0,a.jsxs)("span",{className:`text-sm ${e.change>0?"text-green-600":"text-red-600"}`,children:[e.change>0?"+":"",e.change.toFixed(1),"%"]})]})]})})},t))})},T=e=>{if(!e)return null;let t=[{label:"Total Calls",value:j(e.totalCalls),icon:el.A},{label:"Success Rate",value:`${e.successRate.toFixed(1)}%`,icon:Y.A},{label:"Total Cost",value:b(e.totalCost),icon:ed.A},{label:"Avg Duration",value:y(e.averageDuration),icon:ec.A}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:t.map((e,t)=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(e.icon,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:e.value})]})]})})},t))}),e.callsByAgentType&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Calls by Agent Type"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"h-64",children:(0,a.jsx)(em.u,{width:"100%",height:"100%",children:(0,a.jsxs)(ex.E,{data:Object.entries(e.callsByAgentType).map(([e,t])=>({type:e,count:t})),children:[(0,a.jsx)(eh.d,{strokeDasharray:"3 3"}),(0,a.jsx)(ep.W,{dataKey:"type"}),(0,a.jsx)(eg.h,{}),(0,a.jsx)(ev.m,{}),(0,a.jsx)(ef.y,{dataKey:"count",fill:"#166534"})]})})})})]})]})},C=e=>e?.data?(0,a.jsx)("div",{className:"space-y-4",children:e.data.slice(0,5).map((e,t)=>(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.E,{variant:"outline",children:e.agentType}),(0,a.jsx)(n.E,{variant:"secondary",children:e.callType}),(0,a.jsx)(n.E,{variant:"success"===e.status?"default":"destructive",children:e.status})]}),(0,a.jsxs)("p",{className:"text-sm font-medium",children:[e.modelProvider," - ",e.modelName]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[j(e.totalTokens)," tokens •"," ",b(e.cost)]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:y(e.duration)}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:new Date(e.createdAt).toLocaleDateString()})]})]})})},t))}):null,P=e=>{if(!e.data)return null;switch(e.name){case"Analytics Summary":return N(e.data);case"User Analytics":return w(e.data);case"Activity Metrics":return A(e.data);case"Agent Usage Stats":return T(e.data);case"Agent Calls":return C(e.data);default:return(0,a.jsxs)("div",{className:"bg-muted/50 border rounded p-3 mt-2",children:[(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-2",children:"Raw Response:"}),(0,a.jsx)("pre",{className:"text-xs bg-background p-2 rounded border overflow-auto max-h-32",children:JSON.stringify(e.data,null,2)})]})}},D=e=>s.filter(t=>t.category===e),R=()=>{let e=s.length,t=s.filter(e=>"success"===e.status).length;return{total:e,successful:t,failed:s.filter(e=>"error"===e.status).length,avgDuration:s.length>0?s.reduce((e,t)=>e+(t.duration||0),0)/s.length:0}};return(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Admin Analytics Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Test and visualize admin analytics API endpoints with enhanced data presentation."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[e&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(k,{value:m/h.length*100,className:"w-32",showValue:!0}),(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[m+1,"/",h.length]})]}),(0,a.jsxs)(i.$,{onClick:p,disabled:e,children:[e?(0,a.jsx)(ee.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(eo,{className:"h-4 w-4 mr-2"}),"Run Tests"]})]})]}),s.length>0&&(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Test Overview"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(eu.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Total Tests"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:R().total})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(Y.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Successful"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:R().successful})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(Q.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Failed"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:R().failed})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ec.A,{className:"h-5 w-5 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Avg Duration"}),(0,a.jsx)("p",{className:"text-2xl font-bold",children:y(R().avgDuration)})]})]})]})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Configuration"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-3",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Admin API URL"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"http://localhost:3000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Auth Token"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:o?"✅ Present":"❌ Missing"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"Backend Target"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"http://localhost:3000"})]})]})})]}),s.length>0&&(0,a.jsxs)(K,{defaultValue:"overview",className:"w-full",children:[(0,a.jsxs)(L,{className:"grid w-full grid-cols-4",children:[(0,a.jsx)(O,{value:"overview",children:"Overview"}),(0,a.jsx)(O,{value:"analytics",children:"Analytics"}),(0,a.jsx)(O,{value:"agent",children:"Agent Data"}),(0,a.jsx)(O,{value:"system",children:"System"})]}),(0,a.jsx)(X,{value:"overview",className:"space-y-4",children:(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"All Test Results"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:s.map((e,t)=>(0,a.jsxs)("div",{className:"border rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[f(e.category),g(e.status),(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsx)(n.E,{variant:"outline",children:e.category})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.duration&&(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:y(e.duration)}),v(e.status)]})]}),e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mt-2",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&(0,a.jsx)("div",{className:"mt-4",children:P(e)})]},t))})})]})}),(0,a.jsx)(X,{value:"analytics",className:"space-y-4",children:D("analytics").map((e,t)=>(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[g(e.status),e.name,v(e.status)]})}),(0,a.jsxs)(l.Wu,{children:[e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&P(e)]})]},t))}),(0,a.jsx)(X,{value:"agent",className:"space-y-4",children:D("agent").map((e,t)=>(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[g(e.status),e.name,v(e.status)]})}),(0,a.jsxs)(l.Wu,{children:[e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&P(e)]})]},t))}),(0,a.jsx)(X,{value:"system",className:"space-y-4",children:D("system").map((e,t)=>(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[g(e.status),e.name,v(e.status)]})}),(0,a.jsxs)(l.Wu,{children:[e.error&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded p-3 mb-4",children:(0,a.jsx)("p",{className:"text-sm text-red-800",children:e.error})}),e.data&&"success"===e.status&&P(e)]})]},t))})]})]})})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},48940:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,dynamic:()=>r});var a=s(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx","dynamic"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx","default")},49881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c={children:["",{children:["admin",{children:["api-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,48940)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/api-test/page",pathname:"/admin/api-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},61611:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},62021:(e,t,s)=>{Promise.resolve().then(s.bind(s,48940))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(60687);s(43210);var r=s(8730),n=s(24224),i=s(4780);let l=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:s=!1,...n}){let d=s?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(l({variant:t}),e),...n})}},98469:(e,t,s)=>{Promise.resolve().then(s.bind(s,31683))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,8562,7359,4541,1529,1838,4336,4987,2140],()=>s(49881));module.exports=a})();