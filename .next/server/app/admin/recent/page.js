(()=>{var e={};e.id=2648,e.ids=[2648],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12465:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>c,routeModule:()=>u,tree:()=>p});var s=t(65239),n=t(48088),a=t(88170),i=t.n(a),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let p={children:["",{children:["admin",{children:["recent",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,45500)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/recent/page",pathname:"/admin/recent",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45500:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a,dynamic:()=>n});var s=t(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx","dynamic"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx","default")},56613:(e,r,t)=>{Promise.resolve().then(t.bind(t,45500))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},86674:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,dynamic:()=>i});var s=t(60687),n=t(7126),a=t(75024);let i="force-dynamic";function o(){return(0,s.jsx)(n.U,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Recent Activity"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"View recent system activity and user actions."})]}),(0,s.jsx)(a.t,{activeTab:"recent"})]})})}},93061:(e,r,t)=>{Promise.resolve().then(t.bind(t,86674))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[80,4999,8360,878,5788,8562,7359,1838,4336,4987,2140,674],()=>t(12465));module.exports=s})();