(()=>{var e={};e.id=2430,e.ids=[2430],e.modules={2041:(e,t,s)=>{"use strict";s.d(t,{E:()=>l});var a=s(43210),r=s(49605),i=s(54024),n=["axis","item"],l=(0,a.forwardRef)((e,t)=>a.createElement(i.P,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:n,tooltipPayloadSearcher:r.uN,categoricalChartProps:e,ref:t}))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12640:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("trending-down",[["path",{d:"M16 17h6v-6",key:"t6n2it"}],["path",{d:"m22 17-8.5-8.5-5 5L2 7",key:"x473p"}]])},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>u,gC:()=>m,l6:()=>o,yv:()=>c});var a=s(60687);s(43210);var r=s(72951),i=s(78272),n=s(13964),l=s(3589),d=s(4780);function o({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function x({className:e,size:t="default",children:s,...n}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:s="popper",...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,a.jsx)(h,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(p,{})]})})}function u({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function p({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25782:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i,dynamic:()=>r});var a=s(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx","dynamic"),i=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>U,dynamic:()=>D});var a=s(60687),r=s(7126),i=s(44493),n=s(15079),l=s(87979);s(98647),s(43229);var d=s(25541),o=s(12640),c=s(41862),x=s(41312),m=s(40228),u=s(58559),h=s(53411),p=s(43210),f=s(26101),g=s(61678),j=s(85168),v=s(27747),y=s(19598),b=s(64635),w=s(66424),N=s(61855),A=s(77814),k=s(2041),S=s(22344);let D="force-dynamic";function U(){let[e,t]=(0,p.useState)(null),[s,D]=(0,p.useState)(null),[U,_]=(0,p.useState)(!0),[P,T]=(0,p.useState)(null),[C,z]=(0,p.useState)("day"),{user:R}=(0,l.A)(),Z=e=>{let t=e>0,s=t?d.A:o.A;return(0,a.jsxs)("div",{className:`flex items-center gap-1 ${t?"text-green-600":"text-red-600"}`,children:[(0,a.jsx)(s,{className:"h-3 w-3"}),(0,a.jsxs)("span",{className:"text-xs",children:[Math.abs(e).toFixed(1),"%"]})]})};return U?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(c.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading activity metrics..."})]})})}):P?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:P}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"text-primary hover:underline",children:"Try again"})]})})}):(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Activity Metrics"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Track user engagement and activity patterns."})]}),(0,a.jsxs)(n.l6,{value:C,onValueChange:e=>z(e),children:[(0,a.jsx)(n.bq,{className:"w-[180px]",children:(0,a.jsx)(n.yv,{placeholder:"Select granularity"})}),(0,a.jsxs)(n.gC,{children:[(0,a.jsx)(n.eb,{value:"day",children:"Daily"}),(0,a.jsx)(n.eb,{value:"week",children:"Weekly"}),(0,a.jsx)(n.eb,{value:"month",children:"Monthly"}),(0,a.jsx)(n.eb,{value:"year",children:"Yearly"})]})]})]}),e&&(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Daily Active Users"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.dau}),Z(e.dauChange)]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Weekly Active Users"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.wau}),Z(e.wauChange)]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Monthly Active Users"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.mau}),Z(e.mauChange)]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(i.ZB,{className:"text-sm font-medium",children:"Yearly Active Users"}),(0,a.jsx)(h.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(i.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.yau}),Z(e.yauChange)]})]})]}),s&&(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Active Users Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(f.u,{width:"100%",height:300,children:(0,a.jsxs)(g.b,{data:s.data,children:[(0,a.jsx)(j.d,{strokeDasharray:"3 3"}),(0,a.jsx)(v.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(y.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(w.N,{type:"monotone",dataKey:"activeUsers",stroke:"#8884d8",strokeWidth:2,name:"Active Users"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"New Users Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(f.u,{width:"100%",height:300,children:(0,a.jsxs)(N.Q,{data:s.data,children:[(0,a.jsx)(j.d,{strokeDasharray:"3 3"}),(0,a.jsx)(v.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(y.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(A.Gk,{type:"monotone",dataKey:"newUsers",stroke:"#82ca9d",fill:"#82ca9d",name:"New Users"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"API Requests Trend"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(f.u,{width:"100%",height:300,children:(0,a.jsxs)(k.E,{data:s.data,children:[(0,a.jsx)(j.d,{strokeDasharray:"3 3"}),(0,a.jsx)(v.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(y.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(S.y,{dataKey:"totalRequests",fill:"#ffc658",name:"Total Requests"})]})})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Average Session Time"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(f.u,{width:"100%",height:300,children:(0,a.jsxs)(g.b,{data:s.data,children:[(0,a.jsx)(j.d,{strokeDasharray:"3 3"}),(0,a.jsx)(v.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(y.h,{}),(0,a.jsx)(b.m,{labelFormatter:e=>new Date(e).toLocaleDateString(),formatter:e=>[`${e}m`,"Session Time"]}),(0,a.jsx)(w.N,{type:"monotone",dataKey:"averageSessionTime",stroke:"#ff7300",strokeWidth:2,name:"Avg Session Time (min)"})]})})})]})]}),s&&(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)(i.ZB,{children:"Summary Statistics"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.summary.totalDays}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Days Analyzed"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.summary.averageActiveUsers}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Avg Active Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:s.summary.totalNewUsers}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Total New Users"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[s.summary.growthRate,"%"]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Growth Rate"})]})]})})]})]})})}},30693:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["activity",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25782)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/activity/page",pathname:"/admin/activity",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},33873:e=>{"use strict";e.exports=require("path")},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43229:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});let a="siift_access_token",r="siift_refresh_token",i="siift_user";class n{static setTokens(e,t=!1){let s=t?localStorage:sessionStorage;s.setItem(a,e.accessToken),e.refreshToken&&s.setItem(r,e.refreshToken)}static getAccessToken(){return localStorage.getItem(a)||sessionStorage.getItem(a)}static getRefreshToken(){return localStorage.getItem(r)||sessionStorage.getItem(r)}static setUser(e,t=!1){(t?localStorage:sessionStorage).setItem(i,JSON.stringify(e))}static getUser(){try{let e=localStorage.getItem(i)||sessionStorage.getItem(i);if(!e)return null;let t=JSON.parse(e);return t.createdAt&&"string"==typeof t.createdAt&&(t.createdAt=new Date(t.createdAt)),t.updatedAt&&"string"==typeof t.updatedAt&&(t.updatedAt=new Date(t.updatedAt)),t}catch(e){return console.error("Error parsing user data:",e),null}}static clearSession(){[localStorage,sessionStorage].forEach(e=>{e.removeItem(a),e.removeItem(r),e.removeItem(i)})}static clearInvalidSession(){let e=this.getAccessToken();e&&(!e.includes(".")||3!==e.split(".").length)&&(console.log("Clearing invalid token format"),this.clearSession())}static isAuthenticated(){return!!this.getAccessToken()}static getAuthHeaders(){let e=this.getAccessToken();return e?{Authorization:`Bearer ${e}`}:{}}}},55347:(e,t,s)=>{Promise.resolve().then(s.bind(s,25782))},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},92299:(e,t,s)=>{Promise.resolve().then(s.bind(s,30136))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,8562,7359,5555,4541,1529,2775,7972,1838,4336,4987,2140],()=>s(30693));module.exports=a})();