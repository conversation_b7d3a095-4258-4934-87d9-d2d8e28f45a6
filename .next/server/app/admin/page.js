(()=>{var e={};e.id=3698,e.ids=[3698],e.modules={1132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,dynamic:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx","dynamic"),i=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8359:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>p,tree:()=>c});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1132)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12454:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>n});var s=r(60687),a=r(67602),i=r(16189);r(43210);let n="force-dynamic";function o(){return(0,i.useRouter)(),(0,s.jsx)(a.h,{children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)("div",{className:"text-center",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Redirecting to admin dashboard..."})})})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38455:(e,t,r)=>{Promise.resolve().then(r.bind(r,12454))},44031:(e,t,r)=>{Promise.resolve().then(r.bind(r,1132))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67074:(e,t,r)=>{"use strict";r.d(t,{GF:()=>a,Ni:()=>i});var s=r(52581);let a=(e,t)=>{s.oR.success(e,{description:t?.description,duration:t?.duration||4e3})},i=(e,t)=>{s.oR.error(e,{description:t?.description,duration:t?.duration||5e3})}},67602:(e,t,r)=>{"use strict";r.d(t,{h:()=>o});var s=r(60687),a=r(87979),i=r(99891),n=r(16189);function o({children:e}){let{user:t,isAuthenticated:r,isLoading:o}=(0,a.A)(),d=(0,n.useRouter)();return o?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading..."})]})}):r?t?.email?.includes("siift.ai")?(0,s.jsx)(s.Fragment,{children:e}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-4 text-destructive"}),(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Access Denied"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"You need a siift.ai email address to access the admin panel."}),(0,s.jsx)("button",{onClick:()=>d.push("/user-dashboard"),className:"mt-4 text-primary hover:underline",children:"Go to Dashboard"})]})}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(i.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Authentication Required"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Please log in to access this page."})]})})}r(43210)},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79551:e=>{"use strict";e.exports=require("url")},87979:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(78941),a=r(16189),i=r(43210),n=r(67074);function o(){let{user:e,isAuthenticated:t,error:r,pendingEmailVerification:o,emailVerificationSent:d,actions:c}=(0,s.B)(),l=(0,a.useRouter)(),[u,p]=(0,i.useState)(!1),m=(0,i.useCallback)(async e=>{p(!0);try{await c.login(e);let{user:t}=s.B.getState();(0,n.GF)("Welcome back!",{description:`Logged in as ${t?.email}`}),t?.role==="admin"?l.replace("/admin"):l.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Login failed";throw(0,n.Ni)("Login Failed",{description:e}),t}finally{p(!1)}},[c,l,p]),f=(0,i.useCallback)(async e=>{p(!0);try{await c.signup(e);let{user:t}=s.B.getState();(0,n.GF)("Account created successfully!",{description:`Welcome ${t?.name||t?.email}!`}),t?.role==="admin"?l.replace("/admin"):l.replace("/user-dashboard")}catch(t){let e=t instanceof Error?t.message:"Signup failed";throw(0,n.Ni)("Signup Failed",{description:e}),t}finally{p(!1)}},[c,l,p]),h=(0,i.useCallback)(()=>{c.logout(),l.push("/auth/login")},[c,l]),x=(0,i.useCallback)(()=>{c.setError(null)},[c]),b=(0,i.useCallback)(async(e,t)=>{try{await c.sendEmailVerification(e,t)}catch(e){throw e}},[c]);return{user:e,isAuthenticated:t,isLoading:u,error:r,pendingEmailVerification:o,emailVerificationSent:d,login:m,signup:f,logout:h,clearError:x,sendEmailVerification:b,verifyEmail:(0,i.useCallback)(async(e,t)=>{try{await c.verifyEmail(e,t)}catch(e){throw e}},[c]),resendEmailVerification:(0,i.useCallback)(async e=>{try{await c.resendEmailVerification(e)}catch(e){throw e}},[c]),updateUser:c.updateUser,refreshToken:c.refreshToken}}},96487:()=>{},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n,dynamic:()=>a,revalidate:()=>i});var s=r(37413);let a="force-dynamic",i=0;function n({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,1838],()=>r(8359));module.exports=s})();