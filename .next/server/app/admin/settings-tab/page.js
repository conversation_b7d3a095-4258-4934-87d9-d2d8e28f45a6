(()=>{var e={};e.id=896,e.ids=[896],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},49166:(e,t,r)=>{Promise.resolve().then(r.bind(r,99630))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68941:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>p});var s=r(65239),n=r(48088),a=r(88170),i=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let p={children:["",{children:["admin",{children:["settings-tab",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84104)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/admin/settings-tab/page",pathname:"/admin/settings-tab",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},73024:e=>{"use strict";e.exports=require("node:fs")},73985:(e,t,r)=>{Promise.resolve().then(r.bind(r,84104))},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},84104:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,dynamic:()=>n});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx","dynamic"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx","default")},99630:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,dynamic:()=>i});var s=r(60687),n=r(7126),a=r(75024);let i="force-dynamic";function o(){return(0,s.jsx)(n.U,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Settings"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Configure system settings and preferences."})]}),(0,s.jsx)(a.t,{activeTab:"settings"})]})})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,878,5788,8562,7359,1838,4336,4987,2140,674],()=>r(68941));module.exports=s})();