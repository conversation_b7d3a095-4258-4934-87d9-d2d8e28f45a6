(()=>{var e={};e.id=7365,e.ids=[7365],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},14952:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},17458:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("file-chart-column-increasing",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 18v-2",key:"qcmpov"}],["path",{d:"M12 18v-4",key:"q1q25u"}],["path",{d:"M16 18v-6",key:"15y0np"}]])},17971:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("chevrons-up-down",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},18179:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19959:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},22383:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i,dynamic:()=>r});var a=t(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx","dynamic"),i=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx","default")},24413:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34318:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]])},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40083:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41862:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42833:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b,dynamic:()=>j});var a=t(60687),r=t(43210),i=t(7126),n=t(44493),l=t(29523),c=t(96834),d=t(98647),o=t(5336),h=t(43649),x=t(35071),m=t(58559),p=t(41862),u=t(78122),y=t(93613),f=t(24413),v=t(99891),g=t(61611);let j="force-dynamic";function b(){let[e,s]=(0,r.useState)(null),[t,j]=(0,r.useState)(!1),[b,w]=(0,r.useState)(null),[k,N]=(0,r.useState)(null),A=async()=>{j(!0),w(null);try{let e=localStorage.getItem("siift_access_token")||sessionStorage.getItem("siift_access_token");if(!e)throw Error("No admin token found. Please login first.");(0,d.R)(e);let t=await d.i.getHealthCheck();s(t),N(new Date)}catch(e){w(e.message)}finally{j(!1)}},M=e=>{switch(e.toLowerCase()){case"healthy":case"ok":case"operational":return(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-600"});case"warning":case"degraded":return(0,a.jsx)(h.A,{className:"h-5 w-5 text-yellow-600"});case"error":case"down":case"critical":return(0,a.jsx)(x.A,{className:"h-5 w-5 text-red-600"});default:return(0,a.jsx)(m.A,{className:"h-5 w-5 text-gray-600"})}},q=e=>{switch(e.toLowerCase()){case"healthy":case"ok":case"operational":return"bg-green-100 text-green-800";case"warning":case"degraded":return"bg-yellow-100 text-yellow-800";case"error":case"down":case"critical":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},C=(()=>{if(!e)return"unknown";let s=e.status?.toLowerCase(),t=e.services||{},a=Object.values(t).some(e=>e?.toLowerCase()==="error"||e?.toLowerCase()==="down"),r=Object.values(t).some(e=>e?.toLowerCase()==="warning"||e?.toLowerCase()==="degraded");return"error"===s||a?"error":"warning"===s||r?"warning":"healthy"===s||"ok"===s?"healthy":"unknown"})();return(0,a.jsx)(i.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"System Health"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Monitor system status and service health in real-time."})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[k&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Last updated: ",k.toLocaleTimeString()]}),(0,a.jsxs)(l.$,{onClick:A,disabled:t,variant:"outline",children:[t?(0,a.jsx)(p.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]})]}),(0,a.jsx)(n.Zp,{className:`border-2 ${"healthy"===C?"border-green-200 bg-green-50":"warning"===C?"border-yellow-200 bg-yellow-50":"error"===C?"border-red-200 bg-red-50":"border-gray-200"}`,children:(0,a.jsx)(n.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-white",children:M(C)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"System Status"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,a.jsx)(c.E,{className:q(C),children:C.charAt(0).toUpperCase()+C.slice(1)}),e?.timestamp&&(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:["as of ",new Date(e.timestamp).toLocaleString()]})]})]})]})})}),b&&(0,a.jsx)(n.Zp,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(y.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("p",{className:"text-red-800",children:b})]})})}),t&&!e&&(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}),e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Service Status"})]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,a.jsx)(n.Zp,{className:"border-l-4 border-l-blue-500",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(v.A,{className:"h-6 w-6 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"System"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Main Application"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[M(e.status),(0,a.jsx)(c.E,{className:q(e.status),children:e.status})]})]})})}),e.services?.database&&(0,a.jsx)(n.Zp,{className:"border-l-4 border-l-green-500",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(g.A,{className:"h-6 w-6 text-green-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Database"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Data Storage"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[M(e.services.database),(0,a.jsx)(c.E,{className:q(e.services.database),children:e.services.database})]})]})})}),e.services?.analytics&&(0,a.jsx)(n.Zp,{className:"border-l-4 border-l-purple-500",children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(m.A,{className:"h-6 w-6 text-purple-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold",children:"Analytics"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Data Processing"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[M(e.services.analytics),(0,a.jsx)(c.E,{className:q(e.services.analytics),children:e.services.analytics})]})]})})})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"System Information"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:"Health Check Details"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Status:"}),(0,a.jsx)(c.E,{className:q(e.status),children:e.status})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Timestamp:"}),(0,a.jsx)("span",{className:"font-medium",children:e.timestamp?new Date(e.timestamp).toLocaleString():"N/A"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-muted-foreground",children:"Response Time:"}),(0,a.jsx)("span",{className:"font-medium",children:k?`${Date.now()-k.getTime()}ms`:"N/A"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-lg",children:"Service Dependencies"}),(0,a.jsx)("div",{className:"space-y-2",children:e.services&&Object.entries(e.services).map(([e,s])=>(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-muted-foreground capitalize",children:[e,":"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[M(s),(0,a.jsx)(c.E,{className:q(s),children:s})]})]},e))})]})]})})]}),(0,a.jsx)(n.Zp,{children:(0,a.jsx)(n.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-blue-600"}),(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"This page automatically refreshes every 30 seconds to provide real-time status updates."})]})})})]})]})})}},56085:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},58559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},61611:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64021:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70742:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("badge-check",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},71196:(e,s,t)=>{Promise.resolve().then(t.bind(t,42833))},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},78200:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},79551:e=>{"use strict";e.exports=require("url")},79823:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(65239),r=t(48088),i=t(88170),n=t.n(i),l=t(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d={children:["",{children:["admin",{children:["system",{children:["health",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,22383)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/system/health/page",pathname:"/admin/system/health",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},84764:(e,s,t)=>{Promise.resolve().then(t.bind(t,22383))},85778:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(60687);t(43210);var r=t(8730),i=t(24224),n=t(4780);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c({className:e,variant:s,asChild:t=!1,...i}){let c=t?r.DX:"span";return(0,a.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:s}),e),...i})}},97051:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[80,4999,8360,878,5788,8562,7359,1838,4336,4987,2140],()=>t(79823));module.exports=a})();