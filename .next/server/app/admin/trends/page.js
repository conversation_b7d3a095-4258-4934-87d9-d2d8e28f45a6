(()=>{var e={};e.id=3915,e.ids=[3915],e.modules={1633:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o={children:["",{children:["admin",{children:["trends",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,76407)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(s.bind(s,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/trends/page",pathname:"/admin/trends",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>x,eb:()=>u,gC:()=>m,l6:()=>o,yv:()=>c});var a=s(60687);s(43210);var r=s(72951),n=s(78272),i=s(13964),l=s(3589),d=s(4780);function o({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function x({className:e,size:t="default",children:s,...i}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(p,{}),(0,a.jsx)(r.LM,{className:(0,d.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function u({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function p({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(l.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23026:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34710:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>_,dynamic:()=>U});var a=s(60687),r=s(7126),n=s(44493),i=s(15079),l=s(87979);s(98647);var d=s(41862),o=s(25541),c=s(41312),x=s(23026),m=s(58559),u=s(43210),p=s(26101),h=s(49605),f=s(54024),j=["axis"],g=(0,u.forwardRef)((e,t)=>u.createElement(f.P,{chartName:"ComposedChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:j,tooltipPayloadSearcher:h.uN,categoricalChartProps:e,ref:t})),v=s(85168),y=s(27747),b=s(19598),w=s(64635),N=s(77814),k=s(22344),A=s(61678),D=s(66424),P=s(61855);let U="force-dynamic";function _(){let[e,t]=(0,u.useState)(null),[s,h]=(0,u.useState)(!0),[f,j]=(0,u.useState)(null),[U,_]=(0,u.useState)("day"),{user:z}=(0,l.A)();return s?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Loading trends data..."})]})})}):f?(0,a.jsx)(r.U,{children:(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-destructive mb-4",children:f}),(0,a.jsx)("button",{onClick:()=>window.location.reload(),className:"text-primary hover:underline",children:"Try again"})]})})}):(0,a.jsx)(r.U,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Growth Trends"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Analyze growth patterns and user engagement trends over time."})]}),(0,a.jsxs)(i.l6,{value:U,onValueChange:e=>_(e),children:[(0,a.jsx)(i.bq,{className:"w-[180px]",children:(0,a.jsx)(i.yv,{placeholder:"Select granularity"})}),(0,a.jsxs)(i.gC,{children:[(0,a.jsx)(i.eb,{value:"day",children:"Daily"}),(0,a.jsx)(i.eb,{value:"week",children:"Weekly"}),(0,a.jsx)(i.eb,{value:"month",children:"Monthly"}),(0,a.jsx)(i.eb,{value:"year",children:"Yearly"})]})]})]}),e&&(0,a.jsxs)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Growth Rate"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:["+",e.summary.growthRate,"%"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Over ",e.summary.totalDays," days"]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Avg Active Users"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.summary.averageActiveUsers}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Daily average"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total New Users"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.summary.totalNewUsers}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["In ",e.summary.totalDays," days"]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Peak Active Users"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:Math.max(...e.data.map(e=>e.activeUsers))}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Highest single day"})]})]})]}),e&&(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"User Growth Trends"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(p.u,{width:"100%",height:400,children:(0,a.jsxs)(g,{data:e.data,children:[(0,a.jsx)(v.d,{strokeDasharray:"3 3"}),(0,a.jsx)(y.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(b.h,{yAxisId:"left"}),(0,a.jsx)(b.h,{yAxisId:"right",orientation:"right"}),(0,a.jsx)(w.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(N.Gk,{yAxisId:"left",type:"monotone",dataKey:"activeUsers",fill:"#8884d8",stroke:"#8884d8",fillOpacity:.3,name:"Active Users"}),(0,a.jsx)(k.y,{yAxisId:"right",dataKey:"newUsers",fill:"#82ca9d",name:"New Users"})]})})})]}),e&&(0,a.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"Session Time Trend"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(p.u,{width:"100%",height:300,children:(0,a.jsxs)(A.b,{data:e.data,children:[(0,a.jsx)(v.d,{strokeDasharray:"3 3"}),(0,a.jsx)(y.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(b.h,{}),(0,a.jsx)(w.m,{labelFormatter:e=>new Date(e).toLocaleDateString(),formatter:e=>[`${e}m`,"Session Time"]}),(0,a.jsx)(D.N,{type:"monotone",dataKey:"averageSessionTime",stroke:"#ff7300",strokeWidth:3,name:"Avg Session Time (min)"})]})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"API Usage Trend"})}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)(p.u,{width:"100%",height:300,children:(0,a.jsxs)(P.Q,{data:e.data,children:[(0,a.jsx)(v.d,{strokeDasharray:"3 3"}),(0,a.jsx)(y.W,{dataKey:"date",tickFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(b.h,{}),(0,a.jsx)(w.m,{labelFormatter:e=>new Date(e).toLocaleDateString()}),(0,a.jsx)(N.Gk,{type:"monotone",dataKey:"totalRequests",stroke:"#ffc658",fill:"#ffc658",fillOpacity:.6,name:"Total Requests"})]})})})]})]})]})})}},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76407:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n,dynamic:()=>r});var a=s(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx","dynamic"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx","default")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79238:(e,t,s)=>{Promise.resolve().then(s.bind(s,34710))},79551:e=>{"use strict";e.exports=require("url")},87486:(e,t,s)=>{Promise.resolve().then(s.bind(s,76407))}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[80,4999,8360,878,5788,8562,7359,5555,4541,1529,2775,7972,1838,4336,4987,2140],()=>s(1633));module.exports=a})();