(()=>{var e={};e.id=9977,e.ids=[9977],e.modules={1557:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n,dynamic:()=>a});var s=t(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx","dynamic"),n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9852:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>U,dynamic:()=>I});var s=t(60687),a=t(43210),n=t(91476),i=t(39185),o=t(29308),l=t(43984),c=t(23166),d=t(92576),u=t(88920);function p({showStickyHeader:e=!0}){let[r,t]=(0,a.useState)(!1);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.P.header,{className:"w-full relative z-20 bg-transparent",initial:{opacity:1},animate:{opacity:r?.8:1,scale:r?.98:1},transition:{duration:.2},children:(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,s.jsx)(i.c,{})}),(0,s.jsx)(c.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,s.jsx)("div",{className:"hidden md:flex ml-6",children:(0,s.jsx)(n.N,{})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.U,{}),(0,s.jsx)(o.B,{})]})]})}),e&&(0,s.jsx)(u.N,{children:r&&(0,s.jsxs)(d.P.header,{initial:{y:-100,opacity:0},animate:{y:0,opacity:1},exit:{y:-100,opacity:0},transition:{duration:.3,ease:"easeInOut"},className:"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border shadow-lg",children:[(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04] pointer-events-none",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsxs)("div",{className:"container mx-auto px-4 flex h-16 items-center justify-between relative z-10",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("div",{className:"flex items-center mr-4 md:hidden",children:(0,s.jsx)(i.c,{})}),(0,s.jsx)(c.g,{size:32,animated:!1,showText:!0,href:"/"}),(0,s.jsx)("div",{className:"hidden md:flex ml-6",children:(0,s.jsx)(n.N,{})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.U,{}),(0,s.jsx)(o.B,{})]})]})]})})]})}var m=t(1428);function h(){return(0,s.jsx)("div",{className:"relative z-10 container mx-auto px-4 text-center py-20 md:py-20 flex-1 flex items-center",children:(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(m.j,{variant:"dashboard",title:"Let's build something amazing together!",showWelcomeBadge:!0,showFeatureBadges:!0})})})}var x=t(29523),g=t(44493),f=t(96834),b=t(70334),v=t(93661),y=t(16189);function j(){let e=(0,y.useRouter)(),r=e=>{switch(e){case"active":return"bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300";case"in-progress":case"completed":return"bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300";case"planning":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"}},t=e=>{switch(e){case"blue":return"bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400";case"purple":return"bg-purple-100 dark:bg-purple-900 text-purple-600 dark:text-purple-400";case"green":return"bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400";case"indigo":return"bg-indigo-100 dark:bg-indigo-900 text-indigo-600 dark:text-indigo-400";case"red":return"bg-red-100 dark:bg-red-900 text-red-600 dark:text-red-400";default:return"bg-gray-100 dark:bg-gray-900 text-gray-600 dark:text-gray-400"}};return(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsxs)(g.Zp,{className:"bg-gray-50 dark:bg-card",children:[(0,s.jsx)(g.aR,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(g.ZB,{className:"flex items-center gap-2",children:"Recent Projects"}),(0,s.jsx)(g.BT,{children:"Your active projects and quick access to create new ones."})]}),(0,s.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>{e.push("/projects")},className:"border-2 hover:bg-primary/10 hover:text-primary transition-all duration-200",children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),"View All"]})]})}),(0,s.jsx)(g.Wu,{children:(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-3",children:[{id:"1",name:"Website Redesign",description:"Complete redesign of company website with modern UI",status:"active",progress:75,color:"blue",initials:"WR"},{id:"2",name:"Mobile App",description:"iOS and Android app development for the platform",status:"in-progress",progress:45,color:"purple",initials:"MA"},{id:"3",name:"Marketing Campaign",description:"Q1 marketing campaign planning and execution",status:"completed",progress:100,color:"green",initials:"MC"},{id:"4",name:"API Documentation",description:"Comprehensive API documentation and developer guides",status:"active",progress:85,color:"indigo",initials:"AD"},{id:"5",name:"Database Migration",description:"Migrate legacy database to new cloud infrastructure",status:"planning",progress:15,color:"red",initials:"DM"}].map(a=>(0,s.jsx)(g.Zp,{className:"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer group",onClick:()=>e.push(`/projects/${a.id}`),children:(0,s.jsxs)(g.Wu,{className:"px-4 py-0",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,s.jsx)("div",{className:`w-12 h-12 rounded-lg flex items-center justify-center ${t(a.color)}`,children:(0,s.jsx)("span",{className:"font-semibold",children:a.initials})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.E,{variant:"secondary",className:r(a.status),children:a.status.replace("-"," ")}),(0,s.jsx)(x.$,{variant:"ghost",size:"icon",className:"h-6 w-6",children:(0,s.jsx)(v.A,{className:"h-3 w-3"})})]})]}),(0,s.jsx)("h3",{className:"font-medium mb-2",children:a.name}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-2",children:a.description})]})},a.id))})})]})})}var w=t(18179),k=t(5336),N=t(48730),C=t(25541);function A(){let e=[{title:"Active Projects",value:"4",description:"Currently in progress",icon:w.A,color:"text-blue-600",bgColor:"bg-blue-100 dark:bg-blue-900/20"},{title:"Completed Tasks",value:"23",description:"This month",icon:k.A,color:"text-green-600",bgColor:"bg-green-100 dark:bg-green-900/20"},{title:"Hours Saved",value:"47",description:"Through automation",icon:N.A,color:"text-purple-600",bgColor:"bg-purple-100 dark:bg-purple-900/20"},{title:"Team Efficiency",value:"94%",description:"+12% from last month",icon:C.A,color:"text-indigo-600",bgColor:"bg-indigo-100 dark:bg-indigo-900/20"}];return(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)("div",{className:"flex flex-col gap-10",children:(0,s.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:e.map((e,r)=>(0,s.jsxs)(g.Zp,{className:"bg-gray-50 dark:bg-card hover:shadow-md hover:bg-gray-100 dark:hover:bg-accent/50 transition-all duration-200 cursor-pointer",children:[(0,s.jsxs)(g.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-1",children:[(0,s.jsx)(g.ZB,{className:"text-sm font-medium",children:e.title}),(0,s.jsx)("div",{className:`p-2 rounded-lg ${e.bgColor}`,children:(0,s.jsx)(e.icon,{className:`h-4 w-4 ${e.color}`})})]}),(0,s.jsxs)(g.Wu,{className:"pt-1",children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:e.value}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},r))})})})}var E=t(65353),P=t(38885),D=t(72745),T=t(95450),F=t(93383),R=t(41862);let I="force-dynamic";function U(){(0,y.useRouter)();let{user:e,isSignedIn:r,isLoaded:t}=(0,T.P)(),{data:a,isLoading:n}=(0,D.Jd)(),{data:i}=(0,D.rE)();(0,P.Zd)();let{currentStep:o}=(0,F.h)();return t&&e?(0,s.jsxs)("div",{className:"min-h-screen bg-background relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 project-main-content"}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-[0.02] dark:opacity-[0.04]",style:{backgroundImage:"url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",backgroundSize:"256px 256px"}}),(0,s.jsx)(p,{}),(0,s.jsxs)("main",{className:"relative z-10",children:[(0,s.jsx)(h,{}),(0,s.jsxs)("div",{className:"container mx-auto px-4 space-y-8 pb-8",children:[(0,s.jsx)(A,{}),(0,s.jsx)(j,{})]})]}),"idle"!==o&&(0,s.jsx)(E.x,{})]}):(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-background",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(R.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Loading dashboard..."})]})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},18179:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("folder-open",[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24490:(e,r,t)=>{Promise.resolve().then(t.bind(t,1557))},26134:(e,r,t)=>{"use strict";t.d(r,{UC:()=>et,VY:()=>ea,ZL:()=>ee,bL:()=>Y,bm:()=>en,hE:()=>es,hJ:()=>er,l9:()=>X});var s=t(43210),a=t(70569),n=t(98599),i=t(11273),o=t(96963),l=t(65551),c=t(31355),d=t(32547),u=t(25028),p=t(46059),m=t(14163),h=t(1359),x=t(42247),g=t(63376),f=t(8730),b=t(60687),v="Dialog",[y,j]=(0,i.A)(v),[w,k]=y(v),N=e=>{let{__scopeDialog:r,children:t,open:a,defaultOpen:n,onOpenChange:i,modal:c=!0}=e,d=s.useRef(null),u=s.useRef(null),[p,m]=(0,l.i)({prop:a,defaultProp:n??!1,onChange:i,caller:v});return(0,b.jsx)(w,{scope:r,triggerRef:d,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:p,onOpenChange:m,onOpenToggle:s.useCallback(()=>m(e=>!e),[m]),modal:c,children:t})};N.displayName=v;var C="DialogTrigger",A=s.forwardRef((e,r)=>{let{__scopeDialog:t,...s}=e,i=k(C,t),o=(0,n.s)(r,i.triggerRef);return(0,b.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":Z(i.open),...s,ref:o,onClick:(0,a.m)(e.onClick,i.onOpenToggle)})});A.displayName=C;var E="DialogPortal",[P,D]=y(E,{forceMount:void 0}),T=e=>{let{__scopeDialog:r,forceMount:t,children:a,container:n}=e,i=k(E,r);return(0,b.jsx)(P,{scope:r,forceMount:t,children:s.Children.map(a,e=>(0,b.jsx)(p.C,{present:t||i.open,children:(0,b.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};T.displayName=E;var F="DialogOverlay",R=s.forwardRef((e,r)=>{let t=D(F,e.__scopeDialog),{forceMount:s=t.forceMount,...a}=e,n=k(F,e.__scopeDialog);return n.modal?(0,b.jsx)(p.C,{present:s||n.open,children:(0,b.jsx)(U,{...a,ref:r})}):null});R.displayName=F;var I=(0,f.TL)("DialogOverlay.RemoveScroll"),U=s.forwardRef((e,r)=>{let{__scopeDialog:t,...s}=e,a=k(F,t);return(0,b.jsx)(x.A,{as:I,allowPinchZoom:!0,shards:[a.contentRef],children:(0,b.jsx)(m.sG.div,{"data-state":Z(a.open),...s,ref:r,style:{pointerEvents:"auto",...s.style}})})}),$="DialogContent",_=s.forwardRef((e,r)=>{let t=D($,e.__scopeDialog),{forceMount:s=t.forceMount,...a}=e,n=k($,e.__scopeDialog);return(0,b.jsx)(p.C,{present:s||n.open,children:n.modal?(0,b.jsx)(z,{...a,ref:r}):(0,b.jsx)(O,{...a,ref:r})})});_.displayName=$;var z=s.forwardRef((e,r)=>{let t=k($,e.__scopeDialog),i=s.useRef(null),o=(0,n.s)(r,t.contentRef,i);return s.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(B,{...e,ref:o,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),t.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let r=e.detail.originalEvent,t=0===r.button&&!0===r.ctrlKey;(2===r.button||t)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),O=s.forwardRef((e,r)=>{let t=k($,e.__scopeDialog),a=s.useRef(!1),n=s.useRef(!1);return(0,b.jsx)(B,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:r=>{e.onCloseAutoFocus?.(r),r.defaultPrevented||(a.current||t.triggerRef.current?.focus(),r.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:r=>{e.onInteractOutside?.(r),r.defaultPrevented||(a.current=!0,"pointerdown"===r.detail.originalEvent.type&&(n.current=!0));let s=r.target;t.triggerRef.current?.contains(s)&&r.preventDefault(),"focusin"===r.detail.originalEvent.type&&n.current&&r.preventDefault()}})}),B=s.forwardRef((e,r)=>{let{__scopeDialog:t,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,u=k($,t),p=s.useRef(null),m=(0,n.s)(r,p);return(0,h.Oh)(),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,b.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":Z(u.open),...l,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(K,{titleId:u.titleId}),(0,b.jsx)(V,{contentRef:p,descriptionId:u.descriptionId})]})]})}),q="DialogTitle",M=s.forwardRef((e,r)=>{let{__scopeDialog:t,...s}=e,a=k(q,t);return(0,b.jsx)(m.sG.h2,{id:a.titleId,...s,ref:r})});M.displayName=q;var S="DialogDescription",G=s.forwardRef((e,r)=>{let{__scopeDialog:t,...s}=e,a=k(S,t);return(0,b.jsx)(m.sG.p,{id:a.descriptionId,...s,ref:r})});G.displayName=S;var H="DialogClose",W=s.forwardRef((e,r)=>{let{__scopeDialog:t,...s}=e,n=k(H,t);return(0,b.jsx)(m.sG.button,{type:"button",...s,ref:r,onClick:(0,a.m)(e.onClick,()=>n.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}W.displayName=H;var L="DialogTitleWarning",[Q,J]=(0,i.q)(L,{contentName:$,titleName:q,docsSlug:"dialog"}),K=({titleId:e})=>{let r=J(L),t=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(t))},[t,e]),null},V=({contentRef:e,descriptionId:r})=>{let t=J("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${t.contentName}}.`;return s.useEffect(()=>{let t=e.current?.getAttribute("aria-describedby");r&&t&&(document.getElementById(r)||console.warn(a))},[a,e,r]),null},Y=N,X=A,ee=T,er=R,et=_,es=M,ea=G,en=W},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29308:(e,r,t)=>{"use strict";t.d(r,{B:()=>p});var s=t(60687),a=t(58869),n=t(84027),i=t(40083),o=t(16189),l=t(70042),c=t(66420),d=t(21342),u=t(95450);function p(){let{user:e,signOut:r,firstName:t,email:p,fullName:m}=(0,u.P)(),h=(0,o.useRouter)();return e?(0,s.jsxs)(d.rI,{children:[(0,s.jsx)(d.ty,{asChild:!0,children:(0,s.jsx)("div",{children:(0,s.jsx)(l.V,{icon:a.A,text:t||"User",variant:"ghost",size:"md",layout:"horizontal",showBorder:!0,hoverColor:"green",hoverScale:!0,iconClassName:c.hS.md})})}),(0,s.jsxs)(d.SQ,{className:"w-56 bg-background border-border",align:"end",forceMount:!0,children:[(0,s.jsx)(d.lp,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none text-foreground",children:m||"User"}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:p||""})]})}),(0,s.jsx)(d.mB,{className:"bg-border"}),(0,s.jsxs)(d._2,{onClick:()=>{h.push("/profile")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,s.jsx)(a.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]}),(0,s.jsxs)(d._2,{onClick:()=>{h.push("/settings")},className:"hover:bg-[#166534]/10 hover:text-[#166534] focus:bg-[#166534]/10 focus:text-[#166534] cursor-pointer",children:[(0,s.jsx)(n.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Settings"})]}),(0,s.jsx)(d.mB,{className:"bg-border"}),(0,s.jsxs)(d._2,{onClick:()=>{r()},className:"hover:bg-destructive/10 hover:text-destructive focus:bg-destructive/10 focus:text-destructive cursor-pointer",children:[(0,s.jsx)(i.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]}):null}},33873:e=>{"use strict";e.exports=require("path")},38885:(e,r,t)=>{"use strict";t.d(r,{Zd:()=>c,j9:()=>l});var s=t(8693),a=t(54050),n=t(95450),i=t(50582),o=t(52581);function l(){let{userId:e,getToken:r}=(0,n.P)();(0,s.jE)();let t=function(){let{userId:e,getToken:r}=(0,n.P)(),t=(0,s.jE)();return(0,a.n)({mutationFn:async({internalUserId:t,userData:s})=>{let a=await r(),n=await fetch(`http://localhost:3000/api/users/${t}`,{method:"PATCH",headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}},body:JSON.stringify({...s,clerkId:e})});if(!n.ok){let e=await n.text();throw Error(`Failed to update user: ${n.status} ${e}`)}return n.json()},onMutate:async({userData:r})=>{await t.cancelQueries({queryKey:i.lH.user(e)});let s=t.getQueryData(i.lH.user(e));if(s){let a={...s,...r,updatedAt:new Date().toISOString()};t.setQueryData(i.lH.user(e),a)}return{previousUser:s}},onError:(r,s,a)=>{a?.previousUser&&t.setQueryData(i.lH.user(e),a.previousUser),console.error("Failed to update user:",r),o.oR.error("Failed to update profile. Please try again.")},onSettled:()=>{i.WG.user(e)},onSuccess:e=>{o.oR.success("Profile updated successfully!"),console.log("User updated successfully:",e)}})}();return(0,a.n)({mutationFn:async s=>{let a=await r(),n=await fetch(`http://localhost:3000/api/users/clerk/${e}`,{headers:{"Content-Type":"application/json",...a&&{Authorization:`Bearer ${a}`}}});if(!n.ok)throw Error("User not found in backend. Please sync your account first.");let i=await n.json();return t.mutateAsync({internalUserId:i.id,userData:s})},onError:e=>{console.error("Failed to update user by Clerk ID:",e),o.oR.error(e.message||"Failed to update profile. Please try again.")}})}function c(){let{user:e,userId:r,email:t,firstName:l,lastName:c}=(0,n.P)(),d=function(){let{getToken:e}=(0,n.P)(),r=(0,s.jE)();return(0,a.n)({mutationFn:async r=>{let t=await e(),s="http://localhost:3000",a=await fetch(`${s}/api/users`,{method:"POST",headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}},body:JSON.stringify({...r,role:r.role||"user",status:r.status||"active",timezone:r.timezone||"UTC",preferences:r.preferences||{notifications:!0,theme:"system",language:"en"}})});if(!a.ok){let e=await a.text();if(console.error("Backend response error:",{status:a.status,statusText:a.statusText,errorText:e,url:`${s}/api/users`}),400===a.status&&(e.includes("already exists")||e.includes("clerk ID already exists"))){console.log("User already exists, fetching existing user...");let e=await fetch(`${s}/api/users/clerk/${r.clerkId}`,{headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}}});if(e.ok){let r=await e.json();return console.log("Found existing user:",r),r}throw console.log("Could not fetch existing user, but user exists in backend"),Error("User already exists in backend")}throw Error(`Failed to create user: ${a.status} ${a.statusText} - ${e}`)}return a.json()},onSuccess:(e,t)=>{r.setQueryData(i.lH.user(t.clerkId),e),i.WG.user(t.clerkId),o.oR.success("Account synced successfully!"),console.log("User synced successfully in backend:",e)},onError:(e,r)=>{console.error("Failed to create user in backend:",e),console.error("User data that failed:",r),e.message&&e.message.includes("already exists")?console.log("User already exists in backend - this is expected"):o.oR.error(`Failed to sync account: ${e.message}`)}})}();return(0,a.n)({mutationFn:async()=>{if(!e||!r)throw Error("User not available");let s={email:t||"",firstName:l||"",lastName:c||"",clerkId:r,role:"user",status:"active",avatarUrl:e.imageUrl||"",bio:"",timezone:"UTC",preferences:{notifications:!0,theme:"system",language:"en"}};return d.mutateAsync(s)},onError:e=>{console.error("Failed to sync user to backend:",e),o.oR.error("Failed to sync account. Please contact support.")}})}},39185:(e,r,t)=>{"use strict";t.d(r,{c:()=>h});var s=t(60687),a=t(99891),n=t(12941),i=t(85814),o=t.n(i),l=t(16189),c=t(43210),d=t(29523),u=t(67146),p=t(87979),m=t(4780);function h(){let[e,r]=(0,c.useState)(!1),t=(0,l.usePathname)(),{user:i}=(0,p.A)(),h=i?.role==="admin"?[{name:"Admin",href:"/admin",icon:a.A}]:[{name:"Dashboard",href:"/user-dashboard",icon:void 0}];return(0,s.jsxs)(u.cj,{open:e,onOpenChange:r,children:[(0,s.jsx)(u.CG,{asChild:!0,children:(0,s.jsxs)(d.$,{variant:"ghost",className:"mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors",children:[(0,s.jsx)(n.A,{className:"h-6 w-6"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Menu"})]})}),(0,s.jsx)(u.h,{side:"left",className:"pr-0 bg-background border-r border-border",children:(0,s.jsx)("nav",{className:"flex flex-col space-y-3 mt-6",children:h.filter(e=>t!==e.href).map(e=>{let a="/admin"===e.href?t.startsWith("/admin"):t===e.href;return(0,s.jsxs)(o(),{href:e.href,onClick:()=>r(!1),className:(0,m.cn)("text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",a?"text-[#166534] bg-[#166534]/10":"text-muted-foreground hover:text-[#166534]"),children:[e.icon&&(0,s.jsx)(e.icon,{className:"h-4 w-4"}),e.name,a&&(0,s.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full"})]},e.href)})})})]})}},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},58869:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64242:(e,r,t)=>{Promise.resolve().then(t.bind(t,9852))},72745:(e,r,t)=>{"use strict";t.d(r,{Jd:()=>i,rE:()=>o});var s=t(95450),a=t(50582),n=t(51423);function i(){let{userId:e,getToken:r,isSignedIn:t}=(0,s.P)();return(0,n.I)({queryKey:a.lH.user(e),queryFn:async()=>{if(!e)throw Error("User ID not available");let t=await r(),s=`http://localhost:3000/api/users/clerk/${e}`;console.log("Fetching user from backend:",{url:s,userId:e,hasToken:!!t});let a=new AbortController,n=setTimeout(()=>a.abort(),1e4),i=await fetch(s,{headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}},signal:a.signal});if(clearTimeout(n),console.log("Backend response:",{status:i.status,statusText:i.statusText,ok:i.ok}),!i.ok){if(404===i.status)throw Error("User not found in backend");throw Error(`Failed to fetch user: ${i.status} ${i.statusText}`)}return i.json()},enabled:!!e&&t,staleTime:3e5,gcTime:6e5,retry:(e,r)=>!(r?.message?.includes("User not found")||r?.message?.includes("Failed to fetch"))&&r?.name!=="AbortError"&&e<2,throwOnError:!1})}function o(){let{userId:e,getToken:r,isSignedIn:t}=(0,s.P)();return(0,n.I)({queryKey:["user","exists",e],queryFn:async()=>{if(!e)return!1;try{let t=await r(),s="http://localhost:3000";s.includes("localhost");let a=new AbortController,n=setTimeout(()=>a.abort(),5e3),i=await fetch(`${s}/api/users/clerk/${e}`,{method:"GET",headers:{"Content-Type":"application/json",...t&&{Authorization:`Bearer ${t}`}},signal:a.signal});return clearTimeout(n),console.log("User exists check:",{userId:e,status:i.status,exists:i.ok}),i.ok}catch(e){return console.error("Error checking if user exists:",e),!1}},enabled:!!e&&t,staleTime:3e4,retry:!1,throwOnError:!1})}},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77201:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),a=t(48088),n=t(88170),i=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let c={children:["",{children:["user-dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1557)),"/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/user-dashboard/page",pathname:"/user-dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},91476:(e,r,t)=>{"use strict";t.d(r,{N:()=>c});var s=t(60687),a=t(95450),n=t(4780),i=t(85814),o=t.n(i),l=t(16189);function c(){let e=(0,l.usePathname)(),{user:r}=(0,a.P)();return(0,s.jsx)("nav",{className:"flex items-center space-x-6",children:[{name:"Dashboard",href:"/user-dashboard"}].filter(r=>e!==r.href).map(r=>(0,s.jsxs)(o(),{href:r.href,className:(0,n.cn)("text-sm font-medium transition-colors relative",e===r.href?"text-[#166534] hover:text-[#166534]":"text-muted-foreground hover:text-[#166534]"),children:[r.name,e===r.href&&(0,s.jsx)("div",{className:"absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full"})]},r.href))})}},93661:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[80,4999,8360,878,5788,2576,3884,9647,1838,4336,6912,5353,5746],()=>t(77201));module.exports=s})();