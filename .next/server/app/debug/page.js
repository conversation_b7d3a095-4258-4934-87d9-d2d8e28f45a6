(()=>{var e={};e.id=9302,e.ids=[9302],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8730:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,Dc:()=>d,TL:()=>o});var s=t(43210),a=t(98599),n=t(60687);function o(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var o;let e,i,l=(o=t,(i=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(i=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),d=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{let r=n(...e);return a(...e),r}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(d.ref=r?(0,a.t)(r,l):l),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...o}=e,i=s.Children.toArray(a),l=i.find(c);if(l){let e=l.props.children,a=i.map(r=>r!==l?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...o,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(r,{...o,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var i=o("Slot"),l=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=l,r}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,r,t)=>{"use strict";t.d(r,{hO:()=>l,sG:()=>i});var s=t(43210),a=t(51215),n=t(8730),o=t(60687),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(a?t:r,{...n,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function l(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},14713:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var s=t(60687),a=t(43210),n=t(41330),o=t(14792),i=t(29523),l=t(44493),d=t(96834),c=t(89667),u=t(54300),p=t(34729),g=t(12597),h=t(13861),f=t(70615),x=t(52581);function v(){let{isSignedIn:e,getToken:r}=(0,n.d)(),{user:t}=(0,o.Jd)(),{get:v,post:b,isSignedIn:m}=function(){let{getToken:e,isSignedIn:r}=(0,n.d)(),t=(0,a.useCallback)(async(t,s={})=>{if(!r)throw Error("User is not signed in");let a=await e();if(!a)throw Error("Failed to get authentication token");let n={"Content-Type":"application/json",Authorization:`Bearer ${a}`},o=await fetch(`http://localhost:3000${t}`,{...s,headers:{...n,...s.headers}});if(!o.ok){let e=await o.text();throw Error(`API request failed: ${o.status} ${o.statusText} - ${e}`)}return o},[e,r]),s=(0,a.useCallback)(async e=>(await t(e,{method:"GET"})).json(),[t]),o=(0,a.useCallback)(async(e,r)=>(await t(e,{method:"POST",body:r?JSON.stringify(r):void 0})).json(),[t]),i=(0,a.useCallback)(async(e,r)=>(await t(e,{method:"PUT",body:r?JSON.stringify(r):void 0})).json(),[t]),l=(0,a.useCallback)(async e=>(await t(e,{method:"DELETE"})).json(),[t]),d=(0,a.useCallback)(async(e,r)=>(await t(e,{method:"PATCH",body:r?JSON.stringify(r):void 0})).json(),[t]);return{makeAuthenticatedRequest:t,get:s,post:o,put:i,delete:l,patch:d,isSignedIn:r,getToken:e}}(),{token:y,fetchAndLogToken:j,copyTokenToClipboard:k}=function(){let{getToken:e,isSignedIn:r}=(0,n.d)(),[t,s]=(0,a.useState)(null),[o,i]=(0,a.useState)(!1),l=(0,a.useCallback)(async(t=!0)=>{if(!r)return t&&console.log("❌ User not signed in"),null;i(!0);try{let r=await e();if(s(r),t&&r){console.group("\uD83D\uDD10 Clerk Token Retrieved"),console.log("\uD83C\uDFAB Token:",r),console.log("\uD83D\uDCCF Length:",r.length),console.log("⏰ Retrieved at:",new Date().toISOString());try{let e=JSON.parse(atob(r.split(".")[1]));console.log("\uD83C\uDD94 Subject (User ID):",e.sub),console.log("\uD83D\uDCC5 Issued At:",new Date(1e3*e.iat)),console.log("⏰ Expires At:",new Date(1e3*e.exp)),console.log("\uD83C\uDFE2 Issuer:",e.iss),console.log("\uD83D\uDCCB Full Payload:",e)}catch(e){console.log("Could not decode JWT payload")}console.groupEnd()}return r}catch(e){return console.error("❌ Error fetching token:",e),null}finally{i(!1)}},[e,r]),d=(0,a.useCallback)(async()=>await l(!1),[l]),c=(0,a.useCallback)(()=>{t?(console.group("\uD83D\uDD10 Current Stored Token"),console.log("\uD83C\uDFAB Token:",t),console.log("\uD83D\uDCCF Length:",t.length),console.groupEnd()):console.log("❌ No token currently stored")},[t]);return{token:t,loading:o,isSignedIn:r,fetchAndLogToken:l,getTokenSilently:d,logCurrentToken:c,copyTokenToClipboard:()=>{t&&(navigator.clipboard.writeText(t),console.log("\uD83D\uDCCB Token copied to clipboard"))},tokenLength:t?.length||0,hasToken:!!t}}(),[w,N]=(0,a.useState)(""),[C,A]=(0,a.useState)(!1),[T,P]=(0,a.useState)("/api/auth/me"),[E,S]=(0,a.useState)(""),[D,R]=(0,a.useState)(!1),I=async()=>{try{R(!0);let e=await r();e?(N(e),x.oR.success("Token retrieved successfully!"),console.log("\uD83C\uDFAB Fresh Clerk Token:",e)):x.oR.error("Failed to get token")}catch(e){console.error("Error getting token:",e),x.oR.error("Error getting token")}finally{R(!1)}},$=async()=>{try{R(!0),S("");let e=await v(T);S(JSON.stringify(e,null,2)),x.oR.success("API call successful!")}catch(r){let e=r instanceof Error?r.message:String(r);S(`Error: ${e}`),x.oR.error("API call failed"),console.error("API call error:",r)}finally{R(!1)}},_=async()=>{try{R(!0),S("");let e=await r();if(!e)throw Error("No token available");let t=await fetch(`http://localhost:3000${T}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`}});if(!t.ok)throw Error(`HTTP ${t.status}: ${t.statusText}`);let s=await t.json();S(JSON.stringify(s,null,2)),x.oR.success("Manual API call successful!")}catch(r){let e=r instanceof Error?r.message:String(r);S(`Error: ${e}`),x.oR.error("Manual API call failed"),console.error("Manual API call error:",r)}finally{R(!1)}},O=w?(e=>{try{let r=JSON.parse(atob(e.split(".")[1]));return{userId:r.sub,email:r.email,issuedAt:new Date(1e3*r.iat),expiresAt:new Date(1e3*r.exp),issuer:r.iss,fullPayload:r}}catch(e){return null}})(w):null;return e?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:["Clerk Token Management",(0,s.jsx)(d.E,{variant:e?"default":"destructive",children:e?"Signed In":"Not Signed In"})]})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Current User"}),(0,s.jsxs)("div",{className:"text-sm space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Email:"})," ",t?.emailAddresses[0]?.emailAddress]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"User ID:"})," ",t?.id]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Name:"})," ",t?.firstName," ",t?.lastName]})]})]}),(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(i.$,{onClick:I,disabled:D,className:"w-full",children:D?"Getting Token...":"Get Fresh Clerk Token"})}),w&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("h4",{className:"font-semibold",children:"Current Token"}),(0,s.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>A(!C),children:C?(0,s.jsx)(g.A,{className:"h-4 w-4"}):(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{w&&(navigator.clipboard.writeText(w),x.oR.success("Token copied to clipboard!"))},children:(0,s.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"bg-muted p-3 rounded-lg",children:(0,s.jsx)("code",{className:"text-xs break-all",children:C?w:`${w.substring(0,50)}...`})}),O&&(0,s.jsxs)("div",{className:"text-sm space-y-1",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"User ID:"})," ",O.userId]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Issued:"})," ",O.issuedAt.toLocaleString()]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Expires:"})," ",O.expiresAt.toLocaleString()]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Issuer:"})," ",O.issuer]})]})]})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Test Backend API Calls"})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(u.J,{htmlFor:"endpoint",children:"API Endpoint"}),(0,s.jsx)(c.p,{id:"endpoint",value:T,onChange:e=>P(e.target.value),placeholder:"/api/auth/me"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[(0,s.jsx)(i.$,{onClick:$,disabled:D,variant:"default",children:D?"Calling...":"Test with useClerkApi Hook"}),(0,s.jsx)(i.$,{onClick:_,disabled:D,variant:"outline",children:D?"Calling...":"Test Manual API Call"})]}),E&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"font-semibold",children:"API Response"}),(0,s.jsx)(p.T,{value:E,readOnly:!0,className:"min-h-[200px] font-mono text-xs"})]})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Code Examples"})}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"font-semibold",children:"1. Using useClerkApi Hook (Recommended)"}),(0,s.jsx)("pre",{className:"bg-muted p-3 rounded-lg text-xs overflow-x-auto",children:`import { useClerkApi } from '@/hooks/useClerkApi';

const { get, post, put, delete: del } = useClerkApi();

// GET request
const data = await get('/api/users/me');

// POST request
const result = await post('/api/projects', { name: 'My Project' });`})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"font-semibold",children:"2. Manual Token Usage"}),(0,s.jsx)("pre",{className:"bg-muted p-3 rounded-lg text-xs overflow-x-auto",children:`import { useAuth } from '@clerk/nextjs';

const { getToken } = useAuth();

const token = await getToken();
const response = await fetch('/api/endpoint', {
  headers: {
    'Authorization': \`Bearer \${token}\`,
    'Content-Type': 'application/json'
  }
});`})]})]})]})]}):(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Clerk Token Example"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Please sign in to test Clerk token functionality."})})]})}function b(){return(0,s.jsx)("div",{className:"min-h-screen bg-background p-6",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Debug Dashboard"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Debug tools for testing various integrations and features"})]}),(0,s.jsx)("div",{className:"space-y-8",children:(0,s.jsx)(v,{})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24224:(e,r,t)=>{"use strict";t.d(r,{F:()=>o});var s=t(49384);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,o=(e,r)=>t=>{var s;if((null==r?void 0:r.variants)==null)return n(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:o,defaultVariants:i}=r,l=Object.keys(o).map(e=>{let r=null==t?void 0:t[e],s=null==i?void 0:i[e];if(null===r)return null;let n=a(r)||a(s);return o[e][n]}),d=t&&Object.entries(t).reduce((e,r)=>{let[t,s]=r;return void 0===s||(e[t]=s),e},{});return n(e,l,null==r||null==(s=r.compoundVariants)?void 0:s.reduce((e,r)=>{let{class:t,className:s,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...d}[r]):({...i,...d})[r]===t})?[...e,t,s]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var s=t(60687),a=t(8730),n=t(24224);t(43210);var o=t(4780);let i=(0,n.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:n=!1,...l}){let d=n?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:t,className:e})),...l})}},29959:(e,r,t)=>{Promise.resolve().then(t.bind(t,48024))},33627:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=t(65239),a=t(48088),n=t(88170),o=t.n(n),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["debug",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,48024)),"/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/debug/page",pathname:"/debug",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33873:e=>{"use strict";e.exports=require("path")},34729:(e,r,t)=>{"use strict";t.d(r,{T:()=>o});var s=t(60687),a=t(43210),n=t(4780);let o=a.forwardRef(({className:e,...r},t)=>(0,s.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...r}));o.displayName="Textarea"},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var s=t(60687);t(43210);var a=t(4780);function n({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex px-6 [.border-t]:pt-6",e),...r})}},48024:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx","default")},54300:(e,r,t)=>{"use strict";t.d(r,{J:()=>l});var s=t(60687),a=t(43210),n=t(14163),o=a.forwardRef((e,r)=>(0,s.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));o.displayName="Label";var i=t(4780);function l({className:e,...r}){return(0,s.jsx)(o,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...r})}},60223:(e,r,t)=>{Promise.resolve().then(t.bind(t,14713))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70615:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(60687);t(43210);var a=t(4780);function n({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},96834:(e,r,t)=>{"use strict";t.d(r,{E:()=>l});var s=t(60687);t(43210);var a=t(8730),n=t(24224),o=t(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:r,asChild:t=!1,...n}){let l=t?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:r}),e),...n})}},98599:(e,r,t)=>{"use strict";t.d(r,{s:()=>o,t:()=>n});var s=t(43210);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function o(...e){return s.useCallback(n(...e),e)}}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[80,4999,8360,1838],()=>t(33627));module.exports=s})();