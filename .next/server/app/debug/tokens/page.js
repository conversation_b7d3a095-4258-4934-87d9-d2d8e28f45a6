(()=>{var e={};e.id=6513,e.ids=[4181,6513],e.modules={363:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3363:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(60687),n=r(93854),a=r(74456);function o({children:e,showHeader:t=!0,showFooter:r=!0,constrainHeight:o=!1}){return(0,s.jsxs)("div",{className:`${o?"h-screen":"min-h-screen"} flex flex-col bg-background`,children:[t&&(0,s.jsx)(a<PERSON>,{}),(0,s.jsx)("main",{className:`flex-1 ${o?"min-h-0":""}`,children:e}),r&&(0,s.jsx)(n.Footer,{})]})}function i({children:e}){return(0,s.jsx)(o,{showFooter:!1,constrainHeight:!0,children:(0,s.jsx)("div",{className:"container mx-auto py-6 h-full overflow-y-auto",children:e})})}},10809:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,dynamic:()=>n});var s=r(12907);let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call dynamic() from the server but dynamic is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx","dynamic"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},12941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20253:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),n=r(48088),a=r(88170),o=r.n(a),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["debug",{children:["tokens",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,10809)),"/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/debug/tokens/page",pathname:"/debug/tokens",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},21134:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},22805:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,dynamic:()=>g});var s=r(60687),n=r(43210),a=r(41330),o=r(14792),i=r(29523),l=r(44493),d=r(96834),c=r(78122),u=r(12597),p=r(13861),m=r(70615),x=r(52581);function f(){let{isSignedIn:e,getToken:t,userId:r}=(0,a.d)(),{user:f}=(0,o.Jd)(),{session:h}=(0,o.wV)(),[g,v]=(0,n.useState)({}),[b,y]=(0,n.useState)(!1),[j,k]=(0,n.useState)(!1),w=async()=>{if(e){k(!0);try{let e=await t(),s=await t({template:"integration_firebase"}),n={jwt:e||void 0,template:s||void 0,custom:await t({template:"custom"})||void 0};if(v(n),console.group("\uD83D\uDD10 Clerk Token Information"),console.log("\uD83D\uDCCB User ID:",r),console.log("\uD83D\uDC64 User Email:",f?.emailAddresses[0]?.emailAddress),console.log("\uD83C\uDFAB Session ID:",h?.id),console.log("⏰ Session Last Active:",h?.lastActiveAt),console.log("\uD83D\uDCC5 Session Expires:",h?.expireAt),console.group("\uD83D\uDD11 Tokens"),console.log("JWT Token:",e),console.log("Template Token:",s),console.log("Token Length:",e?.length),e)try{let t=JSON.parse(atob(e.split(".")[1]));console.log("JWT Payload:",t),console.log("Token Issued At:",new Date(1e3*t.iat)),console.log("Token Expires At:",new Date(1e3*t.exp))}catch(e){console.log("Could not decode JWT payload")}console.groupEnd(),console.groupEnd()}catch(e){console.error("❌ Error fetching tokens:",e),x.oR.error("Failed to fetch tokens")}finally{k(!1)}}},N=(e,t)=>{navigator.clipboard.writeText(e),x.oR.success(`${t} copied to clipboard`)},A=e=>e?b?e:`${e.substring(0,20)}...${e.substring(e.length-20)}`:"No token";return e?(0,s.jsxs)(l.Zp,{className:"w-full max-w-4xl",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:["\uD83D\uDD10 Clerk Token Debug",(0,s.jsx)(d.E,{variant:"default",children:"Signed In"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(i.$,{onClick:w,disabled:j,size:"sm",children:[(0,s.jsx)(c.A,{className:`h-4 w-4 mr-2 ${j?"animate-spin":""}`}),"Refresh Tokens"]}),(0,s.jsxs)(i.$,{onClick:()=>y(!b),variant:"outline",size:"sm",children:[b?(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}):(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),b?"Hide":"Show"," Tokens"]})]})]}),(0,s.jsxs)(l.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4 p-4 bg-muted rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"User ID"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:r})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Email"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:f?.emailAddresses[0]?.emailAddress})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Session ID"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground font-mono",children:h?.id})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Last Active"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:h?.lastActiveAt?.toLocaleString()})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"JWT Token"}),g.jwt&&(0,s.jsxs)(i.$,{onClick:()=>N(g.jwt,"JWT Token"),size:"sm",variant:"outline",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Copy"]})]}),(0,s.jsx)("div",{className:"p-3 bg-muted rounded-lg",children:(0,s.jsx)("code",{className:"text-sm break-all",children:g.jwt?A(g.jwt):"Loading..."})})]}),g.template&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Template Token"}),(0,s.jsxs)(i.$,{onClick:()=>N(g.template,"Template Token"),size:"sm",variant:"outline",children:[(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Copy"]})]}),(0,s.jsx)("div",{className:"p-3 bg-muted rounded-lg",children:(0,s.jsx)("code",{className:"text-sm break-all",children:A(g.template)})})]}),(0,s.jsxs)("div",{className:"p-4 bg-blue-50 dark:bg-blue-950 rounded-lg",children:[(0,s.jsx)("h4",{className:"font-semibold text-blue-900 dark:text-blue-100",children:"How to use these tokens:"}),(0,s.jsxs)("ul",{className:"mt-2 text-sm text-blue-800 dark:text-blue-200 space-y-1",children:[(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"JWT Token:"})," Use for API authentication with your backend"]}),(0,s.jsxs)("li",{children:["• ",(0,s.jsx)("strong",{children:"Template Token:"})," Custom tokens with specific claims"]}),(0,s.jsx)("li",{children:"• Check the browser console for detailed token information"}),(0,s.jsx)("li",{children:"• Tokens are automatically refreshed by Clerk"})]})]})]})]}):(0,s.jsxs)(l.Zp,{className:"w-full max-w-2xl",children:[(0,s.jsx)(l.aR,{children:(0,s.jsxs)(l.ZB,{className:"flex items-center gap-2",children:["\uD83D\uDD10 Clerk Token Debug",(0,s.jsx)(d.E,{variant:"secondary",children:"Not Signed In"})]})}),(0,s.jsx)(l.Wu,{children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Please sign in to view Clerk tokens."})})]})}var h=r(3363);let g="force-dynamic";function v(){return(0,s.jsx)(h.N,{children:(0,s.jsxs)("div",{className:"container mx-auto p-6 space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Clerk Token Debug"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"View and debug Clerk authentication tokens. Check the browser console for detailed information."})]}),(0,s.jsx)(f,{})]})})}},26134:(e,t,r)=>{"use strict";r.d(t,{UC:()=>er,VY:()=>en,ZL:()=>ee,bL:()=>Y,bm:()=>ea,hE:()=>es,hJ:()=>et,l9:()=>Q});var s=r(43210),n=r(70569),a=r(98599),o=r(11273),i=r(96963),l=r(65551),d=r(31355),c=r(32547),u=r(25028),p=r(46059),m=r(14163),x=r(1359),f=r(42247),h=r(63376),g=r(8730),v=r(60687),b="Dialog",[y,j]=(0,o.A)(b),[k,w]=y(b),N=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:o,modal:d=!0}=e,c=s.useRef(null),u=s.useRef(null),[p,m]=(0,l.i)({prop:n,defaultProp:a??!1,onChange:o,caller:b});return(0,v.jsx)(k,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:m,onOpenToggle:s.useCallback(()=>m(e=>!e),[m]),modal:d,children:r})};N.displayName=b;var A="DialogTrigger",D=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,o=w(A,r),i=(0,a.s)(t,o.triggerRef);return(0,v.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":J(o.open),...s,ref:i,onClick:(0,n.m)(e.onClick,o.onOpenToggle)})});D.displayName=A;var C="DialogPortal",[T,R]=y(C,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,o=w(C,t);return(0,v.jsx)(T,{scope:t,forceMount:r,children:s.Children.map(n,e=>(0,v.jsx)(p.C,{present:r||o.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:a,children:e})}))})};P.displayName=C;var _="DialogOverlay",E=s.forwardRef((e,t)=>{let r=R(_,e.__scopeDialog),{forceMount:s=r.forceMount,...n}=e,a=w(_,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:s||a.open,children:(0,v.jsx)(M,{...n,ref:t})}):null});E.displayName=_;var I=(0,g.TL)("DialogOverlay.RemoveScroll"),M=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=w(_,r);return(0,v.jsx)(f.A,{as:I,allowPinchZoom:!0,shards:[n.contentRef],children:(0,v.jsx)(m.sG.div,{"data-state":J(n.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),q="DialogContent",O=s.forwardRef((e,t)=>{let r=R(q,e.__scopeDialog),{forceMount:s=r.forceMount,...n}=e,a=w(q,e.__scopeDialog);return(0,v.jsx)(p.C,{present:s||a.open,children:a.modal?(0,v.jsx)(F,{...n,ref:t}):(0,v.jsx)(S,{...n,ref:t})})});O.displayName=q;var F=s.forwardRef((e,t)=>{let r=w(q,e.__scopeDialog),o=s.useRef(null),i=(0,a.s)(t,r.contentRef,o);return s.useEffect(()=>{let e=o.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(U,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=s.forwardRef((e,t)=>{let r=w(q,e.__scopeDialog),n=s.useRef(!1),a=s.useRef(!1);return(0,v.jsx)(U,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),U=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=w(q,r),p=s.useRef(null),m=(0,a.s)(t,p);return(0,x.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":J(u.open),...l,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(K,{titleId:u.titleId}),(0,v.jsx)(X,{contentRef:p,descriptionId:u.descriptionId})]})]})}),$="DialogTitle",z=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=w($,r);return(0,v.jsx)(m.sG.h2,{id:n.titleId,...s,ref:t})});z.displayName=$;var W="DialogDescription",G=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=w(W,r);return(0,v.jsx)(m.sG.p,{id:n.descriptionId,...s,ref:t})});G.displayName=W;var Z="DialogClose",B=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=w(Z,r);return(0,v.jsx)(m.sG.button,{type:"button",...s,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})});function J(e){return e?"open":"closed"}B.displayName=Z;var L="DialogTitleWarning",[H,V]=(0,o.q)(L,{contentName:q,titleName:$,docsSlug:"dialog"}),K=({titleId:e})=>{let t=V(L),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},X=({contentRef:e,descriptionId:t})=>{let r=V("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return s.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(n))},[n,e,t]),null},Y=N,Q=D,ee=P,et=E,er=O,es=z,en=G,ea=B},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>a,aR:()=>o,wL:()=>c});var s=r(60687);r(43210);var n=r(4780);function a({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex px-6 [.border-t]:pt-6",e),...t})}},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},62157:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70424:(e,t,r)=>{Promise.resolve().then(r.bind(r,10809))},70615:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},72575:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},80152:(e,t,r)=>{Promise.resolve().then(r.bind(r,22805))},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var n=r(8730),a=r(24224),o=r(4780);let i=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...a}){let l=r?n.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...a})}},98876:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[80,4999,8360,878,5788,1838,4336,2403],()=>r(20253));module.exports=s})();