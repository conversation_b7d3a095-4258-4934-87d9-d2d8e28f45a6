(()=>{var e={};e.id=6392,e.ids=[6392],e.modules={2051:(e,r,t)=>{Promise.resolve().then(t.bind(t,17658))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11644:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx","default")},17658:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var a=t(60687),s=t(96912),o=t(43210);function n(){let[e,r]=(0,o.useState)(null),t={questions:[{id:"singleChoice",title:"What's your favorite programming language?",subtitle:"Single choice - auto-advances",type:"single",options:[{value:"javascript",label:"JavaScript"},{value:"typescript",label:"TypeScript"},{value:"python",label:"Python"},{value:"rust",label:"Rust"}]},{id:"multipleChoice",title:"Which frameworks do you use?",subtitle:"Multiple choice - select all that apply",type:"multiple",options:[{value:"react",label:"React"},{value:"vue",label:"Vue.js"},{value:"angular",label:"Angular"},{value:"svelte",label:"Svelte"},{value:"nextjs",label:"Next.js"}]},{id:"textInput",title:"What's your name?",subtitle:"Text input field",type:"text",placeholder:"Enter your full name..."},{id:"textareaInput",title:"Tell us about your project",subtitle:"Textarea for longer responses",type:"textarea",placeholder:"Describe your project in detail..."},{id:"booleanChoice",title:"Are you available for freelance work?",subtitle:"Yes/No question",type:"single",options:[{value:!0,label:"Yes, I'm available"},{value:!1,label:"No, not currently"}]}],onComplete:e=>{console.log("Stepper completed with answers:",e),r(e)},onStepChange:(e,r)=>{console.log(`Step ${e+1} - Current answers:`,r)}};return e?(0,a.jsx)("div",{className:"min-h-screen bg-background flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"max-w-2xl w-full",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-[#166534] mb-4",children:"Stepper Demo Complete! \uD83C\uDF89"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Here are the results from your stepper journey:"})]}),(0,a.jsxs)("div",{className:"bg-card border rounded-lg p-6 space-y-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Your Answers:"}),Object.entries(e).map(([e,r])=>(0,a.jsxs)("div",{className:"border-b pb-3 last:border-b-0",children:[(0,a.jsx)("div",{className:"font-medium text-sm text-muted-foreground uppercase tracking-wide",children:e}),(0,a.jsx)("div",{className:"mt-1",children:Array.isArray(r)?(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:r.map((e,r)=>(0,a.jsx)("span",{className:"bg-[#166534] text-white px-2 py-1 rounded text-sm",children:e},r))}):"boolean"==typeof r?(0,a.jsx)("span",{className:`px-2 py-1 rounded text-sm ${r?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"}`,children:r?"Yes":"No"}):(0,a.jsx)("span",{className:"text-foreground",children:r})})]},e))]}),(0,a.jsx)("div",{className:"text-center mt-8",children:(0,a.jsx)("button",{onClick:()=>r(null),className:"bg-[#166534] text-white px-6 py-3 rounded-lg hover:bg-[#166534]/90 transition-colors",children:"Try Again"})})]})}):(0,a.jsx)(s.C,{config:t})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26027:(e,r,t)=>{Promise.resolve().then(t.bind(t,11644))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l});var a=t(60687),s=t(8730),o=t(24224);t(43210);var n=t(4780);let i=(0,o.F)("relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:ring-2 focus-visible:ring-ring/50 focus-visible:ring-offset-2 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive rounded-lg",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-sm hover:bg-primary/90 hover:scale-[1.02] active:scale-[0.98]",destructive:"bg-destructive text-white shadow-sm hover:bg-destructive/90 hover:scale-[1.02] active:scale-[0.98] focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-sm hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 hover:scale-[1.02] active:scale-[0.98]",ghost:"bg-transparent hover:bg-accent hover:text-accent-foreground hover:scale-[1.02] active:scale-[0.98] dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline bg-transparent shadow-none hover:scale-100 active:scale-100",glow:"bg-brand text-brand-foreground shadow-sm hover:bg-brand/90 hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",sidebar:"bg-background hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-ghost":"bg-transparent hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:scale-[1.02] active:scale-[0.98]","sidebar-outline":"bg-background border border-input hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-foreground hover:scale-[1.02] active:scale-[0.98]",green:"bg-green-100 text-green-700 border-green-500 hover:bg-green-600 hover:text-white hover:scale-[1.02] active:scale-[0.98] dark:bg-green-900/30 dark:text-green-400 dark:border-green-700 dark:hover:bg-green-700 dark:hover:text-white",grey:"bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200 hover:text-gray-800 hover:scale-[1.02] active:scale-[0.98] dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:text-gray-200"},size:{default:"h-10 px-4 py-2 text-sm gap-2 has-[>svg]:px-3",sm:"h-8 px-3 py-1.5 text-xs gap-1.5 rounded-md has-[>svg]:px-2.5",lg:"h-12 px-6 py-3 text-base gap-2 rounded-lg has-[>svg]:px-4",icon:"h-10 w-10 p-2.5"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:o=!1,...l}){let d=o?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:r,size:t,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},46705:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=t(65239),s=t(48088),o=t(88170),n=t.n(o),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let d={children:["",{children:["stepper-demo",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,11644)),"/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx"],error:[()=>Promise.resolve().then(t.bind(t,54431)),"/Users/<USER>/Data/new era/siift-next/src/app/error.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/stepper-demo/page",pathname:"/stepper-demo",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[80,4999,8360,878,2576,3884,1838,6912],()=>t(46705));module.exports=a})();