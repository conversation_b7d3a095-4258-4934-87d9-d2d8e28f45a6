{"/page": "app/page.js", "/_not-found/page": "app/_not-found/page.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/auth/signin/route": "app/api/auth/signin/route.js", "/api/health/route": "app/api/health/route.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/projects/route": "app/api/projects/route.js", "/robots.txt/route": "app/robots.txt/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/api/webhooks/clerk/route": "app/api/webhooks/clerk/route.js", "/debug/page": "app/debug/page.js", "/projects/create/page": "app/projects/create/page.js", "/projects/[id]/page": "app/projects/[id]/page.js", "/projects/page": "app/projects/page.js", "/sso-callback/page": "app/sso-callback/page.js", "/stepper-demo/page": "app/stepper-demo/page.js", "/test-ai-input/page": "app/test-ai-input/page.js", "/about/page": "app/about/page.js", "/debug/tokens/page": "app/debug/tokens/page.js", "/blog/page": "app/blog/page.js", "/projects/[id]/edit/page": "app/projects/[id]/edit/page.js", "/profile/page": "app/profile/page.js", "/projects/new/page": "app/projects/new/page.js", "/user-dashboard/page": "app/user-dashboard/page.js", "/blog/[slug]/page": "app/blog/[slug]/page.js", "/settings/page": "app/settings/page.js", "/admin/agent/calls/page": "app/admin/agent/calls/page.js", "/admin/activity/page": "app/admin/activity/page.js", "/admin/agent/usage-stats/page": "app/admin/agent/usage-stats/page.js", "/admin/page": "app/admin/page.js", "/admin/analytics/summary/page": "app/admin/analytics/summary/page.js", "/admin/notifications/page": "app/admin/notifications/page.js", "/admin/analytics/feedbacks/page": "app/admin/analytics/feedbacks/page.js", "/admin/analytics/activity-trends/page": "app/admin/analytics/activity-trends/page.js", "/admin/agent/token-trends/page": "app/admin/agent/token-trends/page.js", "/admin/health/page": "app/admin/health/page.js", "/admin/profile/page": "app/admin/profile/page.js", "/admin/projects/all/page": "app/admin/projects/all/page.js", "/admin/analytics/users/page": "app/admin/analytics/users/page.js", "/admin/analytics/activity-metrics/page": "app/admin/analytics/activity-metrics/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/admin/recent/page": "app/admin/recent/page.js", "/admin/settings-tab/page": "app/admin/settings-tab/page.js", "/admin/projects/page": "app/admin/projects/page.js", "/admin/trends/page": "app/admin/trends/page.js", "/admin/users/page": "app/admin/users/page.js", "/admin/system/health/page": "app/admin/system/health/page.js", "/admin/api-test/page": "app/admin/api-test/page.js", "/auth/forgot-password/page": "app/auth/forgot-password/page.js", "/auth/register/sso-callback/page": "app/auth/register/sso-callback/page.js", "/admin/projects/create/page": "app/admin/projects/create/page.js", "/auth/login/sso-callback/page": "app/auth/login/sso-callback/page.js", "/auth/verify-email/page": "app/auth/verify-email/page.js", "/auth/register/page": "app/auth/register/page.js", "/auth/reset-password/page": "app/auth/reset-password/page.js", "/auth/login/page": "app/auth/login/page.js"}