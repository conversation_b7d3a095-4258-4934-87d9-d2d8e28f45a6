[{"/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx": "1", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx": "2", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx": "3", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx": "4", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-metrics/page.tsx": "5", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx": "6", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/feedbacks/page.tsx": "7", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/summary/page.tsx": "8", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/users/page.tsx": "9", "/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx": "10", "/Users/<USER>/Data/new era/siift-next/src/app/admin/health/page.tsx": "11", "/Users/<USER>/Data/new era/siift-next/src/app/admin/notifications/page.tsx": "12", "/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx": "13", "/Users/<USER>/Data/new era/siift-next/src/app/admin/profile/page.tsx": "14", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/all/page.tsx": "15", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/create/page.tsx": "16", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/page.tsx": "17", "/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx": "18", "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx": "19", "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx": "20", "/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx": "21", "/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx": "22", "/Users/<USER>/Data/new era/siift-next/src/app/admin/users/page.tsx": "23", "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/me/route.ts": "24", "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/signin/route.ts": "25", "/Users/<USER>/Data/new era/siift-next/src/app/api/health/route.ts": "26", "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/[id]/route.ts": "27", "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/route.ts": "28", "/Users/<USER>/Data/new era/siift-next/src/app/api/webhooks/clerk/route.ts": "29", "/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx": "30", "/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx": "31", "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx": "32", "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx": "33", "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/page.tsx": "34", "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx": "35", "/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx": "36", "/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx": "37", "/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx": "38", "/Users/<USER>/Data/new era/siift-next/src/app/page.tsx": "39", "/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx": "40", "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx": "41", "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx": "42", "/Users/<USER>/Data/new era/siift-next/src/app/projects/create/page.tsx": "43", "/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx": "44", "/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx": "45", "/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx": "46", "/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx": "47", "/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx": "48", "/Users/<USER>/Data/new era/siift-next/src/components/admin-sidebar.tsx": "49", "/Users/<USER>/Data/new era/siift-next/src/components/admin-tabbed-content.tsx": "50", "/Users/<USER>/Data/new era/siift-next/src/components/auth/admin-route.tsx": "51", "/Users/<USER>/Data/new era/siift-next/src/components/auth/auth-card.tsx": "52", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/DashboardHeader.tsx": "53", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-hero-section.tsx": "54", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-projects-section.tsx": "55", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx": "56", "/Users/<USER>/Data/new era/siift-next/src/components/landing/feature-grid.tsx": "57", "/Users/<USER>/Data/new era/siift-next/src/components/landing/hero-section.tsx": "58", "/Users/<USER>/Data/new era/siift-next/src/components/landing/landing-page.tsx": "59", "/Users/<USER>/Data/new era/siift-next/src/components/layout/admin-layout.tsx": "60", "/Users/<USER>/Data/new era/siift-next/src/components/layout/dashboard-layout.tsx": "61", "/Users/<USER>/Data/new era/siift-next/src/components/layout/footer.tsx": "62", "/Users/<USER>/Data/new era/siift-next/src/components/layout/header.tsx": "63", "/Users/<USER>/Data/new era/siift-next/src/components/layout/main-layout.tsx": "64", "/Users/<USER>/Data/new era/siift-next/src/components/nav-main.tsx": "65", "/Users/<USER>/Data/new era/siift-next/src/components/nav-user.tsx": "66", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/main-nav.tsx": "67", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/mobile-nav.tsx": "68", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/user-menu.tsx": "69", "/Users/<USER>/Data/new era/siift-next/src/components/project-chat-sidebar.tsx": "70", "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationAnimation.tsx": "71", "/Users/<USER>/Data/new era/siift-next/src/components/project-sidebar.tsx": "72", "/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx": "73", "/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx": "74", "/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx": "75", "/Users/<USER>/Data/new era/siift-next/src/components/team-switcher.tsx": "76", "/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx": "77", "/Users/<USER>/Data/new era/siift-next/src/components/theme-toggle.tsx": "78", "/Users/<USER>/Data/new era/siift-next/src/components/ui/app-loading.tsx": "79", "/Users/<USER>/Data/new era/siift-next/src/components/ui/avatar.tsx": "80", "/Users/<USER>/Data/new era/siift-next/src/components/ui/background-beams.tsx": "81", "/Users/<USER>/Data/new era/siift-next/src/components/ui/badge.tsx": "82", "/Users/<USER>/Data/new era/siift-next/src/components/ui/breadcrumb.tsx": "83", "/Users/<USER>/Data/new era/siift-next/src/components/ui/button.tsx": "84", "/Users/<USER>/Data/new era/siift-next/src/components/ui/card.tsx": "85", "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-bubble.tsx": "86", "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-message-list.tsx": "87", "/Users/<USER>/Data/new era/siift-next/src/components/ui/checkbox.tsx": "88", "/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx": "89", "/Users/<USER>/Data/new era/siift-next/src/components/ui/dropdown-menu.tsx": "90", "/Users/<USER>/Data/new era/siift-next/src/components/ui/form-input.tsx": "91", "/Users/<USER>/Data/new era/siift-next/src/components/ui/form.tsx": "92", "/Users/<USER>/Data/new era/siift-next/src/components/ui/glow.tsx": "93", "/Users/<USER>/Data/new era/siift-next/src/components/ui/icons.tsx": "94", "/Users/<USER>/Data/new era/siift-next/src/components/ui/input.tsx": "95", "/Users/<USER>/Data/new era/siift-next/src/components/ui/label.tsx": "96", "/Users/<USER>/Data/new era/siift-next/src/components/ui/logo.tsx": "97", "/Users/<USER>/Data/new era/siift-next/src/components/ui/message-loading.tsx": "98", "/Users/<USER>/Data/new era/siift-next/src/components/ui/mockup.tsx": "99", "/Users/<USER>/Data/new era/siift-next/src/components/ui/navigation-menu.tsx": "100", "/Users/<USER>/Data/new era/siift-next/src/components/ui/progress.tsx": "101", "/Users/<USER>/Data/new era/siift-next/src/components/ui/scroll-area.tsx": "102", "/Users/<USER>/Data/new era/siift-next/src/components/ui/select.tsx": "103", "/Users/<USER>/Data/new era/siift-next/src/components/ui/separator.tsx": "104", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sheet.tsx": "105", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar-button.tsx": "106", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar.tsx": "107", "/Users/<USER>/Data/new era/siift-next/src/components/ui/skeleton.tsx": "108", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx": "109", "/Users/<USER>/Data/new era/siift-next/src/components/ui/switch.tsx": "110", "/Users/<USER>/Data/new era/siift-next/src/components/ui/tabs.tsx": "111", "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials-columns-1.tsx": "112", "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials.tsx": "113", "/Users/<USER>/Data/new era/siift-next/src/components/ui/textarea.tsx": "114", "/Users/<USER>/Data/new era/siift-next/src/components/ui/tooltip.tsx": "115", "/Users/<USER>/Data/new era/siift-next/src/components/ui/waitlist-section.tsx": "116", "/Users/<USER>/Data/new era/siift-next/src/components/user-tabbed-content.tsx": "117", "/Users/<USER>/Data/new era/siift-next/src/contexts/auth-context.tsx": "118", "/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx": "119", "/Users/<USER>/Data/new era/siift-next/src/hooks/mutations/useUserMutations.ts": "120", "/Users/<USER>/Data/new era/siift-next/src/hooks/queries/useUser.ts": "121", "/Users/<USER>/Data/new era/siift-next/src/hooks/use-auto-scroll.ts": "122", "/Users/<USER>/Data/new era/siift-next/src/hooks/use-mobile.ts": "123", "/Users/<USER>/Data/new era/siift-next/src/hooks/useAuth.ts": "124", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkAuth.ts": "125", "/Users/<USER>/Data/new era/siift-next/src/hooks/useResizable.ts": "126", "/Users/<USER>/Data/new era/siift-next/src/hooks/useToast.ts": "127", "/Users/<USER>/Data/new era/siift-next/src/hooks/useUserSync.ts": "128", "/Users/<USER>/Data/new era/siift-next/src/lib/admin-api.ts": "129", "/Users/<USER>/Data/new era/siift-next/src/lib/api.ts": "130", "/Users/<USER>/Data/new era/siift-next/src/lib/apiClient.ts": "131", "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsData.ts": "132", "/Users/<USER>/Data/new era/siift-next/src/lib/clerk-api.ts": "133", "/Users/<USER>/Data/new era/siift-next/src/lib/constants.ts": "134", "/Users/<USER>/Data/new era/siift-next/src/lib/fonts.ts": "135", "/Users/<USER>/Data/new era/siift-next/src/lib/jwt.ts": "136", "/Users/<USER>/Data/new era/siift-next/src/lib/mock-auth-api.ts": "137", "/Users/<USER>/Data/new era/siift-next/src/lib/mock-email-service.ts": "138", "/Users/<USER>/Data/new era/siift-next/src/lib/mockEventStream.ts": "139", "/Users/<USER>/Data/new era/siift-next/src/lib/projectCreationConfig.ts": "140", "/Users/<USER>/Data/new era/siift-next/src/lib/queryClient.ts": "141", "/Users/<USER>/Data/new era/siift-next/src/lib/realEventStream.ts": "142", "/Users/<USER>/Data/new era/siift-next/src/lib/session.ts": "143", "/Users/<USER>/Data/new era/siift-next/src/lib/tokenStorage.ts": "144", "/Users/<USER>/Data/new era/siift-next/src/lib/types.ts": "145", "/Users/<USER>/Data/new era/siift-next/src/lib/utils.ts": "146", "/Users/<USER>/Data/new era/siift-next/src/middleware.ts": "147", "/Users/<USER>/Data/new era/siift-next/src/stores/businessSectionStore.ts": "148", "/Users/<USER>/Data/new era/siift-next/src/stores/projectCreationStore.ts": "149", "/Users/<USER>/Data/new era/siift-next/src/stores/sessionStore.ts": "150", "/Users/<USER>/Data/new era/siift-next/src/types/BusinessSection.types.ts": "151", "/Users/<USER>/Data/new era/siift-next/src/types/Session.types.ts": "152", "/Users/<USER>/Data/new era/siift-next/src/types/email.types.ts": "153", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkApi.ts": "154", "/Users/<USER>/Data/new era/siift-next/src/app/robots.ts": "155", "/Users/<USER>/Data/new era/siift-next/src/app/sitemap.ts": "156", "/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx": "157", "/Users/<USER>/Data/new era/siift-next/src/components/seo/SEOHead.tsx": "158", "/Users/<USER>/Data/new era/siift-next/src/components/seo/StructuredData.tsx": "159", "/Users/<USER>/Data/new era/siift-next/src/hooks/useAnalytics.ts": "160", "/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx": "161", "/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx": "162", "/Users/<USER>/Data/new era/siift-next/src/app/error.tsx": "163", "/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx": "164", "/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx": "165", "/Users/<USER>/Data/new era/siift-next/src/components/EditableCell.tsx": "166", "/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx": "167", "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenDebug.tsx": "168", "/Users/<USER>/Data/new era/siift-next/src/components/ui/table.tsx": "169", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkToken.ts": "170", "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStore.ts": "171", "/Users/<USER>/Data/new era/siift-next/src/components/ui/resize-handle.tsx": "172", "/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx": "173", "/Users/<USER>/Data/new era/siift-next/src/components/debug/PostHogDebug.tsx": "174", "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenExample.tsx": "175", "/Users/<USER>/Data/new era/siift-next/src/app/about/page.tsx": "176", "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/not-found.tsx": "177", "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/page.tsx": "178", "/Users/<USER>/Data/new era/siift-next/src/app/blog/page.tsx": "179", "/Users/<USER>/Data/new era/siift-next/src/app/test-ai-input/page.tsx": "180", "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-card.tsx": "181", "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-section.tsx": "182", "/Users/<USER>/Data/new era/siift-next/src/components/ui/ai-prompt-demo.tsx": "183", "/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx": "184", "/Users/<USER>/Data/new era/siift-next/src/data/blog-posts.ts": "185", "/Users/<USER>/Data/new era/siift-next/src/types/blog.ts": "186", "/Users/<USER>/Data/new era/siift-next/src/components/project/ContentSections.tsx": "187", "/Users/<USER>/Data/new era/siift-next/src/components/project/PrioritiesDropdown.tsx": "188", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProgressBar.tsx": "189", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx": "190", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectHeader.tsx": "191", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx": "192", "/Users/<USER>/Data/new era/siift-next/src/components/project/index.ts": "193", "/Users/<USER>/Data/new era/siift-next/src/components/shared/ProjectInputSection.tsx": "194", "/Users/<USER>/Data/new era/siift-next/src/components/ui/dotted-background.tsx": "195", "/Users/<USER>/Data/new era/siift-next/src/components/ui/popover.tsx": "196", "/Users/<USER>/Data/new era/siift-next/src/lib/design-tokens.ts": "197", "/Users/<USER>/Data/new era/siift-next/src/mockdata/businessItemQuestions.ts": "198", "/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx": "199", "/Users/<USER>/Data/new era/siift-next/src/components/business-category-cards.tsx": "200", "/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx": "201", "/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx": "202", "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx": "203", "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationQuestionnaire.tsx": "204", "/Users/<USER>/Data/new era/siift-next/src/components/ui/stepper.tsx": "205", "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsData.ts": "206", "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsDataExtended.ts": "207", "/Users/<USER>/Data/new era/siift-next/src/hooks/useBusinessSections.ts": "208", "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsDataNew.ts": "209", "/Users/<USER>/Data/new era/siift-next/src/lib/cardOpacityUtils.ts": "210", "/Users/<USER>/Data/new era/siift-next/src/lib/dependencyManager.ts": "211", "/Users/<USER>/Data/new era/siift-next/src/lib/statusCountingSystem.ts": "212", "/Users/<USER>/Data/new era/siift-next/src/services/browserStorageService.ts": "213", "/Users/<USER>/Data/new era/siift-next/src/services/businessSectionsApi.ts": "214", "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStoreEnhanced.ts": "215", "/Users/<USER>/Data/new era/siift-next/src/types/BusinessItemData.types.ts": "216"}, {"size": 14293, "mtime": 1753675505450, "results": "217", "hashOfConfig": "218"}, {"size": 17842, "mtime": 1753675505489, "results": "219", "hashOfConfig": "218"}, {"size": 15868, "mtime": 1753675505503, "results": "220", "hashOfConfig": "218"}, {"size": 13664, "mtime": 1753675505496, "results": "221", "hashOfConfig": "218"}, {"size": 9942, "mtime": 1753675505565, "results": "222", "hashOfConfig": "218"}, {"size": 11912, "mtime": 1753675505594, "results": "223", "hashOfConfig": "218"}, {"size": 19398, "mtime": 1753675505584, "results": "224", "hashOfConfig": "218"}, {"size": 8154, "mtime": 1753675505601, "results": "225", "hashOfConfig": "218"}, {"size": 12828, "mtime": 1753675505575, "results": "226", "hashOfConfig": "218"}, {"size": 27409, "mtime": 1754123165734, "results": "227", "hashOfConfig": "218"}, {"size": 13221, "mtime": 1753675505478, "results": "228", "hashOfConfig": "218"}, {"size": 663, "mtime": 1753675505559, "results": "229", "hashOfConfig": "218"}, {"size": 761, "mtime": 1753675505552, "results": "230", "hashOfConfig": "218"}, {"size": 644, "mtime": 1753675505518, "results": "231", "hashOfConfig": "218"}, {"size": 645, "mtime": 1753675505456, "results": "232", "hashOfConfig": "218"}, {"size": 3723, "mtime": 1753675505471, "results": "233", "hashOfConfig": "218"}, {"size": 640, "mtime": 1753675505464, "results": "234", "hashOfConfig": "218"}, {"size": 648, "mtime": 1753675505608, "results": "235", "hashOfConfig": "218"}, {"size": 11761, "mtime": 1753675505443, "results": "236", "hashOfConfig": "218"}, {"size": 645, "mtime": 1753675505546, "results": "237", "hashOfConfig": "218"}, {"size": 13284, "mtime": 1753675505511, "results": "238", "hashOfConfig": "218"}, {"size": 12981, "mtime": 1753675505530, "results": "239", "hashOfConfig": "218"}, {"size": 13219, "mtime": 1753675505538, "results": "240", "hashOfConfig": "218"}, {"size": 1097, "mtime": 1753595146912, "results": "241", "hashOfConfig": "218"}, {"size": 1919, "mtime": 1753595158639, "results": "242", "hashOfConfig": "218"}, {"size": 992, "mtime": 1752468771519, "results": "243", "hashOfConfig": "218"}, {"size": 3891, "mtime": 1753672539953, "results": "244", "hashOfConfig": "218"}, {"size": 2881, "mtime": 1753595118765, "results": "245", "hashOfConfig": "218"}, {"size": 5436, "mtime": 1753672571172, "results": "246", "hashOfConfig": "218"}, {"size": 3893, "mtime": 1753675518355, "results": "247", "hashOfConfig": "218"}, {"size": 1303, "mtime": 1753675630418, "results": "248", "hashOfConfig": "218"}, {"size": 10224, "mtime": 1753675518375, "results": "249", "hashOfConfig": "218"}, {"size": 259, "mtime": 1753675518368, "results": "250", "hashOfConfig": "218"}, {"size": 13670, "mtime": 1753675518348, "results": "251", "hashOfConfig": "218"}, {"size": 259, "mtime": 1753675518342, "results": "252", "hashOfConfig": "218"}, {"size": 12893, "mtime": 1753675518361, "results": "253", "hashOfConfig": "218"}, {"size": 9905, "mtime": 1753675518335, "results": "254", "hashOfConfig": "218"}, {"size": 2963, "mtime": 1754542519082, "results": "255", "hashOfConfig": "218"}, {"size": 1172, "mtime": 1753673054748, "results": "256", "hashOfConfig": "218"}, {"size": 12835, "mtime": 1753675745805, "results": "257", "hashOfConfig": "218"}, {"size": 8822, "mtime": 1753675760836, "results": "258", "hashOfConfig": "218"}, {"size": 6311, "mtime": 1754547365665, "results": "259", "hashOfConfig": "218"}, {"size": 766, "mtime": 1753158462887, "results": "260", "hashOfConfig": "218"}, {"size": 5085, "mtime": 1753675869095, "results": "261", "hashOfConfig": "218"}, {"size": 9642, "mtime": 1753595833363, "results": "262", "hashOfConfig": "218"}, {"size": 19859, "mtime": 1753675760823, "results": "263", "hashOfConfig": "218"}, {"size": 613, "mtime": 1753665982933, "results": "264", "hashOfConfig": "218"}, {"size": 3981, "mtime": 1754362345691, "results": "265", "hashOfConfig": "218"}, {"size": 5728, "mtime": 1752982830884, "results": "266", "hashOfConfig": "218"}, {"size": 17129, "mtime": 1753596187151, "results": "267", "hashOfConfig": "218"}, {"size": 2425, "mtime": 1752998545730, "results": "268", "hashOfConfig": "218"}, {"size": 1390, "mtime": 1753853005228, "results": "269", "hashOfConfig": "218"}, {"size": 4728, "mtime": 1753852870110, "results": "270", "hashOfConfig": "218"}, {"size": 517, "mtime": 1754365165459, "results": "271", "hashOfConfig": "218"}, {"size": 6039, "mtime": 1753662236525, "results": "272", "hashOfConfig": "218"}, {"size": 4248, "mtime": 1754543797467, "results": "273", "hashOfConfig": "218"}, {"size": 3966, "mtime": 1753595529167, "results": "274", "hashOfConfig": "218"}, {"size": 12306, "mtime": 1754352771865, "results": "275", "hashOfConfig": "218"}, {"size": 1211, "mtime": 1754328801788, "results": "276", "hashOfConfig": "218"}, {"size": 3642, "mtime": 1752981130099, "results": "277", "hashOfConfig": "218"}, {"size": 392, "mtime": 1754336730216, "results": "278", "hashOfConfig": "218"}, {"size": 5758, "mtime": 1753853710936, "results": "279", "hashOfConfig": "218"}, {"size": 1254, "mtime": 1753852918837, "results": "280", "hashOfConfig": "218"}, {"size": 682, "mtime": 1753593043509, "results": "281", "hashOfConfig": "218"}, {"size": 3421, "mtime": 1752982524772, "results": "282", "hashOfConfig": "218"}, {"size": 5318, "mtime": 1753663916166, "results": "283", "hashOfConfig": "218"}, {"size": 2078, "mtime": 1754336685180, "results": "284", "hashOfConfig": "218"}, {"size": 2778, "mtime": 1754336697245, "results": "285", "hashOfConfig": "218"}, {"size": 2901, "mtime": 1753663572932, "results": "286", "hashOfConfig": "218"}, {"size": 5963, "mtime": 1754121922086, "results": "287", "hashOfConfig": "218"}, {"size": 8683, "mtime": 1754353683403, "results": "288", "hashOfConfig": "218"}, {"size": 11280, "mtime": 1754365318801, "results": "289", "hashOfConfig": "218"}, {"size": 5191, "mtime": 1753686888552, "results": "290", "hashOfConfig": "218"}, {"size": 652, "mtime": 1753664896734, "results": "291", "hashOfConfig": "218"}, {"size": 2110, "mtime": 1752972043397, "results": "292", "hashOfConfig": "218"}, {"size": 3296, "mtime": 1753597668044, "results": "293", "hashOfConfig": "218"}, {"size": 457, "mtime": 1752998625992, "results": "294", "hashOfConfig": "218"}, {"size": 2056, "mtime": 1753662232836, "results": "295", "hashOfConfig": "218"}, {"size": 2136, "mtime": 1752968997388, "results": "296", "hashOfConfig": "218"}, {"size": 1097, "mtime": 1754542519083, "results": "297", "hashOfConfig": "218"}, {"size": 9832, "mtime": 1752999819120, "results": "298", "hashOfConfig": "218"}, {"size": 1631, "mtime": 1754542519083, "results": "299", "hashOfConfig": "218"}, {"size": 2357, "mtime": 1754542519083, "results": "300", "hashOfConfig": "218"}, {"size": 4159, "mtime": 1754542519084, "results": "301", "hashOfConfig": "218"}, {"size": 1976, "mtime": 1754542519084, "results": "302", "hashOfConfig": "218"}, {"size": 2342, "mtime": 1754025864774, "results": "303", "hashOfConfig": "218"}, {"size": 1511, "mtime": 1753594097252, "results": "304", "hashOfConfig": "218"}, {"size": 1236, "mtime": 1754542519085, "results": "305", "hashOfConfig": "218"}, {"size": 800, "mtime": 1754542519085, "results": "306", "hashOfConfig": "218"}, {"size": 8305, "mtime": 1754542519086, "results": "307", "hashOfConfig": "218"}, {"size": 1446, "mtime": 1752712464418, "results": "308", "hashOfConfig": "218"}, {"size": 3759, "mtime": 1752024533987, "results": "309", "hashOfConfig": "218"}, {"size": 1339, "mtime": 1753000764590, "results": "310", "hashOfConfig": "218"}, {"size": 12774, "mtime": 1752994552413, "results": "311", "hashOfConfig": "218"}, {"size": 967, "mtime": 1754542519086, "results": "312", "hashOfConfig": "218"}, {"size": 611, "mtime": 1754542519086, "results": "313", "hashOfConfig": "218"}, {"size": 4133, "mtime": 1753941998467, "results": "314", "hashOfConfig": "218"}, {"size": 1189, "mtime": 1752998409681, "results": "315", "hashOfConfig": "218"}, {"size": 1491, "mtime": 1752994420851, "results": "316", "hashOfConfig": "218"}, {"size": 6664, "mtime": 1754542519087, "results": "317", "hashOfConfig": "218"}, {"size": 1260, "mtime": 1754542519088, "results": "318", "hashOfConfig": "218"}, {"size": 1645, "mtime": 1754542519088, "results": "319", "hashOfConfig": "218"}, {"size": 6253, "mtime": 1754542519089, "results": "320", "hashOfConfig": "218"}, {"size": 699, "mtime": 1754542519089, "results": "321", "hashOfConfig": "218"}, {"size": 4090, "mtime": 1754542519089, "results": "322", "hashOfConfig": "218"}, {"size": 5606, "mtime": 1754335485078, "results": "323", "hashOfConfig": "218"}, {"size": 21633, "mtime": 1754542519090, "results": "324", "hashOfConfig": "218"}, {"size": 276, "mtime": 1754542519090, "results": "325", "hashOfConfig": "218"}, {"size": 1813, "mtime": 1754542519090, "results": "326", "hashOfConfig": "218"}, {"size": 1173, "mtime": 1754542519091, "results": "327", "hashOfConfig": "218"}, {"size": 1969, "mtime": 1754542519091, "results": "328", "hashOfConfig": "218"}, {"size": 2582, "mtime": 1752999564859, "results": "329", "hashOfConfig": "218"}, {"size": 3732, "mtime": 1752999587832, "results": "330", "hashOfConfig": "218"}, {"size": 778, "mtime": 1754542519092, "results": "331", "hashOfConfig": "218"}, {"size": 1891, "mtime": 1754542519092, "results": "332", "hashOfConfig": "218"}, {"size": 4459, "mtime": 1753854489718, "results": "333", "hashOfConfig": "218"}, {"size": 21638, "mtime": 1754332270735, "results": "334", "hashOfConfig": "218"}, {"size": 8062, "mtime": 1752908182083, "results": "335", "hashOfConfig": "218"}, {"size": 1810, "mtime": 1752973774511, "results": "336", "hashOfConfig": "218"}, {"size": 9145, "mtime": 1753672623962, "results": "337", "hashOfConfig": "218"}, {"size": 6777, "mtime": 1754362364766, "results": "338", "hashOfConfig": "218"}, {"size": 3596, "mtime": 1752998402592, "results": "339", "hashOfConfig": "218"}, {"size": 565, "mtime": 1754542519092, "results": "340", "hashOfConfig": "218"}, {"size": 3836, "mtime": 1752972841718, "results": "341", "hashOfConfig": "218"}, {"size": 1451, "mtime": 1753661383143, "results": "342", "hashOfConfig": "218"}, {"size": 2522, "mtime": 1753846474177, "results": "343", "hashOfConfig": "218"}, {"size": 2422, "mtime": 1752973002784, "results": "344", "hashOfConfig": "218"}, {"size": 2489, "mtime": 1753672396206, "results": "345", "hashOfConfig": "218"}, {"size": 9688, "mtime": 1752470232696, "results": "346", "hashOfConfig": "218"}, {"size": 3441, "mtime": 1752460198072, "results": "347", "hashOfConfig": "218"}, {"size": 6817, "mtime": 1754542519093, "results": "348", "hashOfConfig": "218"}, {"size": 5052, "mtime": 1754542519093, "results": "349", "hashOfConfig": "218"}, {"size": 4246, "mtime": 1753672708155, "results": "350", "hashOfConfig": "218"}, {"size": 1636, "mtime": 1753578806610, "results": "351", "hashOfConfig": "218"}, {"size": 1473, "mtime": 1754371361164, "results": "352", "hashOfConfig": "218"}, {"size": 1389, "mtime": 1752026410415, "results": "353", "hashOfConfig": "218"}, {"size": 11277, "mtime": 1752908709717, "results": "354", "hashOfConfig": "218"}, {"size": 11316, "mtime": 1752998830455, "results": "355", "hashOfConfig": "218"}, {"size": 7038, "mtime": 1754365015507, "results": "356", "hashOfConfig": "218"}, {"size": 1928, "mtime": 1753158560369, "results": "357", "hashOfConfig": "218"}, {"size": 2770, "mtime": 1753664857036, "results": "358", "hashOfConfig": "218"}, {"size": 7620, "mtime": 1753158547289, "results": "359", "hashOfConfig": "218"}, {"size": 3088, "mtime": 1752468935955, "results": "360", "hashOfConfig": "218"}, {"size": 5556, "mtime": 1752969250904, "results": "361", "hashOfConfig": "218"}, {"size": 6661, "mtime": 1754542519094, "results": "362", "hashOfConfig": "218"}, {"size": 166, "mtime": 1754542519094, "results": "363", "hashOfConfig": "218"}, {"size": 435, "mtime": 1753663942644, "results": "364", "hashOfConfig": "218"}, {"size": 1659, "mtime": 1753054451476, "results": "365", "hashOfConfig": "218"}, {"size": 6053, "mtime": 1754363021042, "results": "366", "hashOfConfig": "218"}, {"size": 12521, "mtime": 1753686854521, "results": "367", "hashOfConfig": "218"}, {"size": 2312, "mtime": 1754548395863, "results": "368", "hashOfConfig": "218"}, {"size": 2250, "mtime": 1752908751521, "results": "369", "hashOfConfig": "218"}, {"size": 1498, "mtime": 1752908458736, "results": "370", "hashOfConfig": "218"}, {"size": 3282, "mtime": 1753672276773, "results": "371", "hashOfConfig": "218"}, {"size": 873, "mtime": 1753851542088, "results": "372", "hashOfConfig": "218"}, {"size": 1866, "mtime": 1753851520182, "results": "373", "hashOfConfig": "218"}, {"size": 1397, "mtime": 1753686764431, "results": "374", "hashOfConfig": "218"}, {"size": 4443, "mtime": 1753673483383, "results": "375", "hashOfConfig": "218"}, {"size": 5440, "mtime": 1753851497146, "results": "376", "hashOfConfig": "218"}, {"size": 3641, "mtime": 1753673769790, "results": "377", "hashOfConfig": "218"}, {"size": 234, "mtime": 1753675616016, "results": "378", "hashOfConfig": "218"}, {"size": 744, "mtime": 1753676382520, "results": "379", "hashOfConfig": "218"}, {"size": 3545, "mtime": 1753676234917, "results": "380", "hashOfConfig": "218"}, {"size": 2270, "mtime": 1753922304275, "results": "381", "hashOfConfig": "218"}, {"size": 2859, "mtime": 1753676221357, "results": "382", "hashOfConfig": "218"}, {"size": 3415, "mtime": 1754546594059, "results": "383", "hashOfConfig": "218"}, {"size": 17784, "mtime": 1754548082032, "results": "384", "hashOfConfig": "218"}, {"size": 7426, "mtime": 1753683916153, "results": "385", "hashOfConfig": "218"}, {"size": 2531, "mtime": 1754542519091, "results": "386", "hashOfConfig": "218"}, {"size": 2974, "mtime": 1753676418523, "results": "387", "hashOfConfig": "218"}, {"size": 4837, "mtime": 1754547224850, "results": "388", "hashOfConfig": "218"}, {"size": 889, "mtime": 1753846474177, "results": "389", "hashOfConfig": "218"}, {"size": 915, "mtime": 1753842497889, "results": "390", "hashOfConfig": "218"}, {"size": 5415, "mtime": 1753686818939, "results": "391", "hashOfConfig": "218"}, {"size": 9551, "mtime": 1753760500434, "results": "392", "hashOfConfig": "218"}, {"size": 9160, "mtime": 1753853976551, "results": "393", "hashOfConfig": "218"}, {"size": 1679, "mtime": 1753851587474, "results": "394", "hashOfConfig": "218"}, {"size": 5641, "mtime": 1753857588035, "results": "395", "hashOfConfig": "218"}, {"size": 2941, "mtime": 1753852097456, "results": "396", "hashOfConfig": "218"}, {"size": 394, "mtime": 1753857600394, "results": "397", "hashOfConfig": "218"}, {"size": 2360, "mtime": 1754333369468, "results": "398", "hashOfConfig": "218"}, {"size": 1491, "mtime": 1754335159581, "results": "399", "hashOfConfig": "218"}, {"size": 125, "mtime": 1753854785290, "results": "400", "hashOfConfig": "218"}, {"size": 4384, "mtime": 1754546785527, "results": "401", "hashOfConfig": "218"}, {"size": 20888, "mtime": 1754337029263, "results": "402", "hashOfConfig": "218"}, {"size": 420, "mtime": 1753851797609, "results": "403", "hashOfConfig": "218"}, {"size": 5476, "mtime": 1754337035197, "results": "404", "hashOfConfig": "218"}, {"size": 3691, "mtime": 1754332398090, "results": "405", "hashOfConfig": "218"}, {"size": 2628, "mtime": 1754336280674, "results": "406", "hashOfConfig": "218"}, {"size": 7465, "mtime": 1754546203808, "results": "407", "hashOfConfig": "218"}, {"size": 4126, "mtime": 1754332703906, "results": "408", "hashOfConfig": "218"}, {"size": 4629, "mtime": 1754545724166, "results": "409", "hashOfConfig": "218"}, {"size": 326, "mtime": 1754336821666, "results": "410", "hashOfConfig": "218"}, {"size": 11652, "mtime": 1754353733812, "results": "411", "hashOfConfig": "218"}, {"size": 4757, "mtime": 1754329229301, "results": "412", "hashOfConfig": "218"}, {"size": 2313, "mtime": 1754542519087, "results": "413", "hashOfConfig": "218"}, {"size": 12304, "mtime": 1754335563354, "results": "414", "hashOfConfig": "218"}, {"size": 8315, "mtime": 1754545843229, "results": "415", "hashOfConfig": "218"}, {"size": 4613, "mtime": 1754354783938, "results": "416", "hashOfConfig": "218"}, {"size": 2851, "mtime": 1754547070331, "results": "417", "hashOfConfig": "218"}, {"size": 13616, "mtime": 1754547972651, "results": "418", "hashOfConfig": "218"}, {"size": 903, "mtime": 1754545599714, "results": "419", "hashOfConfig": "218"}, {"size": 7014, "mtime": 1754548304340, "results": "420", "hashOfConfig": "218"}, {"size": 14027, "mtime": 1754365041327, "results": "421", "hashOfConfig": "218"}, {"size": 12408, "mtime": 1754362955568, "results": "422", "hashOfConfig": "218"}, {"size": 14575, "mtime": 1754548412277, "results": "423", "hashOfConfig": "218"}, {"size": 10336, "mtime": 1754542838480, "results": "424", "hashOfConfig": "218"}, {"size": 7377, "mtime": 1754548645561, "results": "425", "hashOfConfig": "218"}, {"size": 4756, "mtime": 1754548498233, "results": "426", "hashOfConfig": "218"}, {"size": 5995, "mtime": 1754548551372, "results": "427", "hashOfConfig": "218"}, {"size": 7839, "mtime": 1754547189105, "results": "428", "hashOfConfig": "218"}, {"size": 8699, "mtime": 1754548478524, "results": "429", "hashOfConfig": "218"}, {"size": 8219, "mtime": 1754544167953, "results": "430", "hashOfConfig": "218"}, {"size": 6539, "mtime": 1754547376293, "results": "431", "hashOfConfig": "218"}, {"size": 9858, "mtime": 1754547246480, "results": "432", "hashOfConfig": "218"}, {"size": 504, "mtime": 1754548368498, "results": "433", "hashOfConfig": "218"}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "11yhuq5", {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "812", "messages": "813", "suppressedMessages": "814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "815", "messages": "816", "suppressedMessages": "817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "818", "messages": "819", "suppressedMessages": "820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "821", "messages": "822", "suppressedMessages": "823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "824", "messages": "825", "suppressedMessages": "826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "827", "messages": "828", "suppressedMessages": "829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "830", "messages": "831", "suppressedMessages": "832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "833", "messages": "834", "suppressedMessages": "835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "836", "messages": "837", "suppressedMessages": "838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "839", "messages": "840", "suppressedMessages": "841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "842", "messages": "843", "suppressedMessages": "844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "845", "messages": "846", "suppressedMessages": "847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "848", "messages": "849", "suppressedMessages": "850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "851", "messages": "852", "suppressedMessages": "853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "854", "messages": "855", "suppressedMessages": "856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "857", "messages": "858", "suppressedMessages": "859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "860", "messages": "861", "suppressedMessages": "862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "863", "messages": "864", "suppressedMessages": "865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "866", "messages": "867", "suppressedMessages": "868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "869", "messages": "870", "suppressedMessages": "871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "872", "messages": "873", "suppressedMessages": "874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "875", "messages": "876", "suppressedMessages": "877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "878", "messages": "879", "suppressedMessages": "880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "881", "messages": "882", "suppressedMessages": "883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "884", "messages": "885", "suppressedMessages": "886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "887", "messages": "888", "suppressedMessages": "889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "890", "messages": "891", "suppressedMessages": "892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "893", "messages": "894", "suppressedMessages": "895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "896", "messages": "897", "suppressedMessages": "898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "899", "messages": "900", "suppressedMessages": "901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "902", "messages": "903", "suppressedMessages": "904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "905", "messages": "906", "suppressedMessages": "907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "908", "messages": "909", "suppressedMessages": "910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "911", "messages": "912", "suppressedMessages": "913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "914", "messages": "915", "suppressedMessages": "916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "917", "messages": "918", "suppressedMessages": "919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "920", "messages": "921", "suppressedMessages": "922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "923", "messages": "924", "suppressedMessages": "925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "926", "messages": "927", "suppressedMessages": "928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "929", "messages": "930", "suppressedMessages": "931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "932", "messages": "933", "suppressedMessages": "934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "935", "messages": "936", "suppressedMessages": "937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "938", "messages": "939", "suppressedMessages": "940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "941", "messages": "942", "suppressedMessages": "943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "944", "messages": "945", "suppressedMessages": "946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "947", "messages": "948", "suppressedMessages": "949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "950", "messages": "951", "suppressedMessages": "952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "953", "messages": "954", "suppressedMessages": "955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "956", "messages": "957", "suppressedMessages": "958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "959", "messages": "960", "suppressedMessages": "961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "962", "messages": "963", "suppressedMessages": "964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "965", "messages": "966", "suppressedMessages": "967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "968", "messages": "969", "suppressedMessages": "970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "971", "messages": "972", "suppressedMessages": "973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "974", "messages": "975", "suppressedMessages": "976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "977", "messages": "978", "suppressedMessages": "979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "980", "messages": "981", "suppressedMessages": "982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "983", "messages": "984", "suppressedMessages": "985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "986", "messages": "987", "suppressedMessages": "988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "989", "messages": "990", "suppressedMessages": "991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "992", "messages": "993", "suppressedMessages": "994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "995", "messages": "996", "suppressedMessages": "997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "998", "messages": "999", "suppressedMessages": "1000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1001", "messages": "1002", "suppressedMessages": "1003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1004", "messages": "1005", "suppressedMessages": "1006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1007", "messages": "1008", "suppressedMessages": "1009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1010", "messages": "1011", "suppressedMessages": "1012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1013", "messages": "1014", "suppressedMessages": "1015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1016", "messages": "1017", "suppressedMessages": "1018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1019", "messages": "1020", "suppressedMessages": "1021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1022", "messages": "1023", "suppressedMessages": "1024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1025", "messages": "1026", "suppressedMessages": "1027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1028", "messages": "1029", "suppressedMessages": "1030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1031", "messages": "1032", "suppressedMessages": "1033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1034", "messages": "1035", "suppressedMessages": "1036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1037", "messages": "1038", "suppressedMessages": "1039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1040", "messages": "1041", "suppressedMessages": "1042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1043", "messages": "1044", "suppressedMessages": "1045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1046", "messages": "1047", "suppressedMessages": "1048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1049", "messages": "1050", "suppressedMessages": "1051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1052", "messages": "1053", "suppressedMessages": "1054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1055", "messages": "1056", "suppressedMessages": "1057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1058", "messages": "1059", "suppressedMessages": "1060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1061", "messages": "1062", "suppressedMessages": "1063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1064", "messages": "1065", "suppressedMessages": "1066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1067", "messages": "1068", "suppressedMessages": "1069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1070", "messages": "1071", "suppressedMessages": "1072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1073", "messages": "1074", "suppressedMessages": "1075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1076", "messages": "1077", "suppressedMessages": "1078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1079", "messages": "1080", "suppressedMessages": "1081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx", ["1082", "1083", "1084", "1085", "1086", "1087"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-metrics/page.tsx", ["1088", "1089"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx", ["1090", "1091", "1092", "1093", "1094", "1095", "1096"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/feedbacks/page.tsx", ["1097", "1098", "1099", "1100", "1101", "1102", "1103", "1104", "1105"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/summary/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/users/page.tsx", ["1106", "1107", "1108", "1109", "1110", "1111", "1112"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx", ["1113", "1114", "1115", "1116", "1117", "1118", "1119", "1120", "1121"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/health/page.tsx", ["1122", "1123", "1124", "1125"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/notifications/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/profile/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/all/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/create/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx", ["1126"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx", ["1127", "1128", "1129", "1130", "1131"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx", ["1132", "1133", "1134"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/users/page.tsx", ["1135", "1136"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/me/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/signin/route.ts", ["1137"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/health/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/[id]/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/route.ts", ["1138"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/webhooks/clerk/route.ts", ["1139", "1140"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx", ["1141"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx", ["1142", "1143", "1144", "1145"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/page.tsx", ["1146", "1147", "1148", "1149"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx", ["1150", "1151"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx", ["1152", "1153"], [], "/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx", ["1154", "1155", "1156", "1157", "1158"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx", ["1159", "1160", "1161", "1162"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx", ["1163"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/create/page.tsx", ["1164"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx", ["1165"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx", ["1166", "1167"], [], "/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx", ["1168", "1169", "1170", "1171", "1172"], [], "/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/admin-sidebar.tsx", ["1173", "1174", "1175"], [], "/Users/<USER>/Data/new era/siift-next/src/components/admin-tabbed-content.tsx", ["1176", "1177", "1178", "1179", "1180", "1181"], [], "/Users/<USER>/Data/new era/siift-next/src/components/auth/admin-route.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/auth/auth-card.tsx", ["1182"], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/DashboardHeader.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-hero-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-projects-section.tsx", ["1183", "1184", "1185"], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/feature-grid.tsx", ["1186"], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/hero-section.tsx", ["1187"], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/landing-page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/admin-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/dashboard-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/footer.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/header.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/main-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/nav-main.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/nav-user.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/main-nav.tsx", ["1188"], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/mobile-nav.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/user-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-chat-sidebar.tsx", ["1189", "1190", "1191", "1192", "1193", "1194", "1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationAnimation.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-sidebar.tsx", ["1210"], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx", ["1211", "1212", "1213", "1214", "1215"], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/team-switcher.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/theme-toggle.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/app-loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/background-beams.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/breadcrumb.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/button.tsx", ["1216"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/card.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-bubble.tsx", ["1217", "1218"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-message-list.tsx", ["1219", "1220"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/form-input.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/form.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/glow.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/icons.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/input.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/label.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/logo.tsx", ["1221"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/message-loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/mockup.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/navigation-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/progress.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/select.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sheet.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar-button.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/switch.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials-columns-1.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/textarea.tsx", ["1222"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/waitlist-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/user-tabbed-content.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/contexts/auth-context.tsx", ["1223", "1224", "1225"], [], "/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/mutations/useUserMutations.ts", ["1226", "1227", "1228", "1229", "1230", "1231", "1232"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/queries/useUser.ts", ["1233", "1234"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/use-auto-scroll.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/use-mobile.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkAuth.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useResizable.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useToast.ts", ["1235"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useUserSync.ts", ["1236", "1237", "1238"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/admin-api.ts", ["1239", "1240", "1241", "1242"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/api.ts", ["1243", "1244"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/apiClient.ts", ["1245", "1246", "1247", "1248", "1249", "1250", "1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsData.ts", ["1262"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/clerk-api.ts", ["1263", "1264", "1265"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/constants.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/fonts.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/jwt.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mock-auth-api.ts", ["1266", "1267", "1268"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mock-email-service.ts", ["1269"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mockEventStream.ts", ["1270", "1271", "1272"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/projectCreationConfig.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/queryClient.ts", ["1273", "1274", "1275", "1276"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/realEventStream.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/session.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/tokenStorage.ts", ["1277", "1278"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/types.ts", ["1279", "1280"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/utils.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/middleware.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessSectionStore.ts", ["1281", "1282", "1283"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/projectCreationStore.ts", ["1284", "1285", "1286"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/sessionStore.ts", ["1287", "1288"], [], "/Users/<USER>/Data/new era/siift-next/src/types/BusinessSection.types.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/Session.types.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/email.types.ts", ["1289"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkApi.ts", ["1290", "1291", "1292"], [], "/Users/<USER>/Data/new era/siift-next/src/app/robots.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/sitemap.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx", ["1293"], [], "/Users/<USER>/Data/new era/siift-next/src/components/seo/SEOHead.tsx", ["1294"], [], "/Users/<USER>/Data/new era/siift-next/src/components/seo/StructuredData.tsx", ["1295", "1296"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useAnalytics.ts", ["1297", "1298", "1299", "1300", "1301"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/error.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/EditableCell.tsx", ["1302"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx", ["1303", "1304", "1305", "1306"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenDebug.tsx", ["1307", "1308"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/table.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkToken.ts", ["1309"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStore.ts", ["1310"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/resize-handle.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx", ["1311", "1312", "1313", "1314", "1315", "1316", "1317", "1318"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/PostHogDebug.tsx", ["1319", "1320", "1321", "1322", "1323"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenExample.tsx", ["1324", "1325", "1326", "1327", "1328", "1329"], [], "/Users/<USER>/Data/new era/siift-next/src/app/about/page.tsx", ["1330", "1331", "1332", "1333"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/not-found.tsx", ["1334", "1335"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/page.tsx", ["1336"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/test-ai-input/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-card.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/ai-prompt-demo.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx", ["1337", "1338"], [], "/Users/<USER>/Data/new era/siift-next/src/data/blog-posts.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/blog.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ContentSections.tsx", ["1339", "1340"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/PrioritiesDropdown.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProgressBar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx", ["1341"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectHeader.tsx", ["1342", "1343", "1344"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx", ["1345", "1346", "1347", "1348", "1349", "1350"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/index.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/shared/ProjectInputSection.tsx", ["1351"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/dotted-background.tsx", ["1352"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/popover.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/design-tokens.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/mockdata/businessItemQuestions.ts", ["1353", "1354", "1355"], [], "/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx", ["1356", "1357", "1358"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-category-cards.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx", ["1359", "1360"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx", ["1361"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationQuestionnaire.tsx", ["1362", "1363", "1364", "1365", "1366"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/stepper.tsx", ["1367", "1368", "1369", "1370", "1371", "1372", "1373", "1374"], [], "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsData.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsDataExtended.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useBusinessSections.ts", ["1375", "1376", "1377", "1378"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsDataNew.ts", ["1379", "1380"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/cardOpacityUtils.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/dependencyManager.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/statusCountingSystem.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/services/browserStorageService.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/services/businessSectionsApi.ts", ["1381", "1382", "1383"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStoreEnhanced.ts", ["1384", "1385", "1386"], [], "/Users/<USER>/Data/new era/siift-next/src/types/BusinessItemData.types.ts", [], [], {"ruleId": "1387", "severity": 1, "message": "1388", "line": 35, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 35, "endColumn": 39, "suggestions": "1391"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 58, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 58, "endColumn": 22, "suggestions": "1392"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 267, "column": 46, "nodeType": "1389", "messageId": "1390", "endLine": 267, "endColumn": 49, "suggestions": "1393"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 293, "column": 42, "nodeType": "1389", "messageId": "1390", "endLine": 293, "endColumn": 45, "suggestions": "1394"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 310, "column": 46, "nodeType": "1389", "messageId": "1390", "endLine": 310, "endColumn": 49, "suggestions": "1395"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 335, "column": 44, "nodeType": "1389", "messageId": "1390", "endLine": 335, "endColumn": 47, "suggestions": "1396"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 24, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 24, "endColumn": 39, "suggestions": "1397"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 47, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 47, "endColumn": 22, "suggestions": "1398"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 34, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 34, "endColumn": 39, "suggestions": "1399"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 57, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 57, "endColumn": 22, "suggestions": "1400"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 116, "column": 28, "nodeType": "1389", "messageId": "1390", "endLine": 116, "endColumn": 31, "suggestions": "1401"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 207, "column": 44, "nodeType": "1389", "messageId": "1390", "endLine": 207, "endColumn": 47, "suggestions": "1402"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 246, "column": 46, "nodeType": "1389", "messageId": "1390", "endLine": 246, "endColumn": 49, "suggestions": "1403"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 270, "column": 46, "nodeType": "1389", "messageId": "1390", "endLine": 270, "endColumn": 49, "suggestions": "1404"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 309, "column": 37, "nodeType": "1389", "messageId": "1390", "endLine": 309, "endColumn": 40, "suggestions": "1405"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 39, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 39, "endColumn": 39, "suggestions": "1406"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 70, "column": 27, "nodeType": "1389", "messageId": "1390", "endLine": 70, "endColumn": 30, "suggestions": "1407"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 90, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 90, "endColumn": 22, "suggestions": "1408"}, {"ruleId": "1409", "severity": 1, "message": "1410", "line": 99, "column": 6, "nodeType": "1411", "endLine": 99, "endColumn": 15, "suggestions": "1412"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 112, "column": 24, "nodeType": "1389", "messageId": "1390", "endLine": 112, "endColumn": 27, "suggestions": "1413"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 199, "column": 50, "nodeType": "1389", "messageId": "1390", "endLine": 199, "endColumn": 53, "suggestions": "1414"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 215, "column": 55, "nodeType": "1389", "messageId": "1390", "endLine": 215, "endColumn": 58, "suggestions": "1415"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 234, "column": 48, "nodeType": "1389", "messageId": "1390", "endLine": 234, "endColumn": 51, "suggestions": "1416"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 414, "column": 52, "nodeType": "1389", "messageId": "1390", "endLine": 414, "endColumn": 55, "suggestions": "1417"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 32, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 32, "endColumn": 39, "suggestions": "1418"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 59, "column": 27, "nodeType": "1389", "messageId": "1390", "endLine": 59, "endColumn": 30, "suggestions": "1419"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 78, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 78, "endColumn": 22, "suggestions": "1420"}, {"ruleId": "1409", "severity": 1, "message": "1410", "line": 87, "column": 6, "nodeType": "1411", "endLine": 87, "endColumn": 15, "suggestions": "1421"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 247, "column": 42, "nodeType": "1389", "messageId": "1390", "endLine": 247, "endColumn": 45, "suggestions": "1422"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 299, "column": 33, "nodeType": "1425", "messageId": "1426", "suggestions": "1427"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 299, "column": 56, "nodeType": "1425", "messageId": "1426", "suggestions": "1428"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 45, "column": 10, "nodeType": "1389", "messageId": "1390", "endLine": 45, "endColumn": 13, "suggestions": "1429"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 179, "column": 23, "nodeType": "1389", "messageId": "1390", "endLine": 179, "endColumn": 26, "suggestions": "1430"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 266, "column": 41, "nodeType": "1389", "messageId": "1390", "endLine": 266, "endColumn": 44, "suggestions": "1431"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 317, "column": 38, "nodeType": "1389", "messageId": "1390", "endLine": 317, "endColumn": 41, "suggestions": "1432"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 323, "column": 45, "nodeType": "1389", "messageId": "1390", "endLine": 323, "endColumn": 48, "suggestions": "1433"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 365, "column": 40, "nodeType": "1389", "messageId": "1390", "endLine": 365, "endColumn": 43, "suggestions": "1434"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 424, "column": 40, "nodeType": "1389", "messageId": "1390", "endLine": 424, "endColumn": 43, "suggestions": "1435"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 495, "column": 35, "nodeType": "1389", "messageId": "1390", "endLine": 495, "endColumn": 38, "suggestions": "1436"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 500, "column": 43, "nodeType": "1389", "messageId": "1390", "endLine": 500, "endColumn": 46, "suggestions": "1437"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 61, "column": 28, "nodeType": "1389", "messageId": "1390", "endLine": 61, "endColumn": 31, "suggestions": "1438"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 71, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 71, "endColumn": 22, "suggestions": "1439"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 140, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 140, "endColumn": 22, "suggestions": "1440"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 154, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 154, "endColumn": 22, "suggestions": "1441"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 58, "column": 50, "nodeType": "1389", "messageId": "1390", "endLine": 58, "endColumn": 53, "suggestions": "1442"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 26, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 26, "endColumn": 39, "suggestions": "1443"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 49, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 49, "endColumn": 22, "suggestions": "1444"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 107, "column": 61, "nodeType": "1389", "messageId": "1390", "endLine": 107, "endColumn": 64, "suggestions": "1445"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 112, "column": 63, "nodeType": "1389", "messageId": "1390", "endLine": 112, "endColumn": 66, "suggestions": "1446"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 317, "column": 104, "nodeType": "1389", "messageId": "1390", "endLine": 317, "endColumn": 107, "suggestions": "1447"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 142, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 142, "endColumn": 24, "suggestions": "1448"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 199, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 199, "endColumn": 39, "suggestions": "1449"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 351, "column": 42, "nodeType": "1389", "messageId": "1390", "endLine": 351, "endColumn": 45, "suggestions": "1450"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 74, "column": 30, "nodeType": "1389", "messageId": "1390", "endLine": 74, "endColumn": 33, "suggestions": "1451"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 84, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 84, "endColumn": 24, "suggestions": "1452"}, {"ruleId": "1453", "severity": 1, "message": "1454", "line": 59, "column": 23, "nodeType": null, "messageId": "1455", "endLine": 59, "endColumn": 24}, {"ruleId": "1453", "severity": 1, "message": "1456", "line": 52, "column": 27, "nodeType": null, "messageId": "1455", "endLine": 52, "endColumn": 34}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 32, "column": 12, "nodeType": "1389", "messageId": "1390", "endLine": 32, "endColumn": 15, "suggestions": "1457"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 40, "column": 11, "nodeType": "1389", "messageId": "1390", "endLine": 40, "endColumn": 14, "suggestions": "1458"}, {"ruleId": "1453", "severity": 1, "message": "1459", "line": 36, "column": 5, "nodeType": null, "messageId": "1455", "endLine": 36, "endColumn": 14}, {"ruleId": "1453", "severity": 1, "message": "1460", "line": 8, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 8, "endColumn": 19}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 75, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 75, "endColumn": 22, "suggestions": "1461"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 123, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 123, "endColumn": 22, "suggestions": "1462"}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 159, "column": 16, "nodeType": "1425", "messageId": "1426", "suggestions": "1464"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 74, "column": 29, "nodeType": "1389", "messageId": "1390", "endLine": 74, "endColumn": 32, "suggestions": "1465"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 91, "column": 29, "nodeType": "1389", "messageId": "1390", "endLine": 91, "endColumn": 32, "suggestions": "1466"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 124, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 124, "endColumn": 22, "suggestions": "1467"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 191, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 191, "endColumn": 22, "suggestions": "1468"}, {"ruleId": "1453", "severity": 1, "message": "1469", "line": 44, "column": 19, "nodeType": null, "messageId": "1455", "endLine": 44, "endColumn": 29}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 174, "column": 17, "nodeType": "1425", "messageId": "1426", "suggestions": "1470"}, {"ruleId": "1453", "severity": 1, "message": "1471", "line": 50, "column": 5, "nodeType": null, "messageId": "1455", "endLine": 50, "endColumn": 26}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 175, "column": 17, "nodeType": "1425", "messageId": "1426", "suggestions": "1472"}, {"ruleId": "1453", "severity": 1, "message": "1473", "line": 50, "column": 17, "nodeType": null, "messageId": "1455", "endLine": 50, "endColumn": 28}, {"ruleId": "1453", "severity": 1, "message": "1474", "line": 50, "column": 41, "nodeType": null, "messageId": "1455", "endLine": 50, "endColumn": 54}, {"ruleId": "1453", "severity": 1, "message": "1475", "line": 70, "column": 5, "nodeType": null, "messageId": "1455", "endLine": 70, "endColumn": 13}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 111, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 111, "endColumn": 24, "suggestions": "1476"}, {"ruleId": "1453", "severity": 1, "message": "1477", "line": 138, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 138, "endColumn": 17}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 98, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 98, "endColumn": 24, "suggestions": "1478"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 117, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 117, "endColumn": 22, "suggestions": "1479"}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 162, "column": 22, "nodeType": "1425", "messageId": "1426", "suggestions": "1480"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 243, "column": 73, "nodeType": "1389", "messageId": "1390", "endLine": 243, "endColumn": 76, "suggestions": "1481"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 93, "column": 48, "nodeType": "1389", "messageId": "1390", "endLine": 93, "endColumn": 51, "suggestions": "1482"}, {"ruleId": "1453", "severity": 1, "message": "1483", "line": 10, "column": 24, "nodeType": null, "messageId": "1455", "endLine": 10, "endColumn": 29}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 52, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 52, "endColumn": 22, "suggestions": "1484"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 50, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 50, "endColumn": 24, "suggestions": "1485"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 88, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 88, "endColumn": 22, "suggestions": "1486"}, {"ruleId": "1453", "severity": 1, "message": "1487", "line": 64, "column": 12, "nodeType": null, "messageId": "1455", "endLine": 64, "endColumn": 17}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 123, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 123, "endColumn": 24, "suggestions": "1488"}, {"ruleId": "1453", "severity": 1, "message": "1489", "line": 133, "column": 35, "nodeType": null, "messageId": "1455", "endLine": 133, "endColumn": 40}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 143, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 143, "endColumn": 24, "suggestions": "1490"}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 234, "column": 51, "nodeType": "1425", "messageId": "1426", "suggestions": "1491"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 37, "column": 32, "nodeType": "1389", "messageId": "1390", "endLine": 37, "endColumn": 35, "suggestions": "1492"}, {"ruleId": "1453", "severity": 1, "message": "1493", "line": 256, "column": 17, "nodeType": null, "messageId": "1455", "endLine": 256, "endColumn": 23}, {"ruleId": "1453", "severity": 1, "message": "1494", "line": 257, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 257, "endColumn": 15}, {"ruleId": "1453", "severity": 1, "message": "1495", "line": 4, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 4, "endColumn": 14}, {"ruleId": "1453", "severity": 1, "message": "1496", "line": 4, "column": 16, "nodeType": null, "messageId": "1455", "endLine": 4, "endColumn": 27}, {"ruleId": "1453", "severity": 1, "message": "1497", "line": 4, "column": 29, "nodeType": null, "messageId": "1455", "endLine": 4, "endColumn": 37}, {"ruleId": "1453", "severity": 1, "message": "1498", "line": 4, "column": 39, "nodeType": null, "messageId": "1455", "endLine": 4, "endColumn": 50}, {"ruleId": "1453", "severity": 1, "message": "1499", "line": 17, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 17, "endColumn": 7}, {"ruleId": "1453", "severity": 1, "message": "1500", "line": 19, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 19, "endColumn": 11}, {"ruleId": "1453", "severity": 1, "message": "1501", "line": 10, "column": 8, "nodeType": null, "messageId": "1455", "endLine": 10, "endColumn": 12}, {"ruleId": "1453", "severity": 1, "message": "1502", "line": 6, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 6, "endColumn": 14}, {"ruleId": "1453", "severity": 1, "message": "1501", "line": 7, "column": 8, "nodeType": null, "messageId": "1455", "endLine": 7, "endColumn": 12}, {"ruleId": "1453", "severity": 1, "message": "1503", "line": 79, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 79, "endColumn": 25}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 91, "column": 87, "nodeType": "1425", "messageId": "1426", "suggestions": "1504"}, {"ruleId": "1409", "severity": 1, "message": "1505", "line": 35, "column": 9, "nodeType": "1506", "endLine": 40, "endColumn": 4}, {"ruleId": "1453", "severity": 1, "message": "1507", "line": 11, "column": 11, "nodeType": null, "messageId": "1455", "endLine": 11, "endColumn": 15}, {"ruleId": "1453", "severity": 1, "message": "1508", "line": 3, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 3, "endColumn": 16}, {"ruleId": "1453", "severity": 1, "message": "1509", "line": 5, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 5, "endColumn": 10}, {"ruleId": "1453", "severity": 1, "message": "1510", "line": 6, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 6, "endColumn": 17}, {"ruleId": "1453", "severity": 1, "message": "1511", "line": 7, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 7, "endColumn": 17}, {"ruleId": "1453", "severity": 1, "message": "1512", "line": 9, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 9, "endColumn": 23}, {"ruleId": "1453", "severity": 1, "message": "1513", "line": 9, "column": 25, "nodeType": null, "messageId": "1455", "endLine": 9, "endColumn": 33}, {"ruleId": "1453", "severity": 1, "message": "1514", "line": 9, "column": 35, "nodeType": null, "messageId": "1455", "endLine": 9, "endColumn": 43}, {"ruleId": "1453", "severity": 1, "message": "1515", "line": 18, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 18, "endColumn": 20}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 30, "column": 26, "nodeType": "1389", "messageId": "1390", "endLine": 30, "endColumn": 29, "suggestions": "1516"}, {"ruleId": "1453", "severity": 1, "message": "1517", "line": 102, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 102, "endColumn": 12}, {"ruleId": "1453", "severity": 1, "message": "1518", "line": 108, "column": 20, "nodeType": null, "messageId": "1455", "endLine": 108, "endColumn": 43}, {"ruleId": "1453", "severity": 1, "message": "1519", "line": 109, "column": 23, "nodeType": null, "messageId": "1455", "endLine": 109, "endColumn": 49}, {"ruleId": "1453", "severity": 1, "message": "1520", "line": 112, "column": 20, "nodeType": null, "messageId": "1455", "endLine": 112, "endColumn": 31}, {"ruleId": "1453", "severity": 1, "message": "1521", "line": 114, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 114, "endColumn": 19}, {"ruleId": "1453", "severity": 1, "message": "1522", "line": 114, "column": 21, "nodeType": null, "messageId": "1455", "endLine": 114, "endColumn": 33}, {"ruleId": "1453", "severity": 1, "message": "1523", "line": 116, "column": 11, "nodeType": null, "messageId": "1455", "endLine": 116, "endColumn": 18}, {"ruleId": "1453", "severity": 1, "message": "1524", "line": 119, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 119, "endColumn": 20}, {"ruleId": "1453", "severity": 1, "message": "1525", "line": 123, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 123, "endColumn": 23}, {"ruleId": "1453", "severity": 1, "message": "1526", "line": 126, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 126, "endColumn": 24}, {"ruleId": "1453", "severity": 1, "message": "1527", "line": 127, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 127, "endColumn": 27}, {"ruleId": "1453", "severity": 1, "message": "1528", "line": 137, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 137, "endColumn": 26}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 41, "column": 26, "nodeType": "1389", "messageId": "1390", "endLine": 41, "endColumn": 29, "suggestions": "1529"}, {"ruleId": "1453", "severity": 1, "message": "1530", "line": 7, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 7, "endColumn": 21}, {"ruleId": "1453", "severity": 1, "message": "1531", "line": 18, "column": 25, "nodeType": null, "messageId": "1455", "endLine": 18, "endColumn": 42}, {"ruleId": "1453", "severity": 1, "message": "1494", "line": 19, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 19, "endColumn": 15}, {"ruleId": "1453", "severity": 1, "message": "1532", "line": 46, "column": 24, "nodeType": null, "messageId": "1455", "endLine": 46, "endColumn": 25}, {"ruleId": "1409", "severity": 1, "message": "1533", "line": 118, "column": 6, "nodeType": "1411", "endLine": 118, "endColumn": 72, "suggestions": "1534"}, {"ruleId": "1453", "severity": 1, "message": "1515", "line": 6, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 6, "endColumn": 20}, {"ruleId": "1453", "severity": 1, "message": "1535", "line": 18, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 18, "endColumn": 9}, {"ruleId": "1453", "severity": 1, "message": "1536", "line": 45, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 45, "endColumn": 12}, {"ruleId": "1453", "severity": 1, "message": "1537", "line": 11, "column": 55, "nodeType": null, "messageId": "1455", "endLine": 11, "endColumn": 59}, {"ruleId": "1453", "severity": 1, "message": "1538", "line": 15, "column": 7, "nodeType": null, "messageId": "1455", "endLine": 15, "endColumn": 24}, {"ruleId": "1453", "severity": 1, "message": "1539", "line": 46, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 46, "endColumn": 25}, {"ruleId": "1540", "severity": 1, "message": "1541", "line": 5, "column": 18, "nodeType": "1542", "messageId": "1543", "endLine": 5, "endColumn": 31, "suggestions": "1544"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 126, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 126, "endColumn": 24, "suggestions": "1545"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 193, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 193, "endColumn": 24, "suggestions": "1546"}, {"ruleId": "1453", "severity": 1, "message": "1547", "line": 219, "column": 16, "nodeType": null, "messageId": "1455", "endLine": 219, "endColumn": 24}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 23, "column": 20, "nodeType": "1389", "messageId": "1390", "endLine": 23, "endColumn": 23, "suggestions": "1548"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 40, "column": 20, "nodeType": "1389", "messageId": "1390", "endLine": 40, "endColumn": 23, "suggestions": "1549"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 124, "column": 22, "nodeType": "1389", "messageId": "1390", "endLine": 124, "endColumn": 25, "suggestions": "1550"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 199, "column": 22, "nodeType": "1389", "messageId": "1390", "endLine": 199, "endColumn": 25, "suggestions": "1551"}, {"ruleId": "1453", "severity": 1, "message": "1552", "line": 225, "column": 9, "nodeType": null, "messageId": "1455", "endLine": 225, "endColumn": 20}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 254, "column": 22, "nodeType": "1389", "messageId": "1390", "endLine": 254, "endColumn": 25, "suggestions": "1553"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 295, "column": 22, "nodeType": "1389", "messageId": "1390", "endLine": 295, "endColumn": 25, "suggestions": "1554"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 86, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 86, "endColumn": 37, "suggestions": "1555"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 160, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 160, "endColumn": 37, "suggestions": "1556"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 54, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 54, "endColumn": 37, "suggestions": "1557"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 12, "column": 47, "nodeType": "1389", "messageId": "1390", "endLine": 12, "endColumn": 50, "suggestions": "1558"}, {"ruleId": "1453", "severity": 1, "message": "1487", "line": 46, "column": 16, "nodeType": null, "messageId": "1455", "endLine": 46, "endColumn": 21}, {"ruleId": "1409", "severity": 1, "message": "1559", "line": 80, "column": 6, "nodeType": "1411", "endLine": 80, "endColumn": 47, "suggestions": "1560"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 295, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 295, "endColumn": 37, "suggestions": "1561"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 301, "column": 41, "nodeType": "1389", "messageId": "1390", "endLine": 301, "endColumn": 44, "suggestions": "1562"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 318, "column": 15, "nodeType": "1389", "messageId": "1390", "endLine": 318, "endColumn": 18, "suggestions": "1563"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 341, "column": 25, "nodeType": "1389", "messageId": "1390", "endLine": 341, "endColumn": 28, "suggestions": "1564"}, {"ruleId": "1453", "severity": 1, "message": "1565", "line": 5, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 5, "endColumn": 13}, {"ruleId": "1453", "severity": 1, "message": "1566", "line": 9, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 9, "endColumn": 14}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 15, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 15, "endColumn": 22, "suggestions": "1567"}, {"ruleId": "1453", "severity": 1, "message": "1566", "line": 30, "column": 11, "nodeType": null, "messageId": "1455", "endLine": 30, "endColumn": 22}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 30, "column": 27, "nodeType": "1389", "messageId": "1390", "endLine": 30, "endColumn": 30, "suggestions": "1568"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 48, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 48, "endColumn": 24, "suggestions": "1569"}, {"ruleId": "1453", "severity": 1, "message": "1570", "line": 109, "column": 20, "nodeType": null, "messageId": "1455", "endLine": 109, "endColumn": 32}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 123, "column": 24, "nodeType": "1389", "messageId": "1390", "endLine": 123, "endColumn": 27, "suggestions": "1571"}, {"ruleId": "1453", "severity": 1, "message": "1532", "line": 126, "column": 18, "nodeType": null, "messageId": "1455", "endLine": 126, "endColumn": 19}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 169, "column": 17, "nodeType": "1389", "messageId": "1390", "endLine": 169, "endColumn": 20, "suggestions": "1572"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 176, "column": 18, "nodeType": "1389", "messageId": "1390", "endLine": 176, "endColumn": 21, "suggestions": "1573"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 178, "column": 12, "nodeType": "1389", "messageId": "1390", "endLine": 178, "endColumn": 15, "suggestions": "1574"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 191, "column": 17, "nodeType": "1389", "messageId": "1390", "endLine": 191, "endColumn": 20, "suggestions": "1575"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 193, "column": 12, "nodeType": "1389", "messageId": "1390", "endLine": 193, "endColumn": 15, "suggestions": "1576"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 206, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 206, "endColumn": 22, "suggestions": "1577"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 208, "column": 12, "nodeType": "1389", "messageId": "1390", "endLine": 208, "endColumn": 15, "suggestions": "1578"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 221, "column": 20, "nodeType": "1389", "messageId": "1390", "endLine": 221, "endColumn": 23, "suggestions": "1579"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 228, "column": 20, "nodeType": "1389", "messageId": "1390", "endLine": 228, "endColumn": 23, "suggestions": "1580"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 246, "column": 28, "nodeType": "1389", "messageId": "1390", "endLine": 246, "endColumn": 31, "suggestions": "1581"}, {"ruleId": "1453", "severity": 1, "message": "1517", "line": 257, "column": 45, "nodeType": null, "messageId": "1455", "endLine": 257, "endColumn": 54}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 79, "column": 17, "nodeType": "1389", "messageId": "1390", "endLine": 79, "endColumn": 20, "suggestions": "1582"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 131, "column": 17, "nodeType": "1389", "messageId": "1390", "endLine": 131, "endColumn": 20, "suggestions": "1583"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 158, "column": 17, "nodeType": "1389", "messageId": "1390", "endLine": 158, "endColumn": 20, "suggestions": "1584"}, {"ruleId": "1453", "severity": 1, "message": "1585", "line": 68, "column": 7, "nodeType": null, "messageId": "1455", "endLine": 68, "endColumn": 31}, {"ruleId": "1453", "severity": 1, "message": "1487", "line": 286, "column": 14, "nodeType": null, "messageId": "1455", "endLine": 286, "endColumn": 19}, {"ruleId": "1453", "severity": 1, "message": "1487", "line": 298, "column": 14, "nodeType": null, "messageId": "1455", "endLine": 298, "endColumn": 19}, {"ruleId": "1453", "severity": 1, "message": "1586", "line": 380, "column": 17, "nodeType": null, "messageId": "1455", "endLine": 380, "endColumn": 20}, {"ruleId": "1453", "severity": 1, "message": "1587", "line": 23, "column": 32, "nodeType": null, "messageId": "1455", "endLine": 23, "endColumn": 40}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 224, "column": 28, "nodeType": "1389", "messageId": "1390", "endLine": 224, "endColumn": 31, "suggestions": "1588"}, {"ruleId": "1453", "severity": 1, "message": "1589", "line": 226, "column": 15, "nodeType": null, "messageId": "1455", "endLine": 226, "endColumn": 18}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 8, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 8, "endColumn": 39, "suggestions": "1590"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 22, "column": 24, "nodeType": "1389", "messageId": "1390", "endLine": 22, "endColumn": 27, "suggestions": "1591"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 45, "column": 23, "nodeType": "1389", "messageId": "1390", "endLine": 45, "endColumn": 26, "suggestions": "1592"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 46, "column": 26, "nodeType": "1389", "messageId": "1390", "endLine": 46, "endColumn": 29, "suggestions": "1593"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 160, "column": 24, "nodeType": "1389", "messageId": "1390", "endLine": 160, "endColumn": 27, "suggestions": "1594"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 176, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 176, "endColumn": 24, "suggestions": "1595"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 41, "column": 18, "nodeType": "1389", "messageId": "1390", "endLine": 41, "endColumn": 21, "suggestions": "1596"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 67, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 67, "endColumn": 37, "suggestions": "1597"}, {"ruleId": "1453", "severity": 1, "message": "1598", "line": 3, "column": 37, "nodeType": null, "messageId": "1455", "endLine": 3, "endColumn": 52}, {"ruleId": "1453", "severity": 1, "message": "1599", "line": 3, "column": 54, "nodeType": null, "messageId": "1455", "endLine": 3, "endColumn": 66}, {"ruleId": "1453", "severity": 1, "message": "1600", "line": 7, "column": 11, "nodeType": null, "messageId": "1455", "endLine": 7, "endColumn": 14}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 70, "column": 67, "nodeType": "1389", "messageId": "1390", "endLine": 70, "endColumn": 70, "suggestions": "1601"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 143, "column": 71, "nodeType": "1389", "messageId": "1390", "endLine": 143, "endColumn": 74, "suggestions": "1602"}, {"ruleId": "1453", "severity": 1, "message": "1603", "line": 165, "column": 15, "nodeType": null, "messageId": "1455", "endLine": 165, "endColumn": 20}, {"ruleId": "1453", "severity": 1, "message": "1604", "line": 274, "column": 23, "nodeType": null, "messageId": "1455", "endLine": 274, "endColumn": 31}, {"ruleId": "1453", "severity": 1, "message": "1604", "line": 337, "column": 23, "nodeType": null, "messageId": "1455", "endLine": 337, "endColumn": 31}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 69, "column": 13, "nodeType": "1389", "messageId": "1390", "endLine": 69, "endColumn": 16, "suggestions": "1605"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 62, "column": 37, "nodeType": "1389", "messageId": "1390", "endLine": 62, "endColumn": 40, "suggestions": "1606"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 73, "column": 37, "nodeType": "1389", "messageId": "1390", "endLine": 73, "endColumn": 40, "suggestions": "1607"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 94, "column": 37, "nodeType": "1389", "messageId": "1390", "endLine": 94, "endColumn": 40, "suggestions": "1608"}, {"ruleId": "1453", "severity": 1, "message": "1609", "line": 14, "column": 14, "nodeType": null, "messageId": "1455", "endLine": 14, "endColumn": 21}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 79, "column": 21, "nodeType": "1389", "messageId": "1390", "endLine": 79, "endColumn": 24, "suggestions": "1610"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 4, "column": 24, "nodeType": "1389", "messageId": "1390", "endLine": 4, "endColumn": 27, "suggestions": "1611"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 4, "column": 46, "nodeType": "1389", "messageId": "1390", "endLine": 4, "endColumn": 49, "suggestions": "1612"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 86, "column": 88, "nodeType": "1389", "messageId": "1390", "endLine": 86, "endColumn": 91, "suggestions": "1613"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 91, "column": 81, "nodeType": "1389", "messageId": "1390", "endLine": 91, "endColumn": 84, "suggestions": "1614"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 96, "column": 69, "nodeType": "1389", "messageId": "1390", "endLine": 96, "endColumn": 72, "suggestions": "1615"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 119, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 119, "endColumn": 37, "suggestions": "1616"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 122, "column": 43, "nodeType": "1389", "messageId": "1390", "endLine": 122, "endColumn": 46, "suggestions": "1617"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 17, "column": 16, "nodeType": "1389", "messageId": "1390", "endLine": 17, "endColumn": 19, "suggestions": "1618"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 146, "column": 47, "nodeType": "1389", "messageId": "1390", "endLine": 146, "endColumn": 50, "suggestions": "1619"}, {"ruleId": "1453", "severity": 1, "message": "1620", "line": 350, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 350, "endColumn": 26}, {"ruleId": "1453", "severity": 1, "message": "1621", "line": 350, "column": 28, "nodeType": null, "messageId": "1455", "endLine": 350, "endColumn": 47}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 447, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 447, "endColumn": 37, "suggestions": "1622"}, {"ruleId": "1453", "severity": 1, "message": "1532", "line": 60, "column": 18, "nodeType": null, "messageId": "1455", "endLine": 60, "endColumn": 19}, {"ruleId": "1409", "severity": 1, "message": "1623", "line": 79, "column": 6, "nodeType": "1411", "endLine": 79, "endColumn": 18, "suggestions": "1624"}, {"ruleId": "1453", "severity": 1, "message": "1532", "line": 36, "column": 18, "nodeType": null, "messageId": "1455", "endLine": 36, "endColumn": 19}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 119, "column": 62, "nodeType": "1389", "messageId": "1390", "endLine": 119, "endColumn": 65, "suggestions": "1625"}, {"ruleId": "1453", "severity": 1, "message": "1626", "line": 3, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 3, "endColumn": 22}, {"ruleId": "1453", "severity": 1, "message": "1627", "line": 4, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 4, "endColumn": 25}, {"ruleId": "1453", "severity": 1, "message": "1628", "line": 6, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 6, "endColumn": 14}, {"ruleId": "1453", "severity": 1, "message": "1629", "line": 6, "column": 16, "nodeType": null, "messageId": "1455", "endLine": 6, "endColumn": 27}, {"ruleId": "1453", "severity": 1, "message": "1630", "line": 6, "column": 29, "nodeType": null, "messageId": "1455", "endLine": 6, "endColumn": 39}, {"ruleId": "1453", "severity": 1, "message": "1631", "line": 6, "column": 41, "nodeType": null, "messageId": "1455", "endLine": 6, "endColumn": 50}, {"ruleId": "1453", "severity": 1, "message": "1508", "line": 7, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 7, "endColumn": 16}, {"ruleId": "1453", "severity": 1, "message": "1501", "line": 8, "column": 8, "nodeType": null, "messageId": "1455", "endLine": 8, "endColumn": 12}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 131, "column": 33, "nodeType": "1425", "messageId": "1426", "suggestions": "1632"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 142, "column": 29, "nodeType": "1425", "messageId": "1426", "suggestions": "1633"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 142, "column": 37, "nodeType": "1425", "messageId": "1426", "suggestions": "1634"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 142, "column": 42, "nodeType": "1425", "messageId": "1426", "suggestions": "1635"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 142, "column": 48, "nodeType": "1425", "messageId": "1426", "suggestions": "1636"}, {"ruleId": "1453", "severity": 1, "message": "1637", "line": 19, "column": 16, "nodeType": null, "messageId": "1455", "endLine": 19, "endColumn": 20}, {"ruleId": "1453", "severity": 1, "message": "1638", "line": 19, "column": 34, "nodeType": null, "messageId": "1455", "endLine": 19, "endColumn": 47}, {"ruleId": "1453", "severity": 1, "message": "1639", "line": 20, "column": 11, "nodeType": null, "messageId": "1455", "endLine": 20, "endColumn": 16}, {"ruleId": "1453", "severity": 1, "message": "1640", "line": 20, "column": 18, "nodeType": null, "messageId": "1455", "endLine": 20, "endColumn": 34}, {"ruleId": "1453", "severity": 1, "message": "1641", "line": 20, "column": 36, "nodeType": null, "messageId": "1455", "endLine": 20, "endColumn": 56}, {"ruleId": "1453", "severity": 1, "message": "1487", "line": 123, "column": 14, "nodeType": null, "messageId": "1455", "endLine": 123, "endColumn": 19}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 97, "column": 21, "nodeType": "1425", "messageId": "1426", "suggestions": "1642"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 97, "column": 62, "nodeType": "1425", "messageId": "1426", "suggestions": "1643"}, {"ruleId": "1423", "severity": 1, "message": "1424", "line": 97, "column": 81, "nodeType": "1425", "messageId": "1426", "suggestions": "1644"}, {"ruleId": "1645", "severity": 1, "message": "1646", "line": 141, "column": 23, "nodeType": "1647", "endLine": 145, "endColumn": 25}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 23, "column": 32, "nodeType": "1425", "messageId": "1426", "suggestions": "1648"}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 23, "column": 53, "nodeType": "1425", "messageId": "1426", "suggestions": "1649"}, {"ruleId": "1453", "severity": 1, "message": "1650", "line": 9, "column": 38, "nodeType": null, "messageId": "1455", "endLine": 9, "endColumn": 44}, {"ruleId": "1453", "severity": 1, "message": "1651", "line": 64, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 64, "endColumn": 23}, {"ruleId": "1453", "severity": 1, "message": "1652", "line": 64, "column": 25, "nodeType": null, "messageId": "1455", "endLine": 64, "endColumn": 41}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 10, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 10, "endColumn": 22, "suggestions": "1653"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 11, "column": 18, "nodeType": "1389", "messageId": "1390", "endLine": 11, "endColumn": 21, "suggestions": "1654"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 19, "column": 25, "nodeType": "1389", "messageId": "1390", "endLine": 19, "endColumn": 28, "suggestions": "1655"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 16, "column": 25, "nodeType": "1389", "messageId": "1390", "endLine": 16, "endColumn": 28, "suggestions": "1656"}, {"ruleId": "1453", "severity": 1, "message": "1657", "line": 21, "column": 3, "nodeType": null, "messageId": "1455", "endLine": 21, "endColumn": 16}, {"ruleId": "1453", "severity": 1, "message": "1603", "line": 28, "column": 11, "nodeType": null, "messageId": "1455", "endLine": 28, "endColumn": 16}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 13, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 13, "endColumn": 22, "suggestions": "1658"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 14, "column": 18, "nodeType": "1389", "messageId": "1390", "endLine": 14, "endColumn": 21, "suggestions": "1659"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 15, "column": 17, "nodeType": "1389", "messageId": "1390", "endLine": 15, "endColumn": 20, "suggestions": "1660"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 16, "column": 16, "nodeType": "1389", "messageId": "1390", "endLine": 16, "endColumn": 19, "suggestions": "1661"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 17, "column": 13, "nodeType": "1389", "messageId": "1390", "endLine": 17, "endColumn": 16, "suggestions": "1662"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 20, "column": 31, "nodeType": "1389", "messageId": "1390", "endLine": 20, "endColumn": 34, "suggestions": "1663"}, {"ruleId": "1409", "severity": 1, "message": "1664", "line": 54, "column": 9, "nodeType": "1506", "endLine": 59, "endColumn": 4}, {"ruleId": "1453", "severity": 1, "message": "1665", "line": 73, "column": 41, "nodeType": null, "messageId": "1455", "endLine": 73, "endColumn": 46}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 11, "column": 22, "nodeType": "1389", "messageId": "1390", "endLine": 11, "endColumn": 25, "suggestions": "1666"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 153, "column": 41, "nodeType": "1389", "messageId": "1390", "endLine": 153, "endColumn": 44, "suggestions": "1667"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 199, "column": 34, "nodeType": "1389", "messageId": "1390", "endLine": 199, "endColumn": 37, "suggestions": "1668"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 7, "column": 57, "nodeType": "1389", "messageId": "1390", "endLine": 7, "endColumn": 60, "suggestions": "1669"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 61, "column": 42, "nodeType": "1389", "messageId": "1390", "endLine": 61, "endColumn": 45, "suggestions": "1670"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 65, "column": 65, "nodeType": "1389", "messageId": "1390", "endLine": 65, "endColumn": 68, "suggestions": "1671"}, {"ruleId": "1453", "severity": 1, "message": "1672", "line": 22, "column": 10, "nodeType": null, "messageId": "1455", "endLine": 22, "endColumn": 24}, {"ruleId": "1453", "severity": 1, "message": "1673", "line": 22, "column": 26, "nodeType": null, "messageId": "1455", "endLine": 22, "endColumn": 43}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 80, "column": 15, "nodeType": "1425", "messageId": "1426", "suggestions": "1674"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 160, "column": 42, "nodeType": "1389", "messageId": "1390", "endLine": 160, "endColumn": 45, "suggestions": "1675"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 163, "column": 39, "nodeType": "1389", "messageId": "1390", "endLine": 163, "endColumn": 42, "suggestions": "1676"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 195, "column": 66, "nodeType": "1389", "messageId": "1390", "endLine": 195, "endColumn": 69, "suggestions": "1677"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 198, "column": 39, "nodeType": "1389", "messageId": "1390", "endLine": 198, "endColumn": 42, "suggestions": "1678"}, {"ruleId": "1423", "severity": 1, "message": "1463", "line": 263, "column": 19, "nodeType": "1425", "messageId": "1426", "suggestions": "1679"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 31, "column": 40, "nodeType": "1389", "messageId": "1390", "endLine": 31, "endColumn": 43, "suggestions": "1680"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 32, "column": 64, "nodeType": "1389", "messageId": "1390", "endLine": 32, "endColumn": 67, "suggestions": "1681"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 42, "column": 57, "nodeType": "1389", "messageId": "1390", "endLine": 42, "endColumn": 60, "suggestions": "1682"}, {"ruleId": "1409", "severity": 1, "message": "1683", "line": 77, "column": 6, "nodeType": "1411", "endLine": 77, "endColumn": 53, "suggestions": "1684"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 97, "column": 52, "nodeType": "1389", "messageId": "1390", "endLine": 97, "endColumn": 55, "suggestions": "1685"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 104, "column": 38, "nodeType": "1389", "messageId": "1390", "endLine": 104, "endColumn": 41, "suggestions": "1686"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 108, "column": 40, "nodeType": "1389", "messageId": "1390", "endLine": 108, "endColumn": 43, "suggestions": "1687"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 115, "column": 35, "nodeType": "1389", "messageId": "1390", "endLine": 115, "endColumn": 38, "suggestions": "1688"}, {"ruleId": "1409", "severity": 1, "message": "1689", "line": 132, "column": 5, "nodeType": "1411", "endLine": 132, "endColumn": 36, "suggestions": "1690"}, {"ruleId": "1409", "severity": 1, "message": "1689", "line": 166, "column": 5, "nodeType": "1411", "endLine": 166, "endColumn": 36, "suggestions": "1691"}, {"ruleId": "1409", "severity": 1, "message": "1689", "line": 195, "column": 5, "nodeType": "1411", "endLine": 195, "endColumn": 36, "suggestions": "1692"}, {"ruleId": "1409", "severity": 1, "message": "1689", "line": 238, "column": 5, "nodeType": "1411", "endLine": 238, "endColumn": 36, "suggestions": "1693"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 31, "column": 31, "nodeType": "1389", "messageId": "1390", "endLine": 31, "endColumn": 34, "suggestions": "1694"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 58, "column": 42, "nodeType": "1389", "messageId": "1390", "endLine": 58, "endColumn": 45, "suggestions": "1695"}, {"ruleId": "1453", "severity": 1, "message": "1696", "line": 23, "column": 30, "nodeType": null, "messageId": "1455", "endLine": 23, "endColumn": 40}, {"ruleId": "1453", "severity": 1, "message": "1696", "line": 125, "column": 27, "nodeType": null, "messageId": "1455", "endLine": 125, "endColumn": 37}, {"ruleId": "1453", "severity": 1, "message": "1697", "line": 125, "column": 47, "nodeType": null, "messageId": "1455", "endLine": 125, "endColumn": 54}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 36, "column": 36, "nodeType": "1389", "messageId": "1390", "endLine": 36, "endColumn": 39, "suggestions": "1698"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 39, "column": 19, "nodeType": "1389", "messageId": "1390", "endLine": 39, "endColumn": 22, "suggestions": "1699"}, {"ruleId": "1387", "severity": 1, "message": "1388", "line": 40, "column": 17, "nodeType": "1389", "messageId": "1390", "endLine": 40, "endColumn": 20, "suggestions": "1700"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1701", "1702"], ["1703", "1704"], ["1705", "1706"], ["1707", "1708"], ["1709", "1710"], ["1711", "1712"], ["1713", "1714"], ["1715", "1716"], ["1717", "1718"], ["1719", "1720"], ["1721", "1722"], ["1723", "1724"], ["1725", "1726"], ["1727", "1728"], ["1729", "1730"], ["1731", "1732"], ["1733", "1734"], ["1735", "1736"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["1737"], ["1738", "1739"], ["1740", "1741"], ["1742", "1743"], ["1744", "1745"], ["1746", "1747"], ["1748", "1749"], ["1750", "1751"], ["1752", "1753"], ["1754"], ["1755", "1756"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1757", "1758", "1759", "1760"], ["1761", "1762", "1763", "1764"], ["1765", "1766"], ["1767", "1768"], ["1769", "1770"], ["1771", "1772"], ["1773", "1774"], ["1775", "1776"], ["1777", "1778"], ["1779", "1780"], ["1781", "1782"], ["1783", "1784"], ["1785", "1786"], ["1787", "1788"], ["1789", "1790"], ["1791", "1792"], ["1793", "1794"], ["1795", "1796"], ["1797", "1798"], ["1799", "1800"], ["1801", "1802"], ["1803", "1804"], ["1805", "1806"], ["1807", "1808"], ["1809", "1810"], ["1811", "1812"], "@typescript-eslint/no-unused-vars", "'_' is assigned a value but never used.", "unusedVar", "'request' is defined but never used.", ["1813", "1814"], ["1815", "1816"], "'getValues' is assigned a value but never used.", "'ArrowLeft' is defined but never used.", ["1817", "1818"], ["1819", "1820"], "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["1821", "1822", "1823", "1824"], ["1825", "1826"], ["1827", "1828"], ["1829", "1830"], ["1831", "1832"], "'setSuccess' is assigned a value but never used.", ["1833", "1834", "1835", "1836"], "'emailVerificationSent' is assigned a value but never used.", ["1837", "1838", "1839", "1840"], "'backendUser' is assigned a value but never used.", "'isUserLoading' is assigned a value but never used.", "'setValue' is assigned a value but never used.", ["1841", "1842"], "'initials' is assigned a value but never used.", ["1843", "1844"], ["1845", "1846"], ["1847", "1848", "1849", "1850"], ["1851", "1852"], ["1853", "1854"], "'reset' is assigned a value but never used.", ["1855", "1856"], ["1857", "1858"], ["1859", "1860"], "'error' is defined but never used.", ["1861", "1862"], "'_data' is defined but never used.", ["1863", "1864"], ["1865", "1866", "1867", "1868"], ["1869", "1870"], "'logout' is assigned a value but never used.", "'router' is assigned a value but never used.", "'Tabs' is defined but never used.", "'TabsContent' is defined but never used.", "'TabsList' is defined but never used.", "'TabsTrigger' is defined but never used.", "'Star' is defined but never used.", "'FileText' is defined but never used.", "'Link' is defined but never used.", "'Plus' is defined but never used.", "'getProgressColor' is assigned a value but never used.", ["1871", "1872", "1873", "1874"], "The 'hints' array makes the dependencies of useEffect Hook (at line 82) change on every render. Move it inside the useEffect callback. Alternatively, wrap the initialization of 'hints' in its own useMemo() Hook.", "VariableDeclarator", "'user' is assigned a value but never used.", "'Button' is defined but never used.", "'Tooltip' is defined but never used.", "'TooltipContent' is defined but never used.", "'TooltipTrigger' is defined but never used.", "'MessageCircle' is defined but never used.", "'Columns2' is defined but never used.", "'Columns3' is defined but never used.", "'ICON_SIZES' is defined but never used.", ["1875", "1876"], "'projectId' is defined but never used.", "'externalIsChatCollapsed' is defined but never used.", "'externalSetIsChatCollapsed' is defined but never used.", "'setMessages' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "'setOpen' is assigned a value but never used.", "'isCollapsed' is assigned a value but never used.", "'setIsCollapsed' is assigned a value but never used.", "'isChatCollapsed' is assigned a value but never used.", "'setIsChatCollapsed' is assigned a value but never used.", "'handleWidthToggle' is assigned a value but never used.", ["1877", "1878"], "'useUserSync' is defined but never used.", "'setUserProperties' is assigned a value but never used.", "'e' is defined but never used.", "React Hook useEffect has a missing dependency: 'identifyUser'. Either include it or remove the dependency array.", ["1879"], "'layout' is assigned a value but never used.", "'className' is defined but never used.", "'_ref' is defined but never used.", "'autoScrollEnabled' is assigned a value but never used.", "'dotPulseDuration' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["1880"], ["1881", "1882"], ["1883", "1884"], "'apiError' is defined but never used.", ["1885", "1886"], ["1887", "1888"], ["1889", "1890"], ["1891", "1892"], "'queryClient' is assigned a value but never used.", ["1893", "1894"], ["1895", "1896"], ["1897", "1898"], ["1899", "1900"], ["1901", "1902"], ["1903", "1904"], "React Hook useEffect has a missing dependency: 'syncUserToBackend'. Either include it or remove the dependency array.", ["1905"], ["1906", "1907"], ["1908", "1909"], ["1910", "1911"], ["1912", "1913"], "'AuthTokens' is defined but never used.", "'ApiResponse' is defined but never used.", ["1914", "1915"], ["1916", "1917"], ["1918", "1919"], "'refreshError' is defined but never used.", ["1920", "1921"], ["1922", "1923"], ["1924", "1925"], ["1926", "1927"], ["1928", "1929"], ["1930", "1931"], ["1932", "1933"], ["1934", "1935"], ["1936", "1937"], ["1938", "1939"], ["1940", "1941"], ["1942", "1943"], ["1944", "1945"], ["1946", "1947"], "'generateVerificationCode' is assigned a value but never used.", "'key' is assigned a value but never used.", "'ideaText' is defined but never used.", ["1948", "1949"], "'url' is defined but never used.", ["1950", "1951"], ["1952", "1953"], ["1954", "1955"], ["1956", "1957"], ["1958", "1959"], ["1960", "1961"], ["1962", "1963"], ["1964", "1965"], "'BusinessSection' is defined but never used.", "'BusinessItem' is defined but never used.", "'get' is defined but never used.", ["1966", "1967"], ["1968", "1969"], "'state' is assigned a value but never used.", "'response' is assigned a value but never used.", ["1970", "1971"], ["1972", "1973"], ["1974", "1975"], ["1976", "1977"], "'posthog' is defined but never used.", ["1978", "1979"], ["1980", "1981"], ["1982", "1983"], ["1984", "1985"], ["1986", "1987"], ["1988", "1989"], ["1990", "1991"], ["1992", "1993"], ["1994", "1995"], ["1996", "1997"], "'isHowItWorksOpen' is assigned a value but never used.", "'setIsHowItWorksOpen' is assigned a value but never used.", ["1998", "1999"], "React Hook useEffect has a missing dependency: 'fetchTokens'. Either include it or remove the dependency array.", ["2000"], ["2001", "2002"], "'PostHogDebug' is defined but never used.", "'ClerkTokenDebug' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", ["2003", "2004", "2005", "2006"], ["2007", "2008", "2009", "2010"], ["2011", "2012", "2013", "2014"], ["2015", "2016", "2017", "2018"], ["2019", "2020", "2021", "2022"], "'post' is assigned a value but never used.", "'apiIsSignedIn' is assigned a value but never used.", "'token' is assigned a value but never used.", "'fetchAndLogToken' is assigned a value but never used.", "'copyTokenToClipboard' is assigned a value but never used.", ["2023", "2024", "2025", "2026"], ["2027", "2028", "2029", "2030"], ["2031", "2032", "2033", "2034"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["2035", "2036", "2037", "2038"], ["2039", "2040", "2041", "2042"], "'Share2' is defined but never used.", "'selectedModel' is assigned a value but never used.", "'setSelectedModel' is assigned a value but never used.", ["2043", "2044"], ["2045", "2046"], ["2047", "2048"], ["2049", "2050"], "'activeContent' is defined but never used.", ["2051", "2052"], ["2053", "2054"], ["2055", "2056"], ["2057", "2058"], ["2059", "2060"], ["2061", "2062"], "The 'hints' array makes the dependencies of useEffect Hook (at line 88) change on every render. Move it inside the useEffect callback. Alternatively, wrap the initialization of 'hints' in its own useMemo() Hook.", "'index' is defined but never used.", ["2063", "2064"], ["2065", "2066"], ["2067", "2068"], ["2069", "2070"], ["2071", "2072"], ["2073", "2074"], "'isInfoExpanded' is assigned a value but never used.", "'setIsInfoExpanded' is assigned a value but never used.", ["2075", "2076", "2077", "2078"], ["2079", "2080"], ["2081", "2082"], ["2083", "2084"], ["2085", "2086"], ["2087", "2088", "2089", "2090"], ["2091", "2092"], ["2093", "2094"], ["2095", "2096"], "React Hook useEffect has a missing dependency: 'handleNext'. Either include it or remove the dependency array.", ["2097"], ["2098", "2099"], ["2100", "2101"], ["2102", "2103"], ["2104", "2105"], "React Hook useCallback has a missing dependency: 'updateState'. Either include it or remove the dependency array.", ["2106"], ["2107"], ["2108"], ["2109"], ["2110", "2111"], ["2112", "2113"], "'_projectId' is defined but never used.", "'_itemId' is defined but never used.", ["2114", "2115"], ["2116", "2117"], ["2118", "2119"], {"messageId": "2120", "fix": "2121", "desc": "2122"}, {"messageId": "2123", "fix": "2124", "desc": "2125"}, {"messageId": "2120", "fix": "2126", "desc": "2122"}, {"messageId": "2123", "fix": "2127", "desc": "2125"}, {"messageId": "2120", "fix": "2128", "desc": "2122"}, {"messageId": "2123", "fix": "2129", "desc": "2125"}, {"messageId": "2120", "fix": "2130", "desc": "2122"}, {"messageId": "2123", "fix": "2131", "desc": "2125"}, {"messageId": "2120", "fix": "2132", "desc": "2122"}, {"messageId": "2123", "fix": "2133", "desc": "2125"}, {"messageId": "2120", "fix": "2134", "desc": "2122"}, {"messageId": "2123", "fix": "2135", "desc": "2125"}, {"messageId": "2120", "fix": "2136", "desc": "2122"}, {"messageId": "2123", "fix": "2137", "desc": "2125"}, {"messageId": "2120", "fix": "2138", "desc": "2122"}, {"messageId": "2123", "fix": "2139", "desc": "2125"}, {"messageId": "2120", "fix": "2140", "desc": "2122"}, {"messageId": "2123", "fix": "2141", "desc": "2125"}, {"messageId": "2120", "fix": "2142", "desc": "2122"}, {"messageId": "2123", "fix": "2143", "desc": "2125"}, {"messageId": "2120", "fix": "2144", "desc": "2122"}, {"messageId": "2123", "fix": "2145", "desc": "2125"}, {"messageId": "2120", "fix": "2146", "desc": "2122"}, {"messageId": "2123", "fix": "2147", "desc": "2125"}, {"messageId": "2120", "fix": "2148", "desc": "2122"}, {"messageId": "2123", "fix": "2149", "desc": "2125"}, {"messageId": "2120", "fix": "2150", "desc": "2122"}, {"messageId": "2123", "fix": "2151", "desc": "2125"}, {"messageId": "2120", "fix": "2152", "desc": "2122"}, {"messageId": "2123", "fix": "2153", "desc": "2125"}, {"messageId": "2120", "fix": "2154", "desc": "2122"}, {"messageId": "2123", "fix": "2155", "desc": "2125"}, {"messageId": "2120", "fix": "2156", "desc": "2122"}, {"messageId": "2123", "fix": "2157", "desc": "2125"}, {"messageId": "2120", "fix": "2158", "desc": "2122"}, {"messageId": "2123", "fix": "2159", "desc": "2125"}, {"desc": "2160", "fix": "2161"}, {"messageId": "2120", "fix": "2162", "desc": "2122"}, {"messageId": "2123", "fix": "2163", "desc": "2125"}, {"messageId": "2120", "fix": "2164", "desc": "2122"}, {"messageId": "2123", "fix": "2165", "desc": "2125"}, {"messageId": "2120", "fix": "2166", "desc": "2122"}, {"messageId": "2123", "fix": "2167", "desc": "2125"}, {"messageId": "2120", "fix": "2168", "desc": "2122"}, {"messageId": "2123", "fix": "2169", "desc": "2125"}, {"messageId": "2120", "fix": "2170", "desc": "2122"}, {"messageId": "2123", "fix": "2171", "desc": "2125"}, {"messageId": "2120", "fix": "2172", "desc": "2122"}, {"messageId": "2123", "fix": "2173", "desc": "2125"}, {"messageId": "2120", "fix": "2174", "desc": "2122"}, {"messageId": "2123", "fix": "2175", "desc": "2125"}, {"messageId": "2120", "fix": "2176", "desc": "2122"}, {"messageId": "2123", "fix": "2177", "desc": "2125"}, {"desc": "2160", "fix": "2178"}, {"messageId": "2120", "fix": "2179", "desc": "2122"}, {"messageId": "2123", "fix": "2180", "desc": "2125"}, {"messageId": "2181", "data": "2182", "fix": "2183", "desc": "2184"}, {"messageId": "2181", "data": "2185", "fix": "2186", "desc": "2187"}, {"messageId": "2181", "data": "2188", "fix": "2189", "desc": "2190"}, {"messageId": "2181", "data": "2191", "fix": "2192", "desc": "2193"}, {"messageId": "2181", "data": "2194", "fix": "2195", "desc": "2184"}, {"messageId": "2181", "data": "2196", "fix": "2197", "desc": "2187"}, {"messageId": "2181", "data": "2198", "fix": "2199", "desc": "2190"}, {"messageId": "2181", "data": "2200", "fix": "2201", "desc": "2193"}, {"messageId": "2120", "fix": "2202", "desc": "2122"}, {"messageId": "2123", "fix": "2203", "desc": "2125"}, {"messageId": "2120", "fix": "2204", "desc": "2122"}, {"messageId": "2123", "fix": "2205", "desc": "2125"}, {"messageId": "2120", "fix": "2206", "desc": "2122"}, {"messageId": "2123", "fix": "2207", "desc": "2125"}, {"messageId": "2120", "fix": "2208", "desc": "2122"}, {"messageId": "2123", "fix": "2209", "desc": "2125"}, {"messageId": "2120", "fix": "2210", "desc": "2122"}, {"messageId": "2123", "fix": "2211", "desc": "2125"}, {"messageId": "2120", "fix": "2212", "desc": "2122"}, {"messageId": "2123", "fix": "2213", "desc": "2125"}, {"messageId": "2120", "fix": "2214", "desc": "2122"}, {"messageId": "2123", "fix": "2215", "desc": "2125"}, {"messageId": "2120", "fix": "2216", "desc": "2122"}, {"messageId": "2123", "fix": "2217", "desc": "2125"}, {"messageId": "2120", "fix": "2218", "desc": "2122"}, {"messageId": "2123", "fix": "2219", "desc": "2125"}, {"messageId": "2120", "fix": "2220", "desc": "2122"}, {"messageId": "2123", "fix": "2221", "desc": "2125"}, {"messageId": "2120", "fix": "2222", "desc": "2122"}, {"messageId": "2123", "fix": "2223", "desc": "2125"}, {"messageId": "2120", "fix": "2224", "desc": "2122"}, {"messageId": "2123", "fix": "2225", "desc": "2125"}, {"messageId": "2120", "fix": "2226", "desc": "2122"}, {"messageId": "2123", "fix": "2227", "desc": "2125"}, {"messageId": "2120", "fix": "2228", "desc": "2122"}, {"messageId": "2123", "fix": "2229", "desc": "2125"}, {"messageId": "2120", "fix": "2230", "desc": "2122"}, {"messageId": "2123", "fix": "2231", "desc": "2125"}, {"messageId": "2120", "fix": "2232", "desc": "2122"}, {"messageId": "2123", "fix": "2233", "desc": "2125"}, {"messageId": "2120", "fix": "2234", "desc": "2122"}, {"messageId": "2123", "fix": "2235", "desc": "2125"}, {"messageId": "2120", "fix": "2236", "desc": "2122"}, {"messageId": "2123", "fix": "2237", "desc": "2125"}, {"messageId": "2120", "fix": "2238", "desc": "2122"}, {"messageId": "2123", "fix": "2239", "desc": "2125"}, {"messageId": "2120", "fix": "2240", "desc": "2122"}, {"messageId": "2123", "fix": "2241", "desc": "2125"}, {"messageId": "2120", "fix": "2242", "desc": "2122"}, {"messageId": "2123", "fix": "2243", "desc": "2125"}, {"messageId": "2120", "fix": "2244", "desc": "2122"}, {"messageId": "2123", "fix": "2245", "desc": "2125"}, {"messageId": "2120", "fix": "2246", "desc": "2122"}, {"messageId": "2123", "fix": "2247", "desc": "2125"}, {"messageId": "2120", "fix": "2248", "desc": "2122"}, {"messageId": "2123", "fix": "2249", "desc": "2125"}, {"messageId": "2120", "fix": "2250", "desc": "2122"}, {"messageId": "2123", "fix": "2251", "desc": "2125"}, {"messageId": "2120", "fix": "2252", "desc": "2122"}, {"messageId": "2123", "fix": "2253", "desc": "2125"}, {"messageId": "2120", "fix": "2254", "desc": "2122"}, {"messageId": "2123", "fix": "2255", "desc": "2125"}, {"messageId": "2120", "fix": "2256", "desc": "2122"}, {"messageId": "2123", "fix": "2257", "desc": "2125"}, {"messageId": "2181", "data": "2258", "fix": "2259", "desc": "2260"}, {"messageId": "2181", "data": "2261", "fix": "2262", "desc": "2263"}, {"messageId": "2181", "data": "2264", "fix": "2265", "desc": "2266"}, {"messageId": "2181", "data": "2267", "fix": "2268", "desc": "2269"}, {"messageId": "2120", "fix": "2270", "desc": "2122"}, {"messageId": "2123", "fix": "2271", "desc": "2125"}, {"messageId": "2120", "fix": "2272", "desc": "2122"}, {"messageId": "2123", "fix": "2273", "desc": "2125"}, {"messageId": "2120", "fix": "2274", "desc": "2122"}, {"messageId": "2123", "fix": "2275", "desc": "2125"}, {"messageId": "2120", "fix": "2276", "desc": "2122"}, {"messageId": "2123", "fix": "2277", "desc": "2125"}, {"messageId": "2181", "data": "2278", "fix": "2279", "desc": "2260"}, {"messageId": "2181", "data": "2280", "fix": "2281", "desc": "2263"}, {"messageId": "2181", "data": "2282", "fix": "2283", "desc": "2266"}, {"messageId": "2181", "data": "2284", "fix": "2285", "desc": "2269"}, {"messageId": "2181", "data": "2286", "fix": "2287", "desc": "2260"}, {"messageId": "2181", "data": "2288", "fix": "2289", "desc": "2263"}, {"messageId": "2181", "data": "2290", "fix": "2291", "desc": "2266"}, {"messageId": "2181", "data": "2292", "fix": "2293", "desc": "2269"}, {"messageId": "2120", "fix": "2294", "desc": "2122"}, {"messageId": "2123", "fix": "2295", "desc": "2125"}, {"messageId": "2120", "fix": "2296", "desc": "2122"}, {"messageId": "2123", "fix": "2297", "desc": "2125"}, {"messageId": "2120", "fix": "2298", "desc": "2122"}, {"messageId": "2123", "fix": "2299", "desc": "2125"}, {"messageId": "2181", "data": "2300", "fix": "2301", "desc": "2260"}, {"messageId": "2181", "data": "2302", "fix": "2303", "desc": "2263"}, {"messageId": "2181", "data": "2304", "fix": "2305", "desc": "2266"}, {"messageId": "2181", "data": "2306", "fix": "2307", "desc": "2269"}, {"messageId": "2120", "fix": "2308", "desc": "2122"}, {"messageId": "2123", "fix": "2309", "desc": "2125"}, {"messageId": "2120", "fix": "2310", "desc": "2122"}, {"messageId": "2123", "fix": "2311", "desc": "2125"}, {"messageId": "2120", "fix": "2312", "desc": "2122"}, {"messageId": "2123", "fix": "2313", "desc": "2125"}, {"messageId": "2120", "fix": "2314", "desc": "2122"}, {"messageId": "2123", "fix": "2315", "desc": "2125"}, {"messageId": "2120", "fix": "2316", "desc": "2122"}, {"messageId": "2123", "fix": "2317", "desc": "2125"}, {"messageId": "2120", "fix": "2318", "desc": "2122"}, {"messageId": "2123", "fix": "2319", "desc": "2125"}, {"messageId": "2120", "fix": "2320", "desc": "2122"}, {"messageId": "2123", "fix": "2321", "desc": "2125"}, {"messageId": "2181", "data": "2322", "fix": "2323", "desc": "2260"}, {"messageId": "2181", "data": "2324", "fix": "2325", "desc": "2263"}, {"messageId": "2181", "data": "2326", "fix": "2327", "desc": "2266"}, {"messageId": "2181", "data": "2328", "fix": "2329", "desc": "2269"}, {"messageId": "2120", "fix": "2330", "desc": "2122"}, {"messageId": "2123", "fix": "2331", "desc": "2125"}, {"messageId": "2181", "data": "2332", "fix": "2333", "desc": "2260"}, {"messageId": "2181", "data": "2334", "fix": "2335", "desc": "2263"}, {"messageId": "2181", "data": "2336", "fix": "2337", "desc": "2266"}, {"messageId": "2181", "data": "2338", "fix": "2339", "desc": "2269"}, {"messageId": "2120", "fix": "2340", "desc": "2122"}, {"messageId": "2123", "fix": "2341", "desc": "2125"}, {"messageId": "2120", "fix": "2342", "desc": "2122"}, {"messageId": "2123", "fix": "2343", "desc": "2125"}, {"desc": "2344", "fix": "2345"}, {"messageId": "2346", "fix": "2347", "desc": "2348"}, {"messageId": "2120", "fix": "2349", "desc": "2122"}, {"messageId": "2123", "fix": "2350", "desc": "2125"}, {"messageId": "2120", "fix": "2351", "desc": "2122"}, {"messageId": "2123", "fix": "2352", "desc": "2125"}, {"messageId": "2120", "fix": "2353", "desc": "2122"}, {"messageId": "2123", "fix": "2354", "desc": "2125"}, {"messageId": "2120", "fix": "2355", "desc": "2122"}, {"messageId": "2123", "fix": "2356", "desc": "2125"}, {"messageId": "2120", "fix": "2357", "desc": "2122"}, {"messageId": "2123", "fix": "2358", "desc": "2125"}, {"messageId": "2120", "fix": "2359", "desc": "2122"}, {"messageId": "2123", "fix": "2360", "desc": "2125"}, {"messageId": "2120", "fix": "2361", "desc": "2122"}, {"messageId": "2123", "fix": "2362", "desc": "2125"}, {"messageId": "2120", "fix": "2363", "desc": "2122"}, {"messageId": "2123", "fix": "2364", "desc": "2125"}, {"messageId": "2120", "fix": "2365", "desc": "2122"}, {"messageId": "2123", "fix": "2366", "desc": "2125"}, {"messageId": "2120", "fix": "2367", "desc": "2122"}, {"messageId": "2123", "fix": "2368", "desc": "2125"}, {"messageId": "2120", "fix": "2369", "desc": "2122"}, {"messageId": "2123", "fix": "2370", "desc": "2125"}, {"messageId": "2120", "fix": "2371", "desc": "2122"}, {"messageId": "2123", "fix": "2372", "desc": "2125"}, {"desc": "2373", "fix": "2374"}, {"messageId": "2120", "fix": "2375", "desc": "2122"}, {"messageId": "2123", "fix": "2376", "desc": "2125"}, {"messageId": "2120", "fix": "2377", "desc": "2122"}, {"messageId": "2123", "fix": "2378", "desc": "2125"}, {"messageId": "2120", "fix": "2379", "desc": "2122"}, {"messageId": "2123", "fix": "2380", "desc": "2125"}, {"messageId": "2120", "fix": "2381", "desc": "2122"}, {"messageId": "2123", "fix": "2382", "desc": "2125"}, {"messageId": "2120", "fix": "2383", "desc": "2122"}, {"messageId": "2123", "fix": "2384", "desc": "2125"}, {"messageId": "2120", "fix": "2385", "desc": "2122"}, {"messageId": "2123", "fix": "2386", "desc": "2125"}, {"messageId": "2120", "fix": "2387", "desc": "2122"}, {"messageId": "2123", "fix": "2388", "desc": "2125"}, {"messageId": "2120", "fix": "2389", "desc": "2122"}, {"messageId": "2123", "fix": "2390", "desc": "2125"}, {"messageId": "2120", "fix": "2391", "desc": "2122"}, {"messageId": "2123", "fix": "2392", "desc": "2125"}, {"messageId": "2120", "fix": "2393", "desc": "2122"}, {"messageId": "2123", "fix": "2394", "desc": "2125"}, {"messageId": "2120", "fix": "2395", "desc": "2122"}, {"messageId": "2123", "fix": "2396", "desc": "2125"}, {"messageId": "2120", "fix": "2397", "desc": "2122"}, {"messageId": "2123", "fix": "2398", "desc": "2125"}, {"messageId": "2120", "fix": "2399", "desc": "2122"}, {"messageId": "2123", "fix": "2400", "desc": "2125"}, {"messageId": "2120", "fix": "2401", "desc": "2122"}, {"messageId": "2123", "fix": "2402", "desc": "2125"}, {"messageId": "2120", "fix": "2403", "desc": "2122"}, {"messageId": "2123", "fix": "2404", "desc": "2125"}, {"messageId": "2120", "fix": "2405", "desc": "2122"}, {"messageId": "2123", "fix": "2406", "desc": "2125"}, {"messageId": "2120", "fix": "2407", "desc": "2122"}, {"messageId": "2123", "fix": "2408", "desc": "2125"}, {"messageId": "2120", "fix": "2409", "desc": "2122"}, {"messageId": "2123", "fix": "2410", "desc": "2125"}, {"messageId": "2120", "fix": "2411", "desc": "2122"}, {"messageId": "2123", "fix": "2412", "desc": "2125"}, {"messageId": "2120", "fix": "2413", "desc": "2122"}, {"messageId": "2123", "fix": "2414", "desc": "2125"}, {"messageId": "2120", "fix": "2415", "desc": "2122"}, {"messageId": "2123", "fix": "2416", "desc": "2125"}, {"messageId": "2120", "fix": "2417", "desc": "2122"}, {"messageId": "2123", "fix": "2418", "desc": "2125"}, {"messageId": "2120", "fix": "2419", "desc": "2122"}, {"messageId": "2123", "fix": "2420", "desc": "2125"}, {"messageId": "2120", "fix": "2421", "desc": "2122"}, {"messageId": "2123", "fix": "2422", "desc": "2125"}, {"messageId": "2120", "fix": "2423", "desc": "2122"}, {"messageId": "2123", "fix": "2424", "desc": "2125"}, {"messageId": "2120", "fix": "2425", "desc": "2122"}, {"messageId": "2123", "fix": "2426", "desc": "2125"}, {"messageId": "2120", "fix": "2427", "desc": "2122"}, {"messageId": "2123", "fix": "2428", "desc": "2125"}, {"messageId": "2120", "fix": "2429", "desc": "2122"}, {"messageId": "2123", "fix": "2430", "desc": "2125"}, {"messageId": "2120", "fix": "2431", "desc": "2122"}, {"messageId": "2123", "fix": "2432", "desc": "2125"}, {"messageId": "2120", "fix": "2433", "desc": "2122"}, {"messageId": "2123", "fix": "2434", "desc": "2125"}, {"messageId": "2120", "fix": "2435", "desc": "2122"}, {"messageId": "2123", "fix": "2436", "desc": "2125"}, {"messageId": "2120", "fix": "2437", "desc": "2122"}, {"messageId": "2123", "fix": "2438", "desc": "2125"}, {"messageId": "2120", "fix": "2439", "desc": "2122"}, {"messageId": "2123", "fix": "2440", "desc": "2125"}, {"messageId": "2120", "fix": "2441", "desc": "2122"}, {"messageId": "2123", "fix": "2442", "desc": "2125"}, {"messageId": "2120", "fix": "2443", "desc": "2122"}, {"messageId": "2123", "fix": "2444", "desc": "2125"}, {"messageId": "2120", "fix": "2445", "desc": "2122"}, {"messageId": "2123", "fix": "2446", "desc": "2125"}, {"messageId": "2120", "fix": "2447", "desc": "2122"}, {"messageId": "2123", "fix": "2448", "desc": "2125"}, {"messageId": "2120", "fix": "2449", "desc": "2122"}, {"messageId": "2123", "fix": "2450", "desc": "2125"}, {"messageId": "2120", "fix": "2451", "desc": "2122"}, {"messageId": "2123", "fix": "2452", "desc": "2125"}, {"messageId": "2120", "fix": "2453", "desc": "2122"}, {"messageId": "2123", "fix": "2454", "desc": "2125"}, {"messageId": "2120", "fix": "2455", "desc": "2122"}, {"messageId": "2123", "fix": "2456", "desc": "2125"}, {"messageId": "2120", "fix": "2457", "desc": "2122"}, {"messageId": "2123", "fix": "2458", "desc": "2125"}, {"messageId": "2120", "fix": "2459", "desc": "2122"}, {"messageId": "2123", "fix": "2460", "desc": "2125"}, {"messageId": "2120", "fix": "2461", "desc": "2122"}, {"messageId": "2123", "fix": "2462", "desc": "2125"}, {"messageId": "2120", "fix": "2463", "desc": "2122"}, {"messageId": "2123", "fix": "2464", "desc": "2125"}, {"messageId": "2120", "fix": "2465", "desc": "2122"}, {"messageId": "2123", "fix": "2466", "desc": "2125"}, {"messageId": "2120", "fix": "2467", "desc": "2122"}, {"messageId": "2123", "fix": "2468", "desc": "2125"}, {"desc": "2469", "fix": "2470"}, {"messageId": "2120", "fix": "2471", "desc": "2122"}, {"messageId": "2123", "fix": "2472", "desc": "2125"}, {"messageId": "2181", "data": "2473", "fix": "2474", "desc": "2260"}, {"messageId": "2181", "data": "2475", "fix": "2476", "desc": "2263"}, {"messageId": "2181", "data": "2477", "fix": "2478", "desc": "2266"}, {"messageId": "2181", "data": "2479", "fix": "2480", "desc": "2269"}, {"messageId": "2181", "data": "2481", "fix": "2482", "desc": "2184"}, {"messageId": "2181", "data": "2483", "fix": "2484", "desc": "2187"}, {"messageId": "2181", "data": "2485", "fix": "2486", "desc": "2190"}, {"messageId": "2181", "data": "2487", "fix": "2488", "desc": "2193"}, {"messageId": "2181", "data": "2489", "fix": "2490", "desc": "2184"}, {"messageId": "2181", "data": "2491", "fix": "2492", "desc": "2187"}, {"messageId": "2181", "data": "2493", "fix": "2494", "desc": "2190"}, {"messageId": "2181", "data": "2495", "fix": "2496", "desc": "2193"}, {"messageId": "2181", "data": "2497", "fix": "2498", "desc": "2184"}, {"messageId": "2181", "data": "2499", "fix": "2500", "desc": "2187"}, {"messageId": "2181", "data": "2501", "fix": "2502", "desc": "2190"}, {"messageId": "2181", "data": "2503", "fix": "2504", "desc": "2193"}, {"messageId": "2181", "data": "2505", "fix": "2506", "desc": "2184"}, {"messageId": "2181", "data": "2507", "fix": "2508", "desc": "2187"}, {"messageId": "2181", "data": "2509", "fix": "2510", "desc": "2190"}, {"messageId": "2181", "data": "2511", "fix": "2512", "desc": "2193"}, {"messageId": "2181", "data": "2513", "fix": "2514", "desc": "2260"}, {"messageId": "2181", "data": "2515", "fix": "2516", "desc": "2263"}, {"messageId": "2181", "data": "2517", "fix": "2518", "desc": "2266"}, {"messageId": "2181", "data": "2519", "fix": "2520", "desc": "2269"}, {"messageId": "2181", "data": "2521", "fix": "2522", "desc": "2184"}, {"messageId": "2181", "data": "2523", "fix": "2524", "desc": "2187"}, {"messageId": "2181", "data": "2525", "fix": "2526", "desc": "2190"}, {"messageId": "2181", "data": "2527", "fix": "2528", "desc": "2193"}, {"messageId": "2181", "data": "2529", "fix": "2530", "desc": "2184"}, {"messageId": "2181", "data": "2531", "fix": "2532", "desc": "2187"}, {"messageId": "2181", "data": "2533", "fix": "2534", "desc": "2190"}, {"messageId": "2181", "data": "2535", "fix": "2536", "desc": "2193"}, {"messageId": "2181", "data": "2537", "fix": "2538", "desc": "2260"}, {"messageId": "2181", "data": "2539", "fix": "2540", "desc": "2263"}, {"messageId": "2181", "data": "2541", "fix": "2542", "desc": "2266"}, {"messageId": "2181", "data": "2543", "fix": "2544", "desc": "2269"}, {"messageId": "2181", "data": "2545", "fix": "2546", "desc": "2260"}, {"messageId": "2181", "data": "2547", "fix": "2548", "desc": "2263"}, {"messageId": "2181", "data": "2549", "fix": "2550", "desc": "2266"}, {"messageId": "2181", "data": "2551", "fix": "2552", "desc": "2269"}, {"messageId": "2120", "fix": "2553", "desc": "2122"}, {"messageId": "2123", "fix": "2554", "desc": "2125"}, {"messageId": "2120", "fix": "2555", "desc": "2122"}, {"messageId": "2123", "fix": "2556", "desc": "2125"}, {"messageId": "2120", "fix": "2557", "desc": "2122"}, {"messageId": "2123", "fix": "2558", "desc": "2125"}, {"messageId": "2120", "fix": "2559", "desc": "2122"}, {"messageId": "2123", "fix": "2560", "desc": "2125"}, {"messageId": "2120", "fix": "2561", "desc": "2122"}, {"messageId": "2123", "fix": "2562", "desc": "2125"}, {"messageId": "2120", "fix": "2563", "desc": "2122"}, {"messageId": "2123", "fix": "2564", "desc": "2125"}, {"messageId": "2120", "fix": "2565", "desc": "2122"}, {"messageId": "2123", "fix": "2566", "desc": "2125"}, {"messageId": "2120", "fix": "2567", "desc": "2122"}, {"messageId": "2123", "fix": "2568", "desc": "2125"}, {"messageId": "2120", "fix": "2569", "desc": "2122"}, {"messageId": "2123", "fix": "2570", "desc": "2125"}, {"messageId": "2120", "fix": "2571", "desc": "2122"}, {"messageId": "2123", "fix": "2572", "desc": "2125"}, {"messageId": "2120", "fix": "2573", "desc": "2122"}, {"messageId": "2123", "fix": "2574", "desc": "2125"}, {"messageId": "2120", "fix": "2575", "desc": "2122"}, {"messageId": "2123", "fix": "2576", "desc": "2125"}, {"messageId": "2120", "fix": "2577", "desc": "2122"}, {"messageId": "2123", "fix": "2578", "desc": "2125"}, {"messageId": "2120", "fix": "2579", "desc": "2122"}, {"messageId": "2123", "fix": "2580", "desc": "2125"}, {"messageId": "2120", "fix": "2581", "desc": "2122"}, {"messageId": "2123", "fix": "2582", "desc": "2125"}, {"messageId": "2120", "fix": "2583", "desc": "2122"}, {"messageId": "2123", "fix": "2584", "desc": "2125"}, {"messageId": "2181", "data": "2585", "fix": "2586", "desc": "2260"}, {"messageId": "2181", "data": "2587", "fix": "2588", "desc": "2263"}, {"messageId": "2181", "data": "2589", "fix": "2590", "desc": "2266"}, {"messageId": "2181", "data": "2591", "fix": "2592", "desc": "2269"}, {"messageId": "2120", "fix": "2593", "desc": "2122"}, {"messageId": "2123", "fix": "2594", "desc": "2125"}, {"messageId": "2120", "fix": "2595", "desc": "2122"}, {"messageId": "2123", "fix": "2596", "desc": "2125"}, {"messageId": "2120", "fix": "2597", "desc": "2122"}, {"messageId": "2123", "fix": "2598", "desc": "2125"}, {"messageId": "2120", "fix": "2599", "desc": "2122"}, {"messageId": "2123", "fix": "2600", "desc": "2125"}, {"messageId": "2181", "data": "2601", "fix": "2602", "desc": "2260"}, {"messageId": "2181", "data": "2603", "fix": "2604", "desc": "2263"}, {"messageId": "2181", "data": "2605", "fix": "2606", "desc": "2266"}, {"messageId": "2181", "data": "2607", "fix": "2608", "desc": "2269"}, {"messageId": "2120", "fix": "2609", "desc": "2122"}, {"messageId": "2123", "fix": "2610", "desc": "2125"}, {"messageId": "2120", "fix": "2611", "desc": "2122"}, {"messageId": "2123", "fix": "2612", "desc": "2125"}, {"messageId": "2120", "fix": "2613", "desc": "2122"}, {"messageId": "2123", "fix": "2614", "desc": "2125"}, {"desc": "2615", "fix": "2616"}, {"messageId": "2120", "fix": "2617", "desc": "2122"}, {"messageId": "2123", "fix": "2618", "desc": "2125"}, {"messageId": "2120", "fix": "2619", "desc": "2122"}, {"messageId": "2123", "fix": "2620", "desc": "2125"}, {"messageId": "2120", "fix": "2621", "desc": "2122"}, {"messageId": "2123", "fix": "2622", "desc": "2125"}, {"messageId": "2120", "fix": "2623", "desc": "2122"}, {"messageId": "2123", "fix": "2624", "desc": "2125"}, {"desc": "2625", "fix": "2626"}, {"desc": "2625", "fix": "2627"}, {"desc": "2625", "fix": "2628"}, {"desc": "2625", "fix": "2629"}, {"messageId": "2120", "fix": "2630", "desc": "2122"}, {"messageId": "2123", "fix": "2631", "desc": "2125"}, {"messageId": "2120", "fix": "2632", "desc": "2122"}, {"messageId": "2123", "fix": "2633", "desc": "2125"}, {"messageId": "2120", "fix": "2634", "desc": "2122"}, {"messageId": "2123", "fix": "2635", "desc": "2125"}, {"messageId": "2120", "fix": "2636", "desc": "2122"}, {"messageId": "2123", "fix": "2637", "desc": "2125"}, {"messageId": "2120", "fix": "2638", "desc": "2122"}, {"messageId": "2123", "fix": "2639", "desc": "2125"}, "suggestUnknown", {"range": "2640", "text": "2641"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2642", "text": "2643"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2644", "text": "2641"}, {"range": "2645", "text": "2643"}, {"range": "2646", "text": "2641"}, {"range": "2647", "text": "2643"}, {"range": "2648", "text": "2641"}, {"range": "2649", "text": "2643"}, {"range": "2650", "text": "2641"}, {"range": "2651", "text": "2643"}, {"range": "2652", "text": "2641"}, {"range": "2653", "text": "2643"}, {"range": "2654", "text": "2641"}, {"range": "2655", "text": "2643"}, {"range": "2656", "text": "2641"}, {"range": "2657", "text": "2643"}, {"range": "2658", "text": "2641"}, {"range": "2659", "text": "2643"}, {"range": "2660", "text": "2641"}, {"range": "2661", "text": "2643"}, {"range": "2662", "text": "2641"}, {"range": "2663", "text": "2643"}, {"range": "2664", "text": "2641"}, {"range": "2665", "text": "2643"}, {"range": "2666", "text": "2641"}, {"range": "2667", "text": "2643"}, {"range": "2668", "text": "2641"}, {"range": "2669", "text": "2643"}, {"range": "2670", "text": "2641"}, {"range": "2671", "text": "2643"}, {"range": "2672", "text": "2641"}, {"range": "2673", "text": "2643"}, {"range": "2674", "text": "2641"}, {"range": "2675", "text": "2643"}, {"range": "2676", "text": "2641"}, {"range": "2677", "text": "2643"}, "Update the dependencies array to be: [fetchData, filters]", {"range": "2678", "text": "2679"}, {"range": "2680", "text": "2641"}, {"range": "2681", "text": "2643"}, {"range": "2682", "text": "2641"}, {"range": "2683", "text": "2643"}, {"range": "2684", "text": "2641"}, {"range": "2685", "text": "2643"}, {"range": "2686", "text": "2641"}, {"range": "2687", "text": "2643"}, {"range": "2688", "text": "2641"}, {"range": "2689", "text": "2643"}, {"range": "2690", "text": "2641"}, {"range": "2691", "text": "2643"}, {"range": "2692", "text": "2641"}, {"range": "2693", "text": "2643"}, {"range": "2694", "text": "2641"}, {"range": "2695", "text": "2643"}, {"range": "2696", "text": "2679"}, {"range": "2697", "text": "2641"}, {"range": "2698", "text": "2643"}, "replaceWithAlt", {"alt": "2699"}, {"range": "2700", "text": "2701"}, "Replace with `&quot;`.", {"alt": "2702"}, {"range": "2703", "text": "2704"}, "Replace with `&ldquo;`.", {"alt": "2705"}, {"range": "2706", "text": "2707"}, "Replace with `&#34;`.", {"alt": "2708"}, {"range": "2709", "text": "2710"}, "Replace with `&rdquo;`.", {"alt": "2699"}, {"range": "2711", "text": "2712"}, {"alt": "2702"}, {"range": "2713", "text": "2714"}, {"alt": "2705"}, {"range": "2715", "text": "2716"}, {"alt": "2708"}, {"range": "2717", "text": "2718"}, {"range": "2719", "text": "2641"}, {"range": "2720", "text": "2643"}, {"range": "2721", "text": "2641"}, {"range": "2722", "text": "2643"}, {"range": "2723", "text": "2641"}, {"range": "2724", "text": "2643"}, {"range": "2725", "text": "2641"}, {"range": "2726", "text": "2643"}, {"range": "2727", "text": "2641"}, {"range": "2728", "text": "2643"}, {"range": "2729", "text": "2641"}, {"range": "2730", "text": "2643"}, {"range": "2731", "text": "2641"}, {"range": "2732", "text": "2643"}, {"range": "2733", "text": "2641"}, {"range": "2734", "text": "2643"}, {"range": "2735", "text": "2641"}, {"range": "2736", "text": "2643"}, {"range": "2737", "text": "2641"}, {"range": "2738", "text": "2643"}, {"range": "2739", "text": "2641"}, {"range": "2740", "text": "2643"}, {"range": "2741", "text": "2641"}, {"range": "2742", "text": "2643"}, {"range": "2743", "text": "2641"}, {"range": "2744", "text": "2643"}, {"range": "2745", "text": "2641"}, {"range": "2746", "text": "2643"}, {"range": "2747", "text": "2641"}, {"range": "2748", "text": "2643"}, {"range": "2749", "text": "2641"}, {"range": "2750", "text": "2643"}, {"range": "2751", "text": "2641"}, {"range": "2752", "text": "2643"}, {"range": "2753", "text": "2641"}, {"range": "2754", "text": "2643"}, {"range": "2755", "text": "2641"}, {"range": "2756", "text": "2643"}, {"range": "2757", "text": "2641"}, {"range": "2758", "text": "2643"}, {"range": "2759", "text": "2641"}, {"range": "2760", "text": "2643"}, {"range": "2761", "text": "2641"}, {"range": "2762", "text": "2643"}, {"range": "2763", "text": "2641"}, {"range": "2764", "text": "2643"}, {"range": "2765", "text": "2641"}, {"range": "2766", "text": "2643"}, {"range": "2767", "text": "2641"}, {"range": "2768", "text": "2643"}, {"range": "2769", "text": "2641"}, {"range": "2770", "text": "2643"}, {"range": "2771", "text": "2641"}, {"range": "2772", "text": "2643"}, {"range": "2773", "text": "2641"}, {"range": "2774", "text": "2643"}, {"alt": "2775"}, {"range": "2776", "text": "2777"}, "Replace with `&apos;`.", {"alt": "2778"}, {"range": "2779", "text": "2780"}, "Replace with `&lsquo;`.", {"alt": "2781"}, {"range": "2782", "text": "2783"}, "Replace with `&#39;`.", {"alt": "2784"}, {"range": "2785", "text": "2786"}, "Replace with `&rsquo;`.", {"range": "2787", "text": "2641"}, {"range": "2788", "text": "2643"}, {"range": "2789", "text": "2641"}, {"range": "2790", "text": "2643"}, {"range": "2791", "text": "2641"}, {"range": "2792", "text": "2643"}, {"range": "2793", "text": "2641"}, {"range": "2794", "text": "2643"}, {"alt": "2775"}, {"range": "2795", "text": "2796"}, {"alt": "2778"}, {"range": "2797", "text": "2798"}, {"alt": "2781"}, {"range": "2799", "text": "2800"}, {"alt": "2784"}, {"range": "2801", "text": "2802"}, {"alt": "2775"}, {"range": "2803", "text": "2796"}, {"alt": "2778"}, {"range": "2804", "text": "2798"}, {"alt": "2781"}, {"range": "2805", "text": "2800"}, {"alt": "2784"}, {"range": "2806", "text": "2802"}, {"range": "2807", "text": "2641"}, {"range": "2808", "text": "2643"}, {"range": "2809", "text": "2641"}, {"range": "2810", "text": "2643"}, {"range": "2811", "text": "2641"}, {"range": "2812", "text": "2643"}, {"alt": "2775"}, {"range": "2813", "text": "2814"}, {"alt": "2778"}, {"range": "2815", "text": "2816"}, {"alt": "2781"}, {"range": "2817", "text": "2818"}, {"alt": "2784"}, {"range": "2819", "text": "2820"}, {"range": "2821", "text": "2641"}, {"range": "2822", "text": "2643"}, {"range": "2823", "text": "2641"}, {"range": "2824", "text": "2643"}, {"range": "2825", "text": "2641"}, {"range": "2826", "text": "2643"}, {"range": "2827", "text": "2641"}, {"range": "2828", "text": "2643"}, {"range": "2829", "text": "2641"}, {"range": "2830", "text": "2643"}, {"range": "2831", "text": "2641"}, {"range": "2832", "text": "2643"}, {"range": "2833", "text": "2641"}, {"range": "2834", "text": "2643"}, {"alt": "2775"}, {"range": "2835", "text": "2836"}, {"alt": "2778"}, {"range": "2837", "text": "2838"}, {"alt": "2781"}, {"range": "2839", "text": "2840"}, {"alt": "2784"}, {"range": "2841", "text": "2842"}, {"range": "2843", "text": "2641"}, {"range": "2844", "text": "2643"}, {"alt": "2775"}, {"range": "2845", "text": "2846"}, {"alt": "2778"}, {"range": "2847", "text": "2848"}, {"alt": "2781"}, {"range": "2849", "text": "2850"}, {"alt": "2784"}, {"range": "2851", "text": "2852"}, {"range": "2853", "text": "2641"}, {"range": "2854", "text": "2643"}, {"range": "2855", "text": "2641"}, {"range": "2856", "text": "2643"}, "Update the dependencies array to be: [isSignedIn, user, userLoaded, isAuthenticated, actions, getToken, identifyUser]", {"range": "2857", "text": "2858"}, "replaceEmptyInterfaceWithSuper", {"range": "2859", "text": "2860"}, "Replace empty interface with a type alias.", {"range": "2861", "text": "2641"}, {"range": "2862", "text": "2643"}, {"range": "2863", "text": "2641"}, {"range": "2864", "text": "2643"}, {"range": "2865", "text": "2641"}, {"range": "2866", "text": "2643"}, {"range": "2867", "text": "2641"}, {"range": "2868", "text": "2643"}, {"range": "2869", "text": "2641"}, {"range": "2870", "text": "2643"}, {"range": "2871", "text": "2641"}, {"range": "2872", "text": "2643"}, {"range": "2873", "text": "2641"}, {"range": "2874", "text": "2643"}, {"range": "2875", "text": "2641"}, {"range": "2876", "text": "2643"}, {"range": "2877", "text": "2641"}, {"range": "2878", "text": "2643"}, {"range": "2879", "text": "2641"}, {"range": "2880", "text": "2643"}, {"range": "2881", "text": "2641"}, {"range": "2882", "text": "2643"}, {"range": "2883", "text": "2641"}, {"range": "2884", "text": "2643"}, "Update the dependencies array to be: [isLoaded, user, isUserSynced, isSyncing, syncUserToBackend]", {"range": "2885", "text": "2886"}, {"range": "2887", "text": "2641"}, {"range": "2888", "text": "2643"}, {"range": "2889", "text": "2641"}, {"range": "2890", "text": "2643"}, {"range": "2891", "text": "2641"}, {"range": "2892", "text": "2643"}, {"range": "2893", "text": "2641"}, {"range": "2894", "text": "2643"}, {"range": "2895", "text": "2641"}, {"range": "2896", "text": "2643"}, {"range": "2897", "text": "2641"}, {"range": "2898", "text": "2643"}, {"range": "2899", "text": "2641"}, {"range": "2900", "text": "2643"}, {"range": "2901", "text": "2641"}, {"range": "2902", "text": "2643"}, {"range": "2903", "text": "2641"}, {"range": "2904", "text": "2643"}, {"range": "2905", "text": "2641"}, {"range": "2906", "text": "2643"}, {"range": "2907", "text": "2641"}, {"range": "2908", "text": "2643"}, {"range": "2909", "text": "2641"}, {"range": "2910", "text": "2643"}, {"range": "2911", "text": "2641"}, {"range": "2912", "text": "2643"}, {"range": "2913", "text": "2641"}, {"range": "2914", "text": "2643"}, {"range": "2915", "text": "2641"}, {"range": "2916", "text": "2643"}, {"range": "2917", "text": "2641"}, {"range": "2918", "text": "2643"}, {"range": "2919", "text": "2641"}, {"range": "2920", "text": "2643"}, {"range": "2921", "text": "2641"}, {"range": "2922", "text": "2643"}, {"range": "2923", "text": "2641"}, {"range": "2924", "text": "2643"}, {"range": "2925", "text": "2641"}, {"range": "2926", "text": "2643"}, {"range": "2927", "text": "2641"}, {"range": "2928", "text": "2643"}, {"range": "2929", "text": "2641"}, {"range": "2930", "text": "2643"}, {"range": "2931", "text": "2641"}, {"range": "2932", "text": "2643"}, {"range": "2933", "text": "2641"}, {"range": "2934", "text": "2643"}, {"range": "2935", "text": "2641"}, {"range": "2936", "text": "2643"}, {"range": "2937", "text": "2641"}, {"range": "2938", "text": "2643"}, {"range": "2939", "text": "2641"}, {"range": "2940", "text": "2643"}, {"range": "2941", "text": "2641"}, {"range": "2942", "text": "2643"}, {"range": "2943", "text": "2641"}, {"range": "2944", "text": "2643"}, {"range": "2945", "text": "2641"}, {"range": "2946", "text": "2643"}, {"range": "2947", "text": "2641"}, {"range": "2948", "text": "2643"}, {"range": "2949", "text": "2641"}, {"range": "2950", "text": "2643"}, {"range": "2951", "text": "2641"}, {"range": "2952", "text": "2643"}, {"range": "2953", "text": "2641"}, {"range": "2954", "text": "2643"}, {"range": "2955", "text": "2641"}, {"range": "2956", "text": "2643"}, {"range": "2957", "text": "2641"}, {"range": "2958", "text": "2643"}, {"range": "2959", "text": "2641"}, {"range": "2960", "text": "2643"}, {"range": "2961", "text": "2641"}, {"range": "2962", "text": "2643"}, {"range": "2963", "text": "2641"}, {"range": "2964", "text": "2643"}, {"range": "2965", "text": "2641"}, {"range": "2966", "text": "2643"}, {"range": "2967", "text": "2641"}, {"range": "2968", "text": "2643"}, {"range": "2969", "text": "2641"}, {"range": "2970", "text": "2643"}, {"range": "2971", "text": "2641"}, {"range": "2972", "text": "2643"}, {"range": "2973", "text": "2641"}, {"range": "2974", "text": "2643"}, {"range": "2975", "text": "2641"}, {"range": "2976", "text": "2643"}, {"range": "2977", "text": "2641"}, {"range": "2978", "text": "2643"}, {"range": "2979", "text": "2641"}, {"range": "2980", "text": "2643"}, "Update the dependencies array to be: [fetchTokens, isSignedIn]", {"range": "2981", "text": "2982"}, {"range": "2983", "text": "2641"}, {"range": "2984", "text": "2643"}, {"alt": "2775"}, {"range": "2985", "text": "2986"}, {"alt": "2778"}, {"range": "2987", "text": "2988"}, {"alt": "2781"}, {"range": "2989", "text": "2990"}, {"alt": "2784"}, {"range": "2991", "text": "2992"}, {"alt": "2699"}, {"range": "2993", "text": "2994"}, {"alt": "2702"}, {"range": "2995", "text": "2996"}, {"alt": "2705"}, {"range": "2997", "text": "2998"}, {"alt": "2708"}, {"range": "2999", "text": "3000"}, {"alt": "2699"}, {"range": "3001", "text": "3002"}, {"alt": "2702"}, {"range": "3003", "text": "3004"}, {"alt": "2705"}, {"range": "3005", "text": "3006"}, {"alt": "2708"}, {"range": "3007", "text": "3008"}, {"alt": "2699"}, {"range": "3009", "text": "3010"}, {"alt": "2702"}, {"range": "3011", "text": "3012"}, {"alt": "2705"}, {"range": "3013", "text": "3014"}, {"alt": "2708"}, {"range": "3015", "text": "3016"}, {"alt": "2699"}, {"range": "3017", "text": "3018"}, {"alt": "2702"}, {"range": "3019", "text": "3020"}, {"alt": "2705"}, {"range": "3021", "text": "3022"}, {"alt": "2708"}, {"range": "3023", "text": "3024"}, {"alt": "2775"}, {"range": "3025", "text": "3026"}, {"alt": "2778"}, {"range": "3027", "text": "3028"}, {"alt": "2781"}, {"range": "3029", "text": "3030"}, {"alt": "2784"}, {"range": "3031", "text": "3032"}, {"alt": "2699"}, {"range": "3033", "text": "3034"}, {"alt": "2702"}, {"range": "3035", "text": "3036"}, {"alt": "2705"}, {"range": "3037", "text": "3038"}, {"alt": "2708"}, {"range": "3039", "text": "3040"}, {"alt": "2699"}, {"range": "3041", "text": "3042"}, {"alt": "2702"}, {"range": "3043", "text": "3044"}, {"alt": "2705"}, {"range": "3045", "text": "3046"}, {"alt": "2708"}, {"range": "3047", "text": "3048"}, {"alt": "2775"}, {"range": "3049", "text": "3050"}, {"alt": "2778"}, {"range": "3051", "text": "3052"}, {"alt": "2781"}, {"range": "3053", "text": "3054"}, {"alt": "2784"}, {"range": "3055", "text": "3056"}, {"alt": "2775"}, {"range": "3057", "text": "3058"}, {"alt": "2778"}, {"range": "3059", "text": "3060"}, {"alt": "2781"}, {"range": "3061", "text": "3062"}, {"alt": "2784"}, {"range": "3063", "text": "3064"}, {"range": "3065", "text": "2641"}, {"range": "3066", "text": "2643"}, {"range": "3067", "text": "2641"}, {"range": "3068", "text": "2643"}, {"range": "3069", "text": "2641"}, {"range": "3070", "text": "2643"}, {"range": "3071", "text": "2641"}, {"range": "3072", "text": "2643"}, {"range": "3073", "text": "2641"}, {"range": "3074", "text": "2643"}, {"range": "3075", "text": "2641"}, {"range": "3076", "text": "2643"}, {"range": "3077", "text": "2641"}, {"range": "3078", "text": "2643"}, {"range": "3079", "text": "2641"}, {"range": "3080", "text": "2643"}, {"range": "3081", "text": "2641"}, {"range": "3082", "text": "2643"}, {"range": "3083", "text": "2641"}, {"range": "3084", "text": "2643"}, {"range": "3085", "text": "2641"}, {"range": "3086", "text": "2643"}, {"range": "3087", "text": "2641"}, {"range": "3088", "text": "2643"}, {"range": "3089", "text": "2641"}, {"range": "3090", "text": "2643"}, {"range": "3091", "text": "2641"}, {"range": "3092", "text": "2643"}, {"range": "3093", "text": "2641"}, {"range": "3094", "text": "2643"}, {"range": "3095", "text": "2641"}, {"range": "3096", "text": "2643"}, {"alt": "2775"}, {"range": "3097", "text": "3098"}, {"alt": "2778"}, {"range": "3099", "text": "3100"}, {"alt": "2781"}, {"range": "3101", "text": "3102"}, {"alt": "2784"}, {"range": "3103", "text": "3104"}, {"range": "3105", "text": "2641"}, {"range": "3106", "text": "2643"}, {"range": "3107", "text": "2641"}, {"range": "3108", "text": "2643"}, {"range": "3109", "text": "2641"}, {"range": "3110", "text": "2643"}, {"range": "3111", "text": "2641"}, {"range": "3112", "text": "2643"}, {"alt": "2775"}, {"range": "3113", "text": "3114"}, {"alt": "2778"}, {"range": "3115", "text": "3116"}, {"alt": "2781"}, {"range": "3117", "text": "3118"}, {"alt": "2784"}, {"range": "3119", "text": "3120"}, {"range": "3121", "text": "2641"}, {"range": "3122", "text": "2643"}, {"range": "3123", "text": "2641"}, {"range": "3124", "text": "2643"}, {"range": "3125", "text": "2641"}, {"range": "3126", "text": "2643"}, "Update the dependencies array to be: [canProceed, currentQuestion?.type, handleNext, isLastStep]", {"range": "3127", "text": "3128"}, {"range": "3129", "text": "2641"}, {"range": "3130", "text": "2643"}, {"range": "3131", "text": "2641"}, {"range": "3132", "text": "2643"}, {"range": "3133", "text": "2641"}, {"range": "3134", "text": "2643"}, {"range": "3135", "text": "2641"}, {"range": "3136", "text": "2643"}, "Update the dependencies array to be: [updateState, projectId, toast, handleError]", {"range": "3137", "text": "3138"}, {"range": "3139", "text": "3138"}, {"range": "3140", "text": "3138"}, {"range": "3141", "text": "3138"}, {"range": "3142", "text": "2641"}, {"range": "3143", "text": "2643"}, {"range": "3144", "text": "2641"}, {"range": "3145", "text": "2643"}, {"range": "3146", "text": "2641"}, {"range": "3147", "text": "2643"}, {"range": "3148", "text": "2641"}, {"range": "3149", "text": "2643"}, {"range": "3150", "text": "2641"}, {"range": "3151", "text": "2643"}, [757, 760], "unknown", [757, 760], "never", [1429, 1432], [1429, 1432], [7584, 7587], [7584, 7587], [8541, 8544], [8541, 8544], [9268, 9271], [9268, 9271], [10164, 10167], [10164, 10167], [667, 670], [667, 670], [1339, 1342], [1339, 1342], [787, 790], [787, 790], [1458, 1461], [1458, 1461], [3011, 3014], [3011, 3014], [6282, 6285], [6282, 6285], [7885, 7888], [7885, 7888], [8825, 8828], [8825, 8828], [10392, 10395], [10392, 10395], [908, 911], [908, 911], [1723, 1726], [1723, 1726], [2493, 2496], [2493, 2496], [2624, 2633], "[fetchData, filters]", [2990, 2993], [2990, 2993], [5914, 5917], [5914, 5917], [6558, 6561], [6558, 6561], [7325, 7328], [7325, 7328], [14011, 14014], [14011, 14014], [824, 827], [824, 827], [1532, 1535], [1532, 1535], [2119, 2122], [2119, 2122], [2250, 2259], [8196, 8199], [8196, 8199], "&quot;", [11005, 11039], "\n                                &quot;", "&ldquo;", [11005, 11039], "\n                                &ldquo;", "&#34;", [11005, 11039], "\n                                &#34;", "&rdquo;", [11005, 11039], "\n                                &rdquo;", [11061, 11093], "&quot;\n                              ", [11061, 11093], "&ldquo;\n                              ", [11061, 11093], "&#34;\n                              ", [11061, 11093], "&rdquo;\n                              ", [1043, 1046], [1043, 1046], [4734, 4737], [4734, 4737], [7032, 7035], [7032, 7035], [8375, 8378], [8375, 8378], [8548, 8551], [8548, 8551], [10055, 10058], [10055, 10058], [11830, 11833], [11830, 11833], [14018, 14021], [14018, 14021], [14152, 14155], [14152, 14155], [1915, 1918], [1915, 1918], [2273, 2276], [2273, 2276], [4152, 4155], [4152, 4155], [4552, 4555], [4552, 4555], [1646, 1649], [1646, 1649], [688, 691], [688, 691], [1356, 1359], [1356, 1359], [2902, 2905], [2902, 2905], [3102, 3105], [3102, 3105], [12035, 12038], [12035, 12038], [3881, 3884], [3881, 3884], [5485, 5488], [5485, 5488], [11251, 11254], [11251, 11254], [2164, 2167], [2164, 2167], [2540, 2543], [2540, 2543], [981, 984], [981, 984], [1180, 1183], [1180, 1183], [2540, 2543], [2540, 2543], [4125, 4128], [4125, 4128], "&apos;", [5327, 5362], "\n            Don&apos;t have an account?", "&lsquo;", [5327, 5362], "\n            Don&lsquo;t have an account?", "&#39;", [5327, 5362], "\n            Don&#39;t have an account?", "&rsquo;", [5327, 5362], "\n            Don&rsquo;t have an account?", [2513, 2516], [2513, 2516], [3174, 3177], [3174, 3177], [4650, 4653], [4650, 4653], [7211, 7214], [7211, 7214], [5042, 5079], "\n            Didn&apos;t receive the code?", [5042, 5079], "\n            Didn&lsquo;t receive the code?", [5042, 5079], "\n            Didn&#39;t receive the code?", [5042, 5079], "\n            Didn&rsquo;t receive the code?", [5031, 5068], [5031, 5068], [5031, 5068], [5031, 5068], [3448, 3451], [3448, 3451], [2629, 2632], [2629, 2632], [3061, 3064], [3061, 3064], [4344, 4418], "\n              You don&apos;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&lsquo;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&#39;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&rsquo;t have permission to edit this project\n            ", [7473, 7476], [7473, 7476], [3023, 3026], [3023, 3026], [1700, 1703], [1700, 1703], [1763, 1766], [1763, 1766], [2846, 2849], [2846, 2849], [3477, 3480], [3477, 3480], [4021, 4024], [4021, 4024], [7531, 7628], "\n                    Get notified when projects you&apos;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&lsquo;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&#39;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&rsquo;re working on are updated.\n                  ", [678, 681], [678, 681], [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&apos;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&lsquo;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&#39;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&rsquo;s productivity.\n          ", [962, 965], [962, 965], [1196, 1199], [1196, 1199], [4255, 4321], "[isSignedIn, user, userLoaded, isAuthenticated, actions, getToken, identifyUser]", [75, 161], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [3481, 3484], [3481, 3484], [5157, 5160], [5157, 5160], [643, 646], [643, 646], [1025, 1028], [1025, 1028], [3959, 3962], [3959, 3962], [6186, 6189], [6186, 6189], [7912, 7915], [7912, 7915], [8987, 8990], [8987, 8990], [2368, 2371], [2368, 2371], [4556, 4559], [4556, 4559], [1342, 1345], [1342, 1345], [365, 368], [365, 368], [2370, 2411], "[isLoaded, user, isUserSynced, isSyncing, syncUserToBackend]", [7878, 7881], [7878, 7881], [8011, 8014], [8011, 8014], [8445, 8448], [8445, 8448], [9420, 9423], [9420, 9423], [476, 479], [476, 479], [736, 739], [736, 739], [1094, 1097], [1094, 1097], [3178, 3181], [3178, 3181], [4313, 4316], [4313, 4316], [4494, 4497], [4494, 4497], [4533, 4536], [4533, 4536], [4772, 4775], [4772, 4775], [4811, 4814], [4811, 4814], [5053, 5056], [5053, 5056], [5092, 5095], [5092, 5095], [5338, 5341], [5338, 5341], [5523, 5526], [5523, 5526], [5864, 5867], [5864, 5867], [1897, 1900], [1897, 1900], [3169, 3172], [3169, 3172], [3796, 3799], [3796, 3799], [6171, 6174], [6171, 6174], [321, 324], [321, 324], [894, 897], [894, 897], [1625, 1628], [1625, 1628], [1696, 1699], [1696, 1699], [4222, 4225], [4222, 4225], [4679, 4682], [4679, 4682], [810, 813], [810, 813], [1325, 1328], [1325, 1328], [1501, 1504], [1501, 1504], [3470, 3473], [3470, 3473], [1491, 1494], [1491, 1494], [1631, 1634], [1631, 1634], [1933, 1936], [1933, 1936], [2473, 2476], [2473, 2476], [2234, 2237], [2234, 2237], [90, 93], [90, 93], [112, 115], [112, 115], [2453, 2456], [2453, 2456], [2656, 2659], [2656, 2659], [2821, 2824], [2821, 2824], [3283, 3286], [3283, 3286], [3355, 3358], [3355, 3358], [595, 598], [595, 598], [3999, 4002], [3999, 4002], [13591, 13594], [13591, 13594], [2685, 2697], "[fetchTokens, isSignedIn]", [3844, 3847], [3844, 3847], [4692, 4803], "\n              Check your browser&apos;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&lsquo;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&#39;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&rsquo;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [5139, 5172], "3. Filter by &quot;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &ldquo;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &#34;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &rdquo;posthog\" or \"batch\"", [5139, 5172], "3. Filter by \"posthog&quot; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&ldquo; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&#34; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&rdquo; or \"batch\"", [5139, 5172], "3. Filter by \"posthog\" or &quot;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &ldquo;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &#34;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &rdquo;batch\"", [5139, 5172], "3. Filter by \"posthog\" or \"batch&quot;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&ldquo;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&#34;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&rdquo;", [4624, 4893], "\n                  We&apos;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&lsquo;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&#39;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&rsquo;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &quot;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &ldquo;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &#34;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &rdquo;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&quot; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&ldquo; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&#34; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&rdquo; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [950, 1048], "\n              The blog post you&apos;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&lsquo;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&#39;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&rsquo;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&apos;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&lsquo;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&#39;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&rsquo;t exist or may have been moved.\n            ", [330, 333], [330, 333], [354, 357], [354, 357], [587, 590], [587, 590], [661, 664], [661, 664], [554, 557], [554, 557], [578, 581], [578, 581], [601, 604], [601, 604], [621, 624], [621, 624], [640, 643], [640, 643], [723, 726], [723, 726], [295, 298], [295, 298], [5407, 5410], [5407, 5410], [6657, 6660], [6657, 6660], [216, 219], [216, 219], [1922, 1925], [1922, 1925], [2094, 2097], [2094, 2097], [2938, 2992], "\n            It&apos;s too early to work on this\n          ", [2938, 2992], "\n            It&lsquo;s too early to work on this\n          ", [2938, 2992], "\n            It&#39;s too early to work on this\n          ", [2938, 2992], "\n            It&rsquo;s too early to work on this\n          ", [4864, 4867], [4864, 4867], [5015, 5018], [5015, 5018], [6128, 6131], [6128, 6131], [6287, 6290], [6287, 6290], [8671, 8738], "\n                We&apos;re setting up everything for you\n              ", [8671, 8738], "\n                We&lsquo;re setting up everything for you\n              ", [8671, 8738], "\n                We&#39;re setting up everything for you\n              ", [8671, 8738], "\n                We&rsquo;re setting up everything for you\n              ", [978, 981], [978, 981], [1056, 1059], [1056, 1059], [1333, 1336], [1333, 1336], [2554, 2601], "[canProceed, currentQuestion?.type, handleNext, isLastStep]", [3075, 3078], [3075, 3078], [3207, 3210], [3207, 3210], [3307, 3310], [3307, 3310], [3579, 3582], [3579, 3582], [3599, 3630], "[updateState, projectId, toast, handleError]", [4511, 4542], [5292, 5323], [6508, 6539], [554, 557], [554, 557], [1183, 1186], [1183, 1186], [1291, 1294], [1291, 1294], [1405, 1408], [1405, 1408], [1428, 1431], [1428, 1431]]