[{"/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx": "1", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx": "2", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx": "3", "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx": "4", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-metrics/page.tsx": "5", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx": "6", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/feedbacks/page.tsx": "7", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/summary/page.tsx": "8", "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/users/page.tsx": "9", "/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx": "10", "/Users/<USER>/Data/new era/siift-next/src/app/admin/health/page.tsx": "11", "/Users/<USER>/Data/new era/siift-next/src/app/admin/notifications/page.tsx": "12", "/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx": "13", "/Users/<USER>/Data/new era/siift-next/src/app/admin/profile/page.tsx": "14", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/all/page.tsx": "15", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/create/page.tsx": "16", "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/page.tsx": "17", "/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx": "18", "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx": "19", "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx": "20", "/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx": "21", "/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx": "22", "/Users/<USER>/Data/new era/siift-next/src/app/admin/users/page.tsx": "23", "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/me/route.ts": "24", "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/signin/route.ts": "25", "/Users/<USER>/Data/new era/siift-next/src/app/api/health/route.ts": "26", "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/[id]/route.ts": "27", "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/route.ts": "28", "/Users/<USER>/Data/new era/siift-next/src/app/api/webhooks/clerk/route.ts": "29", "/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx": "30", "/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx": "31", "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx": "32", "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx": "33", "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/page.tsx": "34", "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx": "35", "/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx": "36", "/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx": "37", "/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx": "38", "/Users/<USER>/Data/new era/siift-next/src/app/page.tsx": "39", "/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx": "40", "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx": "41", "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx": "42", "/Users/<USER>/Data/new era/siift-next/src/app/projects/create/page.tsx": "43", "/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx": "44", "/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx": "45", "/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx": "46", "/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx": "47", "/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx": "48", "/Users/<USER>/Data/new era/siift-next/src/components/admin-sidebar.tsx": "49", "/Users/<USER>/Data/new era/siift-next/src/components/admin-tabbed-content.tsx": "50", "/Users/<USER>/Data/new era/siift-next/src/components/auth/admin-route.tsx": "51", "/Users/<USER>/Data/new era/siift-next/src/components/auth/auth-card.tsx": "52", "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGrid.tsx": "53", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/DashboardHeader.tsx": "54", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-hero-section.tsx": "55", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-projects-section.tsx": "56", "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx": "57", "/Users/<USER>/Data/new era/siift-next/src/components/landing/feature-grid.tsx": "58", "/Users/<USER>/Data/new era/siift-next/src/components/landing/hero-section.tsx": "59", "/Users/<USER>/Data/new era/siift-next/src/components/landing/landing-page.tsx": "60", "/Users/<USER>/Data/new era/siift-next/src/components/layout/admin-layout.tsx": "61", "/Users/<USER>/Data/new era/siift-next/src/components/layout/dashboard-layout.tsx": "62", "/Users/<USER>/Data/new era/siift-next/src/components/layout/footer.tsx": "63", "/Users/<USER>/Data/new era/siift-next/src/components/layout/header.tsx": "64", "/Users/<USER>/Data/new era/siift-next/src/components/layout/main-layout.tsx": "65", "/Users/<USER>/Data/new era/siift-next/src/components/nav-main.tsx": "66", "/Users/<USER>/Data/new era/siift-next/src/components/nav-user.tsx": "67", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/main-nav.tsx": "68", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/mobile-nav.tsx": "69", "/Users/<USER>/Data/new era/siift-next/src/components/navigation/user-menu.tsx": "70", "/Users/<USER>/Data/new era/siift-next/src/components/project-chat-sidebar.tsx": "71", "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationAnimation.tsx": "72", "/Users/<USER>/Data/new era/siift-next/src/components/project-sidebar.tsx": "73", "/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx": "74", "/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx": "75", "/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx": "76", "/Users/<USER>/Data/new era/siift-next/src/components/team-switcher.tsx": "77", "/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx": "78", "/Users/<USER>/Data/new era/siift-next/src/components/theme-toggle.tsx": "79", "/Users/<USER>/Data/new era/siift-next/src/components/ui/app-loading.tsx": "80", "/Users/<USER>/Data/new era/siift-next/src/components/ui/avatar.tsx": "81", "/Users/<USER>/Data/new era/siift-next/src/components/ui/background-beams.tsx": "82", "/Users/<USER>/Data/new era/siift-next/src/components/ui/badge.tsx": "83", "/Users/<USER>/Data/new era/siift-next/src/components/ui/breadcrumb.tsx": "84", "/Users/<USER>/Data/new era/siift-next/src/components/ui/button.tsx": "85", "/Users/<USER>/Data/new era/siift-next/src/components/ui/card.tsx": "86", "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-bubble.tsx": "87", "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-message-list.tsx": "88", "/Users/<USER>/Data/new era/siift-next/src/components/ui/checkbox.tsx": "89", "/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx": "90", "/Users/<USER>/Data/new era/siift-next/src/components/ui/dropdown-menu.tsx": "91", "/Users/<USER>/Data/new era/siift-next/src/components/ui/form-input.tsx": "92", "/Users/<USER>/Data/new era/siift-next/src/components/ui/form.tsx": "93", "/Users/<USER>/Data/new era/siift-next/src/components/ui/glow.tsx": "94", "/Users/<USER>/Data/new era/siift-next/src/components/ui/icons.tsx": "95", "/Users/<USER>/Data/new era/siift-next/src/components/ui/input.tsx": "96", "/Users/<USER>/Data/new era/siift-next/src/components/ui/label.tsx": "97", "/Users/<USER>/Data/new era/siift-next/src/components/ui/logo.tsx": "98", "/Users/<USER>/Data/new era/siift-next/src/components/ui/message-loading.tsx": "99", "/Users/<USER>/Data/new era/siift-next/src/components/ui/mockup.tsx": "100", "/Users/<USER>/Data/new era/siift-next/src/components/ui/navigation-menu.tsx": "101", "/Users/<USER>/Data/new era/siift-next/src/components/ui/progress.tsx": "102", "/Users/<USER>/Data/new era/siift-next/src/components/ui/scroll-area.tsx": "103", "/Users/<USER>/Data/new era/siift-next/src/components/ui/select.tsx": "104", "/Users/<USER>/Data/new era/siift-next/src/components/ui/separator.tsx": "105", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sheet.tsx": "106", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar-button.tsx": "107", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar.tsx": "108", "/Users/<USER>/Data/new era/siift-next/src/components/ui/skeleton.tsx": "109", "/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx": "110", "/Users/<USER>/Data/new era/siift-next/src/components/ui/switch.tsx": "111", "/Users/<USER>/Data/new era/siift-next/src/components/ui/tabs.tsx": "112", "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials-columns-1.tsx": "113", "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials.tsx": "114", "/Users/<USER>/Data/new era/siift-next/src/components/ui/textarea.tsx": "115", "/Users/<USER>/Data/new era/siift-next/src/components/ui/tooltip.tsx": "116", "/Users/<USER>/Data/new era/siift-next/src/components/ui/waitlist-section.tsx": "117", "/Users/<USER>/Data/new era/siift-next/src/components/user-tabbed-content.tsx": "118", "/Users/<USER>/Data/new era/siift-next/src/contexts/auth-context.tsx": "119", "/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx": "120", "/Users/<USER>/Data/new era/siift-next/src/hooks/mutations/useUserMutations.ts": "121", "/Users/<USER>/Data/new era/siift-next/src/hooks/queries/useUser.ts": "122", "/Users/<USER>/Data/new era/siift-next/src/hooks/use-auto-scroll.ts": "123", "/Users/<USER>/Data/new era/siift-next/src/hooks/use-mobile.ts": "124", "/Users/<USER>/Data/new era/siift-next/src/hooks/useAuth.ts": "125", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkAuth.ts": "126", "/Users/<USER>/Data/new era/siift-next/src/hooks/useResizable.ts": "127", "/Users/<USER>/Data/new era/siift-next/src/hooks/useToast.ts": "128", "/Users/<USER>/Data/new era/siift-next/src/hooks/useUserSync.ts": "129", "/Users/<USER>/Data/new era/siift-next/src/lib/admin-api.ts": "130", "/Users/<USER>/Data/new era/siift-next/src/lib/api.ts": "131", "/Users/<USER>/Data/new era/siift-next/src/lib/apiClient.ts": "132", "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsData.ts": "133", "/Users/<USER>/Data/new era/siift-next/src/lib/clerk-api.ts": "134", "/Users/<USER>/Data/new era/siift-next/src/lib/constants.ts": "135", "/Users/<USER>/Data/new era/siift-next/src/lib/fonts.ts": "136", "/Users/<USER>/Data/new era/siift-next/src/lib/jwt.ts": "137", "/Users/<USER>/Data/new era/siift-next/src/lib/mock-auth-api.ts": "138", "/Users/<USER>/Data/new era/siift-next/src/lib/mock-email-service.ts": "139", "/Users/<USER>/Data/new era/siift-next/src/lib/mockEventStream.ts": "140", "/Users/<USER>/Data/new era/siift-next/src/lib/projectCreationConfig.ts": "141", "/Users/<USER>/Data/new era/siift-next/src/lib/queryClient.ts": "142", "/Users/<USER>/Data/new era/siift-next/src/lib/realEventStream.ts": "143", "/Users/<USER>/Data/new era/siift-next/src/lib/session.ts": "144", "/Users/<USER>/Data/new era/siift-next/src/lib/tokenStorage.ts": "145", "/Users/<USER>/Data/new era/siift-next/src/lib/types.ts": "146", "/Users/<USER>/Data/new era/siift-next/src/lib/utils.ts": "147", "/Users/<USER>/Data/new era/siift-next/src/middleware.ts": "148", "/Users/<USER>/Data/new era/siift-next/src/stores/businessSectionStore.ts": "149", "/Users/<USER>/Data/new era/siift-next/src/stores/projectCreationStore.ts": "150", "/Users/<USER>/Data/new era/siift-next/src/stores/sessionStore.ts": "151", "/Users/<USER>/Data/new era/siift-next/src/types/BusinessSection.types.ts": "152", "/Users/<USER>/Data/new era/siift-next/src/types/Session.types.ts": "153", "/Users/<USER>/Data/new era/siift-next/src/types/email.types.ts": "154", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkApi.ts": "155", "/Users/<USER>/Data/new era/siift-next/src/app/robots.ts": "156", "/Users/<USER>/Data/new era/siift-next/src/app/sitemap.ts": "157", "/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx": "158", "/Users/<USER>/Data/new era/siift-next/src/components/seo/SEOHead.tsx": "159", "/Users/<USER>/Data/new era/siift-next/src/components/seo/StructuredData.tsx": "160", "/Users/<USER>/Data/new era/siift-next/src/hooks/useAnalytics.ts": "161", "/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx": "162", "/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx": "163", "/Users/<USER>/Data/new era/siift-next/src/app/error.tsx": "164", "/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx": "165", "/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx": "166", "/Users/<USER>/Data/new era/siift-next/src/components/EditableCell.tsx": "167", "/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx": "168", "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenDebug.tsx": "169", "/Users/<USER>/Data/new era/siift-next/src/components/ui/table.tsx": "170", "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkToken.ts": "171", "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStore.ts": "172", "/Users/<USER>/Data/new era/siift-next/src/components/ui/resize-handle.tsx": "173", "/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx": "174", "/Users/<USER>/Data/new era/siift-next/src/components/debug/PostHogDebug.tsx": "175", "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenExample.tsx": "176", "/Users/<USER>/Data/new era/siift-next/src/app/about/page.tsx": "177", "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/not-found.tsx": "178", "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/page.tsx": "179", "/Users/<USER>/Data/new era/siift-next/src/app/blog/page.tsx": "180", "/Users/<USER>/Data/new era/siift-next/src/app/test-ai-input/page.tsx": "181", "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-card.tsx": "182", "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-section.tsx": "183", "/Users/<USER>/Data/new era/siift-next/src/components/ui/ai-prompt-demo.tsx": "184", "/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx": "185", "/Users/<USER>/Data/new era/siift-next/src/data/blog-posts.ts": "186", "/Users/<USER>/Data/new era/siift-next/src/types/blog.ts": "187", "/Users/<USER>/Data/new era/siift-next/src/components/project/ContentSections.tsx": "188", "/Users/<USER>/Data/new era/siift-next/src/components/project/PrioritiesDropdown.tsx": "189", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProgressBar.tsx": "190", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx": "191", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectHeader.tsx": "192", "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx": "193", "/Users/<USER>/Data/new era/siift-next/src/components/project/index.ts": "194", "/Users/<USER>/Data/new era/siift-next/src/components/shared/ProjectInputSection.tsx": "195", "/Users/<USER>/Data/new era/siift-next/src/components/ui/dotted-background.tsx": "196", "/Users/<USER>/Data/new era/siift-next/src/components/ui/popover.tsx": "197", "/Users/<USER>/Data/new era/siift-next/src/lib/design-tokens.ts": "198", "/Users/<USER>/Data/new era/siift-next/src/mockdata/businessItemQuestions.ts": "199", "/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx": "200", "/Users/<USER>/Data/new era/siift-next/src/components/business-category-cards.tsx": "201", "/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx": "202", "/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx": "203", "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx": "204", "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationQuestionnaire.tsx": "205", "/Users/<USER>/Data/new era/siift-next/src/components/ui/stepper.tsx": "206", "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsData.ts": "207", "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsDataExtended.ts": "208", "/Users/<USER>/Data/new era/siift-next/src/hooks/useBusinessSections.ts": "209", "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsDataNew.ts": "210", "/Users/<USER>/Data/new era/siift-next/src/lib/cardOpacityUtils.ts": "211", "/Users/<USER>/Data/new era/siift-next/src/lib/dependencyManager.ts": "212", "/Users/<USER>/Data/new era/siift-next/src/lib/statusCountingSystem.ts": "213", "/Users/<USER>/Data/new era/siift-next/src/services/browserStorageService.ts": "214", "/Users/<USER>/Data/new era/siift-next/src/services/businessSectionsApi.ts": "215", "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStoreEnhanced.ts": "216", "/Users/<USER>/Data/new era/siift-next/src/types/BusinessItemData.types.ts": "217"}, {"size": 14293, "mtime": 1753675505450, "results": "218", "hashOfConfig": "219"}, {"size": 17842, "mtime": 1753675505489, "results": "220", "hashOfConfig": "219"}, {"size": 15868, "mtime": 1753675505503, "results": "221", "hashOfConfig": "219"}, {"size": 13664, "mtime": 1753675505496, "results": "222", "hashOfConfig": "219"}, {"size": 9942, "mtime": 1753675505565, "results": "223", "hashOfConfig": "219"}, {"size": 11912, "mtime": 1753675505594, "results": "224", "hashOfConfig": "219"}, {"size": 19398, "mtime": 1753675505584, "results": "225", "hashOfConfig": "219"}, {"size": 8154, "mtime": 1753675505601, "results": "226", "hashOfConfig": "219"}, {"size": 12828, "mtime": 1753675505575, "results": "227", "hashOfConfig": "219"}, {"size": 27409, "mtime": 1754123165734, "results": "228", "hashOfConfig": "219"}, {"size": 13221, "mtime": 1753675505478, "results": "229", "hashOfConfig": "219"}, {"size": 663, "mtime": 1753675505559, "results": "230", "hashOfConfig": "219"}, {"size": 761, "mtime": 1753675505552, "results": "231", "hashOfConfig": "219"}, {"size": 644, "mtime": 1753675505518, "results": "232", "hashOfConfig": "219"}, {"size": 645, "mtime": 1753675505456, "results": "233", "hashOfConfig": "219"}, {"size": 3723, "mtime": 1753675505471, "results": "234", "hashOfConfig": "219"}, {"size": 640, "mtime": 1753675505464, "results": "235", "hashOfConfig": "219"}, {"size": 648, "mtime": 1753675505608, "results": "236", "hashOfConfig": "219"}, {"size": 11761, "mtime": 1753675505443, "results": "237", "hashOfConfig": "219"}, {"size": 645, "mtime": 1753675505546, "results": "238", "hashOfConfig": "219"}, {"size": 13284, "mtime": 1753675505511, "results": "239", "hashOfConfig": "219"}, {"size": 12981, "mtime": 1753675505530, "results": "240", "hashOfConfig": "219"}, {"size": 13219, "mtime": 1753675505538, "results": "241", "hashOfConfig": "219"}, {"size": 1097, "mtime": 1753595146912, "results": "242", "hashOfConfig": "219"}, {"size": 1919, "mtime": 1753595158639, "results": "243", "hashOfConfig": "219"}, {"size": 992, "mtime": 1752468771519, "results": "244", "hashOfConfig": "219"}, {"size": 3891, "mtime": 1753672539953, "results": "245", "hashOfConfig": "219"}, {"size": 2881, "mtime": 1753595118765, "results": "246", "hashOfConfig": "219"}, {"size": 5436, "mtime": 1753672571172, "results": "247", "hashOfConfig": "219"}, {"size": 3893, "mtime": 1753675518355, "results": "248", "hashOfConfig": "219"}, {"size": 1303, "mtime": 1753675630418, "results": "249", "hashOfConfig": "219"}, {"size": 10224, "mtime": 1753675518375, "results": "250", "hashOfConfig": "219"}, {"size": 259, "mtime": 1753675518368, "results": "251", "hashOfConfig": "219"}, {"size": 13670, "mtime": 1753675518348, "results": "252", "hashOfConfig": "219"}, {"size": 259, "mtime": 1753675518342, "results": "253", "hashOfConfig": "219"}, {"size": 12893, "mtime": 1753675518361, "results": "254", "hashOfConfig": "219"}, {"size": 9905, "mtime": 1753675518335, "results": "255", "hashOfConfig": "219"}, {"size": 2963, "mtime": 1754542519082, "results": "256", "hashOfConfig": "219"}, {"size": 1172, "mtime": 1753673054748, "results": "257", "hashOfConfig": "219"}, {"size": 12835, "mtime": 1753675745805, "results": "258", "hashOfConfig": "219"}, {"size": 8822, "mtime": 1753675760836, "results": "259", "hashOfConfig": "219"}, {"size": 6330, "mtime": 1754544433195, "results": "260", "hashOfConfig": "219"}, {"size": 766, "mtime": 1753158462887, "results": "261", "hashOfConfig": "219"}, {"size": 5085, "mtime": 1753675869095, "results": "262", "hashOfConfig": "219"}, {"size": 9642, "mtime": 1753595833363, "results": "263", "hashOfConfig": "219"}, {"size": 19859, "mtime": 1753675760823, "results": "264", "hashOfConfig": "219"}, {"size": 613, "mtime": 1753665982933, "results": "265", "hashOfConfig": "219"}, {"size": 3981, "mtime": 1754362345691, "results": "266", "hashOfConfig": "219"}, {"size": 5728, "mtime": 1752982830884, "results": "267", "hashOfConfig": "219"}, {"size": 17129, "mtime": 1753596187151, "results": "268", "hashOfConfig": "219"}, {"size": 2425, "mtime": 1752998545730, "results": "269", "hashOfConfig": "219"}, {"size": 1390, "mtime": 1753853005228, "results": "270", "hashOfConfig": "219"}, {"size": 3813, "mtime": 1754546958543, "results": "271", "hashOfConfig": "219"}, {"size": 4728, "mtime": 1753852870110, "results": "272", "hashOfConfig": "219"}, {"size": 517, "mtime": 1754365165459, "results": "273", "hashOfConfig": "219"}, {"size": 6039, "mtime": 1753662236525, "results": "274", "hashOfConfig": "219"}, {"size": 4248, "mtime": 1754543797467, "results": "275", "hashOfConfig": "219"}, {"size": 3966, "mtime": 1753595529167, "results": "276", "hashOfConfig": "219"}, {"size": 12306, "mtime": 1754352771865, "results": "277", "hashOfConfig": "219"}, {"size": 1211, "mtime": 1754328801788, "results": "278", "hashOfConfig": "219"}, {"size": 3642, "mtime": 1752981130099, "results": "279", "hashOfConfig": "219"}, {"size": 392, "mtime": 1754336730216, "results": "280", "hashOfConfig": "219"}, {"size": 5758, "mtime": 1753853710936, "results": "281", "hashOfConfig": "219"}, {"size": 1254, "mtime": 1753852918837, "results": "282", "hashOfConfig": "219"}, {"size": 682, "mtime": 1753593043509, "results": "283", "hashOfConfig": "219"}, {"size": 3421, "mtime": 1752982524772, "results": "284", "hashOfConfig": "219"}, {"size": 5318, "mtime": 1753663916166, "results": "285", "hashOfConfig": "219"}, {"size": 2078, "mtime": 1754336685180, "results": "286", "hashOfConfig": "219"}, {"size": 2778, "mtime": 1754336697245, "results": "287", "hashOfConfig": "219"}, {"size": 2901, "mtime": 1753663572932, "results": "288", "hashOfConfig": "219"}, {"size": 5963, "mtime": 1754121922086, "results": "289", "hashOfConfig": "219"}, {"size": 8683, "mtime": 1754353683403, "results": "290", "hashOfConfig": "219"}, {"size": 11280, "mtime": 1754365318801, "results": "291", "hashOfConfig": "219"}, {"size": 5191, "mtime": 1753686888552, "results": "292", "hashOfConfig": "219"}, {"size": 652, "mtime": 1753664896734, "results": "293", "hashOfConfig": "219"}, {"size": 2110, "mtime": 1752972043397, "results": "294", "hashOfConfig": "219"}, {"size": 3296, "mtime": 1753597668044, "results": "295", "hashOfConfig": "219"}, {"size": 457, "mtime": 1752998625992, "results": "296", "hashOfConfig": "219"}, {"size": 2056, "mtime": 1753662232836, "results": "297", "hashOfConfig": "219"}, {"size": 2136, "mtime": 1752968997388, "results": "298", "hashOfConfig": "219"}, {"size": 1097, "mtime": 1754542519083, "results": "299", "hashOfConfig": "219"}, {"size": 9832, "mtime": 1752999819120, "results": "300", "hashOfConfig": "219"}, {"size": 1631, "mtime": 1754542519083, "results": "301", "hashOfConfig": "219"}, {"size": 2357, "mtime": 1754542519083, "results": "302", "hashOfConfig": "219"}, {"size": 4159, "mtime": 1754542519084, "results": "303", "hashOfConfig": "219"}, {"size": 1976, "mtime": 1754542519084, "results": "304", "hashOfConfig": "219"}, {"size": 2342, "mtime": 1754025864774, "results": "305", "hashOfConfig": "219"}, {"size": 1511, "mtime": 1753594097252, "results": "306", "hashOfConfig": "219"}, {"size": 1236, "mtime": 1754542519085, "results": "307", "hashOfConfig": "219"}, {"size": 800, "mtime": 1754542519085, "results": "308", "hashOfConfig": "219"}, {"size": 8305, "mtime": 1754542519086, "results": "309", "hashOfConfig": "219"}, {"size": 1446, "mtime": 1752712464418, "results": "310", "hashOfConfig": "219"}, {"size": 3759, "mtime": 1752024533987, "results": "311", "hashOfConfig": "219"}, {"size": 1339, "mtime": 1753000764590, "results": "312", "hashOfConfig": "219"}, {"size": 12774, "mtime": 1752994552413, "results": "313", "hashOfConfig": "219"}, {"size": 967, "mtime": 1754542519086, "results": "314", "hashOfConfig": "219"}, {"size": 611, "mtime": 1754542519086, "results": "315", "hashOfConfig": "219"}, {"size": 4133, "mtime": 1753941998467, "results": "316", "hashOfConfig": "219"}, {"size": 1189, "mtime": 1752998409681, "results": "317", "hashOfConfig": "219"}, {"size": 1491, "mtime": 1752994420851, "results": "318", "hashOfConfig": "219"}, {"size": 6664, "mtime": 1754542519087, "results": "319", "hashOfConfig": "219"}, {"size": 1260, "mtime": 1754542519088, "results": "320", "hashOfConfig": "219"}, {"size": 1645, "mtime": 1754542519088, "results": "321", "hashOfConfig": "219"}, {"size": 6253, "mtime": 1754542519089, "results": "322", "hashOfConfig": "219"}, {"size": 699, "mtime": 1754542519089, "results": "323", "hashOfConfig": "219"}, {"size": 4090, "mtime": 1754542519089, "results": "324", "hashOfConfig": "219"}, {"size": 5606, "mtime": 1754335485078, "results": "325", "hashOfConfig": "219"}, {"size": 21633, "mtime": 1754542519090, "results": "326", "hashOfConfig": "219"}, {"size": 276, "mtime": 1754542519090, "results": "327", "hashOfConfig": "219"}, {"size": 1813, "mtime": 1754542519090, "results": "328", "hashOfConfig": "219"}, {"size": 1173, "mtime": 1754542519091, "results": "329", "hashOfConfig": "219"}, {"size": 1969, "mtime": 1754542519091, "results": "330", "hashOfConfig": "219"}, {"size": 2582, "mtime": 1752999564859, "results": "331", "hashOfConfig": "219"}, {"size": 3732, "mtime": 1752999587832, "results": "332", "hashOfConfig": "219"}, {"size": 778, "mtime": 1754542519092, "results": "333", "hashOfConfig": "219"}, {"size": 1891, "mtime": 1754542519092, "results": "334", "hashOfConfig": "219"}, {"size": 4459, "mtime": 1753854489718, "results": "335", "hashOfConfig": "219"}, {"size": 21638, "mtime": 1754332270735, "results": "336", "hashOfConfig": "219"}, {"size": 8062, "mtime": 1752908182083, "results": "337", "hashOfConfig": "219"}, {"size": 1810, "mtime": 1752973774511, "results": "338", "hashOfConfig": "219"}, {"size": 9145, "mtime": 1753672623962, "results": "339", "hashOfConfig": "219"}, {"size": 6777, "mtime": 1754362364766, "results": "340", "hashOfConfig": "219"}, {"size": 3596, "mtime": 1752998402592, "results": "341", "hashOfConfig": "219"}, {"size": 565, "mtime": 1754542519092, "results": "342", "hashOfConfig": "219"}, {"size": 3836, "mtime": 1752972841718, "results": "343", "hashOfConfig": "219"}, {"size": 1451, "mtime": 1753661383143, "results": "344", "hashOfConfig": "219"}, {"size": 2522, "mtime": 1753846474177, "results": "345", "hashOfConfig": "219"}, {"size": 2422, "mtime": 1752973002784, "results": "346", "hashOfConfig": "219"}, {"size": 2489, "mtime": 1753672396206, "results": "347", "hashOfConfig": "219"}, {"size": 9688, "mtime": 1752470232696, "results": "348", "hashOfConfig": "219"}, {"size": 3441, "mtime": 1752460198072, "results": "349", "hashOfConfig": "219"}, {"size": 6817, "mtime": 1754542519093, "results": "350", "hashOfConfig": "219"}, {"size": 5052, "mtime": 1754542519093, "results": "351", "hashOfConfig": "219"}, {"size": 4246, "mtime": 1753672708155, "results": "352", "hashOfConfig": "219"}, {"size": 1636, "mtime": 1753578806610, "results": "353", "hashOfConfig": "219"}, {"size": 1473, "mtime": 1754371361164, "results": "354", "hashOfConfig": "219"}, {"size": 1389, "mtime": 1752026410415, "results": "355", "hashOfConfig": "219"}, {"size": 11277, "mtime": 1752908709717, "results": "356", "hashOfConfig": "219"}, {"size": 11316, "mtime": 1752998830455, "results": "357", "hashOfConfig": "219"}, {"size": 7038, "mtime": 1754365015507, "results": "358", "hashOfConfig": "219"}, {"size": 1928, "mtime": 1753158560369, "results": "359", "hashOfConfig": "219"}, {"size": 2770, "mtime": 1753664857036, "results": "360", "hashOfConfig": "219"}, {"size": 7620, "mtime": 1753158547289, "results": "361", "hashOfConfig": "219"}, {"size": 3088, "mtime": 1752468935955, "results": "362", "hashOfConfig": "219"}, {"size": 5556, "mtime": 1752969250904, "results": "363", "hashOfConfig": "219"}, {"size": 6661, "mtime": 1754542519094, "results": "364", "hashOfConfig": "219"}, {"size": 166, "mtime": 1754542519094, "results": "365", "hashOfConfig": "219"}, {"size": 435, "mtime": 1753663942644, "results": "366", "hashOfConfig": "219"}, {"size": 1659, "mtime": 1753054451476, "results": "367", "hashOfConfig": "219"}, {"size": 6053, "mtime": 1754363021042, "results": "368", "hashOfConfig": "219"}, {"size": 12521, "mtime": 1753686854521, "results": "369", "hashOfConfig": "219"}, {"size": 2206, "mtime": 1754546391957, "results": "370", "hashOfConfig": "219"}, {"size": 2250, "mtime": 1752908751521, "results": "371", "hashOfConfig": "219"}, {"size": 1498, "mtime": 1752908458736, "results": "372", "hashOfConfig": "219"}, {"size": 3282, "mtime": 1753672276773, "results": "373", "hashOfConfig": "219"}, {"size": 873, "mtime": 1753851542088, "results": "374", "hashOfConfig": "219"}, {"size": 1866, "mtime": 1753851520182, "results": "375", "hashOfConfig": "219"}, {"size": 1397, "mtime": 1753686764431, "results": "376", "hashOfConfig": "219"}, {"size": 4443, "mtime": 1753673483383, "results": "377", "hashOfConfig": "219"}, {"size": 5440, "mtime": 1753851497146, "results": "378", "hashOfConfig": "219"}, {"size": 3641, "mtime": 1753673769790, "results": "379", "hashOfConfig": "219"}, {"size": 234, "mtime": 1753675616016, "results": "380", "hashOfConfig": "219"}, {"size": 744, "mtime": 1753676382520, "results": "381", "hashOfConfig": "219"}, {"size": 3545, "mtime": 1753676234917, "results": "382", "hashOfConfig": "219"}, {"size": 2270, "mtime": 1753922304275, "results": "383", "hashOfConfig": "219"}, {"size": 2859, "mtime": 1753676221357, "results": "384", "hashOfConfig": "219"}, {"size": 3415, "mtime": 1754546594059, "results": "385", "hashOfConfig": "219"}, {"size": 18112, "mtime": 1754546662217, "results": "386", "hashOfConfig": "219"}, {"size": 7426, "mtime": 1753683916153, "results": "387", "hashOfConfig": "219"}, {"size": 2531, "mtime": 1754542519091, "results": "388", "hashOfConfig": "219"}, {"size": 2974, "mtime": 1753676418523, "results": "389", "hashOfConfig": "219"}, {"size": 4837, "mtime": 1754547224850, "results": "390", "hashOfConfig": "219"}, {"size": 889, "mtime": 1753846474177, "results": "391", "hashOfConfig": "219"}, {"size": 915, "mtime": 1753842497889, "results": "392", "hashOfConfig": "219"}, {"size": 5415, "mtime": 1753686818939, "results": "393", "hashOfConfig": "219"}, {"size": 9551, "mtime": 1753760500434, "results": "394", "hashOfConfig": "219"}, {"size": 9160, "mtime": 1753853976551, "results": "395", "hashOfConfig": "219"}, {"size": 1679, "mtime": 1753851587474, "results": "396", "hashOfConfig": "219"}, {"size": 5641, "mtime": 1753857588035, "results": "397", "hashOfConfig": "219"}, {"size": 2941, "mtime": 1753852097456, "results": "398", "hashOfConfig": "219"}, {"size": 394, "mtime": 1753857600394, "results": "399", "hashOfConfig": "219"}, {"size": 2360, "mtime": 1754333369468, "results": "400", "hashOfConfig": "219"}, {"size": 1491, "mtime": 1754335159581, "results": "401", "hashOfConfig": "219"}, {"size": 125, "mtime": 1753854785290, "results": "402", "hashOfConfig": "219"}, {"size": 4384, "mtime": 1754546785527, "results": "403", "hashOfConfig": "219"}, {"size": 20888, "mtime": 1754337029263, "results": "404", "hashOfConfig": "219"}, {"size": 420, "mtime": 1753851797609, "results": "405", "hashOfConfig": "219"}, {"size": 5476, "mtime": 1754337035197, "results": "406", "hashOfConfig": "219"}, {"size": 3691, "mtime": 1754332398090, "results": "407", "hashOfConfig": "219"}, {"size": 2628, "mtime": 1754336280674, "results": "408", "hashOfConfig": "219"}, {"size": 7465, "mtime": 1754546203808, "results": "409", "hashOfConfig": "219"}, {"size": 4126, "mtime": 1754332703906, "results": "410", "hashOfConfig": "219"}, {"size": 4629, "mtime": 1754545724166, "results": "411", "hashOfConfig": "219"}, {"size": 326, "mtime": 1754336821666, "results": "412", "hashOfConfig": "219"}, {"size": 11652, "mtime": 1754353733812, "results": "413", "hashOfConfig": "219"}, {"size": 4757, "mtime": 1754329229301, "results": "414", "hashOfConfig": "219"}, {"size": 2313, "mtime": 1754542519087, "results": "415", "hashOfConfig": "219"}, {"size": 12304, "mtime": 1754335563354, "results": "416", "hashOfConfig": "219"}, {"size": 8315, "mtime": 1754545843229, "results": "417", "hashOfConfig": "219"}, {"size": 4613, "mtime": 1754354783938, "results": "418", "hashOfConfig": "219"}, {"size": 2851, "mtime": 1754547070331, "results": "419", "hashOfConfig": "219"}, {"size": 13616, "mtime": 1754547090832, "results": "420", "hashOfConfig": "219"}, {"size": 903, "mtime": 1754545599714, "results": "421", "hashOfConfig": "219"}, {"size": 6645, "mtime": 1754547219115, "results": "422", "hashOfConfig": "219"}, {"size": 14027, "mtime": 1754365041327, "results": "423", "hashOfConfig": "219"}, {"size": 12408, "mtime": 1754362955568, "results": "424", "hashOfConfig": "219"}, {"size": 14540, "mtime": 1754547112847, "results": "425", "hashOfConfig": "219"}, {"size": 10336, "mtime": 1754542838480, "results": "426", "hashOfConfig": "219"}, {"size": 7036, "mtime": 1754543247967, "results": "427", "hashOfConfig": "219"}, {"size": 4721, "mtime": 1754547123786, "results": "428", "hashOfConfig": "219"}, {"size": 5595, "mtime": 1754547164465, "results": "429", "hashOfConfig": "219"}, {"size": 7839, "mtime": 1754547189105, "results": "430", "hashOfConfig": "219"}, {"size": 8290, "mtime": 1754547212096, "results": "431", "hashOfConfig": "219"}, {"size": 8219, "mtime": 1754544167953, "results": "432", "hashOfConfig": "219"}, {"size": 6543, "mtime": 1754547059422, "results": "433", "hashOfConfig": "219"}, {"size": 9858, "mtime": 1754547246480, "results": "434", "hashOfConfig": "219"}, {"size": 451, "mtime": 1754547088427, "results": "435", "hashOfConfig": "219"}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2sh7hf", {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "910", "messages": "911", "suppressedMessages": "912", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "913", "messages": "914", "suppressedMessages": "915", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "916", "messages": "917", "suppressedMessages": "918", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "919", "messages": "920", "suppressedMessages": "921", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "922", "messages": "923", "suppressedMessages": "924", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "925", "messages": "926", "suppressedMessages": "927", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "928", "messages": "929", "suppressedMessages": "930", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "931", "messages": "932", "suppressedMessages": "933", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "934", "messages": "935", "suppressedMessages": "936", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "937", "messages": "938", "suppressedMessages": "939", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "940", "messages": "941", "suppressedMessages": "942", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "943", "messages": "944", "suppressedMessages": "945", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "946", "messages": "947", "suppressedMessages": "948", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "949", "messages": "950", "suppressedMessages": "951", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "952", "messages": "953", "suppressedMessages": "954", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "955", "messages": "956", "suppressedMessages": "957", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "958", "messages": "959", "suppressedMessages": "960", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "961", "messages": "962", "suppressedMessages": "963", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "964", "messages": "965", "suppressedMessages": "966", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "967", "messages": "968", "suppressedMessages": "969", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "970", "messages": "971", "suppressedMessages": "972", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "973", "messages": "974", "suppressedMessages": "975", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "976", "messages": "977", "suppressedMessages": "978", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "979", "messages": "980", "suppressedMessages": "981", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "982", "messages": "983", "suppressedMessages": "984", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "985", "messages": "986", "suppressedMessages": "987", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "988", "messages": "989", "suppressedMessages": "990", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "991", "messages": "992", "suppressedMessages": "993", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "994", "messages": "995", "suppressedMessages": "996", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "997", "messages": "998", "suppressedMessages": "999", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1000", "messages": "1001", "suppressedMessages": "1002", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1003", "messages": "1004", "suppressedMessages": "1005", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1006", "messages": "1007", "suppressedMessages": "1008", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1009", "messages": "1010", "suppressedMessages": "1011", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1012", "messages": "1013", "suppressedMessages": "1014", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1015", "messages": "1016", "suppressedMessages": "1017", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1018", "messages": "1019", "suppressedMessages": "1020", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1021", "messages": "1022", "suppressedMessages": "1023", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1024", "messages": "1025", "suppressedMessages": "1026", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1027", "messages": "1028", "suppressedMessages": "1029", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1030", "messages": "1031", "suppressedMessages": "1032", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1033", "messages": "1034", "suppressedMessages": "1035", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1036", "messages": "1037", "suppressedMessages": "1038", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1039", "messages": "1040", "suppressedMessages": "1041", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1042", "messages": "1043", "suppressedMessages": "1044", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1045", "messages": "1046", "suppressedMessages": "1047", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1048", "messages": "1049", "suppressedMessages": "1050", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1051", "messages": "1052", "suppressedMessages": "1053", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1054", "messages": "1055", "suppressedMessages": "1056", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1057", "messages": "1058", "suppressedMessages": "1059", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1060", "messages": "1061", "suppressedMessages": "1062", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1063", "messages": "1064", "suppressedMessages": "1065", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1066", "messages": "1067", "suppressedMessages": "1068", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1069", "messages": "1070", "suppressedMessages": "1071", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1072", "messages": "1073", "suppressedMessages": "1074", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1075", "messages": "1076", "suppressedMessages": "1077", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1078", "messages": "1079", "suppressedMessages": "1080", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1081", "messages": "1082", "suppressedMessages": "1083", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1084", "messages": "1085", "suppressedMessages": "1086", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Data/new era/siift-next/src/app/admin/activity/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/calls/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/token-trends/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/agent/usage-stats/page.tsx", ["1087", "1088", "1089", "1090", "1091", "1092"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-metrics/page.tsx", ["1093", "1094"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/activity-trends/page.tsx", ["1095", "1096", "1097", "1098", "1099", "1100", "1101"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/feedbacks/page.tsx", ["1102", "1103", "1104", "1105", "1106", "1107", "1108", "1109", "1110"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/summary/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/analytics/users/page.tsx", ["1111", "1112", "1113", "1114", "1115", "1116", "1117"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/api-test/page.tsx", ["1118", "1119", "1120", "1121", "1122", "1123", "1124", "1125", "1126"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/health/page.tsx", ["1127", "1128", "1129", "1130"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/notifications/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/profile/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/all/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/create/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/projects/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/recent/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings/page.tsx", ["1131"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/settings-tab/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/system/health/page.tsx", ["1132", "1133", "1134", "1135", "1136"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/trends/page.tsx", ["1137", "1138", "1139"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/users/page.tsx", ["1140", "1141"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/me/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/auth/signin/route.ts", ["1142"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/health/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/[id]/route.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/projects/route.ts", ["1143"], [], "/Users/<USER>/Data/new era/siift-next/src/app/api/webhooks/clerk/route.ts", ["1144", "1145"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/forgot-password/page.tsx", ["1146"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/page.tsx", ["1147", "1148", "1149", "1150"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/login/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/page.tsx", ["1151", "1152", "1153", "1154"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/register/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/reset-password/page.tsx", ["1155", "1156"], [], "/Users/<USER>/Data/new era/siift-next/src/app/auth/verify-email/page.tsx", ["1157", "1158"], [], "/Users/<USER>/Data/new era/siift-next/src/app/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/profile/page.tsx", ["1159", "1160", "1161", "1162", "1163"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/edit/page.tsx", ["1164", "1165", "1166", "1167"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/[id]/page.tsx", ["1168", "1169"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/create/page.tsx", ["1170"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/new/page.tsx", ["1171"], [], "/Users/<USER>/Data/new era/siift-next/src/app/projects/page.tsx", ["1172", "1173"], [], "/Users/<USER>/Data/new era/siift-next/src/app/settings/page.tsx", ["1174", "1175", "1176", "1177", "1178"], [], "/Users/<USER>/Data/new era/siift-next/src/app/sso-callback/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/user-dashboard/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/admin-sidebar.tsx", ["1179", "1180", "1181"], [], "/Users/<USER>/Data/new era/siift-next/src/components/admin-tabbed-content.tsx", ["1182", "1183", "1184", "1185", "1186", "1187"], [], "/Users/<USER>/Data/new era/siift-next/src/components/auth/admin-route.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/auth/auth-card.tsx", ["1188"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGrid.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/DashboardHeader.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-hero-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-projects-section.tsx", ["1189", "1190", "1191"], [], "/Users/<USER>/Data/new era/siift-next/src/components/dashboard/dashboard-stats-cards.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/feature-grid.tsx", ["1192"], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/hero-section.tsx", ["1193"], [], "/Users/<USER>/Data/new era/siift-next/src/components/landing/landing-page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/admin-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/dashboard-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/footer.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/header.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/layout/main-layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/nav-main.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/nav-user.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/main-nav.tsx", ["1194"], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/mobile-nav.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/navigation/user-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-chat-sidebar.tsx", ["1195", "1196", "1197", "1198", "1199", "1200", "1201", "1202", "1203", "1204", "1205", "1206", "1207", "1208", "1209", "1210", "1211", "1212", "1213", "1214", "1215"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationAnimation.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-sidebar.tsx", ["1216"], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/ClerkSessionProvider.tsx", ["1217", "1218", "1219", "1220", "1221"], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/QueryProvider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/team-switcher.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/theme-provider.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/theme-toggle.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/app-loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/avatar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/background-beams.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/badge.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/breadcrumb.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/button.tsx", ["1222"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/card.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-bubble.tsx", ["1223", "1224"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/chat-message-list.tsx", ["1225", "1226"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/collapsible.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/dropdown-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/form-input.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/form.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/glow.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/icons.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/input.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/label.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/logo.tsx", ["1227"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/message-loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/mockup.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/navigation-menu.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/progress.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/scroll-area.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/select.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/separator.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sheet.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar-button.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sidebar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/skeleton.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/sonner.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/switch.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/tabs.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials-columns-1.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/testimonials.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/textarea.tsx", ["1228"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/tooltip.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/waitlist-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/user-tabbed-content.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/contexts/auth-context.tsx", ["1229", "1230", "1231"], [], "/Users/<USER>/Data/new era/siift-next/src/contexts/background-context.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/mutations/useUserMutations.ts", ["1232", "1233", "1234", "1235", "1236", "1237", "1238"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/queries/useUser.ts", ["1239", "1240"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/use-auto-scroll.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/use-mobile.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkAuth.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useResizable.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useToast.ts", ["1241"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useUserSync.ts", ["1242", "1243", "1244"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/admin-api.ts", ["1245", "1246", "1247", "1248"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/api.ts", ["1249", "1250"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/apiClient.ts", ["1251", "1252", "1253", "1254", "1255", "1256", "1257", "1258", "1259", "1260", "1261", "1262", "1263", "1264", "1265", "1266", "1267"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsData.ts", ["1268"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/clerk-api.ts", ["1269", "1270", "1271"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/constants.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/fonts.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/jwt.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mock-auth-api.ts", ["1272", "1273", "1274"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mock-email-service.ts", ["1275"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/mockEventStream.ts", ["1276", "1277", "1278"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/projectCreationConfig.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/queryClient.ts", ["1279", "1280", "1281", "1282"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/realEventStream.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/session.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/tokenStorage.ts", ["1283", "1284"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/types.ts", ["1285", "1286"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/utils.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/middleware.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessSectionStore.ts", ["1287", "1288", "1289"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/projectCreationStore.ts", ["1290", "1291", "1292"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/sessionStore.ts", ["1293", "1294"], [], "/Users/<USER>/Data/new era/siift-next/src/types/BusinessSection.types.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/Session.types.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/email.types.ts", ["1295"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkApi.ts", ["1296", "1297", "1298"], [], "/Users/<USER>/Data/new era/siift-next/src/app/robots.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/sitemap.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/analytics/PostHogProvider.tsx", ["1299"], [], "/Users/<USER>/Data/new era/siift-next/src/components/seo/SEOHead.tsx", ["1300"], [], "/Users/<USER>/Data/new era/siift-next/src/components/seo/StructuredData.tsx", ["1301", "1302"], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useAnalytics.ts", ["1303", "1304", "1305", "1306", "1307"], [], "/Users/<USER>/Data/new era/siift-next/src/app/admin/layout.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/debug/tokens/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/error.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/loading.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/not-found.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/EditableCell.tsx", ["1308"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-item-table.tsx", ["1309", "1310", "1311", "1312"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenDebug.tsx", ["1313", "1314"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/table.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useClerkToken.ts", ["1315"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStore.ts", ["1316"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/resize-handle.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/debug/page.tsx", ["1317", "1318", "1319", "1320", "1321", "1322", "1323", "1324"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/PostHogDebug.tsx", ["1325", "1326", "1327", "1328", "1329"], [], "/Users/<USER>/Data/new era/siift-next/src/components/debug/ClerkTokenExample.tsx", ["1330", "1331", "1332", "1333", "1334", "1335"], [], "/Users/<USER>/Data/new era/siift-next/src/app/about/page.tsx", ["1336", "1337", "1338", "1339"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/not-found.tsx", ["1340", "1341"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/[slug]/page.tsx", ["1342"], [], "/Users/<USER>/Data/new era/siift-next/src/app/blog/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/app/test-ai-input/page.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-card.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/blog/blog-section.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/ai-prompt-demo.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/animated-ai-input.tsx", ["1343", "1344"], [], "/Users/<USER>/Data/new era/siift-next/src/data/blog-posts.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/types/blog.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ContentSections.tsx", ["1345", "1346"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/PrioritiesDropdown.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProgressBar.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectDetailHeader.tsx", ["1347"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectHeader.tsx", ["1348", "1349", "1350"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/ProjectMainContent.tsx", ["1351", "1352", "1353", "1354", "1355", "1356"], [], "/Users/<USER>/Data/new era/siift-next/src/components/project/index.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/shared/ProjectInputSection.tsx", ["1357"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/dotted-background.tsx", ["1358"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/popover.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/design-tokens.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/mockdata/businessItemQuestions.ts", ["1359", "1360", "1361"], [], "/Users/<USER>/Data/new era/siift-next/src/app/stepper-demo/page.tsx", ["1362", "1363", "1364"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-category-cards.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-item-details-enhanced.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-item-hybrid-view.tsx", ["1365", "1366"], [], "/Users/<USER>/Data/new era/siift-next/src/components/business-sections/BusinessSectionsGridEnhanced.tsx", [], [], "/Users/<USER>/Data/new era/siift-next/src/components/project-creation/ProjectCreationQuestionnaire.tsx", ["1367", "1368", "1369", "1370", "1371"], [], "/Users/<USER>/Data/new era/siift-next/src/components/ui/stepper.tsx", ["1372", "1373", "1374", "1375", "1376", "1377", "1378", "1379"], [], "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsData.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/data/businessItemsDataExtended.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/hooks/useBusinessSections.ts", ["1380", "1381", "1382", "1383"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/businessSectionsDataNew.ts", ["1384", "1385"], [], "/Users/<USER>/Data/new era/siift-next/src/lib/cardOpacityUtils.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/dependencyManager.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/lib/statusCountingSystem.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/services/browserStorageService.ts", [], [], "/Users/<USER>/Data/new era/siift-next/src/services/businessSectionsApi.ts", ["1386", "1387"], [], "/Users/<USER>/Data/new era/siift-next/src/stores/businessItemStoreEnhanced.ts", ["1388", "1389", "1390"], [], "/Users/<USER>/Data/new era/siift-next/src/types/BusinessItemData.types.ts", [], [], {"ruleId": "1391", "severity": 1, "message": "1392", "line": 35, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 35, "endColumn": 39, "suggestions": "1395"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 58, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 58, "endColumn": 22, "suggestions": "1396"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 267, "column": 46, "nodeType": "1393", "messageId": "1394", "endLine": 267, "endColumn": 49, "suggestions": "1397"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 293, "column": 42, "nodeType": "1393", "messageId": "1394", "endLine": 293, "endColumn": 45, "suggestions": "1398"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 310, "column": 46, "nodeType": "1393", "messageId": "1394", "endLine": 310, "endColumn": 49, "suggestions": "1399"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 335, "column": 44, "nodeType": "1393", "messageId": "1394", "endLine": 335, "endColumn": 47, "suggestions": "1400"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 24, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 24, "endColumn": 39, "suggestions": "1401"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 47, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 47, "endColumn": 22, "suggestions": "1402"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 34, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 34, "endColumn": 39, "suggestions": "1403"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 57, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 57, "endColumn": 22, "suggestions": "1404"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 116, "column": 28, "nodeType": "1393", "messageId": "1394", "endLine": 116, "endColumn": 31, "suggestions": "1405"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 207, "column": 44, "nodeType": "1393", "messageId": "1394", "endLine": 207, "endColumn": 47, "suggestions": "1406"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 246, "column": 46, "nodeType": "1393", "messageId": "1394", "endLine": 246, "endColumn": 49, "suggestions": "1407"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 270, "column": 46, "nodeType": "1393", "messageId": "1394", "endLine": 270, "endColumn": 49, "suggestions": "1408"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 309, "column": 37, "nodeType": "1393", "messageId": "1394", "endLine": 309, "endColumn": 40, "suggestions": "1409"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 39, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 39, "endColumn": 39, "suggestions": "1410"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 70, "column": 27, "nodeType": "1393", "messageId": "1394", "endLine": 70, "endColumn": 30, "suggestions": "1411"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 90, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 90, "endColumn": 22, "suggestions": "1412"}, {"ruleId": "1413", "severity": 1, "message": "1414", "line": 99, "column": 6, "nodeType": "1415", "endLine": 99, "endColumn": 15, "suggestions": "1416"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 112, "column": 24, "nodeType": "1393", "messageId": "1394", "endLine": 112, "endColumn": 27, "suggestions": "1417"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 199, "column": 50, "nodeType": "1393", "messageId": "1394", "endLine": 199, "endColumn": 53, "suggestions": "1418"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 215, "column": 55, "nodeType": "1393", "messageId": "1394", "endLine": 215, "endColumn": 58, "suggestions": "1419"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 234, "column": 48, "nodeType": "1393", "messageId": "1394", "endLine": 234, "endColumn": 51, "suggestions": "1420"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 414, "column": 52, "nodeType": "1393", "messageId": "1394", "endLine": 414, "endColumn": 55, "suggestions": "1421"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 32, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 32, "endColumn": 39, "suggestions": "1422"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 59, "column": 27, "nodeType": "1393", "messageId": "1394", "endLine": 59, "endColumn": 30, "suggestions": "1423"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 78, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 78, "endColumn": 22, "suggestions": "1424"}, {"ruleId": "1413", "severity": 1, "message": "1414", "line": 87, "column": 6, "nodeType": "1415", "endLine": 87, "endColumn": 15, "suggestions": "1425"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 247, "column": 42, "nodeType": "1393", "messageId": "1394", "endLine": 247, "endColumn": 45, "suggestions": "1426"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 299, "column": 33, "nodeType": "1429", "messageId": "1430", "suggestions": "1431"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 299, "column": 56, "nodeType": "1429", "messageId": "1430", "suggestions": "1432"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 45, "column": 10, "nodeType": "1393", "messageId": "1394", "endLine": 45, "endColumn": 13, "suggestions": "1433"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 179, "column": 23, "nodeType": "1393", "messageId": "1394", "endLine": 179, "endColumn": 26, "suggestions": "1434"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 266, "column": 41, "nodeType": "1393", "messageId": "1394", "endLine": 266, "endColumn": 44, "suggestions": "1435"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 317, "column": 38, "nodeType": "1393", "messageId": "1394", "endLine": 317, "endColumn": 41, "suggestions": "1436"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 323, "column": 45, "nodeType": "1393", "messageId": "1394", "endLine": 323, "endColumn": 48, "suggestions": "1437"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 365, "column": 40, "nodeType": "1393", "messageId": "1394", "endLine": 365, "endColumn": 43, "suggestions": "1438"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 424, "column": 40, "nodeType": "1393", "messageId": "1394", "endLine": 424, "endColumn": 43, "suggestions": "1439"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 495, "column": 35, "nodeType": "1393", "messageId": "1394", "endLine": 495, "endColumn": 38, "suggestions": "1440"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 500, "column": 43, "nodeType": "1393", "messageId": "1394", "endLine": 500, "endColumn": 46, "suggestions": "1441"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 61, "column": 28, "nodeType": "1393", "messageId": "1394", "endLine": 61, "endColumn": 31, "suggestions": "1442"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 71, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 71, "endColumn": 22, "suggestions": "1443"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 140, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 140, "endColumn": 22, "suggestions": "1444"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 154, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 154, "endColumn": 22, "suggestions": "1445"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 58, "column": 50, "nodeType": "1393", "messageId": "1394", "endLine": 58, "endColumn": 53, "suggestions": "1446"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 26, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 26, "endColumn": 39, "suggestions": "1447"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 49, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 49, "endColumn": 22, "suggestions": "1448"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 107, "column": 61, "nodeType": "1393", "messageId": "1394", "endLine": 107, "endColumn": 64, "suggestions": "1449"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 112, "column": 63, "nodeType": "1393", "messageId": "1394", "endLine": 112, "endColumn": 66, "suggestions": "1450"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 317, "column": 104, "nodeType": "1393", "messageId": "1394", "endLine": 317, "endColumn": 107, "suggestions": "1451"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 142, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 142, "endColumn": 24, "suggestions": "1452"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 199, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 199, "endColumn": 39, "suggestions": "1453"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 351, "column": 42, "nodeType": "1393", "messageId": "1394", "endLine": 351, "endColumn": 45, "suggestions": "1454"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 74, "column": 30, "nodeType": "1393", "messageId": "1394", "endLine": 74, "endColumn": 33, "suggestions": "1455"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 84, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 84, "endColumn": 24, "suggestions": "1456"}, {"ruleId": "1457", "severity": 1, "message": "1458", "line": 59, "column": 23, "nodeType": null, "messageId": "1459", "endLine": 59, "endColumn": 24}, {"ruleId": "1457", "severity": 1, "message": "1460", "line": 52, "column": 27, "nodeType": null, "messageId": "1459", "endLine": 52, "endColumn": 34}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 32, "column": 12, "nodeType": "1393", "messageId": "1394", "endLine": 32, "endColumn": 15, "suggestions": "1461"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 40, "column": 11, "nodeType": "1393", "messageId": "1394", "endLine": 40, "endColumn": 14, "suggestions": "1462"}, {"ruleId": "1457", "severity": 1, "message": "1463", "line": 36, "column": 5, "nodeType": null, "messageId": "1459", "endLine": 36, "endColumn": 14}, {"ruleId": "1457", "severity": 1, "message": "1464", "line": 8, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 8, "endColumn": 19}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 75, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 75, "endColumn": 22, "suggestions": "1465"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 123, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 123, "endColumn": 22, "suggestions": "1466"}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 159, "column": 16, "nodeType": "1429", "messageId": "1430", "suggestions": "1468"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 74, "column": 29, "nodeType": "1393", "messageId": "1394", "endLine": 74, "endColumn": 32, "suggestions": "1469"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 91, "column": 29, "nodeType": "1393", "messageId": "1394", "endLine": 91, "endColumn": 32, "suggestions": "1470"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 124, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 124, "endColumn": 22, "suggestions": "1471"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 191, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 191, "endColumn": 22, "suggestions": "1472"}, {"ruleId": "1457", "severity": 1, "message": "1473", "line": 44, "column": 19, "nodeType": null, "messageId": "1459", "endLine": 44, "endColumn": 29}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 174, "column": 17, "nodeType": "1429", "messageId": "1430", "suggestions": "1474"}, {"ruleId": "1457", "severity": 1, "message": "1475", "line": 50, "column": 5, "nodeType": null, "messageId": "1459", "endLine": 50, "endColumn": 26}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 175, "column": 17, "nodeType": "1429", "messageId": "1430", "suggestions": "1476"}, {"ruleId": "1457", "severity": 1, "message": "1477", "line": 50, "column": 17, "nodeType": null, "messageId": "1459", "endLine": 50, "endColumn": 28}, {"ruleId": "1457", "severity": 1, "message": "1478", "line": 50, "column": 41, "nodeType": null, "messageId": "1459", "endLine": 50, "endColumn": 54}, {"ruleId": "1457", "severity": 1, "message": "1479", "line": 70, "column": 5, "nodeType": null, "messageId": "1459", "endLine": 70, "endColumn": 13}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 111, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 111, "endColumn": 24, "suggestions": "1480"}, {"ruleId": "1457", "severity": 1, "message": "1481", "line": 138, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 138, "endColumn": 17}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 98, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 98, "endColumn": 24, "suggestions": "1482"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 117, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 117, "endColumn": 22, "suggestions": "1483"}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 162, "column": 22, "nodeType": "1429", "messageId": "1430", "suggestions": "1484"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 243, "column": 73, "nodeType": "1393", "messageId": "1394", "endLine": 243, "endColumn": 76, "suggestions": "1485"}, {"ruleId": "1413", "severity": 1, "message": "1486", "line": 90, "column": 6, "nodeType": "1415", "endLine": 90, "endColumn": 41, "suggestions": "1487"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 93, "column": 48, "nodeType": "1393", "messageId": "1394", "endLine": 93, "endColumn": 51, "suggestions": "1488"}, {"ruleId": "1457", "severity": 1, "message": "1489", "line": 10, "column": 24, "nodeType": null, "messageId": "1459", "endLine": 10, "endColumn": 29}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 52, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 52, "endColumn": 22, "suggestions": "1490"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 50, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 50, "endColumn": 24, "suggestions": "1491"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 88, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 88, "endColumn": 22, "suggestions": "1492"}, {"ruleId": "1457", "severity": 1, "message": "1493", "line": 64, "column": 12, "nodeType": null, "messageId": "1459", "endLine": 64, "endColumn": 17}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 123, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 123, "endColumn": 24, "suggestions": "1494"}, {"ruleId": "1457", "severity": 1, "message": "1495", "line": 133, "column": 35, "nodeType": null, "messageId": "1459", "endLine": 133, "endColumn": 40}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 143, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 143, "endColumn": 24, "suggestions": "1496"}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 234, "column": 51, "nodeType": "1429", "messageId": "1430", "suggestions": "1497"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 37, "column": 32, "nodeType": "1393", "messageId": "1394", "endLine": 37, "endColumn": 35, "suggestions": "1498"}, {"ruleId": "1457", "severity": 1, "message": "1499", "line": 256, "column": 17, "nodeType": null, "messageId": "1459", "endLine": 256, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1500", "line": 257, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 257, "endColumn": 15}, {"ruleId": "1457", "severity": 1, "message": "1501", "line": 4, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 4, "endColumn": 14}, {"ruleId": "1457", "severity": 1, "message": "1502", "line": 4, "column": 16, "nodeType": null, "messageId": "1459", "endLine": 4, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1503", "line": 4, "column": 29, "nodeType": null, "messageId": "1459", "endLine": 4, "endColumn": 37}, {"ruleId": "1457", "severity": 1, "message": "1504", "line": 4, "column": 39, "nodeType": null, "messageId": "1459", "endLine": 4, "endColumn": 50}, {"ruleId": "1457", "severity": 1, "message": "1505", "line": 17, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 17, "endColumn": 7}, {"ruleId": "1457", "severity": 1, "message": "1506", "line": 19, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 19, "endColumn": 11}, {"ruleId": "1457", "severity": 1, "message": "1507", "line": 10, "column": 8, "nodeType": null, "messageId": "1459", "endLine": 10, "endColumn": 12}, {"ruleId": "1457", "severity": 1, "message": "1508", "line": 6, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 6, "endColumn": 14}, {"ruleId": "1457", "severity": 1, "message": "1507", "line": 7, "column": 8, "nodeType": null, "messageId": "1459", "endLine": 7, "endColumn": 12}, {"ruleId": "1457", "severity": 1, "message": "1509", "line": 79, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 79, "endColumn": 25}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 91, "column": 87, "nodeType": "1429", "messageId": "1430", "suggestions": "1510"}, {"ruleId": "1413", "severity": 1, "message": "1511", "line": 35, "column": 9, "nodeType": "1512", "endLine": 40, "endColumn": 4}, {"ruleId": "1457", "severity": 1, "message": "1513", "line": 11, "column": 11, "nodeType": null, "messageId": "1459", "endLine": 11, "endColumn": 15}, {"ruleId": "1457", "severity": 1, "message": "1514", "line": 3, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 3, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1515", "line": 5, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 5, "endColumn": 10}, {"ruleId": "1457", "severity": 1, "message": "1516", "line": 6, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 6, "endColumn": 17}, {"ruleId": "1457", "severity": 1, "message": "1517", "line": 7, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 7, "endColumn": 17}, {"ruleId": "1457", "severity": 1, "message": "1518", "line": 9, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 9, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1519", "line": 9, "column": 25, "nodeType": null, "messageId": "1459", "endLine": 9, "endColumn": 33}, {"ruleId": "1457", "severity": 1, "message": "1520", "line": 9, "column": 35, "nodeType": null, "messageId": "1459", "endLine": 9, "endColumn": 43}, {"ruleId": "1457", "severity": 1, "message": "1521", "line": 18, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 18, "endColumn": 20}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 30, "column": 26, "nodeType": "1393", "messageId": "1394", "endLine": 30, "endColumn": 29, "suggestions": "1522"}, {"ruleId": "1457", "severity": 1, "message": "1523", "line": 102, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 102, "endColumn": 12}, {"ruleId": "1457", "severity": 1, "message": "1524", "line": 108, "column": 20, "nodeType": null, "messageId": "1459", "endLine": 108, "endColumn": 43}, {"ruleId": "1457", "severity": 1, "message": "1525", "line": 109, "column": 23, "nodeType": null, "messageId": "1459", "endLine": 109, "endColumn": 49}, {"ruleId": "1457", "severity": 1, "message": "1526", "line": 112, "column": 20, "nodeType": null, "messageId": "1459", "endLine": 112, "endColumn": 31}, {"ruleId": "1457", "severity": 1, "message": "1527", "line": 114, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 114, "endColumn": 19}, {"ruleId": "1457", "severity": 1, "message": "1528", "line": 114, "column": 21, "nodeType": null, "messageId": "1459", "endLine": 114, "endColumn": 33}, {"ruleId": "1457", "severity": 1, "message": "1529", "line": 116, "column": 11, "nodeType": null, "messageId": "1459", "endLine": 116, "endColumn": 18}, {"ruleId": "1457", "severity": 1, "message": "1530", "line": 119, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 119, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1531", "line": 123, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 123, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1532", "line": 126, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 126, "endColumn": 24}, {"ruleId": "1457", "severity": 1, "message": "1533", "line": 127, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 127, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1534", "line": 137, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 137, "endColumn": 26}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 41, "column": 26, "nodeType": "1393", "messageId": "1394", "endLine": 41, "endColumn": 29, "suggestions": "1535"}, {"ruleId": "1457", "severity": 1, "message": "1536", "line": 7, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 7, "endColumn": 21}, {"ruleId": "1457", "severity": 1, "message": "1537", "line": 18, "column": 25, "nodeType": null, "messageId": "1459", "endLine": 18, "endColumn": 42}, {"ruleId": "1457", "severity": 1, "message": "1500", "line": 19, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 19, "endColumn": 15}, {"ruleId": "1457", "severity": 1, "message": "1538", "line": 46, "column": 24, "nodeType": null, "messageId": "1459", "endLine": 46, "endColumn": 25}, {"ruleId": "1413", "severity": 1, "message": "1539", "line": 118, "column": 6, "nodeType": "1415", "endLine": 118, "endColumn": 72, "suggestions": "1540"}, {"ruleId": "1457", "severity": 1, "message": "1521", "line": 6, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 6, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1541", "line": 18, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 18, "endColumn": 9}, {"ruleId": "1457", "severity": 1, "message": "1542", "line": 45, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 45, "endColumn": 12}, {"ruleId": "1457", "severity": 1, "message": "1543", "line": 11, "column": 55, "nodeType": null, "messageId": "1459", "endLine": 11, "endColumn": 59}, {"ruleId": "1457", "severity": 1, "message": "1544", "line": 15, "column": 7, "nodeType": null, "messageId": "1459", "endLine": 15, "endColumn": 24}, {"ruleId": "1457", "severity": 1, "message": "1545", "line": 46, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 46, "endColumn": 25}, {"ruleId": "1546", "severity": 1, "message": "1547", "line": 5, "column": 18, "nodeType": "1548", "messageId": "1549", "endLine": 5, "endColumn": 31, "suggestions": "1550"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 126, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 126, "endColumn": 24, "suggestions": "1551"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 193, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 193, "endColumn": 24, "suggestions": "1552"}, {"ruleId": "1457", "severity": 1, "message": "1553", "line": 219, "column": 16, "nodeType": null, "messageId": "1459", "endLine": 219, "endColumn": 24}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 23, "column": 20, "nodeType": "1393", "messageId": "1394", "endLine": 23, "endColumn": 23, "suggestions": "1554"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 40, "column": 20, "nodeType": "1393", "messageId": "1394", "endLine": 40, "endColumn": 23, "suggestions": "1555"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 124, "column": 22, "nodeType": "1393", "messageId": "1394", "endLine": 124, "endColumn": 25, "suggestions": "1556"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 199, "column": 22, "nodeType": "1393", "messageId": "1394", "endLine": 199, "endColumn": 25, "suggestions": "1557"}, {"ruleId": "1457", "severity": 1, "message": "1558", "line": 225, "column": 9, "nodeType": null, "messageId": "1459", "endLine": 225, "endColumn": 20}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 254, "column": 22, "nodeType": "1393", "messageId": "1394", "endLine": 254, "endColumn": 25, "suggestions": "1559"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 295, "column": 22, "nodeType": "1393", "messageId": "1394", "endLine": 295, "endColumn": 25, "suggestions": "1560"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 86, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 86, "endColumn": 37, "suggestions": "1561"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 160, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 160, "endColumn": 37, "suggestions": "1562"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 54, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 54, "endColumn": 37, "suggestions": "1563"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 12, "column": 47, "nodeType": "1393", "messageId": "1394", "endLine": 12, "endColumn": 50, "suggestions": "1564"}, {"ruleId": "1457", "severity": 1, "message": "1493", "line": 46, "column": 16, "nodeType": null, "messageId": "1459", "endLine": 46, "endColumn": 21}, {"ruleId": "1413", "severity": 1, "message": "1565", "line": 80, "column": 6, "nodeType": "1415", "endLine": 80, "endColumn": 47, "suggestions": "1566"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 295, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 295, "endColumn": 37, "suggestions": "1567"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 301, "column": 41, "nodeType": "1393", "messageId": "1394", "endLine": 301, "endColumn": 44, "suggestions": "1568"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 318, "column": 15, "nodeType": "1393", "messageId": "1394", "endLine": 318, "endColumn": 18, "suggestions": "1569"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 341, "column": 25, "nodeType": "1393", "messageId": "1394", "endLine": 341, "endColumn": 28, "suggestions": "1570"}, {"ruleId": "1457", "severity": 1, "message": "1571", "line": 5, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 5, "endColumn": 13}, {"ruleId": "1457", "severity": 1, "message": "1572", "line": 9, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 9, "endColumn": 14}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 15, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 15, "endColumn": 22, "suggestions": "1573"}, {"ruleId": "1457", "severity": 1, "message": "1572", "line": 30, "column": 11, "nodeType": null, "messageId": "1459", "endLine": 30, "endColumn": 22}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 30, "column": 27, "nodeType": "1393", "messageId": "1394", "endLine": 30, "endColumn": 30, "suggestions": "1574"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 48, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 48, "endColumn": 24, "suggestions": "1575"}, {"ruleId": "1457", "severity": 1, "message": "1576", "line": 109, "column": 20, "nodeType": null, "messageId": "1459", "endLine": 109, "endColumn": 32}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 123, "column": 24, "nodeType": "1393", "messageId": "1394", "endLine": 123, "endColumn": 27, "suggestions": "1577"}, {"ruleId": "1457", "severity": 1, "message": "1538", "line": 126, "column": 18, "nodeType": null, "messageId": "1459", "endLine": 126, "endColumn": 19}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 169, "column": 17, "nodeType": "1393", "messageId": "1394", "endLine": 169, "endColumn": 20, "suggestions": "1578"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 176, "column": 18, "nodeType": "1393", "messageId": "1394", "endLine": 176, "endColumn": 21, "suggestions": "1579"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 178, "column": 12, "nodeType": "1393", "messageId": "1394", "endLine": 178, "endColumn": 15, "suggestions": "1580"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 191, "column": 17, "nodeType": "1393", "messageId": "1394", "endLine": 191, "endColumn": 20, "suggestions": "1581"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 193, "column": 12, "nodeType": "1393", "messageId": "1394", "endLine": 193, "endColumn": 15, "suggestions": "1582"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 206, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 206, "endColumn": 22, "suggestions": "1583"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 208, "column": 12, "nodeType": "1393", "messageId": "1394", "endLine": 208, "endColumn": 15, "suggestions": "1584"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 221, "column": 20, "nodeType": "1393", "messageId": "1394", "endLine": 221, "endColumn": 23, "suggestions": "1585"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 228, "column": 20, "nodeType": "1393", "messageId": "1394", "endLine": 228, "endColumn": 23, "suggestions": "1586"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 246, "column": 28, "nodeType": "1393", "messageId": "1394", "endLine": 246, "endColumn": 31, "suggestions": "1587"}, {"ruleId": "1457", "severity": 1, "message": "1523", "line": 257, "column": 45, "nodeType": null, "messageId": "1459", "endLine": 257, "endColumn": 54}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 79, "column": 17, "nodeType": "1393", "messageId": "1394", "endLine": 79, "endColumn": 20, "suggestions": "1588"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 131, "column": 17, "nodeType": "1393", "messageId": "1394", "endLine": 131, "endColumn": 20, "suggestions": "1589"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 158, "column": 17, "nodeType": "1393", "messageId": "1394", "endLine": 158, "endColumn": 20, "suggestions": "1590"}, {"ruleId": "1457", "severity": 1, "message": "1591", "line": 68, "column": 7, "nodeType": null, "messageId": "1459", "endLine": 68, "endColumn": 31}, {"ruleId": "1457", "severity": 1, "message": "1493", "line": 286, "column": 14, "nodeType": null, "messageId": "1459", "endLine": 286, "endColumn": 19}, {"ruleId": "1457", "severity": 1, "message": "1493", "line": 298, "column": 14, "nodeType": null, "messageId": "1459", "endLine": 298, "endColumn": 19}, {"ruleId": "1457", "severity": 1, "message": "1592", "line": 380, "column": 17, "nodeType": null, "messageId": "1459", "endLine": 380, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1593", "line": 23, "column": 32, "nodeType": null, "messageId": "1459", "endLine": 23, "endColumn": 40}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 224, "column": 28, "nodeType": "1393", "messageId": "1394", "endLine": 224, "endColumn": 31, "suggestions": "1594"}, {"ruleId": "1457", "severity": 1, "message": "1595", "line": 226, "column": 15, "nodeType": null, "messageId": "1459", "endLine": 226, "endColumn": 18}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 8, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 8, "endColumn": 39, "suggestions": "1596"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 22, "column": 24, "nodeType": "1393", "messageId": "1394", "endLine": 22, "endColumn": 27, "suggestions": "1597"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 45, "column": 23, "nodeType": "1393", "messageId": "1394", "endLine": 45, "endColumn": 26, "suggestions": "1598"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 46, "column": 26, "nodeType": "1393", "messageId": "1394", "endLine": 46, "endColumn": 29, "suggestions": "1599"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 160, "column": 24, "nodeType": "1393", "messageId": "1394", "endLine": 160, "endColumn": 27, "suggestions": "1600"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 176, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 176, "endColumn": 24, "suggestions": "1601"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 41, "column": 18, "nodeType": "1393", "messageId": "1394", "endLine": 41, "endColumn": 21, "suggestions": "1602"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 67, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 67, "endColumn": 37, "suggestions": "1603"}, {"ruleId": "1457", "severity": 1, "message": "1604", "line": 3, "column": 37, "nodeType": null, "messageId": "1459", "endLine": 3, "endColumn": 52}, {"ruleId": "1457", "severity": 1, "message": "1605", "line": 3, "column": 54, "nodeType": null, "messageId": "1459", "endLine": 3, "endColumn": 66}, {"ruleId": "1457", "severity": 1, "message": "1606", "line": 7, "column": 11, "nodeType": null, "messageId": "1459", "endLine": 7, "endColumn": 14}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 70, "column": 67, "nodeType": "1393", "messageId": "1394", "endLine": 70, "endColumn": 70, "suggestions": "1607"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 143, "column": 71, "nodeType": "1393", "messageId": "1394", "endLine": 143, "endColumn": 74, "suggestions": "1608"}, {"ruleId": "1457", "severity": 1, "message": "1609", "line": 165, "column": 15, "nodeType": null, "messageId": "1459", "endLine": 165, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1610", "line": 274, "column": 23, "nodeType": null, "messageId": "1459", "endLine": 274, "endColumn": 31}, {"ruleId": "1457", "severity": 1, "message": "1610", "line": 337, "column": 23, "nodeType": null, "messageId": "1459", "endLine": 337, "endColumn": 31}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 69, "column": 13, "nodeType": "1393", "messageId": "1394", "endLine": 69, "endColumn": 16, "suggestions": "1611"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 62, "column": 37, "nodeType": "1393", "messageId": "1394", "endLine": 62, "endColumn": 40, "suggestions": "1612"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 73, "column": 37, "nodeType": "1393", "messageId": "1394", "endLine": 73, "endColumn": 40, "suggestions": "1613"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 94, "column": 37, "nodeType": "1393", "messageId": "1394", "endLine": 94, "endColumn": 40, "suggestions": "1614"}, {"ruleId": "1457", "severity": 1, "message": "1615", "line": 14, "column": 14, "nodeType": null, "messageId": "1459", "endLine": 14, "endColumn": 21}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 79, "column": 21, "nodeType": "1393", "messageId": "1394", "endLine": 79, "endColumn": 24, "suggestions": "1616"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 4, "column": 24, "nodeType": "1393", "messageId": "1394", "endLine": 4, "endColumn": 27, "suggestions": "1617"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 4, "column": 46, "nodeType": "1393", "messageId": "1394", "endLine": 4, "endColumn": 49, "suggestions": "1618"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 86, "column": 88, "nodeType": "1393", "messageId": "1394", "endLine": 86, "endColumn": 91, "suggestions": "1619"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 91, "column": 81, "nodeType": "1393", "messageId": "1394", "endLine": 91, "endColumn": 84, "suggestions": "1620"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 96, "column": 69, "nodeType": "1393", "messageId": "1394", "endLine": 96, "endColumn": 72, "suggestions": "1621"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 119, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 119, "endColumn": 37, "suggestions": "1622"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 122, "column": 43, "nodeType": "1393", "messageId": "1394", "endLine": 122, "endColumn": 46, "suggestions": "1623"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 17, "column": 16, "nodeType": "1393", "messageId": "1394", "endLine": 17, "endColumn": 19, "suggestions": "1624"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 146, "column": 47, "nodeType": "1393", "messageId": "1394", "endLine": 146, "endColumn": 50, "suggestions": "1625"}, {"ruleId": "1457", "severity": 1, "message": "1626", "line": 358, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 358, "endColumn": 26}, {"ruleId": "1457", "severity": 1, "message": "1627", "line": 358, "column": 28, "nodeType": null, "messageId": "1459", "endLine": 358, "endColumn": 47}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 455, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 455, "endColumn": 37, "suggestions": "1628"}, {"ruleId": "1457", "severity": 1, "message": "1538", "line": 60, "column": 18, "nodeType": null, "messageId": "1459", "endLine": 60, "endColumn": 19}, {"ruleId": "1413", "severity": 1, "message": "1629", "line": 79, "column": 6, "nodeType": "1415", "endLine": 79, "endColumn": 18, "suggestions": "1630"}, {"ruleId": "1457", "severity": 1, "message": "1538", "line": 36, "column": 18, "nodeType": null, "messageId": "1459", "endLine": 36, "endColumn": 19}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 119, "column": 62, "nodeType": "1393", "messageId": "1394", "endLine": 119, "endColumn": 65, "suggestions": "1631"}, {"ruleId": "1457", "severity": 1, "message": "1632", "line": 3, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 3, "endColumn": 22}, {"ruleId": "1457", "severity": 1, "message": "1633", "line": 4, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 4, "endColumn": 25}, {"ruleId": "1457", "severity": 1, "message": "1634", "line": 6, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 6, "endColumn": 14}, {"ruleId": "1457", "severity": 1, "message": "1635", "line": 6, "column": 16, "nodeType": null, "messageId": "1459", "endLine": 6, "endColumn": 27}, {"ruleId": "1457", "severity": 1, "message": "1636", "line": 6, "column": 29, "nodeType": null, "messageId": "1459", "endLine": 6, "endColumn": 39}, {"ruleId": "1457", "severity": 1, "message": "1637", "line": 6, "column": 41, "nodeType": null, "messageId": "1459", "endLine": 6, "endColumn": 50}, {"ruleId": "1457", "severity": 1, "message": "1514", "line": 7, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 7, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1507", "line": 8, "column": 8, "nodeType": null, "messageId": "1459", "endLine": 8, "endColumn": 12}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 131, "column": 33, "nodeType": "1429", "messageId": "1430", "suggestions": "1638"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 142, "column": 29, "nodeType": "1429", "messageId": "1430", "suggestions": "1639"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 142, "column": 37, "nodeType": "1429", "messageId": "1430", "suggestions": "1640"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 142, "column": 42, "nodeType": "1429", "messageId": "1430", "suggestions": "1641"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 142, "column": 48, "nodeType": "1429", "messageId": "1430", "suggestions": "1642"}, {"ruleId": "1457", "severity": 1, "message": "1643", "line": 19, "column": 16, "nodeType": null, "messageId": "1459", "endLine": 19, "endColumn": 20}, {"ruleId": "1457", "severity": 1, "message": "1644", "line": 19, "column": 34, "nodeType": null, "messageId": "1459", "endLine": 19, "endColumn": 47}, {"ruleId": "1457", "severity": 1, "message": "1645", "line": 20, "column": 11, "nodeType": null, "messageId": "1459", "endLine": 20, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1646", "line": 20, "column": 18, "nodeType": null, "messageId": "1459", "endLine": 20, "endColumn": 34}, {"ruleId": "1457", "severity": 1, "message": "1647", "line": 20, "column": 36, "nodeType": null, "messageId": "1459", "endLine": 20, "endColumn": 56}, {"ruleId": "1457", "severity": 1, "message": "1493", "line": 123, "column": 14, "nodeType": null, "messageId": "1459", "endLine": 123, "endColumn": 19}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 97, "column": 21, "nodeType": "1429", "messageId": "1430", "suggestions": "1648"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 97, "column": 62, "nodeType": "1429", "messageId": "1430", "suggestions": "1649"}, {"ruleId": "1427", "severity": 1, "message": "1428", "line": 97, "column": 81, "nodeType": "1429", "messageId": "1430", "suggestions": "1650"}, {"ruleId": "1651", "severity": 1, "message": "1652", "line": 141, "column": 23, "nodeType": "1653", "endLine": 145, "endColumn": 25}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 23, "column": 32, "nodeType": "1429", "messageId": "1430", "suggestions": "1654"}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 23, "column": 53, "nodeType": "1429", "messageId": "1430", "suggestions": "1655"}, {"ruleId": "1457", "severity": 1, "message": "1656", "line": 9, "column": 38, "nodeType": null, "messageId": "1459", "endLine": 9, "endColumn": 44}, {"ruleId": "1457", "severity": 1, "message": "1657", "line": 64, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 64, "endColumn": 23}, {"ruleId": "1457", "severity": 1, "message": "1658", "line": 64, "column": 25, "nodeType": null, "messageId": "1459", "endLine": 64, "endColumn": 41}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 10, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 10, "endColumn": 22, "suggestions": "1659"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 11, "column": 18, "nodeType": "1393", "messageId": "1394", "endLine": 11, "endColumn": 21, "suggestions": "1660"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 19, "column": 25, "nodeType": "1393", "messageId": "1394", "endLine": 19, "endColumn": 28, "suggestions": "1661"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 16, "column": 25, "nodeType": "1393", "messageId": "1394", "endLine": 16, "endColumn": 28, "suggestions": "1662"}, {"ruleId": "1457", "severity": 1, "message": "1663", "line": 21, "column": 3, "nodeType": null, "messageId": "1459", "endLine": 21, "endColumn": 16}, {"ruleId": "1457", "severity": 1, "message": "1609", "line": 28, "column": 11, "nodeType": null, "messageId": "1459", "endLine": 28, "endColumn": 16}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 13, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 13, "endColumn": 22, "suggestions": "1664"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 14, "column": 18, "nodeType": "1393", "messageId": "1394", "endLine": 14, "endColumn": 21, "suggestions": "1665"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 15, "column": 17, "nodeType": "1393", "messageId": "1394", "endLine": 15, "endColumn": 20, "suggestions": "1666"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 16, "column": 16, "nodeType": "1393", "messageId": "1394", "endLine": 16, "endColumn": 19, "suggestions": "1667"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 17, "column": 13, "nodeType": "1393", "messageId": "1394", "endLine": 17, "endColumn": 16, "suggestions": "1668"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 20, "column": 31, "nodeType": "1393", "messageId": "1394", "endLine": 20, "endColumn": 34, "suggestions": "1669"}, {"ruleId": "1413", "severity": 1, "message": "1670", "line": 54, "column": 9, "nodeType": "1512", "endLine": 59, "endColumn": 4}, {"ruleId": "1457", "severity": 1, "message": "1671", "line": 73, "column": 41, "nodeType": null, "messageId": "1459", "endLine": 73, "endColumn": 46}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 11, "column": 22, "nodeType": "1393", "messageId": "1394", "endLine": 11, "endColumn": 25, "suggestions": "1672"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 153, "column": 41, "nodeType": "1393", "messageId": "1394", "endLine": 153, "endColumn": 44, "suggestions": "1673"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 199, "column": 34, "nodeType": "1393", "messageId": "1394", "endLine": 199, "endColumn": 37, "suggestions": "1674"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 7, "column": 57, "nodeType": "1393", "messageId": "1394", "endLine": 7, "endColumn": 60, "suggestions": "1675"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 61, "column": 42, "nodeType": "1393", "messageId": "1394", "endLine": 61, "endColumn": 45, "suggestions": "1676"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 65, "column": 65, "nodeType": "1393", "messageId": "1394", "endLine": 65, "endColumn": 68, "suggestions": "1677"}, {"ruleId": "1457", "severity": 1, "message": "1678", "line": 22, "column": 10, "nodeType": null, "messageId": "1459", "endLine": 22, "endColumn": 24}, {"ruleId": "1457", "severity": 1, "message": "1679", "line": 22, "column": 26, "nodeType": null, "messageId": "1459", "endLine": 22, "endColumn": 43}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 160, "column": 42, "nodeType": "1393", "messageId": "1394", "endLine": 160, "endColumn": 45, "suggestions": "1680"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 163, "column": 39, "nodeType": "1393", "messageId": "1394", "endLine": 163, "endColumn": 42, "suggestions": "1681"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 195, "column": 66, "nodeType": "1393", "messageId": "1394", "endLine": 195, "endColumn": 69, "suggestions": "1682"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 198, "column": 39, "nodeType": "1393", "messageId": "1394", "endLine": 198, "endColumn": 42, "suggestions": "1683"}, {"ruleId": "1427", "severity": 1, "message": "1467", "line": 263, "column": 19, "nodeType": "1429", "messageId": "1430", "suggestions": "1684"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 31, "column": 40, "nodeType": "1393", "messageId": "1394", "endLine": 31, "endColumn": 43, "suggestions": "1685"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 32, "column": 64, "nodeType": "1393", "messageId": "1394", "endLine": 32, "endColumn": 67, "suggestions": "1686"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 42, "column": 57, "nodeType": "1393", "messageId": "1394", "endLine": 42, "endColumn": 60, "suggestions": "1687"}, {"ruleId": "1413", "severity": 1, "message": "1688", "line": 77, "column": 6, "nodeType": "1415", "endLine": 77, "endColumn": 53, "suggestions": "1689"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 97, "column": 52, "nodeType": "1393", "messageId": "1394", "endLine": 97, "endColumn": 55, "suggestions": "1690"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 104, "column": 38, "nodeType": "1393", "messageId": "1394", "endLine": 104, "endColumn": 41, "suggestions": "1691"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 108, "column": 40, "nodeType": "1393", "messageId": "1394", "endLine": 108, "endColumn": 43, "suggestions": "1692"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 115, "column": 35, "nodeType": "1393", "messageId": "1394", "endLine": 115, "endColumn": 38, "suggestions": "1693"}, {"ruleId": "1413", "severity": 1, "message": "1694", "line": 111, "column": 6, "nodeType": "1415", "endLine": 111, "endColumn": 37, "suggestions": "1695"}, {"ruleId": "1413", "severity": 1, "message": "1694", "line": 139, "column": 6, "nodeType": "1415", "endLine": 139, "endColumn": 37, "suggestions": "1696"}, {"ruleId": "1413", "severity": 1, "message": "1694", "line": 166, "column": 6, "nodeType": "1415", "endLine": 166, "endColumn": 37, "suggestions": "1697"}, {"ruleId": "1413", "severity": 1, "message": "1694", "line": 200, "column": 6, "nodeType": "1415", "endLine": 200, "endColumn": 37, "suggestions": "1698"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 31, "column": 31, "nodeType": "1393", "messageId": "1394", "endLine": 31, "endColumn": 34, "suggestions": "1699"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 58, "column": 42, "nodeType": "1393", "messageId": "1394", "endLine": 58, "endColumn": 45, "suggestions": "1700"}, {"ruleId": "1457", "severity": 1, "message": "1523", "line": 125, "column": 27, "nodeType": null, "messageId": "1459", "endLine": 125, "endColumn": 36}, {"ruleId": "1457", "severity": 1, "message": "1701", "line": 125, "column": 46, "nodeType": null, "messageId": "1459", "endLine": 125, "endColumn": 52}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 36, "column": 36, "nodeType": "1393", "messageId": "1394", "endLine": 36, "endColumn": 39, "suggestions": "1702"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 39, "column": 19, "nodeType": "1393", "messageId": "1394", "endLine": 39, "endColumn": 22, "suggestions": "1703"}, {"ruleId": "1391", "severity": 1, "message": "1392", "line": 40, "column": 17, "nodeType": "1393", "messageId": "1394", "endLine": 40, "endColumn": 20, "suggestions": "1704"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1705", "1706"], ["1707", "1708"], ["1709", "1710"], ["1711", "1712"], ["1713", "1714"], ["1715", "1716"], ["1717", "1718"], ["1719", "1720"], ["1721", "1722"], ["1723", "1724"], ["1725", "1726"], ["1727", "1728"], ["1729", "1730"], ["1731", "1732"], ["1733", "1734"], ["1735", "1736"], ["1737", "1738"], ["1739", "1740"], "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["1741"], ["1742", "1743"], ["1744", "1745"], ["1746", "1747"], ["1748", "1749"], ["1750", "1751"], ["1752", "1753"], ["1754", "1755"], ["1756", "1757"], ["1758"], ["1759", "1760"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["1761", "1762", "1763", "1764"], ["1765", "1766", "1767", "1768"], ["1769", "1770"], ["1771", "1772"], ["1773", "1774"], ["1775", "1776"], ["1777", "1778"], ["1779", "1780"], ["1781", "1782"], ["1783", "1784"], ["1785", "1786"], ["1787", "1788"], ["1789", "1790"], ["1791", "1792"], ["1793", "1794"], ["1795", "1796"], ["1797", "1798"], ["1799", "1800"], ["1801", "1802"], ["1803", "1804"], ["1805", "1806"], ["1807", "1808"], ["1809", "1810"], ["1811", "1812"], ["1813", "1814"], ["1815", "1816"], "@typescript-eslint/no-unused-vars", "'_' is assigned a value but never used.", "unusedVar", "'request' is defined but never used.", ["1817", "1818"], ["1819", "1820"], "'getValues' is assigned a value but never used.", "'ArrowLeft' is defined but never used.", ["1821", "1822"], ["1823", "1824"], "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", ["1825", "1826", "1827", "1828"], ["1829", "1830"], ["1831", "1832"], ["1833", "1834"], ["1835", "1836"], "'setSuccess' is assigned a value but never used.", ["1837", "1838", "1839", "1840"], "'emailVerificationSent' is assigned a value but never used.", ["1841", "1842", "1843", "1844"], "'backendUser' is assigned a value but never used.", "'isUserLoading' is assigned a value but never used.", "'setValue' is assigned a value but never used.", ["1845", "1846"], "'initials' is assigned a value but never used.", ["1847", "1848"], ["1849", "1850"], ["1851", "1852", "1853", "1854"], ["1855", "1856"], "React Hook useEffect has a missing dependency: 'projectId'. Either include it or remove the dependency array.", ["1857"], ["1858", "1859"], "'reset' is assigned a value but never used.", ["1860", "1861"], ["1862", "1863"], ["1864", "1865"], "'error' is defined but never used.", ["1866", "1867"], "'_data' is defined but never used.", ["1868", "1869"], ["1870", "1871", "1872", "1873"], ["1874", "1875"], "'logout' is assigned a value but never used.", "'router' is assigned a value but never used.", "'Tabs' is defined but never used.", "'TabsContent' is defined but never used.", "'TabsList' is defined but never used.", "'TabsTrigger' is defined but never used.", "'Star' is defined but never used.", "'FileText' is defined but never used.", "'Link' is defined but never used.", "'Plus' is defined but never used.", "'getProgressColor' is assigned a value but never used.", ["1876", "1877", "1878", "1879"], "The 'hints' array makes the dependencies of useEffect Hook (at line 82) change on every render. Move it inside the useEffect callback. Alternatively, wrap the initialization of 'hints' in its own useMemo() Hook.", "VariableDeclarator", "'user' is assigned a value but never used.", "'Button' is defined but never used.", "'Tooltip' is defined but never used.", "'TooltipContent' is defined but never used.", "'TooltipTrigger' is defined but never used.", "'MessageCircle' is defined but never used.", "'Columns2' is defined but never used.", "'Columns3' is defined but never used.", "'ICON_SIZES' is defined but never used.", ["1880", "1881"], "'projectId' is defined but never used.", "'externalIsChatCollapsed' is defined but never used.", "'externalSetIsChatCollapsed' is defined but never used.", "'setMessages' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "'setOpen' is assigned a value but never used.", "'isCollapsed' is assigned a value but never used.", "'setIsCollapsed' is assigned a value but never used.", "'isChatCollapsed' is assigned a value but never used.", "'setIsChatCollapsed' is assigned a value but never used.", "'handleWidthToggle' is assigned a value but never used.", ["1882", "1883"], "'useUserSync' is defined but never used.", "'setUserProperties' is assigned a value but never used.", "'e' is defined but never used.", "React Hook useEffect has a missing dependency: 'identifyUser'. Either include it or remove the dependency array.", ["1884"], "'layout' is assigned a value but never used.", "'className' is defined but never used.", "'_ref' is defined but never used.", "'autoScrollEnabled' is assigned a value but never used.", "'dotPulseDuration' is assigned a value but never used.", "@typescript-eslint/no-empty-object-type", "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["1885"], ["1886", "1887"], ["1888", "1889"], "'apiError' is defined but never used.", ["1890", "1891"], ["1892", "1893"], ["1894", "1895"], ["1896", "1897"], "'queryClient' is assigned a value but never used.", ["1898", "1899"], ["1900", "1901"], ["1902", "1903"], ["1904", "1905"], ["1906", "1907"], ["1908", "1909"], "React Hook useEffect has a missing dependency: 'syncUserToBackend'. Either include it or remove the dependency array.", ["1910"], ["1911", "1912"], ["1913", "1914"], ["1915", "1916"], ["1917", "1918"], "'AuthTokens' is defined but never used.", "'ApiResponse' is defined but never used.", ["1919", "1920"], ["1921", "1922"], ["1923", "1924"], "'refreshError' is defined but never used.", ["1925", "1926"], ["1927", "1928"], ["1929", "1930"], ["1931", "1932"], ["1933", "1934"], ["1935", "1936"], ["1937", "1938"], ["1939", "1940"], ["1941", "1942"], ["1943", "1944"], ["1945", "1946"], ["1947", "1948"], ["1949", "1950"], ["1951", "1952"], "'generateVerificationCode' is assigned a value but never used.", "'key' is assigned a value but never used.", "'ideaText' is defined but never used.", ["1953", "1954"], "'url' is defined but never used.", ["1955", "1956"], ["1957", "1958"], ["1959", "1960"], ["1961", "1962"], ["1963", "1964"], ["1965", "1966"], ["1967", "1968"], ["1969", "1970"], "'BusinessSection' is defined but never used.", "'BusinessItem' is defined but never used.", "'get' is defined but never used.", ["1971", "1972"], ["1973", "1974"], "'state' is assigned a value but never used.", "'response' is assigned a value but never used.", ["1975", "1976"], ["1977", "1978"], ["1979", "1980"], ["1981", "1982"], "'posthog' is defined but never used.", ["1983", "1984"], ["1985", "1986"], ["1987", "1988"], ["1989", "1990"], ["1991", "1992"], ["1993", "1994"], ["1995", "1996"], ["1997", "1998"], ["1999", "2000"], ["2001", "2002"], "'isHowItWorksOpen' is assigned a value but never used.", "'setIsHowItWorksOpen' is assigned a value but never used.", ["2003", "2004"], "React Hook useEffect has a missing dependency: 'fetchTokens'. Either include it or remove the dependency array.", ["2005"], ["2006", "2007"], "'PostHogDebug' is defined but never used.", "'ClerkTokenDebug' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'CardHeader' is defined but never used.", "'CardTitle' is defined but never used.", ["2008", "2009", "2010", "2011"], ["2012", "2013", "2014", "2015"], ["2016", "2017", "2018", "2019"], ["2020", "2021", "2022", "2023"], ["2024", "2025", "2026", "2027"], "'post' is assigned a value but never used.", "'apiIsSignedIn' is assigned a value but never used.", "'token' is assigned a value but never used.", "'fetchAndLogToken' is assigned a value but never used.", "'copyTokenToClipboard' is assigned a value but never used.", ["2028", "2029", "2030", "2031"], ["2032", "2033", "2034", "2035"], ["2036", "2037", "2038", "2039"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["2040", "2041", "2042", "2043"], ["2044", "2045", "2046", "2047"], "'Share2' is defined but never used.", "'selectedModel' is assigned a value but never used.", "'setSelectedModel' is assigned a value but never used.", ["2048", "2049"], ["2050", "2051"], ["2052", "2053"], ["2054", "2055"], "'activeContent' is defined but never used.", ["2056", "2057"], ["2058", "2059"], ["2060", "2061"], ["2062", "2063"], ["2064", "2065"], ["2066", "2067"], "The 'hints' array makes the dependencies of useEffect Hook (at line 88) change on every render. Move it inside the useEffect callback. Alternatively, wrap the initialization of 'hints' in its own useMemo() Hook.", "'index' is defined but never used.", ["2068", "2069"], ["2070", "2071"], ["2072", "2073"], ["2074", "2075"], ["2076", "2077"], ["2078", "2079"], "'isInfoExpanded' is assigned a value but never used.", "'setIsInfoExpanded' is assigned a value but never used.", ["2080", "2081"], ["2082", "2083"], ["2084", "2085"], ["2086", "2087"], ["2088", "2089", "2090", "2091"], ["2092", "2093"], ["2094", "2095"], ["2096", "2097"], "React Hook useEffect has a missing dependency: 'handleNext'. Either include it or remove the dependency array.", ["2098"], ["2099", "2100"], ["2101", "2102"], ["2103", "2104"], ["2105", "2106"], "React Hook useCallback has a missing dependency: 'updateState'. Either include it or remove the dependency array.", ["2107"], ["2108"], ["2109"], ["2110"], ["2111", "2112"], ["2113", "2114"], "'itemId' is defined but never used.", ["2115", "2116"], ["2117", "2118"], ["2119", "2120"], {"messageId": "2121", "fix": "2122", "desc": "2123"}, {"messageId": "2124", "fix": "2125", "desc": "2126"}, {"messageId": "2121", "fix": "2127", "desc": "2123"}, {"messageId": "2124", "fix": "2128", "desc": "2126"}, {"messageId": "2121", "fix": "2129", "desc": "2123"}, {"messageId": "2124", "fix": "2130", "desc": "2126"}, {"messageId": "2121", "fix": "2131", "desc": "2123"}, {"messageId": "2124", "fix": "2132", "desc": "2126"}, {"messageId": "2121", "fix": "2133", "desc": "2123"}, {"messageId": "2124", "fix": "2134", "desc": "2126"}, {"messageId": "2121", "fix": "2135", "desc": "2123"}, {"messageId": "2124", "fix": "2136", "desc": "2126"}, {"messageId": "2121", "fix": "2137", "desc": "2123"}, {"messageId": "2124", "fix": "2138", "desc": "2126"}, {"messageId": "2121", "fix": "2139", "desc": "2123"}, {"messageId": "2124", "fix": "2140", "desc": "2126"}, {"messageId": "2121", "fix": "2141", "desc": "2123"}, {"messageId": "2124", "fix": "2142", "desc": "2126"}, {"messageId": "2121", "fix": "2143", "desc": "2123"}, {"messageId": "2124", "fix": "2144", "desc": "2126"}, {"messageId": "2121", "fix": "2145", "desc": "2123"}, {"messageId": "2124", "fix": "2146", "desc": "2126"}, {"messageId": "2121", "fix": "2147", "desc": "2123"}, {"messageId": "2124", "fix": "2148", "desc": "2126"}, {"messageId": "2121", "fix": "2149", "desc": "2123"}, {"messageId": "2124", "fix": "2150", "desc": "2126"}, {"messageId": "2121", "fix": "2151", "desc": "2123"}, {"messageId": "2124", "fix": "2152", "desc": "2126"}, {"messageId": "2121", "fix": "2153", "desc": "2123"}, {"messageId": "2124", "fix": "2154", "desc": "2126"}, {"messageId": "2121", "fix": "2155", "desc": "2123"}, {"messageId": "2124", "fix": "2156", "desc": "2126"}, {"messageId": "2121", "fix": "2157", "desc": "2123"}, {"messageId": "2124", "fix": "2158", "desc": "2126"}, {"messageId": "2121", "fix": "2159", "desc": "2123"}, {"messageId": "2124", "fix": "2160", "desc": "2126"}, {"desc": "2161", "fix": "2162"}, {"messageId": "2121", "fix": "2163", "desc": "2123"}, {"messageId": "2124", "fix": "2164", "desc": "2126"}, {"messageId": "2121", "fix": "2165", "desc": "2123"}, {"messageId": "2124", "fix": "2166", "desc": "2126"}, {"messageId": "2121", "fix": "2167", "desc": "2123"}, {"messageId": "2124", "fix": "2168", "desc": "2126"}, {"messageId": "2121", "fix": "2169", "desc": "2123"}, {"messageId": "2124", "fix": "2170", "desc": "2126"}, {"messageId": "2121", "fix": "2171", "desc": "2123"}, {"messageId": "2124", "fix": "2172", "desc": "2126"}, {"messageId": "2121", "fix": "2173", "desc": "2123"}, {"messageId": "2124", "fix": "2174", "desc": "2126"}, {"messageId": "2121", "fix": "2175", "desc": "2123"}, {"messageId": "2124", "fix": "2176", "desc": "2126"}, {"messageId": "2121", "fix": "2177", "desc": "2123"}, {"messageId": "2124", "fix": "2178", "desc": "2126"}, {"desc": "2161", "fix": "2179"}, {"messageId": "2121", "fix": "2180", "desc": "2123"}, {"messageId": "2124", "fix": "2181", "desc": "2126"}, {"messageId": "2182", "data": "2183", "fix": "2184", "desc": "2185"}, {"messageId": "2182", "data": "2186", "fix": "2187", "desc": "2188"}, {"messageId": "2182", "data": "2189", "fix": "2190", "desc": "2191"}, {"messageId": "2182", "data": "2192", "fix": "2193", "desc": "2194"}, {"messageId": "2182", "data": "2195", "fix": "2196", "desc": "2185"}, {"messageId": "2182", "data": "2197", "fix": "2198", "desc": "2188"}, {"messageId": "2182", "data": "2199", "fix": "2200", "desc": "2191"}, {"messageId": "2182", "data": "2201", "fix": "2202", "desc": "2194"}, {"messageId": "2121", "fix": "2203", "desc": "2123"}, {"messageId": "2124", "fix": "2204", "desc": "2126"}, {"messageId": "2121", "fix": "2205", "desc": "2123"}, {"messageId": "2124", "fix": "2206", "desc": "2126"}, {"messageId": "2121", "fix": "2207", "desc": "2123"}, {"messageId": "2124", "fix": "2208", "desc": "2126"}, {"messageId": "2121", "fix": "2209", "desc": "2123"}, {"messageId": "2124", "fix": "2210", "desc": "2126"}, {"messageId": "2121", "fix": "2211", "desc": "2123"}, {"messageId": "2124", "fix": "2212", "desc": "2126"}, {"messageId": "2121", "fix": "2213", "desc": "2123"}, {"messageId": "2124", "fix": "2214", "desc": "2126"}, {"messageId": "2121", "fix": "2215", "desc": "2123"}, {"messageId": "2124", "fix": "2216", "desc": "2126"}, {"messageId": "2121", "fix": "2217", "desc": "2123"}, {"messageId": "2124", "fix": "2218", "desc": "2126"}, {"messageId": "2121", "fix": "2219", "desc": "2123"}, {"messageId": "2124", "fix": "2220", "desc": "2126"}, {"messageId": "2121", "fix": "2221", "desc": "2123"}, {"messageId": "2124", "fix": "2222", "desc": "2126"}, {"messageId": "2121", "fix": "2223", "desc": "2123"}, {"messageId": "2124", "fix": "2224", "desc": "2126"}, {"messageId": "2121", "fix": "2225", "desc": "2123"}, {"messageId": "2124", "fix": "2226", "desc": "2126"}, {"messageId": "2121", "fix": "2227", "desc": "2123"}, {"messageId": "2124", "fix": "2228", "desc": "2126"}, {"messageId": "2121", "fix": "2229", "desc": "2123"}, {"messageId": "2124", "fix": "2230", "desc": "2126"}, {"messageId": "2121", "fix": "2231", "desc": "2123"}, {"messageId": "2124", "fix": "2232", "desc": "2126"}, {"messageId": "2121", "fix": "2233", "desc": "2123"}, {"messageId": "2124", "fix": "2234", "desc": "2126"}, {"messageId": "2121", "fix": "2235", "desc": "2123"}, {"messageId": "2124", "fix": "2236", "desc": "2126"}, {"messageId": "2121", "fix": "2237", "desc": "2123"}, {"messageId": "2124", "fix": "2238", "desc": "2126"}, {"messageId": "2121", "fix": "2239", "desc": "2123"}, {"messageId": "2124", "fix": "2240", "desc": "2126"}, {"messageId": "2121", "fix": "2241", "desc": "2123"}, {"messageId": "2124", "fix": "2242", "desc": "2126"}, {"messageId": "2121", "fix": "2243", "desc": "2123"}, {"messageId": "2124", "fix": "2244", "desc": "2126"}, {"messageId": "2121", "fix": "2245", "desc": "2123"}, {"messageId": "2124", "fix": "2246", "desc": "2126"}, {"messageId": "2121", "fix": "2247", "desc": "2123"}, {"messageId": "2124", "fix": "2248", "desc": "2126"}, {"messageId": "2121", "fix": "2249", "desc": "2123"}, {"messageId": "2124", "fix": "2250", "desc": "2126"}, {"messageId": "2121", "fix": "2251", "desc": "2123"}, {"messageId": "2124", "fix": "2252", "desc": "2126"}, {"messageId": "2121", "fix": "2253", "desc": "2123"}, {"messageId": "2124", "fix": "2254", "desc": "2126"}, {"messageId": "2121", "fix": "2255", "desc": "2123"}, {"messageId": "2124", "fix": "2256", "desc": "2126"}, {"messageId": "2121", "fix": "2257", "desc": "2123"}, {"messageId": "2124", "fix": "2258", "desc": "2126"}, {"messageId": "2182", "data": "2259", "fix": "2260", "desc": "2261"}, {"messageId": "2182", "data": "2262", "fix": "2263", "desc": "2264"}, {"messageId": "2182", "data": "2265", "fix": "2266", "desc": "2267"}, {"messageId": "2182", "data": "2268", "fix": "2269", "desc": "2270"}, {"messageId": "2121", "fix": "2271", "desc": "2123"}, {"messageId": "2124", "fix": "2272", "desc": "2126"}, {"messageId": "2121", "fix": "2273", "desc": "2123"}, {"messageId": "2124", "fix": "2274", "desc": "2126"}, {"messageId": "2121", "fix": "2275", "desc": "2123"}, {"messageId": "2124", "fix": "2276", "desc": "2126"}, {"messageId": "2121", "fix": "2277", "desc": "2123"}, {"messageId": "2124", "fix": "2278", "desc": "2126"}, {"messageId": "2182", "data": "2279", "fix": "2280", "desc": "2261"}, {"messageId": "2182", "data": "2281", "fix": "2282", "desc": "2264"}, {"messageId": "2182", "data": "2283", "fix": "2284", "desc": "2267"}, {"messageId": "2182", "data": "2285", "fix": "2286", "desc": "2270"}, {"messageId": "2182", "data": "2287", "fix": "2288", "desc": "2261"}, {"messageId": "2182", "data": "2289", "fix": "2290", "desc": "2264"}, {"messageId": "2182", "data": "2291", "fix": "2292", "desc": "2267"}, {"messageId": "2182", "data": "2293", "fix": "2294", "desc": "2270"}, {"messageId": "2121", "fix": "2295", "desc": "2123"}, {"messageId": "2124", "fix": "2296", "desc": "2126"}, {"messageId": "2121", "fix": "2297", "desc": "2123"}, {"messageId": "2124", "fix": "2298", "desc": "2126"}, {"messageId": "2121", "fix": "2299", "desc": "2123"}, {"messageId": "2124", "fix": "2300", "desc": "2126"}, {"messageId": "2182", "data": "2301", "fix": "2302", "desc": "2261"}, {"messageId": "2182", "data": "2303", "fix": "2304", "desc": "2264"}, {"messageId": "2182", "data": "2305", "fix": "2306", "desc": "2267"}, {"messageId": "2182", "data": "2307", "fix": "2308", "desc": "2270"}, {"messageId": "2121", "fix": "2309", "desc": "2123"}, {"messageId": "2124", "fix": "2310", "desc": "2126"}, {"desc": "2311", "fix": "2312"}, {"messageId": "2121", "fix": "2313", "desc": "2123"}, {"messageId": "2124", "fix": "2314", "desc": "2126"}, {"messageId": "2121", "fix": "2315", "desc": "2123"}, {"messageId": "2124", "fix": "2316", "desc": "2126"}, {"messageId": "2121", "fix": "2317", "desc": "2123"}, {"messageId": "2124", "fix": "2318", "desc": "2126"}, {"messageId": "2121", "fix": "2319", "desc": "2123"}, {"messageId": "2124", "fix": "2320", "desc": "2126"}, {"messageId": "2121", "fix": "2321", "desc": "2123"}, {"messageId": "2124", "fix": "2322", "desc": "2126"}, {"messageId": "2121", "fix": "2323", "desc": "2123"}, {"messageId": "2124", "fix": "2324", "desc": "2126"}, {"messageId": "2182", "data": "2325", "fix": "2326", "desc": "2261"}, {"messageId": "2182", "data": "2327", "fix": "2328", "desc": "2264"}, {"messageId": "2182", "data": "2329", "fix": "2330", "desc": "2267"}, {"messageId": "2182", "data": "2331", "fix": "2332", "desc": "2270"}, {"messageId": "2121", "fix": "2333", "desc": "2123"}, {"messageId": "2124", "fix": "2334", "desc": "2126"}, {"messageId": "2182", "data": "2335", "fix": "2336", "desc": "2261"}, {"messageId": "2182", "data": "2337", "fix": "2338", "desc": "2264"}, {"messageId": "2182", "data": "2339", "fix": "2340", "desc": "2267"}, {"messageId": "2182", "data": "2341", "fix": "2342", "desc": "2270"}, {"messageId": "2121", "fix": "2343", "desc": "2123"}, {"messageId": "2124", "fix": "2344", "desc": "2126"}, {"messageId": "2121", "fix": "2345", "desc": "2123"}, {"messageId": "2124", "fix": "2346", "desc": "2126"}, {"desc": "2347", "fix": "2348"}, {"messageId": "2349", "fix": "2350", "desc": "2351"}, {"messageId": "2121", "fix": "2352", "desc": "2123"}, {"messageId": "2124", "fix": "2353", "desc": "2126"}, {"messageId": "2121", "fix": "2354", "desc": "2123"}, {"messageId": "2124", "fix": "2355", "desc": "2126"}, {"messageId": "2121", "fix": "2356", "desc": "2123"}, {"messageId": "2124", "fix": "2357", "desc": "2126"}, {"messageId": "2121", "fix": "2358", "desc": "2123"}, {"messageId": "2124", "fix": "2359", "desc": "2126"}, {"messageId": "2121", "fix": "2360", "desc": "2123"}, {"messageId": "2124", "fix": "2361", "desc": "2126"}, {"messageId": "2121", "fix": "2362", "desc": "2123"}, {"messageId": "2124", "fix": "2363", "desc": "2126"}, {"messageId": "2121", "fix": "2364", "desc": "2123"}, {"messageId": "2124", "fix": "2365", "desc": "2126"}, {"messageId": "2121", "fix": "2366", "desc": "2123"}, {"messageId": "2124", "fix": "2367", "desc": "2126"}, {"messageId": "2121", "fix": "2368", "desc": "2123"}, {"messageId": "2124", "fix": "2369", "desc": "2126"}, {"messageId": "2121", "fix": "2370", "desc": "2123"}, {"messageId": "2124", "fix": "2371", "desc": "2126"}, {"messageId": "2121", "fix": "2372", "desc": "2123"}, {"messageId": "2124", "fix": "2373", "desc": "2126"}, {"messageId": "2121", "fix": "2374", "desc": "2123"}, {"messageId": "2124", "fix": "2375", "desc": "2126"}, {"desc": "2376", "fix": "2377"}, {"messageId": "2121", "fix": "2378", "desc": "2123"}, {"messageId": "2124", "fix": "2379", "desc": "2126"}, {"messageId": "2121", "fix": "2380", "desc": "2123"}, {"messageId": "2124", "fix": "2381", "desc": "2126"}, {"messageId": "2121", "fix": "2382", "desc": "2123"}, {"messageId": "2124", "fix": "2383", "desc": "2126"}, {"messageId": "2121", "fix": "2384", "desc": "2123"}, {"messageId": "2124", "fix": "2385", "desc": "2126"}, {"messageId": "2121", "fix": "2386", "desc": "2123"}, {"messageId": "2124", "fix": "2387", "desc": "2126"}, {"messageId": "2121", "fix": "2388", "desc": "2123"}, {"messageId": "2124", "fix": "2389", "desc": "2126"}, {"messageId": "2121", "fix": "2390", "desc": "2123"}, {"messageId": "2124", "fix": "2391", "desc": "2126"}, {"messageId": "2121", "fix": "2392", "desc": "2123"}, {"messageId": "2124", "fix": "2393", "desc": "2126"}, {"messageId": "2121", "fix": "2394", "desc": "2123"}, {"messageId": "2124", "fix": "2395", "desc": "2126"}, {"messageId": "2121", "fix": "2396", "desc": "2123"}, {"messageId": "2124", "fix": "2397", "desc": "2126"}, {"messageId": "2121", "fix": "2398", "desc": "2123"}, {"messageId": "2124", "fix": "2399", "desc": "2126"}, {"messageId": "2121", "fix": "2400", "desc": "2123"}, {"messageId": "2124", "fix": "2401", "desc": "2126"}, {"messageId": "2121", "fix": "2402", "desc": "2123"}, {"messageId": "2124", "fix": "2403", "desc": "2126"}, {"messageId": "2121", "fix": "2404", "desc": "2123"}, {"messageId": "2124", "fix": "2405", "desc": "2126"}, {"messageId": "2121", "fix": "2406", "desc": "2123"}, {"messageId": "2124", "fix": "2407", "desc": "2126"}, {"messageId": "2121", "fix": "2408", "desc": "2123"}, {"messageId": "2124", "fix": "2409", "desc": "2126"}, {"messageId": "2121", "fix": "2410", "desc": "2123"}, {"messageId": "2124", "fix": "2411", "desc": "2126"}, {"messageId": "2121", "fix": "2412", "desc": "2123"}, {"messageId": "2124", "fix": "2413", "desc": "2126"}, {"messageId": "2121", "fix": "2414", "desc": "2123"}, {"messageId": "2124", "fix": "2415", "desc": "2126"}, {"messageId": "2121", "fix": "2416", "desc": "2123"}, {"messageId": "2124", "fix": "2417", "desc": "2126"}, {"messageId": "2121", "fix": "2418", "desc": "2123"}, {"messageId": "2124", "fix": "2419", "desc": "2126"}, {"messageId": "2121", "fix": "2420", "desc": "2123"}, {"messageId": "2124", "fix": "2421", "desc": "2126"}, {"messageId": "2121", "fix": "2422", "desc": "2123"}, {"messageId": "2124", "fix": "2423", "desc": "2126"}, {"messageId": "2121", "fix": "2424", "desc": "2123"}, {"messageId": "2124", "fix": "2425", "desc": "2126"}, {"messageId": "2121", "fix": "2426", "desc": "2123"}, {"messageId": "2124", "fix": "2427", "desc": "2126"}, {"messageId": "2121", "fix": "2428", "desc": "2123"}, {"messageId": "2124", "fix": "2429", "desc": "2126"}, {"messageId": "2121", "fix": "2430", "desc": "2123"}, {"messageId": "2124", "fix": "2431", "desc": "2126"}, {"messageId": "2121", "fix": "2432", "desc": "2123"}, {"messageId": "2124", "fix": "2433", "desc": "2126"}, {"messageId": "2121", "fix": "2434", "desc": "2123"}, {"messageId": "2124", "fix": "2435", "desc": "2126"}, {"messageId": "2121", "fix": "2436", "desc": "2123"}, {"messageId": "2124", "fix": "2437", "desc": "2126"}, {"messageId": "2121", "fix": "2438", "desc": "2123"}, {"messageId": "2124", "fix": "2439", "desc": "2126"}, {"messageId": "2121", "fix": "2440", "desc": "2123"}, {"messageId": "2124", "fix": "2441", "desc": "2126"}, {"messageId": "2121", "fix": "2442", "desc": "2123"}, {"messageId": "2124", "fix": "2443", "desc": "2126"}, {"messageId": "2121", "fix": "2444", "desc": "2123"}, {"messageId": "2124", "fix": "2445", "desc": "2126"}, {"messageId": "2121", "fix": "2446", "desc": "2123"}, {"messageId": "2124", "fix": "2447", "desc": "2126"}, {"messageId": "2121", "fix": "2448", "desc": "2123"}, {"messageId": "2124", "fix": "2449", "desc": "2126"}, {"messageId": "2121", "fix": "2450", "desc": "2123"}, {"messageId": "2124", "fix": "2451", "desc": "2126"}, {"messageId": "2121", "fix": "2452", "desc": "2123"}, {"messageId": "2124", "fix": "2453", "desc": "2126"}, {"messageId": "2121", "fix": "2454", "desc": "2123"}, {"messageId": "2124", "fix": "2455", "desc": "2126"}, {"messageId": "2121", "fix": "2456", "desc": "2123"}, {"messageId": "2124", "fix": "2457", "desc": "2126"}, {"messageId": "2121", "fix": "2458", "desc": "2123"}, {"messageId": "2124", "fix": "2459", "desc": "2126"}, {"messageId": "2121", "fix": "2460", "desc": "2123"}, {"messageId": "2124", "fix": "2461", "desc": "2126"}, {"messageId": "2121", "fix": "2462", "desc": "2123"}, {"messageId": "2124", "fix": "2463", "desc": "2126"}, {"messageId": "2121", "fix": "2464", "desc": "2123"}, {"messageId": "2124", "fix": "2465", "desc": "2126"}, {"messageId": "2121", "fix": "2466", "desc": "2123"}, {"messageId": "2124", "fix": "2467", "desc": "2126"}, {"messageId": "2121", "fix": "2468", "desc": "2123"}, {"messageId": "2124", "fix": "2469", "desc": "2126"}, {"messageId": "2121", "fix": "2470", "desc": "2123"}, {"messageId": "2124", "fix": "2471", "desc": "2126"}, {"desc": "2472", "fix": "2473"}, {"messageId": "2121", "fix": "2474", "desc": "2123"}, {"messageId": "2124", "fix": "2475", "desc": "2126"}, {"messageId": "2182", "data": "2476", "fix": "2477", "desc": "2261"}, {"messageId": "2182", "data": "2478", "fix": "2479", "desc": "2264"}, {"messageId": "2182", "data": "2480", "fix": "2481", "desc": "2267"}, {"messageId": "2182", "data": "2482", "fix": "2483", "desc": "2270"}, {"messageId": "2182", "data": "2484", "fix": "2485", "desc": "2185"}, {"messageId": "2182", "data": "2486", "fix": "2487", "desc": "2188"}, {"messageId": "2182", "data": "2488", "fix": "2489", "desc": "2191"}, {"messageId": "2182", "data": "2490", "fix": "2491", "desc": "2194"}, {"messageId": "2182", "data": "2492", "fix": "2493", "desc": "2185"}, {"messageId": "2182", "data": "2494", "fix": "2495", "desc": "2188"}, {"messageId": "2182", "data": "2496", "fix": "2497", "desc": "2191"}, {"messageId": "2182", "data": "2498", "fix": "2499", "desc": "2194"}, {"messageId": "2182", "data": "2500", "fix": "2501", "desc": "2185"}, {"messageId": "2182", "data": "2502", "fix": "2503", "desc": "2188"}, {"messageId": "2182", "data": "2504", "fix": "2505", "desc": "2191"}, {"messageId": "2182", "data": "2506", "fix": "2507", "desc": "2194"}, {"messageId": "2182", "data": "2508", "fix": "2509", "desc": "2185"}, {"messageId": "2182", "data": "2510", "fix": "2511", "desc": "2188"}, {"messageId": "2182", "data": "2512", "fix": "2513", "desc": "2191"}, {"messageId": "2182", "data": "2514", "fix": "2515", "desc": "2194"}, {"messageId": "2182", "data": "2516", "fix": "2517", "desc": "2261"}, {"messageId": "2182", "data": "2518", "fix": "2519", "desc": "2264"}, {"messageId": "2182", "data": "2520", "fix": "2521", "desc": "2267"}, {"messageId": "2182", "data": "2522", "fix": "2523", "desc": "2270"}, {"messageId": "2182", "data": "2524", "fix": "2525", "desc": "2185"}, {"messageId": "2182", "data": "2526", "fix": "2527", "desc": "2188"}, {"messageId": "2182", "data": "2528", "fix": "2529", "desc": "2191"}, {"messageId": "2182", "data": "2530", "fix": "2531", "desc": "2194"}, {"messageId": "2182", "data": "2532", "fix": "2533", "desc": "2185"}, {"messageId": "2182", "data": "2534", "fix": "2535", "desc": "2188"}, {"messageId": "2182", "data": "2536", "fix": "2537", "desc": "2191"}, {"messageId": "2182", "data": "2538", "fix": "2539", "desc": "2194"}, {"messageId": "2182", "data": "2540", "fix": "2541", "desc": "2261"}, {"messageId": "2182", "data": "2542", "fix": "2543", "desc": "2264"}, {"messageId": "2182", "data": "2544", "fix": "2545", "desc": "2267"}, {"messageId": "2182", "data": "2546", "fix": "2547", "desc": "2270"}, {"messageId": "2182", "data": "2548", "fix": "2549", "desc": "2261"}, {"messageId": "2182", "data": "2550", "fix": "2551", "desc": "2264"}, {"messageId": "2182", "data": "2552", "fix": "2553", "desc": "2267"}, {"messageId": "2182", "data": "2554", "fix": "2555", "desc": "2270"}, {"messageId": "2121", "fix": "2556", "desc": "2123"}, {"messageId": "2124", "fix": "2557", "desc": "2126"}, {"messageId": "2121", "fix": "2558", "desc": "2123"}, {"messageId": "2124", "fix": "2559", "desc": "2126"}, {"messageId": "2121", "fix": "2560", "desc": "2123"}, {"messageId": "2124", "fix": "2561", "desc": "2126"}, {"messageId": "2121", "fix": "2562", "desc": "2123"}, {"messageId": "2124", "fix": "2563", "desc": "2126"}, {"messageId": "2121", "fix": "2564", "desc": "2123"}, {"messageId": "2124", "fix": "2565", "desc": "2126"}, {"messageId": "2121", "fix": "2566", "desc": "2123"}, {"messageId": "2124", "fix": "2567", "desc": "2126"}, {"messageId": "2121", "fix": "2568", "desc": "2123"}, {"messageId": "2124", "fix": "2569", "desc": "2126"}, {"messageId": "2121", "fix": "2570", "desc": "2123"}, {"messageId": "2124", "fix": "2571", "desc": "2126"}, {"messageId": "2121", "fix": "2572", "desc": "2123"}, {"messageId": "2124", "fix": "2573", "desc": "2126"}, {"messageId": "2121", "fix": "2574", "desc": "2123"}, {"messageId": "2124", "fix": "2575", "desc": "2126"}, {"messageId": "2121", "fix": "2576", "desc": "2123"}, {"messageId": "2124", "fix": "2577", "desc": "2126"}, {"messageId": "2121", "fix": "2578", "desc": "2123"}, {"messageId": "2124", "fix": "2579", "desc": "2126"}, {"messageId": "2121", "fix": "2580", "desc": "2123"}, {"messageId": "2124", "fix": "2581", "desc": "2126"}, {"messageId": "2121", "fix": "2582", "desc": "2123"}, {"messageId": "2124", "fix": "2583", "desc": "2126"}, {"messageId": "2121", "fix": "2584", "desc": "2123"}, {"messageId": "2124", "fix": "2585", "desc": "2126"}, {"messageId": "2121", "fix": "2586", "desc": "2123"}, {"messageId": "2124", "fix": "2587", "desc": "2126"}, {"messageId": "2121", "fix": "2588", "desc": "2123"}, {"messageId": "2124", "fix": "2589", "desc": "2126"}, {"messageId": "2121", "fix": "2590", "desc": "2123"}, {"messageId": "2124", "fix": "2591", "desc": "2126"}, {"messageId": "2121", "fix": "2592", "desc": "2123"}, {"messageId": "2124", "fix": "2593", "desc": "2126"}, {"messageId": "2121", "fix": "2594", "desc": "2123"}, {"messageId": "2124", "fix": "2595", "desc": "2126"}, {"messageId": "2182", "data": "2596", "fix": "2597", "desc": "2261"}, {"messageId": "2182", "data": "2598", "fix": "2599", "desc": "2264"}, {"messageId": "2182", "data": "2600", "fix": "2601", "desc": "2267"}, {"messageId": "2182", "data": "2602", "fix": "2603", "desc": "2270"}, {"messageId": "2121", "fix": "2604", "desc": "2123"}, {"messageId": "2124", "fix": "2605", "desc": "2126"}, {"messageId": "2121", "fix": "2606", "desc": "2123"}, {"messageId": "2124", "fix": "2607", "desc": "2126"}, {"messageId": "2121", "fix": "2608", "desc": "2123"}, {"messageId": "2124", "fix": "2609", "desc": "2126"}, {"desc": "2610", "fix": "2611"}, {"messageId": "2121", "fix": "2612", "desc": "2123"}, {"messageId": "2124", "fix": "2613", "desc": "2126"}, {"messageId": "2121", "fix": "2614", "desc": "2123"}, {"messageId": "2124", "fix": "2615", "desc": "2126"}, {"messageId": "2121", "fix": "2616", "desc": "2123"}, {"messageId": "2124", "fix": "2617", "desc": "2126"}, {"messageId": "2121", "fix": "2618", "desc": "2123"}, {"messageId": "2124", "fix": "2619", "desc": "2126"}, {"desc": "2620", "fix": "2621"}, {"desc": "2620", "fix": "2622"}, {"desc": "2620", "fix": "2623"}, {"desc": "2620", "fix": "2624"}, {"messageId": "2121", "fix": "2625", "desc": "2123"}, {"messageId": "2124", "fix": "2626", "desc": "2126"}, {"messageId": "2121", "fix": "2627", "desc": "2123"}, {"messageId": "2124", "fix": "2628", "desc": "2126"}, {"messageId": "2121", "fix": "2629", "desc": "2123"}, {"messageId": "2124", "fix": "2630", "desc": "2126"}, {"messageId": "2121", "fix": "2631", "desc": "2123"}, {"messageId": "2124", "fix": "2632", "desc": "2126"}, {"messageId": "2121", "fix": "2633", "desc": "2123"}, {"messageId": "2124", "fix": "2634", "desc": "2126"}, "suggestUnknown", {"range": "2635", "text": "2636"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2637", "text": "2638"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2639", "text": "2636"}, {"range": "2640", "text": "2638"}, {"range": "2641", "text": "2636"}, {"range": "2642", "text": "2638"}, {"range": "2643", "text": "2636"}, {"range": "2644", "text": "2638"}, {"range": "2645", "text": "2636"}, {"range": "2646", "text": "2638"}, {"range": "2647", "text": "2636"}, {"range": "2648", "text": "2638"}, {"range": "2649", "text": "2636"}, {"range": "2650", "text": "2638"}, {"range": "2651", "text": "2636"}, {"range": "2652", "text": "2638"}, {"range": "2653", "text": "2636"}, {"range": "2654", "text": "2638"}, {"range": "2655", "text": "2636"}, {"range": "2656", "text": "2638"}, {"range": "2657", "text": "2636"}, {"range": "2658", "text": "2638"}, {"range": "2659", "text": "2636"}, {"range": "2660", "text": "2638"}, {"range": "2661", "text": "2636"}, {"range": "2662", "text": "2638"}, {"range": "2663", "text": "2636"}, {"range": "2664", "text": "2638"}, {"range": "2665", "text": "2636"}, {"range": "2666", "text": "2638"}, {"range": "2667", "text": "2636"}, {"range": "2668", "text": "2638"}, {"range": "2669", "text": "2636"}, {"range": "2670", "text": "2638"}, {"range": "2671", "text": "2636"}, {"range": "2672", "text": "2638"}, "Update the dependencies array to be: [fetchData, filters]", {"range": "2673", "text": "2674"}, {"range": "2675", "text": "2636"}, {"range": "2676", "text": "2638"}, {"range": "2677", "text": "2636"}, {"range": "2678", "text": "2638"}, {"range": "2679", "text": "2636"}, {"range": "2680", "text": "2638"}, {"range": "2681", "text": "2636"}, {"range": "2682", "text": "2638"}, {"range": "2683", "text": "2636"}, {"range": "2684", "text": "2638"}, {"range": "2685", "text": "2636"}, {"range": "2686", "text": "2638"}, {"range": "2687", "text": "2636"}, {"range": "2688", "text": "2638"}, {"range": "2689", "text": "2636"}, {"range": "2690", "text": "2638"}, {"range": "2691", "text": "2674"}, {"range": "2692", "text": "2636"}, {"range": "2693", "text": "2638"}, "replaceWithAlt", {"alt": "2694"}, {"range": "2695", "text": "2696"}, "Replace with `&quot;`.", {"alt": "2697"}, {"range": "2698", "text": "2699"}, "Replace with `&ldquo;`.", {"alt": "2700"}, {"range": "2701", "text": "2702"}, "Replace with `&#34;`.", {"alt": "2703"}, {"range": "2704", "text": "2705"}, "Replace with `&rdquo;`.", {"alt": "2694"}, {"range": "2706", "text": "2707"}, {"alt": "2697"}, {"range": "2708", "text": "2709"}, {"alt": "2700"}, {"range": "2710", "text": "2711"}, {"alt": "2703"}, {"range": "2712", "text": "2713"}, {"range": "2714", "text": "2636"}, {"range": "2715", "text": "2638"}, {"range": "2716", "text": "2636"}, {"range": "2717", "text": "2638"}, {"range": "2718", "text": "2636"}, {"range": "2719", "text": "2638"}, {"range": "2720", "text": "2636"}, {"range": "2721", "text": "2638"}, {"range": "2722", "text": "2636"}, {"range": "2723", "text": "2638"}, {"range": "2724", "text": "2636"}, {"range": "2725", "text": "2638"}, {"range": "2726", "text": "2636"}, {"range": "2727", "text": "2638"}, {"range": "2728", "text": "2636"}, {"range": "2729", "text": "2638"}, {"range": "2730", "text": "2636"}, {"range": "2731", "text": "2638"}, {"range": "2732", "text": "2636"}, {"range": "2733", "text": "2638"}, {"range": "2734", "text": "2636"}, {"range": "2735", "text": "2638"}, {"range": "2736", "text": "2636"}, {"range": "2737", "text": "2638"}, {"range": "2738", "text": "2636"}, {"range": "2739", "text": "2638"}, {"range": "2740", "text": "2636"}, {"range": "2741", "text": "2638"}, {"range": "2742", "text": "2636"}, {"range": "2743", "text": "2638"}, {"range": "2744", "text": "2636"}, {"range": "2745", "text": "2638"}, {"range": "2746", "text": "2636"}, {"range": "2747", "text": "2638"}, {"range": "2748", "text": "2636"}, {"range": "2749", "text": "2638"}, {"range": "2750", "text": "2636"}, {"range": "2751", "text": "2638"}, {"range": "2752", "text": "2636"}, {"range": "2753", "text": "2638"}, {"range": "2754", "text": "2636"}, {"range": "2755", "text": "2638"}, {"range": "2756", "text": "2636"}, {"range": "2757", "text": "2638"}, {"range": "2758", "text": "2636"}, {"range": "2759", "text": "2638"}, {"range": "2760", "text": "2636"}, {"range": "2761", "text": "2638"}, {"range": "2762", "text": "2636"}, {"range": "2763", "text": "2638"}, {"range": "2764", "text": "2636"}, {"range": "2765", "text": "2638"}, {"range": "2766", "text": "2636"}, {"range": "2767", "text": "2638"}, {"range": "2768", "text": "2636"}, {"range": "2769", "text": "2638"}, {"alt": "2770"}, {"range": "2771", "text": "2772"}, "Replace with `&apos;`.", {"alt": "2773"}, {"range": "2774", "text": "2775"}, "Replace with `&lsquo;`.", {"alt": "2776"}, {"range": "2777", "text": "2778"}, "Replace with `&#39;`.", {"alt": "2779"}, {"range": "2780", "text": "2781"}, "Replace with `&rsquo;`.", {"range": "2782", "text": "2636"}, {"range": "2783", "text": "2638"}, {"range": "2784", "text": "2636"}, {"range": "2785", "text": "2638"}, {"range": "2786", "text": "2636"}, {"range": "2787", "text": "2638"}, {"range": "2788", "text": "2636"}, {"range": "2789", "text": "2638"}, {"alt": "2770"}, {"range": "2790", "text": "2791"}, {"alt": "2773"}, {"range": "2792", "text": "2793"}, {"alt": "2776"}, {"range": "2794", "text": "2795"}, {"alt": "2779"}, {"range": "2796", "text": "2797"}, {"alt": "2770"}, {"range": "2798", "text": "2791"}, {"alt": "2773"}, {"range": "2799", "text": "2793"}, {"alt": "2776"}, {"range": "2800", "text": "2795"}, {"alt": "2779"}, {"range": "2801", "text": "2797"}, {"range": "2802", "text": "2636"}, {"range": "2803", "text": "2638"}, {"range": "2804", "text": "2636"}, {"range": "2805", "text": "2638"}, {"range": "2806", "text": "2636"}, {"range": "2807", "text": "2638"}, {"alt": "2770"}, {"range": "2808", "text": "2809"}, {"alt": "2773"}, {"range": "2810", "text": "2811"}, {"alt": "2776"}, {"range": "2812", "text": "2813"}, {"alt": "2779"}, {"range": "2814", "text": "2815"}, {"range": "2816", "text": "2636"}, {"range": "2817", "text": "2638"}, "Update the dependencies array to be: [setSections, setLoading, setError, projectId]", {"range": "2818", "text": "2819"}, {"range": "2820", "text": "2636"}, {"range": "2821", "text": "2638"}, {"range": "2822", "text": "2636"}, {"range": "2823", "text": "2638"}, {"range": "2824", "text": "2636"}, {"range": "2825", "text": "2638"}, {"range": "2826", "text": "2636"}, {"range": "2827", "text": "2638"}, {"range": "2828", "text": "2636"}, {"range": "2829", "text": "2638"}, {"range": "2830", "text": "2636"}, {"range": "2831", "text": "2638"}, {"alt": "2770"}, {"range": "2832", "text": "2833"}, {"alt": "2773"}, {"range": "2834", "text": "2835"}, {"alt": "2776"}, {"range": "2836", "text": "2837"}, {"alt": "2779"}, {"range": "2838", "text": "2839"}, {"range": "2840", "text": "2636"}, {"range": "2841", "text": "2638"}, {"alt": "2770"}, {"range": "2842", "text": "2843"}, {"alt": "2773"}, {"range": "2844", "text": "2845"}, {"alt": "2776"}, {"range": "2846", "text": "2847"}, {"alt": "2779"}, {"range": "2848", "text": "2849"}, {"range": "2850", "text": "2636"}, {"range": "2851", "text": "2638"}, {"range": "2852", "text": "2636"}, {"range": "2853", "text": "2638"}, "Update the dependencies array to be: [isSignedIn, user, userLoaded, isAuthenticated, actions, getToken, identifyUser]", {"range": "2854", "text": "2855"}, "replaceEmptyInterfaceWithSuper", {"range": "2856", "text": "2857"}, "Replace empty interface with a type alias.", {"range": "2858", "text": "2636"}, {"range": "2859", "text": "2638"}, {"range": "2860", "text": "2636"}, {"range": "2861", "text": "2638"}, {"range": "2862", "text": "2636"}, {"range": "2863", "text": "2638"}, {"range": "2864", "text": "2636"}, {"range": "2865", "text": "2638"}, {"range": "2866", "text": "2636"}, {"range": "2867", "text": "2638"}, {"range": "2868", "text": "2636"}, {"range": "2869", "text": "2638"}, {"range": "2870", "text": "2636"}, {"range": "2871", "text": "2638"}, {"range": "2872", "text": "2636"}, {"range": "2873", "text": "2638"}, {"range": "2874", "text": "2636"}, {"range": "2875", "text": "2638"}, {"range": "2876", "text": "2636"}, {"range": "2877", "text": "2638"}, {"range": "2878", "text": "2636"}, {"range": "2879", "text": "2638"}, {"range": "2880", "text": "2636"}, {"range": "2881", "text": "2638"}, "Update the dependencies array to be: [isLoaded, user, isUserSynced, isSyncing, syncUserToBackend]", {"range": "2882", "text": "2883"}, {"range": "2884", "text": "2636"}, {"range": "2885", "text": "2638"}, {"range": "2886", "text": "2636"}, {"range": "2887", "text": "2638"}, {"range": "2888", "text": "2636"}, {"range": "2889", "text": "2638"}, {"range": "2890", "text": "2636"}, {"range": "2891", "text": "2638"}, {"range": "2892", "text": "2636"}, {"range": "2893", "text": "2638"}, {"range": "2894", "text": "2636"}, {"range": "2895", "text": "2638"}, {"range": "2896", "text": "2636"}, {"range": "2897", "text": "2638"}, {"range": "2898", "text": "2636"}, {"range": "2899", "text": "2638"}, {"range": "2900", "text": "2636"}, {"range": "2901", "text": "2638"}, {"range": "2902", "text": "2636"}, {"range": "2903", "text": "2638"}, {"range": "2904", "text": "2636"}, {"range": "2905", "text": "2638"}, {"range": "2906", "text": "2636"}, {"range": "2907", "text": "2638"}, {"range": "2908", "text": "2636"}, {"range": "2909", "text": "2638"}, {"range": "2910", "text": "2636"}, {"range": "2911", "text": "2638"}, {"range": "2912", "text": "2636"}, {"range": "2913", "text": "2638"}, {"range": "2914", "text": "2636"}, {"range": "2915", "text": "2638"}, {"range": "2916", "text": "2636"}, {"range": "2917", "text": "2638"}, {"range": "2918", "text": "2636"}, {"range": "2919", "text": "2638"}, {"range": "2920", "text": "2636"}, {"range": "2921", "text": "2638"}, {"range": "2922", "text": "2636"}, {"range": "2923", "text": "2638"}, {"range": "2924", "text": "2636"}, {"range": "2925", "text": "2638"}, {"range": "2926", "text": "2636"}, {"range": "2927", "text": "2638"}, {"range": "2928", "text": "2636"}, {"range": "2929", "text": "2638"}, {"range": "2930", "text": "2636"}, {"range": "2931", "text": "2638"}, {"range": "2932", "text": "2636"}, {"range": "2933", "text": "2638"}, {"range": "2934", "text": "2636"}, {"range": "2935", "text": "2638"}, {"range": "2936", "text": "2636"}, {"range": "2937", "text": "2638"}, {"range": "2938", "text": "2636"}, {"range": "2939", "text": "2638"}, {"range": "2940", "text": "2636"}, {"range": "2941", "text": "2638"}, {"range": "2942", "text": "2636"}, {"range": "2943", "text": "2638"}, {"range": "2944", "text": "2636"}, {"range": "2945", "text": "2638"}, {"range": "2946", "text": "2636"}, {"range": "2947", "text": "2638"}, {"range": "2948", "text": "2636"}, {"range": "2949", "text": "2638"}, {"range": "2950", "text": "2636"}, {"range": "2951", "text": "2638"}, {"range": "2952", "text": "2636"}, {"range": "2953", "text": "2638"}, {"range": "2954", "text": "2636"}, {"range": "2955", "text": "2638"}, {"range": "2956", "text": "2636"}, {"range": "2957", "text": "2638"}, {"range": "2958", "text": "2636"}, {"range": "2959", "text": "2638"}, {"range": "2960", "text": "2636"}, {"range": "2961", "text": "2638"}, {"range": "2962", "text": "2636"}, {"range": "2963", "text": "2638"}, {"range": "2964", "text": "2636"}, {"range": "2965", "text": "2638"}, {"range": "2966", "text": "2636"}, {"range": "2967", "text": "2638"}, {"range": "2968", "text": "2636"}, {"range": "2969", "text": "2638"}, {"range": "2970", "text": "2636"}, {"range": "2971", "text": "2638"}, {"range": "2972", "text": "2636"}, {"range": "2973", "text": "2638"}, {"range": "2974", "text": "2636"}, {"range": "2975", "text": "2638"}, {"range": "2976", "text": "2636"}, {"range": "2977", "text": "2638"}, "Update the dependencies array to be: [fetchTokens, isSignedIn]", {"range": "2978", "text": "2979"}, {"range": "2980", "text": "2636"}, {"range": "2981", "text": "2638"}, {"alt": "2770"}, {"range": "2982", "text": "2983"}, {"alt": "2773"}, {"range": "2984", "text": "2985"}, {"alt": "2776"}, {"range": "2986", "text": "2987"}, {"alt": "2779"}, {"range": "2988", "text": "2989"}, {"alt": "2694"}, {"range": "2990", "text": "2991"}, {"alt": "2697"}, {"range": "2992", "text": "2993"}, {"alt": "2700"}, {"range": "2994", "text": "2995"}, {"alt": "2703"}, {"range": "2996", "text": "2997"}, {"alt": "2694"}, {"range": "2998", "text": "2999"}, {"alt": "2697"}, {"range": "3000", "text": "3001"}, {"alt": "2700"}, {"range": "3002", "text": "3003"}, {"alt": "2703"}, {"range": "3004", "text": "3005"}, {"alt": "2694"}, {"range": "3006", "text": "3007"}, {"alt": "2697"}, {"range": "3008", "text": "3009"}, {"alt": "2700"}, {"range": "3010", "text": "3011"}, {"alt": "2703"}, {"range": "3012", "text": "3013"}, {"alt": "2694"}, {"range": "3014", "text": "3015"}, {"alt": "2697"}, {"range": "3016", "text": "3017"}, {"alt": "2700"}, {"range": "3018", "text": "3019"}, {"alt": "2703"}, {"range": "3020", "text": "3021"}, {"alt": "2770"}, {"range": "3022", "text": "3023"}, {"alt": "2773"}, {"range": "3024", "text": "3025"}, {"alt": "2776"}, {"range": "3026", "text": "3027"}, {"alt": "2779"}, {"range": "3028", "text": "3029"}, {"alt": "2694"}, {"range": "3030", "text": "3031"}, {"alt": "2697"}, {"range": "3032", "text": "3033"}, {"alt": "2700"}, {"range": "3034", "text": "3035"}, {"alt": "2703"}, {"range": "3036", "text": "3037"}, {"alt": "2694"}, {"range": "3038", "text": "3039"}, {"alt": "2697"}, {"range": "3040", "text": "3041"}, {"alt": "2700"}, {"range": "3042", "text": "3043"}, {"alt": "2703"}, {"range": "3044", "text": "3045"}, {"alt": "2770"}, {"range": "3046", "text": "3047"}, {"alt": "2773"}, {"range": "3048", "text": "3049"}, {"alt": "2776"}, {"range": "3050", "text": "3051"}, {"alt": "2779"}, {"range": "3052", "text": "3053"}, {"alt": "2770"}, {"range": "3054", "text": "3055"}, {"alt": "2773"}, {"range": "3056", "text": "3057"}, {"alt": "2776"}, {"range": "3058", "text": "3059"}, {"alt": "2779"}, {"range": "3060", "text": "3061"}, {"range": "3062", "text": "2636"}, {"range": "3063", "text": "2638"}, {"range": "3064", "text": "2636"}, {"range": "3065", "text": "2638"}, {"range": "3066", "text": "2636"}, {"range": "3067", "text": "2638"}, {"range": "3068", "text": "2636"}, {"range": "3069", "text": "2638"}, {"range": "3070", "text": "2636"}, {"range": "3071", "text": "2638"}, {"range": "3072", "text": "2636"}, {"range": "3073", "text": "2638"}, {"range": "3074", "text": "2636"}, {"range": "3075", "text": "2638"}, {"range": "3076", "text": "2636"}, {"range": "3077", "text": "2638"}, {"range": "3078", "text": "2636"}, {"range": "3079", "text": "2638"}, {"range": "3080", "text": "2636"}, {"range": "3081", "text": "2638"}, {"range": "3082", "text": "2636"}, {"range": "3083", "text": "2638"}, {"range": "3084", "text": "2636"}, {"range": "3085", "text": "2638"}, {"range": "3086", "text": "2636"}, {"range": "3087", "text": "2638"}, {"range": "3088", "text": "2636"}, {"range": "3089", "text": "2638"}, {"range": "3090", "text": "2636"}, {"range": "3091", "text": "2638"}, {"range": "3092", "text": "2636"}, {"range": "3093", "text": "2638"}, {"range": "3094", "text": "2636"}, {"range": "3095", "text": "2638"}, {"range": "3096", "text": "2636"}, {"range": "3097", "text": "2638"}, {"range": "3098", "text": "2636"}, {"range": "3099", "text": "2638"}, {"range": "3100", "text": "2636"}, {"range": "3101", "text": "2638"}, {"alt": "2770"}, {"range": "3102", "text": "3103"}, {"alt": "2773"}, {"range": "3104", "text": "3105"}, {"alt": "2776"}, {"range": "3106", "text": "3107"}, {"alt": "2779"}, {"range": "3108", "text": "3109"}, {"range": "3110", "text": "2636"}, {"range": "3111", "text": "2638"}, {"range": "3112", "text": "2636"}, {"range": "3113", "text": "2638"}, {"range": "3114", "text": "2636"}, {"range": "3115", "text": "2638"}, "Update the dependencies array to be: [canProceed, currentQuestion?.type, handleNext, isLastStep]", {"range": "3116", "text": "3117"}, {"range": "3118", "text": "2636"}, {"range": "3119", "text": "2638"}, {"range": "3120", "text": "2636"}, {"range": "3121", "text": "2638"}, {"range": "3122", "text": "2636"}, {"range": "3123", "text": "2638"}, {"range": "3124", "text": "2636"}, {"range": "3125", "text": "2638"}, "Update the dependencies array to be: [updateState, projectId, toast, handleError]", {"range": "3126", "text": "3127"}, {"range": "3128", "text": "3127"}, {"range": "3129", "text": "3127"}, {"range": "3130", "text": "3127"}, {"range": "3131", "text": "2636"}, {"range": "3132", "text": "2638"}, {"range": "3133", "text": "2636"}, {"range": "3134", "text": "2638"}, {"range": "3135", "text": "2636"}, {"range": "3136", "text": "2638"}, {"range": "3137", "text": "2636"}, {"range": "3138", "text": "2638"}, {"range": "3139", "text": "2636"}, {"range": "3140", "text": "2638"}, [757, 760], "unknown", [757, 760], "never", [1429, 1432], [1429, 1432], [7584, 7587], [7584, 7587], [8541, 8544], [8541, 8544], [9268, 9271], [9268, 9271], [10164, 10167], [10164, 10167], [667, 670], [667, 670], [1339, 1342], [1339, 1342], [787, 790], [787, 790], [1458, 1461], [1458, 1461], [3011, 3014], [3011, 3014], [6282, 6285], [6282, 6285], [7885, 7888], [7885, 7888], [8825, 8828], [8825, 8828], [10392, 10395], [10392, 10395], [908, 911], [908, 911], [1723, 1726], [1723, 1726], [2493, 2496], [2493, 2496], [2624, 2633], "[fetchData, filters]", [2990, 2993], [2990, 2993], [5914, 5917], [5914, 5917], [6558, 6561], [6558, 6561], [7325, 7328], [7325, 7328], [14011, 14014], [14011, 14014], [824, 827], [824, 827], [1532, 1535], [1532, 1535], [2119, 2122], [2119, 2122], [2250, 2259], [8196, 8199], [8196, 8199], "&quot;", [11005, 11039], "\n                                &quot;", "&ldquo;", [11005, 11039], "\n                                &ldquo;", "&#34;", [11005, 11039], "\n                                &#34;", "&rdquo;", [11005, 11039], "\n                                &rdquo;", [11061, 11093], "&quot;\n                              ", [11061, 11093], "&ldquo;\n                              ", [11061, 11093], "&#34;\n                              ", [11061, 11093], "&rdquo;\n                              ", [1043, 1046], [1043, 1046], [4734, 4737], [4734, 4737], [7032, 7035], [7032, 7035], [8375, 8378], [8375, 8378], [8548, 8551], [8548, 8551], [10055, 10058], [10055, 10058], [11830, 11833], [11830, 11833], [14018, 14021], [14018, 14021], [14152, 14155], [14152, 14155], [1915, 1918], [1915, 1918], [2273, 2276], [2273, 2276], [4152, 4155], [4152, 4155], [4552, 4555], [4552, 4555], [1646, 1649], [1646, 1649], [688, 691], [688, 691], [1356, 1359], [1356, 1359], [2902, 2905], [2902, 2905], [3102, 3105], [3102, 3105], [12035, 12038], [12035, 12038], [3881, 3884], [3881, 3884], [5485, 5488], [5485, 5488], [11251, 11254], [11251, 11254], [2164, 2167], [2164, 2167], [2540, 2543], [2540, 2543], [981, 984], [981, 984], [1180, 1183], [1180, 1183], [2540, 2543], [2540, 2543], [4125, 4128], [4125, 4128], "&apos;", [5327, 5362], "\n            Don&apos;t have an account?", "&lsquo;", [5327, 5362], "\n            Don&lsquo;t have an account?", "&#39;", [5327, 5362], "\n            Don&#39;t have an account?", "&rsquo;", [5327, 5362], "\n            Don&rsquo;t have an account?", [2513, 2516], [2513, 2516], [3174, 3177], [3174, 3177], [4650, 4653], [4650, 4653], [7211, 7214], [7211, 7214], [5042, 5079], "\n            Didn&apos;t receive the code?", [5042, 5079], "\n            Didn&lsquo;t receive the code?", [5042, 5079], "\n            Didn&#39;t receive the code?", [5042, 5079], "\n            Didn&rsquo;t receive the code?", [5031, 5068], [5031, 5068], [5031, 5068], [5031, 5068], [3448, 3451], [3448, 3451], [2629, 2632], [2629, 2632], [3061, 3064], [3061, 3064], [4344, 4418], "\n              You don&apos;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&lsquo;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&#39;t have permission to edit this project\n            ", [4344, 4418], "\n              You don&rsquo;t have permission to edit this project\n            ", [7473, 7476], [7473, 7476], [2901, 2936], "[setSections, setLoading, setError, projectId]", [3042, 3045], [3042, 3045], [1700, 1703], [1700, 1703], [1763, 1766], [1763, 1766], [2846, 2849], [2846, 2849], [3477, 3480], [3477, 3480], [4021, 4024], [4021, 4024], [7531, 7628], "\n                    Get notified when projects you&apos;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&lsquo;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&#39;re working on are updated.\n                  ", [7531, 7628], "\n                    Get notified when projects you&rsquo;re working on are updated.\n                  ", [678, 681], [678, 681], [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&apos;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&lsquo;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&#39;s productivity.\n          ", [2687, 2801], "\n            Powerful features designed to streamline your workflow and boost your team&rsquo;s productivity.\n          ", [962, 965], [962, 965], [1196, 1199], [1196, 1199], [4255, 4321], "[isSignedIn, user, userLoaded, isAuthenticated, actions, getToken, identifyUser]", [75, 161], "type TextareaProps = React.TextareaHTMLAttributes<HTMLTextAreaElement>", [3481, 3484], [3481, 3484], [5157, 5160], [5157, 5160], [643, 646], [643, 646], [1025, 1028], [1025, 1028], [3959, 3962], [3959, 3962], [6186, 6189], [6186, 6189], [7912, 7915], [7912, 7915], [8987, 8990], [8987, 8990], [2368, 2371], [2368, 2371], [4556, 4559], [4556, 4559], [1342, 1345], [1342, 1345], [365, 368], [365, 368], [2370, 2411], "[isLoaded, user, isUserSynced, isSyncing, syncUserToBackend]", [7878, 7881], [7878, 7881], [8011, 8014], [8011, 8014], [8445, 8448], [8445, 8448], [9420, 9423], [9420, 9423], [476, 479], [476, 479], [736, 739], [736, 739], [1094, 1097], [1094, 1097], [3178, 3181], [3178, 3181], [4313, 4316], [4313, 4316], [4494, 4497], [4494, 4497], [4533, 4536], [4533, 4536], [4772, 4775], [4772, 4775], [4811, 4814], [4811, 4814], [5053, 5056], [5053, 5056], [5092, 5095], [5092, 5095], [5338, 5341], [5338, 5341], [5523, 5526], [5523, 5526], [5864, 5867], [5864, 5867], [1897, 1900], [1897, 1900], [3169, 3172], [3169, 3172], [3796, 3799], [3796, 3799], [6171, 6174], [6171, 6174], [321, 324], [321, 324], [894, 897], [894, 897], [1625, 1628], [1625, 1628], [1696, 1699], [1696, 1699], [4222, 4225], [4222, 4225], [4679, 4682], [4679, 4682], [810, 813], [810, 813], [1325, 1328], [1325, 1328], [1501, 1504], [1501, 1504], [3470, 3473], [3470, 3473], [1491, 1494], [1491, 1494], [1631, 1634], [1631, 1634], [1933, 1936], [1933, 1936], [2473, 2476], [2473, 2476], [2234, 2237], [2234, 2237], [90, 93], [90, 93], [112, 115], [112, 115], [2453, 2456], [2453, 2456], [2656, 2659], [2656, 2659], [2821, 2824], [2821, 2824], [3283, 3286], [3283, 3286], [3355, 3358], [3355, 3358], [595, 598], [595, 598], [4010, 4013], [4010, 4013], [13919, 13922], [13919, 13922], [2685, 2697], "[fetchTokens, isSignedIn]", [3844, 3847], [3844, 3847], [4692, 4803], "\n              Check your browser&apos;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&lsquo;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&#39;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [4692, 4803], "\n              Check your browser&rsquo;s Network tab or PostHog dashboard to verify the event was sent.\n            ", [5139, 5172], "3. Filter by &quot;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &ldquo;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &#34;posthog\" or \"batch\"", [5139, 5172], "3. Filter by &rdquo;posthog\" or \"batch\"", [5139, 5172], "3. Filter by \"posthog&quot; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&ldquo; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&#34; or \"batch\"", [5139, 5172], "3. Filter by \"posthog&rdquo; or \"batch\"", [5139, 5172], "3. Filter by \"posthog\" or &quot;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &ldquo;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &#34;batch\"", [5139, 5172], "3. Filter by \"posthog\" or &rdquo;batch\"", [5139, 5172], "3. Filter by \"posthog\" or \"batch&quot;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&ldquo;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&#34;", [5139, 5172], "3. Filter by \"posthog\" or \"batch&rdquo;", [4624, 4893], "\n                  We&apos;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&lsquo;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&#39;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We&rsquo;re old enough to remember when the term \"lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &quot;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &ldquo;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &#34;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term &rdquo;lifestyle business\" was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&quot; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&ldquo; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&#34; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [4624, 4893], "\n                  We're old enough to remember when the term \"lifestyle business&rdquo; was a dirty word, but as the dust of SaaS-era gluttony settles, \n                  we see that sustainable, attractive opportunities exist - they are not too small or too few & they are ", [950, 1048], "\n              The blog post you&apos;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&lsquo;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&#39;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you&rsquo;re looking for doesn't exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&apos;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&lsquo;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&#39;t exist or may have been moved.\n            ", [950, 1048], "\n              The blog post you're looking for doesn&rsquo;t exist or may have been moved.\n            ", [330, 333], [330, 333], [354, 357], [354, 357], [587, 590], [587, 590], [661, 664], [661, 664], [554, 557], [554, 557], [578, 581], [578, 581], [601, 604], [601, 604], [621, 624], [621, 624], [640, 643], [640, 643], [723, 726], [723, 726], [295, 298], [295, 298], [5407, 5410], [5407, 5410], [6657, 6660], [6657, 6660], [216, 219], [216, 219], [1922, 1925], [1922, 1925], [2094, 2097], [2094, 2097], [4864, 4867], [4864, 4867], [5015, 5018], [5015, 5018], [6128, 6131], [6128, 6131], [6287, 6290], [6287, 6290], [8671, 8738], "\n                We&apos;re setting up everything for you\n              ", [8671, 8738], "\n                We&lsquo;re setting up everything for you\n              ", [8671, 8738], "\n                We&#39;re setting up everything for you\n              ", [8671, 8738], "\n                We&rsquo;re setting up everything for you\n              ", [978, 981], [978, 981], [1056, 1059], [1056, 1059], [1333, 1336], [1333, 1336], [2554, 2601], "[canProceed, currentQuestion?.type, handleNext, isLastStep]", [3075, 3078], [3075, 3078], [3207, 3210], [3207, 3210], [3307, 3310], [3307, 3310], [3579, 3582], [3579, 3582], [3501, 3532], "[updateState, projectId, toast, handleError]", [4335, 4366], [5075, 5106], [6170, 6201], [554, 557], [554, 557], [1183, 1186], [1183, 1186], [1291, 1294], [1291, 1294], [1405, 1408], [1405, 1408], [1428, 1431], [1428, 1431]]